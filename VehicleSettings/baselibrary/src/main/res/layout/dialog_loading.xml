<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/border_bg_dialog"
    android:minWidth="230dp"
    android:padding="10dp">

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableBottom="@drawable/line_dividing_dashed"
        android:drawablePadding="7dp"
        android:text="上传中"
        android:textColor="@color/common_grey"
        android:textSize="16sp" />

    <com.bitech.base.view.UINumberProgressBar xmlns:custom="http://schemas.android.com/apk/res-auto"
        android:id="@+id/progressbar"
        style="@style/NumberProgressBar_Default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignStart="@id/title_tv"
        android:layout_alignRight="@id/title_tv"
        android:layout_below="@id/title_tv"
        android:layout_centerInParent="true"
        android:layout_marginBottom="20dp"
        android:layout_marginTop="20dp"
        custom:progress="0" />
</RelativeLayout>