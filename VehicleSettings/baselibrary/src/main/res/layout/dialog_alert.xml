<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/border_bg_dialog"
    android:padding="10dp">

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:text="标题"
        android:textColor="@color/common_black"
        android:textSize="@dimen/font_48px" />

    <TextView
        android:id="@+id/content_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_tv"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="45dp"
        android:text="内容"
        android:textColor="@color/common_typeface"
        android:textSize="@dimen/font_36px" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/content_tv"
        android:layout_centerHorizontal="true"
        android:gravity="center_horizontal"
        android:layout_marginTop="50dp"
        android:orientation="horizontal"
        android:layout_marginBottom="20dp">

        <Button
            android:id="@+id/positiveButton"
            android:layout_width="110dp"
            android:layout_height="45dp"
            android:layout_marginStart="25dp"
            android:layout_marginEnd="10dp"
            android:background="@drawable/submit_bg"
            android:ellipsize="end"
            android:gravity="center"
            android:padding="5dp"
            android:singleLine="true"
            android:textColor="@color/common_white"
            android:textSize="@dimen/font_36px" />

        <Button
            android:id="@+id/negativeButton"
            android:layout_width="110dp"
            android:layout_height="45dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="35dp"
            android:background="@drawable/cancel_bg"
            android:ellipsize="end"
            android:gravity="center"
            android:padding="5dp"
            android:singleLine="true"
            android:textColor="@color/common_white"
            android:textSize="@dimen/font_36px"
            />
    </LinearLayout>

</RelativeLayout>