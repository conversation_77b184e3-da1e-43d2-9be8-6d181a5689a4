<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CircleImageView">
        <attr name="border_width" format="dimension" />
        <attr name="border_color" format="color" />
    </declare-styleable>

    <declare-styleable name="NumberProgressBar">
        <attr name="progress" format="integer" />
        <attr name="max" format="integer" />

        <attr name="progress_unreached_color" format="color" />
        <attr name="progress_reached_color" format="color" />

        <attr name="progress_reached_bar_height" format="dimension" />
        <attr name="progress_unreached_bar_height" format="dimension" />

        <attr name="progress_text_size" format="dimension" />
        <attr name="progress_text_color" format="color" />

        <attr name="progress_text_offset" format="dimension" />

        <attr name="progress_text_visibility" format="enum">
            <enum name="visible" value="0" />
            <enum name="invisible" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="NumberProgressBarThemes">
        <attr name="numberProgressBarStyle" format="reference" />
    </declare-styleable>

    <declare-styleable name="ImageSliderThemes">
        <attr name="TypeValue" format="string" />
    </declare-styleable>
</resources>