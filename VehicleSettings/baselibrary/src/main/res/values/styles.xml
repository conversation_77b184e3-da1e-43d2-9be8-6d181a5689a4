<resources>

    <!-- Base application theme. -->
    <style name="BaseTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:listDivider">@drawable/divider</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="AppTheme" parent="BaseTheme">
        <!-- Customize your theme here. -->
        <item name="android:listDivider">@drawable/divider</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="dialog" parent="@android:style/Theme.Dialog">
        <!--去掉边框-->
        <item name="android:windowFrame">@null</item>
        <!--悬浮-->
        <item name="android:windowIsFloating">true</item>
        <!--半透明-->
        <item name="android:windowIsTranslucent">false</item>
        <!--不需要标题-->
        <item name="android:windowNoTitle">true</item>
        <!--背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--允许模糊-->
        <item name="android:backgroundDimEnabled">true</item>
        <!--全屏幕-->
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="NumberProgressBar_Default">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>

        <item name="max">100</item>
        <item name="progress">0</item>

        <item name="progress_unreached_color">#CCCCCC</item>
        <item name="progress_reached_color">#3498DB</item>

        <item name="progress_text_size">10sp</item>
        <item name="progress_text_color">#3498DB</item>

        <item name="progress_reached_bar_height">1.5dp</item>
        <item name="progress_unreached_bar_height">0.75dp</item>
    </style>

</resources>
