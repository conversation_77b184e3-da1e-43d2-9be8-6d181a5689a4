package com.bitech.base.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.TextView;

import com.bitech.base.R;

/**
 * Created by <PERSON><PERSON> on 16/4/20.
 */
public class LoadingDialog extends Dialog {
    private UINumberProgressBar bnp;
    private String titleStr;

    public LoadingDialog(Context context) {
        super(context, R.style.Dialog);
    }

    public LoadingDialog(Context context, int theme) {
        super(context, theme);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        this.setContentView(R.layout.dialog_loading);
        setCancelable(false);
        bnp = (UINumberProgressBar) findViewById(R.id.progressbar);

        TextView titleTV = (TextView) findViewById(R.id.title_tv);
        titleTV.setText(TextUtils.isEmpty(titleStr) ? "加载中 ... " : titleStr);
    }

    /**
     * 设置进度条最大值
     *
     * @param max
     */
    public void setMax(long max) {
        bnp.setMax(max);
    }

    /**
     * 设置进度条完成了多少
     *
     * @param progress
     */
    public void setProgress(long progress) {
        bnp.setProgress(progress);
    }

    /**
     * 获取进度条最大值
     *
     * @return
     */
    public long getMax() {
        return bnp.getMax();
    }

    @Override
    public void setTitle(CharSequence title) {
        titleStr = title.toString();
    }

    @Override
    public void setTitle(int titleId) {
        titleStr = getContext().getResources().getString(titleId);
    }
}
