package com.bitech.base.utils;

import android.text.TextUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by garry on 2015/2/9.
 */
public class Validator {
    /**
     * 验证手机号是否正确
     *
     * @param phoneNum
     * @return
     */
    public static VResult isCellphone(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            return new VResult(false, "请输入注册手机号码！");
        }
        boolean isCorrect = phoneNum.matches("^((13[0-9])|(147)|(145)|(15[0-9])|(17[0-9])|(18[0-9]))\\d{8}$");
        if (!isCorrect) {
            return new VResult(false, "请输入正确的手机号码");

        }
        return new VResult(true, "手机号格式正确");


    }

    /**
     * 验证密码长度
     */
    public static VResult validatePWD(String pwd) {
        if (TextUtils.isEmpty(pwd)) {
            return new VResult(false, "请输入密码！");
        }
        if (pwd.length() < 6) {
            return new VResult(false, "密码格式不正确");

        }

        if (pwd.length() > 20) {
            return new VResult(false, "密码格式不正确");

        }

        Pattern pattern = Pattern.compile("^(?![^a-zA-Z]+$)(?!\\D+$).{6,}$", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(pwd);
        if (!matcher.matches()) {
            return new VResult(false, "密码格式不正确");
        }

        return new VResult(true, "密码格式正确");
    }

    /**
     * 验证确认密码
     */
    public static VResult validatePWDC(String pwd, String pwdC) {
        if (TextUtils.isEmpty(pwd)) {
            return new VResult(false, "请输入密码！");
        }
        if (TextUtils.isEmpty(pwdC)) {
            return new VResult(false, "请输入确认密码！");
        }
        if (!isConformPwd(pwd)) {
            return new VResult(false, "请输入6位数字与字母组合密码");
        }
        if (!pwdC.equals(pwd)) {
            return new VResult(false, "请重新输入确认密码！");

        }

        return new VResult(true, "密码格式正确");
    }


    /**
     * 验证数据是否为空
     */
    public static VResult validateValue(String key, String value) {
        if (TextUtils.isEmpty(value)) {
            return new VResult(false, "请输入" + key + "!");
        }


        return new VResult(true, "");
    }

    /**
     * 验证时间格式
     */
    public static VResult validateTime(String key, String time) {
        if (TextUtils.isEmpty(time)) {
            return new VResult(false, "请输入" + key + "!");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date date = null;
        try {
            date = simpleDateFormat.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
            return new VResult(false, "请按格式输入" + key);
        }

        return new VResult(true, "");
    }

    /**
     * 验证时间格式
     */
    public static VResult validateTime2(String key, String time) {
        if (TextUtils.isEmpty(time)) {
            return new VResult(false, "请输入" + key + "!");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = simpleDateFormat.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
            return new VResult(false, "请按格式输入" + key);
        }

        return new VResult(true, "");
    }

    public static class VResult {

        public boolean isCorrect;
        public String wrongMessage;

        public VResult(boolean isCorrect, String wrongMessage) {
            this.isCorrect = isCorrect;
            this.wrongMessage = wrongMessage;
        }

    }

    public static boolean isPhone(String phoneNum) {
        return phoneNum.matches("^((13[0-9])|(15[0-9])|(18[0-9]))\\d{8}$");

    }

    private static boolean isConformPwd(String pwd) {
        Pattern pattern = Pattern.compile("^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6}$");
        Matcher matcher = pattern.matcher(pwd);
        return matcher.matches();
    }

}
