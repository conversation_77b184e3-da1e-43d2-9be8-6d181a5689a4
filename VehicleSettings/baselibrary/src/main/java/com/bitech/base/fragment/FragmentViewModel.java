package com.bitech.base.fragment;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

public class FragmentViewModel extends ViewModel {
    private MutableLiveData<String> currentFragmentTag = new MutableLiveData<>();

    public void setCurrentFragment(String fragmentTag) {
        currentFragmentTag.setValue(fragmentTag);
    }

    public LiveData<String> getCurrentFragment() {
        return currentFragmentTag;
    }
}
