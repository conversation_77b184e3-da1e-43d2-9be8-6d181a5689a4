package com.bitech.base.view;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bitech.base.R;

/**
 * Created by agu<PERSON> on 15/3/20.
 */
public class UIAlert extends Dialog {
    public UIAlert(Context context) {
        super(context);
    }

    public UIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected UIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder {

        private final Context context;
        private String title;
        private String message;
        private boolean isCan = true;
        private String positiveButtonText;
        private String negativeButtonText;
        private Button negativeButton;
        private UIAlert dialog = null;

        private OnClickListener
                positiveButtonClickListener,
                negativeButtonClickListener;


        public Builder(Context context) {
            this.context = context;
        }

        /**
         * Set the Dialog message from String
         *
         * @return
         */
        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }

        /**
         * Set the Dialog message from resource
         *
         * @return
         */
        public Builder setMessage(int message) {
            this.message = (String) context.getText(message);
            return this;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Set the Dialog title from resource
         *
         * @param title
         * @return
         */
        public Builder setTitle(int title) {
            this.title = (String) context.getText(title);
            return this;
        }

        /**
         * Set the Dialog title from String
         *
         * @param title
         * @return
         */
        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        /**
         * Set the positive button resource and it's listener
         *
         * @param positiveButtonText
         * @param listener
         * @return
         */
        public Builder setPositiveButton(int positiveButtonText,
                                         OnClickListener listener) {
            this.positiveButtonText = (String) context
                    .getText(positiveButtonText);
            this.positiveButtonClickListener = listener;
            return this;
        }

        /**
         * Set the positive button text and it's listener
         *
         * @param positiveButtonText
         * @param listener
         * @return
         */
        public Builder setPositiveButton(String positiveButtonText,
                                         OnClickListener listener) {
            this.positiveButtonText = positiveButtonText;
            this.positiveButtonClickListener = listener;
            return this;
        }

        /**
         * Set the negative button resource and it's listener
         *
         * @param negativeButtonText
         * @param listener
         * @return
         */
        public Builder setNegativeButton(int negativeButtonText,
                                         OnClickListener listener) {
            this.negativeButtonText = (String) context
                    .getText(negativeButtonText);
            this.negativeButtonClickListener = listener;
            return this;
        }

        public void setNegativeButtonText(String text){
            if (negativeButton != null){
                negativeButton.setText(text);
            }
        }

        /**
         * Set the negative button text and it's listener
         *
         * @param negativeButtonText
         * @param listener
         * @return
         */
        public Builder setNegativeButton(String negativeButtonText,
                                         OnClickListener listener) {
            this.negativeButtonText = negativeButtonText;
            this.negativeButtonClickListener = listener;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public UIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new UIAlert(context,
                        R.style.Dialog);
            }
            View layout = View.inflate(context, R.layout.dialog_alert, null);
            dialog.addContentView(layout, new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.WRAP_CONTENT));
            // set the dialog title
            TextView titleTV = (TextView) layout.findViewById(R.id.title_tv);
            if (!TextUtils.isEmpty(title)) {
                titleTV.setText(title);
            } else {
                titleTV.setVisibility(View.GONE);
            }

//            set the content message
            TextView messageTV = (TextView) layout.findViewById(R.id.content_tv);
            if (!TextUtils.isEmpty(message)) {
                messageTV.setText(message);
                if (message.contains("失败")) {
                    messageTV.setTextColor(context.getResources().getColor(R.color.common_red));
                }
            } else {
                messageTV.setVisibility(View.GONE);
            }

            // set the confirm button
            Button positiveButton = (Button) layout.findViewById(R.id.positiveButton);
            if (!TextUtils.isEmpty(positiveButtonText)) {
                positiveButton.setText(positiveButtonText);
                if (positiveButtonClickListener != null) {
                    positiveButton.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            positiveButtonClickListener.onClick(
                                    dialog,
                                    DialogInterface.BUTTON_POSITIVE);
                            dialog.dismiss();
                        }
                    });
                } else {
                    positiveButton.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            dialog.dismiss();
                        }
                    });
                }
            } else {
                positiveButton.setVisibility(View.GONE);
            }

            // set the cancel button
            negativeButton = (Button) layout.findViewById(R.id.negativeButton);
            if (!TextUtils.isEmpty(negativeButtonText)) {
                negativeButton.setText(negativeButtonText);
                if (negativeButtonClickListener != null) {
                    negativeButton.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            negativeButtonClickListener.onClick(
                                    dialog,
                                    DialogInterface.BUTTON_NEGATIVE);
                        }
                    });
                } else {
                    negativeButton.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            dialog.dismiss();
                        }
                    });
                }
            } else {
                // if no confirm button just set the visibility to GONE
                negativeButton.setVisibility(
                        View.GONE);
            }

            dialog.setCancelable(isCan);

            dialog.setContentView(layout);
            return dialog;
        }

    }

}
