package com.bitech.base.utils;

import android.Manifest;
import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Vibrator;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class AppUtil {

    /**
     * 获取包名
     *
     * @param cxt
     * @return
     */
    private static String getPkgName(Context cxt) {
        PackageManager pManager = cxt.getPackageManager();
        PackageInfo pkgInfo = null;
        try {
            pkgInfo = pManager.getPackageInfo(cxt.getPackageName(), 0);
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return pkgInfo.packageName;
    }


    /**
     * 检查 Manifest是否配置某个权限
     *
     * @param permission
     * @return 配置了权限返回true 反之返回 false
     */
    public static boolean hasPermission(Context context, String permission) {
        final PackageManager pm = context.getPackageManager();
        try {
            return pm.checkPermission(permission, context.getPackageName()) == PackageManager.PERMISSION_GRANTED;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取PackageInfo实例
     *
     * @return PackageInfo
     */
    public static PackageInfo getPackageInfo(Context context) {
        final PackageManager pm = context.getPackageManager();
        try {
            return pm.getPackageInfo(context.getPackageName(), 0);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取应用版本名称 对应Manifest中的 versionName
     *
     * @return 返回 应用版本名称
     */
    public static String getAppVersionName(Context context) {
        PackageInfo info = getPackageInfo(context);
        if (info == null) {
            return null;
        } else {
            return info.versionName;
        }
    }

    public static int getSDKCode() {
        return Build.VERSION.SDK_INT;
    }

    /**
     * 获取应用版本名称 对应Manifest中的 versionCode
     *
     * @return 返回 应用版本名号
     */
    public static int getAppVersionCode(Context context) {
        PackageInfo info = getPackageInfo(context);
        if (info == null) {
            return -1;
        } else {
            return info.versionCode;
        }
    }

    /**
     * 获取终端设备的CPU信息
     */
    public static String getCpuInfo() {
        String str = null;
        String result = null;
        FileReader localFileReader = null;
        BufferedReader localBufferedReader = null;
        try {
            localFileReader = new FileReader("/proc/cpuinfo");
            if (localFileReader != null) {
                try {
                    localBufferedReader = new BufferedReader(localFileReader,
                            1024);
                    str = localBufferedReader.readLine();
                    localBufferedReader.close();
                    localFileReader.close();
                } catch (IOException localIOException) {
                }
            }
        } catch (FileNotFoundException localFileNotFoundException) {
        }
        if (str != null) {
            int i = str.indexOf(58) + 1;
            str = str.substring(i);
            result = str.trim();
        }
        return result;
    }

    /**
     * 得到设备的mac地址
     * <p/>
     * 需要设置如下权限
     * <p/>
     * "android.permission.ACCESS_WIFI_STATE"
     * <p/>
     * "android.permission.INTERNET"
     *
     * @return 返回 设备的Mac地址
     */
    public static String getMac(Context context) {
        // TODO Auto-generated method stub
        String mac = null;
        try {
            WifiManager localWifiManager = (WifiManager) context
                    .getSystemService(Context.WIFI_SERVICE);
            WifiInfo localWifiInfo = localWifiManager.getConnectionInfo();
            mac = localWifiInfo.getMacAddress();
        } catch (Exception localException) {

        }
        return mac;
    }

    public static boolean checkWiFiConnectSuccess(Context inContext) {
        Context context = inContext.getApplicationContext();
        ConnectivityManager connectivity = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivity != null) {
            NetworkInfo[] info = connectivity.getAllNetworkInfo();
            if (info != null) {
                for (int i = 0; i < info.length; i++) {
                    if (info[i].getTypeName().equals("WIFI") && info[i].isConnected()) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获取设备的IMEI
     * <p/>
     * 需要如下权限
     * <p/>
     * android.permission.READ_PHONE_STATE
     *
     * @return 返回 设备的imei
     */
    public static String getIMEI(Context context) {
        String imei = "011472001975695";
        TelephonyManager telephonyManager = (TelephonyManager) context
                .getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager == null) {
            return null;
        }

        try {
            if (hasPermission(context, "android.permission.READ_PHONE_STATE")) {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
                    return imei;
                }
            }
            imei = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
            //android 10以上已经获取不了imei了 用 android id代替
            if (TextUtils.isEmpty(imei)) {
                imei = Settings.System.getString(
                        context.getContentResolver(), Settings.Secure.ANDROID_ID);
            }
        } catch (Exception localException) {
        }
        return imei;
    }

    /**
     * 获取手机型号
     */
    public static String getModel() {
        return Build.MODEL;
    }

    /**
     * 获取手机品牌
     */
    public static String getBrand() {
        return android.os.Build.BRAND;
    }

    /**
     * 得到接入类型
     *
     * @param mContext
     */
    public static String[] getAccesstype(Context mContext) {
        // TODO Auto-generated method stub
        String[] arrayOfString = {"Unknown", "Unknown"};
        PackageManager localPackageManager = mContext.getPackageManager();
        if (!hasPermission(mContext, "android.permission.ACCESS_NETWORK_STATE")) {
            arrayOfString[0] = "Unknown";
            return arrayOfString;
        }
        ConnectivityManager localConnectivityManager = (ConnectivityManager) mContext
                .getSystemService(Service.CONNECTIVITY_SERVICE);
        if (localConnectivityManager == null) {
            arrayOfString[0] = "Unknown";
            return arrayOfString;
        }
        NetworkInfo localNetworkInfo1 = localConnectivityManager
                .getNetworkInfo(1);
        if (localNetworkInfo1.getState() == NetworkInfo.State.CONNECTED) {
            arrayOfString[0] = "Wi-Fi";
            return arrayOfString;
        }
        NetworkInfo localNetworkInfo2 = localConnectivityManager
                .getNetworkInfo(0);
        if (localNetworkInfo2.getState() == NetworkInfo.State.CONNECTED) {
            arrayOfString[0] = "2G/3G";
            arrayOfString[1] = localNetworkInfo2.getSubtypeName();
            return arrayOfString;
        }
        return arrayOfString;
    }

    /**
     * 获取设备的显示
     *
     * @return 返回 设备的Display 对象
     */
    public static Display getDisplay(Context context) {
        Display display = null;
        final WindowManager windowManager = (WindowManager) context
                .getSystemService(Context.WINDOW_SERVICE);

        display = windowManager.getDefaultDisplay();
        return display;
    }

    /**
     * 获取屏幕的宽度
     *
     * @return 返回 屏幕的宽度 出现异常情况返回-1
     */
    public static int getScreenWidth(Context context) {
        int width = -1;
        Display display = null;
        display = getDisplay(context);
        if (display != null) {
            DisplayMetrics metric = new DisplayMetrics();
            display.getMetrics(metric);
            width = metric.widthPixels;
        }

        return width;
    }

    /**
     * 获取屏幕的高度
     *
     * @return 返回 屏幕的高度 出现异常情况返回-1
     */
    public static int getScreenHeight(Context context) {
        int height = -1;
        Display display = null;
        display = getDisplay(context);
        if (display != null) {
            DisplayMetrics metric = new DisplayMetrics();
            display.getMetrics(metric);
            height = metric.heightPixels;
        }

        return height;
    }

    /**
     * 获取屏幕的密度
     *
     * @return 返回 屏幕的密度 出现异常情况返回-1
     */
    public static float getScreenDensity(Context context) {
        float density = -1;
        Display display = null;
        display = getDisplay(context);
        if (display != null) {
            DisplayMetrics metric = new DisplayMetrics();
            display.getMetrics(metric);
            density = metric.density;
        }

        return density;
    }

    /**
     * 获取屏幕的密度
     *
     * @return 返回 屏幕的密度 出现异常情况返回-1
     */
    public static float getScreenDensityDpi(Context context) {
        int densityDpi = -1;
        Display display = null;
        display = getDisplay(context);
        if (display != null) {
            DisplayMetrics metric = new DisplayMetrics();
            display.getMetrics(metric);
            densityDpi = metric.densityDpi;
        }

        return densityDpi;
    }


    /**
     * 卸载指定应用包
     *
     * @param cxt
     */
    public static void uninstallApp(Context cxt) {
        String pkgName = getPkgName(cxt);
        Uri data = Uri.fromParts("package", pkgName, null);
        Intent intent = new Intent(Intent.ACTION_DELETE, data);
        cxt.startActivity(intent);
    }

    /**
     * 判断是否安装了应用
     */
    public static boolean isInstall(Context mContext, String packageName) {
        if (TextUtils.isEmpty(packageName)) {
            return false;
        }
        PackageInfo packageInfo;
        try {
            packageInfo = mContext.getPackageManager().getPackageInfo(
                    packageName, 0);
        } catch (NameNotFoundException e) {
            packageInfo = null;
            e.printStackTrace();
        }
        return packageInfo != null;
    }

    /**
     * 获取渠道名
     * 获取application中指定的meta-data
     *
     * @return 如果没有获取成功(没有对应值 ， 或者异常)，则返回值为空
     */
    public static String getAppMetaData(Context ctx, String key) {
        if (ctx == null || TextUtils.isEmpty(key)) {
            return null;
        }
        try {
            PackageManager packageManager = ctx.getPackageManager();
            if (packageManager != null) {
                ApplicationInfo applicationInfo = packageManager.getApplicationInfo(ctx.getPackageName(), PackageManager.GET_META_DATA);
                if (applicationInfo != null) {
                    if (applicationInfo.metaData != null) {
                        Object resultData = applicationInfo.metaData.get(key);
                        if (resultData != null) {
                            return resultData.toString();
                        }
                    }
                }
            }
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    /*
     * MD5加密
     */
    public static String getMD5Str(String str) {
        MessageDigest messageDigest = null;

        try {
            messageDigest = MessageDigest.getInstance("MD5");

            messageDigest.reset();

            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            System.out.println("NoSuchAlgorithmException caught!");
            System.exit(-1);
        }

        byte[] byteArray = messageDigest.digest();

        StringBuffer md5StrBuff = new StringBuffer();

        for (int i = 0; i < byteArray.length; i++) {
            if (Integer.toHexString(0xFF & byteArray[i]).length() == 1) {
                md5StrBuff.append("0").append(Integer.toHexString(0xFF & byteArray[i]));
            } else {
                md5StrBuff.append(Integer.toHexString(0xFF & byteArray[i]));
            }
        }
        return md5StrBuff.toString();
    }

    /**
     * 高德转百度坐标
     */
    public static String bd_encrypt(double gg_lon, double gg_lat) {
        double x_pi = 3.14159265358979324;
        double x = gg_lon, y = gg_lat;
        double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
        double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
        double geoLng = z * Math.cos(theta) + 0.0065;
        double geoLat = z * Math.sin(theta) + 0.006;
        return geoLng + "," + geoLat;
    }

    /**
     * 百度转高德
     *
     * @param bd_lat
     * @param bd_lon
     */
    public static String bd_decrypt(double bd_lat, double bd_lon) {
        double x_pi = 3.14159265358979324;
        double x = bd_lon - 0.0065, y = bd_lat - 0.006;
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
        double ggLat = z * Math.sin(theta);
        double ggLng = z * Math.cos(theta);
        return ggLng + "," + ggLat;
    }


    //    　/*时间戳转换成字符窜*/
    public static String getDateToString(long time) {
        Date d = new Date(time * 1000);
        SimpleDateFormat sf = new SimpleDateFormat("MM.dd HH:ss");
        return sf.format(d);
    }

    /**
     * final Activity activity  ：调用该方法的Activity实例
     * long milliseconds ：震动的时长，单位是毫秒
     */
    public static void Vibrate(final Activity activity, long milliseconds) {
        Vibrator vib = (Vibrator) activity.getSystemService(Service.VIBRATOR_SERVICE);
        vib.vibrate(milliseconds);
    }

    /**
     * 强制隐藏软键盘
     */
    public static void hideSoftInput(Context activity, View view) {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }

    /**
     * 读取assets下的txt文件，返回utf-8 String
     *
     * @param context
     * @param fileName 不包括后缀
     * @return
     */
    public static String readAssetsFile(Context context, String fileName) {
        try {
            //Return an AssetManager instance for your application's package
            InputStream is = context.getAssets().open(fileName);
            int size = is.available();
            // Read the entire asset into a local byte buffer.
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            // Convert the buffer into a string.
            String text = new String(buffer, StandardCharsets.UTF_8);
            // Finally stick the string into the text view.
            return text;
        } catch (IOException e) {
            // Should never happen!
//            throw new RuntimeException(e);
            e.printStackTrace();
        }
        return "读取错误，请检查文件名";
    }

    public static long getFileSize(File file) {
        long size = 0;
        if (file.exists()) {
            try {
                FileInputStream fis = new FileInputStream(file);
                size = fis.available();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return size;
    }

    public static boolean isGrantExternalRW(Activity activity) {
        if (AppUtil.getSDKCode() > 22) {
            if (ContextCompat.checkSelfPermission(activity,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                //先判断有没有权限 ，没有就在这里进行权限的申请
                ActivityCompat.requestPermissions(activity,
                        new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE}, 1);
                return false;

            }
            return true;
        }
        return true;
    }

    public static boolean isCameraPermission(Activity activity) {
        if (AppUtil.getSDKCode() > 22) {
            if (ContextCompat.checkSelfPermission(activity,
                    android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                //先判断有没有权限 ，没有就在这里进行权限的申请
                ActivityCompat.requestPermissions(activity,
                        new String[]{android.Manifest.permission.CAMERA}, 100);
                return false;

            }
            return true;
        }
        return true;
    }

    // 设置状态栏、导航栏颜色的方法
    public static void setBarColor(Activity activity, int color) {
        // 确保版本号大于等于5.0（Android Lollipop，API 21）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 设置状态栏颜色
            Window window = activity.getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(activity.getColor(color));
            window.setNavigationBarColor(activity.getColor(color));
        }
    }

}
