package com.bitech.base.net;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.bitech.base.R;
import com.bitech.base.app.ConfigKeys;
import com.bitech.base.app.Latte;
import com.bitech.base.net.callback.IError;
import com.bitech.base.net.callback.IFailure;
import com.bitech.base.net.callback.ISuccess;
import com.bitech.base.utils.AppUtil;
import com.bitech.base.utils.Util;
import com.bitech.base.view.loader.LoaderStyle;

import java.io.File;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.WeakHashMap;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

/**
 * Created by agui on 2018/9/26.
 */
public abstract class PosRequest {

    public void post(final String url, Map map, WeakHashMap<String, Object> params, final Context context, LoaderStyle loaderStyle) {
//        map.put("version", AppUtil.getAppVersionName(context));
//        StringBuilder sb = new StringBuilder();
//        Set<String> keySet = map.keySet();
//        Iterator<String> iterator = keySet.iterator();
//        StringBuilder md5 = new StringBuilder();
//        while (iterator.hasNext()) {
//            String key = iterator.next();
//            sb.append(key + "=" + map.get(key) + ",");
//            md5.append(map.get(key));
//        }
//        md5.append((String) Latte.getConfiguration(ConfigKeys.SEED_16_CHARACTER));
        try {
//            params.put("sign", Util.getMD5Str(md5.toString()));
//            params.put("os", "2");
            map.put("version", AppUtil.getAppVersionName(context));
            map.put("imei", AppUtil.getIMEI(Latte.getApplicationContext()));
            map.put("deviceBrand", AppUtil.getBrand());
            map.put("systemModel", AppUtil.getModel());
            params.put("verJson",new Gson().toJson(map));
        } catch (Exception e) {
            e.printStackTrace();
        }
        RestClient.builder().url(url)
                .params(params)
                .success(new ISuccess() {
                    @Override
                    public void onSuccess(String response) {
                        onHttpFinish(response);
                    }
                })
                .error((code, msg) -> onHttpError(msg))
                .failure(() -> onHttpError(Latte.getApplicationContext().getString(R.string.no_network_hint)))
                .loader(context, loaderStyle)
                .build()
                .post();
    }

    /**
     * 上传单个文件
     */
    public void upload(final String url, Map map, File file, WeakHashMap<String, Object> params, final Context
            context) {
        map.put("version", AppUtil.getAppVersionName(context));
        params.put("os", "2");
        params.put("version", AppUtil.getAppVersionName(context));
        params.put("imei", AppUtil.getIMEI(context));
        params.put("device_brand", AppUtil.getBrand());
        params.put("system_model", AppUtil.getModel());
        StringBuilder sb = new StringBuilder();
        Set<String> keySet = map.keySet();
        Iterator<String> iterator = keySet.iterator();
        StringBuilder md5 = new StringBuilder();
        while (iterator.hasNext()) {
            String key = iterator.next();
            sb.append(key + "=" + map.get(key) + ",");
            md5.append(map.get(key));
        }
        md5.append((String) Latte.getConfiguration(ConfigKeys.SEED_16_CHARACTER));

        try {
            params.put("sign", Util.getMD5Str(md5.toString()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        RestClient.builder()
                .url(url)
                .file(file)
                .params(params)
                .success(response -> onHttpFinish(response))
                .error((code, msg) -> onHttpError(msg))
                .failure(() -> onHttpError(context.getString(R.string.no_network_hint)))
                .build()
                .upload();

    }

    /**
     * 上传多个文件
     */
    public void uploadFiles(final String url, Map map, Map file, MultipartBody.Builder builder, final Context
            context) {
        Log.d("TAG", "uploadFiles: url="+url);
        map.put("version", AppUtil.getAppVersionName(context));
        builder.addFormDataPart("os", "2");
        builder.addFormDataPart("version", AppUtil.getAppVersionName(context));
        builder.addFormDataPart("imei", AppUtil.getIMEI(context));
        builder.addFormDataPart("device_brand", AppUtil.getBrand());
        builder.addFormDataPart("system_model", AppUtil.getModel());
        StringBuilder sb = new StringBuilder();
        Set<String> keySet = map.keySet();
        Iterator<String> iterator = keySet.iterator();
        StringBuilder md5 = new StringBuilder();
        while (iterator.hasNext()) {
            String key = iterator.next();
            sb.append(key + "=" + map.get(key) + ",");
            md5.append(map.get(key));
        }
        md5.append((String) Latte.getConfiguration(ConfigKeys.SEED_16_CHARACTER));

        try {
            builder.addFormDataPart("sign", Util.getMD5Str(md5.toString()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> keySetFile = file.keySet();
        Iterator<String> iteratorFile = keySetFile.iterator();
        while (iteratorFile.hasNext()) {
            String key = iteratorFile.next();
            File uploadFile = new File(file.get(key).toString());
            builder.addFormDataPart(key, uploadFile.getName(), RequestBody.create(MediaType.parse(MultipartBody.FORM.toString()), uploadFile));
        }
        RestClient.builder()
                .url(url)
                .params(new WeakHashMap<>())
                .body(builder.build())
                .success(response -> onHttpFinish(response))
                .error((code, msg) -> onHttpError(msg))
                .failure(() -> onHttpError(context.getString(R.string.no_network_hint)))
                .build()
                .post();

    }

    public void get(String url, WeakHashMap<String, Object> params, final Context context) {
        RestClient.builder()
                .url(url)
                .params(params)
                .success(response -> onHttpFinish(response))
                .error((code, msg) -> onHttpError(msg))
                .failure(() -> onHttpError(context.getString(R.string.no_network_hint)))
                .build()
                .get();
    }


    public abstract void onHttpFinish(String s);

    public abstract void onHttpError(String s);
}
