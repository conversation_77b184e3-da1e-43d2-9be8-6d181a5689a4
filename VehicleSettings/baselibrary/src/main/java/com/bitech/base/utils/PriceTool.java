package com.bitech.base.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * Created by LvMeng on 15/3/10.
 */
public class PriceTool {

    /**
     * 两个商品相加的总和
     * @param p1    商品1的价格
     * @param p2    商品2的价格
     * @return      总和
     */
    public static double add(double p1,double p2){
        return new BigDecimal(p1).add(new BigDecimal(p2)).doubleValue();
    }

    /**
     * 获取价格 × 数量
     * @param price 价格
     * @param num   数量
     * @return      总价
     */
    public static double totalPrice(double price,int num){
        return new BigDecimal(price).multiply(new BigDecimal(num)).doubleValue();
    }

    /**
     * 获取保留两位小数的价格
     * @param price 系统价格
     * @return      人类可见的价格
     */
    public static String getHumanityPrice(double price){
        return new DecimalFormat("0.00").format(new BigDecimal(price).divide(new BigDecimal(100),2, BigDecimal.ROUND_UP));
    }
}
