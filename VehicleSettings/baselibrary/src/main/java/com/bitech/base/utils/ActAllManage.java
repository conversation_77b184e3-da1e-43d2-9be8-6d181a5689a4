package com.bitech.base.utils;

import android.app.Activity;

import java.util.HashMap;
import java.util.Map;

public class ActAllManage {

    public static Map<String,Activity> map = new HashMap<>();


    /**
     * 将Activity加入到管理
     * @param name      activity 姓名
     * @param activity  activity 实例
     */
    public static void add(String name,Activity activity){
        map.put(name,activity);
    }

    /**
     * 关闭所有activity
     */
    public static void deleteAll(){
        for (String key : map.keySet()){
            Activity a = map.get(key);
            if (a != null){
                a.finish();
            }
        }
        map.clear();
    }

    /**
     * 清空Activity集合
     */
    public static void clear(){
        map.clear();
    }
}
