package com.bitech.base.utils;

import android.content.Context;
import android.text.TextUtils;
import android.text.format.Time;

import com.bitech.base.R;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Formatter;
import java.util.Locale;

public class DateUtils {

    private static String mTimeFormat;

    public static String getFormatDateStr(Date date, String format) {
        if (format == null || format == "") {
            format = "yyyy-MM-dd";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.format(date);
        return sdf.format(date);
    }

    public static String getFormatDateStr(Date date) {
        return getFormatDateStr(date, null);
    }

    public static String format(Date value) {
        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (value != null) {
            return dateFormatter.format(value);
        } else {
            return "";
        }
    }

    public static String getDateTime(Long time) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date date = new Date(time);
        return simpleDateFormat.format(date);
    }

    public static String getDateTime1(Long time) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(time);
        return simpleDateFormat.format(date);
    }


    public static String formatDate(Date value) {
        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        if (value != null) {
            return dateFormatter.format(value);
        } else {
            return "";
        }
    }

    public static String formatDynamicDate(Context context, long timestamp) {
        int day7 = 7 * 24 * 60 * 60 * 1000;
        int day3 = 3 * 24 * 60 * 60 * 1000;
        int day2 = 2 * 24 * 60 * 60 * 1000;
        int day1 = 24 * 60 * 60 * 1000;
        int hour1 = 60 * 60 * 1000;
        int minute30 = 30 * 60 * 1000;
        int minute1 = 60 * 1000;

        Time oldTime = new Time();
        oldTime.set(timestamp);

        long now = System.currentTimeMillis();
        long msInterval = Math.abs(now - timestamp);
        if (msInterval >= day7) {
            return getDate(timestamp);
        } else if (msInterval >= day3) {
            return context.getResources().getString(R.string.time_fmt_4days);
        } else if (msInterval >= day2) {
            String fmt = context.getResources().getString(R.string.time_fmt_2days);
            return String.format(fmt, oldTime.hour, oldTime.minute);
        } else if (msInterval >= day1) {
            String fmt = context.getResources().getString(R.string.time_fmt_1days);
            return String.format(fmt, oldTime.hour, oldTime.minute);
        } else if (msInterval >= hour1) {
            int hour = (int) (msInterval / hour1);
            String fmt = context.getResources().getString(R.string.time_fmt_hour);
            return String.format(fmt, hour);
        } else if (msInterval >= minute30) {
            return context.getResources().getString(R.string.time_fmt_hour_half);
        } else if (msInterval >= minute1) {
            int minute = (int) (msInterval / minute1);
            String fmt = context.getResources().getString(R.string.time_fmt_minute);
            return String.format(fmt, minute);
        } else {
            return context.getResources().getString(R.string.time_fmt_now);
        }
    }

    public static String getDate(long time) {
        SimpleDateFormat localDateFmt = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);
        Date date = new Date(time);
        String localTime = localDateFmt.format(date);
        return localTime;
    }

    public static Date getStringToDateTime(String str) {
        if (TextUtils.isEmpty(str)) {
            return new Date(System.currentTimeMillis());
        } else {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date();
            try {
                date = simpleDateFormat.parse(str);
                return date;
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    //获取当前完整的日期和时间
    public static String getNowDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy:MM:dd HH:mm:ss");
        return sdf.format(new Date());
    }

    //获取当前日期
    public static String getNowDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        return sdf.format(new Date());
    }

    //获取当前时间
    public static String getNowTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        return sdf.format(new Date());
    }

    //获取当前时间不包含秒
    public static String getNowTimeM(Context context) {
        Calendar calendar = Calendar.getInstance();
        // 设置时区，可以根据需要设置为其他时区
//        calendar.setTimeZone(java.util.TimeZone.getTimeZone("GMT"));
        int minute = calendar.get(Calendar.MINUTE);
        int hour;
        if (!is24HourFormat(context)) {
            hour = calendar.get(Calendar.HOUR) == 0 ? 12 : calendar.get(Calendar.HOUR);
            int am_pm = calendar.get(Calendar.AM_PM);
            if (am_pm == Calendar.AM) {
                mTimeFormat = "AM";
            } else {
                mTimeFormat = "PM";
            }
        } else {
            hour = calendar.get(Calendar.HOUR_OF_DAY);
        }
        String time = (hour >= 10 ? hour : "0" + hour) + " " + ((minute >= 10) ? minute : ("0" + minute));
        // 获取当前时间
        return time;
    }

    public static boolean is24HourFormat(Context context) {
        return android.text.format.DateFormat.is24HourFormat(context);
    }

    public static String getTimeFormat() {
        return mTimeFormat;
    }

    //转换当前时间不包含时
    public static String parseTime(int oldTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("mm:ss");// 时间格式
        String newTime = sdf.format(new Date(oldTime));
        return newTime;
    }

    //获取当前日期(精确到毫秒)
    public static String getNowTimeDetail() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss:SSS");
        return sdf.format(new Date());
    }

    //获取当前日期是星期几
    public static String getWeekOfDate(Date date) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    //将时间字符串转为时间戳字符串
    public static String getStringTimestamp(String time) {
        String timestamp = null;

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Long longTime = sdf.parse(time).getTime() / 1000;
            timestamp = Long.toString(longTime);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timestamp;
    }

    //将长整型时间转为为分秒
    public static String time(long millionSeconds) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(millionSeconds);
        return simpleDateFormat.format(c.getTime());
    }

    //将长整型时间转为时分 24小时
    public static String lontToTime(long millionSeconds, boolean is24time) {
        if (is24time) {
            return lontToTime(millionSeconds);
        } else {
            return lontTo12Time(millionSeconds);
        }
    }

    //将长整型时间转为时分 24小时
    public static String lontToTime(long millionSeconds) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(millionSeconds);
        return simpleDateFormat.format(c.getTime());
    }

    //将长整型时间转为时分 12小时
    public static String lontTo12Time(long millionSeconds) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("hh:mm a");
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(millionSeconds);
        return simpleDateFormat.format(c.getTime());
    }

    //将长度转换为时间
    public static StringBuilder mFormatBuilder = new StringBuilder();
    public static Formatter mFormatter = new Formatter(mFormatBuilder, Locale.getDefault());

    public static String stringForTime(int timeMs) {
        int totalSeconds = timeMs / 1000;

        int seconds = totalSeconds % 60;
        int minutes = (totalSeconds / 60) % 60;
        int hours = totalSeconds / 3600;

        mFormatBuilder.setLength(0);
        if (hours > 0) {
            return mFormatter.format("%d:%02d:%02d", hours, minutes, seconds).toString();
        } else {
            return mFormatter.format("%02d:%02d", minutes, seconds).toString();
        }
    }

    /**
     * 定义一个方法用来格式化获取到的时间
     */
    public static String formatTime(int time) {
        if (time / 1000 % 60 < 10) {
            return time / 1000 / 60 + ":0" + time / 1000 % 60;

        } else {
            return time / 1000 / 60 + ":" + time / 1000 % 60;
        }

    }

}
