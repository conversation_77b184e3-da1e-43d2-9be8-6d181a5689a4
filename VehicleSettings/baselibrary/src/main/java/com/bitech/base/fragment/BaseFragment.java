package com.bitech.base.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.fragment.app.Fragment;
import xyz.iyer.cloudposlib.zprogresshud.ZProgressHUD;

public abstract class BaseFragment extends Fragment{

    protected Activity mContext;
    private ZProgressHUD zp;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = getActivity();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = onCreateView(inflater, container);
        initView();
        setListener();
        return rootView;
    }

    protected abstract View onCreateView(LayoutInflater inflater, ViewGroup container);

    protected abstract void initView();

    protected abstract void setListener();

    protected void showProgress(String message) {
        hideProgress();
        zp = new ZProgressHUD(getActivity());
        if (!TextUtils.isEmpty(message)) {
            zp.setMessage(message);
        } else {
            zp.setMessage("加载中");
        }
        zp.show();
    }

    protected void hideProgress() {
        if (zp != null && zp.isShowing()) {
            zp.dismiss();
        }
    }

    protected void onBackPressedSupport(){
        mContext.onBackPressed();
        mContext.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }
}
