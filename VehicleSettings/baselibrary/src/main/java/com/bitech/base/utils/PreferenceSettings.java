package com.bitech.base.utils;

import android.content.Context;
import android.content.SharedPreferences.Editor;

import java.util.ArrayList;
import java.util.List;

public final class PreferenceSettings {
    private static final String PREFERENCE_NAME = "preference.xml";
    private static final int FILE_MODE = Context.MODE_PRIVATE;

    /**
     * 缓存变量。
     */
    public enum SettingsField {
        ROW_3_CENTER, FRONT_ROW, CYCLE_MODE, AIR_CLEAN_OPEN, AQS_SW_CONDITION,
        // 快捷方式控制
        Q_CENTRAL_LOCKING_STATUS,Q_REAR_TAIL_GATE_STATUS, Q_REAR_MIRROR_STATUS,Q_WINDOW_STATUS,Q_CLOSE_UNLOCK_STATUS,
        R_DMS_CAMERA_STATUS, R_FATIGUE_DETECTION_STATUS, R_DISTRACTION_STATUS, R_CALL_STATUS, R_DRINK_STATUS, R_SEAT_HEAT_STATUS, R_SEAT_VENTILATION_STATUS, R_SIGHT_UNLOCK_STATUS, R_GREET_STATUS, R_SMOKE_STATUS,

        USER_NAME
    }

    public static int getSettingInt(Context context, SettingsField field, int defaultValue) {
        return context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).getInt(field.name(), defaultValue);
    }

    public static void setSettingInt(Context context, SettingsField field, int value) {
        Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
        settingEditor.putInt(field.name(), value);
        settingEditor.commit();
    }

    public static String getSettingString(Context context, SettingsField field, String defaultValue) {
        return context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).getString(field.name(), defaultValue);
    }

    public static String getSettingString(Context context, String name, String defaultValue) {
        return context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).getString(name, defaultValue);
    }

    public static void setSettingString(Context context, SettingsField field, String value) {
        Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
        settingEditor.putString(field.name(), value);
        settingEditor.commit();
    }

    public static void setSettingString(Context context, String name, String value) {
        Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
        settingEditor.putString(name, value);
        settingEditor.commit();
    }

    public static long getSettingLong(Context context, SettingsField field, long defaultValue) {
        return context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).getLong(field.name(), defaultValue);
    }

    public static void setSettingLong(Context context, SettingsField field, long value) {
        Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
        settingEditor.putLong(field.name(), value);
        settingEditor.commit();
    }

    public static Float getSettingFloat(Context context, SettingsField field, Float defaultValue) {
        return context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).getFloat(field.name(), defaultValue);
    }

    public static void setSettingFloat(Context context, SettingsField field, Float value) {
        Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
        settingEditor.putFloat(field.name(), value);
        settingEditor.commit();
    }


    public static boolean getSettingBoolean(Context context, SettingsField field, boolean defaultValue) {
        return context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).getBoolean(field.name(), defaultValue);
    }

    public static void setSettingBoolean(Context context, SettingsField field, boolean value) {
        Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
        settingEditor.putBoolean(field.name(), value);
        settingEditor.commit();
    }

    public static void setArrayInt(Context context, SettingsField field, List<Integer> array) {
        if (array != null && array.size() != 0) {
            StringBuilder sBuilder = new StringBuilder();
            for (Integer item : array) {
                sBuilder.append(item);
                sBuilder.append(",");
            }
            Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
            settingEditor.putString(field.name(), sBuilder.toString());
            settingEditor.commit();
        }
    }

    public static List<Integer> getArrayInt(Context context, SettingsField field, String defaultValue) {
        List<Integer> array = new ArrayList<Integer>();
        String s = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).getString(field.name(), defaultValue);
        if (s != null) {
            String[] ids = s.split(",");
            for (int i = 0; i < ids.length; i++) {
                int val = Integer.parseInt(ids[i]);
                array.add(val);
            }
        }
        return array;
    }

    public static void remove(Context context, SettingsField field) {
        Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
        settingEditor.remove(field.name());
        settingEditor.commit();
    }

    public static void clear(Context context) {
        Editor settingEditor = context.getSharedPreferences(PREFERENCE_NAME, FILE_MODE).edit();
        settingEditor.clear().commit();
    }
}
