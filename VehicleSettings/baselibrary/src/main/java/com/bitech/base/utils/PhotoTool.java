package com.bitech.base.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.UUID;


/**
 * Created by agui on 15/2/11.
 */
public class PhotoTool {
    private final int chooseFromAlbum;
    private final int chooseFromCamera;
    private final int cropBigPicture;

    private Uri imageUri;
    private final Activity context;
    private Bitmap temBitmap;
    private int degree;

    public PhotoTool(Activity context, int chooseFromAlbum, int chooseFromCamera, int cropBigPicture) {
        this.context = context;
        this.chooseFromAlbum = chooseFromAlbum;
        this.chooseFromCamera = chooseFromCamera;
        this.cropBigPicture = cropBigPicture;
        setPhotoPath();
    }

    public boolean isSelected() {
        return new File(imageUri.getPath()).exists();
    }

    /**
     * 获取原图片存储路径
     * 备注：默认存在ImageLoad的缓存文件夹
     *
     * @return
     */
    public String getPhotoPath() {

        return imageUri.getPath();
    }

    /**
     * 设置图片的路径
     *
     * @param
     */
    private void setPhotoPath() {
        String path = context.getCacheDir().getPath();
        String name = UUID.randomUUID() + ".jpg";

        File file = new File(path);
        if (file.exists() && !file.isDirectory()) {
            file.delete();
        }
        if (!file.exists()) {
            file.mkdir();
        }
        imageUri = Uri.parse("file://" + path + "/" + name);
    }

    /**
     * 获取Uri的路径
     *
     * @param uri
     */
    public String getPhotoPathByUri(Uri uri) {
        Cursor cursor = null;
        try {
            String[] proj = {MediaStore.Images.Media.DATA};
            cursor = context.getContentResolver().query(uri, proj, null, null, null);
            int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
            cursor.moveToFirst();
            return new File(cursor.getString(column_index)).getPath();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    /**
     * 从相册获取照片
     */
    @SuppressLint("InlinedApi")
    public void getPhotoFromAlbum() {
        if (Build.VERSION.SDK_INT < 19) {
            Intent intent = new Intent(Intent.ACTION_GET_CONTENT, null);
            intent.setType("image/*");
            intent.putExtra("crop", "true");
            intent.putExtra("aspectX", 1);
            intent.putExtra("aspectY", 1);
            intent.putExtra("outputX", 600);
            intent.putExtra("outputY", 600);
            intent.putExtra("scale", true);
            intent.putExtra("return-data", false);
            intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
            intent.putExtra("outputFormat", imageUri.getPath());
            intent.putExtra("noFaceDetection", false); // no face detection
            context.startActivityForResult(intent, chooseFromAlbum);
        } else {
            Intent intent = new Intent();
            intent.setType("image/*");
            intent.setAction(Intent.ACTION_PICK);
            context.startActivityForResult(intent, chooseFromAlbum);
        }
    }

    /**
     * 从相机获取
     */
    public void getPhotoFromCamera() {
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);//action is capture
        intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
        context.startActivityForResult(intent, chooseFromCamera);
    }

    //营业执照进入相册
    public void enterAlbum() {
        Intent intent = new Intent();
        intent.setType("image/*");
        intent.setAction(Intent.ACTION_GET_CONTENT);
        context.startActivityForResult(intent, chooseFromAlbum);
    }

    //营业执照进入相机
    public void enterCamera() {
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
        context.startActivityForResult(intent, chooseFromCamera);
    }

    /**
     * 裁剪图片
     *
     * @param uri
     * @param outputX
     * @param outputY
     * @param requestCode
     */
    @SuppressLint("InlinedApi")
    public void cropImageUri(Uri uri, int outputX, int outputY, int requestCode) {
        Intent intent = new Intent("com.android.camera.action.CROP");
        intent.setDataAndType(uri, "image/*");
        intent.putExtra("crop", "true");// 才能出剪辑的小方框，不然没有剪辑功能，只能选取图片
        intent.putExtra("aspectX", 1); // 放大缩小比例的X
        intent.putExtra("aspectY", 1);// 放大缩小比例的X   这里的比例为：   1:1
        intent.putExtra("outputX", outputX);  //这个是限制输出图片大小
        intent.putExtra("outputY", outputY);
        intent.putExtra("scale", true);
        intent.putExtra("return-data", false);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, uri);
        intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
        intent.putExtra("noFaceDetection", true); // no face detection
        context.startActivityForResult(intent, requestCode);
    }

    /**
     * 复制图片到缓存文件夹
     *
     * @param path
     */
    private void copyFile(String path) {
        try {
            InputStream in = new FileInputStream(new File(path));
            OutputStream out = new FileOutputStream(new File(imageUri.getPath()));
            byte[] b = new byte[1024];
            int len;
            while ((len = in.read(b)) > 0) {
                out.write(b, 0, len);
                out.flush();
            }
            in.close();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据路径获取Bitm
     * 说明：当调用getPhotoFromCamera()获取图像时，在onActivityResult()中用此获取图片
     *
     * @param
     * @return
     */
    public Bitmap getBitmap(boolean isReset) {
        if (imageUri == null) {
            return null;
        }

        if (isReset && temBitmap != null && !temBitmap.isRecycled()) {
            temBitmap.recycle();
        }
        temBitmap = BitmapFactory.decodeFile(imageUri.getPath());
        return temBitmap;
    }


    public int readPictureDegree(String path) {
        degree = 0;
        try {
            ExifInterface exifInterface = new ExifInterface(path);
            int orientation = exifInterface.getAttributeInt(
                    ExifInterface.TAG_ORIENTATION,
                    ExifInterface.ORIENTATION_NORMAL);
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    degree = 90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    degree = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    degree = 270;
                    break;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return degree;
    }

    public Bitmap rotateBitmapByDegree(Bitmap bm, int degree) {

        Bitmap returnBm = null;
        // 根据旋转角度，生成旋转矩阵
        Matrix matrix = new Matrix();
        matrix.postRotate(degree);
        try {
            // 将原始图片按照旋转矩阵进行旋转，并得到新的图片
            returnBm = Bitmap.createBitmap(bm, 0, 0, bm.getWidth(), bm.getHeight(), matrix, true);

        } catch (OutOfMemoryError e) {
        }
        if (returnBm == null) {
            returnBm = bm;
        }
        if (bm != returnBm) {
            bm.recycle();
        }
        return returnBm;
    }

    /**
     * @param requestCode 请求码
     * @param resultCode  响应码
     * @param data        数据
     * @return true 获取剪切图成功 false 还没获取到剪切图
     */
    @SuppressLint("InlinedApi")
    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode != Activity.RESULT_OK) {//result is not correct
            File f = new File(imageUri.getPath());
            return false;
        }

        if (requestCode == chooseFromAlbum) {
            if (cropBigPicture == -1) {
                copyFile(getPath(context, data.getData()));
                zoomImageAuto(imageUri.getPath());
                return true;

            } else {
                if (Build.VERSION.SDK_INT >= 19) {
                    copyFile(getPath(context, data.getData()));
                    cropImageUri(imageUri, 600, 600, cropBigPicture);
                    return false;
                }
                return true;
            }
        } else if (requestCode == chooseFromCamera) {
            if (cropBigPicture == -1) {
                degree = readPictureDegree(imageUri.getPath());
                zoomImageAuto(imageUri.getPath());
                temBitmap = rotateBitmapByDegree(BitmapFactory.decodeFile(imageUri.getPath()), degree);
                bitmapToFile(temBitmap);
                return true;
            } else {
                cropImageUri(imageUri, 600, 600, cropBigPicture);
            }
            return false;
            //decodeUriAsBitmap(imageUri);
        } else {
            return requestCode == cropBigPicture;
        }
    }


    private boolean zoomImageAuto(String sourcePath) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(sourcePath, options);
        if (options.outWidth <= 0) {
            return false;
        }
        options.inJustDecodeBounds = false;
        int sampleSize = options.outWidth / 600;
        if (sampleSize > 1) {
            options.inSampleSize = sampleSize;
        } else {
            options.inSampleSize = 1;
        }
        try {
            Bitmap bmp = BitmapFactory.decodeFile(sourcePath, options);
            bitmapToFile(bmp);
        } catch (OutOfMemoryError error) {
            // TODO: handle exception
        }
        return false;
    }


    private boolean bitmapToFile(Bitmap bmp) {
        if (bmp != null) {
            try {
                FileOutputStream outStream = new FileOutputStream(new File(imageUri.getPath()));
                if (bmp.compress(Bitmap.CompressFormat.JPEG, 80, outStream)) {
                    outStream.flush();
                    outStream.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            bmp.recycle();
            bmp = null;
            System.gc();
            return true;
        }
        return false;
    }


    @SuppressLint("NewApi")
    private String getPath(final Context context, final Uri uri) {

        final boolean isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;

        // DocumentProvider
        if (isKitKat && DocumentsContract.isDocumentUri(context, uri)) {
            // ExternalStorageProvider
            if (isExternalStorageDocument(uri)) {
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] split = docId.split(":");
                final String type = split[0];

                if ("primary".equalsIgnoreCase(type)) {
                    return Environment.getExternalStorageDirectory() + "/"
                            + split[1];
                }
            }
            // DownloadsProvider
            else if (isDownloadsDocument(uri)) {
                final String id = DocumentsContract.getDocumentId(uri);
                final Uri contentUri = ContentUris.withAppendedId(
                        Uri.parse("content://downloads/public_downloads"),
                        Long.valueOf(id));

                return getDataColumn(context, contentUri, null, null);
            }
            // MediaProvider
            else if (isMediaDocument(uri)) {
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] split = docId.split(":");
                final String type = split[0];

                Uri contentUri = null;
                if ("image".equals(type)) {
                    contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
                } else if ("video".equals(type)) {
                    contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
                } else if ("audio".equals(type)) {
                    contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
                }

                final String selection = "_id=?";
                final String[] selectionArgs = new String[]{split[1]};

                return getDataColumn(context, contentUri, selection,
                        selectionArgs);
            }
        }
        // MediaStore (and general)
        else if ("content".equalsIgnoreCase(uri.getScheme())) {

            // Return the remote address
            if (isGooglePhotosUri(uri)) {
                return uri.getLastPathSegment();
            }

            return getDataColumn(context, uri, null, null);
        }
        // File
        else if ("file".equalsIgnoreCase(uri.getScheme())) {
            return uri.getPath();
        }

        return null;
    }

    private String getDataColumn(Context context, Uri uri, String selection,
                                 String[] selectionArgs) {

        Cursor cursor = null;
        final String column = "_data";
        final String[] projection = {column};

        try {
            cursor = context.getContentResolver().query(uri, projection,
                    selection, selectionArgs, null);
            if (cursor != null && cursor.moveToFirst()) {
                final int index = cursor.getColumnIndexOrThrow(column);
                return cursor.getString(index);
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    private static boolean isExternalStorageDocument(Uri uri) {
        return "com.android.externalstorage.documents".equals(uri
                .getAuthority());
    }

    private static boolean isDownloadsDocument(Uri uri) {
        return "com.android.providers.downloads.documents".equals(uri
                .getAuthority());
    }

    private static boolean isMediaDocument(Uri uri) {
        return "com.android.providers.media.documents".equals(uri
                .getAuthority());
    }

    private static boolean isGooglePhotosUri(Uri uri) {
        return "com.google.android.apps.photos.content".equals(uri
                .getAuthority());
    }

    /**
     * 获取缩略图
     *
     * @param width  缩放后的宽
     * @param height 缩放后的高
     * @return
     */
    public Bitmap getThumbnail(float width, float height) {
//        if (TextUtils.isEmpty(temFile)) {
//            return null;
//        }
//
//        String name = Calendar.getInstance().getTimeInMillis() + ".jpg";
//        thumbFile = new File(TEM_PATH, name).getPath();
//
//        FileOutputStream out = null;
//        try {
//            out = new FileOutputStream(thumbFile);
//            // 获取图形文件
//            Bitmap bitmapOrg = BitmapFactory.decodeFile(temFile);
//            int oldWidth = bitmapOrg.getWidth();
//            int oldHeight = bitmapOrg.getHeight();
//            // 定义矩阵对象
//            Matrix matrix = new Matrix();
//            // 缩放原图
//            matrix.postScale(width / bitmapOrg.getWidth(), height / bitmapOrg.getHeight());
//            Bitmap reBitmap = Bitmap.createBitmap(bitmapOrg, 0, 0,
//                    oldWidth, oldHeight, matrix, true);
//
//            reBitmap.compress(Bitmap.CompressFormat.JPEG, 100, out); // bmp is your Bitmap instance
//            bitmapOrg.recycle();
//            reBitmap.recycle();
//            return BitmapFactory.decodeFile(thumbFile);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                if (out != null) {
//                    out.close();
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
        return null;

    }

    /**
     * recycle Bitmap
     *
     * @return 是否删除成功
     */
    public void recycle() {
        if (temBitmap != null && !temBitmap.isRecycled()) {
            temBitmap.recycle();
        }
    }

//    /**
//     * 清除临时文件
//     * 注意：只有在使用getPhotoPath()默认缓存路径的图片才会被清理
//     */
//    public void cleanImage() {
//        File file = new File(TEM_PATH);
//        if (file != null && file.isDirectory()) {
//            File[] files = file.listFiles();
//            for (File f : files) {
//                f.delete();
//            }
//        }
//    }


}