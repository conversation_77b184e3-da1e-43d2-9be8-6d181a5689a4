package com.bitech.base.activity;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.bitech.base.R;

import java.util.List;

import xyz.iyer.cloudposlib.zprogresshud.ZProgressHUD;

public abstract class BaseActivity extends AppCompatActivity {
    protected Activity mContext;
    private ZProgressHUD zp;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = this;
        setContentView(getLayoutResId());
        initView();
        setListener();
    }


    @Override
    protected void onResume() {
        super.onResume();
    }

    /**
     * 加载布局文件
     *
     * @return int
     */
    protected abstract View getLayoutResId();

    protected abstract void initView();

    protected abstract void setListener();

    protected void replaceFragment(Fragment fragment, int layoutId) {
        if (!isFinishing()) {
            FragmentManager fragmentManager = getSupportFragmentManager();
            FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
            fragmentTransaction.setCustomAnimations(R.anim.slide_up, R.anim.slide_out);
            //fragmentTransaction.setCustomAnimations(R.anim.fade_in, R.anim.fade_out);
            fragmentTransaction.replace(layoutId, fragment);
            fragmentTransaction.addToBackStack(null);
            fragmentTransaction.commit();
            //overridePendingTransition(R.anim.nav_default_enter_anim,R.anim.nav_default_exit_anim);
        }
    }

    protected void showProgress(String message) {
        hideProgress();
        zp = new ZProgressHUD(this);
        if (!TextUtils.isEmpty(message)) {
            zp.setMessage(message);
        } else {
            zp.setMessage("加载中");
        }
        zp.show();
    }

    protected void hideProgress() {
        if (zp != null && zp.isShowing()) {
            zp.dismiss();
        }
    }
}
