apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-kapt'

android {
    compileSdk 34
    namespace "com.bitech.base"

    defaultConfig {
        minSdk 26
        targetSdkVersion 34

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    api 'androidx.appcompat:appcompat:1.2.0'
    api project(':zProgressHUD')
    api 'com.google.code.gson:gson:2.11.0'
    api files('libs/eventbus.jar')
    //网络请求
    api 'com.squareup.okio:okio:1.9.0'
    api 'com.squareup.okhttp3:okhttp:3.11.0'
    api 'com.squareup.retrofit2:retrofit:2.3.0'
    api 'com.squareup.retrofit2:converter-scalars:2.3.0'
    //Loader依赖
    api 'com.wang.avi:library:2.1.3'

    //工具包
    api 'com.blankj:utilcode:1.7.1'

    //Log
    api 'com.orhanobut:logger:2.1.1'

    //状态栏
    api 'com.readystatesoftware.systembartint:systembartint:1.0.3'
}
