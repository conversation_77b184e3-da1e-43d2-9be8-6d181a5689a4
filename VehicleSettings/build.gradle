// Top-level build file where you can add configuration options common to all sub-projects/modules.
 
buildscript {
    ext {
        kotlin_version = '1.8.0'
    }
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven {
            allowInsecureProtocol = true
            url "http://************:8081/repository/Chery_platform/"
        }
        google()
        mavenCentral()
    }
    dependencies {
        //gradle版本建议使用4.1.3
        classpath "com.android.tools.build:gradle:8.5.0"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.4.1'
//        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.3.1'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url "https://jitpack.io" }
        maven {
            allowInsecureProtocol = true
            url "http://************:8081/repository/Chery_platform/"
        }
        google()
        mavenCentral()
        flatDir { dirs("aars") }

        //本地引用时指定platformLib aar路径，后面使用远程依赖
        flatDir {
            dirs 'libs'
        }
    }
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions.jvmTarget = "17"
    }

    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KaptGenerateStubs).configureEach {
        kotlinOptions.jvmTarget = "17"
    }

    tasks.withType(JavaCompile).configureEach { javaCompile ->
        javaCompile.doFirst {
            def originalFiles = javaCompile.options.bootstrapClasspath?.files
            if (originalFiles != null) {
                def newFiles = [file("./build_libs/framework.jar")] + originalFiles
                javaCompile.options.bootstrapClasspath = files(newFiles)
            }
        }
    }


}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    annotation = "1.3.0"
    supportV4 = "28.0.0"
    coroutinesCore = "1.6.4"
    coroutinesAndroid = "1.6.4"
    junit = "4.13.2"
    junitExt = "1.1.5"
    espressoCore = "3.5.1"

    kotlin_version = "1.8.0"
    appcompat = "1.6.1"
    coreKtx = "1.9.0"
    constraintLayout = "2.1.3"
    fragment = "1.5.1"
    cardView = "1.0.0"
    recyclerview = "1.2.1"
    material = "1.6.1"
    lifecycleViewModel = "2.6.2"
    appStartup = "1.1.1"
    hilt_version = "2.44"
    kotlinCoroutines = "1.6.4"
    timber = "5.0.1"
    binding_version = "1.1.9"
    bindables = "1.1.0"
    glide = "4.13.2"
    utilCode = "1.31.1"
    unpeekLivedata = "7.8.0"
    lottieVersion = "5.2.0"
}
