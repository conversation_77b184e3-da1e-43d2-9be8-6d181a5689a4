def androidxcorektx = "1.3.0"
def annotationVersion = "1.3.0"
def supportV4Version = "28.0.0"
def coroutinesCoreVersion = "1.6.4"
def coroutinesAndroidVersion = "1.6.4"
def appcompatVersion = "1.3.1"
def constraintlayoutVersion = "2.1.0"
def materialVersion = "1.3.0"

apply plugin: 'com.android.application'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-kapt'

android {
    compileSdk 34
    namespace "com.bitech.vehiclesettings"

    defaultConfig {
        applicationId "com.bitech.vehiclesettings"
        minSdkVersion 30
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.1"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding = true
        dataBinding = true
    }

    tasks.withType(JavaCompile).configureEach {
        options.fork = true
        options.forkOptions.jvmArgs += [
                '--add-exports=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED',
                '--add-exports=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED',
                '--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED']
    }

    signingConfigs {
        release {
            keyAlias 'platform'
            keyPassword 'android'
            storeFile file(rootProject.projectDir.path + "/keystore/platform-MTK8678.keystore")
            storePassword 'android'
        }
        debug {
            keyAlias 'platform'
            keyPassword 'android'
            storeFile file(rootProject.projectDir.path + "/keystore/platform-MTK8678.keystore")
            storePassword 'android'
        }
//        release {
//            keyAlias 'megaplatform'
//            keyPassword '@megatronix'
//            storeFile file(rootProject.projectDir.path + "/keystore/chery_platform.keystore")
//            storePassword '@megatronix'
//        }
//        debug {
//            keyAlias 'megaplatform'
//            keyPassword '@megatronix'
//            storeFile file(rootProject.projectDir.path + "/keystore/chery_platform.keystore")
//            storePassword '@megatronix'
//        }
    }

    buildTypes {
        release {
            minifyEnabled false
            //去除无用资源
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }

        debug {
            minifyEnabled false
            //去除无用资源
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures {
        aidl = true
    }
    sourceSets {
        main {
            aidl.srcDirs = ['src/main/aidl']
        }
    }
}

dependencies {
    /*LiveEventBus*/
    implementation 'io.github.jeremyliao:live-event-bus-x:1.8.0'
    /*车模*/
    implementation(name: 'vehicle-sdk-release-1.0.0', ext: 'aar')
    implementation(name: 'LionMediaApi-1.7.6-SNAPSHOT', ext: 'aar')
    implementation(name: 'lion-api-1.1.9-RELEASE', ext: 'aar')
    //implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'])
    implementation 'androidx.bluetooth:bluetooth:1.0.0-alpha02'
    implementation 'com.android.framwork:carFramwork:0.0.1@jar'
    implementation project(':baselibrary')
//    implementation files('libs\\msgcenter.jar')
    implementation files('libs\\lib_util.jar')
    implementation files('libs\\lib_tts_base.jar')
    implementation files('libs\\lib_tts_aidl.jar')
    implementation 'com.chery.logger:logger:0.0.1@aar'
    implementation files('libs/account-sdk-sp-1.0.79-SNAPSHOT.aar')
    implementation files('libs\\icvp_library_2025_07_11.jar')
    implementation files('libs\\chery_vdbus_v2.2.jar')
    compileOnly files('libs/android.car.jar')
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'androidx.room:room-common:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    implementation files('libs\\adaptor.jar')
    implementation "androidx.slice:slice-view:1.0.0"
    implementation "androidx.slice:slice-builders-ktx:1.0.0-alpha6"
    implementation 'androidx.compose.ui:ui-geometry-android:1.7.8'
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
    implementation 'androidx.compose.ui:ui-graphics-android:1.7.8'
    kapt "androidx.room:room-compiler:2.6.1" // 对于 Kotlin
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    //Google Material控件,以及迁移到AndroidX下一些控件的依赖
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.1.0'
    implementation 'androidx.annotation:annotation:1.1.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    //RecyclerView最好的适配器，让你的适配器一目了然，告别代码冗余
    implementation "io.github.cymchad:BaseRecyclerViewAdapterHelper4:4.1.4"
    //权限请求框架
    implementation 'com.tbruyelle.rxpermissions2:rxpermissions:0.9.4@aar'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    // skin-support-constraint-layout ConstraintLayout 控件支持
    implementation 'androidx.dynamicanimation:dynamicanimation:1.0.0'
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    //RxJava的依赖包
    implementation 'io.reactivex.rxjava3:rxjava:3.1.6'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.2'

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$kotlinCoroutines"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleViewModel"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycleViewModel"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycleViewModel"
    implementation "androidx.lifecycle:lifecycle-service:$lifecycleViewModel"
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation "com.luckycatlabs:SunriseSunsetCalculator:1.2"
    implementation 'io.github.ShawnLin013:number-picker:2.4.13'
    implementation project(':Platforming:platformLib')
    implementation 'androidx.slice:slice-builders:1.1.0-alpha02'
    implementation 'androidx.slice:slice-core:1.1.0-alpha02'
    implementation 'com.tencent.tav:libpag:4.3.50'
    // debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.14'
}
