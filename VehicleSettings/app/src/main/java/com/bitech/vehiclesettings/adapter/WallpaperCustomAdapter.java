package com.bitech.vehiclesettings.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.WallpaperBean;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.presenter.display.WallpaperPresenter;
import com.bitech.vehiclesettings.utils.EToast;
import com.bumptech.glide.Glide;
import com.chery.ivi.vdb.client.listener.VDNotifyListener;
import com.chery.ivi.vdb.event.id.wallpaper.VDWallpaperInfo;

import java.util.ArrayList;

public class WallpaperCustomAdapter extends RecyclerView.Adapter<WallpaperCustomAdapter.VH> {

    Context context;
    WallpaperPresenter presenter = new WallpaperPresenter();

    public boolean isEditMode = false;

    public static ArrayList<VDWallpaperInfo> staticSelectedIndex;
    private static final long CLICK_INTERVAL = 500; // 500ms 内点击无效
    private long lastClickTime = 0;
    ArrayList<WallpaperBean> wallpaperBeans = new ArrayList<>();

    public VDNotifyListener mVDNotifyListener = (vdEvent, i) -> {
        if (vdEvent == null) return;

        new Handler(Looper.getMainLooper()).post(() -> {
            forceRefresh();
        });
    };

    public WallpaperCustomAdapter(Context context) {
        this.context = context;

        staticSelectedIndex = WallpaperPresenter.getGalleryWallpapers();
        for (VDWallpaperInfo staticSelectedIndex : staticSelectedIndex) {
            WallpaperBean wallpaperBean = new WallpaperBean(Uri.parse(staticSelectedIndex.uri));
            wallpaperBeans.add(wallpaperBean);
        }
    }

    class VH extends RecyclerView.ViewHolder {
        public ImageView ivBackground;
        public ImageView ivCheckbox;
        public TextView tvAdd;
        public ImageView ivAdd;
        public ImageView ivDel;

        public VH(@NonNull View itemView) {
            super(itemView);
            ivBackground = itemView.findViewById(R.id.ivBackground);
            ivCheckbox = itemView.findViewById(R.id.ivCheckbox);
            tvAdd = itemView.findViewById(R.id.tvAdd);
            ivAdd = itemView.findViewById(R.id.ivAdd);
            ivDel = itemView.findViewById(R.id.ivDel);
        }
    }

    @NonNull
    @Override
    public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new VH(LayoutInflater.from(context).inflate(R.layout.item_custom_wallpaper_rv_list, parent, false));
    }

    @SuppressLint({"ResourceAsColor", "UseCompatLoadingForDrawables"})
    @Override
    public void onBindViewHolder(@NonNull VH holder, int position) {
        WallpaperBean wallpaperBean;


        holder.ivDel.setVisibility(isEditMode ? View.VISIBLE : View.GONE);

        // 第一个显示 去图库添加
        boolean isGo2Gallery = position == 0;
        int pos = position == 0 ? position : position - 1;

        wallpaperBean = isGo2Gallery
                ? new WallpaperBean(R.mipmap.display_wp_store, R.string.str_wrapper_null)
                : wallpaperBeans.get(pos);

        if (isGo2Gallery) {
            holder.ivCheckbox.setVisibility(View.GONE);
            holder.tvAdd.setVisibility(View.VISIBLE);
            holder.ivAdd.setVisibility(View.VISIBLE);
            holder.ivDel.setVisibility(View.GONE);

            // 去图库卡片背景
            if (DisplayPresenter.getDisplayMode()) {
                holder.ivBackground.setBackgroundColor(ContextCompat.getColor(context, R.color.color_5E6062));
                holder.ivAdd.setImageResource(R.mipmap.ic_add_wallpaper_night);
                holder.tvAdd.setTextColor(ContextCompat.getColor(context, R.color.white));
            } else {
                holder.ivBackground.setBackgroundColor(
                        ContextCompat.getColor(context, android.R.color.transparent));
                holder.ivAdd.setImageResource(R.mipmap.ic_add_wallpaper);
                holder.tvAdd.setTextColor(ContextCompat.getColor(context, R.color.black));
            }

            Glide.with(context).clear(holder.ivBackground);
            holder.ivBackground.setImageDrawable(null);
        } else {
            holder.ivCheckbox.setVisibility(isEditMode ? View.GONE : View.VISIBLE);

            holder.ivBackground.setBackgroundColor(
                    ContextCompat.getColor(context, android.R.color.transparent));
        }


        if (!isGo2Gallery) {
            holder.ivCheckbox.setImageResource(staticSelectedIndex.get(pos).selected ?
                    R.mipmap.ic_checkbox_true_wallpaper : R.mipmap.ic_checkbox_false_wallpaper);
        }

        if (wallpaperBean.getUri() != null) {
            Glide.with(context)
                    .load(wallpaperBean.getUri())
                    .centerCrop()
                    .placeholder(R.mipmap.display_wp_3d)
                    .into(holder.ivBackground);
        }

        holder.itemView.setOnClickListener(view -> {
            // 去图库
            if (isGo2Gallery) {
                onGo2GalleryClickListener.onClick();
                return;
            }
            // TODO 壁纸详情
            EToast.showToast(context, "壁纸详情", 0, false);
        });

        holder.ivCheckbox.setOnClickListener(v -> {
            long now = System.currentTimeMillis();
            if (now - lastClickTime < CLICK_INTERVAL) {
                return; // 忽略重复点击
            }
            lastClickTime = now;

            updateSelection(position);
        });

        holder.ivDel.setOnClickListener(v -> {
            // 删除壁纸
            WallpaperPresenter.deleteWallpaper(staticSelectedIndex.get(pos));
            forceRefresh();
        });
    }

    private void updateSelection(int newIndex) {
        newIndex--;
        VDWallpaperInfo wallpaperInfo = staticSelectedIndex.get(newIndex);
        if (WallpaperPresenter.getGalleryWallpapersSelectedSize() + WallpaperPresenter.getPRSETWallpapersSelectedSize() == 1) {
            EToast.showToast(context, context.getText(R.string.str_wrapper_toast_set_failure2), Toast.LENGTH_SHORT, false);
            return;
        }
        if (wallpaperInfo.selected) {
            WallpaperPresenter.hidePreWallpaper(wallpaperInfo);
            wallpaperInfo.selected = false;
        } else {
            if (staticSelectedIndex.size() >= WallpaperPresenter.MAX_STATIC_SELECTION) {
                EToast.showToast(context, context.getText(R.string.str_wrapper_toast_set_failure), Toast.LENGTH_SHORT, false);
                return;
            }
            WallpaperPresenter.setWallpaper(wallpaperInfo);
            wallpaperInfo.selected = true;
        }
        notifyItemChanged(++newIndex); // 局部刷新
    }

    // 刷新数据源
    public void forceRefresh() {
        new Handler(Looper.getMainLooper()).post(() -> {
            ArrayList<VDWallpaperInfo> newData = WallpaperPresenter.getGalleryWallpapers();
            staticSelectedIndex = new ArrayList<>(newData);
//            staticSelectedIndex.add(new VDWallpaperInfo(0, 0, 0, null, 0, 0, 0, false, 0, 0, null, false, false));
            wallpaperBeans.clear();
            for (VDWallpaperInfo info : staticSelectedIndex) {
                wallpaperBeans.add(new WallpaperBean(Uri.parse(info.uri)));
            }
            notifyDataSetChanged();
        });
    }

    @Override
    public int getItemCount() {
        if (wallpaperBeans.isEmpty()) {
            return 1;
        }
        return wallpaperBeans.size() + 1;
    }

    public interface OnGo2GalleryClickListener {
        void onClick();
    }

    private OnGo2GalleryClickListener onGo2GalleryClickListener;

    public void setOnGo2GalleryClickListener(OnGo2GalleryClickListener listener) {
        this.onGo2GalleryClickListener = listener;
    }

    public void editMode() {
        isEditMode = !isEditMode;
        forceRefresh();
    }
}

