package com.bitech.vehiclesettings.view.system;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSInstrumentUnitBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSUnitSettingBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSVerifyInfoBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSVersionMsgBinding;
import com.bitech.vehiclesettings.presenter.system.SystemPresenterListener;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.Locale;

public class VersionMsgUIAlert extends BaseDialog {
    private static final String TAG = VersionMsgUIAlert.class.getSimpleName();
    private DialogAlertSVersionMsgBinding binding;
    private boolean isBlueOpen = false;

    public VersionMsgUIAlert(@NonNull Context context) {
        super(context, R.style.Dialog);
        initView();
    }

    public VersionMsgUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        initView();
    }

    protected VersionMsgUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        initView();
    }

    private void initView() {
        binding = DialogAlertSVersionMsgBinding.inflate(LayoutInflater.from(getContext()));
        setContentView(binding.getRoot());

        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1884; // 或者使用具体的像素值
            layoutParams.height = 1128;
            window.setAttributes(layoutParams);
        }
    }

    public void setBlueOpen(boolean blueOpen) {
        isBlueOpen = blueOpen;
    }

    public boolean isBlueOpen() {
        return isBlueOpen;
    }

    @Override
    public void dismiss() {
        unregisterReceiver(getContext());
        super.dismiss();
    }

    private void unregisterReceiver(Context context) {
        // Implementation for unregistering receivers
    }

    public interface OnProgressChangedListener {
    }
}