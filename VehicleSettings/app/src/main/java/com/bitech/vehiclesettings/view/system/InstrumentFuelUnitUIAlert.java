package com.bitech.vehiclesettings.view.system;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSInstrumentUnitBinding;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class InstrumentFuelUnitUIAlert extends BaseDialog {
    private static final String TAG = InstrumentFuelUnitUIAlert.class.getSimpleName();
    private static InstrumentFuelUnitUIAlert.OnProgressChangedListener onProgressChangedListener;

    public InstrumentFuelUnitUIAlert(@NonNull Context context) {
        super(context);
    }

    public InstrumentFuelUnitUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected InstrumentFuelUnitUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static InstrumentFuelUnitUIAlert.OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(InstrumentFuelUnitUIAlert.OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = true;
        private DialogAlertSInstrumentUnitBinding binding;
        private String phoneNumber;
        private boolean isBlueOpen = false;
        public InstrumentFuelUnitUIAlert dialog;

        public Builder(Context context) {
            this.context = context;
        }

        public InstrumentFuelUnitUIAlert.Builder setCancelable(boolean isCancelable) {
            this.isCancelable = isCancelable;
            return this;
        }

        public InstrumentFuelUnitUIAlert.Builder setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
            return this;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public InstrumentFuelUnitUIAlert create() {
            dialog = new InstrumentFuelUnitUIAlert(context, R.style.Dialog);
            binding = DialogAlertSInstrumentUnitBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1128; // 或者使用具体的像素值
                layoutParams.height = 508;

                window.setAttributes(layoutParams);
            }
            initPicker();
            return dialog;
        }

        private void initPicker() {
            binding.spvPicker.setItems(R.string.str_instrument_fuel_unit_1km,
                    R.string.str_instrument_fuel_unit_100km);
            binding.spvPicker.setSelectedIndex(onProgressChangedListener.getInstrumentFuelUnit(), false);
            binding.spvPicker.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    onProgressChangedListener.onSwitch(index);
                }

                @Override
                public void onItemClicked(int index, String text) {
                    // 点击事件入口
                }
            });

        }

        public void updateInstrumentFuelUnitUI(int instrumentFuelUnit) {
            if (dialog != null) {
                binding.spvPicker.setSelectedIndex(instrumentFuelUnit, true);
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void dismiss() {
        unregisterReceiver(getContext());
        super.dismiss();
    }

    private void unregisterReceiver(Context context) {
        // Implementation for unregistering receivers
    }

    public interface OnProgressChangedListener {
        int getInstrumentFuelUnit();

        void setInstrumentFuelUnit(int state);

        void onSwitch(int index);
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;
}
