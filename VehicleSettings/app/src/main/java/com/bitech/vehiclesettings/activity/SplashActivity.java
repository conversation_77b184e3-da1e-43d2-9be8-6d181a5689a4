package com.bitech.vehiclesettings.activity;

import android.content.Intent;
import android.os.Handler;
import android.view.View;

import com.bitech.base.activity.BaseActivity;
import com.bitech.vehiclesettings.databinding.ActivitySplashBinding;

public class SplashActivity extends BaseActivity {

    private ActivitySplashBinding binding;

    @Override
    protected View getLayoutResId() {
        binding = ActivitySplashBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    protected void initView() {

    }

    @Override
    protected void setListener() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // 执行耗时操作后跳转主界面
                startActivity(new Intent(SplashActivity.this, MainActivity.class));
                finish();
            }
        }, 100);
    }
}
