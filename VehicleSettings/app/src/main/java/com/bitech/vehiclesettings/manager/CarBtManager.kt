package com.bitech.vehiclesettings.manager

import android.bluetooth.BluetoothA2dpSink
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothHeadsetClient
import android.bluetooth.BluetoothMapClient
import android.bluetooth.BluetoothPbapClient
import android.bluetooth.BluetoothProfile
import android.bluetooth.le.AdvertiseCallback
import android.bluetooth.le.AdvertiseSettings
import android.bluetooth.le.BluetoothLeAdvertiser
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.SystemProperties
import android.provider.CallLog
import android.provider.ContactsContract
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.BtDeviceBean
import com.bitech.vehiclesettings.bean.BtPairedBean
import com.bitech.vehiclesettings.broadcast.BluetoothReceiver
import com.bitech.vehiclesettings.broadcast.UsbStateReceiver
import com.bitech.vehiclesettings.common.SettingDialogManager
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.PhoneLink
import com.bitech.vehiclesettings.view.dialog.BtConnectSelectedDialog
import com.bitech.vehiclesettings.view.dialog.BtConnectTypeDialog
import com.bitech.vehiclesettings.view.dialog.BtPairedCodeDialog
import com.bitech.vehiclesettings.view.dialog.ComTipsDialog
import com.bitech.vehiclesettings.view.dialog.ConfirmDialog
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.chery.aauto.devicelist.AAutoDevice
import com.chery.adapter.androidauto.connection.devicelist.ConnectState
import com.chery.adapter.androidauto.connection.devicelist.ConnectType
import com.chery.adapter.carplay.connection.devicelist.CarplayDevice
import com.chery.adapter.common.config.IConstant
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.client.bind.VDThreadType
import com.chery.ivi.vdb.client.listener.VDNotifyListener
import com.chery.ivi.vdb.event.VDEvent
import com.chery.ivi.vdb.event.base.VDKey
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink
import com.chery.ivi.vdb.event.id.phonelink.VDValuePhoneLink
import com.chery.ivi.vdb.event.id.phonelink.bean.VDLinkDevice
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.lang.Runnable
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.Timer
import java.util.concurrent.CopyOnWriteArrayList


/**
 * @ClassName: CarBtManager
 * 
 * @Date:  2024/1/23 9:20
 * @Description: 车辆蓝牙管理类.
 **/
class CarBtManager : BluetoothReceiver.BluetoothStateListener {

    //是否是用户点击主动配对的，决定配对框是否有确定按钮，配对失败或成功还原
    var isUserCreateBond = false
    //用户是否主动取消了配对，决定是否弹配对失败框，配对失败或成功还原
    private var isCancelPairingByUser = false

    //是否在蓝牙设置页面
    var isBtSettingUI = false

    // 蓝牙适配器
    private var bluetoothAdapter: BluetoothAdapter? = null

    // 设备管理对象
    private var carDmManager = CarDmManager.instance

    // 环境上下文
    private var context = MyApplication.getContext()

    // 蓝牙广播接收器对象
    private var bluetoothReceiver: BluetoothReceiver? = null

    // 语言通话协议
    private var headsetClient: BluetoothHeadsetClient? = null

    // 音频传输协议
    private var a2dpSink: BluetoothA2dpSink? = null

    // 电话本访问协议
    private var pbapClient: BluetoothPbapClient? = null

    // 消息访问协议
    private var mapClient: BluetoothMapClient? = null

    // 已配对列表是否初始化
    private var btPairedListIsInit = false

    // 是否在配对中
    private var isPairing = false

    // 当前正在配对的蓝牙设备
    private var pairedBtDevice: BluetoothDevice? = null

    // 是否需要连接蓝牙音乐
    private var isNeedConnectA2dp = false

    // 当前carPlay连接设备
    private var carPlayBean: BtDeviceBean? = null

    // 当前AA连接设备
    private var androidAutoBean: BtDeviceBean? = null

    // 设备配对倒计时管理对象
    private var pairedTimerTask: CountDownTimeManager? = null

    // 可被发现倒计时管理对象
    private var timerTask: CountDownTimeManager? = null

    // 蓝牙可用设备列表
    private var btScanDevicesList = CopyOnWriteArrayList<BtDeviceBean>()

    // 蓝牙配对列表
    private var btPairedDeviceList = CopyOnWriteArrayList<BtDeviceBean>()

    // 蓝牙已连接列表(已连接的蓝牙设备,CarPlay设备对应的蓝牙设备,AA对应的蓝牙设备),只用于休眠唤醒回连
    private var btConnectedDeviceList = CopyOnWriteArrayList<BtDeviceBean>()

    // 自定义Toast
    private var toast: SettingsToast? = null

    // 蓝牙配对弹窗对象
    private var btPairedCodeDialog: BtPairedCodeDialog? = null

    // 连接类型弹窗
    private var btConnectTypeDialog: BtConnectTypeDialog? = null

    //连接选择替换弹窗
    private var btConnectSelectedDialog:BtConnectSelectedDialog? = null

    //cpaa首次开启弹框
    private var cpAaFirstConnectTipsDialog: ConfirmDialog? = null

    //连接成功时间，用这个格式给出YYYY/MM/DD HH:MM:SS
    private var connectTime: String = ""

    // 协程互斥锁
    private val btScopeMutex = Mutex()

    // handler对象
    private var handler = Handler(Looper.getMainLooper())

    private val USB_UEVENT_PATH = "DEVPATH=/devices/virtual/apple/usb_event"
    private var isSupportCarplay: Boolean = false

    private var isPhoneLinking = false

    //首次配对成功的设备
    private var pairedBluetoothDevice: BluetoothDevice ?= null
    private val bluetoothLeAdvertiser: BluetoothLeAdvertiser? = bluetoothAdapter?.bluetoothLeAdvertiser
    // 广告回调
    private val advertisingCallback = object : AdvertiseCallback() {
        override fun onStartSuccess(settingsInEffect: AdvertiseSettings) {
            Log.d(TAG, "BLE advertising started successfully")
        }

        override fun onStartFailure(errorCode: Int) {
            Log.e(TAG, "Failed to start BLE advertising: $errorCode")
        }
    }
    //是否远距离断开
    private var isRemoteDisconnect = false
    // 蓝牙远距离断开回连runnable
    private var remoteRunnable = Runnable {
        LogUtil.d(TAG, "remoteRunnable : lash back the bt device!")
        // 回连远距离断开蓝牙
        remoteDisconnectedLashBack()
    }

    //互联状态类
    private val phoneLink = PhoneLink()

    // 蓝牙配置文件服务的连接状态监听
    private val profileServiceListener: BluetoothProfile.ServiceListener =
        object : BluetoothProfile.ServiceListener {
            override fun onServiceConnected(profile: Int, proxy: BluetoothProfile?) {
                // 服务连接成功
                LogUtil.d(TAG, "onServiceConnected : profile = $profile , proxy$proxy")
                when (profile) {
                    BluetoothProfile.HEADSET_CLIENT -> {
                        LogUtil.d(TAG, "onServiceConnected : get hfp service success!")
                        headsetClient = proxy as BluetoothHeadsetClient
                        // 初始化蓝牙配对列表
                        initPairedList()
                        startBtReconnection()
                    }

                    BluetoothProfile.A2DP_SINK -> {
                        LogUtil.d(TAG, "onServiceConnected : get a2dp service success!")
                        a2dpSink = proxy as BluetoothA2dpSink
                        // 初始化蓝牙配对列表
                        initPairedList()
                        startBtReconnection()
                    }

                    BluetoothProfile.PBAP_CLIENT -> {
                        LogUtil.d(TAG, "onServiceConnected : get pbap service success!")
                        pbapClient = proxy as BluetoothPbapClient
                    }

                    BluetoothProfile.MAP_CLIENT -> {
                        LogUtil.d(TAG, "onServiceConnected : get map service success!")
                        mapClient = proxy as BluetoothMapClient
                    }
                }
            }

            override fun onServiceDisconnected(profile: Int) {
                // 服务断开连接
                LogUtil.d(TAG, "onServiceDisconnected : profile = $profile")
                when (profile) {
                    BluetoothProfile.HEADSET_CLIENT -> {
                        LogUtil.d(TAG, "onServiceDisconnected : disconnect hfp service success!")
                        headsetClient = null
                    }

                    BluetoothProfile.A2DP_SINK -> {
                        LogUtil.d(TAG, "onServiceDisconnected : disconnect a2dp service success!")
                        a2dpSink = null
                    }

                    BluetoothProfile.PBAP_CLIENT -> {
                        LogUtil.d(TAG, "onServiceDisconnected : disconnect pbap service success!")
                        pbapClient = null
                    }

                    BluetoothProfile.MAP_CLIENT -> {
                        LogUtil.d(TAG, "onServiceDisconnected : disconnect map service success!")
                        mapClient = null
                    }
                }
            }
        }

    /**
     * 初始化蓝牙管理对象数据.
     *
     */
    fun initBtManager(context: Context) {
        LogUtil.d(TAG, "initBtManager:")
        // 初始化蓝牙已连接列表
        readBtConnectedList()
        // 初始化蓝牙配对列表
        initPairedList()
        // 获取蓝牙广播接收器对象
        bluetoothReceiver = BluetoothReceiver()
        // 注册蓝牙广播
        registerBluetoothReceiver(context)
        // 注册蓝牙广播监听
        bluetoothReceiver!!.registerBtStateListener(javaClass.simpleName, this)
        // 注册USB事件监听
        val usbReceiver = UsbStateReceiver()
        val filter = IntentFilter().apply {
            addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
            addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        }
        context.registerReceiver(usbReceiver, filter)
        // 设置设备管理相关监听
        carDmManager.setDeviceListenerCp(javaClass.simpleName, CpDeviceListener())
        carDmManager.setDeviceListenerAa(javaClass.simpleName, AaSettingBinderListener())
        //互联监听
        Handler(Looper.getMainLooper()).postDelayed({
            kotlin.runCatching {
                VDBus.getDefault().addSubscribe(VDEventPhoneLink.PHONE_STATE)
                VDBus.getDefault().subscribeCommit()
                VDBus.getDefault().registerVDNotifyListener(phoneLinkListener)
            }.onFailure { it.printStackTrace() }
        }, 2000L)   // 2000 毫秒 = 2 秒
        if (bluetoothAdapter == null) {
            bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            if (!bluetoothAdapter!!.isEnabled) {
                // 蓝牙未打开，则自动打开蓝牙
                setBluetoothStatus(true)
            }
            if (bluetoothAdapter != null) {
                if (headsetClient == null) {
                    LogUtil.d(TAG, "initBtManager: getProfileProxy hfp")
                    // 获取蓝牙电话通信配置协议
                    bluetoothAdapter!!.getProfileProxy(
                        context,
                        profileServiceListener,
                        BluetoothProfile.HEADSET_CLIENT
                    )
                }
                if (a2dpSink == null) {
                    LogUtil.d(TAG, "initBtManager: getProfileProxy a2dp")
                    // 获取蓝牙音频传输配置协议
                    bluetoothAdapter!!.getProfileProxy(
                        context,
                        profileServiceListener,
                        BluetoothProfile.A2DP_SINK
                    )
                }
                if (pbapClient == null) {
                    // 获取蓝牙电话本访问配置协议
                    bluetoothAdapter!!.getProfileProxy(
                        context,
                        profileServiceListener,
                        BluetoothProfile.PBAP_CLIENT
                    )
                }
                if (mapClient == null) {
                    // 获取蓝牙信息访问配置协议
                    bluetoothAdapter!!.getProfileProxy(
                        context,
                        profileServiceListener,
                        BluetoothProfile.MAP_CLIENT
                    )
                }
            }
        }
    }

    //互联监听
    private val phoneLinkListener = object : VDNotifyListener {
        override fun onVDNotify(event: VDEvent, threadType: Int) {
            if (threadType != VDThreadType.MAIN_THREAD) return
            when (event.id) {
                VDEventPhoneLink.PHONE_STATE -> {
                    val bundle = event.payload ?: return
                    phoneLink.status = bundle.getInt(VDKey.STATUS)
                    phoneLink.type = bundle.getInt(VDKey.TYPE)
                    phoneLink.isWireless =
                        bundle.getBoolean(VDKey.ENABLE)
                    Log.d(TAG, "onVDNotify: 手机互联监听状态" + phoneLink.status)
                    if (phoneLink.type == PhoneLink.TYPE_CARPLAY) {
                        phoneLink.typeName = PhoneLink.TYPE_CARPLAY_NAME
                    } else if (phoneLink.type == PhoneLink.TYPE_CARLINK) {
                        phoneLink.typeName = PhoneLink.TYPE_CARLINK_NAME
                    } else if (phoneLink.type == PhoneLink.TYPE_HICAR) {
                        phoneLink.typeName = PhoneLink.TYPE_HICAR_NAME
                    }
                    // TODO: 根据 status / type / isWireless 做业务
                }
            }
        }
    }

    /**
     * 注册蓝牙广播.
     *
     * @param context
     */
    private fun registerBluetoothReceiver(context: Context) {
        LogUtil.d(TAG, "registerBluetoothReceiver : ")
        val filter = IntentFilter().apply {
            // 蓝牙状态
            addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
            // 蓝牙可被发现
            addAction(BluetoothAdapter.ACTION_SCAN_MODE_CHANGED)
            // 蓝牙扫描结果action
            addAction(BluetoothDevice.ACTION_FOUND)
            // 蓝牙名称变化的广播
            addAction(BluetoothAdapter.ACTION_LOCAL_NAME_CHANGED)
            // 设备绑定状态广播
            addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED)
            // 设备配对请求广播
            addAction(BluetoothDevice.ACTION_PAIRING_REQUEST)
            // 扫描开始action
            addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED)
            // 扫描结束action
            addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
            // hfp连接状态广播
            addAction(BluetoothHeadsetClient.ACTION_CONNECTION_STATE_CHANGED)
            // a2dp连接状态广播
            addAction(BluetoothA2dpSink.ACTION_CONNECTION_STATE_CHANGED)
            // pbap连接状态广播
            addAction(BluetoothPbapClient.ACTION_CONNECTION_STATE_CHANGED)
            // map连接状态光比
            addAction(BluetoothMapClient.ACTION_CONNECTION_STATE_CHANGED)
            // ACL链路断开广播
            addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
            // 添加其他需要的action...
            priority = 999 // 设置最高优先级（范围：-1000~1000）
        }

        context.registerReceiver(bluetoothReceiver, filter)
    }

    /**
     * 注销蓝牙广播.
     *
     * @param context
     */
    private fun unregisterBluetoothReceiver(context: Context) {
        LogUtil.d(TAG, "unregisterBluetoothReceiver : ")
        context.unregisterReceiver(bluetoothReceiver)
    }

    /**
     * 开机完成后,初始化蓝牙相关数据.
     *
     */
    fun initBtData() {
        LogUtil.d(TAG, "initBtData : ")
        // 初始化蓝牙已连接列表
        readBtConnectedList()
        // 初始化蓝牙配对列表
        initPairedList()
    }

    /**
     * 构建一个蓝牙实体.
     *
     * @param bluetoothDevice 蓝牙设备对象
     * @return 蓝牙设备对象
     */
    private fun createBtDeviceBean(bluetoothDevice: BluetoothDevice): BtDeviceBean {
        val device = BtDeviceBean(bluetoothDevice)
        if (headsetClient == null) {
            device.hfpState = BluetoothProfile.STATE_DISCONNECTED
        } else {
            device.hfpState = headsetClient!!.getConnectionState(bluetoothDevice)
        }
        if (a2dpSink == null) {
            device.a2dpState = BluetoothProfile.STATE_DISCONNECTED
        } else {
            device.a2dpState = a2dpSink!!.getConnectionState(bluetoothDevice)
        }
        device.bondState = BluetoothDevice.BOND_BONDED
        return device
    }

    /**
     * 设置蓝牙状态.
     *
     * @param btStatus 蓝牙状态
     * @return 返回是否设置成功
     */
    fun setBluetoothStatus(btStatus: Boolean){
        // 蓝牙设置情况
        LogUtil.d(TAG, "setBluetoothStatus : status = $btStatus")
        val setBlueStatus = if (btStatus) {
            bluetoothAdapter!!.enable()
        } else {
            val event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
            if (event != null) {
                val bundle = event.payload
                val status = bundle.getInt(VDKey.STATUS)
                val type = bundle.getInt(VDKey.TYPE)
                val isWireless = bundle.getBoolean(VDKey.ENABLE)
                if(status > 0&&type == 8){
                    showCloseBluetoothConfirmation(type)
                    return
                }
            }
            // 关闭蓝牙
            bluetoothAdapter!!.disable()
        }
        LogUtil.d(TAG, "setBluetoothStatus : setBlueStatus = $setBlueStatus")
        return
    }

    private fun showCloseBluetoothConfirmation(type: Int) {
        Handler(Looper.getMainLooper()).post {
            ConfirmDialog(MyApplication.getContext()).apply {
                setTips("关闭蓝牙连接会影响ICCOA Carlink通话，是否确定关闭蓝牙？")
                setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
                    override fun onConfirmClick() {
                        bluetoothAdapter!!.disable()
                        dismiss()
                    }
                    override fun onCancelClick() {
                        dismiss()
                    }
                })
                show()
            }
        }
    }

    /**
     * 获取当前蓝牙开关状态.
     *
     * @return 蓝牙状态
     */
    fun getBluetoothStatus(): Boolean {
        return if (bluetoothAdapter != null) {
            bluetoothAdapter!!.isEnabled
        } else {
            false
        }
    }

    /**
     * 设置蓝牙可被发现状态.
     *
     * @param btFoundStatus 可被发现状态.
     * @return Boolean
     */
    fun setBtFoundStatus(btFoundStatus: Boolean): Boolean {
        // 创建意图以请求蓝牙可见性
        val discoverableIntent = Intent(BluetoothAdapter.ACTION_REQUEST_DISCOVERABLE).apply {
            // 设置可见性持续时间（秒），最大值为3600秒（1小时）
            putExtra(BluetoothAdapter.EXTRA_DISCOVERABLE_DURATION, 180) // 5分钟
            // 检查是否为非Activity上下文，如果是则添加FLAG_ACTIVITY_NEW_TASK标志
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        return true
    }

    //蓝牙可见性开关
    fun getBtFoundSwitch(): Boolean {
        val scanMode = bluetoothAdapter!!.scanMode
        LogUtil.d(TAG, "getBtFoundSwitch : scanMode = $scanMode")
        return scanMode == BluetoothAdapter.SCAN_MODE_CONNECTABLE_DISCOVERABLE
    }


    /**
     * 获取蓝牙可被发现状态.
     *
     * @return Int
     */
    fun getBtFoundStatus(): Int {
        val scanMode = bluetoothAdapter!!.scanMode
        LogUtil.d(TAG, "getBtFoundStatus : scanMode = $scanMode")
        return scanMode
    }

    /**
     * 获取当前连接的蓝牙设备名称（优先返回非 CP/AA 的蓝牙设备）
     * @return 当前连接蓝牙设备的名称，未连接返回 null
     */
    fun getCurrentConnectedBluetoothDeviceName(): String? {
        val connectedList = getConnectedDevices()
        if (connectedList.isEmpty()) {
            return null
        }

        // 优先返回非 CP/AA 的蓝牙设备名称
        val btDevice = connectedList.find {
            it.cPConnectedState != IConstant.ConnectState.CONNECTED &&
                    it.aAConnectedState != ConnectState.CONNECTED.state &&
                    (it.hfpState == BluetoothProfile.STATE_CONNECTED || it.a2dpState == BluetoothProfile.STATE_CONNECTED)
        }

        return btDevice?.device?.name ?: run {
            // 如果没有非 CP/AA 的蓝牙设备，则返回 CP 或 AA 的蓝牙设备名称
            val fallback = connectedList.firstOrNull()
            fallback?.device?.name
        }
    }

    /**
     * 设置蓝牙名称.
     *
     * @param btName 蓝牙名称
     * @return
     */
    fun setBtName(btName: String): Boolean {
        LogUtil.d(TAG, "setBtName : bt name = $btName")
        return bluetoothAdapter!!.setName(btName)
    }

    /**
     * 获取本机蓝牙名称
     *
     * @return
     */
    fun getBtName(): String? {
        return bluetoothAdapter!!.name
    }

    /**
     * 开始扫描蓝牙设备.
     *
     * @return 开始扫描设置状态.
     */
    fun startScanBtDevice(): Boolean {
        LogUtil.d(TAG, "startScanBtDevice : ")
        return bluetoothAdapter!!.startDiscovery()
    }

    /**
     * 是否处于扫描中.
     *
     * @return 蓝牙扫描状态
     */
    fun isDiscovering(): Boolean {
        return bluetoothAdapter!!.isDiscovering
    }

    /**
     * 获取当前扫描状态.
     *
     * @return
     */
    fun getScanState(): Boolean {
        return bluetoothAdapter!!.isDiscovering
    }

    /**
     * 停止蓝牙设备扫描.
     *
     * @return 停止扫描设置状态
     */
    fun stopScanBtDevice(): Boolean {
        LogUtil.d(TAG, "stopScanBtDevice : ")
        return if (bluetoothAdapter!!.isDiscovering) {
            bluetoothAdapter!!.cancelDiscovery()
        } else {
            true
        }
    }

    /**
     * 获取蓝牙配对列表.
     *
     * @return
     */
    fun getBtPairedDevices(): MutableList<BluetoothDevice> {
        return bluetoothAdapter!!.bondedDevices.toMutableList()
    }

    /**
     * 获取a2dp已连接的设备列表.
     *
     * @return
     */
    fun getA2dpConnectedDevicesList(): List<BluetoothDevice>{
        return a2dpSink?.run {
            this.connectedDevices
        } ?: arrayListOf<BluetoothDevice>()
    }

    /**
     * 获取hfp已连接的设备列表.
     *
     * @return
     */
    fun getHfpConnectedDevicesList(): List<BluetoothDevice>{
        return headsetClient?.run {
            this.connectedDevices
        } ?: arrayListOf<BluetoothDevice>()
    }

    /**
     * 获取蓝牙已连接的设备列表.
     *
     * @return
     */
    fun getBluetoothConnectedDevicesList(): List<BluetoothDevice>{
        val a2dpList = getA2dpConnectedDevicesList()
        val hfpList = getHfpConnectedDevicesList()
        val mergedList  = a2dpList + hfpList
        //合并后去重
        val distinctMergedList = mergedList.toSet().toList()
        LogUtil.d(TAG, "getBluetoothConnectedDevicesList : a2dpList.size = ${a2dpList.size} ,  hfpList.size = ${hfpList.size} , distinctMergedList.size = ${distinctMergedList.size}")
        return distinctMergedList
    }
    /**
     * 解除配对.
     *
     * @param device 待解除的配对设备
     * @return Boolean
     */
    fun removeBond(device: BluetoothDevice): Boolean {
        // 停止自动连接
        for (listener in btInfoListenerMap!!.values) {
            listener.onStopAutoConnectTimerTask()
        }
        return device.removeBond()
    }

    /**
     * 移除蓝牙配对列表中所有配对信息.
     *
     */
    private fun removeAllBond() {
        LogUtil.d(TAG, "removeAllBond : ")
        getBtPairedDevices().forEach {
            // 移除蓝牙配对
            it.removeBond()
            // 删除DM对应的设备
            CarDmManager.instance.deleteDeviceCpAa(it)
        }
    }

    /**
     * 根据mac地址，获取蓝牙设备.
     *
     * @param btAddress mac地址
     * @return 对应的蓝牙设备
     */
    fun getDevice(btAddress: String): BluetoothDevice {
        LogUtil.i(TAG, "getDevice : btAddress = $btAddress")
        return bluetoothAdapter!!.getRemoteDevice(btAddress)
    }

    /**
     * 连接蓝牙电话.
     *
     * @param device 需要连接的设备.
     * @return
     */
    private fun connectBtPhone(device: BluetoothDevice): Boolean {
//        clearPbapData();
        return if (headsetClient != null) {
            LogUtil.i(TAG, "connectBtPhone : device = ${device.name}(${device.address})")
            headsetClient!!.connect(device)
        } else {
            false
        }
    }

    /**
     * 断开蓝牙电话.
     *
     * @param device 需要断开的设备.
     * @return
     */
    fun disconnectBtPhone(device: BluetoothDevice): Boolean {
//        clearPbapData();
        return if (headsetClient != null) {
            LogUtil.i(TAG, "disconnectBtPhone : device = ${device.name}(${device.address})")
            headsetClient!!.disconnect(device)
        } else {
            false
        }
    }

    /**
     * 连接蓝牙音乐.
     *
     * @param device 需要连接的设备
     * @return
     */
    fun connectBtMusic(device: BluetoothDevice): Boolean {
        return if (a2dpSink != null) {
            LogUtil.i(TAG, "connectBtMusic : device = ${device.name}(${device.address})")
            a2dpSink!!.connect(device)
        } else {
            false
        }
    }

    /**
     * 断开蓝牙音乐.
     *
     * @param device 需要断开的设备.
     * @return
     */
    fun disconnectBtMusic(device: BluetoothDevice): Boolean {
        return if (a2dpSink != null) {
            LogUtil.i(TAG, "disconnectBtMusic : device = ${device.name}(${device.address})")
            a2dpSink!!.disconnect(device)
        } else {
            false
        }
    }

    /**
     * 断开Pbap.
     *
     * @param device 需要断开的设备.
     * @return
     */
    fun disconnectPbap(device: BluetoothDevice): Boolean {
        return if (pbapClient != null) {
            LogUtil.i(TAG, "zhc6whu 断开pbap disconnectBtMusic : device = ${device.name}(${device.address})")
            pbapClient!!.disconnect(device)
        } else {
            false
        }
    }

    /**
     * 当前已配对蓝牙设备列表是否已存在设备连接.
     *
     * @return
     */
    fun hasBtConnected(): Boolean {
        bluetoothAdapter!!.bondedDevices.forEach { device ->
            if (getHfpConnectionState(device) == BluetoothProfile.STATE_CONNECTED
                || getA2dpConnectionState(device) == BluetoothProfile.STATE_CONNECTED
            ) {
                return true
            }
        }
        return false
    }

    /**
     * 当前已配对蓝牙列表是否存在连接中的设备.
     *
     * @return
     */
    fun hasBtConnecting(): Boolean {
        bluetoothAdapter!!.bondedDevices.forEach { device ->
            if (getHfpConnectionState(device) == BluetoothProfile.STATE_CONNECTING
                || getA2dpConnectionState(device) == BluetoothProfile.STATE_CONNECTING
            ) {
                return true
            }
        }
        return false
    }

    /**
     * 判断该设备是否断开连接.
     *
     * @param device
     * @return
     */
    fun isDeviceDisconnected(device: BluetoothDevice): Boolean {
        return (getHfpConnectionState(device) == BluetoothProfile.STATE_DISCONNECTED
                && getA2dpConnectionState(device) == BluetoothProfile.STATE_DISCONNECTED)
    }

    /**
     * 获取Hfp,蓝牙电话通信的连接状态.
     *
     * @param device 设备
     * @return 连接状态 Int
     */
    fun getHfpConnectionState(device: BluetoothDevice): Int {
        var state = BluetoothProfile.STATE_DISCONNECTED
        if (headsetClient != null) {
            state = headsetClient!!.getConnectionState(device)
            LogUtil.i(TAG, "getHfpConnectionState : state = $state")
        }
        return state
    }

    /**
     * 获取a2dp音频传输服务连接状态.
     *
     * @param device
     * @return Int
     */
    fun getA2dpConnectionState(device: BluetoothDevice): Int {
        var state = BluetoothProfile.STATE_DISCONNECTED
        if (a2dpSink != null) {
            state = a2dpSink!!.getConnectionState(device)
            LogUtil.i(TAG, "getA2dpConnectionState : state = $state")
        }
        return state
    }

    /**
     * 恢复出厂设置.
     *
     */
    fun factoryRecoveryBt() {
        LogUtil.d(TAG, "clearConnectedBtDevices : ")
        // 蓝牙开启
        setBluetoothStatus(true)
        // 蓝牙可被发现关闭
//        setBtFoundStatus(false)
        // 移除所有蓝牙已配对列表
        removeAllBond()
        //删除BT/WIFI的配置文件
        SystemProperties.set("sys.factory.reset","1")
        // 清空列表
        btConnectedDeviceList.clear()
        btPairedDeviceList.clear()
        btScanDevicesList.clear()
        // 已连接列表默认值
        val sharedPreferences = MyApplication.getContext()
            .getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE)
        sharedPreferences
            .edit()
            .putString(Contacts.SHARED_PRE_BT_DEVICE, Contacts.BT_DEVICE_DEFAULT)
            .apply()
        // 远距离断开蓝牙默认值
        MyApplication.getContext().getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE)
            .edit()
            .putString(Contacts.SHARED_PRE_LONG_DISTANCE_BT_DEVICE, Contacts.BT_DEVICE_DEFAULT)
            .apply()
        // 远距离断开false
        setIsRemoteDisconnect(false)
    }

    /**
     * 保存已连接的蓝牙设备及连接状态.
     *
     * @param device 当前正在连接的蓝牙设备
     */
    private fun saveCurrentBtDevice(device: BluetoothDevice) {
        LogUtil.d(TAG, "saveConnectedBtDevice: device = ${device.name}(${device.address})")
        val btDeviceBean =
            btPairedDeviceList.find { TextUtils.equals(it.device.address, device.address) }
        if (btDeviceBean != null) {
            if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED
                || btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED
            ) {
                /*btDeviceBean.pairedTime = System.currentTimeMillis()
                // 设备时间添加到数据库
                SettingApplication.settingDataBase.btDeviceDao().insertOrUpdateBtDevice(
                    BtPairedBean(btDeviceBean.device.address, btDeviceBean.pairedTime)
                )*/

                // 当前蓝牙已连接，则保存当前的连接设备到列表
                //T13JSUPPLY-252，保存之前将cp和aa状态赋值
                // 在已连接列表中查找该对象
                val btConnectedDevice = btPairedDeviceList.find {
                    TextUtils.equals(
                        it.device.address,
                        btDeviceBean.device.address
                    )
                }
                if (btConnectedDevice != null) {
                    btDeviceBean.aAConnectedState = btConnectedDevice.aAConnectedState
                    btDeviceBean.cPConnectedState = btConnectedDevice.cPConnectedState
                }
                addConnectedBtToList(btDeviceBean)

                val disconnectDevice = readRemoteDisconnectBtDevice()
                if (disconnectDevice != null) {
                    if(disconnectDevice.address == device.address){
                        // 将远距离断开的蓝牙置空
                        saveDistanceBtDevice(Contacts.BT_DEVICE_DEFAULT)
                        // 设置远距离断开设备不存在
                        setIsRemoteDisconnect(false)
                    }
                }
            }
            if (btDeviceBean.hfpState == BluetoothProfile.STATE_DISCONNECTED
                && btDeviceBean.a2dpState == BluetoothProfile.STATE_DISCONNECTED
            ) {
                if (Contacts.isStandbyMode) {
                    // 当前处于待机模式，导致蓝牙断开，则保存该蓝牙到蓝牙已连接列表
                    addConnectedBtToList(btDeviceBean)
                } else {
                    // 非待机模式下，当前蓝牙已断开，但该对象的CP或AA已连接，则不从蓝牙列表移除,否则,则移除
                    if (btDeviceBean.cPConnectedState != IConstant.ConnectState.CONNECTED
                        && btDeviceBean.aAConnectedState != ConnectState.CONNECTED.state
                    ) {
                        LogUtil.d(TAG, "saveConnectedBtDevice : remove bt!")
                        deleteConnectedBtFromList(btDeviceBean)
                    } else {
                        LogUtil.d(TAG, "saveConnectedBtDevice : not remove bt!")
                    }
                }
            }
        }
    }

    /**
     * 保存远距离断开前连接的蓝牙设备.
     *
     * @param btDeviceBean 蓝牙设备
     */
    private fun saveDistanceBtDevice(btDeviceBean: Any) {
        LogUtil.i(TAG, "saveDistanceBtDevice : btDeviceBean = $btDeviceBean")
        GlobalScope.launch(Dispatchers.Default) {
            var btDeviceString = ""
            if (btDeviceBean is BtDeviceBean || btDeviceBean is BluetoothDevice) {
                // 如果是蓝牙设备 则进行格式转换后存储
                val gson = Gson()
                btDeviceString = gson.toJson(btDeviceBean)
            }
            if (btDeviceBean is String) {
                // 如果是字符串，则当前蓝牙已断开，存储默认值
                btDeviceString = btDeviceBean
            }
            // 将已连接的蓝牙设备保存
            MyApplication.getContext().getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE)
                .edit()
                .putString(Contacts.SHARED_PRE_LONG_DISTANCE_BT_DEVICE, btDeviceString)
                .apply()
            if (btDeviceBean !is String) {
                // 蓝牙设备非默认值,则触发蓝牙回连机制
                handler.postDelayed(remoteRunnable, LASH_BACK_PERIOD)
            }
        }
    }

    /**
     * 将已连接的蓝牙对象添加到已连接列表.
     *
     * @param btDeviceBean 已连接的蓝牙对象
     */
    private fun addConnectedBtToList(btDeviceBean: BtDeviceBean) {
        GlobalScope.launch(Dispatchers.IO) {
            LogUtil.i(
                TAG, "addConnectedBtToList : " +
                        "name = ${btDeviceBean.device.name} , " +
                        "address = ${btDeviceBean.device.address} , "+
                        "a2dp = ${btDeviceBean.a2dpState} , "+
                        "hfp = ${btDeviceBean.hfpState} , "+
                        "cPConnectedState = ${btDeviceBean.cPConnectedState} , "+
                        "aAConnectedState = ${btDeviceBean.aAConnectedState}"
            )
            // 在已连接列表中查找该对象
            val btConnectedDevice = btConnectedDeviceList.find {
                TextUtils.equals(
                    it.device.address,
                    btDeviceBean.device.address
                )
            }
            if (btConnectedDevice == null) {
                // 已连接列表不存在该对象,则直接进行添加到列表
                btConnectedDeviceList.add(btDeviceBean)
            } else {
                // 若列表中存在该对象,则先移除，然后添加
                btConnectedDeviceList.remove(btConnectedDevice)
                btConnectedDeviceList.add(btDeviceBean)
            }
            LogUtil.i(TAG, "addConnectedBtToList : link = ${btDeviceBean.androidAutoLinkType}")
            LogUtil.i(
                TAG, "addConnectedBtToList : add success! , " +
                        "size = ${btConnectedDeviceList.size}"
            )
            // 保存列表
            val gson = Gson()
            val btDeviceString = gson.toJson(btConnectedDeviceList)
            // 将已连接的蓝牙设备保存到SharedPreferences中
            MyApplication.getContext().getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE)
                .edit()
                .putString(Contacts.SHARED_PRE_BT_DEVICE, btDeviceString)
                .apply()
        }
    }

    /**
     * 从蓝牙已连接列表中删除断开连接的蓝牙设备.
     *
     * @param btDeviceBean 待删除的蓝牙设备
     */
    private fun deleteConnectedBtFromList(btDeviceBean: BtDeviceBean) {
        GlobalScope.launch(Dispatchers.IO) {
            LogUtil.i(
                TAG, "deleteConnectedBtFromList : " +
                        "name = ${btDeviceBean.device.name} , " +
                        "address = ${btDeviceBean.device.address}"
            )
            // 在已连接列表中查找该对象
            val btConnectedDevice = btConnectedDeviceList.find {
                TextUtils.equals(
                    it.device.address,
                    btDeviceBean.device.address
                )
            }
            if (btConnectedDevice != null) {
                // 若列表中存在该对象,则删除
                btConnectedDeviceList.remove(btConnectedDevice)
                LogUtil.i(
                    TAG, "deleteConnectedBtFromList : delete success! , " +
                            "size = ${btConnectedDeviceList.size}"
                )
                // 保存列表
                val gson = Gson()
                val btDeviceString = gson.toJson(btConnectedDeviceList)
                // 将已连接的蓝牙设备保存到SharedPreferences中
                MyApplication.getContext().getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE)
                    .edit()
                    .putString(Contacts.SHARED_PRE_BT_DEVICE, btDeviceString)
                    .apply()
            }
        }
    }

    /**
     * 从SharedPreferences中读取已连接列表.
     *
     */
    private fun readBtConnectedList() {
        val btDeviceString = MyApplication.getContext().getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE).getString(
            Contacts.SHARED_PRE_BT_DEVICE,
            Contacts.BT_DEVICE_DEFAULT
        )
        LogUtil.i(TAG, "readBtConnectedList : btDeviceString = $btDeviceString")
        if (!TextUtils.equals(btDeviceString, Contacts.BT_DEVICE_DEFAULT)) {
            try {
                // 将读取的数据进行转换
                val gson = Gson()
                // 构建转换类型
                val typeToken = object : TypeToken<CopyOnWriteArrayList<BtDeviceBean>>() {}
                val cleanString = btDeviceString?.replace(Regex("\"mBluetoothBondCache\"\\s*:\\s*\\{.*?\\}"), "")
                    ?.replace(Regex("\\s*\"mAttributionSource\"\\s*:\\s*\\{.*?\\}"), "")
                btConnectedDeviceList = gson.fromJson(cleanString, typeToken.type)
                Log.d(TAG, "readBtConnectedList: zhc6whu:"+btDeviceString)
//                btConnectedDeviceList = gson.fromJson(btDeviceString, typeToken.type)
                // 清除已连接列表中的没有配对的有线CP设备
                val beDevice = btConnectedDeviceList.find {
                    it.carPlayLinkType == Contacts.CP_CONNECT_WIRED
                            && it.bondState == BluetoothDevice.BOND_NONE
                }
                if (beDevice != null) {
                    LogUtil.i(TAG, "readBtConnectedList : " +
                                    "remove wired device = ${beDevice.device.name}")
                    deleteConnectedBtFromList(beDevice)
                }
                LogUtil.i(TAG, "readBtConnectedList : size = ${btConnectedDeviceList.size}")
            } catch (exception: JsonSyntaxException) {
                exception.printStackTrace()
                // 已连接列表恢复默认值
                MyApplication.getContext().getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE)
                    .edit()
                    .putString(Contacts.SHARED_PRE_BT_DEVICE, Contacts.BT_DEVICE_DEFAULT)
                    .apply()
                LogUtil.i(TAG, "readBtConnectedList : exception = ${exception.message}")
            }
        }
    }

    /**
     * 保存当前carPlay连接的蓝牙对象.
     *
     * @param carplayDevice CarplayDevice
     */
    private fun saveCarPlayDevice(carplayDevice: CarplayDevice) {
        if (carplayDevice.connectState == IConstant.ConnectState.CONNECTED) {
            // CP已连接,拒绝本设备蓝牙连接
            rejectCpBluetoothConnect(carplayDevice.bluetoothAddress,true)
            // 构建蓝牙设备对象
            val newDevice = BtDeviceBean(getDevice(carplayDevice.bluetoothAddress))
            LogUtil.i(TAG, "saveCarPlayDevice : device = ${newDevice.device}")
            // 有线carPlay蓝牙列表属性赋值
            if (carplayDevice.isWirelessLink) {
                newDevice.carPlayLinkType = Contacts.CP_CONNECT_WIRELESS
            } else {
                newDevice.carPlayLinkType = Contacts.CP_CONNECT_WIRED
            }
            newDevice.wiredName = newDevice.device.name ?: carplayDevice.deviceName
            newDevice.cPConnectedState = carplayDevice.connectState
            if (carplayDevice.isWirelessLink) {
                newDevice.bondState = BluetoothDevice.BOND_BONDED
            } else {
                newDevice.bondState = BluetoothDevice.BOND_NONE
            }
            newDevice.isSupportWirelessCP = carDmManager.checkWirelessCp(newDevice.device)
           /* newDevice.pairedTime = System.currentTimeMillis()
            // 设备时间添加到数据库
            SettingApplication.settingDataBase.btDeviceDao().insertOrUpdateBtDevice(
                BtPairedBean(newDevice.device.address, newDevice.pairedTime)
            )*/
            // 保存当前连接的carPlay对应的蓝牙设备到已连接列表
            addConnectedBtToList(newDevice)
        }
        if (carplayDevice.connectState == IConstant.ConnectState.IDLE) {
            LogUtil.d(TAG, "saveCarPlayDevice : set cp device default!")
            // CP已断开连接,允许本设备进行蓝牙连接
            rejectCpBluetoothConnect(carplayDevice.bluetoothAddress,false)
            // 当前不在Standby模式下
            if (!Contacts.isStandbyMode) {
                val btDeviceBean =
                    btPairedDeviceList.find { TextUtils.equals(it.device.address, carplayDevice.bluetoothAddress) }
                if (btDeviceBean != null) {
                    //T13J-940 cp断开时，如果蓝牙还连着，无需从蓝牙已连接列表移除
                    if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED
                        || btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED){
                        LogUtil.d(TAG, "saveCarPlayDevice : hfp or a2dp connected , not move")
                    }else{
                        // 当前不在Standby模式下,CP断开，则从蓝牙已连接列表移除
                        LogUtil.d(TAG, "saveCarPlayDevice1 : move cp!")
                        val newDevice = BtDeviceBean(getDevice(carplayDevice.bluetoothAddress))
                        deleteConnectedBtFromList(newDevice)
                    }
                }else{
                    // 当前不在Standby模式下,CP断开，则从蓝牙已连接列表移除
                    LogUtil.d(TAG, "saveCarPlayDevice2 : move cp!")
                    val newDevice = BtDeviceBean(getDevice(carplayDevice.bluetoothAddress))
                    deleteConnectedBtFromList(newDevice)
                }
            }
            // CP连接已断开，判断是否需要关闭热点
            if (Contacts.isNeedCloseHotspot) {
                LogUtil.d(TAG, "saveCarPlayDevice : close hotspot!")
                // 需要关闭热点
                CarWifiManager.instance.closeWifiHotspot()
                Contacts.isNeedCloseHotspot = false
            }
        }
    }

    /**
     * 保存当前AA连接的蓝牙对象.
     *
     * @param devices AA设备列表
     */
    private fun saveAADevice(devices: MutableList<AAutoDevice>) {
        devices.forEach { aAutoDevice ->
            if (aAutoDevice.connectState.state == ConnectState.CONNECTED.state
                && !TextUtils.equals(aAutoDevice.btMacAddress, AAutoDevice.BT_ADDR_NULL)
            ) {
                // 构建蓝牙设备对象
                val newDevice = BtDeviceBean(getDevice(aAutoDevice.btMacAddress))
                LogUtil.i(TAG, "saveAADevice : device = ${newDevice.device}")
                // 有线AA蓝牙列表属性赋值
                if (aAutoDevice.connectType.type
                    == ConnectType.CONNECT_WIRELESS.type
                ) {
                    newDevice.androidAutoLinkType =
                        ConnectType.CONNECT_WIRELESS.type
                }
                if (aAutoDevice.connectType.type
                    == ConnectType.CONNECT_WIRED.type
                ) {
                    newDevice.androidAutoLinkType = ConnectType.CONNECT_WIRED.type
                    newDevice.bondState = BluetoothDevice.BOND_NONE
                }
                newDevice.aAConnectedState = aAutoDevice.connectState.state
                newDevice.wiredName =
                    newDevice.device.name ?: aAutoDevice.btDeviceName
                newDevice.isSupportWirelessAA = true
               /* newDevice.pairedTime = System.currentTimeMillis()
                // 设备时间添加到数据库
                SettingApplication.settingDataBase.btDeviceDao().insertOrUpdateBtDevice(
                    BtPairedBean(newDevice.device.address, newDevice.pairedTime)
                )*/
                // 保存当前连接的AA对应的蓝牙到蓝牙已连接列表
                addConnectedBtToList(newDevice)
            }
            if (aAutoDevice.connectState.state == ConnectState.DISCONNECTED.state) {
                if (!Contacts.isStandbyMode && !Contacts.isBtLashBack) {
                    LogUtil.d(TAG, "saveAADevice : delete the device!")
                    // 当前不在standby模式下导致AA连接已断开，将当前AA对应的蓝牙设备从蓝牙已连接列表中移除
                    val newDevice = BtDeviceBean(getDevice(aAutoDevice.btMacAddress))
                    if (getA2dpConnectionState(newDevice.device) != BluetoothProfile.STATE_CONNECTED
                        && getHfpConnectionState(newDevice.device)
                        != BluetoothProfile.STATE_CONNECTED
                    ) {
                        // AA断开,当前的蓝牙或音乐协议未断开,则不移除,都断开，则移除
                        deleteConnectedBtFromList(newDevice)
                    }
                }
                // AA连接已断开，判断是否需要关闭热点
                if (Contacts.isNeedCloseHotspot) {
                    LogUtil.d(TAG, "saveAADevice : close hotspot!")
                    // 需要关闭热点
                    CarWifiManager.instance.closeWifiHotspot()
                    Contacts.isNeedCloseHotspot = false
                }
            }
        }
    }

    /**
     * 是否为远距离导致蓝牙断开.
     *
     * @param isRemoteDisconnect Boolean
     */
    private fun setIsRemoteDisconnect(isRemoteDisconnect: Boolean) {
        LogUtil.i(TAG, "setIsRemoteDisconnect : isRemoteDisconnect = $isRemoteDisconnect")
        this.isRemoteDisconnect = isRemoteDisconnect
    }

    /**
     * 获取是否为远距离导致蓝牙断开.
     *
     * @return Boolean
     */
    private fun getIsRemoteDisconnect(): Boolean {
        LogUtil.i(TAG, "getIsRemoteDisconnect : isRemoteDisconnect = $isRemoteDisconnect")
        return isRemoteDisconnect
    }

    /**
     * 获取已连接列表.
     *
     * @return 已连接列表
     */
    fun getConnectedDevices(): CopyOnWriteArrayList<BtDeviceBean> {
        val list = btPairedDeviceList.filter {
            it.cPConnectedState ==  IConstant.ConnectState.CONNECTED
                    ||it.aAConnectedState == ConnectState.CONNECTED.state
                    ||it.a2dpState ==  BluetoothProfile.STATE_CONNECTED
                    ||it.hfpState == BluetoothProfile.STATE_CONNECTED
        }
        LogUtil.i(TAG, "getConnectedDevices : list = ${list.size}")
        return CopyOnWriteArrayList(list)
    }

    /**
     * 读取当远距离断开的蓝牙设备.
     *
     * @return BtDeviceBean
     */
    private fun readRemoteDisconnectBtDevice(): BluetoothDevice? {
        val btString = MyApplication.getContext().getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE).getString(
            Contacts.SHARED_PRE_LONG_DISTANCE_BT_DEVICE,
            Contacts.BT_DEVICE_DEFAULT
        )
        LogUtil.i(TAG, "readBtDevice : btString = $btString")
        if (!TextUtils.equals(btString, Contacts.BT_DEVICE_DEFAULT)) {
            return try {
                // 非默认值，则进行返回
                val gson = Gson()
                gson.fromJson(btString, BluetoothDevice::class.java)
            } catch (exception: JsonSyntaxException) {
                exception.printStackTrace()
                // 远距离断开蓝牙恢复默认值
                MyApplication.getContext().getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE)
                    .edit()
                    .putString(
                        Contacts.SHARED_PRE_LONG_DISTANCE_BT_DEVICE,
                        Contacts.BT_DEVICE_DEFAULT
                    )
                    .apply()
                LogUtil.i(TAG, "readRemoteDisconnectBtDevice : exception = ${exception.message}")
                null
            }
        }
        return null
    }

    /**
     * 开始蓝牙重连(突然下电或退出standby模式后回连).
     *
     */
    fun startBtReconnection() {
        val bluetoothIsOpen = bluetoothAdapter?.state == BluetoothAdapter.STATE_ON
        LogUtil.i(TAG, "startBtReconnection : size = ${btConnectedDeviceList.size} , bluetoothIsOpen = $bluetoothIsOpen")
        if(!bluetoothIsOpen){
            return
        }
        LogUtil.i(TAG, "startBtReconnection : Contacts.isConnectCpProxy = ${Contacts.isConnectCpProxy} , Contacts.isConnectDevCpProxy = ${Contacts.isConnectDevCpProxy}")
        if (headsetClient == null || a2dpSink == null || btConnectedDeviceList.size == 0) {
            // 蓝牙电话和音乐协议为空或蓝牙已连接列表为空或CP协议未连接,则无需进行蓝牙重连
            if (btConnectedDeviceList.size == 0) {
                // 回连标志位置为false
                Contacts.isBtLashBack = false
            }
            LogUtil.i(TAG, "startBtReconnection : return")
            return
        }
        if (Contacts.isBtLashBack) {
            LogUtil.i(TAG, "startBtReconnection")
            // 将蓝牙已连接列表中的蓝牙进行回连
            val btCpAaDevice =
                btConnectedDeviceList.find {
                    it.cPConnectedState == IConstant.ConnectState.CONNECTED
                            || it.aAConnectedState == ConnectState.CONNECTED.state
                }
            if (btCpAaDevice != null) {
                // 存在CP或AA连接,则回连对应cp或aa
                if (btCpAaDevice.cPConnectedState == IConstant.ConnectState.CONNECTED) {
                    if(btCpAaDevice.carPlayLinkType == Contacts.CP_CONNECT_WIRED){
                        //如果是有线，由cp自己处理有线cp回连
                        LogUtil.d(TAG, "startBtReconnection : not to connect cp")
                    }else{
                        LogUtil.d(TAG, "startBtReconnection : start connect cp!")
                        // CP处于连接状态,则回连CP
                        connectDevice(btCpAaDevice, Contacts.BT_CONNECT_CP)
                    }
                } else if (btCpAaDevice.aAConnectedState == ConnectState.CONNECTED.state) {
                    LogUtil.d(TAG, "startBtReconnection : start connect aa!")
                    // AA连接状态,则只回连蓝牙电话
                    connectDevice(btCpAaDevice, Contacts.BT_CONNECT_AA)
                }
            } else {
                // 不存在CP或AA,则存在蓝牙连接,此时回连最后连接的蓝牙
                LogUtil.d(TAG, "startBtReconnection : start connect bt!")
                // 当前设备未连接CP,也未连接AA,则重连蓝牙
                //TODO 优先回连常用设备
                //遍历已连接的设备，看看已连接的设备中是否有常用设备
                val btConnectedDevice = btConnectedDeviceList.find {
                    it.isPreferencesDevice
                }
                if (btConnectedDevice != null) {
                    connectDevice(btConnectedDevice, Contacts.BT_CONNECT_ALL)
                }
                connectDevice(btConnectedDeviceList.last(), Contacts.BT_CONNECT_ALL)
            }
            handler.postDelayed({
                // 延迟1s后,回连标志位置为false
                Contacts.isBtLashBack = false
            }, PERIOD)
        } else {
            // 蓝牙无需回连
            LogUtil.i(TAG, "startBtReconnection : no need reconnection bt!")
        }
    }

    // 清除 PBAP 相关数据
    private fun clearPbapData() {
        Log.d("BTLog" , "clearPbapData: 清除 PBAP 相关数据")
            clearContacts(context.contentResolver)
            clearCallLogs(context.contentResolver)
    }

    // 清除联系人数据
    private fun clearContacts(contentResolver: ContentResolver) {
        contentResolver.delete(
            ContactsContract.RawContacts.CONTENT_URI,
            null,
            null
        )
    }

    // 清除通话记录数据
    private fun clearCallLogs(contentResolver: ContentResolver) {
        contentResolver.delete(
            CallLog.Calls.CONTENT_URI,
            null,
            null
        )
    }

    /**
     * 远距离断开回连.
     *
     */
    private fun remoteDisconnectedLashBack() {
        GlobalScope.launch(Dispatchers.Default) {
            // 当前存在远距离断开的蓝牙设备,则进行重连
            val disconnectDevice = readRemoteDisconnectBtDevice()
            if (disconnectDevice != null) {
                if(getHfpConnectionState(disconnectDevice) == BluetoothProfile.STATE_CONNECTED
                    || getA2dpConnectionState(disconnectDevice) == BluetoothProfile.STATE_CONNECTED){
                    // 当前设备已连接，则不做响应
                    LogUtil.d(
                        TAG,
                        "remoteDisconnectedLashBack : this device(${disconnectDevice.address}) has connected, " +
                                "the connection will not be reconnected!"
                    )
                }else{
                    LogUtil.d(TAG, "remoteDisconnectedLashBack : Lash back device!")
                    // 扫描设备与短距离断开设备一致,且信号在90以内，且当前不存在已连接设备，则重连
                    connectDeviceArbitration(
                        createBtDeviceBean(disconnectDevice),
                        Contacts.BT_CONNECT_ALL
                    )
                }
            } else {
                LogUtil.d(TAG, "remoteDisconnectedLashBack : not need to conn bt!")
            }
        }
    }
    /**
     * 设置协议栈是否拒绝手机端下一次HFP的连接请求.
     *
     * @param isReject 是否拒绝
     */
    fun rejectHfpConnect(address: String,isReject: Boolean) {
        LogUtil.i(TAG, "rejectHfpConnect : address = $address, isReject = $isReject")
        //bluetoothAdapter!!.AutoRejectConn_ex(address.toByteArray(),Contacts.PROFILE_ID_HFP, isReject)
    }

    /**
     * 设置协议栈是否拒绝手机端下一次A2DP的连接请求.
     *
     * @param isReject 是否拒绝
     */
    fun rejectA2dpConnect(address: String,isReject: Boolean) {
        LogUtil.i(TAG, "rejectA2dpConnect :  address = $address , isReject = $isReject")
        //bluetoothAdapter!!.AutoRejectConn_ex(address.toByteArray(),Contacts.PROFILE_ID_A2DP, isReject)
        //bluetoothAdapter!!.AutoRejectConn_ex(address.toByteArray(),Contacts.PROFILE_ID_AVRCP, isReject)
    }

    /**
     * 设置协议栈是否拒绝cp设备的a2dp和hfp连接请求.
     *
     * @param isReject 是否拒绝
     */
    //TODO 解除注释
    fun rejectCpBluetoothConnect(address: String,isReject: Boolean) {
        LogUtil.i(TAG, "rejectCpBluetoothConnect :  address = $address , isReject = $isReject")
//        bluetoothAdapter!!.AutoRejectConn_ex(getDevice(address),BluetoothAdapter.APP_CP, isReject)
    }

    /**
     * 设置协议栈是否拒绝aa设备的a2dp连接请求.
     *
     * @param isReject 是否拒绝
     */
    //TODO 解除注释
    fun rejectAaBluetoothConnect(address: String,isReject: Boolean) {
        LogUtil.i(TAG, "rejectAaBluetoothConnect :  address = $address , isReject = $isReject")
//        bluetoothAdapter!!.AutoRejectConn_ex(getDevice(address),BluetoothAdapter.APP_AA, isReject)
    }

    /**
     * 释放对象.
     *
     */
    fun unInitBtManager() {
        LogUtil.d(TAG, "unInitBtManager:")
        // 注销蓝牙广播
        unregisterBluetoothReceiver(MyApplication.getContext())
        // 注销CP和AA相关监听
        carDmManager.unregisterDeviceListenerCp(javaClass.simpleName)
        carDmManager.unregisterDeviceListenerAa(javaClass.simpleName)
        // 移除蓝牙广播监听
        bluetoothReceiver?.unregisterBtStateListener(javaClass.simpleName)
        bluetoothReceiver = null
        bluetoothAdapter = null
        headsetClient = null
        a2dpSink = null
        pbapClient = null
        mapClient = null
    }

    /*----------------------------------蓝牙配对-连接连接事件处理-------------------------------------*/

    /**
     * 获取蓝牙扫描列表.
     *
     * @return btScanDevicesList
     */
    fun getBtScanList(): CopyOnWriteArrayList<BtDeviceBean> {
        LogUtil.i(TAG, "getBtScanList : btScanDevicesList = ${btScanDevicesList.size}")
        return btScanDevicesList
    }

    /**
     * 清空扫描列表.
     *
     */
    fun clearBtScanList() {
        LogUtil.d(TAG, "clearBtScanList : ")
        // 清空扫描列表
        btScanDevicesList.clear()
        // 更新扫描列表
        for (listener in btInfoListenerMap!!.values) {
            listener.onUpdateBtScanList(btScanDevicesList)
        }
    }

    /**
     * 获取蓝牙已配对列表.
     *
     * @return
     */
    fun getBtPairedList(): CopyOnWriteArrayList<BtDeviceBean> {
        // 初始化蓝牙已配对列表
        initPairedList()
        btPairedDeviceList.sort()
        LogUtil.i(TAG, "getBtPairedList : size = ${btPairedDeviceList.size}")
        return btPairedDeviceList
    }

    /**
     * 获取蓝牙扫描列表,不排序.
     *
     * @return
     */
    fun getBtPairedListNoSort(): CopyOnWriteArrayList<BtDeviceBean> {
        LogUtil.i(TAG, "getBtPairedList : size = ${btPairedDeviceList.size}")
        return btPairedDeviceList
    }

    /**
     * 获取蓝牙配对中状态.
     *
     * @return
     */
    fun getBtPairing(): Boolean {
        LogUtil.i(TAG, "getBtPairing : isPairing = $isPairing")
        return isPairing
    }

    /**
     * 蓝牙自动连接，连接a2dp和hfp.
     *
     * @param device 待连接设备
     */
    fun autoConnectA2dpAndHfp(device: BluetoothDevice){
        LogUtil.i(TAG, "autoConnectA2dpAndHfp : device = ${device.name}(${device.address})")
        connectBtMusic(device)
        connectBtPhone(device)
    }

    /**
     * 蓝牙设备连接仲裁.
     *
     * @param btDeviceBean 待连接设备
     * @param connectType 连接类型
     */
    fun connectDeviceArbitration(
        btDeviceBean: BtDeviceBean,
        connectType: Int = Contacts.BT_CONNECT_ALL
    ) {
        val connectedList = getConnectedDevices()
        connectedList.forEach {
            LogUtil.i(TAG, "connectDeviceArbitration : connectedList item = $it")
        }
        if (connectedList.size > 1 && !hasExistConnectedDevice(btDeviceBean)) {
            LogUtil.i(TAG, "connectDeviceArbitration : connected two device!")
            // 已连接2个设备(不包含待连接的设备),则显示连接替换弹窗
            GlobalScope.launch(Dispatchers.Main){
                showConnectedSelectedDialog(
                    connectedList[0],
                    connectedList[1],
                    btDeviceBean,
                    connectType
                )
            }
        } else if (connectedList.size == 1
            || (connectedList.size == 2 && hasExistConnectedDevice(btDeviceBean))
        ) {
            LogUtil.i(TAG, "connectDeviceArbitration : connected one device!")
            // 已连接1个设备,则需要根据已连接类型进行仲裁或已连接两个设备,但其中一个包含待连接设备
            when (connectType) {
                Contacts.BT_CONNECT_A2DP -> {
                    // 新设备需要连接蓝牙音乐，则需判断当前是否存在蓝牙音乐的连接,存在,则断开之前的,连接新的设备
                    val state = getA2dpConnectionState(btDeviceBean.device)
                    if(state ==  BluetoothProfile.STATE_CONNECTED || state == BluetoothProfile.STATE_CONNECTING){
                        //如果这台设备已经连上了a2dp，无需再断开
                    }else{
                        disconnectAllDeviceA2dp()
                    }
                    // 连接新设备的蓝牙音乐
                    handler.postDelayed(
                        { connectDevice(btDeviceBean, Contacts.BT_CONNECT_A2DP) },
                        LASH_BACK_PERIOD
                    )
                }

                Contacts.BT_CONNECT_ALL -> {
                    // 新设备需要连接蓝牙音乐，则需判断当前是否存在蓝牙音乐的连接,存在,则断开之前的,连接新的设备
                    val state = getA2dpConnectionState(btDeviceBean.device)
                    if(state ==  BluetoothProfile.STATE_CONNECTED || state == BluetoothProfile.STATE_CONNECTING){
                        //如果这台设备已经连上了a2dp，无需再断开
                    }else{
                        disconnectAllDeviceA2dp()
                    }
                    // 连接新设备的蓝牙音乐
                    handler.postDelayed(
                        { connectDevice(btDeviceBean, Contacts.BT_CONNECT_ALL) },
                        LASH_BACK_PERIOD
                    )
                }

                Contacts.BT_CONNECT_CP -> {
                    // 新设备需要连接CP,则需判断当前是否存在CP或AA连接，存在，则断开，然后连接新设备CP
                    disconnectAllDeviceCpOrAa()
                    // 连接CP
                    handler.postDelayed(
                        { connectDevice(btDeviceBean, Contacts.BT_CONNECT_CP) },
                        LASH_BACK_PERIOD
                    )
                }

                Contacts.BT_CONNECT_AA -> {
                    // 新设备需要连接AA,则需判断当前是否存在CP或AA连接，存在，则断开，然后连接新设备AA
                    disconnectAllDeviceCpOrAa()
                    // 连接AA
                    handler.postDelayed(
                        { connectDevice(btDeviceBean, Contacts.BT_CONNECT_AA) },
                        LASH_BACK_PERIOD
                    )
                }

                else -> {
                    // 其他类型的连接，无需仲裁,直接连接即可
                    connectDevice(btDeviceBean, connectType)
                }
            }
        } else {
            LogUtil.i(TAG, "connectDeviceArbitration : no device connected!")
            // 当前不存在设备连接,则直接进行连接,无需仲裁
            connectDevice(btDeviceBean, connectType)
        }
    }

    /**
     * 连接蓝牙.
     *
     * @param btDeviceBean 蓝牙对象
     * @param connectType 连接类型
     */
    private fun connectDevice(
        btDeviceBean: BtDeviceBean,
        connectType: Int = Contacts.BT_CONNECT_ALL
    ) {
        LogUtil.d(
            TAG, "connectDevice : " +
                    "btDeviceBean = ${btDeviceBean.device.name}(${btDeviceBean.device.address}) , " +
                    "connectType = $connectType"
        )
        val event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
        if (event != null) {
            val bundle = event.payload
            val status = bundle.getInt(VDKey.STATUS)
            val type = bundle.getInt(VDKey.TYPE)
            val isWireless = bundle.getBoolean(VDKey.ENABLE)
            LogUtil.d(TAG, "closeWifiHotspot : $event+$type+$isWireless")
            if(status > 0){
                showCloseHotspotConfirmation(type,btDeviceBean,connectType)
                return
            }
        }
        stopScanBtDevice()
        // 判断当前列表是否存在已连接的设备
        GlobalScope.launch(Dispatchers.Default) {
            if (isConnecting(btDeviceBean.device)) {
                // 当前设备正在连接，不做响应
                LogUtil.d(TAG, "connectDevice : device is currently connecting!")
                return@launch
            }
            when (connectType) {
                Contacts.BT_CONNECT_HFP -> {
                    LogUtil.d(TAG, "connectDevice :  connect hfp!")
                    // 不需要连接蓝牙音乐
                    isNeedConnectA2dp = false
                    stopScanBtDevice()
                    // 当前设备未连接蓝牙电话，则进行蓝牙电话
                    connectBtPhone(btDeviceBean.device)
                }

                Contacts.BT_CONNECT_A2DP -> {
                    LogUtil.d(TAG, "connectDevice :  connect a2dp!")
                    // 需要连接蓝牙音乐
                    isNeedConnectA2dp = true
                    stopScanBtDevice()
                    // 当前设备未连接蓝牙音乐，则进行蓝牙音乐连接
                    connectBtMusic(btDeviceBean.device)
                }

                Contacts.BT_CONNECT_ALL -> {
                    LogUtil.d(TAG, "connectDevice :  connect hfp and a2dp!")
                    // 需要连接蓝牙音乐和电话,先连接蓝牙电话,电话连接完成后,再连接蓝牙音乐
                    if (btDeviceBean.isAlwaysPlayPhoneMedia) {
                        isNeedConnectA2dp = true
                        Settings.System.putInt(
                            context?.contentResolver,
                            "settings_phone_always_play_audio",
                            1
                        )//1表示On
                    } else {
                        isNeedConnectA2dp = false
                    }
                    stopScanBtDevice()
                    // 设置优先级
                    mapClient?.setPriority(btDeviceBean.device, BluetoothProfile.PRIORITY_ON)
                    pbapClient?.setPriority(btDeviceBean.device, BluetoothProfile.PRIORITY_ON)
                    // 当前设备未连接蓝牙电话，则进行蓝牙电话
                    connectBtPhone(btDeviceBean.device)
                }

                Contacts.BT_CONNECT_AA -> {
                    LogUtil.d(TAG, "connectDevice :  connect aa!")
                    // 连接AA
                    stopScanBtDevice()
                    carDmManager.connectDeviceAa(btDeviceBean.device)
                }

                Contacts.BT_CONNECT_CP -> {
                    LogUtil.d(TAG, "connectDevice :  connect cp!")
                    //T13JSUPPLY-252 连接cp停止扫描
                    stopScanBtDevice()
                    // 连接CP
                    carDmManager.connectDeviceCp(btDeviceBean.device)
                }

                Contacts.BT_CONNECT_AA_TO_CP -> {
                    LogUtil.d(TAG, "connectDevice : aa connect to cp!")
                    stopScanBtDevice()
                    // 断开当前已连接的AA，并切换到CP
                    carDmManager.connectDeviceAaToCp(btDeviceBean.device)
                }

                Contacts.BT_CONNECT_CP_TO_AA -> {
                    stopScanBtDevice()
                    LogUtil.d(TAG, "connectDevice : cp connect to aa!")
                    // 断开当前已连接的CP，并切换到AA
                    carDmManager.connectDeviceCpToAA(btDeviceBean.device)
                }
            }
        }
    }

    private fun showCloseHotspotConfirmation(type: Int,btDeviceBean: BtDeviceBean,connectType: Int = Contacts.BT_CONNECT_ALL) {
        Handler(Looper.getMainLooper()).post {
            ConfirmDialog(MyApplication.getContext()).apply {
                var phoneLinkType = ""
                var serverId = VDValuePhoneLink.ServerId.COMMON
                if (type == 8){
                    phoneLinkType="ICCOA Carlink"
                    serverId = VDValuePhoneLink.ServerId.CARLINK
                }else if (type ==1){
                    phoneLinkType="Apple CarPlay"
                    serverId = VDValuePhoneLink.ServerId.CARPLAY
                }else if (type == 7){
                    phoneLinkType="HUAWEI HiCar"
                    serverId = VDValuePhoneLink.ServerId.HICAR
                }
                // 检查设备是否已经配对过
                if (btDeviceBean.device.bondState != BluetoothDevice.BOND_BONDED) {
                    LogUtil.d(TAG, "Device is not previously paired: ${btDeviceBean.device.name}")
                    setTips(
                        MyApplication.getContext()
                            .getString(R.string.bt_cp_phone_wire_tips2, phoneLinkType)
                    )
                } else {
                    LogUtil.d(TAG, "Device is previously paired: ${btDeviceBean.device.name}")
                    setTips(
                        MyApplication.getContext()
                            .getString(R.string.bt_cp_phone_wire_tips3, phoneLinkType)
                    )
                }
                setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
                    override fun onConfirmClick() {
                        stopScanBtDevice()
                        val payload = Bundle()
                        payload.putInt(
                            VDKey.TYPE,
                            serverId
                        )
                        val event1 =
                            VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
                        val bundle = event1.payload
                        val devicePhoneLink = bundle.getParcelable<VDLinkDevice>(VDKey.INFO)
                        payload.putParcelable(
                            VDKey.DATA,
                            devicePhoneLink
                        ) //(VDLinkDevice)device,要断开的设备
                        val event = VDEvent(VDEventPhoneLink.DISCONNECT_DEVICE, payload)
                        VDBus.getDefault().set(event)
                        val carWifiManager = CarWifiManager.instance
                        carWifiManager.closeFirstWifiHotspot()
                        // 判断当前列表是否存在已连接的设备
                        GlobalScope.launch(Dispatchers.Default) {
                            if (isConnecting(btDeviceBean.device)) {
                                // 当前设备正在连接，不做响应
                                LogUtil.d(TAG, "connectDevice : device is currently connecting!")
                                return@launch
                            }
                            when (connectType) {
                                Contacts.BT_CONNECT_HFP -> {
                                    LogUtil.d(TAG, "connectDevice :  connect hfp!")
                                    // 不需要连接蓝牙音乐
                                    isNeedConnectA2dp = false
                                    stopScanBtDevice()
                                    // 当前设备未连接蓝牙电话，则进行蓝牙电话
                                    connectBtPhone(btDeviceBean.device)
                                }

                                Contacts.BT_CONNECT_A2DP -> {
                                    LogUtil.d(TAG, "connectDevice :  connect a2dp!")
                                    // 需要连接蓝牙音乐
                                    isNeedConnectA2dp = true
                                    stopScanBtDevice()
                                    // 当前设备未连接蓝牙音乐，则进行蓝牙音乐连接
                                    connectBtMusic(btDeviceBean.device)
                                }

                                Contacts.BT_CONNECT_ALL -> {
                                    LogUtil.d(TAG, "connectDevice :  connect hfp and a2dp!")
                                    // 需要连接蓝牙音乐和电话,先连接蓝牙电话,电话连接完成后,再连接蓝牙音乐
                                    isNeedConnectA2dp = false
                                    stopScanBtDevice()
                                    // 当前设备未连接蓝牙电话，则进行蓝牙电话
                                    connectBtPhone(btDeviceBean.device)
                                }

                                Contacts.BT_CONNECT_AA -> {
                                    LogUtil.d(TAG, "connectDevice :  connect aa!")
                                    // 连接AA
                                    stopScanBtDevice()
                                    carDmManager.connectDeviceAa(btDeviceBean.device)
                                }

                                Contacts.BT_CONNECT_CP -> {
                                    LogUtil.d(TAG, "connectDevice :  connect cp!")
                                    //T13JSUPPLY-252 连接cp停止扫描
                                    stopScanBtDevice()
                                    // 连接CP
                                    carDmManager.connectDeviceCp(btDeviceBean.device)
                                }

                                Contacts.BT_CONNECT_AA_TO_CP -> {
                                    LogUtil.d(TAG, "connectDevice : aa connect to cp!")
                                    stopScanBtDevice()
                                    // 断开当前已连接的AA，并切换到CP
                                    carDmManager.connectDeviceAaToCp(btDeviceBean.device)
                                }

                                Contacts.BT_CONNECT_CP_TO_AA -> {
                                    stopScanBtDevice()
                                    LogUtil.d(TAG, "connectDevice : cp connect to aa!")
                                    // 断开当前已连接的CP，并切换到AA
                                    carDmManager.connectDeviceCpToAA(btDeviceBean.device)
                                }
                            }
                        }
                        dismiss()
                    }
                    override fun onCancelClick() {
                        dismiss()
                    }
                })
                show()
            }
        }
    }

    fun getPhoneLinkStatus(): Boolean {
        val event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
        if (event != null) {
            val bundle = event.payload
            val status = bundle.getInt(VDKey.STATUS)
            val type = bundle.getInt(VDKey.TYPE)
            val isWireless = bundle.getBoolean(VDKey.ENABLE)
            LogUtil.d(TAG, "getPhoneLinkStatus : $status")
            if (status == 2) {
                return true
            }
        }
        return false
    }

    /**
     * 通过蓝牙mac地址进行蓝牙连接(提供CP或AA设备管理的关联连接)
     *
     * @param address 蓝牙mac地址
     * @param connectType 连接类型
     */
    fun connectDeviceOfAddress(address: String, connectType: Int) {
        LogUtil.i(TAG, "connectDeviceOfAddress : address = $address , connectType = $connectType")
        // 从蓝牙配对列表寻找该对象,若存在,则开始连接
        val btDeviceBean = btPairedDeviceList.find { TextUtils.equals(it.device.address, address) }
        if (btDeviceBean != null) {
            LogUtil.d(TAG, "connectDeviceOfAddress : start connect!")
            // 存在该对象,则进行蓝牙连接
            stopScanBtDevice()
            connectDevice(btDeviceBean, connectType)
        }
    }

    /**
     * 断开蓝牙设备.
     *
     * @param btDeviceBean 蓝牙对象
     * @param disconnectType 断开连接类型
     * @param isAutoConnectBt AA或CP断开后是否自动连接蓝牙
     */
    fun disconnectDevice(
        btDeviceBean: BtDeviceBean,
        disconnectType: Int = Contacts.BT_CONNECT_ALL,
        isAutoConnectBt: Boolean = false
    ) {
        LogUtil.i(
            TAG, "disconnectDevice : " +
                    "disconnect name = ${btDeviceBean.device.name}(${btDeviceBean.device.address}) , " +
                    "disconnectType = $disconnectType , " +
                    "isAutoConnectBt = $isAutoConnectBt"
        )
        when (disconnectType) {
            Contacts.BT_CONNECT_HFP -> {
                if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED) {
                    // 断开蓝牙电话
                    disconnectBtPhone(btDeviceBean.device)
                    LogUtil.d(TAG, "disconnectDevice : disconnect bt phone!")
                }
            }

            Contacts.BT_CONNECT_A2DP -> {
                if (btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED) {
                    // 断开蓝牙音乐
                    disconnectBtMusic(btDeviceBean.device)
                    LogUtil.d(TAG, "disconnectDeviceMusic : disconnect bt music!")
                }
            }

            Contacts.BT_CONNECT_ALL -> {
                // 蓝牙连接断开
                if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED) {
                    // 断开蓝牙电话
                    disconnectBtPhone(btDeviceBean.device)
                    LogUtil.d(TAG, "disconnectDevice : disconnect bt phone!")
                }
                if (btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED) {
                    // 断开蓝牙音乐
                    disconnectBtMusic(btDeviceBean.device)
                    LogUtil.d(TAG, "disconnectDeviceMusic : disconnect bt music!")
                }
                // 断开pbap
                disconnectPbap(btDeviceBean.device)
                LogUtil.d(TAG, "disconnectDeviceMusic : disconnect bt pbap!")
            }

            Contacts.BT_CONNECT_CP -> {
                LogUtil.d(TAG, "disconnectDeviceMusic : disconnect cp!")
                if (btDeviceBean.carPlayLinkType == Contacts.CP_CONNECT_WIRED) {
                    // 断开有线CP设备,并不进行有线对应的蓝牙回连
                    carDmManager.disconnectDeviceCp(btDeviceBean.device, false)
                } else {
                    // 断开CP设备
                    carDmManager.disconnectDeviceCp(btDeviceBean.device, isAutoConnectBt)
                }
            }

            Contacts.BT_CONNECT_AA -> {
                LogUtil.d(TAG, "disconnectDeviceMusic : disconnect aa!")
               /* if (btDeviceBean.androidAutoLinkType == ConnectType.CONNECT_WIRED.type) {
                    // 在断开有线AA设备
                    carDmManager.disconnectDeviceAa(btDeviceBean.device, false)
                } else {
                    // 在断开无线无线AA设备
                    carDmManager.disconnectDeviceAa(btDeviceBean.device, isAutoConnectBt)
                }*/
                carDmManager.disconnectDeviceAa(btDeviceBean.device, isAutoConnectBt)
            }
        }
        if (getBtPairedListNoSort().size <= 1) {
            // 如果当前已配对列表只有一个蓝牙设备，则断开连接后，停止自动连接计时器
            for (listener in btInfoListenerMap!!.values) {
                listener.onStopAutoConnectTimerTask()
            }
        }
    }

    /**
     * 通过蓝牙mac地址断开蓝牙连接(提供CP或AA设备管理的关联连接断开)
     *
     * @param address 蓝牙mac地址
     * @param connectType 连接类型
     */
    fun disconnectDeviceOfAddress(address: String, connectType: Int) {
        LogUtil.i(
            TAG, "disconnectDeviceOfAddress : " +
                    "address = $address , " +
                    "connectType = $connectType"
        )
        // 从蓝牙配对列表寻找该对象,若存在,则断开
        val btDeviceBean = btPairedDeviceList.find { TextUtils.equals(it.device.address, address) }
        if (btDeviceBean != null) {
            LogUtil.d(TAG, "connectDeviceOfAddress : start disconnect!")
            // 存在该对象,则进行蓝牙连接
            disconnectDevice(btDeviceBean, connectType)
        }
    }

    /**
     * 断开指定设备的所有连接.
     *
     * @param device 断开设备
     */
    fun disconnectedDevice(device: BtDeviceBean) {
        LogUtil.i(TAG, "disconnectedDevice : device = $device")
        if (device.cPConnectedState == IConstant.ConnectState.CONNECTED) {
            LogUtil.d(TAG, "disconnectDevice : disconnect cp!")
            // CP已连接,断开CP
            disconnectDevice(device, Contacts.BT_CONNECT_CP, false)
        } else if (device.aAConnectedState == ConnectState.CONNECTED.state) {
            LogUtil.d(TAG, "disconnectDevice : disconnect aa!")
            // AA已连接,断开AA
            disconnectDevice(device, Contacts.BT_CONNECT_AA, false)
        } else {
            if (device.hfpState == BluetoothProfile.STATE_CONNECTED) {
                LogUtil.d(TAG, "disconnectDevice : disconnect bt phone!")
                // 断开蓝牙电话
                disconnectDevice(device, Contacts.BT_CONNECT_HFP)
            }
            if (device.a2dpState == BluetoothProfile.STATE_CONNECTED) {
                LogUtil.d(TAG, "disconnectDevice : disconnect bt music!")
                // 断开蓝牙音乐
                disconnectDevice(device, Contacts.BT_CONNECT_A2DP)
            }
            if (device.a2dpState == BluetoothProfile.STATE_CONNECTED) {
                LogUtil.d(TAG, "disconnectDevice : disconnect bt music!")
                // 断开蓝牙音乐
                disconnectDevice(device, Contacts.BT_CONNECT_A2DP)
            }
        }
    }

    /**
     * 关闭蓝牙开关时，断开当前连接的蓝牙设备的状态，更新列表状态.
     *
     */
    fun disconnectDeviceStatus() {
        LogUtil.d(TAG, "disconnectDeviceStatus :")
        // 断开当前已连接的设备,更新列表信息
        btPairedDeviceList.forEachIndexed { index, device ->
            if (device.hfpState == BluetoothProfile.STATE_CONNECTED
                || device.a2dpState == BluetoothProfile.STATE_CONNECTED
            ) {
                device.hfpState = BluetoothProfile.STATE_DISCONNECTED
                device.a2dpState = BluetoothProfile.STATE_DISCONNECTED
                btPairedDeviceList[index] = device
                return@forEachIndexed
            }
        }
    }

    /**
     * 断开当前已连接的蓝牙音乐(仲裁使用).
     *
     */
    private fun disconnectAllDeviceA2dp() {
        // 从蓝牙配对列表中查询音乐连接的对象
        val deviceBean =
            btPairedDeviceList.find { it.a2dpState == BluetoothProfile.STATE_CONNECTED }
        if (deviceBean != null) {
            // 存在蓝牙音乐连接对象,则直接断开音乐
            disconnectDevice(deviceBean, Contacts.BT_CONNECT_A2DP)
        }
    }

    /**
     * 断开当前已连接的CP或AA设备(仲裁使用).
     *
     */
    private fun disconnectAllDeviceCpOrAa() {
        // 从已连接列表中查询CP或AA连接的蓝牙设备对象
        val deviceBean =
            btPairedDeviceList.find {
                it.cPConnectedState == IConstant.ConnectState.CONNECTED
                        || it.aAConnectedState == ConnectState.CONNECTED.state
            }
        if (deviceBean != null) {
            if (deviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED) {
                // 存在CP连接对象,则直接断开CP，断开后不需要回连蓝牙
                disconnectDevice(deviceBean, Contacts.BT_CONNECT_CP, false)
            } else {
                // 存在AA连接对象,则直接断开AA,断开后不需要回连蓝牙
                disconnectDevice(deviceBean, Contacts.BT_CONNECT_AA, false)
            }
        }
    }

    /**
     * 判断目标设备是否在已连接列表设备中存在.
     *
     * @param device 目标设备
     * @return Boolean
     */
    fun hasExistConnectedDevice(device: BtDeviceBean): Boolean {
        val connectBean = getConnectedDevices().find {
            TextUtils.equals(
                it.device.address,
                device.device.address
            )
        }
        val isExist = connectBean != null
        LogUtil.i(TAG, "hasExistConnectedDevice : isExist = $isExist")
        return isExist
    }

    /**
     * 打开carPlay.
     *
     * @param beDevice 蓝牙设备.
     */
    fun openCarPlay(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "openCarPlay : beDevice = ${beDevice.device.name}")
        carDmManager.requestForegroundCp()
    }

    /**
     * 打开AndroidAuto.
     *
     * @param beDevice 蓝牙设备
     */
    fun openAndroidAuto(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "openAndroidAuto : beDevice = ${beDevice.device.name}")
        carDmManager.requestForegroundAa()
    }

    /**
     * 已配对列表中设备是否存在连接中的设备.
     *
     * @return Boolean
     */
    fun hasConnectingDevice(): Boolean {
        val connectingDevice = btPairedDeviceList.find {
            it.hfpState == BluetoothProfile.STATE_CONNECTING
                    || it.a2dpState == BluetoothProfile.STATE_CONNECTING
                    || it.aAConnectedState == ConnectState.CONNECTING.state
                    || it.cPConnectedState == IConstant.ConnectState.CONNECTING
        }
        LogUtil.i(TAG, "hasConnectingDevice : connectingDevice = $connectingDevice")
        if (connectingDevice != null) {
            // 当前存在连接中的设备
            return true
        }
        return false
    }

    /**
     * 已配对列表中设备是否存在连接中的设备.
     *
     * @return Boolean
     */
    fun isConnecting(bluetoothDevice:BluetoothDevice): Boolean {
        val device = btPairedDeviceList.find {
            it.device.address.equals(bluetoothDevice.address)
        }
        if(device != null){
            LogUtil.i(TAG, "isConnecting : device = $device")
            return device.hfpState == BluetoothProfile.STATE_CONNECTING
                    || device.a2dpState == BluetoothProfile.STATE_CONNECTING
                    || device.aAConnectedState == ConnectState.CONNECTING.state
                    || device.cPConnectedState == IConstant.ConnectState.CONNECTING
        }else {
            LogUtil.i(TAG, "isConnecting : device is null")
            return false
        }


    }

    /**
     * 当前是否存在Cp连接.
     *
     * @return 状态
     */
    fun hasConnectDeviceCp(): Boolean {
        val hasConnectDeviceCp = carDmManager.hasConnectDeviceCp()
        LogUtil.i(TAG, "hasConnectDeviceCp : hasConnectDeviceCp = $hasConnectDeviceCp")
        return hasConnectDeviceCp
    }

    /**
     * 当前是否存在AA连接.
     *
     * @return 状态
     */
    fun hasConnectDeviceAa(): Boolean {
        val hasConnectDeviceAa = carDmManager.hasConnectDeviceAa()
        LogUtil.i(TAG, "hasConnectDeviceAa : hasConnectDeviceAa = $hasConnectDeviceAa")
        return hasConnectDeviceAa
    }

    /**
     * 解除蓝牙配对.
     *
     * @param btDeviceBean
     */
    fun removeBond(btDeviceBean: BtDeviceBean) {
        LogUtil.d(TAG, "removeBond : ")
        // 移除蓝牙配对设备
        removeBond(btDeviceBean.device)
        // 从已连接列表中移除该设备
        deleteConnectedBtFromList(btDeviceBean)
        if (btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED) {
            // CP已连接,移除前则断开CP
            carDmManager.disconnectDeviceCp(btDeviceBean.device, false)
            handler.postDelayed({
                // 从蓝牙已配对列表中移除该设备
                devicePairedRemoveUpdateList(btDeviceBean)
            }, LASH_BACK_PERIOD)
        } else if (btDeviceBean.aAConnectedState == ConnectState.CONNECTED.state) {
            // AA已连接,移除前则先断开AA
            carDmManager.disconnectDeviceAa(btDeviceBean.device, false)
            handler.postDelayed({
                // 从蓝牙已配对列表中移除该设备
                devicePairedRemoveUpdateList(btDeviceBean)
            }, LASH_BACK_PERIOD)
        } else {
            // 从蓝牙已配对列表中移除该设备
            devicePairedRemoveUpdateList(btDeviceBean)
        }
    }

    /**
     * 获取当前连接的carPlay设备.
     *
     * @return BtDeviceBean
     */
    fun getCarPlayBean(): BtDeviceBean? {
        if (carPlayBean == null) {
            btPairedDeviceList.forEach {
                if (it.cPConnectedState == IConstant.ConnectState.CONNECTED) {
                    carPlayBean = it
                    return@forEach
                }
            }
        }
        LogUtil.i(TAG, "getCarPlayBean : carPlayBean = $carPlayBean")
        return carPlayBean
    }

    /**
     * 获取当前连接的无线AA设备.
     *
     * @return BtDeviceBean
     */
    fun getAndroidAutoBean(): BtDeviceBean? {
        if (androidAutoBean == null) {
            btPairedDeviceList.forEach {
                if (it.aAConnectedState == ConnectState.CONNECTED.state) {
                    androidAutoBean = it
                    return@forEach
                }
            }
        }
        LogUtil.i(TAG, "getAndroidAutoBean : androidAutoBean = $androidAutoBean")
        return androidAutoBean
    }

    /**
     * 开始设备配对倒计时.
     *
     * @param device 配对设备
     */
    private fun startPairedTimeTask(device: BluetoothDevice) {
        LogUtil.d(TAG, "startPairedTimeTask : ")
        // 设备配对计时30s，30s后算超时，取消配对
        pairedTimerTask = CountDownTimeManager(PAIRED_TIME) { count ->
            // 倒计时
            if (count == 0) {
                if (isPairing) {
                    // 设备配对超时，取消设备配对
                    device.setPairingConfirmation(false)
                }
            }
        }
        // 间隔1秒，开始计时
        Timer().schedule(pairedTimerTask, 0, PERIOD)
    }

    /**
     * 停止设备配对计时器.
     *
     */
    private fun stopPairedTimerTask() {
        LogUtil.d(TAG, "stopPairedTimerTask : ")
        pairedBtDevice = null
        pairedTimerTask?.cancel()
        pairedTimerTask = null
    }

    /**
     * 开始倒计时任务.
     *
     */
    private fun startTimerTask() {
        LogUtil.d(TAG, "startTimerTask : ")
        timerTask = CountDownTimeManager(CUT_DOWN_TIME) { count ->
            // 倒计时回调
            for (listener in btInfoListenerMap!!.values) {
                listener.onBtFoundCountdownChanged(count.toString())
            }
            if (count == 0) {
                // 关闭可被发现，并关闭计时
//                setBtFoundStatus(false)
            }
        }
        // 间隔1秒，开始计时
        Timer().schedule(timerTask, 0, PERIOD)
    }

    /**
     * 结束倒计时任务.
     *
     */
    private fun stopTimerTask() {
        LogUtil.d(TAG, "stopTimerTask : ")
        timerTask?.cancel()
        timerTask = null
        // 倒计时回调
        for (listener in btInfoListenerMap!!.values) {
            listener.onBtFoundCountdownChanged(FOUND_STOP)
        }
    }

    /**
     * 更新蓝牙配对列表.
     *
     * @param pairedList 列表
     */
    private fun updateBtPairedList(pairedList: CopyOnWriteArrayList<BtDeviceBean>) {
        LogUtil.d(TAG, "updateBtPairedList : size = ${pairedList.size}")
        GlobalScope.launch(Dispatchers.Main) {
            // 蓝牙列表排序
            pairedList.sort()
            // 更新蓝牙列表
            for (listener in btInfoListenerMap!!.values) {
                listener.onUpdateBtPairedList(pairedList)
            }
        }
    }

    /**
     * 初始化蓝牙配对列表.
     */
    @Synchronized
    private fun initPairedList() {
        if (headsetClient == null || a2dpSink == null || btPairedListIsInit || !getBluetoothStatus()) {
            // 更新列表并回调监听
            LogUtil.d(TAG, "initPairedList: return , btPairedListIsInit = $btPairedListIsInit, btState = ${getBluetoothStatus()} , " +
                    "headsetClient = $headsetClient , a2dpSink = $a2dpSink")
            updateBtPairedList(btPairedDeviceList)
            return
        }
        // 已配对列表已初始化
        btPairedListIsInit = true

            val devices = getBtPairedDevices()
            LogUtil.d(TAG, "initPairedList: devices = ${devices.size}")
            carPlayBean = getCarPlayBean()
            androidAutoBean = getAndroidAutoBean()
            // 清空当前蓝牙已配对列表
            btPairedDeviceList.clear()
            devices.forEach { bluetoothDevice ->
                val btDeviceBean = BtDeviceBean(bluetoothDevice)
                btDeviceBean.a2dpState = getA2dpConnectionState(bluetoothDevice)
                btDeviceBean.hfpState = getHfpConnectionState(bluetoothDevice)
                btDeviceBean.bondState = BluetoothDevice.BOND_BONDED
                btDeviceBean.isSupportWirelessCP = carDmManager.checkWirelessCp(bluetoothDevice)
                btDeviceBean.isSupportWirelessAA = carDmManager.checkWirelessAa(bluetoothDevice)
                btDeviceBean.cPConnectedState = carDmManager.getDeviceCpState(bluetoothDevice)
                btDeviceBean.aAConnectedState = carDmManager.getDeviceAaState(bluetoothDevice)
                //报错显示settingDataBase为空
                Log.d(TAG, "initPairedList: 是否为空："+ MyApplication.settingDataBase)
                btDeviceBean.pairedTime = MyApplication.settingDataBase.btDeviceDao()
                    .getPairedTime(bluetoothDevice.address) ?: 0L
                // CP设备连接状态
                if (carPlayBean != null
                    && carPlayBean?.cPConnectedState == IConstant.ConnectState.CONNECTED
                ) {
                    if (TextUtils.equals(
                            btDeviceBean.device.address, carPlayBean!!.device.address
                        )
                    ) {
                        btDeviceBean.cPConnectedState =
                            carDmManager.getDeviceCpState(carPlayBean!!.device)
                        btDeviceBean.carPlayLinkType = carPlayBean!!.carPlayLinkType
                        btDeviceBean.pairedTime = carPlayBean?.pairedTime ?: 0L
                    }
                }
                // AA设备连接状态
                if (androidAutoBean != null
                    && androidAutoBean?.aAConnectedState == ConnectState.CONNECTED.state
                ) {
                    if (TextUtils.equals(
                            btDeviceBean.device.address,
                            androidAutoBean!!.device.address
                        )
                    ) {
                        btDeviceBean.aAConnectedState =
                            carDmManager.getDeviceAaState(androidAutoBean!!.device)
                        btDeviceBean.androidAutoLinkType = androidAutoBean!!.androidAutoLinkType
                        btDeviceBean.pairedTime = androidAutoBean?.pairedTime ?: 0L
                    }
                }
                LogUtil.d(
                    TAG, "initPairedList : bondDevice = ${bluetoothDevice.name}(${bluetoothDevice.address}) " +
                            ", time = ${btDeviceBean.pairedTime}"
                )
                btPairedDeviceList.add(btDeviceBean)
            }
            if (carPlayBean != null) {
                if (!btPairedDeviceList.contains(carPlayBean)
                    && carPlayBean!!.carPlayLinkType == Contacts.CP_CONNECT_WIRED
                ) {
                    // 在配对列表中不存在当前连接的，有线carPlay对应的蓝牙设备，则添加（针对有线），无线carPlay肯定存在
                    btPairedDeviceList.add(carPlayBean)
                }
            }
            if (androidAutoBean != null) {
                if (!btPairedDeviceList.contains(androidAutoBean)) {
                    // 在配对列表中不存在当前连接的AA对应的蓝牙设备，则添加（针对有线），无线AA肯定存在
                    btPairedDeviceList.add(androidAutoBean)
                }
            }
            // 更新列表并回调监听
            updateBtPairedList(btPairedDeviceList)

    }

    /**
     * 添加设备到蓝牙扫描列表.
     *
     * @param device 扫描到的蓝牙设备
     */
    private suspend fun addScanList(device: BluetoothDevice) {
        LogUtil.i(TAG, "addScanList : device = ${device.name} , address = ${device.address}")
        // 在蓝牙配对列表中查询该对象,配对列表中不存在则继续后续操作
        if (btPairedDeviceList.find {
                TextUtils.equals(it.device.address, device.address)
            } != null) {
            return
        }
        // 在蓝牙扫描列表中查询该对象
        val scanDevice =
            btScanDevicesList.find { TextUtils.equals(it.device.address, device.address) }
        if (scanDevice != null) {
            // 该对象在蓝牙扫描列表中存在,存在,判断扫描列表中的对象名称是否为null，为null则替换
            if (scanDevice.device.name == null) {
                scanDevice.device = device
            }
        } else {
            // 该对象在蓝牙扫描列表中不存在,则构建对象后添加
            val btDeviceBean = BtDeviceBean(device)
            //TODO 解除注释
//            if (CarConfigInfoManager.instance.hasCarPlay()) {
//                // carPlay已配置,则检测是否支持无线CP
//                btDeviceBean.isSupportWirelessCP = carDmManager.checkWirelessCp(device)
//            }
            // 添加到蓝牙扫描列表
            btScanDevicesList.add(btDeviceBean)
        }
        withContext(Dispatchers.Main) {
            // 监听回调,更新蓝牙扫描列表
            for (listener in btInfoListenerMap!!.values) {
                listener.onUpdateBtScanList(btScanDevicesList)
            }
        }
    }

    /**
     * 设备配对成功，更新配对及扫描列表.
     *
     * @param device 配对完成的设备.
     */
    private fun devicePairedSuccessUpdateList(device: BluetoothDevice) {
        LogUtil.d(TAG, "devicePairedSuccessUpdateList : device = ${device.name}")
        GlobalScope.launch(Dispatchers.Default) {
            val deviceBean = btScanDevicesList.find {
                TextUtils.equals(it.device.address, device.address)
            }
            if (deviceBean != null) {
                // 如果配对的设备在扫描列表中存在，配对成功后，则移除该设备（从车机中配对其他设备情况）;
                // 如果配对设备不在扫描列表，则扫描列表不做处理（其他设备配对车机的情况）
                btScanDevicesList.remove(deviceBean)
                withContext(Dispatchers.Main) {
                    // 通知UI更新蓝牙扫描列表
                    for (listener in btInfoListenerMap!!.values) {
                        listener.onUpdateBtScanList(btScanDevicesList)
                    }
                }
            }
            // 将配对成功的设备添加到配对列表中，并开始连接
            addPairedBtToList(device)
        }
    }

    /**
     * 从配对列表删除已配对信息.
     *
     * @param btDeviceBean 配对完成的设备.
     */
    private fun devicePairedRemoveUpdateList(btDeviceBean: BtDeviceBean) {
        LogUtil.d(TAG, "devicePairedRemoveUpdateList : device = ${btDeviceBean.device.name}")
        btPairedDeviceList.remove(btDeviceBean)
        GlobalScope.launch(Dispatchers.IO) {
            // 从数据库移除对象
            MyApplication.settingDataBase.btDeviceDao().deleteBtDevice(
                BtPairedBean(btDeviceBean.device.address, btDeviceBean.pairedTime)
            )
        }
        updateBtPairedList(btPairedDeviceList)
        // 删除DM对应的设备
        carDmManager.deleteDeviceCpAa(btDeviceBean.device)
    }

    /**
     * 从配对列表移除有线设备.
     *
     * @param btDeviceBean 有线蓝牙对象
     */
    private fun removeWiredDevice(btDeviceBean: BtDeviceBean) {
        LogUtil.i(TAG, "removeWiredDevice : name = ${btDeviceBean.device.name}")
        // 从列表移除
        btPairedDeviceList.remove(btDeviceBean)
        // 更新列表
        updateBtPairedList(btPairedDeviceList)
    }

    /**
     * 添加配对设备到蓝牙配对列表.
     *
     * @param device 配对设备列表
     */
    private suspend fun addPairedBtToList(device: BluetoothDevice) {
        LogUtil.d(TAG, "addPairedBtToList: device = ${device.name}")
        val btDeviceBean = BtDeviceBean(device)
        btDeviceBean.a2dpState = BluetoothProfile.STATE_DISCONNECTED
        btDeviceBean.hfpState = BluetoothProfile.STATE_DISCONNECTED
        btDeviceBean.bondState = BluetoothDevice.BOND_BONDED
        btDeviceBean.isSupportWirelessCP = carDmManager.checkWirelessCp(device)
        btDeviceBean.isSupportWirelessAA = carDmManager.checkWirelessAa(device)
        btDeviceBean.cPConnectedState = carDmManager.getDeviceCpState(device)
        btDeviceBean.aAConnectedState = carDmManager.getDeviceAaState(device)
        btDeviceBean.pairedTime = System.currentTimeMillis()
        GlobalScope.launch(Dispatchers.IO) {
            // 设备时间添加到数据库
            MyApplication.settingDataBase.btDeviceDao().insertOrUpdateBtDevice(
                BtPairedBean(btDeviceBean.device.address, btDeviceBean.pairedTime)
            )
        }
        if (btPairedDeviceList.size >= 5) {
            // 配对列表超过5个时，先排序后，删除列表最后一个，然后再添加
            btPairedDeviceList.sort()
            val removeLast = btPairedDeviceList.removeLast()
            // 删除最后一个设备
            removeBond(removeLast)
        }
        val deviceBean = btPairedDeviceList.find {
            TextUtils.equals(
                it.device.address,
                btDeviceBean.device.address
            )
        }
        if (deviceBean == null) {
            // 在列表中不存在，则添加到列表首位
            btPairedDeviceList.add(btDeviceBean)
        } else {
            // 在列表中存在，则更新列表
            deviceBean.device = btDeviceBean.device
            deviceBean.hfpState = btDeviceBean.hfpState
            deviceBean.preHfpState = btDeviceBean.preHfpState
            deviceBean.a2dpState = btDeviceBean.a2dpState
            deviceBean.preA2dpState = btDeviceBean.preA2dpState
            deviceBean.bondState = btDeviceBean.bondState
            deviceBean.isSupportWirelessCP = btDeviceBean.isSupportWirelessCP
            deviceBean.isSupportWirelessAA = btDeviceBean.isSupportWirelessAA
            deviceBean.cPConnectedState = btDeviceBean.cPConnectedState
            deviceBean.aAConnectedState = btDeviceBean.aAConnectedState
            deviceBean.pairedTime = btDeviceBean.pairedTime
        }
        // 更新配对列表
        updateBtPairedList(btPairedDeviceList)
        if (!carDmManager.hasConnectWiredAa(device) && !carDmManager.hasConnectWiredCp(device)) {
            // 判断是否已连接了两个设备(BT,AA,CP都算已连接设备)
            val connectedList = getConnectedDevices()
            withContext(Dispatchers.Main) {
                if (connectedList.size > 1 && !hasExistConnectedDevice(btDeviceBean)) {
                    // 连接设备达到2个，则显示连接替换弹窗
                    showConnectedSelectedDialog(
                        connectedList[0],
                        connectedList[1],
                        btDeviceBean,
                        null
                    )
                }else{
                    // 连接设备未达到2个，则直接显示连接选择弹窗
                    showCpAAFirstConnectTipsDialog(btDeviceBean)
                }
            }
        } else {
            // 有线CP或AA进行了配对,则不显示选择连接弹窗,其他情况则显示
            LogUtil.d(TAG, "addPairedBtToList : wired cp or aa paired!")
        }
    }

    /**
     * 更新蓝牙配对列表Hfp连接状态.
     *
     * @param device 当前正在连接的蓝牙设备
     * @param state 连接状态
     * @param preState 上一次连接状态.
     */
    private fun updateBtPairedHfpStatusList(
        device: BluetoothDevice,
        state: Int,
        preState: Int,
    ) {
        LogUtil.d(
            TAG, "updateBtPairedHfpStatusList : " +
                    "device = ${device.name} , " +
                    "preState = $preState , " +
                    "state = $state"
        )
        val btDeviceBean =
            btPairedDeviceList.find { TextUtils.equals(it.device.address, device.address) }
        if (btDeviceBean != null) {
            // 设备在蓝牙配对列表中找到,则开始更新hfp连接状态
            btDeviceBean.a2dpState = getA2dpConnectionState(device)
            btDeviceBean.hfpState = state
            btDeviceBean.preHfpState = preState
            btDeviceBean.isSupportWirelessCP = carDmManager.checkWirelessCp(device)
            btDeviceBean.isSupportWirelessAA = carDmManager.checkWirelessAa(device)
            btDeviceBean.cPConnectedState = carDmManager.getDeviceCpState(device)
            btDeviceBean.aAConnectedState = carDmManager.getDeviceAaState(device)
            btDeviceBean.carPlayLinkType = carDmManager.getDeviceCpLink(device)
            btDeviceBean.androidAutoLinkType = carDmManager.getDeviceAaLink(device)
            // 更新蓝牙配对列表
            updateBtPairedList(btPairedDeviceList)
            if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED) {
                LogUtil.d(TAG, "updateBtPairedHfpStatusList : isNeedConnectA2dp = $isNeedConnectA2dp")
                if (isNeedConnectA2dp) {
                    if(getA2dpConnectedDevicesList().isEmpty()){
                        LogUtil.d(TAG, "updateBtPairedHfpStatusList : connect a2dp!")
                        // 蓝牙电话连接成功，需要连接蓝牙音乐,则开始连接蓝牙音乐
                        connectDevice(btDeviceBean, Contacts.BT_CONNECT_A2DP)
                    }
                } else {
                    // 停止自动连接计时器
                    for (listener in btInfoListenerMap!!.values) {
                        listener.onStopAutoConnectTimerTask()
                    }
                    if (carDmManager.hasConnectDeviceCp(device)) {
                        // 连接成功,若当前设备CP已连接,则需断开HFP
                        disconnectDevice(btDeviceBean, Contacts.BT_CONNECT_HFP)
                    }
                }
            }
            if ((btDeviceBean.preHfpState == BluetoothProfile.STATE_CONNECTING
                        && btDeviceBean.hfpState == BluetoothProfile.STATE_DISCONNECTED)
                && btDeviceBean.a2dpState == BluetoothProfile.STATE_DISCONNECTED
            ) {
                if (getIsRemoteDisconnect() && readRemoteDisconnectBtDevice()?.address == device.address) {
                    LogUtil.d(TAG, "updateBtPairedHfpStatusList : lash back the bt device!")
                    // 当前为远距离断开,进行蓝牙轮询连接,导致的蓝牙连接失败,则不显示弹窗,再次发起连接
                    handler.postDelayed(remoteRunnable, LASH_BACK_PERIOD)
                } else {
                    if (isNeedConnectA2dp) {
                        LogUtil.d(TAG, "updateBtPairedHfpStatusList : isNeedConnectA2dp is true")
                        if(getA2dpConnectedDevicesList().isEmpty()){
                            LogUtil.d(TAG, "updateBtPairedHfpStatusList : connect bt a2dp!")
                            // 蓝牙电话连接失败，需要连接蓝牙音乐,则开始蓝牙音乐的连接
                            connectDevice(btDeviceBean, Contacts.BT_CONNECT_A2DP)
                        }
                    } else {
                        LogUtil.d(TAG, "updateBtPairedHfpStatusList : connected fail!")
                        // 其他情况连接失败,则显示弹窗
                        showToast(context.getString(R.string.bt_device_connect_fail))
                        // 连接失败，显示失败提示，并断开当前的电话连接
                        disconnectDevice(btDeviceBean, Contacts.BT_CONNECT_HFP)
                    }
                }
            }
        }
    }

    /**
     * 更新蓝牙配对列表A2dp连接状态.
     *
     * @param device 当前正在连接的蓝牙设备
     * @param state 连接状态
     * @param preState 上一次连接状态.
     */
    private fun updateBtPairedA2dpStatusList(
        device: BluetoothDevice,
        state: Int,
        preState: Int,
    ) {
        LogUtil.d(
            TAG, "updateBtPairedA2dpStatusList : " +
                    "device = ${device.name} , " +
                    "preState = $preState , " +
                    "state = $state"
        )
        val btDeviceBean =
            btPairedDeviceList.find { TextUtils.equals(it.device.address, device.address) }
        if (btDeviceBean != null) {
            // 设备在蓝牙配对列表中找到,则开始更新hfp连接状态
            btDeviceBean.hfpState = getHfpConnectionState(device)
            btDeviceBean.a2dpState = state
            btDeviceBean.preA2dpState = preState
            btDeviceBean.isSupportWirelessCP = carDmManager.checkWirelessCp(device)
            btDeviceBean.isSupportWirelessAA = carDmManager.checkWirelessAa(device)
            btDeviceBean.cPConnectedState = carDmManager.getDeviceCpState(device)
            btDeviceBean.aAConnectedState = carDmManager.getDeviceAaState(device)
            btDeviceBean.carPlayLinkType = carDmManager.getDeviceCpLink(device)
            btDeviceBean.androidAutoLinkType = carDmManager.getDeviceAaLink(device)
            // 更新蓝牙配对列表
            updateBtPairedList(btPairedDeviceList)
            if (btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED) {
                // 停止自动连接计时器
                for (listener in btInfoListenerMap!!.values) {
                    listener.onStopAutoConnectTimerTask()
                }
                // 连接成功,需要连接蓝牙音乐标志位复位
                isNeedConnectA2dp = false
                if (carDmManager.hasConnectDeviceAa(device)
                    || carDmManager.hasConnectDeviceCp(device)
                ) {
                    // 连接成功,若当前设备的AA或CP已连接,则需断开A2DP
                    disconnectDevice(btDeviceBean, Contacts.BT_CONNECT_A2DP)
                }
            }
            if ((btDeviceBean.preA2dpState == BluetoothProfile.STATE_CONNECTING
                        && btDeviceBean.a2dpState == BluetoothProfile.STATE_DISCONNECTED)
                && btDeviceBean.hfpState == BluetoothProfile.STATE_DISCONNECTED
            ) {
                // 连接失败,需要连接蓝牙音乐标志位复位
                isNeedConnectA2dp = false
                if (getIsRemoteDisconnect() && readRemoteDisconnectBtDevice()?.address == device.address) {
                    LogUtil.d(TAG, "updateBtPairedA2dpStatusList : lash back the bt device!")
                    // 当前为远距离断开,进行蓝牙轮询连接,导致的蓝牙连接失败,则不显示弹窗,再次发起连接
                    handler.postDelayed(remoteRunnable, LASH_BACK_PERIOD)
                } else {
                    LogUtil.d(TAG, "updateBtPairedA2dpStatusList : connected fail!")
                    // 其他情况连接失败,则显示弹窗
                     showToast(context.getString(R.string.bt_device_connect_fail))
                    // 连接失败，显示失败提示，并断开当前的音乐连接
                    disconnectDevice(btDeviceBean, Contacts.BT_CONNECT_A2DP)
                }
            }
        }
    }

    fun getConnectTime(): String {
        return connectTime
    }

    fun showConnectedSelectedDialog(
        deviceOne: BtDeviceBean,
        deviceTwo: BtDeviceBean,
        operationReplace:((BtDeviceBean)->Unit),
        operationCancel:(()->Unit)
    ) {
        GlobalScope.launch(Dispatchers.Main){
            if(btConnectSelectedDialog?.isShowing == true){
                LogUtil.i(TAG, "showConnectedSelectedDialog2 isShowing")
                btConnectSelectedDialog?.dismiss()
                btConnectSelectedDialog = null
            }
            LogUtil.i(TAG, "showConnectedSelectedDialog2")
            // 构建连接选择替换弹窗对象
            btConnectSelectedDialog = BtConnectSelectedDialog(context,deviceOne , deviceTwo)
            // 事件订阅
            btConnectSelectedDialog?.setDialogClickCallback(object :
                BtConnectSelectedDialog.OnDeviceDialogClickCallback {
                override fun onBtDeviceOne(device: BtDeviceBean) {
                    LogUtil.i(TAG, "showConnectedSelectedDialog2 onBtDeviceOne : disconnect = ${device.device.name}(${device.device.address})")
                    operationReplace.invoke(device)
                }

                override fun onBtDeviceTwo(device: BtDeviceBean) {
                    LogUtil.i(TAG, "showConnectedSelectedDialog2 onBtDeviceTwo : disconnect = ${device.device.name}(${device.device.address})")
                    operationReplace.invoke(device)
                }
            })
            btConnectSelectedDialog?.setOnCancelListener {
                LogUtil.i(TAG, "showConnectedSelectedDialog2 cancel")
                operationCancel.invoke()
            }
            // 显示弹窗
            btConnectSelectedDialog?.show()
        }
    }

    /**
     * 显示连接选择替换弹窗.
     *
     * @param deviceOne 已连接设备1
     * @param deviceTwo 已连接设备2
     * @param connectDevice 待连接的设备
     * @param connectType 连接类型
     */
    private fun showConnectedSelectedDialog(
        deviceOne: BtDeviceBean,
        deviceTwo: BtDeviceBean,
        connectDevice: BtDeviceBean,
        connectType: Int?
    ) {
        if(btConnectSelectedDialog?.isShowing == true){
            LogUtil.i(TAG, "showConnectedSelectedDialog isShowing")
            btConnectSelectedDialog?.dismiss()
            btConnectSelectedDialog = null
        }
        LogUtil.i(TAG, "showConnectedSelectedDialog")
        // 构建连接选择替换弹窗对象
        btConnectSelectedDialog = BtConnectSelectedDialog(context, deviceOne, deviceTwo)
        // 事件订阅
        btConnectSelectedDialog?.setDialogClickCallback(object :
            BtConnectSelectedDialog.OnDeviceDialogClickCallback {
            override fun onBtDeviceOne(device: BtDeviceBean) {
                LogUtil.i(TAG, "onBtDeviceOne : disconnect = ${device.device.name}(${device.device.address})")
                // 断开设备1的连接
                disconnectedDevice(device)
                if (connectType == null) {
                    showCpAAFirstConnectTipsDialog(connectDevice)
                } else {
                    if(connectType == Contacts.BT_CONNECT_A2DP){
                        // 新设备需要连接蓝牙音乐，则需判断当前是否存在蓝牙音乐的连接,存在,则断开之前的,连接新的设备
                        val state = getA2dpConnectionState(connectDevice.device)
                        if(state ==  BluetoothProfile.STATE_CONNECTED || state == BluetoothProfile.STATE_CONNECTING){
                            //如果这台设备已经连上了a2dp，无需再断开
                        }else{
                            disconnectAllDeviceA2dp()
                        }
                    }
                    // 开始连接新设备
                    handler.postDelayed(
                        { connectDevice(connectDevice, connectType) },
                        LASH_BACK_PERIOD
                    )
                }
            }

            override fun onBtDeviceTwo(device: BtDeviceBean) {
                LogUtil.i(TAG, "onBtDeviceTwo : disconnect = ${device.device.name}(${device.device.address})")
                // 断开设备2的连接
                disconnectedDevice(device)
                if (connectType == null) {
                    showCpAAFirstConnectTipsDialog(connectDevice)
                } else {
                    if(connectType == Contacts.BT_CONNECT_A2DP){
                        // 新设备需要连接蓝牙音乐，则需判断当前是否存在蓝牙音乐的连接,存在,则断开之前的,连接新的设备
                        val state = getA2dpConnectionState(connectDevice.device)
                        if(state ==  BluetoothProfile.STATE_CONNECTED || state == BluetoothProfile.STATE_CONNECTING){
                            //如果这台设备已经连上了a2dp，无需再断开
                        }else{
                            disconnectAllDeviceA2dp()
                        }
                    }
                    // 开始连接新设备
                    handler.postDelayed(
                        { connectDevice(connectDevice, connectType) },
                        LASH_BACK_PERIOD
                    )
                }
            }
        })
        // 显示弹窗
        btConnectSelectedDialog?.show()
    }

    /**
     * 显示连接类型弹窗.
     *
     * @param device 待连接的设备
     */
    private fun showConnectTypeDialog(device: BtDeviceBean) {
        LogUtil.d(TAG, "showConnectTypeDialog : device = $device")
        if (btConnectTypeDialog != null) {
            btConnectTypeDialog?.dismiss()
            btConnectTypeDialog = null
        }
        // 构建蓝牙连接类型弹窗
        btConnectTypeDialog = BtConnectTypeDialog(context, device)
        // 设备按钮点击监听
        btConnectTypeDialog?.setDialogClickCallback(object :
            BtConnectTypeDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick(deviceBean: BtDeviceBean, connectType: Int) {
                LogUtil.i(
                    TAG, "onConfirmClick : " +
                            "connect bt = ${deviceBean.device.name} ," +
                            " connectType = $connectType"
                )
                // 通过对应连接类型,连接对应蓝牙
                connectDeviceArbitration(deviceBean, connectType)
            }

            override fun onCancelClick() {
                LogUtil.d(TAG, "onCancelClick : ")
            }
        })
        // 显示弹窗
        //btConnectTypeDialog?.show()
        //T13J-1917 去除该弹框直接连接蓝牙
        // 开始连接新设备

        //T13J-3420 新设备需要连接蓝牙音乐，则需判断当前是否存在蓝牙音乐的连接,存在,则断开之前的,连接新的设备
        val state = getA2dpConnectionState(device.device)
        if(state ==  BluetoothProfile.STATE_CONNECTED || state == BluetoothProfile.STATE_CONNECTING){
            //如果这台设备已经连上了a2dp，无需再断开
        }else{
            disconnectAllDeviceA2dp()
        }
        handler.postDelayed(
            { connectDevice(device,  Contacts.BT_CONNECT_ALL) },
            LASH_BACK_PERIOD
        )
    }

    /**
     * 刷新连接类型弹窗(用于AA和CP检测到支持时进行刷新).
     *
     * @param address AA或CP设备对应的mac地址
     * @param refreshType 刷新类型,是AA还是CP
     */
    fun refreshConnectTypeDialog(address: String, refreshType: Int) {
        LogUtil.d(TAG, "refreshConnectTypeDialog : address = $address")
        // 从已配对列表中查询对应蓝牙设备
        val deviceBean =
            btPairedDeviceList.find { TextUtils.equals(it.device.address, address) }
        if (deviceBean != null) {
            if (refreshType == Contacts.BT_CONNECT_CP) {
                deviceBean.isSupportWirelessCP = true
            } else if (refreshType == Contacts.BT_CONNECT_AA) {
                deviceBean.isSupportWirelessAA = true
            }
            // 更新列表
            updateBtPairedList(btPairedDeviceList)
            if (btConnectTypeDialog != null && btConnectTypeDialog!!.isShowing) {
                // 刷新连接类型弹窗
                btConnectTypeDialog?.refreshLayout(deviceBean)
            } else {
                // 当前连接类型弹窗未显示,则不进行刷新
                LogUtil.d(TAG, "refreshConnectTypeDialog : this dialog is not show!")
            }
        } else {
            // 当前设备未找到,则不进行刷新
            LogUtil.d(TAG, "refreshConnectTypeDialog : this device is not exit!")
        }
    }

    /**
     * 显示蓝牙配对弹窗.
     *
     * @param device 待配对的蓝牙设备.
     * @param pairedCode 蓝牙配对码
     */
    private fun showBtPairedCodeDialog(device: BluetoothDevice, pairedCode: String) {
        LogUtil.i(
            TAG, "showBtPairedCodeDialog : " +
                    "device = ${device.name} , " +
                    "pairedCode = $pairedCode"
        )

        // 如果远距离断开回连时，进行配对过程（说明手机上删除了配对记录），此时停止远距离断开逻辑，避免一直在发生配对
        // 将远距离断开的蓝牙置空
        saveDistanceBtDevice(Contacts.BT_DEVICE_DEFAULT)
        // 设置远距离断开设备不存在
        setIsRemoteDisconnect(false)

        //T13J-897 临时打断息屏模式
        SettingDialogManager.interruptStandbyMode(context)
        // 构建蓝牙配对码弹窗对象
        btPairedCodeDialog = BtPairedCodeDialog(MyApplication.getContext())
        // 设置蓝牙配对码
        btPairedCodeDialog?.setBtPairedCode(pairedCode)
        // 监听确定按钮回调
        btPairedCodeDialog?.setDialogClickCallback(object :
            BtPairedCodeDialog.OnConfirmDialogClickCallback {
            override fun onDmsConfirmClick() {
                LogUtil.d(TAG, "showBtPairedCodeDialog  onDmsConfirmClick:")
                // 设置允许配对 T13J-1912 无需调用setPin
                //device.setPin(pairedCode.toByteArray())

                //获取常用设备选项
                val btDevice = BtDeviceBean(device)
                if(btPairedCodeDialog!!.isPreferencesCheckboxChecked()){
                    for (beDevice in getBtPairedList()) {
                        beDevice.isPreferencesDevice = false
                    }
                    btDevice.isPreferencesDevice = true
                }
                device.setPairingConfirmation(true)
                SettingDialogManager.recoverStandbyMode(context)
            }
            override fun onDmsCancelClick() {
                LogUtil.d(TAG, "showBtPairedCodeDialog  onDmsCancelClick:")
                isCancelPairingByUser = true
                device.cancelBondProcess()
                SettingDialogManager.recoverStandbyMode(context)
            }
        })

        // 显示蓝牙配对弹窗
        btPairedCodeDialog?.show()

        //用户主动点击的配对，直接允许配对
        if(isUserCreateBond){
            //device.setPairingConfirmation(true)
        }
    }

    /**
     * 显示cpaa 首次配对连接的弹框.
     *
     * @param btDevice 配对失败设备
     */
    private fun showCpAAFirstConnectTipsDialog(connectDevice: BtDeviceBean,isAaDevice: Boolean = false){
       GlobalScope.launch(Dispatchers.Main){
           var isCpDevice = CarDmManager.instance.checkWirelessCp(connectDevice.device)
//           val bundle = Bundle()
//           bundle.putInt(VDKey.TYPE, VDValuePhoneLink.ServerId.CARPLAY)
//           bundle.putString(VDKey.DATA, connectDevice.device.address)
//           val vdEvent = VDEvent(VDEventPhoneLink.SUPPORT_WIRELESS)
//           vdEvent.payload = bundle
//           val event = VDBus.getDefault().getOnce(vdEvent) // 失败了会返回null
//           if (event != null) {
//               val payload = event.payload
//               val isSupportWireless = payload.getBoolean(VDKey.ENABLE)
//               Log.d(TAG, "showPhoneLinkIcon"+connectDevice.device.name + " 判断显示支持carplay互联:"+isSupportWireless)
//               if (isSupportWireless){
//                   isCpDevice = true
//               }
//           }
           LogUtil.i(TAG, "zhc6whu showCpAAFirstConnectTipsDialog : connectDevice = ${connectDevice.device.name} , isCpDevice = $isCpDevice , isAaDevice = $isAaDevice")
           if(cpAaFirstConnectTipsDialog?.isShowing == true){
               cpAaFirstConnectTipsDialog?.dismiss()
           }
           if(isCpDevice){
               cpAaFirstConnectTipsDialog = ConfirmDialog(context)
               cpAaFirstConnectTipsDialog?.setTips(
                   context.getString(R.string.dm_cp_first_connected_tips, connectDevice.device.name)
               )
               cpAaFirstConnectTipsDialog!!.setConfirmBtnText(context.getString(R.string.dm_cp_connected_success_tips))
               cpAaFirstConnectTipsDialog!!.setCancelBtnText(context.getString(R.string.str_blue))
               cpAaFirstConnectTipsDialog?.setDialogClickCallback(object :
                   ConfirmDialog.OnConfirmDialogClickCallback {
                   override fun onConfirmClick() {
                       LogUtil.i(TAG, "zhc6whu showCpAAFirstConnectTipsDialog : onConfirmClick")
                       val vdLinkDevice = VDLinkDevice()
                       vdLinkDevice.btAddress = connectDevice.device.address
                       vdLinkDevice.type = VDValuePhoneLink.DeviceType.CARPLAY
                       vdLinkDevice.isWireless = true
                       val payload = Bundle()
                       payload.putInt(
                           VDKey.TYPE,
                           VDValuePhoneLink.ServerId.CARPLAY
                       ) //互联服务ID,VDValuePhoneLink.ServerId.COMMON
                       payload.putParcelable(VDKey.DATA, vdLinkDevice) //(VDLinkDevice)device,要连接的设备
                       val event = VDEvent(VDEventPhoneLink.CONNECT_DEVICE, payload)
                       Log.d(TAG, "onBtPhoneLink: " + event)
                       VDBus.getDefault().set(event)
                       cpAaFirstConnectTipsDialog!!.dismiss()
                   }
                   override fun onCancelClick() {
                       LogUtil.i(TAG, "zhc6whu showCpAAFirstConnectTipsDialog : onCancelClick")
                       cpAaFirstConnectTipsDialog!!.dismiss()
                   } })

               cpAaFirstConnectTipsDialog?.show()
           }else if(!connectDevice.isSupportWirelessAA){
               LogUtil.i(TAG, "zhc6whu showCpAAFirstConnectTipsDialog : showConnectTypeDialog")
               showConnectTypeDialog(connectDevice)
           }
       }
    }

    /**
     * 显示配对失败弹窗.
     *
     * @param btDevice 配对失败设备
     */
    private fun showPairedFailedDialog(btDevice: BluetoothDevice) {
        LogUtil.i(TAG, "showPairedFailedDialog : btDevice = ${btDevice.name}")
        // 显示配对失败弹窗
        val conTipsDialog = ComTipsDialog(context, Contacts.BT_PAIRED_FAIL_DIALOG)
        conTipsDialog.setDialogClickCallback(object :
            ComTipsDialog.OnConfirmDialogClickCallback {
            override fun onDmsConfirmClick() {
                LogUtil.d(TAG, "onDmsConfirmClick : ")
            }
        })
        conTipsDialog.setTips(
            context.resources
                .getString(R.string.bt_paired_fail_tips, btDevice.name)
        )
        conTipsDialog.show()
    }

    /**
     * 显示提示Toast.
     *
     * @param message 提示信息
     */
    private fun showToast(message: String) {
        LogUtil.d(TAG, "showToast : message = $message")
        GlobalScope.launch(Dispatchers.Main) {
            SettingsToast.showToast(message)
        }
    }

    /*--------------------------------蓝牙广播事件相关状态监听回调-------------------------------------*/

    /**
     * 蓝牙名称发生改变时监听.
     *
     * @param btName 修改之后的蓝牙名称.
     */
    override fun onBtNameChanged(btName: String) {
        LogUtil.i(TAG, "onBtNameChanged : btName = $btName")
        // 监听回调
        for (listener in btInfoListenerMap!!.values) {
            listener.onBtNameChanged(btName)
        }
    }

    /**
     * 蓝牙状态监听.
     *
     * @param state
     */
    override fun onBluetoothStateChanged(state: Int) {
        LogUtil.i(TAG, "onBluetoothStateChanged : state = $state")
        // 监听回调
        for (listener in btInfoListenerMap!!.values) {
            listener.onBluetoothStateChanged(state)
        }
        if (state == BluetoothAdapter.STATE_ON) {
            // 蓝牙开启，获取蓝牙配对列表
            btPairedListIsInit = false
            getBtPairedList()
            GlobalScope.launch (Dispatchers.IO){
                delay(500)
                //T13JSUPPLY-170蓝牙开启开始重连
                startBtReconnection()
            }
        }else if(state == BluetoothAdapter.STATE_OFF){
            //T13J-1533 蓝牙关闭后，没有a2dp和hfp断开广播发出，此处主动将状态置成DISCONNECTED
            btPairedDeviceList.forEach {
                it.a2dpState = BluetoothProfile.STATE_DISCONNECTED
                it.hfpState = BluetoothProfile.STATE_DISCONNECTED
            }
            updateBtPairedList(btPairedDeviceList)
            //T13J-1776 蓝牙关闭清空扫描列表
            clearBtScanList()
            //T13J-3358 蓝牙关闭，将正在配对标志位清空
            isPairing = false
        }
    }

    /**
     * 可被发现监听.
     *
     * @param foundState
     */
    override fun onBluetoothFoundStateChanged(foundState: Int) {
        LogUtil.i(TAG, "onBluetoothFoundStateChanged : foundState = $foundState")
        // 监听回调
        for (listener in btInfoListenerMap!!.values) {
            listener.onBluetoothFoundStateChanged(foundState)
        }
        when (foundState) {
            BluetoothAdapter.SCAN_MODE_CONNECTABLE_DISCOVERABLE -> {
                // 开启蓝牙可被发现倒计时
                startTimerTask()
            }

            BluetoothAdapter.SCAN_MODE_CONNECTABLE -> {
                // 关闭蓝牙可被发现倒计时
                stopTimerTask()
            }

            BluetoothAdapter.SCAN_MODE_NONE -> {
                // 关闭蓝牙可被发现倒计时
                stopTimerTask()
            }
        }
    }

    /**
     * 发现蓝牙可用设备时回调.
     *
     * @param device 设备对象
     * @param deviceRssi 蓝牙设备信号强度
     */
    override fun onBtScanDevice(device: BluetoothDevice, deviceRssi: Short) {
        LogUtil.d(
            TAG, "onBtScanDevice : " +
                    "device name = ${device.name} , " +
                    "address = ${device.address} , " +
                    "deviceRssi = $deviceRssi"
        )
        val RSSI_THRESHOLD = -68 // 信号强度阈值，可根据需求调整
        // 检查信号强度是否达到阈值
        if (deviceRssi < RSSI_THRESHOLD) {
            LogUtil.d(TAG, "Ignore weak signal device: ${device.name}, RSSI=$deviceRssi")
            return // 跳过信号弱的设备
        }
        GlobalScope.launch(Dispatchers.Default) {
            btScopeMutex.withLock {
                // 添加设备
                addScanList(device)
            }
        }
    }

    /**
     * 蓝牙开始扫描时回调.
     *
     */
    override fun onStartScan() {
        LogUtil.d(TAG, "onStartScan : ")
        // 开始扫描监听回调
        for (listener in btInfoListenerMap!!.values) {
            listener.onStartScan()
        }
        Contacts.isStopScan = false
    }

    /**
     * 蓝牙停止扫描时回调.
     *
     */
    override fun onStopScan() {
        LogUtil.d(TAG, "onStopScan : ")
        // 监听回调
        for (listener in btInfoListenerMap!!.values) {
            listener.onStopScan()
        }
    }

    /**
     * 蓝牙配对时回调.
     *
     * @param pairedState
     * @param device
     */
    override fun onPairedState(pairedState: Int, device: BluetoothDevice) {
        LogUtil.d(TAG, "onPairedState : device = ${device.name} , pairedState = $pairedState")
        // 更新蓝牙配对状态
        val pairedDevice =
            btScanDevicesList.find { TextUtils.equals(it.device.address, device.address) }
        if (pairedDevice != null) {
            pairedDevice.bondState = pairedState
            // 通知UI更新列表
            for (listener in btInfoListenerMap!!.values) {
                listener.onUpdateBtScanList(btScanDevicesList)
            }
        }
        // 通知UI更新列表
        for (listener in btInfoListenerMap!!.values) {
            listener.onPairedStateChange(pairedState,device)
        }
        when (pairedState) {
            BluetoothDevice.BOND_BONDING -> {
                // 非有线AA及非有线CP进行配对,则无需启动计时
                if (!carDmManager.hasConnectWiredAa(device)
                    && !carDmManager.hasConnectWiredCp(device)
                ) {
                    // 正在配对中
                    isPairing = true
                    // 保存当前配对的蓝牙设备
                    pairedBtDevice = device
                    startPairedTimeTask(device)
                }
                pairedBluetoothDevice = null
            }

            BluetoothDevice.BOND_BONDED -> {
                isPairing = false
                isUserCreateBond = false
                isCancelPairingByUser = false
                stopPairedTimerTask()
                // 配对成功，从可用列表移除,并刷新配对列表
                devicePairedSuccessUpdateList(device)
                //断开除了这个设备的所有连接
                // 获取所有已连接的设备
                val connectedDevices = getConnectedDevices()

                // 断开除新设备外的所有已连接设备
                connectedDevices.forEach { btDevice ->
                    if (btDevice.device.address != device.address) {
                        disconnectedDevice(btDevice)
                    }
                }

                btPairedCodeDialog?.dismiss()
                pairedBluetoothDevice = device
            }

            BluetoothDevice.BOND_NONE -> {
                if (isPairing && TextUtils.equals(pairedBtDevice?.address, device.address)) {
                    LogUtil.d(TAG, "onPairedState : paired is fail , isCancelPairingByUser = $isCancelPairingByUser")
                    stopPairedTimerTask()
                    // 配对失败或取消配对,显示弹窗
                    if(!isCancelPairingByUser){
                        showPairedFailedDialog(device)
                    }
                    isPairing = false
                }
                val noneDevice = btPairedDeviceList.find {
                    it.device.address == device.address
                }
                if(noneDevice != null){
                    btPairedDeviceList.remove(noneDevice)
                    GlobalScope.launch(Dispatchers.IO) {
                        // 从数据库移除对象
                        MyApplication.settingDataBase.btDeviceDao().deleteBtDevice(
                            BtPairedBean(noneDevice.device.address, noneDevice.pairedTime)
                        )
                    }
                    deleteConnectedBtFromList(noneDevice)
                }

                updateBtPairedList(btPairedDeviceList)
                btPairedCodeDialog?.dismiss()
                isUserCreateBond = false
                isCancelPairingByUser = false
                pairedBluetoothDevice = null
            }
        }
    }

    /**
     * 蓝牙配对校验时回调.
     *
     * @param device 校验设备
     * @param pairingKey 校验码
     */
    override fun onPairedCode(device: BluetoothDevice, pairingKey: String) {
        LogUtil.i(
            TAG, "onPairedCode : " +
                    "device = ${device.name} , " +
                    "mac = ${device.address} , " +
                    "pairingKey = $pairingKey"
        )
        // 当前非有线AA或有线CP点击开启无线进行OOB配对,则显示配对码弹窗,有线AA或有线CP情况下,则不显示配对码,进行无感配对
        if (!carDmManager.hasConnectWiredAa(device) && !carDmManager.hasConnectWiredCp(device)) {
            showBtPairedCodeDialog(device, pairingKey)
        }
    }

    /**
     * hfp 连接过程中状态回调.
     *
     * @param state 当前状态
     * @param preState 上一次状态
     * @param device 连接设备
     */
    override fun onBtHfpConnectState(state: Int, preState: Int, device: BluetoothDevice) {
        LogUtil.d(
            TAG, "onBtHfpConnectState : " +
                    "device = ${device.name}(${device.address}) , " +
                    "state = $state , " +
                    "preState = $preState , "+
                    "isBtSettingUI = $isBtSettingUI"
        )
        GlobalScope.launch(Dispatchers.Default) {
            btScopeMutex.withLock {
                // 更新蓝牙配对列表状态
                updateBtPairedHfpStatusList(device, state, preState)
                // 保存当前已连接的蓝牙信息
                saveCurrentBtDevice(device)
            }
        }
        if (state == BluetoothProfile.STATE_CONNECTED) {
            //连接成功时间，用这个格式给出YYYY/MM/DD HH:MM:SS
            connectTime =
                SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.getDefault()).format(Date())
            Log.d(TAG, "onBtHfpConnectState: ConnectTime:" + connectTime)
        }
        if(!isBtSettingUI && state == BluetoothProfile.STATE_DISCONNECTED){
            //T13J-946 hfp和a2dp都断开，且不在蓝牙设置页面，弹出toast
            if(getA2dpConnectionState(device) == BluetoothProfile.STATE_DISCONNECTED){
                showToast(context.getString(R.string.bt_device_disconnect_tips))
            }
        }
    }

    /**
     * a2dp 连接过程中状态回调.
     *
     * @param state 当前状态
     * @param preState 上一次状态
     * @param device 连接设备
     */
    override fun onBtA2dpConnectState(state: Int, preState: Int, device: BluetoothDevice) {
        LogUtil.d(
            TAG, "onBtA2dpConnectState : " +
                    "device = ${device.name}(${device.address}) , " +
                    "state = $state , " +
                    "preState = $preState , "+
                    "isBtSettingUI = $isBtSettingUI"
        )
        GlobalScope.launch(Dispatchers.Default) {
            btScopeMutex.withLock {
                // 更新蓝牙配对列表状态
                updateBtPairedA2dpStatusList(device, state, preState)
                // 保存当前已连接的蓝牙信息
                saveCurrentBtDevice(device)
            }
        }

        if(!isBtSettingUI && state == BluetoothProfile.STATE_DISCONNECTED){
            //T13J-946 hfp和a2dp都断开，且不在蓝牙设置页面，弹出toast
            if(getHfpConnectionState(device) == BluetoothProfile.STATE_DISCONNECTED){
                showToast(context.getString(R.string.bt_device_disconnect_tips))
            }
        }
    }

    /**
     * pbap和map 连接过程中状态回调.
     *
     * @param state 当前状态
     * @param preState 上一次状态
     * @param device 连接设备
     */
    override fun onBtPbapAndMapConnectState(state: Int, preState: Int, device: BluetoothDevice) {

    }

    /**
     * 当蓝牙连接设备ACL断开时回调.
     *
     * @param device 断开的设备
     * @param reason 断开的原因
     */
    override fun onBtACLDisconnected(device: BluetoothDevice, reason: Int) {
        LogUtil.i(TAG, "onBtACLDisconnected : device = ${device.name}(${device.address}) , reason = $reason")
        if (reason == Contacts.BT_ACL_DISCONNECTED_8) {
            // 距离过远，导致的蓝牙断开，距离回来后，需要重连该蓝牙，因此，先保留该蓝牙设备
            saveDistanceBtDevice(device)
            // 设置远距离断开蓝牙状态
            setIsRemoteDisconnect(true)
        }
    }

    /*-----------------------------------carPlay连接状态监听----------------------------------------*/
    /**
     * @ClassName: CpDeviceListener
     * 
     * @Date:  2024/4/11 15:13
     * @Description: Cp类的相关监听.
     **/
    inner class CpDeviceListener : CarDmManager.CpDeviceListenerNotify() {
        override fun updateDeviceList(devices: MutableList<CarplayDevice>?) {
            super.updateDeviceList(devices)
            devices?.forEach { cPDevice ->
                val btDeviceBean = btPairedDeviceList.find {
                    TextUtils.equals(
                        cPDevice.bluetoothAddress,
                        it.device.address
                    )
                }
                if (btDeviceBean != null) {
                    btDeviceBean.cPConnectedState = cPDevice.connectState
                    if (cPDevice.isWirelessLink) {
                        btDeviceBean.carPlayLinkType = Contacts.CP_CONNECT_WIRELESS
                    } else {
                        btDeviceBean.carPlayLinkType = Contacts.CP_CONNECT_WIRED
                    }
                }else{
                    if (cPDevice.connectState == IConstant.ConnectState.CONNECTED
                        && !cPDevice.isWirelessLink
                    ) {
                        // 在蓝牙配对列表中没有找到，则为有线carPlay连接，但对应的蓝牙设备未连接，因此添加到列表
                        val newDevice = BtDeviceBean(getDevice(cPDevice.bluetoothAddress))
                        LogUtil.i(TAG, "notifyStateChanged : device = ${newDevice.device}")
                        // 有线carPlay蓝牙列表属性赋值
                        if (cPDevice.isWirelessLink) {
                            newDevice.carPlayLinkType = Contacts.CP_CONNECT_WIRELESS
                        } else {
                            newDevice.carPlayLinkType = Contacts.CP_CONNECT_WIRED
                        }
                        newDevice.wiredName = newDevice.device.name ?: cPDevice.deviceName
                        newDevice.cPConnectedState = cPDevice.connectState
                        newDevice.bondState = BluetoothDevice.BOND_NONE
                        newDevice.isSupportWirelessCP = carDmManager.checkWirelessCp(newDevice.device)
                        newDevice.hfpState = getHfpConnectionState(newDevice.device)
                        newDevice.a2dpState = getA2dpConnectionState(newDevice.device)
                        //newDevice.pairedTime = System.currentTimeMillis()
                        // 添加到蓝牙配对列表
                        btPairedDeviceList.add(newDevice)
                        // 更新蓝牙配对设备列表
                        updateBtPairedList(btPairedDeviceList)
                        // 更新carPlay连接设备
                        carPlayBean = newDevice
                    }
                }
            }
            // 更新蓝牙配对设备列表
            updateBtPairedList(btPairedDeviceList)
        }
        /**
         * carPlay自动重连.
         *
         * @param isConnected
         */
        override fun notifyReconnectStatus(isConnected: Boolean) {
            LogUtil.i(TAG, "notifyReconnectStatus : isConnected = $isConnected")
        }

        /**
         * carPlay连接过程中状态回调.
         *
         * @param device carPlay设备
         */
        override fun notifyStateChanged(device: CarplayDevice) {
            LogUtil.d(
                TAG, "notifyStateChanged : " +
                        "device = ${device.deviceName}(${device.bluetoothAddress}), " +
                        "state = ${device.connectState} , " +
                        "wirelessLink = ${device.isWirelessLink}"
            )
            // 保存当前carPlay连接的设备
            saveCarPlayDevice(device)
            // 从配对列表中寻找到carPlay的蓝牙设备
            val btDeviceBean = btPairedDeviceList.find {
                TextUtils.equals(
                    it.device.address,
                    device.bluetoothAddress
                )
            }
            if (btDeviceBean != null) {
                // 更新carPlay状态
                btDeviceBean.cPConnectedState = device.connectState
                btDeviceBean.hfpState = getHfpConnectionState(btDeviceBean.device)
                btDeviceBean.a2dpState = getA2dpConnectionState(btDeviceBean.device)
                if (device.isWirelessLink) {
                    btDeviceBean.carPlayLinkType = Contacts.CP_CONNECT_WIRELESS
                } else {
                    btDeviceBean.carPlayLinkType = Contacts.CP_CONNECT_WIRED
                }
                btDeviceBean.wiredName = device.deviceName
                btDeviceBean.isSupportWirelessCP = carDmManager.checkWirelessCp(btDeviceBean.device)
                // 更新蓝牙配对设备列表
                updateBtPairedList(btPairedDeviceList)
                if (device.connectState == IConstant.ConnectState.CONNECTED) {
                    // 更新carPlay连接设备
                    carPlayBean = btDeviceBean
                }
                if (device.connectState == IConstant.ConnectState.IDLE) {
                    // carPlay断开连接，当前连接置为null
                    carPlayBean = null
                    // 在列表中寻找到有线CarPlay对应的设备，若有线CarPlay断开，且之前对应的蓝牙设备在配对列表中不存在，则移除
                    val deviceBean = btPairedDeviceList.find {
                        TextUtils.equals(
                            it.device.address,
                            device.bluetoothAddress
                        )
                    }
                    if (deviceBean != null) {
                        if (deviceBean.carPlayLinkType == Contacts.CP_CONNECT_WIRED
                            && deviceBean.bondState == BluetoothDevice.BOND_NONE
                        ) {
                            // 有线CP，且蓝牙未绑定，则移除有线carPlay设备
                            removeWiredDevice(deviceBean)
                        }
                    }
                }
            } else {
                if (device.connectState == IConstant.ConnectState.CONNECTED
                    && !device.isWirelessLink
                ) {
                    // 在蓝牙配对列表中没有找到，则为有线carPlay连接，但对应的蓝牙设备未连接，因此添加到列表
                    val newDevice = BtDeviceBean(getDevice(device.bluetoothAddress))
                    LogUtil.i(TAG, "notifyStateChanged : device = ${newDevice.device}")
                    // 有线carPlay蓝牙列表属性赋值
                    if (device.isWirelessLink) {
                        newDevice.carPlayLinkType = Contacts.CP_CONNECT_WIRELESS
                    } else {
                        newDevice.carPlayLinkType = Contacts.CP_CONNECT_WIRED
                    }
                    newDevice.wiredName = newDevice.device.name ?: device.deviceName
                    newDevice.cPConnectedState = device.connectState
                    newDevice.bondState = BluetoothDevice.BOND_NONE
                    newDevice.isSupportWirelessCP = carDmManager.checkWirelessCp(newDevice.device)
                    newDevice.hfpState = getHfpConnectionState(newDevice.device)
                    newDevice.a2dpState = getA2dpConnectionState(newDevice.device)
                    //newDevice.pairedTime = System.currentTimeMillis()
                    // 添加到蓝牙配对列表
                    btPairedDeviceList.add(newDevice)
                    // 更新蓝牙配对设备列表
                    updateBtPairedList(btPairedDeviceList)
                    // 更新carPlay连接设备
                    carPlayBean = newDevice
                }
            }
        }
    }
    /*-----------------------------------carPlay连接状态监听----------------------------------------*/

    /*---------------------------------androidAuto连接状态监听--------------------------------------*/
    /**
     * @ClassName: AaSettingBinderListener
     * 
     * @Date:  2024/4/22 10:32
     * @Description: AA连接状态实现类.
     **/
    inner class AaSettingBinderListener : CarDmManager.SettingBinderListener() {
        /**
         * 列表更新时回调.
         *
         * @param devices AA设备
         */
        override fun updateDeviceList(devices: MutableList<AAutoDevice>) {
            LogUtil.d(TAG, "updateDeviceList aa: size = ${devices.size}")
            GlobalScope.launch(Dispatchers.Default) {
                // 保存当前连接的AA设备
                saveAADevice(devices)
                devices.forEach { aAutoDevice ->
                    LogUtil.i(
                        TAG, "updateDeviceList aa: " +
                                "device = ${aAutoDevice.btDeviceName} , " +
                                "state = ${aAutoDevice.connectState} , " +
                                "mac = ${aAutoDevice.btMacAddress} , " +
                                "linkType = ${aAutoDevice.connectType}"
                    )
                    val btDeviceBean = btPairedDeviceList.find {
                        TextUtils.equals(
                            aAutoDevice.btMacAddress,
                            it.device.address
                        )
                    }
                    if (btDeviceBean != null) {
                        LogUtil.d(TAG, "updateDeviceList aa: btDeviceBean != null")
                        // 更新是否支持AA
                        btDeviceBean.isSupportWirelessAA = true
                        // 更新AA状态
                        btDeviceBean.aAConnectedState = aAutoDevice.connectState.state
                        if (aAutoDevice.connectType.type == ConnectType.CONNECT_WIRELESS.type) {
                            btDeviceBean.androidAutoLinkType = ConnectType.CONNECT_WIRELESS.type
                        }
                        if (aAutoDevice.connectType.type == ConnectType.CONNECT_WIRED.type) {
                            btDeviceBean.androidAutoLinkType = ConnectType.CONNECT_WIRED.type
                        }
                        btDeviceBean.hfpState =
                            getHfpConnectionState(btDeviceBean.device)
                        btDeviceBean.a2dpState =
                            getA2dpConnectionState(btDeviceBean.device)
                        btDeviceBean.wiredName = aAutoDevice.btDeviceName

                        //如果是刚配对的aa设备，需要弹出aa首弹框
                        if(aAutoDevice.btMacAddress.equals(pairedBluetoothDevice?.address)){
                            //有线不弹aa首弹框
                            if(aAutoDevice.connectType.type == ConnectType.CONNECT_WIRED.type){
                                pairedBluetoothDevice = null
                                //正在连或已经连上aa，不弹aa首弹框
                            }else if(aAutoDevice.connectState.state == ConnectState.CONNECTED.state || aAutoDevice.connectState.state == ConnectState.CONNECTING.state){
                                pairedBluetoothDevice = null
                            }else{
                                showCpAAFirstConnectTipsDialog(btDeviceBean,true)
                            }
                        }

                        if (aAutoDevice.connectState.state == ConnectState.CONNECTED.state) {
                            // 更新AA连接设备
                            androidAutoBean = btDeviceBean
                        }
                        if (aAutoDevice.connectState.state == ConnectState.DISCONNECTED.state) {
                            // 有线AA断开时
                            androidAutoBean = null
                            if (btDeviceBean.androidAutoLinkType == ConnectType.CONNECT_WIRED.type
                                && btDeviceBean.bondState == BluetoothDevice.BOND_NONE
                            ) {
                                // 有线AA断开，且对应蓝牙设备未配对时，则从列表移除
                                removeWiredDevice(btDeviceBean)
                            }
                        }
                    } else {
                        if (aAutoDevice.connectState.state == ConnectState.CONNECTED.state
                            && aAutoDevice.connectType == ConnectType.CONNECT_WIRED
                            && !TextUtils.equals(aAutoDevice.btMacAddress, AAutoDevice.BT_ADDR_NULL)
                        ) {
                            // 在蓝牙配对列表中没有找到，则为有线AA连接，但对应的蓝牙设备未连接，因此添加到列表
                            val newDevice = BtDeviceBean(getDevice(aAutoDevice.btMacAddress))
                            LogUtil.i(TAG, "updateDeviceList : device = ${newDevice.device}")
                            // 有线AA蓝牙列表属性赋值
                            if (aAutoDevice.connectType.type
                                == ConnectType.CONNECT_WIRELESS.type
                            ) {
                                newDevice.androidAutoLinkType =
                                    ConnectType.CONNECT_WIRELESS.type
                            }
                            if (aAutoDevice.connectType.type
                                == ConnectType.CONNECT_WIRED.type
                            ) {
                                newDevice.androidAutoLinkType = ConnectType.CONNECT_WIRED.type
                                newDevice.bondState = BluetoothDevice.BOND_NONE
                            }
                            newDevice.aAConnectedState = aAutoDevice.connectState.state
                            newDevice.wiredName =
                                newDevice.device.name ?: aAutoDevice.btDeviceName
                            newDevice.isSupportWirelessAA = true
                            newDevice.hfpState = getHfpConnectionState(newDevice.device)
                            newDevice.a2dpState = getA2dpConnectionState(newDevice.device)
                            newDevice.wiredName = aAutoDevice.btDeviceName
                            //newDevice.pairedTime = System.currentTimeMillis()
                            // 添加到蓝牙配对列表
                            btPairedDeviceList.add(newDevice)
                            // 更新蓝牙配对设备列表
                            //updateBtPairedList(btPairedDeviceList)
                            // 更新AA连接设备
                            androidAutoBean = newDevice
                        }
                    }
                }
                // 更新蓝牙配对设备列表
                updateBtPairedList(btPairedDeviceList)
            }
        }
    }
    /*------------------------------------蓝牙管理相关状态监听----------------------------------------*/

    /**
     * 注册蓝牙信息监听.
     *
     * @param strClassName 类名
     * @param btInfoListener 蓝牙信息监听
     */
    fun registerBtInfoListener(strClassName: String, btInfoListener: BtInfoListenerCallback) {
        LogUtil.d(TAG, "registerBtInfoListener : strClassName = $strClassName , btInfoListener = $btInfoListener")
        // 添加监听
        btInfoListenerMap?.set(strClassName, btInfoListener)
    }

    /**
     * 注销蓝牙信息监听.
     *
     * @param strClassName 类名
     */
    fun unregisterBtInfoListener(strClassName: String?) {
        LogUtil.d(TAG, "unregisterBtInfoListener : strClassName = $strClassName")
        // 移除监听
        btInfoListenerMap?.remove(strClassName)
    }

    /**
     * @ClassName: BtInfoListenerCallback
     * 
     * @Date:  2024/6/8 10:25
     * @Description: 蓝牙信息.
     **/
    interface BtInfoListenerCallback {
        /**
         * 蓝牙名称发生改变时回调.
         *
         * @param btName
         */
        fun onBtNameChanged(btName: String)

        /**
         * 蓝牙状态发生改变时回调.
         *
         * @param state 蓝牙状态
         */
        fun onBluetoothStateChanged(state: Int)

        /**
         * 蓝牙可被发现状态改变时回调.
         *
         * @param foundState 蓝牙可被发现状态
         */
        fun onBluetoothFoundStateChanged(foundState: Int)

        /**
         * 蓝牙可被发现倒计时.
         *
         * @param countdown 倒计时
         */
        fun onBtFoundCountdownChanged(countdown: String)

        /**
         * 开始扫描时回调.
         *
         */
        fun onStartScan()

        /**
         * 停止扫描.
         *
         */
        fun onStopScan()

        /**
         * 更新蓝牙扫描列表.
         *
         * @param btScanDevicesList 蓝牙扫描列表
         */
        fun onUpdateBtScanList(btScanDevicesList: CopyOnWriteArrayList<BtDeviceBean>)

        /**
         * 更新蓝牙配对列表.
         *
         * @param btPairedDeviceList 蓝牙配对列表
         */
        fun onUpdateBtPairedList(btPairedDeviceList: CopyOnWriteArrayList<BtDeviceBean>)

        /**
         * 停止自动连接倒计时.
         *
         */
        fun onStopAutoConnectTimerTask()

        fun onPairedStateChange(pairedState: Int, device: BluetoothDevice)
    }

    companion object {
        // 日志标志位
        private const val TAG = "CarBtManager"

        // 计时器间隔
        private const val PERIOD = 1000L

        // 远距离蓝牙回连间隔
        private const val LASH_BACK_PERIOD = 500L

        // 蓝牙可被发现倒计时
        private const val CUT_DOWN_TIME = 180

        // 设备配对倒计时
        private const val PAIRED_TIME = 30

        // 可被发现关闭标志位
        const val FOUND_STOP = "STOP"

        // 蓝牙相关信息监听
        private var btInfoListenerMap: MutableMap<String, BtInfoListenerCallback>? = HashMap()

        // 单例对象
        val instance: CarBtManager by lazy(LazyThreadSafetyMode.PUBLICATION) {
            CarBtManager()
        }
    }
}
