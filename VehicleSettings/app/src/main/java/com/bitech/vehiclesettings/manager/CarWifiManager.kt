package com.bitech.vehiclesettings.manager

import android.annotation.SuppressLint
import android.car.CarInfoManager
import android.car.hardware.CarPropertyValue
import android.car.hardware.property.CarPropertyManager
import android.content.Context
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.wifi.*
import android.net.wifi.WifiConfiguration.KeyMgmt
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.bean.WifiDeviceBean
import com.bitech.vehiclesettings.broadcast.SliceReceiver
import com.bitech.vehiclesettings.broadcast.WifiReceiver
import com.bitech.vehiclesettings.provider.ProviderURI
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.EToast
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.PhoneLink
import com.bitech.vehiclesettings.view.dialog.ConfirmDialog
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.VDEvent
import com.chery.ivi.vdb.event.base.VDKey
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink
import com.chery.ivi.vdb.event.id.phonelink.VDValuePhoneLink
import com.chery.ivi.vdb.event.id.phonelink.bean.VDLinkDevice
import java.util.Timer


/**
 * @ClassName: CarWifiManager
 *
 * @Date:  2024/2/4 17:28
 * @Description: 车辆WIFI管理类.
 **/
class CarWifiManager {
    //T13JSUPPLY-883 客户产线老化EOL功能:
    //1.接收到EOL_ROUTINE_FACTORY_RESET信号后进行恢复出厂设置操作并关闭wifi
    //2.从ID_CAL_DIAGNOSTIC_WIFI_NAME获取wifi名称，从ID_CAL_DIAGNOSTIC_WIFI_PASSWORD获取wifi密码，并在进入信号范围内时自动连接
    //true:处于客户产线老化EOL功能
    private var isAging = false

    // 车辆属性管理对象
    private var carPropertyManager: CarPropertyManager? = null
    private var carInfoManager: CarInfoManager? = null

    // 车辆WIFI管理对象
    private var wifiManager: WifiManager? = null

    // WIFI广播接收器对象
    private var wifiReceiver: WifiReceiver? = null

    // 车辆信息管理对象
    private var carConfigInfoManager: CarConfigInfoManager? = null

    // 网络连接管理对象
    private var connectivityManager: ConnectivityManager? = null

    // WIFI可用扫描倒计时管理对象
    private var wifiScanTimerTask: CountDownTimeManager? = null

    private var confirmDialog: ConfirmDialog? = null

    //互联状态类
    private var phoneLink = PhoneLink()
    //扫描到的设备数量
    private var scanResultCount = 0
    var isAddWifi = false
    private var is5GBand = false

    // 热点打开监听
    private val startTetheringCallback by lazy {
        object : ConnectivityManager.OnStartTetheringCallback() {
            override fun onTetheringStarted() {
                LogUtil.d(TAG, "OnStartTetheringCallback :")
                SliceReceiver.notifyChange(ProviderURI.BROADCAST)
            }

            override fun onTetheringFailed() {
                LogUtil.d(TAG, "onTetheringFailed :")
            }
        }
    }

    // 系统属性设置监听回调
    private val carPropertyCallback: CarPropertyManager.CarPropertyEventCallback by lazy {
        object : CarPropertyManager.CarPropertyEventCallback {
            override fun onChangeEvent(carValue: CarPropertyValue<*>) {
                val id = carValue.propertyId
                val value = carValue.value
                LogUtil.i(TAG, "carPropertyCallback onChangeEvent : id = $id")
                //TODO 取消注释
//                when (id) {
//                    CarInfoManager.ID_CAL_DIAGNOSTIC_WIFI_PASSWORD ->{
//                        //收到密码回调，开始打开并连接wifi
//                        if(getAgingPassword().isNullOrEmpty()){
//                            isAging = false
//                            LogUtil.i(TAG, "carPropertyCallback password is null")
//                        }else {
//                            isAging = true
//                            if(getWifiState()){
//                                startAgingWifiScan()
//                            }else{
//                                setWifiState(true)
//                            }
//                        }
//                    }
//                }

            }

            override fun onErrorEvent(p0: Int, p1: Int) {
            }
        }
    }

    /**
     * 初始化WIFI管理相关数据.
     *
     * @param context 环境上下文
     */
    fun initWifiManager(context: Context) {
        LogUtil.d(TAG, "initWifiManager : ")
        // 初始化WIFI状态广播
        wifiReceiver = WifiReceiver()
        // 注册WIFI广播
        registerWifiReceiver(context)
        // 初始化WIFI管理对象
        wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        // 初始化车辆配置信息管理对象
        carConfigInfoManager = CarConfigInfoManager.instance
        // 初始化网络连接管理对象
        connectivityManager = MyApplication.getContext()
            .getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        // 初始化车辆属性管理对象
        carPropertyManager = CarBaseManager.getCarPropertyManager()
        carInfoManager = CarBaseManager.getCarInfoManager()
//诊断
//        carPropertyManager?.registerCallback(carPropertyCallback,
//            CarInfoManager.ID_CAL_DIAGNOSTIC_WIFI_PASSWORD,
//            PageConstants.VehicleSettings.RATE
//        )


        wifiReceiver!!.registerWifiStateListener(
            this.javaClass.simpleName,
            object : WifiReceiver.WifiStateListener {
                override fun onWifiStateChange(state: Int) {
                    if (!isAging) {
                        LogUtil.d(TAG, "onWifiStateChange : isAging is false")
                        return
                    }
                    LogUtil.d(TAG, "onWifiStateChange : wifi state = $state ")
                    when (state) {
                        WifiManager.WIFI_STATE_ENABLED -> {
                            // WIFI开关打开
                            startAgingWifiScan()
                        }

                        WifiManager.WIFI_STATE_DISABLED -> {
                            // WIFI开关关闭
                            stopWifiScan(null)
                        }
                    }

                }

                override fun onWifiHotspotStateChange(state: Int) {

                }

                override fun onWifiHotspotJoin(hotspotMac: String, hostName: String) {

                }

                override fun onWifiHotspotLeave(hotspotMac: String) {

                }

                override fun onWifiScanResult() {
                    if (!isAging) {
                        LogUtil.d(TAG, "onWifiScanResult : isAging is false")
                        return
                    }
                    val wifiInfo = getConnectionInfo()
                    LogUtil.d(
                        TAG,
                        "onWifiScanResult : wifi scan result , wifiInfo ssid = ${wifiInfo?.ssid}"
                    )
                    val agingSsid = getAgingSsid()
                    if (agingSsid.isNullOrBlank()) {
                        LogUtil.d(TAG, "onWifiScanResult : agingSsid is null")
                        return
                    }
                    if (wifiInfo?.ssid?.replace("\"", "")?.equals(agingSsid) == true) {
                        stopWifiScan(null)
                        LogUtil.d(TAG, "onWifiScanResult : has connected")
                        return
                    }

                    val wifiScanResults = getWifiScanResult()
                    if (!wifiScanResults.isNullOrEmpty()) {
                        LogUtil.d(
                            TAG,
                            "onWifiScanResult : size = ${wifiScanResults.size} , wifiScanList " +
                                    "= ${wifiScanResults.toTypedArray().contentToString()}"
                        )
                        val wifiConnectedList = arrayListOf<WifiDeviceBean>()
                        val wifiConfigurationList = getWifiConfigList()
                        wifiConfigurationList?.forEach { wifiConfiguration ->
                            if (wifiConfiguration.BSSID != null) {
                                wifiConnectedList.add(
                                    wifiConfigurationToWifiDeviceBean(
                                        wifiConfiguration
                                    )
                                )
                            }
                        }
                        wifiScanResults.forEach { scanResult ->
                            //扫描到目标wifi
                            if (scanResult.SSID.equals(agingSsid)) {
                                LogUtil.d(TAG, "onWifiScanResult : start scan connect")
                                val wifiDeviceBean = analyzeScanResult(scanResult)
                                wifiDeviceBean.wifiConnectedState = NetworkInfo.State.CONNECTING
                                wifiDeviceBean.wifiPassword = getAgingPassword()
                                wifiDeviceBean.wifiConfiguration =
                                    createWifiConfiguration(wifiDeviceBean)
                                // 连接WIFI
                                connectedWifi(
                                    wifiDeviceBean,
                                    Contacts.WIFI_CONNECTED_FROM_SCAN_LIST
                                )
                                /* val wifiConfigurationBean= wifiConnectedList.find {
                                     it.wifiSSID == agingSsid
                                 }
                                 //目标wifi不在已连接wifi列表里
                                 if(wifiConfigurationBean == null){
                                     LogUtil.d(TAG, "onWifiScanResult : start scan connect")
                                     val wifiDeviceBean = analyzeScanResult(scanResult)
                                     wifiDeviceBean.wifiConnectedState = NetworkInfo.State.CONNECTING
                                     wifiDeviceBean.wifiPassword = getAgingPassword()
                                     wifiDeviceBean.wifiConfiguration =
                                         createWifiConfiguration(wifiDeviceBean)
                                     // 连接WIFI
                                     connectedWifi(
                                         wifiDeviceBean,
                                         Contacts.WIFI_CONNECTED_FROM_SCAN_LIST
                                     )
                                 }else{
                                     //目标wifi在已连接wifi列表里
                                     LogUtil.d(TAG, "onWifiScanResult : start config connect")
                                     wifiConfigurationBean.wifiConnectedState = NetworkInfo.State.CONNECTING
                                     // 连接WIFI
                                     connectedWifi(
                                         wifiConfigurationBean,
                                         Contacts.WIFI_CONNECTED_FROM_CONNECTED_LIST
                                     )
                                 }*/
                                return@forEach
                            }
                        }
                    }

                }

                override fun onNetworkStateChange(
                    wifiInfo: WifiInfo,
                    networkInfo: NetworkInfo
                ) {
                    if (!isAging) {
                        LogUtil.d(TAG, "onNetworkStateChange : isAging is false")
                        return
                    }
                    val ssid = wifiInfo.ssid
                    LogUtil.d(
                        TAG,
                        "onNetworkStateChange : wifi ssid = $ssid , connected state = ${networkInfo.state}"
                    )
                    if (networkInfo.state == NetworkInfo.State.CONNECTED) {
                        if (ssid.equals(getAgingSsid())) {
                            stopWifiScan(null)
                        }
                    }
                }

                override fun onWifiPasswordError() {

                }

            }
        )

    }

    /**
     * 对扫描结果进行解析.
     *
     * @param scanResult 扫描结果对象.
     * @return WifiDeviceBean
     */
    private fun analyzeScanResult(scanResult: ScanResult): WifiDeviceBean {
        // 对WIFI扫描结果进行解析
        val wifiDeviceBean = WifiDeviceBean(scanResult.BSSID)
        wifiDeviceBean.wifiSSID = scanResult.SSID
        wifiDeviceBean.wifiCapabilities = scanResult.capabilities
        wifiDeviceBean.wifiLevel = scanResult.level
        wifiDeviceBean.wifiScanTime = scanResult.timestamp
        return wifiDeviceBean
    }

    /**
     * 将WIFI配置文件解析为WIFI实体对象.
     *
     * @param wifiConfiguration wifi配置文件
     * @return WIFI实体对象
     */
    private fun wifiConfigurationToWifiDeviceBean(wifiConfiguration: WifiConfiguration): WifiDeviceBean {
        val wifiDeviceBean = WifiDeviceBean(wifiConfiguration.BSSID)
        wifiDeviceBean.wifiSSID = wifiConfiguration.SSID.replace("\"", "")
        wifiDeviceBean.wifiNetWorkId = wifiConfiguration.networkId
        wifiDeviceBean.wifiConfiguration = wifiConfiguration
        val wifiInfo = getConnectionInfo()
        // 设置连接状态
        if (wifiInfo != null) {
            LogUtil.d(TAG, "getWifiConfigList : wifiInfo = ${wifiInfo.ssid}")
            if (TextUtils.equals(wifiConfiguration.SSID, wifiInfo.ssid)) {
                // 与当前连接的网络信息一致，则对当前已连接的网络进行状态设置
                when (wifiInfo.supplicantState) {
                    SupplicantState.DISCONNECTED, SupplicantState.INTERFACE_DISABLED,
                    SupplicantState.INACTIVE, SupplicantState.SCANNING,
                    SupplicantState.DORMANT, SupplicantState.UNINITIALIZED,
                    SupplicantState.INVALID -> {
                        // 已断开
                        wifiDeviceBean.wifiConnectedState =
                            NetworkInfo.State.DISCONNECTED
                    }

                    SupplicantState.COMPLETED -> {
                        // 已连接
                        wifiDeviceBean.wifiConnectedState = NetworkInfo.State.CONNECTED
                    }

                    SupplicantState.AUTHENTICATING, SupplicantState.ASSOCIATED,
                    SupplicantState.FOUR_WAY_HANDSHAKE,
                    SupplicantState.GROUP_HANDSHAKE -> {
                        // 连接中
                        wifiDeviceBean.wifiConnectedState = NetworkInfo.State.CONNECTING
                    }

                    else -> {
                        wifiDeviceBean.wifiConnectedState =
                            NetworkInfo.State.DISCONNECTED
                    }
                }
            } else {
                // 未发现当前连接对象，则全为未连接
                wifiDeviceBean.wifiConnectedState = NetworkInfo.State.DISCONNECTED
            }
        } else {
            // 没获取到对象，则说明当前无连接，则状态全为DISCONNECTED
            wifiDeviceBean.wifiConnectedState = NetworkInfo.State.DISCONNECTED
        }
        if (wifiConfiguration.wepKeys[0] != null) {
            wifiDeviceBean.wifiCapabilities = Contacts.WIFI_CAP_WEP
        } else {
            if (wifiConfiguration.allowedKeyManagement
                    .get(WifiConfiguration.KeyMgmt.NONE)
            ) {
                // 无密码
                wifiDeviceBean.wifiPassword = ""
                wifiDeviceBean.wifiCapabilities = ""
            } else if (wifiConfiguration.allowedKeyManagement
                    .get(WifiConfiguration.KeyMgmt.WPA_PSK)
            ) {
                // WPA加密类型
                wifiDeviceBean.wifiCapabilities = Contacts.WIFI_CAP_WPA
                //TODO 修改完善加密类型
//            } else if (wifiConfiguration.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.SAE)) {
//                // 混合加密类型
//                wifiDeviceBean.wifiCapabilities = Contacts.WIFI_CAP_WPA2_3
            } else if (wifiConfiguration.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.WPA_EAP)) {
                // EAP加密类型
                wifiDeviceBean.wifiCapabilities = Contacts.WIFI_CAP_EAP
            } else {
                // 其他类型
                wifiDeviceBean.wifiCapabilities = Contacts.WIFI_CAP_EAP
            }
        }
        LogUtil.d(
            TAG,
            "getWifiConfigList : ssid = ${wifiDeviceBean.wifiSSID} , " +
                    "password = ${wifiDeviceBean.wifiPassword} , " +
                    "networkId = ${wifiDeviceBean.wifiNetWorkId} , " +
                    "CAP = ${wifiDeviceBean.wifiCapabilities} " +
                    "state = ${wifiDeviceBean.wifiConnectedState}"
        )
        return wifiDeviceBean
    }

    /**
     * 注册WIFI广播.
     *
     * @param context 环境上下文
     */
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private fun registerWifiReceiver(context: Context) {
        LogUtil.d(TAG, "registerWifiReceiver : ")
        val filter = IntentFilter()
        // WIFI打开/关闭状态Action
        filter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
        // WIFI热点打开/关闭状态Action
        filter.addAction(WifiManager.WIFI_AP_STATE_CHANGED_ACTION)
        // 热点接入广播
        filter.addAction("android.net.wifi.WIFI_AP_STA_JOIN_WITH_NAME")
        // filter.addAction(Contacts.WIFI_AP_STA_JOIN_ACTION)
        // 热点断开广播
        filter.addAction(Contacts.WIFI_AP_STA_LEAVE_ACTION)
        // WIFI可用扫描列表广播
        filter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)
        // WIFI连接过程网络状态广播
        filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
        // WIFI接入点状态广播
        filter.addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION)
        // WIFI连接/断开状态广播
        filter.addAction(WifiManager.SUPPLICANT_CONNECTION_CHANGE_ACTION)

        context.registerReceiver(wifiReceiver, filter)
    }

    fun isAging(): Boolean {
        return isAging
    }

    /**
     * 注销WIFI广播.
     *
     * @param context 环境上下文
     */
    private fun unregisterWifiReceiver(context: Context) {
        LogUtil.d(TAG, "unregisterWifiReceiver : ")
        context.unregisterReceiver(wifiReceiver)
    }


    /**
     * 设置WIFI开关状态(打开或关闭).
     *
     * @param state
     */
    fun setWifiState(state: Boolean) {
        LogUtil.d(TAG, "setWifiState : wifi state = $state")
        val event2 = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE)
        // 使用安全调用操作符和 let 作用域函数处理非空情况
        event2?.let { safeEvent ->
            val bundle1 = safeEvent.payload
            // 使用默认值避免可能的空值异常
            val status = bundle1.getInt(VDKey.STATUS, -1)
            val type = bundle1.getInt(VDKey.TYPE, -1)
            val isWireless = bundle1.getBoolean(VDKey.ENABLE, false)
            // 处理获取到的数据
            Log.d(
                TAG,
                "intiListener: zhc6whu:Status: $status, Type: $type, Wireless: $isWireless"
            )
            if (status > 0 && state) {
                EToast.showToast(
                    MyApplication.getContext(),
                    "打开Wi-Fi可能会断开CarPlay/HiCar/Carlink连接",
                    Toast.LENGTH_SHORT,
                    false
                )
                val payload = Bundle()
                if (type == PhoneLink.TYPE_SERVERID_CARPLAY) {
                    payload.putInt(
                        VDKey.TYPE,
                        VDValuePhoneLink.ServerId.CARPLAY
                    )
                } else if (type == PhoneLink.TYPE_SERVERID_CARLINK) {
                    payload.putInt(
                        VDKey.TYPE,
                        VDValuePhoneLink.ServerId.CARLINK
                    )
                } else if (type == PhoneLink.TYPE_SERVERID_HICAR) {
                    payload.putInt(
                        VDKey.TYPE,
                        VDValuePhoneLink.ServerId.HICAR
                    )
                }
                val event1 = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
                val bundle = event1.payload
                val devicePhoneLink = bundle.getParcelable<VDLinkDevice>(VDKey.INFO)
                payload.putParcelable(VDKey.DATA, devicePhoneLink) //(VDLinkDevice)device,要断开的设备
                val event = VDEvent(VDEventPhoneLink.DISCONNECT_DEVICE, payload)
                VDBus.getDefault().set(event)
            }
        }
        wifiManager!!.isWifiEnabled = state
    }

    /**
     * 获取WIFI状态.
     *
     * @return
     */
    fun getWifiState(): Boolean {
        LogUtil.d(TAG, "getWifiState : ")
        return wifiManager!!.isWifiEnabled
    }

    /**
     * 获取接入或断开热点的设备名称.
     *
     * @param hotspotMac 接入或断开热点设备的MAC地址
     * @return 名称
     */
    //TODO 解除注释
    fun getConnectedHotspotDevices(hotspotMac: String): String {
//        val hotspotName: String? = wifiManager!!.getClientDeviceName(hotspotMac)
        val hotspotName: String = hotspotMac
        LogUtil.d(
            TAG, "getConnectedHotspotDevices : hotspotMac = $hotspotMac , " +
                    "hotspotName = $hotspotName"
        )
        return hotspotName ?: ""
    }

    /**
     * 更新热点名称及密码.
     *
     * @param hotspotName 热点名称.
     * @param hotspotPassword 热点密码.
     */
    fun updateHotspotInfo(hotspotName: String, hotspotPassword: String) {
        LogUtil.d(TAG, "updateHotspotInfo : zhc6whu ssid = $hotspotName , password = $hotspotPassword")
        // 获取WIFI热点配置
        val config: WifiConfiguration? = wifiManager!!.getWifiApConfiguration()
        if (config == null) {
            LogUtil.d(TAG, "updateHotspotInfo : wifi hotspot is null!")
            return
        }
        // 设置热点SSID
        config.SSID = hotspotName
        // 清除安全配置
        config.allowedKeyManagement.clear()
        config.allowedPairwiseCiphers.clear()
        config.allowedGroupCiphers.clear()
        // 设置密码
        config.preSharedKey = hotspotPassword
        // 设置热点使用频段
        config.apBand =
            if (get5GBand()) WifiConfiguration.AP_BAND_5GHZ else WifiConfiguration.AP_BAND_2GHZ
        // 设置热点通道为0,系统会自动选择一个通道
        config.apChannel = 0
        // 设置密匙管理方式
        if (hotspotPassword.isNotBlank()) {
            config.allowedKeyManagement.set(KeyMgmt.WPA2_PSK)
        } else {
            config.allowedKeyManagement.set(KeyMgmt.NONE)
        }
        // 使配置生效
        wifiManager!!.setWifiApConfiguration(config)
        //如果当前热点已开启
        if (getHotspotState()) {
            restartWifiHotspot()
        }else{
            closeWifiHotspot()
        }
    }

    /**
     * 更新热点名称及密码.
     *
     * @param hotspotName 热点名称.
     * @param hotspotPassword 热点密码.
     */
    fun updateHotspotInfoForSystem(hotspotName: String, hotspotPassword: String) {
        LogUtil.d(TAG, "updateHotspotInfo : zhc6whu ssid = $hotspotName , password = $hotspotPassword")
        // 获取WIFI热点配置
        val config: WifiConfiguration? = wifiManager!!.getWifiApConfiguration()
        if (config == null) {
            LogUtil.d(TAG, "updateHotspotInfo : wifi hotspot is null!")
            return
        }
        // 设置热点SSID
        config.SSID = hotspotName
        // 清除安全配置
        config.allowedKeyManagement.clear()
        config.allowedPairwiseCiphers.clear()
        config.allowedGroupCiphers.clear()
        // 设置密码
        config.preSharedKey = hotspotPassword
        // 设置热点使用频段
        config.apBand =
            if (get5GBand()) WifiConfiguration.AP_BAND_5GHZ else WifiConfiguration.AP_BAND_2GHZ
        // 设置热点通道为0,系统会自动选择一个通道
        config.apChannel = 0
        // 设置密匙管理方式
        if (hotspotPassword.isNotBlank()) {
            config.allowedKeyManagement.set(KeyMgmt.WPA2_PSK)
        } else {
            config.allowedKeyManagement.set(KeyMgmt.NONE)
        }
        // 使配置生效
        wifiManager!!.setWifiApConfiguration(config)
    }

    /**
     * 关闭WIFI热点.
     *
     */
    private fun restartWifiHotspot() {
        LogUtil.d(TAG, "closeWifiHotspot : ")
        // 关闭热点
        connectivityManager!!.stopTethering(ConnectivityManager.TETHERING_WIFI)
        SliceReceiver.notifyChange(ProviderURI.BROADCAST)

        Handler(Looper.getMainLooper()).postDelayed({
            // 启用自动关闭热点开关功能
            Settings.Global.putInt(
                MyApplication.getContext().contentResolver,
                Settings.Global.SOFT_AP_TIMEOUT_ENABLED,
                1
            )
            connectivityManager?.startTethering(
                ConnectivityManager.TETHERING_WIFI,
                true,
                startTetheringCallback,
                null
            )
            SliceReceiver.notifyChange(ProviderURI.BROADCAST)
        }, 2000L)   // 2000 ms = 2 s
    }


    /**
     * 更新热点名称及密码.
     *
     * @param hotspotName 热点名称.
     * @param hotspotPassword 热点密码.
     */
    fun updateFirstHotspotInfo(hotspotName: String, hotspotPassword: String) {
        LogUtil.d(TAG, "updateHotspotInfo : zhc6whu ssid = $hotspotName , password = $hotspotPassword")
        // 获取WIFI热点配置
        val config: WifiConfiguration? = wifiManager!!.getWifiApConfiguration()
        if (config == null) {
            LogUtil.d(TAG, "updateHotspotInfo : wifi hotspot is null!")
            return
        }
        // 设置热点SSID
        config.SSID = hotspotName
        // 清除安全配置
        config.allowedKeyManagement.clear()
        config.allowedPairwiseCiphers.clear()
        config.allowedGroupCiphers.clear()
        // 设置密码
        config.preSharedKey = hotspotPassword
        // 设置热点使用频段
        config.apBand =
            if (get5GBand()) WifiConfiguration.AP_BAND_5GHZ else WifiConfiguration.AP_BAND_2GHZ
        // 设置热点通道为0,系统会自动选择一个通道
        config.apChannel = 0
        // 设置密匙管理方式
        if (hotspotPassword.isNotBlank()) {
            config.allowedKeyManagement.set(KeyMgmt.WPA2_PSK)
        } else {
            config.allowedKeyManagement.set(KeyMgmt.NONE)
        }
        // 使配置生效
        wifiManager!!.setWifiApConfiguration(config)
        // 关闭热点
        closeFirstWifiHotspot()
    }

    /**
     *开启WIFI热点.
     *
     */
    fun openWifiHotspot() {
        // 启用自动关闭热点开关功能
        Settings.Global.putInt(
            MyApplication.getContext().contentResolver,
            Settings.Global.SOFT_AP_TIMEOUT_ENABLED,
            1
        )
        // 启动热点，类型为WIFI热点
        connectivityManager!!.startTethering(
            ConnectivityManager.TETHERING_WIFI,
            true,
            startTetheringCallback,
            null
        )
        SliceReceiver.notifyChange(ProviderURI.BROADCAST)
    }

    /**
     * 关闭WIFI热点.
     *
     */
    fun closeWifiHotspot() {
        LogUtil.d(TAG, "closeWifiHotspot : ")
        val event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
        if (event != null) {
            val bundle = event.payload
            val status = bundle.getInt(VDKey.STATUS)
            val type = bundle.getInt(VDKey.TYPE)
            val isWireless = bundle.getBoolean(VDKey.ENABLE)
            LogUtil.d(TAG, "closeWifiHotspot : $event+$type+$isWireless")
            LogUtil.d(TAG, "closeWifiHotspot zhc6whu : $status")
            if(status >0){
                showCloseHotspotConfirmation(type)
                return
            }
        }
        // 关闭热点
        connectivityManager!!.stopTethering(ConnectivityManager.TETHERING_WIFI)
        SliceReceiver.notifyChange(ProviderURI.BROADCAST)
    }

    /**
     * 上电关闭WIFI热点.
     *
     */
    fun closeFirstWifiHotspot() {
        LogUtil.d(TAG, "closeFirstWifiHotspot : ")
        // 关闭热点
        connectivityManager!!.stopTethering(ConnectivityManager.TETHERING_WIFI)
        SliceReceiver.notifyChange(ProviderURI.BROADCAST)
    }
    private fun showCloseHotspotConfirmation(type: Int) {
        Handler(Looper.getMainLooper()).post {
            ConfirmDialog(MyApplication.getContext()).apply {
                var phoneLinkType = ""
                when (type) {
                    0 -> phoneLinkType = "IPOD"
                    1 -> phoneLinkType = "Apple CarPlay"
                    2 -> phoneLinkType = ""
                    3 -> phoneLinkType = ""
                    4 -> phoneLinkType = ""
                    5 -> phoneLinkType = ""
                    6 -> phoneLinkType = ""
                    7 -> phoneLinkType = "HUAWEI HiCar"
                    8 -> phoneLinkType = "ICCOA Carlink"
                 }
                setTips("关闭车机热点将会断开当前${phoneLinkType}连接，确定要关闭热点？")
                setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
                    override fun onConfirmClick() {
                        connectivityManager?.stopTethering(ConnectivityManager.TETHERING_WIFI)
                        dismiss()
                    }
                    override fun onCancelClick() {
                        dismiss()
                    }
                })
                show()
            }
        }
    }

    /**
     * 获取热点状态.
     *
     * @return Boolean
     */
    fun getHotspotState(): Boolean {
        return wifiManager!!.getWifiApState() == WifiManager.WIFI_AP_STATE_ENABLED
    }

    /**
     * 根据网络开关状态，设置网络开关通知状态.
     *
     * @param switchState 网络通知开关状态
     */
    fun setNetWorkNotification(switchState: Boolean) {
        val value = if (switchState) {
            NETWORK_NOTIFICATION_OPEN
        } else {
            NETWORK_NOTIFICATION_CLOSE
        }
        LogUtil.d(TAG, "setNetWorkNotification : switchState = $switchState , value = $value")
        // 设置系统属性值
        Settings.System.putString(
            MyApplication.getContext().contentResolver,
            Contacts.NETWORK_NOTIFICATION_KEY,
            value
        )
    }

    /**
     * 获取网络开关状态.
     *
     * @return 网络开关状态
     */
    fun getNetWorkNotification(): Boolean {
        val value = Settings.System.getString(
            MyApplication.getContext().contentResolver,
            Contacts.NETWORK_NOTIFICATION_KEY
        )
        LogUtil.d(TAG, "getNetWorkNotification : value = $value")
        if (value == null) {
            return false
        }
        return TextUtils.equals(value, NETWORK_NOTIFICATION_OPEN)
    }

    /**
     * 获取WIFI热点的SSID(名称).
     *
     */
    fun getWifiHotspotSSID(): String {
        var ssid = "获取热点名失败"
        try {
            // 获取热点网络配置信息
            val wifiConfiguration: WifiConfiguration? = wifiManager!!.getWifiApConfiguration()
            if (wifiConfiguration != null) {
                // 读取热点SSID
                ssid = wifiConfiguration.SSID
                return ssid
            }
        } catch (exception: Exception) {
            LogUtil.d(TAG, "getWifiHotspotSSID : exception = ${exception.printStackTrace()}")
        }
        return ssid
    }

    /**
     * 获取WIFI的密码.
     *
     * @return
     */
    fun getWifiHotspotPassword(): String {
        var password = ""
        try {
            // 获取热点网络配置信息
            val wifiConfiguration: WifiConfiguration? = wifiManager!!.getWifiApConfiguration()
            if (wifiConfiguration != null) {
                // 读取热点密码
                password = wifiConfiguration.preSharedKey
                return password
            }
        } catch (exception: Exception) {
            LogUtil.d(TAG, "getWifiHotspotPassword : exception = ${exception.printStackTrace()}")
        }
        return password
    }

    /**
     * 开始WIFI扫描.
     *
     * @param wifiScanningState wifi扫描状态,无需要该变量可传递null
     */
    fun startWifiScan(wifiScanningState: MutableLiveData<Boolean>?) {
        LogUtil.d(TAG, "startWifiScan : ")
        // 先关闭当前的计时器任务，然后再开始新的任务
        stopWifiScan(wifiScanningState)
        wifiScanTimerTask = CountDownTimeManager(WIFI_SCAN_COUNT) { count ->
            if (count == 0) {
                // 计时结束，关闭计时器,停止扫描
                stopWifiScan(wifiScanningState)
            } else {
                // 开始扫描一次
                wifiManager!!.startScan()
            }
        }
        // 间隔10秒，开始计时,开始扫任务
        Timer().schedule(wifiScanTimerTask, 0, WIFI_SCAN_TIME_DELAY)
        wifiScanningState?.postValue(true)
    }

    //判断是否是曾经连接过的wifi
    fun isWifiConnected(wifiDeviceBean: WifiDeviceBean): Boolean {
        val wifiConfigurationList = getWifiConfigList()
        if (wifiConfigurationList != null) {
            for (wifiConfiguration in wifiConfigurationList) {
                Log.d(
                    TAG,
                    "isWifiConnected: 判断是否是连接过的 wifi" + wifiDeviceBean.wifiSSID + "___" + wifiDeviceBean.wifiNetWorkId + "___" + wifiConfiguration.networkId
                )
                if (TextUtils.equals(
                        wifiConfiguration.SSID,
                        "\"" + wifiDeviceBean.wifiSSID + "\""
                    )
                ) {
                    Log.d(
                        TAG,
                        "isWifiConnected: 判断是否是连接过的 wifi" + wifiDeviceBean.wifiSSID + "___" + wifiDeviceBean.wifiNetWorkId + "___" + wifiConfiguration.networkId
                    )
                    return true
                }
            }
        }
        return false
    }

    /**
     * 开始老化WIFI扫描.
     *
     */
    fun startAgingWifiScan() {
        LogUtil.d(TAG, "startAgingWifiScan : ")
        // 先关闭当前的计时器任务，然后再开始新的任务
        stopWifiScan(null)
        wifiScanTimerTask = CountDownTimeManager(Int.MAX_VALUE) { count ->
            LogUtil.d(TAG, "startAgingWifiScan : startScan")
            // 开始扫描一次
            wifiManager!!.startScan()
        }
        // 间隔20秒，开始计时,开始扫任务
        Timer().schedule(wifiScanTimerTask, 0, 20000L)
    }

    /**
     * 开始扫描.
     *
     */
    fun startWifiScan() {
        // 开始扫描一次
        wifiManager!!.startScan()
    }

    /**
     * 停止WIFI扫描.
     *
     * @param wifiScanningState WIFI扫描状态,需要该变量可传递null
     */
    fun stopWifiScan(wifiScanningState: MutableLiveData<Boolean>?) {
        LogUtil.d(TAG, "stopWifiScan : ")
        wifiScanTimerTask?.cancel()
        wifiScanTimerTask = null
        wifiScanningState?.postValue(false)
    }

    /**
     * 获取WIFI扫描列表.
     *
     * @return WIFI扫描结果列表.
     */
    fun getWifiScanResult(): List<ScanResult>? {
        LogUtil.d(TAG, "getWifiScanResult :最近一次扫描结果 ")
        return wifiManager!!.scanResults
    }

    /**
     * 连接WIFI.
     *
     * @param wifiDeviceBean 待连接的WIFI对象
     * @param type 连接方式 0-> 从扫描列表进行连接，保存配置；1->从已连接列表进行连接，不保存配置
     */
    fun connectedWifi(wifiDeviceBean: WifiDeviceBean, type: Int) {
        LogUtil.d(TAG, "connectedWifi : configuration = ${wifiDeviceBean.wifiConfiguration}")
        // 自动发起连接
        wifiManager!!.connect(
            wifiDeviceBean.wifiConfiguration,
            object : WifiManager.ActionListener {
                override fun onSuccess() {
                    LogUtil.d(TAG, "connectedWifi :onSuccess!")
                    if (type == Contacts.WIFI_CONNECTED_FROM_SCAN_LIST) {
                        // 从WIFI扫描列表连接成功，则保存WIFI配置信息，否则，不进行保存
                        wifiManager!!.save(wifiDeviceBean.wifiConfiguration, null)
                    }
                }

                override fun onFailure(reason: Int) {
                    LogUtil.d(TAG, "connectedWifi : onFailure reason = $reason")
                }
            })
    }

    /**
     * 断开指定WIFI的连接.
     *
     * @param wifiDeviceBean 当前WIFI对象
     */
    fun disconnectedWifi(wifiDeviceBean: WifiDeviceBean) {
        LogUtil.d(TAG, "disconnectedWifi : wifiNetWorkId = ${wifiDeviceBean.wifiNetWorkId}")
        wifiManager!!.disableNetwork(wifiDeviceBean.wifiNetWorkId)
        //判断是否断开成功
        if (wifiManager!!.disconnect()) {
            LogUtil.d(TAG, "disconnectedWifi : disconnect success")
        } else {
            LogUtil.d(TAG, "disconnectedWifi : disconnect fail")
        }
    }

    /**
     * 断开指定WIFI的连接.
     *
     * @param wifiNetWorkId 当前WIFI对象
     */
    fun disconnectedWifi(wifiNetWorkId: Int) {
        LogUtil.d(TAG, "disconnectedWifi : wifiNetWorkId = $wifiNetWorkId")
        wifiManager!!.disableNetwork(wifiNetWorkId)
    }

    /**
     * 删除WIFI配置信息(忽略网络)
     *
     * @param wifiDeviceBean 待删除的WIFI配置
     */
    fun deleteWifiConfig(wifiDeviceBean: WifiDeviceBean) {
        LogUtil.d(TAG, "deleteWifiConfig : wifiNetWorkId = ${wifiDeviceBean.wifiNetWorkId}")
        // 获取所有已保存的 Wi-Fi 配置
        val configuredNetworks = wifiManager?.configuredNetworks ?: emptyList()
        Log.d(TAG, "deleteWifiConfig: " + configuredNetworks)

        // 查找匹配 SSID 的网络配置
        val wifiConfig = configuredNetworks.firstOrNull { config ->
            // SSID 在配置中通常用双引号包裹，需要处理这种情况
            val configSsid = config.SSID.removeSurrounding("\"")
            configSsid == wifiDeviceBean.wifiSSID
        }
        if (wifiConfig != null) {
            Log.d(TAG, "deleteWifiConfig: " + wifiConfig.networkId)
            wifiManager!!.forget(wifiConfig.networkId, object : WifiManager.ActionListener {
                override fun onSuccess() {
                    Log.d(TAG, "deleteWifiConfig Wi-Fi 配置删除成功")
                }

                override fun onFailure(reason: Int) {
                    Log.e(TAG, "Wi-Fi 配置删除失败，reason: $reason")
                }
            })
        }
        wifiManager!!.saveConfiguration()
    }

    /**
     * 删除WIFI配置信息(忽略网络)
     *
     * @param wifiNetWorkId 待删除的WIFI配置id
     */
    fun deleteWifiConfig(wifiNetWorkId: Int) {
        LogUtil.d(TAG, "deleteWifiConfig : wifiNetWorkId = $wifiNetWorkId")
        wifiManager!!.removeNetwork(wifiNetWorkId)
        wifiManager!!.saveConfiguration()
    }

    /**
     * 清除所有wifi配置信息.
     *
     */
    fun deleteAllWifiConfig() {
        LogUtil.d(TAG, "deleteAllWifiConfig : ")
        getWifiConfigList()?.forEach {
            wifiManager!!.removeNetwork(it.networkId)
            wifiManager!!.saveConfiguration()
        }
    }

    /**
     * 获取网络连接信息.
     *
     * @return WifiInfo
     */
    fun getConnectionInfo(): WifiInfo? {
        return wifiManager!!.connectionInfo
    }

    /**
     * 获取WIFI配置列表.
     *
     * @return MutableList<WifiConfiguration>
     */
    fun getWifiConfigList(): MutableList<WifiConfiguration>? {
        return wifiManager!!.configuredNetworks
    }

    /**
     * 根据WIFI实体对象，构建WIFI配置信息.
     *
     * @param wifiDeviceBean WIFI实体对象
     * @return WIFI配置信息
     */
    fun createWifiConfiguration(wifiDeviceBean: WifiDeviceBean): WifiConfiguration {
        LogUtil.d(TAG, "createWifiConfiguration : password = ${wifiDeviceBean.wifiPassword}")
        // 构建一个WIFI配置对象
        val configuration = WifiConfiguration()
        // 清空原始数据
        configuration.allowedAuthAlgorithms.clear()
        configuration.allowedGroupCiphers.clear()
        configuration.allowedKeyManagement.clear()
        configuration.allowedPairwiseCiphers.clear()
        configuration.allowedProtocols.clear()
        // 设置SSID
        configuration.SSID = "\"" + wifiDeviceBean.wifiSSID + "\""
        // 设置BSSID
        configuration.BSSID = wifiDeviceBean.wifiBSSID
        configuration.hiddenSSID = true
        val wifiConfiguration = isExistInConfiguredNetworks(wifiDeviceBean.wifiSSID)
        if (wifiConfiguration != null) {
            // 移除已经存在的WIFI配置信息
            wifiManager!!.removeNetwork(wifiConfiguration.networkId)
        }
        if (wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_WEP)) {
            // WEP加密网络设置
            configuration.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.SHARED)
            configuration.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP104)
            configuration.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE)
            configuration.wepTxKeyIndex = 0
            configuration.status = WifiConfiguration.Status.ENABLED
            val passwordLength = wifiDeviceBean.wifiPassword.length
            if ((passwordLength == 10 || passwordLength == 26)
                && wifiDeviceBean.wifiPassword.matches(REGEX.toRegex())
            ) {
                configuration.wepKeys[0] = wifiDeviceBean.wifiPassword
            } else {
                configuration.wepKeys[0] = '"' + wifiDeviceBean.wifiPassword + '"'
            }
        } else if (wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA)) {
            // WPA加密网络设置
            configuration.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
            configuration.preSharedKey = "\"" + wifiDeviceBean.wifiPassword + "\""
            configuration.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
            configuration.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
            configuration.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
            configuration.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
            configuration.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
            configuration.status = WifiConfiguration.Status.ENABLED
        } else if (wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA3)
            || wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA2_3)
        ) {
            // WPA2/WPA3 WPA3 加密网络设置
            //TODO 修改完善加密类型
//            configuration.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.SAE)
            configuration.preSharedKey = "\"" + wifiDeviceBean.wifiPassword + "\""
            configuration.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
            configuration.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
            configuration.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
            configuration.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
            configuration.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
            configuration.status = WifiConfiguration.Status.ENABLED
//            configuration.requirePMF = true
        } else if (wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_EAP)) {
            // EAP加密网络设置
            configuration.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_EAP)
            configuration.preSharedKey = "\"" + wifiDeviceBean.wifiPassword + "\""
            configuration.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
            configuration.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
            configuration.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
            configuration.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
            configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
            configuration.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
            configuration.status = WifiConfiguration.Status.ENABLED
        } else {
            if (!wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_WEP)
                && !wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_EAP)
                && !wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA)
                && !wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA2_3)
                && !wifiDeviceBean.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA3)
            ) {
                // 无密码设置
                configuration.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE)
                configuration.allowedAuthAlgorithms.clear()
                configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
                configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
            } else {
                // 其他加密网络设置
                configuration.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
                configuration.preSharedKey = "\"" + wifiDeviceBean.wifiPassword + "\""
                configuration.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
                configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
                configuration.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
                configuration.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
                configuration.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
                configuration.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
                configuration.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
                configuration.status = WifiConfiguration.Status.ENABLED
            }
        }
        return configuration
    }

    /**
     * 判断当前网络是否再已配置列表中.
     *
     * @param ssid wifi ssid
     * @return WifiConfiguration
     */
    private fun isExistInConfiguredNetworks(ssid: String): WifiConfiguration? {
        // 获取WIFI配置列表
        val wifiConfigList = getWifiConfigList() ?: return null
        // 若SSID再WIFI配置列表中存在，则返回true
        wifiConfigList.forEach { configure ->
            if (TextUtils.equals(configure.SSID, "\"" + ssid + "\"")) {
                return configure
            }
        }
        return null
    }

    /**
     * 判断当前WIFI频段是否为5G.
     *
     * @return Boolean
     */
    fun get5GBand(): Boolean {
        Log.d(TAG, "zhc6whu:热点兼容性： $is5GBand")
        return is5GBand
    }

    fun set5GBand(band: Boolean) {
        is5GBand = band
        Log.d(TAG, "zhc6whu:热点兼容性设置为： $is5GBand")
    }

    /**
     * 释放WIFI管理相关数据.
     *
     */
    fun unInitWifiManager() {
        LogUtil.d(TAG, "unInitWifiManager : ")
        // 注销WIFI广播
        unregisterWifiReceiver(MyApplication.getContext())
        carPropertyManager?.unregisterCallback(carPropertyCallback)
        wifiReceiver = null
        wifiManager = null
        connectivityManager = null
        carConfigInfoManager = null
        carPropertyManager = null
        carInfoManager = null
    }

    //TODO 取消注释
    private fun getAgingSsid(): String {
//        val agingSsid = carInfoManager?.getStringProperty(CarInfoManager.ID_CAL_DIAGNOSTIC_WIFI_NAME)
//        LogUtil.d(TAG, "getAgingSsid : agingSsid = $agingSsid")
//        return agingSsid?.trim() ?:""
        return "半瓶矿泉水"
    }

    //TODO 取消注释
    private fun getAgingPassword(): String {
//        val agingPassword = carInfoManager?.getStringProperty(CarInfoManager.ID_CAL_DIAGNOSTIC_WIFI_PASSWORD)
//        LogUtil.d(TAG, "getAgingPassword : agingPassword = $agingPassword")
//        return agingPassword?.trim() ?:""
        return "11223344"
    }

    companion object {
        // 日志标志位
        private const val TAG = "CarWifiManager"

        // 网络通知开关关
        private const val NETWORK_NOTIFICATION_CLOSE = "close"

        // 网络通知开关开
        const val NETWORK_NOTIFICATION_OPEN = "open"

        // 可用WIFI扫描时间间隔，10S扫描一次
        private const val WIFI_SCAN_TIME_DELAY = 10000L

        // 可用WIFI扫描次数，60S，10S一次，共计6次
        private const val WIFI_SCAN_COUNT = 6

        // WIFI密码正则表达式
        private const val REGEX = "[0-9A-Fa-f]*"

        // 单例对象
        val instance: CarWifiManager by lazy(LazyThreadSafetyMode.PUBLICATION) {
            CarWifiManager()
        }
    }
}
