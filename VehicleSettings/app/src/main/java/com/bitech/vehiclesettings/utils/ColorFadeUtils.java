package com.bitech.vehiclesettings.utils;

import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.view.View;
import android.view.ViewGroup;

import com.bitech.vehiclesettings.R;

public class ColorFadeUtils {
    // 模式常量
    public static final int MODE_BRIGHTNESS = 1;  // 仅提升亮度
    public static final int MODE_TRANSLUCENT = 2; // 仅调整透明度
    public static final int MODE_COMBINE = 3;     // 亮度+透明度组合

    /**
     * 应用颜色变浅效果
     * @param container 目标容器视图
     * @param brightnessScale 亮度缩放系数（建议1.0-2.0）
     * @param alphaScale 透明度缩放系数（建议0.0-1.0）
     * @param mode 效果模式
     */
    public static void applyFadeEffect(ViewGroup container,
                                       float brightnessScale,
                                       float alphaScale,
                                       int mode) {
        traverseViews(container, true, brightnessScale, alphaScale, mode);
    }

    /**
     * 移除颜色变浅效果
     * @param container 目标容器视图
     */
    public static void removeFadeEffect(ViewGroup container) {
        traverseViews(container, false, 1f, 1f, 0);
    }

    private static void traverseViews(ViewGroup viewGroup,
                                      boolean applyEffect,
                                      float brightness,
                                      float alpha,
                                      int mode) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View child = viewGroup.getChildAt(i);
            if (child instanceof ViewGroup) {
                traverseViews((ViewGroup) child, applyEffect, brightness, alpha, mode);
            }
            applyEffectToView(child, applyEffect, brightness, alpha, mode);
        }
        applyEffectToView(viewGroup, applyEffect, brightness, alpha, mode);
    }

    private static void applyEffectToView(View view,
                                          boolean applyEffect,
                                          float brightness,
                                          float alpha,
                                          int mode) {
        if (applyEffect) {
            // 保存原始状态
            if (view.getTag(R.id.original_state) == null) {
                OriginalState state = new OriginalState();
                state.layerType = view.getLayerType();
                state.alpha = view.getAlpha();
                view.setTag(R.id.original_state, state);
            }

            // 创建颜色矩阵
            ColorMatrix matrix = new ColorMatrix();
            if (mode == MODE_BRIGHTNESS || mode == MODE_COMBINE) {
                matrix.setScale(brightness, brightness, brightness, 1f);
            }
            if (mode == MODE_TRANSLUCENT || mode == MODE_COMBINE) {
                view.setAlpha(alpha); // 直接设置透明度
            }

            // 应用硬件层
            Paint paint = new Paint();
            paint.setColorFilter(new ColorMatrixColorFilter(matrix));
            view.setLayerType(View.LAYER_TYPE_HARDWARE, paint);
        } else {
            // 恢复原始状态
            Object tag = view.getTag(R.id.original_state);
            if (tag instanceof OriginalState) {
                OriginalState state = (OriginalState) tag;
                view.setLayerType(state.layerType, null);
                view.setAlpha(state.alpha);
                view.setTag(R.id.original_state, null);
            }
        }
    }

    // 保存原始状态的内部类
    private static class OriginalState {
        int layerType;
        float alpha;
    }
}
