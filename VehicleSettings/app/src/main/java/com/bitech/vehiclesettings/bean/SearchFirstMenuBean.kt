package com.bitech.vehiclesettings.bean

import android.widget.TextView

/**
 * @ClassName: SearchFirstMenuBean
 * 
 * @Date:  2024/4/16 10:37
 * @Description: 设置一级菜单实体对象.
 **/
data class SearchFirstMenuBean(val menuName: String) {

    // 一级菜单对应的图标资源id
    var firstLevelMenuIconId = -1

    // 一级菜单对应的页面ID
    var firstMenuId = -1

    // 一级菜单对应的二级菜单列表
    var secondMenuList = mutableListOf<SearchSecondMenu>()

    /**
     * @ClassName: SearchSecondMenu
     * 
     * @Date:  2024/4/16 10:43
     * @Description: 搜索二级菜单类.
     **/
    data class SearchSecondMenu(val secondMenuName: String) {

        // 二级菜单文言项对应的TextView
        var nameTextView: TextView? = null

        // 二级菜单文言项对应的控件需要在页面内部滑动的距离
        var scrollDistance = -1
    }
}
