package com.bitech.vehiclesettings.service

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.interfaces.newenergy.INewEnergyListener
import com.bitech.platformlib.manager.NewEnergyManager
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.carapi.constants.Car3DModel
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy
import com.bitech.vehiclesettings.contentprovider.NewEnergyContentProvider
import com.bitech.vehiclesettings.fragment.NewEnergyFragment
import com.bitech.vehiclesettings.service.VehicleServiceHandler.MSG_SHOW_THERMAL_RUNAWAY_DIS
import com.bitech.vehiclesettings.utils.GlobalPopupWindowUtils
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.ThreeDModelUtil
import com.bitech.vehiclesettings.view.newenergy.ChargeSOCDialog
import com.bitech.vehiclesettings.view.newenergy.ComConfirmDialog
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.bitech.vehiclesettings.widget.NewEnergyWidget
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicReference

/**
 * @description: 新能源相关监听处理
 */
class NewEnergyLifeCycle(
    private val handler: VehicleServiceHandler,
    private val lifecycleOwner: LifecycleOwner
) : LifecycleEventObserver {

    private var newEnergyManager: NewEnergyManager? = null
    private val powerMode = AtomicInteger(CarNewEnergy.PowerMode.OFF)
    private val apcLevelLimit = AtomicInteger(CarNewEnergy.ApcLevelLimit.LEVEL_0)
    private val carMode = AtomicInteger(CarNewEnergy.CarMode.NORMAL_MODE)
    private val lbatipVehPwrMod = AtomicInteger(CarNewEnergy.LbatipVehPwrMod.NO_WARNING)
    private val eesAvailable = AtomicInteger(CarNewEnergy.EnergySavingAvailable.UN_AVAILABLE)
    private val chargeStopSOC = AtomicInteger()
    private val dischargeStopSOC = AtomicInteger()
    private val bookChargeStatus = AtomicInteger()
    private val chargeRemainTime = AtomicInteger()
    private val chargeCurrent = AtomicReference<Float>()
    private val chargeVoltage = AtomicReference<Float>()
    private val dischargePowerMax = AtomicReference<Float>()
    private val dischargePowerCurrent = AtomicReference<Float>()
    private var chargeSOCDialog: ChargeSOCDialog? = null
    private var comConfirmDialog: ComConfirmDialog? = null
    private var showRoomDialog: ComConfirmDialog? = null

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                LogUtil.i(TAG, "onCreate: $source")
                newEnergyManager = BitechCar.getInstance()
                    .getServiceManager(BitechCar.CAR_ENERGY_MANAGER) as NewEnergyManager
                newEnergyManager?.addCallback(TAG, msgCallback)
                newEnergyManager?.registerListener()

                lifecycleOwner.lifecycleScope.launch(Dispatchers.Default) {
                    //动力电池热失控报警
                    val thermalRunawayDisValue = newEnergyManager?.thermalRunawayDis ?: 0
                    //负载限制
                    powerMode.set(newEnergyManager?.powerStatus ?: 0)
                    apcLevelLimit.set(newEnergyManager?.apcLevelLimit ?: 0)
                    carMode.set(newEnergyManager?.carMode ?: 0)
                    lbatipVehPwrMod.set(newEnergyManager?.lbatipVehPwrMod ?: 0)
                    eesAvailable.set(newEnergyManager?.eesModeAvailable ?: 0)
                    //立即放电
                    mCurrentSOC.set((newEnergyManager?.batteryLevel ?: 0).coerceIn(0, 100))
                    chargeStopSOC.set(
                        (newEnergyManager?.chargeSocThreshold ?: 80).coerceIn(
                            80,
                            100
                        )
                    )
                    dischargeStopSOC.set(
                        (newEnergyManager?.getDischargeSocLowThreshold(lifecycleOwner as Context)
                            ?: 30).coerceIn(30, 100)
                    )
                    //ICC记忆/设置放电下限值
                    newEnergyManager?.setDischargeSocThreshold(
                        lifecycleOwner as Context,
                        dischargeStopSOC.get()
                    )
                    //预约充电状态
                    bookChargeStatus.set(newEnergyManager?.bookChargeStatus ?: 0)
                    //充放电状态
                    chargingStatus.set(newEnergyManager?.bmsChargeSts == CarNewEnergy.FastChargingSts.PARKING_CHARGE)
                    dischargingStatus.set(newEnergyManager?.dischargeSts == CarNewEnergy.V2LFunctionSts.ON)
                    //展车模式
                    exhibitionMod.set(newEnergyManager?.exhibitionMod ?: 0)
                    //纯电续航
                    mEnduranceElectric.set(
                        newEnergyManager?.enduranceElectricCurrentMode.takeIf { it != Int.MIN_VALUE }
                            ?: 0)

                    notifyNewEnergyUri()
                    notifyWidget()

                    withContext(Dispatchers.Main) {
                        //负载限制Toast
                        showLoadLimitPrompt()
                        //全局弹窗-动力电池热失控报警
                        showThermalRunawayDis(thermalRunawayDisValue)
                    }
                }
            }

            Lifecycle.Event.ON_DESTROY -> {
                LogUtil.i(TAG, "onDestroy: $source")
                newEnergyManager?.removeCallback(TAG)
            }

            else -> {}
        }
    }

    private val msgCallback: INewEnergyListener = object : INewEnergyListener {
        override fun onThermalRunawayDis(value: Int) {
            Log.d(TAG, "onThermalRunawayDis: $value")
            handler.obtainMessage(MSG_SHOW_THERMAL_RUNAWAY_DIS, value).sendToTarget()
        }

        override fun onPowerMode(value: Int) {
            Log.d(TAG, "onPowerMode: $value")
            powerMode.set(value)
            showLoadLimitPrompt()
        }

        override fun onApcLevelLimit(value: Int) {
            Log.d(TAG, "onApcLevelLimit: $value")
            apcLevelLimit.set(value)
            showLoadLimitPrompt()
        }

        override fun onCarMode(value: Int) {
            Log.d(TAG, "onCarMode: $value")
            carMode.set(value)
            showLoadLimitPrompt()
        }

        override fun onLbatipVehPwrMod(value: Int) {
            Log.d(TAG, "onLbatipVehPwrMod: $value")
            lbatipVehPwrMod.set(value)
            showLoadLimitPrompt()
        }

        override fun onEesModeAvailable(value: Int) {
            Log.d(TAG, "onEesModeAvailable: $value")
            eesAvailable.set(value)
            showLoadLimitPrompt()
        }

        override fun onChargeGunStatus(value: Int) {
            Log.d(TAG, "onChargeGunStatus: $value")
            lifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                //插入放电枪，立即放电/立即充电
                if (value == CarNewEnergy.SlowGunConnectSts.V2L) {
                    hideComConfirmDialog()
                    showDischargeDialog()
                } else if (value == CarNewEnergy.SlowGunConnectSts.CHARGE_CONNECTED) {
                    hideDischargeDialog()
                    if (bookChargeStatus.get() == CarNewEnergy.BookChargeWaitingSts.WAITING_CHARGE) {
                        //预约充电等待状态
                        //当前SOC大于等于预约充电SOC，提示无法立即充电
                        val toastResId = if (mCurrentSOC.get() >= chargeStopSOC.get()) {
                            R.string.ne_unable_charge_immediately
                        } else {
                            0
                        }
                        showComConfirmDialog(
                            true,
                            R.string.ne_whether_charing_immediately,
                            ComConfirmDialog.DialogType.CANCEL_BOOK_CHARGING,
                            toastResId
                        )
                    }
                } else {
                    hideDischargeDialog()
                    hideComConfirmDialog()
                }
            }
        }


        override fun onBatteryLevel(value: Int) {
            Log.d(TAG, "onBatteryLevel: $value")
            mCurrentSOC.set(value)
            lifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                GlobalPopupWindowUtils.refreshChargingContent()
            }
            notifyWidget()
            //电池电量--通知3D车模
            notify3DBatteryPercent()
        }

        override fun onChargeSocThreshold(value: Int) {
            Log.d(TAG, "onChargeSocThreshold: $value")
            chargeStopSOC.set(value)
        }

        override fun onDischargeSocLowThreshold(value: Int) {
            Log.d(TAG, "onDischargeSocLowThreshold: $value")
            dischargeStopSOC.set(value)
        }

        override fun onBookChargeStatus(value: Int) {
            Log.d(TAG, "onBookChargeStatus: $value")
            bookChargeStatus.set(value)
        }

        override fun onBmsChargeSts(value: Int) {
            Log.d(TAG, "onBmsChargeSts: $value")
            chargingStatus.set(value == CarNewEnergy.FastChargingSts.PARKING_CHARGE)
            notifyNewEnergyUri()
            //充电状态-通知3D车模
            notify3DChargeMode()
        }

        override fun onDischargeSts(value: Int) {
            Log.d(TAG, "onDischargeSts: $value")
            dischargingStatus.set(value == CarNewEnergy.V2LFunctionSts.ON)
            notifyNewEnergyUri()
            //放电状态 -通知3D车模
            notify3DChargeMode()
        }

        override fun onExhibitionMod(value: Int) {
            Log.d(TAG, "onExhibitionMod: $value")
            exhibitionMod.set(value)
            notifyNewEnergyUri()
        }

        override fun onShowRoomReq(value: Int) {
            Log.d(TAG, "onShowRoomReq: $value")
            lifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                hideShowRoomDialog()
                when (value) {
                    CarNewEnergy.ShowRoomReq.ENTER -> {
                        // 显示进入展车模式弹窗
                        if (showRoomDialog == null || !showRoomDialog!!.isShowing) {
                            Log.d(TAG, "onShowRoomReq: 显示展车模式")
                            showShowRoomDialog(ComConfirmDialog.DialogType.ENTER_SHOWROOM)
                        }
                    }

                    CarNewEnergy.ShowRoomReq.EXIT -> {
                        // 显示退出展车模式弹窗
                        Log.d(TAG, "onShowRoomReq: 隐藏展车模式")
                        showShowRoomDialog(ComConfirmDialog.DialogType.EXIT_SHOWROOM)
                    }
                }
            }
        }

        override fun onEnduranceElectricCurrent(value: Int) {
            Log.d(TAG, "onEnduranceElectricCurrent: $value")
            mEnduranceElectric.set(value)
            notifyWidget()
        }

        override fun onRemainChgTime(value: Int) {
            //充电剩余时间（单位：分钟）-通知3D车模
            notify3DChargeRemainTime()
        }

        override fun onBmsPackCurrentDis(value: Float) {
            //BMS电流-通知3D车模
            notify3DChargePower()
        }

        override fun onBmsPackVoltageDis(value: Float) {
            //BMS电压-通知3D车模
            notify3DChargePower()
        }

        override fun onDischargePower(value: Float) {
            //当前放电功率-通知3D车模
            notify3DDischargePower()
        }

        override fun onDischargePowerLimit(value: Float) {
            //最大充电功率-通知3D车模
            notify3DDischargePower()
        }

    }

    /**
     * 4.1.10.2运输模式和极致节能模式下负载限制提示(5.1架构功能猫迷
     * 前置条件:a&b&(c|d)
     * a.电源处于COM/ON档,"FRZCU_PowerModeFLZCU_9_PoweriMode= 0x1:COM/0x2:ON
     * b.ICC收到APC限制等级为等级5"APC_Level_Limit=0x5:1Level5"(主机降低功耗)
     * c.极致节能模式为开启状态,主机需无条件发送周期性信号ICC_DA_115_ExtreEnergySaveMode=0x1:ON"信号
     * 发送给FLZCU,备注:若车型无极致节能模式需求,则无此条件
     * d.当车辆模式为运输模式"VCC_1_CarMode=0x2:Transport Mode
     * 触发条件:a
     * a.ICC收到负载限制中控提示"FLZCU_LbatipVehPwrMod=0x1:Warning
     * 执行输出:a
     * a.中控屏显示"进入节能状态,舒适娱乐等功能受限"
     *
     * ********低电量负载限制提示(5.1架构功能描述)
     * 前置条件:(a&b&c)
     * a.电源处于COM/ON档,"FRZCU_PowerModeFLZCU_9_PowerMode= 0x1:COM/0x2:ON
     * b.ICC收到APC限制等级为等级5"APC_Level_Limit=0x5:Level 5"(主机降低功耗)
     * c.车辆模式为用户模式"VCC_1_CarMode=0x0:Normal Mode"
     * 触发条件:a
     * a.收到负载限制中控提示"FLZCU_LbatipVehPwrMod=0x1:Warning信号
     * 执行输出:a
     * b.中控屏应当显示"蓄电池电量低,舒适娱乐功能进入节能模式我"
     */
    fun showLoadLimitPrompt() {
        lifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
            if ((powerMode.get() == CarNewEnergy.PowerMode.COMFORTABLE || powerMode.get() == CarNewEnergy.PowerMode.ON)
                && apcLevelLimit.get() == CarNewEnergy.ApcLevelLimit.LEVEL_5
                && (carMode.get() == CarNewEnergy.CarMode.TRANSPORT_MODE
                        || eesAvailable.get() == CarNewEnergy.EnergySavingAvailable.AVAILABLE)
                && lbatipVehPwrMod.get() == CarNewEnergy.LbatipVehPwrMod.WARNING
            ) {
                SettingsToast.showToast(R.string.ne_enter_energy_saving_state)
            } else if ((powerMode.get() == CarNewEnergy.PowerMode.COMFORTABLE || powerMode.get() == CarNewEnergy.PowerMode.ON)
                && apcLevelLimit.get() == CarNewEnergy.ApcLevelLimit.LEVEL_5
                && carMode.get() == CarNewEnergy.CarMode.NORMAL_MODE
                && lbatipVehPwrMod.get() == CarNewEnergy.LbatipVehPwrMod.WARNING
            ) {
                SettingsToast.showToast(R.string.ne_low_battery_level)
            }
        }
    }

    /**
     * 显示立即放电弹窗
     */
    private fun showDischargeDialog() {
        //电量不足
        if (mCurrentSOC.get() <= 30) {
            showComConfirmDialog(
                false,
                R.string.ne_discharge_error_low_battery,
                ComConfirmDialog.DialogType.LOW_BATTERY_DISCHARGE_ERR,
                0
            )
        } else {
            //立即充电
            chargeSOCDialog = ChargeSOCDialog(
                MyApplication.getContext(), false, mCurrentSOC.get(),
                chargeStopSOC.get(), dischargeStopSOC.get(), true
            )
            chargeSOCDialog?.show()
        }
    }

    private fun hideDischargeDialog() {
        chargeSOCDialog?.dismiss()
    }

    /**
     * 显示展车模式弹窗.
     */
    private fun showShowRoomDialog(dialogType: ComConfirmDialog.DialogType) {
        var resId = 0
        if (dialogType == ComConfirmDialog.DialogType.ENTER_SHOWROOM) {
            resId = R.string.ne_whether_open_show_room
        } else {
            resId = R.string.ne_whether_close_show_room
        }
        showComConfirmDialog(true, resId, dialogType, 0)
    }

    private fun hideShowRoomDialog() {
        showRoomDialog?.dismiss()
    }

    /**
     * 通用弹窗
     * A、显示立即放电弹窗
     * B、显示展车模式弹窗
     * C、显示预约充电等待弹窗
     * ----1、接收到TBOX发送的TBOX_BookChrgSts=0x1:WaitingCharge
     * ----2、接收到OBC_CC_ConnectSts=0x1:Charge Connected
     * ----3、⼤屏弹窗提⽰“当前处于预约充电等待，是否需要⽴即充电”供⽤⼾
     * ----确认，计时15s，超时未操作，按默认否执⾏（预约充电）；若选择是，则发送预约充电关闭（⽴即充
     * ----电）设置信号：ICC_BookChrgSetReq：0x2:CancellBook
     */
    private fun showComConfirmDialog(
        hasCancel: Boolean,
        contentResId: Int,
        dialogType: ComConfirmDialog.DialogType,
        toastResId: Int
    ) {
        comConfirmDialog = ComConfirmDialog(
            MyApplication.getContext(),
            hasCancel,
            MyApplication.getContext().getString(contentResId),
            dialogType,
            toastResId
        )
        comConfirmDialog?.show()
    }

    private fun hideComConfirmDialog() {
        comConfirmDialog?.dismiss()
    }

    private fun notifyNewEnergyUri() {
        Log.d(TAG, "notifyNewEnergyUri: ")
        (lifecycleOwner as Context).contentResolver.notifyChange(
            NewEnergyContentProvider.NEW_ENERGY_URI, null
        )
    }

    private fun notifyWidget() {
        Log.d(TAG, "notifyWidget: ")
        val context = lifecycleOwner as Context
        context.sendBroadcast(
            Intent(NewEnergyWidget.ACTION_NEW_ENERGY_WIDGET_REFRESH)
                .setClassName(
                    context, NewEnergyWidget::class.java.name
                ).apply { setPackage(context.packageName) })
    }

    private fun notify3DChargeMode() {
        var status = Car3DModel.ChargeMode.NONE
        if (chargingStatus.get()) status = Car3DModel.ChargeMode.CHARGING
        else if (dischargingStatus.get()) status = Car3DModel.ChargeMode.DISCHARGING
        ThreeDModelUtil.setChargeMode(status, null)
    }

    private fun notify3DBatteryPercent() {
        ThreeDModelUtil.setBatteryState(mCurrentSOC.get(), null)
    }

    private fun notify3DChargePower() {
        val powerText = NewEnergyFragment.getPowerText(chargeCurrent.get(), chargeVoltage.get())
        val bmsCurrentText = NewEnergyFragment.getBmsCurrentText(chargeCurrent.get())
        val bmsVoltageText = NewEnergyFragment.getBmsVoltageText(chargeVoltage.get())

        ThreeDModelUtil.setChargeState(powerText, bmsCurrentText, bmsVoltageText, null)
    }

    private fun notify3DDischargePower() {
        val dischargingPowerText =
            NewEnergyFragment.getDischargingPowerText(dischargePowerCurrent.get())
        val dischargingPowerMaxText =
            NewEnergyFragment.getDischargingPowerMaxText(dischargePowerMax.get())

        ThreeDModelUtil.setDischargeState(dischargingPowerMaxText, dischargingPowerText, null)
    }

    private fun notify3DChargeRemainTime() {
        ThreeDModelUtil.setChargeRemainTime(chargeRemainTime.get(), null)
    }

    companion object {
        const val TAG = "NewEnergyLifeCycle"
        val mCurrentSOC = AtomicInteger()
        val mEnduranceElectric = AtomicInteger()
        val chargingStatus = AtomicBoolean()
        val dischargingStatus = AtomicBoolean()
        val exhibitionMod = AtomicInteger()

        fun showThermalRunawayDis(signalVal: Int) {
            if (signalVal == 1) {
                GlobalPopupWindowUtils.showThermalRunawayView()
            } else {
                GlobalPopupWindowUtils.hideThermalRunawayView()
            }
        }

        fun showChargingView(isShow: Boolean) {
            if (isShow) {
                GlobalPopupWindowUtils.showChargingView()
            } else {
                GlobalPopupWindowUtils.hideChargingView()
            }
        }

    }

}