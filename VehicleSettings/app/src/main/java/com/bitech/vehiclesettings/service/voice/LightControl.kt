package com.bitech.vehiclesettings.service.voice

import android.content.Context
import android.database.ContentObserver
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.constants.CarLight
import com.bitech.platformlib.interfaces.config.handler.ConfigManagerHandler
import com.bitech.platformlib.manager.ConfigManager
import com.bitech.platformlib.manager.LightManager
import com.bitech.platformlib.manager.NewEnergyManager
import com.bitech.platformlib.utils.AtmoLightConst
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.fragment.LightBaseFragment
import com.bitech.vehiclesettings.presenter.light.LightInPresenter
import com.bitech.vehiclesettings.presenter.light.LightOutPresenter
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.DialogNavigationUtils
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P

class LightControl() {
    private var lightInPresenter: LightInPresenter? = LightInPresenter(MyApplication.getContext())
    private var carServer: LightManager = BitechCar.getInstance()
        .getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER) as LightManager
    private var mConfigManager: ConfigManager? = ConfigManager.getInstance()
    private val context: Context = MyApplication.getInstance()
    private val lightBaseFragment: LightBaseFragment = LightBaseFragment()
    private val mLightOutPresenter: LightOutPresenter =
        LightOutPresenter(MyApplication.getContext())
    private var settingsObserver: ContentObserver? = null

    private var _lightSw: Int = 0
    private var lightSw: Int
        get() = _lightSw
        set(value) {
            if (_lightSw != value) {
                _lightSw = value
                onLightSwChanged(value)
            }
        }

    private fun registerSettingsObserver() {
        settingsObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
            override fun onChange(selfChange: Boolean) {
                super.onChange(selfChange)
                // 系统设置发生变化时更新内部值
                updateLightSw()
            }
        }

        // 注册监听器
        context.contentResolver.registerContentObserver(
            Settings.Global.getUriFor(PrefsConst.GlobalValue.L_LIGHT_SW),
            false,
            settingsObserver!!
        )
    }

    fun updateLightSw() {
        lightInPresenter?.getLightSw()?.let {
            lightSw = it  // 这会自动触发监听器（如果值发生变化）
        }
    }

    private fun onLightSwChanged(newValue: Int) {
        Log.d("LightControl", "LightSw changed to: $newValue")

        fun updateLightSw() {
            lightInPresenter?.getLightSw()?.let {
                lightSw = it  // 这会自动触发监听器（如果值发生变化）
            }
        }
    }

    private fun unregisterSettingsObserver() {
        settingsObserver?.let {
            context.contentResolver.unregisterContentObserver(it)
        }
    }

    // 在适当的时候调用注册和注销方法
    fun startMonitoring() {
        registerSettingsObserver()
    }

    fun stopMonitoring() {
        unregisterSettingsObserver()
    }
    fun getRandomNumber(): Int {
        val numbers = listOf(0, 1, 2, 3)
        return numbers.random()
    }

    object LightConstat {
        const val CONFIG_SUPPORTED: Int = 1
    }

    private val configManagerHandler: ConfigManagerHandler = ConfigManagerHandler()
    private var mNewEnergyManager: NewEnergyManager? = NewEnergyManager.getInstance();
    private fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
    }

    private fun sendResultCode(respondId: String, mValue: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    //特殊提示语id
    private fun sendResultCode(respondId: String, mValue: String, mUnique: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        param.unique = mUnique
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
    }

    /**
     * 氛围灯xx位置开关
     * @param flag
     */
    fun setdfLight(flag: Boolean, position: String) {
//        val inLight = lightInPresenter?.getLightSw()
        val inLight = Settings.Global.getInt(
            MyApplication.getContext().contentResolver,
            PrefsConst.GlobalValue.L_LIGHT_SW,
            0 // 默认值，如果获取不到则返回0
        )
//        val inLight = PrefsConst.GlobalValue.L_LIGHT_SW
        val eco = mNewEnergyManager?.getEesModeAvailable()
        val showCar = mNewEnergyManager?.getExhibitionMod()
        var disposition = configManagerHandler?.getInteriorAtmosphereLightConfig()
        var dispositionex = configManagerHandler?.getBackAtmosphereLightConfig()
        if (flag) {
            if (disposition == CarSettingConstant.InLight_Config_NotExist) {
                sendResultCode(VDVRRespondID.open_atmosphere_lamp_3)
            } else {
                if (eco == CarSettingConstant.Ultra_Power_Saving_Mode_Exist) {
                    sendResultCode(VDVRRespondID.open_atmosphere_lamp_4)
                } else {
                    when (position) {
                        "F" -> {
                            updateLightSw()
                            if (inLight == CarSettingConstant.InLight_OFF) {
                                Log.d("sendResultCode", "sendResultCode:开启前氛围灯")
                                lightInPresenter!!.setAmbLightSw(1, CommonConst.LIGHT_SW_FRONT)
                                sendResultCode(
                                    VDVRRespondID.open_qian_atmosphere_front_1,
                                    "前排"
                                )
                            } else {
                                Log.d("sendResultCode", "sendResultCode:前氛围灯已开启")
                                sendResultCode(
                                    VDVRRespondID.open_qian_atmosphere_front_3,
                                    "前排"
                                )
                            }
                        }

                        "B" -> {
                            updateLightSw()
                            if (dispositionex == CarSettingConstant.InLight_Partition_Control_NotExist) {
//                                sendResultCode(VDVRRespondID.open_other_atmosphere_wrong_1)
                            } else {
                                if (inLight == CarSettingConstant.InLight_OFF) {
                                    Log.d("sendResultCode", "sendResultCode:开启后氛围灯")
                                    lightInPresenter!!.setAmbLightSw(
                                        1,
                                        CommonConst.LIGHT_SW_REAR
                                    )
                                    sendResultCode(
                                        VDVRRespondID.open_hou_atmosphere_front_1,
                                        "后排"
                                    )
                                } else {
                                    Log.d("sendResultCode", "sendResultCode:后氛围灯已开启")
                                    sendResultCode(
                                        VDVRRespondID.open_hou_atmosphere_front_3,
                                        "后排"
                                    )
                                }
                            }
                        }

                        "ALL" -> {
                            updateLightSw()
                            if (inLight == CarSettingConstant.InLight_OFF) {
                                Log.d("sendResultCode", "sendResultCode:开启氛围灯")
                                lightInPresenter!!.setAmbLightSw(1, CommonConst.LIGHT_SW_ALL)
                                sendResultCode(
                                    VDVRRespondID.open_all_atmosphere_front_1,
                                    "全车"
                                )
                            } else {
                                Log.d("sendResultCode", "sendResultCode:氛围灯已开启")
                                sendResultCode(
                                    VDVRRespondID.open_all_atmosphere_front_3,
                                    "全车"
                                )
                            }
                        }

                        "" -> {
                            updateLightSw()
                            if (inLight == CarSettingConstant.InLight_OFF) {
                                Log.d("sendResultCode", "sendResultCode:开启氛围灯")
                                lightInPresenter!!.setAmbLightSw(1, CommonConst.LIGHT_SW_ALL)
                                sendResultCode(VDVRRespondID.open_atmosphere_lamp_1)
                            } else {
                                Log.d("sendResultCode", "sendResultCode:氛围灯已开启")
                                sendResultCode(VDVRRespondID.open_atmosphere_lamp_2)
                            }
                        }
                    }
                }
            }
        } else {
            if (disposition == CarSettingConstant.InLight_Config_NotExist) {
                sendResultCode(VDVRRespondID.close_atmosphere_lamp_3)
            } else {
                if (showCar == CarSettingConstant.Display_Car_Mode_Open) {
                    sendResultCode(VDVRRespondID.close_atmosphere_lamp_4)
                } else {
                    when (position) {
                        "F" -> {
                            updateLightSw()
                            if (inLight == CarSettingConstant.InLight_ON) {
                                Log.d("sendResultCode", "sendResultCode:关闭前氛围灯")
                                lightInPresenter!!.setAmbLightSw(
                                    CarSettingConstant.InLight_ON,
                                    CommonConst.LIGHT_SW_FRONT
                                )
                                sendResultCode(
                                    VDVRRespondID.close_qian_atmosphere_front_1,
                                    "前排"
                                )
                            } else {
                                Log.d("sendResultCode", "sendResultCode:前氛围灯已关闭")
                                sendResultCode(
                                    VDVRRespondID.close_qian_atmosphere_front_3,
                                    "前排"
                                )
                            }
                        }

                        "B" -> {
                            updateLightSw()
                            if (dispositionex == CarSettingConstant.InLight_Config_NotExist) {
//                                    sendResultCode(VDVRRespondID.close_other_atmosphere_front_4)
                            } else {
                                if (inLight == CarSettingConstant.InLight_ON) {
                                    Log.d("sendResultCode", "sendResultCode:关闭后氛围灯")
                                    lightInPresenter!!.setAmbLightSw(
                                        CarSettingConstant.InLight_OFF,
                                        CommonConst.LIGHT_SW_REAR
                                    )
                                    sendResultCode(
                                        VDVRRespondID.close_hou_atmosphere_front_1,
                                        "后排"
                                    )
                                } else {
                                    Log.d("sendResultCode", "sendResultCode:后氛围灯已关闭")
                                    sendResultCode(
                                        VDVRRespondID.close_hou_atmosphere_front_3,
                                        "后排"
                                    )
                                }
                            }
                        }

                        "ALL" -> {
                            updateLightSw()
                            if (inLight == CarSettingConstant.InLight_ON) {
                                Log.d("sendResultCode", "sendResultCode:关闭氛围灯")
                                lightInPresenter!!.setAmbLightSw(
                                    CarSettingConstant.InLight_OFF,
                                    CommonConst.LIGHT_SW_ALL
                                )
                                sendResultCode(
                                    VDVRRespondID.close_all_atmosphere_front_1,
                                    "全车"
                                )
                            } else {
                                Log.d("sendResultCode", "sendResultCode:氛围灯已关闭")
                                sendResultCode(
                                    VDVRRespondID.close_all_atmosphere_front_3,
                                    "全车"
                                )
                            }
                        }

                        "" -> {
                            updateLightSw()
                            if (inLight == CarSettingConstant.InLight_ON) {
                                lightInPresenter?.setAmbLightSw(
                                    CarSettingConstant.InLight_OFF,
                                    CommonConst.LIGHT_SW_ALL
                                )
                                Log.d("sendResultCode", "sendResultCode:关闭氛围灯")
                                sendResultCode(VDVRRespondID.close_atmosphere_lamp_2)
                            } else {
                                Log.d("sendResultCode", "sendResultCode:氛围灯已关闭")
                                sendResultCode(VDVRRespondID.close_atmosphere_lamp_1)
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 氛围灯分区控制
     * @param flag
     */
    fun setAmbientLightZoneControl(flag: Boolean) {
        var them = lightInPresenter?.getThemeMode()
        var inOrOut = Prefs.get(PrefsConst.SELECT_TAB, 0);
        var disposition = configManagerHandler?.getInteriorAtmosphereLightConfig()
        var dispositionex = configManagerHandler?.getBackAtmosphereLightConfig()
        disposition = 1
        dispositionex = 1
        if (flag) {
            if (disposition == CarSettingConstant.InLight_Config_NotExist) {
//                sendResultCode(VDVRRespondID.close_atmosphere_zone_4)
            } else {
                if (dispositionex == CarSettingConstant.InLight_Partition_Control_NotExist) {
                    sendResultCode(VDVRRespondID.open_atmosphere_zone_3)
                } else {
                    if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == 0 && them == 1) {
                        sendResultCode(VDVRRespondID.open_atmosphere_zone_2)
                    } else {
                        DialogNavigationUtils.launchMainActivity(
                            context,
                            MainActivity.MainTabIndex.LIGHT,
                            LightBaseFragment.LIGHT_SETECT_IN,
                            CommonConst.DIALOG_OPEN
                        )
                        lightBaseFragment.selectTab(CarSettingConstant.Car_Light_Type_Inside)
                        sendResultCode(VDVRRespondID.open_atmosphere_zone_1)
                    }
                }
            }
        } else {
            if (disposition == CarSettingConstant.InLight_Config_NotExist) {
//                sendResultCode(VDVRRespondID.close_atmosphere_zone_4)
            } else {
                if (dispositionex == CarSettingConstant.InLight_Partition_Control_NotExist) {
                    sendResultCode(VDVRRespondID.close_atmosphere_zone_3)
                } else {
                    if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == CarSettingConstant.Car_Light_Type_Inside && them == CarSettingConstant.Theme_Mode_Customize) {
                        DialogNavigationUtils.launchMainActivity(
                            context,
                            MainActivity.MainTabIndex.LIGHT,
                            LightBaseFragment.LIGHT_SETECT_IN,
                            CommonConst.DIALOG_CLOSE
                        )
                        sendResultCode(VDVRRespondID.close_atmosphere_zone_1)
                    } else {
                        sendResultCode(VDVRRespondID.close_atmosphere_zone_2)
                    }
                }
            }
        }
    }

    /**氛围灯效果切换
     * @param value
     */
    fun setInLightEffect(value: String) {
        var inLight = lightInPresenter?.getLightEffect()
        var inLightsw = lightInPresenter?.getLightSw()
        var disposition = configManagerHandler?.getInteriorAtmosphereLightConfig()
        disposition = 1

        if (disposition == CarSettingConstant.InLight_Config_NotExist) {
            sendResultCode(VDVRRespondID.switch_atmosphere_effect_3)
        } else {
            when (value) {
                CarSettingConstant.InLight_Static.toString() -> {
                    if (inLightsw == CarSettingConstant.InLight_ON) {
                        if (inLight == CarSettingConstant.InLight_Static) {
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_2, "静态")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:静态")
                            lightInPresenter?.setLightEffect(CarSettingConstant.InLight_Static)
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_1, "静态")
                        }
                    } else {
                        if (inLight == CarSettingConstant.InLight_Static) {
                            lightInPresenter!!.setAmbLightSw(
                                CarSettingConstant.InLight_ON,
                                CommonConst.LIGHT_SW_ALL
                            )
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_4, "静态")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:静态")
                            lightInPresenter!!.setAmbLightSw(
                                CarSettingConstant.InLight_ON,
                                CommonConst.LIGHT_SW_ALL
                            )
                            lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Static)
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_1, "静态")
                        }
                    }
                }

                CarSettingConstant.InLight_Breathe.toString() -> {
                    if (inLightsw == CarSettingConstant.InLight_ON) {
                        if (inLight == CarSettingConstant.InLight_Breathe) {
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_2, "呼吸")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:呼吸")
                            lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Breathe)
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_1, "呼吸")
                        }
                    } else {
                        if (inLight == CarSettingConstant.InLight_Breathe) {
                            lightInPresenter!!.setAmbLightSw(
                                CarSettingConstant.InLight_ON,
                                CommonConst.LIGHT_SW_ALL
                            )
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_4, "呼吸")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:呼吸")
                            lightInPresenter!!.setAmbLightSw(
                                CarSettingConstant.InLight_ON,
                                CommonConst.LIGHT_SW_ALL
                            )
                            lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Breathe)
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_1, "呼吸")
                        }
                    }
                }

                CarSettingConstant.InLight_Gradient.toString() -> {
                    if (inLightsw == CarSettingConstant.InLight_ON) {
                        if (inLight == CarSettingConstant.InLight_Gradient) {
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_2, "渐变")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:渐变")
                            lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Gradient)
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_1, "渐变")
                        }
                    } else {
                        if (inLight == CarSettingConstant.InLight_Gradient) {
                            lightInPresenter!!.setAmbLightSw(
                                CarSettingConstant.InLight_ON,
                                CommonConst.LIGHT_SW_ALL
                            )
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_4, "渐变")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:渐变")
                            lightInPresenter!!.setAmbLightSw(
                                CarSettingConstant.InLight_ON,
                                CommonConst.LIGHT_SW_ALL
                            )
                            lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Gradient)
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_1, "渐变")
                        }
                    }
                }

                CarSettingConstant.InLight_MusicRhythm.toString() -> {
                    if (inLightsw == CarSettingConstant.InLight_ON) {
                        if (inLight == CarSettingConstant.InLight_MusicRhythm) {
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_2, "音乐律动")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:音乐律动")
                            lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_MusicRhythm)
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_1, "音乐律动")
                        }
                    } else {
                        if (inLight == CarSettingConstant.InLight_MusicRhythm) {
                            lightInPresenter!!.setAmbLightSw(
                                CarSettingConstant.InLight_ON,
                                CommonConst.LIGHT_SW_ALL
                            )
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_4, "音乐律动")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:音乐律动")
                            lightInPresenter!!.setAmbLightSw(
                                CarSettingConstant.InLight_ON,
                                CommonConst.LIGHT_SW_ALL
                            )
                            lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_MusicRhythm)
                            sendResultCode(VDVRRespondID.switch_atmosphere_effect_1, "音乐律动")
                        }
                    }
                }
            }
        }
    }

    /**设置音乐律动
     * @param flag
     */
    fun setMusicLightControl(flag: Boolean) {
        var lightsw = lightInPresenter?.getLightSw()
        var eff = lightInPresenter?.getLightEffect()

        if (flag) {
            if (eff == CarSettingConstant.InLight_MusicRhythm) {
//                音乐律动已打开
                sendResultCode(VDVRRespondID.open_atmosphere_lamp_music_rhythm_2)
            } else {
                if (lightsw == CarSettingConstant.InLight_ON) {
                    lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_MusicRhythm)
                    sendResultCode(VDVRRespondID.open_atmosphere_lamp_music_rhythm_1)
                } else {
                    lightInPresenter!!.setAmbLightSw(
                        CarSettingConstant.InLight_ON,
                        CommonConst.LIGHT_SW_ALL
                    )
                    lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_MusicRhythm)
                    sendResultCode(VDVRRespondID.open_atmosphere_lamp_music_rhythm_3)
                }
            }
        } else {
            if (eff == CarSettingConstant.InLight_MusicRhythm) {
                lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Static)
                sendResultCode(VDVRRespondID.close_atmosphere_lamp_music_rhythm_1)
            } else {
                if (lightsw == CarSettingConstant.InLight_ON) {
                    lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Static)
                    sendResultCode(VDVRRespondID.close_atmosphere_lamp_music_rhythm_2)
                } else {
                    sendResultCode(VDVRRespondID.close_atmosphere_lamp_music_rhythm_3)
                }
            }
        }
    }

    /**动态氛围灯律动模式
     * @param value
     */
    fun setDynamicInLight(value: String) {
        var lightsw = lightInPresenter?.getLightSw()
        var eff = lightInPresenter?.getLightEffect()

        if (eff == CarSettingConstant.InLight_MusicRhythm) {
            sendResultCode(VDVRRespondID.dynamic_atmosphere_lamp_music_rhythm_mode_2, "音乐律动")
        } else {
            if (lightsw == CarSettingConstant.InLight_ON) {
                lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_MusicRhythm)
                sendResultCode(
                    VDVRRespondID.dynamic_atmosphere_lamp_music_rhythm_mode_1,
                    "音乐律动"
                )
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                sendResultCode(
                    VDVRRespondID.dynamic_atmosphere_lamp_music_rhythm_mode_3,
                    "音乐律动"
                )
            }
        }
    }

    /**无指定模式
     * @param
     */
    fun setNullMode() {
        var lightsw = lightInPresenter?.getLightSw()
        var disposition = configManagerHandler?.getInteriorAtmosphereLightConfig()
        disposition = 1

        if (disposition == CarSettingConstant.InLight_Config_NotExist) {
            sendResultCode(VDVRRespondID.switch_atmosphere_effect_3)
        } else {
            if (lightsw == CarSettingConstant.InLight_ON) {
                lightInPresenter!!.setLightEffect(getRandomNumber())
                //todo  发送随机数
                sendResultCode(VDVRRespondID.switch_atmosphere_lamp_mode_3)
            } else {
                lightInPresenter!!.setAmbLightSw(1, CommonConst.LIGHT_SW_ALL)
                lightInPresenter!!.setLightEffect(getRandomNumber())
                //todo  发送随机数
                sendResultCode(VDVRRespondID.switch_atmosphere_lamp_mode_2)
            }
        }
    }

    fun getRandomNumberZ(): Int {
        val numbers = listOf(1, 2, 3)
        return numbers.random()
    }

    /**动态氛围灯模式
     * @param flag
     */
    fun setDyInLight(flag: Boolean) {
        //todo 配置字
        if (flag) {
            if (AtmoLightConst.DefaultValue.L_LIGHT_SW == CarSettingConstant.InLight_ON) {
                if (lightInPresenter?.lightEffect == CarSettingConstant.InLight_Static) {
                    lightInPresenter!!.setLightEffect(getRandomNumberZ())
                    sendResultCode(VDVRRespondID.open_dynamic_atmosphere_lamp_1)
                } else {
                    sendResultCode(VDVRRespondID.open_dynamic_atmosphere_lamp_2)
                }
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.setLightEffect(getRandomNumberZ())
                sendResultCode(VDVRRespondID.open_dynamic_atmosphere_lamp_3)
            }
        } else {
            if (lightInPresenter?.lightSw == CarSettingConstant.InLight_ON) {
                if (lightInPresenter?.lightEffect == CarSettingConstant.InLight_Static) {
                    sendResultCode(VDVRRespondID.close_dynamic_atmosphere_lamp_1)
                } else {
                    lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Static)
                    sendResultCode(VDVRRespondID.close_dynamic_atmosphere_lamp_2)
                }
            } else {
                sendResultCode(VDVRRespondID.close_dynamic_atmosphere_lamp_3)
            }
        }
    }

    /**设置氛围灯颜色
     * @param value
     */
    fun setcolorInLight(value: String) {
        val presenter = lightInPresenter
        var lightsw = lightInPresenter?.getLightSw()
        var color = lightInPresenter?.getCurColorLin()
        var them = lightInPresenter?.getThemeMode()
        var eff = lightInPresenter?.getLightEffect()
        var disposition = configManagerHandler?.getInteriorAtmosphereLightConfig()
        disposition = 1

        if (disposition == CarSettingConstant.InLight_Config_NotExist) {
            sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_color_5)
        } else {
            if (presenter == null) {
                Log.e("LightControl", "Attempted to use uninitialized presenter")
                return
            }
            if (them == CarSettingConstant.Theme_Mode_Customize) {
                if (value.toInt() == CarSettingConstant.InLight_Red || value.toInt() == CarSettingConstant.InLight_Orange || value.toInt() == CarSettingConstant.InLight_Yellow || value.toInt() == CarSettingConstant.InLight_Green || value.toInt() == CarSettingConstant.InLight_Cyan || value.toInt() == CarSettingConstant.InLight_Blue || value.toInt() == CarSettingConstant.InLight_Purple) {
                    if (lightsw == CarSettingConstant.InLight_ON) {
                        if (eff == CarSettingConstant.InLight_Static || lightInPresenter?.lightEffect == CarSettingConstant.InLight_Breathe) {
                            if (value.toInt() == color) {
                                if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                                    VDVRRespondID.set_specific_atmosphere_lamp_color_2,
                                    "红色"
                                )
                                else if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                                    VDVRRespondID.set_specific_atmosphere_lamp_color_2,
                                    "橙色"
                                )
                                else if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                                    VDVRRespondID.set_specific_atmosphere_lamp_color_2,
                                    "黄色"
                                )
                                else if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                                    VDVRRespondID.set_specific_atmosphere_lamp_color_2,
                                    "绿色"
                                )
                                else if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                                    VDVRRespondID.set_specific_atmosphere_lamp_color_2,
                                    "青色"
                                )
                                else if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                                    VDVRRespondID.set_specific_atmosphere_lamp_color_2,
                                    "蓝色"
                                )
                                else if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                                    VDVRRespondID.set_specific_atmosphere_lamp_color_2,
                                    "紫色"
                                )
                                else {
                                    DialogNavigationUtils.launchMainActivity(
                                        context,
                                        MainActivity.MainTabIndex.LIGHT,
                                        LightBaseFragment.LIGHT_SETECT_IN,
                                        CommonConst.DIALOG_OPEN
                                    )
                                    sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_color_3)
                                }
                            } else {
                                lightInPresenter!!.changeColorLin(value.toInt())
                                sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_color_1)
                            }
                        }
                        if (eff == CarSettingConstant.InLight_Gradient || eff == CarSettingConstant.InLight_MusicRhythm) {
                            sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_color_7)
                        }
                    } else {
                        lightInPresenter!!.setAmbLightSw(
                            CarSettingConstant.InLight_ON,
                            CommonConst.LIGHT_SW_ALL
                        )
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeColorLin(value.toInt())
                        if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                            VDVRRespondID.set_specific_atmosphere_lamp_color_6,
                            "红色"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                            VDVRRespondID.set_specific_atmosphere_lamp_color_6,
                            "橙色"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                            VDVRRespondID.set_specific_atmosphere_lamp_color_6,
                            "黄色"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                            VDVRRespondID.set_specific_atmosphere_lamp_color_6,
                            "绿色"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                            VDVRRespondID.set_specific_atmosphere_lamp_color_6,
                            "青色"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                            VDVRRespondID.set_specific_atmosphere_lamp_color_6,
                            "蓝色"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                            VDVRRespondID.set_specific_atmosphere_lamp_color_6,
                            "紫色"
                        )
                    }
                } else {
                    lightInPresenter!!.chgThemeMode(CarSettingConstant.Theme_Mode_Theme)
                    lightInPresenter!!.changeColorLin(value.toInt())
                    if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_4,
                        "红色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_4,
                        "橙色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_4,
                        "黄色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_4,
                        "绿色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_4,
                        "青色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_4,
                        "蓝色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_4,
                        "紫色"
                    )
                }
            } else {
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (eff == CarSettingConstant.InLight_Static || eff == CarSettingConstant.InLight_Breathe) {
                        if (color == value.toInt()) {
                            if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                                VDVRRespondID.set_specific_atmosphere_lamp_color_8,
                                "红色"
                            )
                            if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                                VDVRRespondID.set_specific_atmosphere_lamp_color_8,
                                "橙色"
                            )
                            if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                                VDVRRespondID.set_specific_atmosphere_lamp_color_8,
                                "黄色"
                            )
                            if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                                VDVRRespondID.set_specific_atmosphere_lamp_color_8,
                                "绿色"
                            )
                            if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                                VDVRRespondID.set_specific_atmosphere_lamp_color_8,
                                "青色"
                            )
                            if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                                VDVRRespondID.set_specific_atmosphere_lamp_color_8,
                                "蓝色"
                            )
                            if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                                VDVRRespondID.set_specific_atmosphere_lamp_color_8,
                                "紫色"
                            )
                        } else {
                            lightInPresenter!!.changeColorLin(value.toInt())
                            sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_color_9)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_color_10)
                    }
                } else {
                    lightInPresenter!!.setAmbLightSw(
                        CarSettingConstant.InLight_ON,
                        CommonConst.LIGHT_SW_ALL
                    )
                    lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                    lightInPresenter!!.changeColorLin(value.toInt())
                    if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_11,
                        "红色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_11,
                        "橙色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_11,
                        "黄色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_11,
                        "绿色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_11,
                        "青色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_11,
                        "蓝色"
                    )
                    if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                        VDVRRespondID.set_specific_atmosphere_lamp_color_11,
                        "紫色"
                    )
                }
            }
            if (lightsw == CarSettingConstant.InLight_ON) {
                if (value.toInt() > 11) {
                    sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_color_3)
                }
            }
        }
    }

    /**随机色
     * @param
     */
    fun setRandomColor() {
        val numbers1 = listOf(0, 1, 2, 3, 4, 5, 6)
        val numbers2 = listOf(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)
        var lightsw = lightInPresenter?.getLightSw()
        var them = lightInPresenter?.getThemeMode()
        var eff = lightInPresenter?.getLightEffect()

        if (them == CarSettingConstant.Theme_Mode_Customize) {
            if (lightsw == CarSettingConstant.InLight_ON) {
                if (eff == CarSettingConstant.InLight_Static || eff == CarSettingConstant.InLight_Breathe) {
                    var number = numbers1.random()
                    lightInPresenter!!.changeColorLin(number)
                    if (number == CarSettingConstant.InLight_Red) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_1,
                        "红色"
                    )
                    if (number == CarSettingConstant.InLight_Orange) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_1,
                        "橙色"
                    )
                    if (number == CarSettingConstant.InLight_Yellow) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_1,
                        "黄色"
                    )
                    if (number == CarSettingConstant.InLight_Green) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_1,
                        "绿色"
                    )
                    if (number == CarSettingConstant.InLight_Cyan) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_1,
                        "青色"
                    )
                    if (number == CarSettingConstant.InLight_Blue) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_1,
                        "蓝色"
                    )
                    if (number == CarSettingConstant.InLight_Purple) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_1,
                        "紫色"
                    )
                } else {
                    sendResultCode(VDVRRespondID.switch_single_color_atmosphere_lamp_2)
                }
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                var number = numbers1.random()
                lightInPresenter!!.changeColorLin(number)
                if (number == CarSettingConstant.InLight_Red) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_3,
                    "红色"
                )
                if (number == CarSettingConstant.InLight_Orange) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_3,
                    "橙色"
                )
                if (number == CarSettingConstant.InLight_Yellow) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_3,
                    "黄色"
                )
                if (number == CarSettingConstant.InLight_Green) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_3,
                    "绿色"
                )
                if (number == CarSettingConstant.InLight_Cyan) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_3,
                    "青色"
                )
                if (number == CarSettingConstant.InLight_Blue) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_3,
                    "蓝色"
                )
                if (number == CarSettingConstant.InLight_Purple) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_3,
                    "紫色"
                )
            }
        } else {
            if (lightsw == CarSettingConstant.InLight_ON) {
                if (eff == CarSettingConstant.InLight_Static || eff == CarSettingConstant.InLight_Breathe) {
                    var number = numbers1.random()
                    lightInPresenter!!.changeColorLin(number)
                    if (number == CarSettingConstant.InLight_Red) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_4,
                        "红色"
                    )
                    if (number == CarSettingConstant.InLight_Orange) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_4,
                        "橙色"
                    )
                    if (number == CarSettingConstant.InLight_Yellow) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_4,
                        "黄色"
                    )
                    if (number == CarSettingConstant.InLight_Green) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_4,
                        "绿色"
                    )
                    if (number == CarSettingConstant.InLight_Cyan) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_4,
                        "青色"
                    )
                    if (number == CarSettingConstant.InLight_Blue) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_4,
                        "蓝色"
                    )
                    if (number == CarSettingConstant.InLight_Purple) sendResultCode(
                        VDVRRespondID.switch_single_color_atmosphere_lamp_4,
                        "紫色"
                    )
                } else {
                    sendResultCode(VDVRRespondID.switch_single_color_atmosphere_lamp_5)
                }
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                var number = numbers1.random()
                lightInPresenter!!.changeColorLin(number)
                if (number == CarSettingConstant.InLight_Red) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_6,
                    "红色"
                )
                if (number == CarSettingConstant.InLight_Orange) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_6,
                    "橙色"
                )
                if (number == CarSettingConstant.InLight_Yellow) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_6,
                    "黄色"
                )
                if (number == CarSettingConstant.InLight_Green) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_6,
                    "绿色"
                )
                if (number == CarSettingConstant.InLight_Cyan) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_6,
                    "青色"
                )
                if (number == CarSettingConstant.InLight_Blue) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_6,
                    "蓝色"
                )
                if (number == CarSettingConstant.InLight_Purple) sendResultCode(
                    VDVRRespondID.switch_single_color_atmosphere_lamp_6,
                    "紫色"
                )
            }
        }
    }

    /**无指定颜色
     *
     */

    fun setNodesignationColor() {
        val numbers1 = listOf(0, 1, 2, 3, 4, 5, 6)
        val numbers2 = listOf(7, 8, 9, 10, 11)
        if (lightInPresenter?.themeMode == 1) {
            if (lightInPresenter?.lightSw == 1) {
                if (lightInPresenter?.lightEffect == CarSettingConstant.InLight_Static || lightInPresenter?.lightEffect == CarSettingConstant.InLight_Breathe) {
                    lightInPresenter!!.changeColorLin(numbers1.random())
                    sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_zhuti_1)
                } else {
                    sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_zhuti_3)
                }
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                lightInPresenter!!.changeColorLin(numbers1.random())
                sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_zhuti_4)
            }
        } else {
            if (lightInPresenter?.lightSw == CarSettingConstant.InLight_ON) {
//                lightInPresenter.themeMode=1
                lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                lightInPresenter!!.changeColorLin(numbers1.random())
                sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_zhuti_5)
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
//                lightInPresenter.themeMode=1
                lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                lightInPresenter!!.changeColorLin(numbers1.random())
                sendResultCode(VDVRRespondID.set_specific_atmosphere_lamp_zhuti_6)
            }
        }
    }

    /**无指定颜色
     * @param value
     */
    fun setNodesignationColor2(value: String) {
        val numbers1 = listOf(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)
        val numbers2 = listOf(7, 8, 9, 10, 11)
        var lightsw = lightInPresenter?.getLightSw()
        var color = lightInPresenter?.getCurColorLin()

        if (lightsw == CarSettingConstant.InLight_ON) {
            if (color == CarSettingConstant.COLOR_RED || color == CarSettingConstant.InLight_Orange || color == CarSettingConstant.InLight_Yellow || color == CarSettingConstant.COLOR_GREEN || color == CarSettingConstant.InLight_Cyan || color == CarSettingConstant.InLight_Blue || color == CarSettingConstant.InLight_Purple) {
                var number = numbers1.random()
                lightInPresenter!!.changeColorLin(number)
                if (number == CarSettingConstant.InLight_Red) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "红色"
                )
                if (number == CarSettingConstant.InLight_Orange) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "橙色"
                )
                if (number == CarSettingConstant.InLight_Yellow) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "黄色"
                )
                if (number == CarSettingConstant.InLight_Green) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "绿色"
                )
                if (number == CarSettingConstant.InLight_Cyan) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "青色"
                )
                if (number == CarSettingConstant.InLight_Blue) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "蓝色"
                )
                if (number == CarSettingConstant.InLight_Purple) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "紫色"
                )
                if (number == CarSettingConstant.InLight_Double_Color_Mode_7) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "双色"
                )
                if (number == CarSettingConstant.InLight_Double_Color_Mode_8) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "双色"
                )
                if (number == CarSettingConstant.InLight_Double_Color_Mode_9) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "双色"
                )
                if (number == CarSettingConstant.InLight_Double_Color_Mode_10) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "双色"
                )
                if (number == CarSettingConstant.InLight_Double_Color_Mode_11) sendResultCode(
                    VDVRRespondID.switch_atmosphere_lamp_color_1,
                    "双色"
                )
            }
        } else {
            lightInPresenter!!.setAmbLightSw(1, CommonConst.LIGHT_SW_ALL)
            var number = numbers1.random()
            lightInPresenter!!.changeColorLin(number)
            if (number == CarSettingConstant.InLight_Red) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "红色"
            )
            if (number == CarSettingConstant.InLight_Orange) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "橙色"
            )
            if (number == CarSettingConstant.InLight_Yellow) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "黄色"
            )
            if (number == CarSettingConstant.InLight_Green) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "绿色"
            )
            if (number == CarSettingConstant.InLight_Cyan) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "青色"
            )
            if (number == CarSettingConstant.InLight_Blue) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "蓝色"
            )
            if (number == CarSettingConstant.InLight_Purple) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "紫色"
            )
            if (number == CarSettingConstant.InLight_Double_Color_Mode_7) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "双色"
            )
            if (number == CarSettingConstant.InLight_Double_Color_Mode_8) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "双色"
            )
            if (number == CarSettingConstant.InLight_Double_Color_Mode_9) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "双色"
            )
            if (number == CarSettingConstant.InLight_Double_Color_Mode_10) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "双色"
            )
            if (number == CarSettingConstant.InLight_Double_Color_Mode_11) sendResultCode(
                VDVRRespondID.switch_atmosphere_lamp_color_2,
                "双色"
            )
        }
    }

    /**氛围灯亮度调到最高、最低
     * @param value
     * 5:调到最高 6:调到最低
     */
    fun setBrightMax_Min(value: String) {
        var lightsw = lightInPresenter?.getLightSw()
        var bright = lightInPresenter?.getCurBrightness()

        if (value.toInt() == CarSettingConstant.InLight_Brightness_Max) {
            if (lightsw == CarSettingConstant.InLight_ON) {
                if (bright == CarSettingConstant.InLight_Brightness_100) {
                    sendResultCode(VDVRRespondID.adjust_atmosphere_lamp_brightness_to_max_1)
                } else {
                    lightInPresenter!!.changeBrightness(CarSettingConstant.InLight_Brightness_100)
                    sendResultCode(VDVRRespondID.adjust_atmosphere_lamp_brightness_to_max_2)
                }
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.changeBrightness(CarSettingConstant.InLight_Brightness_100)
                sendResultCode(VDVRRespondID.adjust_atmosphere_lamp_brightness_to_max_3)
            }
        }
        if (value.toInt() == CarSettingConstant.InLight_Brightness_Min) {
            if (lightsw == CarSettingConstant.InLight_ON) {
                if (bright == CarSettingConstant.InLight_Brightness_0) {
                    sendResultCode(VDVRRespondID.adjust_atmosphere_lamp_brightness_to_min_1)
                } else {
                    lightInPresenter!!.changeBrightness(CarSettingConstant.InLight_Brightness_0)
                    sendResultCode(VDVRRespondID.adjust_atmosphere_lamp_brightness_to_min_2)
                }
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.changeBrightness(CarSettingConstant.InLight_Brightness_0)
                sendResultCode(VDVRRespondID.adjust_atmosphere_lamp_brightness_to_min_3)
            }
        }
    }

    /**氛围灯调高百分比
     * @param value
     */
    fun setBrightPoint(value: String, position: String) {
        var lightsw = lightInPresenter?.getLightSw()
        var bright = lightInPresenter?.getCurBrightness()
        var eff = lightInPresenter?.getLightEffect()
        val result = position.plus("-").plus(value)

        when (position) {
            "F" -> {
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (bright == value.toInt()) {
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                            result
                        )
                    }
                    if (eff == CarSettingConstant.InLight_Static) {
                        if (value.toInt() == bright) {
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                                result
                            )
                        } else {
                            lightInPresenter!!.changeBrightness(value.toInt())
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_2,
                                result
                            )
                        }
                        if (value.toInt() > CarSettingConstant.InLight_Brightness_100 || value.toInt() < CarSettingConstant.InLight_Brightness_0) {
                            sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
                    }
                } else {
                    if (value.toInt() <= CarSettingConstant.InLight_Brightness_100 && value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                        lightInPresenter!!.setAmbLightSw(1, CommonConst.LIGHT_SW_FRONT)
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeBrightness(value.toInt())
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_5,
                            value
                        )
                    }
                }
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (eff == CarSettingConstant.InLight_Static) {
                        if (value.toInt() == bright) {
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                                result
                            )
                        } else {
                            lightInPresenter!!.changeBrightness(value.toInt())
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_2,
                                result
                            )
                        }
                        if (value.toInt() > CarSettingConstant.InLight_Brightness_100 || value.toInt() < CarSettingConstant.InLight_Brightness_0) {
                            sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
                    }
                } else {
                    if (value.toInt() <= CarSettingConstant.InLight_Brightness_100 && value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                        lightInPresenter!!.setAmbLightSw(
                            CarSettingConstant.InLight_ON,
                            CommonConst.LIGHT_SW_FRONT
                        )
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeBrightness(value.toInt())
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_5,
                            value
                        )
                    }
                }
            }

            "B" -> {
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (bright == value.toInt()) {
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                            result
                        )
                    }
                    if (eff == CarSettingConstant.InLight_Static) {
                        if (value.toInt() == bright) {
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                                result
                            )
                        } else {
                            lightInPresenter!!.changeBrightness(value.toInt())
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_2,
                                result
                            )
                        }
                        if (value.toInt() > CarSettingConstant.InLight_Brightness_100 || value.toInt() < CarSettingConstant.InLight_Brightness_0) {
                            sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
                    }
                } else {
                    if (value.toInt() <= CarSettingConstant.InLight_Brightness_100 && value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                        lightInPresenter!!.setAmbLightSw(1, CommonConst.LIGHT_SW_REAR)
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeBrightness(value.toInt())
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_5,
                            value
                        )
                    }
                }
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (eff == CarSettingConstant.InLight_Static) {
                        if (value.toInt() == bright) {
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                                result
                            )
                        } else {
                            lightInPresenter!!.changeBrightness(value.toInt())
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_2,
                                result
                            )
                        }
                        if (value.toInt() > CarSettingConstant.InLight_Brightness_100 || value.toInt() < CarSettingConstant.InLight_Brightness_0) {
                            sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
                    }
                } else {
                    if (value.toInt() <= CarSettingConstant.InLight_Brightness_100 && value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                        lightInPresenter!!.setAmbLightSw(
                            CarSettingConstant.InLight_ON,
                            CommonConst.LIGHT_SW_REAR
                        )
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeBrightness(value.toInt())
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_5,
                            value
                        )
                    }
                }
            }

            "ALL" -> {
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (bright == value.toInt()) {
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                            result
                        )
                    }
                    if (eff == CarSettingConstant.InLight_Static) {
                        if (value.toInt() == bright) {
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                                result
                            )
                        } else {
                            lightInPresenter!!.changeBrightness(value.toInt())
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_2,
                                result
                            )
                        }
                        if (value.toInt() > CarSettingConstant.InLight_Brightness_100 || value.toInt() < CarSettingConstant.InLight_Brightness_0) {
                            sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
                    }
                } else {
                    if (value.toInt() <= CarSettingConstant.InLight_Brightness_100 && value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                        lightInPresenter!!.setAmbLightSw(
                            CarSettingConstant.InLight_ON,
                            CommonConst.LIGHT_SW_ALL
                        )
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeBrightness(value.toInt())
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_5,
                            value
                        )
                    }
                }
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (eff == CarSettingConstant.InLight_Static) {
                        if (value.toInt() == bright) {
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                                result
                            )
                        } else {
                            lightInPresenter!!.changeBrightness(value.toInt())
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_2,
                                result
                            )
                        }
                        if (value.toInt() > CarSettingConstant.InLight_Brightness_100 || value.toInt() < CarSettingConstant.InLight_Brightness_0) {
                            sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
                    }
                } else {
                    if (value.toInt() <= CarSettingConstant.InLight_Brightness_100 && value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                        lightInPresenter!!.setAmbLightSw(
                            CarSettingConstant.InLight_ON,
                            CommonConst.LIGHT_SW_ALL
                        )
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeBrightness(value.toInt())
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_5,
                            value
                        )
                    }
                }
            }

            "" -> {
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (bright == value.toInt()) {
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                            value
                        )
                    }
                    if (eff == CarSettingConstant.InLight_Static) {
                        if (value.toInt() == bright) {
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                                value
                            )
                        } else {
                            lightInPresenter!!.changeBrightness(value.toInt())
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_2,
                                value
                            )
                        }
                        if (value.toInt() > CarSettingConstant.InLight_Brightness_100 || value.toInt() < CarSettingConstant.InLight_Brightness_0) {
                            sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
                    }
                } else {
                    if (value.toInt() <= CarSettingConstant.InLight_Brightness_100 && value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                        lightInPresenter!!.setAmbLightSw(
                            CarSettingConstant.InLight_ON,
                            CommonConst.LIGHT_SW_ALL
                        )
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeBrightness(value.toInt())
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_5,
                            value
                        )
                    }
                }
                if (lightsw == CarSettingConstant.InLight_ON) {
                    if (eff == CarSettingConstant.InLight_Static) {
                        if (value.toInt() == bright) {
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_1,
                                value
                            )
                        } else {
                            lightInPresenter!!.changeBrightness(value.toInt())
                            sendResultCode(
                                VDVRRespondID.Adjust_brightness_ambient_light_percentage_2,
                                value
                            )
                        }
                        if (value.toInt() > CarSettingConstant.InLight_Brightness_100 || value.toInt() < CarSettingConstant.InLight_Brightness_0) {
                            sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                        }
                    } else {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
                    }
                } else {
                    if (value.toInt() <= CarSettingConstant.InLight_Brightness_100 && value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                        lightInPresenter!!.setAmbLightSw(
                            CarSettingConstant.InLight_ON,
                            CommonConst.LIGHT_SW_ALL
                        )
                        lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                        lightInPresenter!!.changeBrightness(value.toInt())
                        sendResultCode(
                            VDVRRespondID.Adjust_brightness_ambient_light_percentage_5,
                            value
                        )
                    }
                }
            }
        }
    }

    /**氛围灯亮度调高百分比
     * @param value
     */
    fun setPlusBrightness(value: String) {
        var lightsw = lightInPresenter?.getLightSw()
        var bright = lightInPresenter?.getCurBrightness()
        var eff = lightInPresenter?.getLightEffect()

        if (lightsw == CarSettingConstant.InLight_ON) {
            if (eff == CarSettingConstant.InLight_Static) {
                if (bright == CarSettingConstant.InLight_Brightness_100) {
                    sendResultCode(VDVRRespondID.Increase_percentage_ambient_lighting_4)
                }
                bright?.let { num ->
                    if (num <= CarSettingConstant.InLight_Brightness_100) {
                        bright?.let { num ->
                            if (num + value.toInt() < CarSettingConstant.InLight_Brightness_100) {
                                lightInPresenter!!.changeBrightness(num + value.toInt())
                                sendResultCode(
                                    VDVRRespondID.Increase_percentage_ambient_lighting_2,
                                    num.toString()
                                )
                            } else if (num + value.toInt() == CarSettingConstant.InLight_Brightness_100) {
                                lightInPresenter!!.changeBrightness(num + value.toInt())
                                sendResultCode(VDVRRespondID.Increase_percentage_ambient_lighting_3)
                            }
                        }
                    }
                }
            } else {
                sendResultCode(VDVRRespondID.Increase_percentage_ambient_lighting_5)
            }
        } else {
            bright?.let { num ->
                if (num + value.toInt() <= CarSettingConstant.InLight_Brightness_100) {
                    lightInPresenter!!.setAmbLightSw(
                        CarSettingConstant.InLight_ON,
                        CommonConst.LIGHT_SW_ALL
                    )
                    lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                    lightInPresenter!!.changeBrightness(num + value.toInt())
                    sendResultCode(
                        VDVRRespondID.Increase_percentage_ambient_lighting_6,
                        num.toString()
                    )
                }
            }
        }
    }

    /**氛围灯亮度调低百分比
     * @param value
     */
    fun setMinusBrightness(value: String) {
        var lightsw = lightInPresenter?.getLightSw()
        var bright = lightInPresenter?.getCurBrightness()
        var eff = lightInPresenter?.getLightEffect()

        if (lightsw == CarSettingConstant.InLight_ON) {
            if (eff == CarSettingConstant.InLight_Static) {
                if (bright == CarSettingConstant.InLight_Brightness_0) {
                    sendResultCode(VDVRRespondID.Percentage_percentage_ambient_lighting_4)
                }
                bright?.let { num ->
                    if (num > CarSettingConstant.InLight_Brightness_0) {
                        bright?.let { num ->
                            if (num - value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                                lightInPresenter!!.changeBrightness(num - value.toInt())
                                sendResultCode(
                                    VDVRRespondID.Percentage_percentage_ambient_lighting_2,
                                    num.toString()
                                )
                            } else if (num - value.toInt() == CarSettingConstant.InLight_Brightness_0) {
                                lightInPresenter!!.changeBrightness(num - value.toInt())
                                sendResultCode(VDVRRespondID.Percentage_percentage_ambient_lighting_3)
                            }
                        }
                    }
                }
            } else {
                sendResultCode(VDVRRespondID.Percentage_percentage_ambient_lighting_5)
            }
        } else {
            bright?.let { num ->
                if (num - value.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                    sendResultCode(VDVRRespondID.Percentage_percentage_ambient_lighting_6)
                }
            }
        }
    }

    /**氛围灯亮度设置
     * @param value
     */
    fun setBrightPos(position: String, value: String) {
        val pos = position.toInt()
        val number = value.toInt()

        if (lightInPresenter?.getLightSw() == CarSettingConstant.InLight_ON) {
            if (lightInPresenter?.themeMode == CarSettingConstant.Theme_Mode_Theme) {
                if (lightInPresenter?.lightEffect == CarSettingConstant.InLight_Static || lightInPresenter?.lightEffect == CarSettingConstant.InLight_Breathe) {
                    if (number == lightInPresenter?.getCurBrightness(pos)) {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_1)
                    } else {
                        lightInPresenter?.changeBrightness(pos, number)
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_2)
                    }
                    if (position.toInt() > CarSettingConstant.InLight_Brightness_100 || position.toInt() < CarSettingConstant.InLight_Brightness_0) {
                        sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_3)
                    }
                }
            } else {
                sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_4)
            }
        } else {
            if (position.toInt() <= CarSettingConstant.InLight_Brightness_100 && position.toInt() >= CarSettingConstant.InLight_Brightness_0) {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.lightEffect = CarSettingConstant.InLight_Static
                lightInPresenter!!.changeBrightness(pos, number)
                sendResultCode(VDVRRespondID.Adjust_brightness_ambient_light_percentage_5, value)
            }
        }
    }

    /**
     * 打开指定车内灯
     */
    fun openDesignateInsideLight(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.open_designative_in_car_lamp_1)
        }
    }

    /**
     * 关闭指定车内灯
     */
    fun closeDesignateInsideLight(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.close_designative_in_car_lamp_1)
        }
    }

    /**
     * 设置手套箱灯
     */
    fun setGloveBoxLight(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_brightness_little_1)
        }
    }

    /**
     * 设置扶手箱灯
     */
    fun setHandrailBoxLight(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_brightness_little_1)
        }
    }

    /**
     * 设置照脚灯
     */
    fun setFootlight(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_brightness_little_1)
        }
    }

    /**
     * 手套箱灯调到具体挡位
     */
    fun gloveBoxLightValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_gear_1)
        }
    }

    /**
     * 扶手箱灯调到具体挡位
     */
    fun handrailBoxLightValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_gear_1)
        }
    }

    /**
     * 照脚灯调到具体挡位
     */
    fun footlightValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_gear_1)
        }
    }

    /**
     * 手套箱灯时长调大、小一点，最大、最低
     */
    fun gloveBoxLightTime(value: String) {
        if (value != "") {
            when (value.toInt()) {
                LightConstant.AR_PARAM_High -> sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_time_little_1)
                LightConstant.AR_PARAM_Low -> sendResultCode(VDVRRespondID.lower_designative_in_car_lamp_time_little_1)
                LightConstant.AR_PARAM_Max -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_max_1)
                LightConstant.AR_PARAM_Min -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_min_1)
                else -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_min_1)
            }
        }
    }

    /**
     * 扶手箱灯时长调大、小一点，最大、最低
     */
    fun handrailBoxLightTime(value: String) {
        if (value != "") {
            when (value.toInt()) {
                LightConstant.AR_PARAM_High -> sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_time_little_1)
                LightConstant.AR_PARAM_Low -> sendResultCode(VDVRRespondID.lower_designative_in_car_lamp_time_little_1)
                LightConstant.AR_PARAM_Max -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_max_1)
                LightConstant.AR_PARAM_Min -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_min_1)
                else -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_min_1)
            }
        }
    }

    /**
     * 照脚灯时长调大、小一点，最大、最低
     */
    fun footlightTime(value: String) {
        if (value != "") {
            when (value.toInt()) {
                LightConstant.AR_PARAM_High -> sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_time_little_1)
                LightConstant.AR_PARAM_Low -> sendResultCode(VDVRRespondID.lower_designative_in_car_lamp_time_little_1)
                LightConstant.AR_PARAM_Max -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_max_1)
                LightConstant.AR_PARAM_Min -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_min_1)
                else -> sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_min_1)
            }
        }
    }

    /**
     * 手套箱灯照明时长调到具体数值
     */
    fun gloveBoxLightTimeValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_number_1)
        }
    }

    /**
     * 扶手箱灯照明时长调到具体数值
     */
    fun handrailBoxLightTimeValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_number_1)
        }
    }

    /**
     * 照脚灯照明时长调到具体数值
     */
    fun footlightTimeValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_time_to_number_1)
        }
    }

    /**
     * 手套箱灯照明时长调高数值
     */
    fun gloveBoxLightTimeValueUp(value: String) {
        if ((value != "")) {
            sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_time_by_number_1)
        }
    }

    /**
     * 扶手箱灯照明时长调高数值
     */
    fun handrailBoxLightTimeValueUp(value: String) {
        if ((value != "")) {
            sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_time_by_number_1)
        }
    }

    /**
     * 照脚灯照明时长调高数值
     */
    fun footlightTimeValueUp(value: String) {
        if ((value != "")) {
            sendResultCode(VDVRRespondID.raise_designative_in_car_lamp_time_by_number_1)
        }
    }

    /**
     * 手套箱灯照明时长调低数值
     */
    fun gloveBoxLightTimeValueDown(value: String) {
        if ((value != "")) {
            sendResultCode(VDVRRespondID.lower_designative_in_car_lamp_time_by_number_1)
        }
    }

    /**
     * 扶手箱灯照明时长调低数值
     */
    fun handrailBoxLightTimeValueDown(value: String) {
        if ((value != "")) {
            sendResultCode(VDVRRespondID.lower_designative_in_car_lamp_time_by_number_1)
        }
    }

    /**
     * 照脚灯照明时长调低数值
     */
    fun footlightTimeValueDown(value: String) {
        if ((value != "")) {
            sendResultCode(VDVRRespondID.lower_designative_in_car_lamp_time_by_number_1)
        }
    }

    /**
     * 打开指定车外灯
     */
    fun openDesignateOuterLight(value: String) {
        if ((value != "")) {
            sendResultCode(VDVRRespondID.open_designative_out_car_lamp_1)
        }
    }

    /**
     * 关闭指定车外灯
     */
    fun closeDesignateOuterLight(value: String) {
        if ((value != "")) {
            sendResultCode(VDVRRespondID.close_designative_out_car_lamp_1)
        }
    }

    /**
     * 打开or关闭自动大灯
     */
    fun openOrcloseAutoLamp(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_automatic_lamp_3)
        } else {
            sendResultCode(VDVRRespondID.close_designative_out_car_lamp_6)
        }
    }

    /**
     * 打开or关闭位置灯
     */
    fun openOrclosePositionLight(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_position_lamp_2)
        } else {
            sendResultCode(VDVRRespondID.close_position_lamp_2)
        }
    }

    /**
     * 打开or关闭近光灯
     */
    fun openOrcloseNearLight(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_low_lamp_2)
        } else {
            sendResultCode(VDVRRespondID.close_low_lamp_2)
        }
    }

    /**
     * 智能迎宾设置为xxx
     */
    fun setIntelligentWelcome(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_intelligent_welcome_to_xxx_4)
        }
    }

    /**
     * 打开or关闭智能远光灯
     */
    fun openorcloseSmartHighLamp(flag: Boolean) {
        if (flag) {
            if (mLightOutPresenter.highLowSwitch == LightConstant.LIGHT_HIGH_SWITCH) {
                sendResultCode(VDVRRespondID.open_intelligent_high_lamp_2)
            } else {
                mLightOutPresenter.highLowSwitch = LightConstant.LIGHT_HIGH_SWITCH
                sendResultCode(VDVRRespondID.open_intelligent_high_lamp_1)
            }
        } else {
            if (mLightOutPresenter.highLowSwitch == LightConstant.LIGHT_LOW_SWITCH) {
                sendResultCode(VDVRRespondID.close_intelligent_high_lamp_1)
            } else {
                mLightOutPresenter.highLowSwitch = LightConstant.LIGHT_LOW_SWITCH
                sendResultCode(VDVRRespondID.close_intelligent_high_lamp_2)
            }
        }
    }

    /**
     * 外音乐律动模式设置为xxx
     */
    fun setOuterMusicLawMode(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_external_music_rhythm_to_xxx_mode_1)
        }
    }

    /**
     * 打开or关闭随机音乐律动
     */
    fun openOrcloseRandomMusicalGrooves(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_random_music_rhythm_1)
        } else {
            sendResultCode(VDVRRespondID.close_random_music_rhythm_1)
        }
    }

    /**
     * 打开or关闭数字交互信号灯
     */
    fun openOrclosedigitSignalLight(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_digital_interactive_signal_lamp_1)
        } else {
            sendResultCode(VDVRRespondID.close_digital_interactive_signal_lamp_1)
        }
    }

    /**
     * 数字交互信号灯设置为xxx
     */
    fun setDigitSignalLight(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_digital_interactive_signal_lamp_to_xxx_1)
        }
    }

    /**
     * 切换指定车外灯模式，无指定值
     */
    fun switchDesignateOuterLightMode() {
        sendResultCode(VDVRRespondID.switch_designative_out_car_lamp_mode_1)
    }

    /**
     * 车外灯照明时长调大一点，小一点，最小，最大
     */
    fun setOuterLightTime(value: String) {
        if (value != "") {
            val (num1, num2) = value.split("-").map { it.toInt() }
            when (num2) {
                LightConstant.AR_PARAM_High -> sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_time_little_1)
                LightConstant.AR_PARAM_Low -> sendResultCode(VDVRRespondID.lower_designative_out_car_lamp_time_little_1)
                LightConstant.AR_PARAM_Max -> sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_time_to_max_1)
                LightConstant.AR_PARAM_Min -> sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_time_to_min_1)
                else -> sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_time_to_min_1)
            }
        }
    }

    /**
     * 车外灯照明时长调高相对数值
     */
    fun raiseDesignativeOutLampTimeValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_time_by_number_1)
        }
    }

    /**
     * 车外灯照明时长调低相对数值
     */
    fun lowerDesignativeOutLampTimeValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.lower_designative_out_car_lamp_time_by_number_1)
        }
    }

    /**
     * 车外灯照明时长调到具体数值
     */
    fun setOuterLightTimeValue(value: String) {
        if (mConfigManager?.getLampDelayOffConfig() == LightConstant.CONFIG_SUPPORTED && mConfigManager?.getAutoLightConfig() == LightConstant.CONFIG_SUPPORTED) {
            if (value != "") {
                when (value.toInt()) {
                    LightConstant.DELAY_0S -> {
                        if (carServer.getBeamDelaySts() == CarLight.BeamDelaySts.DELAY_0S) {
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_3,
                                "0秒"
                            )
                        } else {
                            carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_0S)
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_1,
                                "0秒"
                            )
                        }
                    }

                    LightConstant.DELAY_10S -> {
                        if (carServer.getBeamDelaySts() == CarLight.BeamDelaySts.DELAY_10S) {
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_3,
                                "10秒"
                            )
                        } else {
                            carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_10S)
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_1,
                                "10秒"
                            )
                        }
                    }

                    LightConstant.DELAY_20S -> {
                        if (carServer.getBeamDelaySts() == CarLight.BeamDelaySts.DELAY_20S) {
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_3,
                                "20秒"
                            )
                        } else {
                            carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_20S)
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_1,
                                "20秒"
                            )
                        }
                    }

                    LightConstant.DELAY_30S -> {
                        if (carServer.getBeamDelaySts() == CarLight.BeamDelaySts.DELAY_30S) {
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_3,
                                "30秒"
                            )
                        } else {
                            carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_30S)
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_1,
                                "30秒"
                            )
                        }
                    }

                    LightConstant.DELAY_60S -> {
                        if (carServer.getBeamDelaySts() == CarLight.BeamDelaySts.DELAY_60S) {
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_3,
                                "60秒"
                            )
                        } else {
                            carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_60S)
                            sendResultCode(
                                VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_1,
                                "60秒"
                            )
                        }
                    }
                }
            }
        } else {
            sendResultCode(VDVRRespondID.adjust_designative_in_car_lamp_brightness_to_number_2_1)
        }
    }

    /**
     * 前方车距灯光提醒灵敏度调节
     */
    fun setFrontDistanceLightSensitivity(value: String) {
        if (value != "") {
            when (value.toInt()) {
                LightConstant.AR_PARAM_High -> sendResultCode(VDVRRespondID.raise_leading_vehicle_distance_light_reminder_sensitivity_little_1)
                LightConstant.AR_PARAM_Low -> sendResultCode(VDVRRespondID.lower_leading_vehicle_distance_light_reminder_sensitivity_little_1)
                LightConstant.AR_PARAM_Middle_High -> sendResultCode(VDVRRespondID.adjust_leading_vehicle_distance_light_reminder_sensitivity_to_gear_1)
                LightConstant.AR_PARAM_Middle -> sendResultCode(VDVRRespondID.adjust_leading_vehicle_distance_light_reminder_sensitivity_to_gear_1)
                LightConstant.AR_PARAM_Middle_Low -> sendResultCode(VDVRRespondID.adjust_leading_vehicle_distance_light_reminder_sensitivity_to_gear_1)
                LightConstant.AR_PARAM_Max -> sendResultCode(VDVRRespondID.adjuset_leading_vehicle_distance_light_reminder_sensitivity_to_max_1)
                LightConstant.AR_PARAM_Min -> sendResultCode(VDVRRespondID.adjuset_leading_vehicle_distance_light_reminder_sensitivity_to_min_1)
                else -> sendResultCode(VDVRRespondID.adjuset_leading_vehicle_distance_light_reminder_sensitivity_to_min_1)
            }
        }
    }

    /**
     * 前方车距灯光提醒灵敏度调高具体数值
     */
    fun raiseFrontDistanceLightSensitivityValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.raise_leading_vehicle_distance_light_reminder_sensitivity_by_number_1)
        }
    }

    /**
     * 前方车距灯光提醒灵敏度调低具体数值
     */
    fun lowerFrontDistanceLightSensitivityValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.lower_leading_vehicle_distance_light_reminder_sensitivity_by_number_1)
        }
    }

    /**
     * 前方车距灯光提醒灵敏度调到具体数值
     */
    fun setFrontDistanceLightSensitivityValue(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.adjust_leading_vehicle_distance_light_reminder_sensitivity_to_number_1)
        }
    }

    /**自动大灯调高一点、低一点、最高、最低
     * @param value
     */
    fun autoLightControl(value: String) {
        if (value.toInt() == CarSettingConstant.Auto_High_Light_Adjust_Up) {
            sendResultCode(VDVRRespondID.raise_auto_headlight_sensitivity_little_1)
        }
        if (value.toInt() == CarSettingConstant.Auto_High_Light_Adjust_Down) {
            sendResultCode(VDVRRespondID.lower_auto_headlight_sensitivity_little_1)
        }
        if (value.toInt() == CarSettingConstant.Auto_High_Light_Adjust_Max) {
            sendResultCode(VDVRRespondID.adjuset_auto_headlight_sensitivity_to_max_1)
        }
        if (value.toInt() == CarSettingConstant.Auto_High_Light_Adjust_Min) {
            sendResultCode(VDVRRespondID.adjuset_auto_headlight_sensitivity_to_min_1)
        }
    }

    /**自动大灯调到具体数值
     * @param value
     */
    fun autoLightToNumber(value: String) {
        sendResultCode(VDVRRespondID.adjuset_auto_headlight_sensitivity_to_number_1)
    }

    /**自动大灯调到挡位
     * @param value
     */
    fun autoLightToGears(value: String) {
        if (value.toInt() == CarSettingConstant.Auto_High_Light_Adjust_Low || value.toInt() == CarSettingConstant.Auto_High_Light_Adjust_Mid || value.toInt() == CarSettingConstant.Auto_High_Light_Adjust_High) {
            sendResultCode(VDVRRespondID.adjuset_auto_headlight_sensitivity_to_gear_1)
        }
    }

    /**主题内灯光调到具体颜色
     * @param value
     */
    fun setThemeInLight(value: String) {
        var lightsw = lightInPresenter?.getLightSw()
        var color = lightInPresenter?.getCurColorLin()
        var them = lightInPresenter?.getThemeMode()
        var eff = lightInPresenter?.getLightEffect()
        if (them == CarSettingConstant.Theme_Mode_Customize) {
            if (lightsw == CarSettingConstant.InLight_ON) {
                if (eff == CarSettingConstant.InLight_Static || eff == CarSettingConstant.InLight_Breathe) {
                    if (color == value.toInt()) {
                        lightInPresenter!!.changeColorLin(value.toInt())
                        sendResultCode(VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_2)
                    } else {
                        if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "朱颜"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "丹橙"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "栗黄"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "玺绿"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "竹青"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "品岚"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "槿紫"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_7) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "春绿"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_8) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "粉韵"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_9) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "枫红"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_10) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "湖青"
                        )
                        if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_11) sendResultCode(
                            VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_3,
                            "苍绿"
                        )
                    }
                } else {
                    sendResultCode(VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_4)
                }
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Static)
                lightInPresenter!!.changeColorLin(value.toInt())
                if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "朱颜"
                )
                if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "丹橙"
                )
                if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "栗黄"
                )
                if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "玺绿"
                )
                if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "竹青"
                )
                if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "品岚"
                )
                if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "槿紫"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_7) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "春绿"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_8) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "粉韵"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_9) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "枫红"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_10) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "湖青"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_11) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_5,
                    "苍绿"
                )
            }
        } else {
            if (lightsw == CarSettingConstant.InLight_ON) {
                lightInPresenter?.chgThemeMode(CarSettingConstant.Theme_Mode_Customize)
                lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Static)
                lightInPresenter!!.changeColorLin(value.toInt())
                if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "朱颜"
                )
                if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "丹橙"
                )
                if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "栗黄"
                )
                if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "玺绿"
                )
                if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "竹青"
                )
                if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "品岚"
                )
                if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "槿紫"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_7) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "春绿"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_8) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "粉韵"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_9) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "枫红"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_10) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "湖青"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_11) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_6,
                    "苍绿"
                )
            } else {
                lightInPresenter!!.setAmbLightSw(
                    CarSettingConstant.InLight_ON,
                    CommonConst.LIGHT_SW_ALL
                )
                lightInPresenter!!.setLightEffect(CarSettingConstant.InLight_Static)
                lightInPresenter!!.changeColorLin(value.toInt())
                if (value.toInt() == CarSettingConstant.InLight_Red) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "朱颜"
                )
                if (value.toInt() == CarSettingConstant.InLight_Orange) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "丹橙"
                )
                if (value.toInt() == CarSettingConstant.InLight_Yellow) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "栗黄"
                )
                if (value.toInt() == CarSettingConstant.InLight_Green) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "玺绿"
                )
                if (value.toInt() == CarSettingConstant.InLight_Cyan) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "竹青"
                )
                if (value.toInt() == CarSettingConstant.InLight_Blue) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "品岚"
                )
                if (value.toInt() == CarSettingConstant.InLight_Purple) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "槿紫"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_7) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "春绿"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_8) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "粉韵"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_9) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "枫红"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_10) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "湖青"
                )
                if (value.toInt() == CarSettingConstant.InLight_Double_Color_Mode_11) sendResultCode(
                    VDVRRespondID.switch_specific_multiple_color_atmosphere_lamp_7,
                    "苍绿"
                )
            }
        }
    }

    /**氛围灯自动亮度模式
     * @param flag
     */
    fun setAmbientLightAutoBrightnessMode(flag: Boolean) {
        //todo 配置字判断
        var eff = lightInPresenter?.getLightEffect()
        if (flag) {
            if (eff == CarSettingConstant.InLight_Gradient || eff == CarSettingConstant.InLight_MusicRhythm) {
                sendResultCode(VDVRRespondID.Turn_automatic_brightness_mode_atmosphere_4)
            } else {
                if (lightInPresenter?.getAutoLamp() == CarSettingConstant.InLight_Auto_Brightness_ON) {
                    sendResultCode(VDVRRespondID.Turn_automatic_brightness_mode_atmosphere_2)
                } else {
                    lightInPresenter!!.setBrightnessAuto(CarSettingConstant.InLight_Auto_Brightness_ON)
                    sendResultCode(VDVRRespondID.Turn_automatic_brightness_mode_atmosphere_3)
                }
            }
        } else {
            if (eff == CarSettingConstant.InLight_Gradient || eff == CarSettingConstant.InLight_MusicRhythm) {
                sendResultCode(VDVRRespondID.off_automatic_brightness_mode_atmosphere_4)
            } else {
                if (lightInPresenter?.getAutoLamp() == CarSettingConstant.InLight_Auto_Brightness_OFF) {
                    sendResultCode(VDVRRespondID.off_automatic_brightness_mode_atmosphere_3)
                } else {
                    lightInPresenter!!.setBrightnessAuto(CarSettingConstant.InLight_Auto_Brightness_OFF)
                    sendResultCode(VDVRRespondID.off_automatic_brightness_mode_atmosphere_2)
                }
            }
        }
    }

    /**氛围灯切换为指定模式
     * @param value
     */
    fun themeInLight(value: String) {
        //todo 配置字判断
        var lightsw = lightInPresenter?.getLightSw()
        var them = lightInPresenter?.getThemeMode()
        var eff = lightInPresenter?.getLightEffect()
        if (lightsw == CarSettingConstant.InLight_ON) {
            if (eff == CarSettingConstant.InLight_Gradient || eff == CarSettingConstant.InLight_MusicRhythm) {
                sendResultCode(VDVRRespondID.Switch_ambient_light_designated_mode_6)
            } else {
                if (them == value.toInt()) {
                    if (them == CarSettingConstant.Theme_Mode_Theme) sendResultCode(
                        VDVRRespondID.Switch_ambient_light_designated_mode_2,
                        "主题"
                    )
                    if (them == CarSettingConstant.Theme_Mode_Customize) sendResultCode(
                        VDVRRespondID.Switch_ambient_light_designated_mode_2,
                        "自定义"
                    )
                } else {
                    lightInPresenter!!.chgThemeMode(value.toInt())
                    sendResultCode(VDVRRespondID.Switch_ambient_light_designated_mode_3)
                }
            }
        } else {
            lightInPresenter!!.setAmbLightSw(
                CarSettingConstant.InLight_ON,
                CommonConst.LIGHT_SW_ALL
            )
            lightInPresenter!!.chgThemeMode(value.toInt())
            if (them == CarSettingConstant.Theme_Mode_Theme) sendResultCode(
                VDVRRespondID.Switch_ambient_light_designated_mode_4,
                "主题"
            )
            if (them == CarSettingConstant.Theme_Mode_Customize) sendResultCode(
                VDVRRespondID.Switch_ambient_light_designated_mode_4,
                "自定义"
            )
        }
    }


    object LightConstant {
        const val CONFIG_SUPPORTED: Int = 1
        const val AR_PARAM_High: Int = 0
        const val AR_PARAM_Low: Int = 1
        const val AR_PARAM_Middle: Int = 2
        const val AR_PARAM_Middle_High: Int = 3
        const val AR_PARAM_Middle_Low: Int = 4
        const val AR_PARAM_Max: Int = 5
        const val AR_PARAM_Min: Int = 6
        const val LIGHT_LOW_SWITCH: Int = 0
        const val LIGHT_HIGH_SWITCH: Int = 1
        const val DELAY_0S: Int = 0
        const val DELAY_10S: Int = 10
        const val DELAY_20S: Int = 20
        const val DELAY_30S: Int = 30
        const val DELAY_60S: Int = 60

    }
}

