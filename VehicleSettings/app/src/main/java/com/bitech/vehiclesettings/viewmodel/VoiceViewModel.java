package com.bitech.vehiclesettings.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

public class VoiceViewModel extends ViewModel {
    // 声场
    private final MutableLiveData<Integer> eq = new MutableLiveData<>();
    // 环绕音
    private final MutableLiveData<Integer> surroundSound = new MutableLiveData<>();
    // 虚拟现场
    private final MutableLiveData<Integer> virtualScene = new MutableLiveData<>();
    // 头枕扬声器
    private final MutableLiveData<Integer> headRest = new MutableLiveData<>();
    // 声场坐标
    private final MutableLiveData<Integer> positionX = new MutableLiveData<>();
    private final MutableLiveData<Integer> positionY = new MutableLiveData<>();
    // 均衡调节
    private final MutableLiveData<Integer> subBass = new MutableLiveData<>();
    private final MutableLiveData<Integer> bass = new MutableLiveData<>();
    private final MutableLiveData<Integer> lowMid = new MutableLiveData<>();
    private final MutableLiveData<Integer> mid = new MutableLiveData<>();
    private final MutableLiveData<Integer> highMid = new MutableLiveData<>();
    private final MutableLiveData<Integer> treble = new MutableLiveData<>();
    private final MutableLiveData<Integer> superTreble = new MutableLiveData<>();
    // 来电播报
    private final MutableLiveData<Integer> callBroadcast = new MutableLiveData<>();
    // 车外低速模拟音
    private static final MutableLiveData<Integer> lowSpeedAnalog = new MutableLiveData<>();
    // 车外低速模拟音
    private static final MutableLiveData<Integer> mediaProgress = new MutableLiveData<>();
    // 车外低速模拟音
    private static final MutableLiveData<Integer> naviProgress = new MutableLiveData<>();
    // 车外低速模拟音
    private static final MutableLiveData<Integer> VRProgress = new MutableLiveData<>();
    // 车外低速模拟音
    private static final MutableLiveData<Integer> phoneProgress = new MutableLiveData<>();
    // 外放模式
    private final MutableLiveData<Integer> voiceExternalMode = new MutableLiveData<>();
    // 导航压低媒体音
    private final MutableLiveData<Integer> voiceLowerMediaTone = new MutableLiveData<>();
    // 按键音
    private final MutableLiveData<Integer> buttonSound = new MutableLiveData<>();
    // 报警音类型
    private final MutableLiveData<Integer> alarmType = new MutableLiveData<>();
    // 随速补偿
    private final MutableLiveData<Integer> compensation = new MutableLiveData<>();

    public MutableLiveData<Integer> getEq() {
        return eq;
    }
    public void setEq(Integer status) {
        eq.postValue(status);
    }
    public MutableLiveData<Integer> getSurroundSound() {
        return surroundSound;
    }
    public void setSurroundSound(Integer status) {
        surroundSound.postValue(status);
    }
    public MutableLiveData<Integer> getVirtualScene() {
        return virtualScene;
    }
    public void setVirtualScene(Integer status) {
        virtualScene.postValue(status);
    }
    public MutableLiveData<Integer> getHeadRest() {
        return headRest;
    }
    public void setHeadRest(Integer status) {
        headRest.postValue(status);
    }
    public MutableLiveData<Integer> getPositionX() {
        return positionX;
    }
    public void setPositionX(Integer status) {
        positionX.postValue(status);
    }
    public MutableLiveData<Integer> getPositionY() {
        return positionY;
    }
    public void setPositionY(Integer status) {
        positionY.postValue(status);
    }
    public MutableLiveData<Integer> getSubBass() {
        return subBass;
    }
    public void setSubBass(Integer status) {
        subBass.postValue(status);
    }
    public MutableLiveData<Integer> getBass() {
        return bass;
    }
    public void setBass(Integer status) {
        bass.postValue(status);
    }
    public MutableLiveData<Integer> getLowMid() {
        return lowMid;
    }
    public void setLowMid(Integer status) {
        lowMid.postValue(status);
    }
    public MutableLiveData<Integer> getMid() {
        return mid;
    }
    public void setMid(Integer status) {
        mid.postValue(status);
    }
    public MutableLiveData<Integer> getHighMid() {
        return highMid;
    }
    public void setHighMid(Integer status) {
        highMid.postValue(status);
    }
    public MutableLiveData<Integer> getTreble() {
        return treble;
    }
    public void setTreble(Integer status) {
        treble.postValue(status);
    }
    public MutableLiveData<Integer> getSuperTreble() {
        return superTreble;
    }
    public void setSuperTreble(Integer status) {
        superTreble.postValue(status);
    }
    public MutableLiveData<Integer> getCallBroadcast() {
        return callBroadcast;
    }
    public void setCallBroadcast(Integer status) {
        callBroadcast.postValue(status);
    }
    public static MutableLiveData<Integer> getLowSpeedAnalog() {
        return lowSpeedAnalog;
    }
    public static void setLowSpeedAnalog(Integer status) {
        lowSpeedAnalog.postValue(status);
    }
    public MutableLiveData<Integer> getVoiceExternalMode() {
        return voiceExternalMode;
    }
    public void setVoiceExternalMode(Integer status) {
        voiceExternalMode.postValue(status);
    }
    public MutableLiveData<Integer> getVoiceLowerMediaTone() {
        return voiceLowerMediaTone;
    }
    public void setVoiceLowerMediaTone(Integer status) {
        voiceLowerMediaTone.postValue(status);
    }
    public MutableLiveData<Integer> getButtonSound() {
        return buttonSound;
    }
    public void setButtonSound(Integer status) {
        buttonSound.postValue(status);
    }
    public MutableLiveData<Integer> getAlarmType() {
        return alarmType;
    }
    public void setAlarmType(Integer status) {
        alarmType.postValue(status);
    }
    public MutableLiveData<Integer> getCompensation() {
        return compensation;
    }
    public void setCompensation(Integer status) {
        compensation.postValue(status);
    }
    public static MutableLiveData<Integer> getMediaProgress() {
        return mediaProgress;
    }
    public static void setMediaProgress(Integer status) {
        mediaProgress.postValue(status);
    }

    public static MutableLiveData<Integer> getNaviProgress() {
        return naviProgress;
    }
    public static void setNaviProgress(Integer status) {
        naviProgress.postValue(status);
    }

    public static MutableLiveData<Integer> getPhoneProgress() {
        return phoneProgress;
    }
    public static void setPhoneProgress(Integer status) {
        phoneProgress.postValue(status);
    }

    public static MutableLiveData<Integer> getVRProgress() {
        return VRProgress;
    }
    public static void setVRProgress(Integer status) {
        VRProgress.postValue(status);
    }

    @Override
    protected void onCleared() {
        super.onCleared();

    }
}
