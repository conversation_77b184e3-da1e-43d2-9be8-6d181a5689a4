package com.bitech.vehiclesettings.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import java.util.Objects;

public class CarSettingModel extends ViewModel {
    // 后尾门高度
    private final MutableLiveData<Integer> hudRoate = new MutableLiveData<Integer>();
    // 雨刮器灵敏度
    private final MutableLiveData<Integer> wiperSensitivity = new MutableLiveData<>();
    // 锁车自动升窗
    private final MutableLiveData<Integer> lockAutoRaiseWindow = new MutableLiveData<>();
    // 设防提示
    private final MutableLiveData<Integer> defenseReminder = new MutableLiveData<>();
    // 左儿童锁
    private final MutableLiveData<Integer> leftChildLock = new MutableLiveData<>();
    // 右儿童锁
    private final MutableLiveData<Integer> rightChildLock = new MutableLiveData<>();
    // 自动落锁
    private final MutableLiveData<Integer> automaticLocking = new MutableLiveData<>();
    // 驻车自动解锁
    private final MutableLiveData<Integer> automaticParkingUnlock = new MutableLiveData<>();
    // 雨刮器维修模式
    private final MutableLiveData<Integer> wiperRepairMode = new MutableLiveData<>();
    // 超速报警
    private final MutableLiveData<Integer> overSpeed = new MutableLiveData<>();
    // 疲劳驾驶提醒
    private final MutableLiveData<Integer> fatigueDrivingReminder = new MutableLiveData<>();
    // 保养提示
    private final MutableLiveData<Integer> maintainTips = new MutableLiveData<>();
    // 保养里程复位
    private final MutableLiveData<Integer> maintainReset = new MutableLiveData<>();
    // 副驾安全气囊
    private final MutableLiveData<Integer> driveAirBag = new MutableLiveData<>();
    // 超速报警seekbar
    private final MutableLiveData<Integer> overSpeedSeekbar = new MutableLiveData<>();
    // 疲劳驾驶seekbar
    private final MutableLiveData<Integer> fatigueDrivingSeekbar = new MutableLiveData<>();

    public MutableLiveData<Integer> getHudRoate() {
        return hudRoate;
    }

    public void setHudRoate(Integer status) {
        hudRoate.postValue(status);
    }

    public MutableLiveData<Integer> getWiperSensitivity() {
        return wiperSensitivity;
    }

    public void setWiperSensitivity(Integer status) {
        if (Objects.equals(wiperSensitivity.getValue(), status)) return;
        wiperSensitivity.postValue(status);
    }

    public MutableLiveData<Integer> getLockAutoRaiseWindow() {
        return lockAutoRaiseWindow;
    }

    public void setLockAutoRaiseWindow(Integer status) {
        lockAutoRaiseWindow.postValue(status);
    }

    public MutableLiveData<Integer> getDefenseReminder() {
        return defenseReminder;
    }

    public void setDefenseReminder(Integer status) {
        defenseReminder.postValue(status);
    }

    public MutableLiveData<Integer> getLeftChildLock() {
        return leftChildLock;
    }

    public void setLeftChildLock(Integer status) {
        leftChildLock.postValue(status);
    }

    public MutableLiveData<Integer> getRightChildLock() {
        return rightChildLock;
    }

    public void setRightChildLock(Integer status) {
        rightChildLock.postValue(status);
    }

    public MutableLiveData<Integer> getAutomaticLocking() {
        return automaticLocking;
    }

    public void setAutomaticLocking(Integer status) {
        automaticLocking.postValue(status);
    }

    public MutableLiveData<Integer> getAutomaticParkingUnlock() {
        return automaticParkingUnlock;
    }

    public void setAutomaticParkingUnlock(Integer status) {
        automaticParkingUnlock.postValue(status);
    }

    public MutableLiveData<Integer> getWiperRepairMode() {
        return wiperRepairMode;
    }

    public void setWiperRepairMode(Integer status) {
        wiperRepairMode.postValue(status);
    }

    public MutableLiveData<Integer> getOverSpeed() {
        return overSpeed;
    }

    public void setOverSpeed(Integer status) {
        overSpeed.postValue(status);
    }

    public MutableLiveData<Integer> getFatigueDrivingReminder() {
        return fatigueDrivingReminder;
    }

    public void setFatigueDrivingReminder(Integer status) {
        fatigueDrivingReminder.postValue(status);
    }

    public MutableLiveData<Integer> getMaintainTips() {
        return maintainTips;
    }

    public void setMaintainTips(Integer status) {
        maintainTips.postValue(status);
    }

    public MutableLiveData<Integer> getMaintainReset() {
        return maintainReset;
    }

    public void setMaintainReset(Integer status) {
        maintainReset.postValue(status);
    }

    public MutableLiveData<Integer> getDriveAirBag() {
        return driveAirBag;
    }

    public void setDriveAirBag(Integer status) {
        driveAirBag.postValue(status);
    }

    public MutableLiveData<Integer> getOverSpeedSeekbar() {
        return overSpeedSeekbar;
    }

    public void setOverSpeedSeekbar(Integer status) {
        overSpeedSeekbar.postValue(status);
    }

    public MutableLiveData<Integer> getFatigueDrivingSeekbar() {
        return fatigueDrivingSeekbar;
    }

    public void setFatigueDrivingSeekbar(Integer status) {
        fatigueDrivingSeekbar.postValue(status);
    }
}
