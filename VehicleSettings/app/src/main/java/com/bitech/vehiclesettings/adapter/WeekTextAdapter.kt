package com.bitech.vehiclesettings.adapter;

import android.content.Context
import android.view.ViewGroup
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.ItemNeRepeatWeekBinding
import com.chad.library.adapter4.BaseQuickAdapter
import com.chad.library.adapter4.viewholder.DataBindingHolder

/**
 * 每周重复：周一到周日
 */
class WeekTextAdapter : BaseQuickAdapter<WeekTextAdapter.WeekText, DataBindingHolder<ItemNeRepeatWeekBinding>>() {

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): DataBindingHolder<ItemNeRepeatWeekBinding> {
        return DataBindingHolder(R.layout.item_ne_repeat_week, parent)
    }

    override fun onBindViewHolder(holder: DataBindingHolder<ItemNeRepeatWeekBinding>, position: Int, item: WeekText?) {
        if (item == null) return
        holder.binding.tvText.text = item.text
        holder.binding.checked = item.isChecked
        holder.binding.executePendingBindings()
    }

    data class WeekText(val text: String, var isChecked: <PERSON>olean)

}
