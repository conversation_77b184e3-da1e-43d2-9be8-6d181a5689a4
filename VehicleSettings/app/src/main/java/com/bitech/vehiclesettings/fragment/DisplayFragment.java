package com.bitech.vehiclesettings.fragment;

import static com.bitech.vehiclesettings.presenter.display.DisplayPresenter.DEFAULT_SYSTEM_COLOR;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.ContentResolver;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.lifecycle.ViewModelProvider;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.display.IDisplayManagerListener;
import com.bitech.platformlib.manager.DisplayManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.GalleryWallpaperInfo;
import com.bitech.vehiclesettings.bean.LauncherWallpaperInfo;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.carapi.constants.Car3DModel;
import com.bitech.vehiclesettings.carapi.constants.CarDisplay;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.carapi.constants.CarWallpaper;
import com.bitech.vehiclesettings.databinding.FragmentDisplayBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.presenter.display.WallpaperPresenter;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.service.display.DisplayLifeCycle;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.ElasticAnimationUtil;
import com.bitech.vehiclesettings.utils.GsonUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.SystemColorUtil;
import com.bitech.vehiclesettings.utils.ThreeDModelUtil;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.display.CleanUIAlert;
import com.bitech.vehiclesettings.view.display.ResetDisplayUIAlert;
import com.bitech.vehiclesettings.view.display.ShowLyricsUIAlert;
import com.bitech.vehiclesettings.view.display.SystemColorUIAlert;
import com.bitech.vehiclesettings.view.display.VideoLimitUIAlert;
import com.bitech.vehiclesettings.view.display.WallpaperUIAlert;
import com.bitech.vehiclesettings.viewmodel.DisplayViewModel;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;
import com.chery.ivi.vdb.event.id.wallpaper.VDWallpaperInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.subjects.PublishSubject;


@RequiresApi(api = Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
public class DisplayFragment extends BaseFragment<FragmentDisplayBinding> implements SafeHandlerCallback, CleanUIAlert.OnBackGestureListener, SharedPreferences.OnSharedPreferenceChangeListener {
    private static final String TAG = DisplayFragment.class.getSimpleName();

    private final PublishSubject<Integer> fontSizeSubject = PublishSubject.create();
    private final PublishSubject<Integer> dayNightSubject = PublishSubject.create();
    private final PublishSubject<Integer> fpSubject = PublishSubject.create();
    private static boolean init = true;
    DisplayPresenter displayPresenter;
    WallpaperPresenter wallpaperPresenter;

    DisplayViewModel viewModel;
    private volatile boolean isActive;
    private SafeHandler displayHandler;

    VideoLimitUIAlert.Builder videoLimitAlert;

    WallpaperUIAlert.Builder wallpaperAlert;

    SystemColorUIAlert.Builder systemColorAlert;

    ResetDisplayUIAlert.Builder resetUIAlert;

    ContentResolver resolver;
    Uri fpSettingUri = Settings.Global.getUriFor(PrefsConst.GlobalValue.DISPLAY_FP);
    Uri zkpAutoSettingUri = Settings.Global.getUriFor(PrefsConst.GlobalValue.DISPLAY_AUTO_ZKP);
    Uri ybpAutoSettingUri = Settings.Global.getUriFor(PrefsConst.GlobalValue.DISPLAY_AUTO_YBP);
    Uri displayModeSettingUri = Settings.Global.getUriFor(PrefsConst.DISPLAY_MODE);

    ContentObserver observer;
    private MainActViewModel mainActViewModel;
    public static final int WallpaperType = 10;


    private DisplayManager displayManager = (DisplayManager) BitechCar.getInstance()
            .getServiceManager(BitechCar.CAR_DISPLAY_MANAGER);


    private final ActivityResultLauncher<Intent> galleryLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getData() != null) {
                    Intent data = result.getData();
                    String json = data.getStringExtra("selected_data_list");

                    // 反序列化
                    GalleryWallpaperInfo galleryInfo = GsonUtils.fromJson(json, GalleryWallpaperInfo.class);
                    List<LauncherWallpaperInfo> selectedPaths = galleryInfo.getData();
                    List<LauncherWallpaperInfo> collect = selectedPaths.stream().filter(paper -> paper.getHide() == 0).toList();
                    Log.d(TAG, "图片数据: " + collect);
                    ArrayList<VDWallpaperInfo> vdWallpaperInfos = new ArrayList<>();
                    ArrayList<VDWallpaperInfo> galleryWallpapers = WallpaperPresenter.getGalleryWallpapers();
                    collect.forEach(info -> {
                        Log.d("Wallpaper", "图片路径: " + info.getUri());
                        VDWallpaperInfo wallpaperInfo = new VDWallpaperInfo(0, CarWallpaper.WallpaperStyle.IMAGE, 0, info.getUri(), 0f, 0f, 0f, false, 0, 0, "", true, false);
                        wallpaperInfo.type = CarWallpaper.Type.GALLERY;

                        // 检查是否已存在相同URI的壁纸
                        boolean exists = galleryWallpapers.stream()
                                .anyMatch(wp -> wp.uri.equals(wallpaperInfo.uri));

                        if (!exists) {
                            vdWallpaperInfos.add(wallpaperInfo);
                        } else {
                            Log.d(TAG, "壁纸已存在，跳过添加: " + wallpaperInfo.uri);
                        }
                    });

                    WallpaperPresenter.addWallpapers(vdWallpaperInfos);

                }
            }
    );

    private void displayRegLightListen() {
        if (displayManager == null) {
            displayManager = (DisplayManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_DISPLAY_MANAGER);
        }
        displayManager.addCallback(TAG, new IDisplayManagerListener() {

            @Override
            public void getAPCCallback(int status) {

            }

            @Override
            public void getBacklightModeCallback(int status) {

            }

            @Override
            public void getSystemColorCallback(int status) {
                Log.d(TAG, "systemColor--getSystemColorCallback: " + status);
                List<Integer> colors = Arrays.asList(
                        R.color.system_color_blue, R.color.system_color_purple, R.color.system_color_cyan,
                        R.color.system_color_green, R.color.system_color_orange, R.color.system_color_red
                );
                List<Integer> themes = Arrays.asList(
                        R.style.OverlayThemeBlue, R.style.OverlayThemePurple, R.style.OverlayThemeCyan,
                        R.style.OverlayThemeGreen, R.style.OverlayThemeOrange, R.style.OverlayThemeRed
                );
                if (!Objects.equals(Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue), themes.get(status))) {
                    if (systemColorAlert != null) {
                        systemColorAlert.changeSystemColor(colors.get(status));
                    }
                }
            }
        });
        displayManager.registerListener();
    }

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null || binding.scrollView == null) {
            // binding未初始化或视图已销毁，直接返回或打印日志
            Log.w(TAG, "loadPageAnim called but binding or scrollView is null");
            return;
        }
        loadPageAnim(binding.scrollView, currentPosition, position);
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isActive = true;
        displayPresenter = DisplayPresenter.getInstance();
        wallpaperPresenter = new WallpaperPresenter();
        displayHandler = new SafeHandler(this);
        Prefs.registerListener(this);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = getLayoutResId(inflater, container).getRoot();
        // 注册监听事件
        displayRegLightListen();
        initObserver();
        if (Objects.isNull(binding)) {
            return rootView;
        }
        return rootView;
    }

    @Override
    protected FragmentDisplayBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentDisplayBinding.inflate(inflater, container, false);
        return binding;
    }

    @Override
    protected void initView() {
        binding.spvDayNight.setItems(R.string.str_light_mode, R.string.str_night_mode, R.string.str_auto_mode);
        binding.spvFontSize.setItems(R.string.str_display_font_size_little, R.string.str_display_font_size_standard, R.string.str_display_font_size_large);
        ElasticAnimationUtil.applyElasticTouchEffect(binding.btnZKPAUTO);
        ElasticAnimationUtil.applyElasticTouchEffect(binding.btnYBPAUTO);
        ElasticAnimationUtil.applyElasticTouchEffect(binding.btnXDPAUTO);
    }

    protected void initData() {
        viewModel.initData();
        Log.d(TAG, "initData()");
        binding.spvDayNight.setItems(R.string.str_light_mode, R.string.str_night_mode, R.string.str_auto_mode);
        if (DisplayPresenter.getAutoMode()) {
            Log.d(TAG, "显示模式：自动");
            binding.spvDayNight.setSelectedIndex(2, false);
        } else if (!DisplayPresenter.getDisplayMode()) {
            Log.d(TAG, "显示模式：浅色");
            binding.spvDayNight.setSelectedIndex(0, false);
        } else {
            Log.d(TAG, "显示模式：深色");
            binding.spvDayNight.setSelectedIndex(1, false);
        }
        binding.spvFontSize.setItems(R.string.str_display_font_size_little, R.string.str_display_font_size_standard, R.string.str_display_font_size_large);

        if (displayPresenter.getFontSize() == CarDisplay.FONT_SIZE_LITTLE) {
            Log.d(TAG, "字体：小");
            binding.spvFontSize.setSelectedIndex(0, false);
        } else if (displayPresenter.getFontSize() == CarDisplay.FONT_SIZE_STANDARD) {
            Log.d(TAG, "字体：标准");
            binding.spvFontSize.setSelectedIndex(1, false);
        } else if (displayPresenter.getFontSize() == CarDisplay.FONT_SIZE_LARGE) {
            Log.d(TAG, "字体：大");
            binding.spvFontSize.setSelectedIndex(2, false);
        }

        viewModel.setFp(displayPresenter.getFp());
        binding.swShowLyrics.setChecked(DisplayPresenter.getShowLyrics() == CarDisplay.Lyrics.SHOW);
        viewModel.setVideoLimit(displayPresenter.getVideoLimit());
        initSeekbar();
    }

    private void initSeekbar() {
        int zkpBrightness = displayPresenter.getZKPBrightness();
        zkpBrightness = Math.max(CarDisplay.BRIGHT_LIMIT, zkpBrightness);
        Log.d(TAG, "init中控屏亮度：" + zkpBrightness);
        viewModel.setZkp(zkpBrightness);
        binding.sbZkp.setProgress(zkpBrightness);
        binding.sbZkp.setSeekBarStyle(mContext, binding.sbZkp.getSeekBar());
        binding.btnZKPAUTO.setSelected(CommonUtils.IntToBool(displayPresenter.getZKPAuto()));
        binding.btnYBPAUTO.setSelected(CommonUtils.IntToBool(displayPresenter.getYBPAuto()));

        int ybpBrightness = displayPresenter.getYBPBrightness();
        Log.d(TAG, "init仪表屏亮度：" + ybpBrightness);
        binding.sbYbp.setProgress(ybpBrightness);
        binding.sbYbp.setSeekBarStyle(mContext, binding.sbYbp.getSeekBar());
//        int xdpBrightness = displayPresenter.getXDPPBrightness();
        Log.d(TAG, "init吸顶屏亮度：");
//        binding.sbXdp.setProgress(xdpBrightness);
        binding.sbXdp.setSeekBarStyle(mContext, binding.sbXdp.getSeekBar());
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        resolver = requireContext().getContentResolver();

        observer = new ContentObserver(new Handler(Looper.getMainLooper())) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                super.onChange(selfChange, uri);
                int backLight = DisplayLifeCycle.backLight.get();
                if (binding != null && getContext() != null) {
                    switch (uri.getLastPathSegment()) {
                        case PrefsConst.GlobalValue.DISPLAY_FP:
                            int value = Prefs.getGlobalValue(PrefsConst.GlobalValue.DISPLAY_FP, PrefsConst.DefaultValue.DISPLAY_FP);
                            binding.swFp.setChecked(value == 1);
                            Log.d(TAG, "Split screen auto mode:" + value);
                            break;
                        case PrefsConst.GlobalValue.DISPLAY_AUTO_ZKP:
                            int zkpAuto = Prefs.getGlobalValue(PrefsConst.GlobalValue.DISPLAY_AUTO_ZKP, PrefsConst.TRUE);
                            binding.btnZKPAUTO.setSelected(zkpAuto == PrefsConst.TRUE);
                            Log.d(TAG, "Central control auto mode:" + zkpAuto);
                            if (DisplayPresenter.getInstance().getZKPAuto() == PrefsConst.TRUE) {
                                int level = 0;
                                if (DisplayPresenter.getDisplayMode()) {
                                    level = DisplayPresenter.zkpAutoLightReverseNight(backLight);
                                } else {
                                    level = DisplayPresenter.zkpAutoLightReverseDay(backLight);
                                }
                                Log.d(TAG, "Central control level: " + level);
                                int zkpBrightnessLimit = DisplayPresenter.getInstance().getZKPBrightnessLimit(level);
                                DisplayPresenter.getInstance().setZKPBrightness(zkpBrightnessLimit);
                            }
                            break;
                        case PrefsConst.GlobalValue.DISPLAY_AUTO_YBP:
                            int ybpAuto = Prefs.getGlobalValue(PrefsConst.GlobalValue.DISPLAY_AUTO_YBP, PrefsConst.TRUE);
                            binding.btnYBPAUTO.setSelected(ybpAuto == PrefsConst.TRUE);
                            Log.d(TAG, "appearance auto mode: " + ybpAuto);
                            if (DisplayPresenter.getInstance().getYBPAuto() == PrefsConst.TRUE) {
                                int level = 0;
                                if (DisplayPresenter.getDisplayMode()) {
                                    level = DisplayPresenter.ybpAutoLightReverseNight(backLight);
                                } else {
                                    level = DisplayPresenter.ybpAutoLightReverseDay(backLight);
                                }
                                Log.d(TAG, "appearance level: " + level);
                                DisplayPresenter.getInstance().setYBPBrightness(level);
                            }
                            break;
                        case PrefsConst.DISPLAY_MODE:
                            switch (Prefs.getGlobalValue(PrefsConst.DISPLAY_MODE, CarDisplay.DEFAULT_AUTO)) {
                                case CarDisplay.DAY:
                                    Log.d(TAG, "显示模式 浅色");
                                    DisplayPresenter.getInstance().setDisplayMode(CarDisplay.DAY, true);
                                    break;
                                case CarDisplay.NIGHT:
                                    Log.d(TAG, "显示模式 深色");
                                    DisplayPresenter.getInstance().setDisplayMode(CarDisplay.NIGHT, true);
                                    break;
                                case CarDisplay.AUTO:
                                    Log.d(TAG, "显示模式 自动");
                                    DisplayPresenter.getInstance().setDisplayMode(CarDisplay.AUTO, true);
                                    break;
                            }
                            break;
                        default:
                            break;
                    }

                }
            }
        };
        resolver.registerContentObserver(fpSettingUri, false, observer);
        resolver.registerContentObserver(zkpAutoSettingUri, false, observer);
        resolver.registerContentObserver(ybpAutoSettingUri, false, observer);
        resolver.registerContentObserver(displayModeSettingUri, false, observer);

        initData();
    }


    @SuppressLint({"CheckResult", "SuspiciousIndentation"})
    @Override
    protected void setListener() {
        if (binding != null) {
            binding.sbZkp.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    if (progress <= CarDisplay.BRIGHT_LIMIT) {
                        progress = CarDisplay.BRIGHT_LIMIT;
                    }
                    if (fromUser) {
                        int limited = displayPresenter.getZKPBrightnessLimit(progress);
                        displayPresenter.setZKPBrightness(limited);
                        seekBar.setProgress(limited);
                    }
                    binding.sbZkp.setSeekBarStyle(mContext, seekBar);
                }

                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {
                    binding.scrollView.setEnableScroll(false);
                }

                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {
                    binding.scrollView.setEnableScroll(true);
                    SliceReceiver.notifyChange(ProviderURI.LIGHT);
                    displayPresenter.setZKPAuto(PrefsConst.FALSE);
                    binding.btnZKPAUTO.setSelected(false);
                }
            });

            binding.btnZKPAUTO.setOnClickListener(v -> {
                if (displayPresenter.getZKPAuto() == PrefsConst.TRUE) {
                    binding.btnZKPAUTO.setSelected(false);
                    displayPresenter.setZKPAuto(PrefsConst.FALSE);
                } else {
                    binding.btnZKPAUTO.setSelected(true);
                    displayPresenter.setZKPAuto(PrefsConst.TRUE);
                    updateZKPBackLight(PrefsConst.TRUE);
                }
            });

            binding.sbYbp.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    if (progress <= CarDisplay.BRIGHT_LIMIT) {
                        progress = CarDisplay.BRIGHT_LIMIT;
                    }
                    if (fromUser) {
                        displayPresenter.setYBPBrightness(progress);
                        seekBar.setProgress(progress);
                    }
                    binding.sbYbp.setSeekBarStyle(mContext, seekBar);
                }

                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {
                    binding.scrollView.setEnableScroll(false);
                }

                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {
                    binding.scrollView.setEnableScroll(true);
                    displayPresenter.setYBPAuto(PrefsConst.FALSE);
                    binding.btnYBPAUTO.setSelected(false);
                }
            });
            binding.btnYBPAUTO.setOnClickListener(v -> {
                if (displayPresenter.getYBPAuto() == PrefsConst.TRUE) {
                    binding.btnYBPAUTO.setSelected(false);
                    displayPresenter.setYBPAuto(PrefsConst.FALSE);
                } else {
                    binding.btnYBPAUTO.setSelected(true);
                    displayPresenter.setYBPAuto(PrefsConst.TRUE);
                    updateYBPBackLight(PrefsConst.TRUE);
                }
            });
            binding.btnXDPAUTO.setOnClickListener(v -> {
                displayPresenter.setXDPAuto();
            });
            binding.sbXdp.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    if (progress <= 0) {
                        progress = 1;
                    }
                    if (fromUser) {
                        seekBar.setProgress(progress);
                    }
                    binding.sbXdp.setSeekBarStyle(mContext, seekBar);
                }

                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {
                    binding.scrollView.setEnableScroll(false);
                }

                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {
                    binding.scrollView.setEnableScroll(true);
                }
            });
            /* 壁纸 */
            binding.llWallpaper.setOnClickListener(v -> {
                if (wallpaperAlert == null) {
                    Log.d(TAG, "壁纸弹窗对象为null");
                    wallpaperAlert = new WallpaperUIAlert.Builder(mContext);
                    wallpaperAlert.setOpenGalleryListener(() -> {
                        Intent intent = new Intent();
                        GalleryWallpaperInfo galleryWallpaperInfo = new GalleryWallpaperInfo();
                        galleryWallpaperInfo.setData(new ArrayList<>());
                        String galleryWallpaperInfoListJson = GsonUtils.toJson(galleryWallpaperInfo);
                        Log.d(TAG, "galleryWallpaperJson:" + galleryWallpaperInfoListJson);
                        Bundle bundle = new Bundle();
                        bundle.putString("selected_data_list", galleryWallpaperInfoListJson);
                        intent.putExtras(bundle);
                        ComponentName componentName = new ComponentName(
                                "com.mega.chery.gallery",
                                "com.mega.chery.gallery.ui.wallpaper.WallpaperSelectActivity");
                        intent.setComponent(componentName);
                        galleryLauncher.launch(intent);
                    });
                }
                Log.d(TAG, "弹出壁纸弹窗");
                wallpaperAlert.create().show();
            });

            /* 系统色*/
            binding.rlSystemColor.setOnClickListener(v -> {
                if (systemColorAlert == null) {
                    Log.d(TAG, "系统色弹窗对象为null");
                    systemColorAlert = new SystemColorUIAlert.Builder(mContext, displayPresenter);
                }
                Log.d(TAG, "弹出系统色弹窗");
                systemColorAlert.create().show();
            });

            /* 字体 */
            binding.spvFontSize.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    fontSizeSubject.onNext(index);
                }

                @Override
                public void onItemClicked(int index, String text) {
                    // 点击事件入口
                }
            });

            if (init) {
                fontSizeSubject
                        .debounce(1000, TimeUnit.MILLISECONDS)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(index -> {
                            switch (index) {
                                case 0:
                                    displayPresenter.setFontSize(CarDisplay.FONT_SIZE_LITTLE);
                                    break;
                                case 1:
                                    displayPresenter.setFontSize(CarDisplay.FONT_SIZE_STANDARD);
                                    break;
                                case 2:
                                    displayPresenter.setFontSize(CarDisplay.FONT_SIZE_LARGE);
                                    break;
                            }
                        });

                dayNightSubject
                        .debounce(1000, TimeUnit.MILLISECONDS)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(index -> {
                            switch (index) {
                                case 0:
                                    displayPresenter.setDisplayMode(CarDisplay.DAY, true);
                                    break;
                                case 1:
                                    displayPresenter.setDisplayMode(CarDisplay.NIGHT, true);
                                    break;
                                case 2:
                                    displayPresenter.setDisplayMode(CarDisplay.AUTO, true);
                                    break;
                            }
                        });

                fpSubject.debounce(500, TimeUnit.MILLISECONDS)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(status -> {
                            if (status == 0) {
                                Log.d(TAG, "取消分屏");
                                viewModel.setFp(PrefsConst.FALSE);
                                displayPresenter.setFp(PrefsConst.FALSE);
                            } else {
                                Log.d(TAG, "开启分屏");
                                viewModel.setFp(PrefsConst.TRUE);
                                displayPresenter.setFp(PrefsConst.TRUE);
                            }
                        });
            }


            /* 主题 */
            binding.spvDayNight.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    dayNightSubject.onNext(index);
                    if (index == 0) {
                        ThreeDModelUtil.setWeatherTimeState(Car3DModel.WeatherTime.WEATHER_1, Car3DModel.WeatherTime.TIME_2, null);
                    } else if (index == 1) {
                        ThreeDModelUtil.setWeatherTimeState(Car3DModel.WeatherTime.WEATHER_1, Car3DModel.WeatherTime.TIME_4, null);
                    }
                }

                @Override
                public void onItemClicked(int index, String text) {
                    // 点击事件入口
                }
            });


            /* 仪表显示歌词 */
            binding.swShowLyrics.setOnUniversalSwitchChangeListener((switchView, isChecked) -> {
                ShowLyricsUIAlert showLyricsAlert = new ShowLyricsUIAlert(mContext, binding.swShowLyrics);
                if (isChecked) {
                    Log.d(TAG, "仪表显示歌词 弹窗");
                    showLyricsAlert.show();
                } else {
                    displayPresenter.setShowLyrics(PrefsConst.FALSE);
                    binding.swShowLyrics.setChecked(false);
                    Log.d(TAG, "仪表不显示歌词");
                }
            });

            /* 视频限制 */
            binding.swVideoLimit.post(() ->
                    {
                        if (binding == null) {
                            return;
                        }
                        binding.swVideoLimit.setOnCheckedChangeListener((buttonView, isChecked) -> {
                            if (viewModel.videoLimit.getValue() == PrefsConst.FALSE && isChecked == false)
                                return;
                            if (videoLimitAlert == null) {
                                Log.d(TAG, "视频限制弹窗对象为null");
                                videoLimitAlert = new VideoLimitUIAlert.Builder(mContext, binding.swVideoLimit).setOnDialogClickListener(new VideoLimitUIAlert.Builder.OnDialogClickListener() {
                                    @Override
                                    public void onConfirm() {
                                        viewModel.setVideoLimit(PrefsConst.FALSE);
                                        displayPresenter.setVideoLimit(PrefsConst.FALSE);
                                        Log.d(TAG, "确认关闭视频限制");
                                    }

                                    @Override
                                    public void onCancel() {
                                        binding.swVideoLimit.setChecked(true);
                                        Log.d(TAG, "取消关闭视频限制");
                                    }
                                });
                            }
                            VideoLimitUIAlert videoLimitUIAlert = videoLimitAlert.create();
                            if (!isChecked) {
                                Log.d(TAG, "视频限制 弹窗");
                                videoLimitUIAlert.show();
                            } else {
                                viewModel.setVideoLimit(PrefsConst.TRUE);
                                displayPresenter.setVideoLimit(PrefsConst.TRUE);
                                Log.d(TAG, "确认关闭视频限制");
                            }
                        });
                    }
            );

            binding.swFp.setOnCheckedChangeListener((buttonView, isChecked) -> {
                fpSubject.onNext(isChecked ? PrefsConst.TRUE : PrefsConst.FALSE);
            });


            binding.rlClean.setOnClickListener(v -> {
                CleanUIAlert instance = CleanUIAlert.getInstance(mContext);
                instance.setOnBackGestureListener(this);
                instance.show();
            });

            binding.rlReset.setOnClickListener(v -> {
                if (resetUIAlert == null) {
                    resetUIAlert = new ResetDisplayUIAlert.Builder(mContext);
                    resetUIAlert.setOnDialogClickListener(new ResetDisplayUIAlert.Builder.OnDialogClickListener() {

                        @Override
                        public void onConfirm() {
                            displayPresenter.reset();
                            Log.d(TAG, "reset: 系统色");
                            displayPresenter.setSystemColor(DEFAULT_SYSTEM_COLOR);
                            mContext.recreate();
                        }

                        @Override
                        public void onCancel() {
                        }
                    });
                }
                resetUIAlert.create().show();
            });

            binding.rlProtect.setOnClickListener(v -> {
                // TODO 临时方案 先发送信号
                MsgUtil.getInstance().setSignalVal("DrivingInfo/PowerKey", 1);
                try {
                    Thread.sleep(300);
                } catch (InterruptedException e) {
                    return;
                }
                MsgUtil.getInstance().setSignalVal("DrivingInfo/PowerKey", 0);
//                ScreensaverConfig config = ScreensaverConfigHelper.getConfig(mContext);
//                Log.d(TAG, "屏保: " + config);
            });


            // 系统色
            SystemColorUIAlert.setOnProgressChangedListener(new SystemColorUIAlert.onProgressChangedListener() {
                @Override
                public int getSystemColor() {
                    String globalSystemColor = MyApplication.getInstance().getSystemColorValue();
                    return SystemColorUtil.SystemColorValueToStyle(globalSystemColor);
                }

                @Override
                public void setSystemColor(int selectedColor) {
                    displayPresenter.setSystemColor(selectedColor);
                    mContext.recreate();
                }
            });
        }
    }

    @Override
    protected void initObserve() {

    }

    @Override
    public void onResume() {
        super.onResume();
        initView();
        initData();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isActive = false;
        Prefs.unregisterListener(this);
    }


    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);

        outState.putString("TAG", TAG);
    }

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            default:
                break;
        }
    }

    @Override
    public boolean isActive() {
        return isActive;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 注销 ContentObserver，防止泄漏或空指针
        if (resolver != null && observer != null) {
            resolver.unregisterContentObserver(observer);
            Log.d(TAG, "已注销 ContentObserver");
            observer = null;
        }

        if (binding != null) {
            binding = null;
        }

        if (displayHandler != null) {
            displayHandler.dispose();
        }
    }


    /**
     * ui更新
     */
    private void initObserver() {
        //MainActivity的ViewModel
        mainActViewModel = new ViewModelProvider(requireActivity()).get(MainActViewModel.class);
        processTargetDialogEvent(mainActViewModel.getTargetDialogLiveEvent().getValue());
        mainActViewModel.getTargetDialogLiveEvent().observe(getViewLifecycleOwner(), this::processTargetDialogEvent);
        viewModel = new ViewModelProvider(this).get(DisplayViewModel.class);
        viewModel.getVideoLimit().observe(this, status -> {
            binding.swVideoLimit.setChecked(CommonUtils.IntToBool(status));
        });
        viewModel.getZkp().observe(this, progress -> {
            Log.d(TAG, "中控屏view: " + progress);
            binding.sbZkp.setProgress(progress);
        });
        viewModel.getFp().observe(this, status -> {
            Log.d(TAG, "分屏view: " + status);
            binding.swFp.setChecked(CommonUtils.IntToBool(status));
        });
        viewModel.apcLevelLiveData.observe(this, this::updateZKP);
        viewModel.backLightLiveData.observe(this, (value) -> {
            updateZKPBackLight(value);
            updateYBPBackLight(value);
        });
    }

    private void processTargetDialogEvent(TargetDialogInfo targetDialog) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= " + targetDialog);
        if (targetDialog == null) {
            return;
        }
        //具体Tab索引
        if (targetDialog.getTargetTab() == MainActivity.MainTabIndex.DISPLAY) {
            switch (targetDialog.getTargetDialog()) {
                case WallpaperType:
                    if (targetDialog.getOperation() == 1) {
                        if (wallpaperAlert == null) {
                            Log.d(TAG, "壁纸弹窗对象为null");
                            wallpaperAlert = new WallpaperUIAlert.Builder(mContext);
                            wallpaperAlert.setOpenGalleryListener(() -> {
                                Intent intent = new Intent();
                                GalleryWallpaperInfo galleryWallpaperInfo = new GalleryWallpaperInfo();
                                galleryWallpaperInfo.setData(new ArrayList<>());
                                String galleryWallpaperInfoListJson = GsonUtils.toJson(galleryWallpaperInfo);
                                Log.d(TAG, "galleryWallpaperJson:" + galleryWallpaperInfoListJson);
                                Bundle bundle = new Bundle();
                                bundle.putString("selected_data_list", galleryWallpaperInfoListJson);
                                intent.putExtras(bundle);
                                ComponentName componentName = new ComponentName(
                                        "com.mega.chery.gallery",
                                        "com.mega.chery.gallery.ui.wallpaper.WallpaperSelectActivity");
                                intent.setComponent(componentName);
                                galleryLauncher.launch(intent);
                            });
                        }
                        Log.d(TAG, "弹出壁纸弹窗");
                        wallpaperAlert.create().show();
                    } else {
                        wallpaperAlert.create().dismiss();
                    }
                    break;
            }
        }
    }

    private void updateZKP(Integer state) {
        if (state == CarNewEnergy.ApcLevelLimit.LEVEL_4 || state == CarNewEnergy.ApcLevelLimit.LEVEL_5) {
            int zkpBrightness = displayPresenter.getZKPBrightness();
            int zkpBrightnessLimit = displayPresenter.getZKPBrightnessLimit(zkpBrightness);
            displayPresenter.setZKPBrightness(zkpBrightnessLimit);
            if (Objects.isNull(binding)) {
                return;
            }
            binding.sbZkp.setProgress(zkpBrightnessLimit);
            binding.sbZkp.setSeekBarStyle(getActivity(), binding.sbZkp.getSeekBar());
            SliceReceiver.notifyChange(ProviderURI.LIGHT);
        }
    }

    public void updateZKPBackLight(Integer state) {
        if (displayPresenter.getZKPAuto() != PrefsConst.TRUE) {
            Log.d(TAG, "中控屏未开启自动亮度: " + state);
            return;
        }
        int level = 0;
        if (DisplayPresenter.getDisplayMode()) {
            level = DisplayPresenter.zkpAutoLightReverseNight(state);
        } else {
            level = DisplayPresenter.zkpAutoLightReverseDay(state);
        }
        Log.d(TAG, "lifecycle--getBacklightModeCallback--中控屏level: " + level);
        int zkpBrightnessLimit = DisplayPresenter.getInstance().getZKPBrightnessLimit(level);
        DisplayPresenter.getInstance().setZKPBrightness(zkpBrightnessLimit);
        if (Objects.isNull(binding)) {
            return;
        }
        binding.sbZkp.setProgress(zkpBrightnessLimit);
        binding.sbZkp.setSeekBarStyle(getActivity(), binding.sbZkp.getSeekBar());
        SliceReceiver.notifyChange(ProviderURI.LIGHT);
    }

    public void updateYBPBackLight(Integer state) {
        if (displayPresenter.getYBPAuto() != PrefsConst.TRUE) {
            Log.d(TAG, "仪表盘未开启自动亮度: " + state);
            return;
        }
        int level = 0;
        if (DisplayPresenter.getDisplayMode()) {
            level = DisplayPresenter.ybpAutoLightReverseNight(state);
        } else {
            level = DisplayPresenter.ybpAutoLightReverseDay(state);
        }
        Log.d(TAG, "lifecycle--getBacklightModeCallback--仪表盘level: " + level);
        DisplayPresenter.getInstance().setYBPBrightness(level);
        if (Objects.isNull(binding)) {
            return;
        }
        binding.sbYbp.setProgress(level);
        binding.sbYbp.setSeekBarStyle(getActivity(), binding.sbYbp.getSeekBar());
    }

    @Override
    public void onInterceptBackGesture(boolean intercept) {
        // 在这里可以什么都不做，或者通知 Activity
        if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).onInterceptBackGesture(intercept);
        }
    }


    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, @Nullable String key) {
        if (binding == null || !isAdded()) {
            return;
        }
        switch (key) {

            case PrefsConst.DISPLAY_ZKP:
                binding.sbZkp.setProgress(Prefs.getInt(PrefsConst.DISPLAY_ZKP));
                break;
            case PrefsConst.DISPLAY_YBP:
                binding.sbYbp.setProgress(Prefs.getInt(PrefsConst.DISPLAY_YBP));
                break;
        }
    }

}
