package com.bitech.vehiclesettings.view.display;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertDisplayWallpaperDetailBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class WallpaperDetailUIAlert extends BaseDialog {
    private static final String TAG = WallpaperDetailUIAlert.class.getSimpleName();


    public WallpaperDetailUIAlert(Context context) {
        super(context);
    }

    public WallpaperDetailUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected WallpaperDetailUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private WallpaperDetailUIAlert dialog = null;
        private View layout;
        private DialogAlertDisplayWallpaperDetailBinding binding;

        public Builder(Context context) {
            this.context = context;
        }


        public WallpaperDetailUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public WallpaperDetailUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new WallpaperDetailUIAlert(context,R.style.Dialog);
            binding = DialogAlertDisplayWallpaperDetailBinding.inflate(LayoutInflater.from(context));

            binding.btnConfirm.setOnClickListener(v -> dialog.cancel());
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);

            return dialog;
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }


}
