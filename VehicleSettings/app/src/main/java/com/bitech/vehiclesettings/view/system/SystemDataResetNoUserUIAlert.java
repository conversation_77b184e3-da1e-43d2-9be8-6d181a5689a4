package com.bitech.vehiclesettings.view.system;

import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.LinearInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSVerifyInfoNoUserBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;

public class SystemDataResetNoUserUIAlert extends Dialog {
    private static final String TAG = SystemDataResetNoUserUIAlert.class.getSimpleName();
    private static OnProgressChangedListener onProgressChangedListener;

    public SystemDataResetNoUserUIAlert(@NonNull Context context) {
        super(context);
        initSystemWindow();
    }

    public SystemDataResetNoUserUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        initSystemWindow();
    }

    protected SystemDataResetNoUserUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        initSystemWindow();
    }

    private void initSystemWindow() {
        Window window = getWindow();
        if (window != null) {
            // 设置为系统级窗口（覆盖状态栏和 Dock）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ERROR);
            } else {
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }

            // 关键参数：全屏 + 覆盖系统 UI + 阻止交互
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;
            params.flags =
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                            | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                            | WindowManager.LayoutParams.FLAG_DIM_BEHIND;

            params.dimAmount = 0.6f; // 背景变暗程度
            window.setAttributes(params);
        }
    }

    public static OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = false;
        private DialogAlertSVerifyInfoNoUserBinding binding;
        private SystemDataResetNoUserUIAlert dialog;
        private float alphaScale = 0.3f;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCancelable(boolean isCancelable) {
            this.isCancelable = isCancelable;
            return this;
        }

        public SystemDataResetNoUserUIAlert create() {
            dialog = new SystemDataResetNoUserUIAlert(context, R.style.Dialog);
            binding = DialogAlertSVerifyInfoNoUserBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            // 设置窗口大小（可调整）
            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1176;  // 固定宽度
                layoutParams.height = 548; // 固定高度
                window.setAttributes(layoutParams);
            }

            setupListeners();
            return dialog;
        }

        private void setupListeners() {
            // 恢复出厂设置按钮
            binding.tvSysResetConfirm.setOnClickListener(view -> performFactoryReset());

            // 取消按钮
            binding.tvSysResetCancel.setOnClickListener(v -> dialog.dismiss());
        }

        private void performFactoryReset() {
            binding.ivResetConfirm.setVisibility(View.VISIBLE);
            ObjectAnimator rotationAnimator = ObjectAnimator.ofFloat(
                    binding.ivResetConfirm,
                    "rotation",
                    0f, 360f
            );
            rotationAnimator.setDuration(7000);
            rotationAnimator.setRepeatCount(ObjectAnimator.INFINITE);
            rotationAnimator.setInterpolator(new LinearInterpolator());
            rotationAnimator.start();

            GrayEffectUtils.applyGrayEffect(binding.llResetConfirm);
            if (onProgressChangedListener != null) {
                onProgressChangedListener.factoryReset();
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface OnProgressChangedListener {
        void factoryReset();
    }
}