package com.bitech.vehiclesettings.view.carsetting;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.SegmentItemBean;
import com.bitech.vehiclesettings.databinding.DialogAlertCDriveAirbagBinding;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class DriveAirbagUIAlert extends BaseDialog {
    private static final String TAG = DriveAirbagUIAlert.class.getSimpleName();
    private static DriveAirbagUIAlert.onProgressChangedListener onProgressChangedListener;

    public DriveAirbagUIAlert(@NonNull Context context) {
        super(context);
    }

    public DriveAirbagUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected DriveAirbagUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static DriveAirbagUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(DriveAirbagUIAlert.onProgressChangedListener onProgressChangedListener) {
        DriveAirbagUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertCDriveAirbagBinding binding;
        private boolean switchDriveBagFlag = true;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private DriveAirbagUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public DriveAirbagUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public DriveAirbagUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new DriveAirbagUIAlert(context,
                        R.style.Dialog);
            binding = DialogAlertCDriveAirbagBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128;
            layoutParams.height = 508;
            window.setAttributes(layoutParams);

            initSegmentPickerView();
            // 设置切换选项的事件
            setSwitchEvent();
            return dialog;
        }

        DriveAirbagConfirmUIAlert.Builder driveAirbagConfirmUIAlert;

        private void setSwitchEvent() {
            binding.spvDriveAirbag.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    if (switchDriveBagFlag) {
                        driveAirbagConfirmUIAlert = new DriveAirbagConfirmUIAlert.Builder(context, binding.spvDriveAirbag.getSelectedIndex());
                        driveAirbagConfirmUIAlert.create().show();
                    } else {
                        switchSuccess(binding.spvDriveAirbag.getSelectedIndex());
                        switchDriveBagFlag = true;
                    }
                }

                @Override
                public void onItemClicked(int index, String text) {

                }
            });

            DriveAirbagConfirmUIAlert.setOnProgressChangedListener((alert, type) -> {
                switchSuccess(type);
            });
        }

        private void switchSuccess(int index) {
            binding.spvDriveAirbag.setChangeable(true);
            binding.spvDriveAirbag.setSelectedListen(false);
            binding.spvDriveAirbag.setSelectedIndex(index == 0 ? 1 : 0, true);
            onProgressChangedListener.switchSuccess(binding.spvDriveAirbag.getSelectedIndex());
            binding.spvDriveAirbag.setSelectedListen(true);
            binding.spvDriveAirbag.setChangeable(false);
        }

        public DialogAlertCDriveAirbagBinding getBinding() {
            return binding;
        }

        private void initSegmentPickerView() {
            binding.spvDriveAirbag.setItems(
                    new SegmentItemBean(R.mipmap.ic_airbag_close, R.mipmap.ic_airbag_close_selected, context.getString(R.string.str_carsetting_safe_close)),
                    new SegmentItemBean(R.mipmap.ic_airbag_open, R.mipmap.ic_airbag_open_selected, context.getString(R.string.str_carsetting_safe_open))
            );
            binding.spvDriveAirbag.setSelectedIndex(onProgressChangedListener.getInitStatus(), false);
            // 设置不可主动切换
            binding.spvDriveAirbag.setChangeable(false);
            onProgressChangedListener.onSwitch(binding);
        }

        public void updateDriveAirBagUI(Integer status) {
            if (dialog != null) {
                binding.spvDriveAirbag.setSelectedIndex(status, true);
            }
        }

        public void setSwitchDriveBagFlag(boolean switchDriveBagFlag) {
            this.switchDriveBagFlag = switchDriveBagFlag;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(DialogAlertCDriveAirbagBinding binding);

        void switchSuccess(int index);

        int getInitStatus();
    }
}
