package com.bitech.vehiclesettings.view.condition;

import static androidx.databinding.adapters.ViewGroupBindingAdapter.setListener;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarCondition;
import com.bitech.vehiclesettings.databinding.DialogAlertConditionRepairCheckBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.SingleSwitchListener;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class RepairCheckUIAlert extends BaseDialog {
    private static final String TAG = RepairCheckUIAlert.class.getSimpleName();
    private static RepairCheckUIAlert.onProgressChangedListener onProgressChangedListener;

    public RepairCheckUIAlert(@NonNull Context context) {
        super(context);
    }

    public RepairCheckUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected RepairCheckUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static RepairCheckUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(RepairCheckUIAlert.onProgressChangedListener onProgressChangedListener) {
        RepairCheckUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private RepairCheckUIAlert.OnDialogResultListener listener;

    public static class Builder {
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConditionRepairCheckBinding binding;
        private SunShadeCurtainModeUIAlert.Builder sunShadeCurtainModeUIAlert;
        private Integer wiperRepairMode = 0;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private RepairCheckUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public RepairCheckUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public RepairCheckUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new RepairCheckUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertConditionRepairCheckBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 644;
            window.setAttributes(layoutParams);
            initData();
            binding.swWiperRepair.setOnCheckedChangeListener(new SingleSwitchListener() {
                @Override
                public void onSingleCheckedChanged(@NonNull CompoundButton buttonView, boolean isChecked) {
                    onProgressChangedListener.setWiperRepair(isChecked);
                }
            });
            binding.viewSwitchOverlay.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getOnProgressChangedListener().getWiperRepairState() == CarCondition.FLZCU_WipeMntnModeFailCause.INHIBITION_MODE) {
                        EToast.showToast(context, context.getString(R.string.str_wiper_repair_disable_mode_1), 0, false);
                    } else if (getOnProgressChangedListener().getWiperRepairState() == CarCondition.FLZCU_WipeMntnModeFailCause.GEAR_P_ONLY) {
                        EToast.showToast(context, context.getString(R.string.str_wiper_repair_disable_mode_2), 0, false);
                    } else if (getOnProgressChangedListener().getWiperRepairState() == CarCondition.FLZCU_WipeMntnModeFailCause.BOTH) {
                        EToast.showToast(context, context.getString(R.string.str_wiper_repair_disable_mode_3), 0, false);
                    }
                }
            });
            binding.rlSunShadeCurtainRepairMode.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onProgressChangedListener.setSunShadeCurtainMode();
                }
            });
            return dialog;
        }

        private void initData() {
            // 若触发条件
            // 1，则雨刮维修模式开关置灰;若用戶点击置灰的开关，则大屏弹窗显示置灰原因“前备箱开启时,禁用雨刮维修模式”;
            // 2. 若触发条件2，则雨刮维修模式开关置灰;若用戶点击置灰的开关，则大屏弹窗显示置灰原因“雨刮维修模式只能在P档下开启”;
            // 3. 若机械雨刮配置有效，若触发条件c，则雨刮维修模式开关关置灰，若用戶点击置灰的开关，则大屏弹窗显示置灰原因“请先将前雨刮位置还原，档位调整到到P档下，雨刮开关拨到OFF下，关闭前备箱，再开启雨刮维修模式”;
            // 4. 若电子雨刮配置有效，若触发条件c，则雨刮维修模式开关置置灰，若用戶点击置灰的开关，则大屏弹窗显示置灰原因“雨刮维修模式只能在P档下，雨刮档位为OFF，雨刮归位且前备箱关闭时开启”;
            int signalVal = onProgressChangedListener.getWiperRepairState();
            updateWiperRepairDisable(signalVal);
            // 雨刮维修模式
            int wiperRepairMode = onProgressChangedListener.getWiperRepairMode();
            updateWiperRepairMode(wiperRepairMode);
            // 遮阳帘维修模式
            int sunShadeCurtainMode = onProgressChangedListener.getSunShadeCurtainMode();
            updateSunShadeRepairModeUI(sunShadeCurtainMode);
        }

        public void updateWiperRepairDisable(Integer signalVal) {
            if (signalVal == CarCondition.FLZCU_WipeMntnModeFailCause.NOT_ACTIVE) {
                GrayEffectUtils.removeGrayEffect(binding.llWiperRepairMode);
                binding.viewSwitchOverlay.setVisibility(View.GONE);
            } else if (signalVal == CarCondition.FLZCU_WipeMntnModeFailCause.INHIBITION_MODE ||
                    signalVal == CarCondition.FLZCU_WipeMntnModeFailCause.GEAR_P_ONLY ||
                    signalVal == CarCondition.FLZCU_WipeMntnModeFailCause.BOTH) {
                GrayEffectUtils.applyGrayEffect(binding.llWiperRepairMode);
                binding.viewSwitchOverlay.setVisibility(View.VISIBLE);
                binding.viewSwitchOverlay.setEnabled(true);
            }
        }

        public void updateWiperRepairMode(Integer signalVal) {
            if (signalVal == CarCondition.FLZCU_WipeMaintenanceSWSts.NOT_ACTIVE
                    || signalVal == CarCondition.FLZCU_WipeMaintenanceSWSts.FRONT_WIPER ||
                    signalVal == CarCondition.FLZCU_WipeMaintenanceSWSts.REAR_WIPER) {
                binding.swWiperRepair.setChecked(false);
            } else {
                binding.swWiperRepair.setChecked(true);
            }
        }

        public void updateSunShadeRepairModeUI(Integer status) {
            if (status == 1) {
                GrayEffectUtils.applyGrayEffect(binding.rlSunShadeCurtainRepairMode);
            } else if (status == 0) {
                GrayEffectUtils.removeGrayEffect(binding.rlSunShadeCurtainRepairMode);
            }
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void setWiperRepair(boolean isChecked);

        int getWiperRepairMode();

        int getWiperRepairState();

        void setSunShadeCurtainMode();

        int getSunShadeCurtainMode();
    }
}
