package com.bitech.vehiclesettings.view.common;

import android.content.Context;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.widget.ImageView;

public class ZoomTouchListener implements View.OnTouchListener {

    private ScaleGestureDetector scaleGestureDetector;
    private float scaleFactor = 1.0f;

    public ZoomTouchListener(Context context) {
        // 修正构造函数，传递 context 和监听器
        scaleGestureDetector = new ScaleGestureDetector(context, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                scaleFactor *= detector.getScaleFactor();
                scaleFactor = Math.max(0.5f, Math.min(scaleFactor, 3.0f)); // 限制缩放范围
                return true;
            }
        });
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if (v instanceof ImageView) {
            scaleGestureDetector.onTouchEvent(event);
            v.setScaleX(scaleFactor);
            v.setScaleY(scaleFactor);
            return true;
        }
        return false;
    }
}

