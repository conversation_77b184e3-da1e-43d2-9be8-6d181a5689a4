package com.bitech.vehiclesettings.view.negative;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.databinding.DialogAlertNConfirmExitParkPowerBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertNParkPowerBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class ParkPowerCancelUIAlert extends BaseDialog {
    public ParkPowerCancelUIAlert(@NonNull Context context) {
        super(context);
    }

    public ParkPowerCancelUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ParkPowerCancelUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertNConfirmExitParkPowerBinding binding;
        NewEnergyManager newEnergyManager = NewEnergyManager.getInstance();

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private ParkPowerCancelUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public ParkPowerCancelUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public ParkPowerCancelUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new ParkPowerCancelUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertNConfirmExitParkPowerBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            // 获取对话框的Window对象
            init();
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 502;
            layoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
            window.setAttributes(layoutParams);
            return dialog;
        }

        public void init() {
            binding.btnCancel.setOnClickListener(v -> this.dismiss());

            binding.btnConfirm.setOnClickListener(v -> {
                Log.d("ParkPowerCancelUIAlert", "确认退出驻车保电");
                newEnergyManager.setParkPowerStatus(CarNewEnergy.ParkPowerSet.OFF);
                this.dismiss();
            });
        }


        private void dismiss() {
            dialog.dismiss();
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }


}
