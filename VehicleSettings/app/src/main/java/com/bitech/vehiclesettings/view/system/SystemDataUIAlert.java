package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.adapter.PrivacyStatementAdapter;
import com.bitech.vehiclesettings.adapter.SystemDataAdapter;
import com.bitech.vehiclesettings.bean.PrivacyStatementBean;
import com.bitech.vehiclesettings.bean.SystemDataBean;
import com.bitech.vehiclesettings.databinding.DialogAlertSDataBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSPrivacyStatementBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.common.NoToggleSwitch;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.ArrayList;
import java.util.List;

public class SystemDataUIAlert extends BaseDialog {
    private static final String TAG = SystemDataUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public SystemDataUIAlert(@NonNull Context context) {
        super(context);
    }

    public SystemDataUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected SystemDataUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static SystemDataUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        SystemDataUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSDataBinding binding;


        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        public SystemDataUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public SystemDataUIAlert create(String title, int type) {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new SystemDataUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSDataBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128;
            layoutParams.height = 800;
            window.setAttributes(layoutParams);

            // 初始化标题
            initTitle(title);
            // 初始化内容
            initItem(type);

            return dialog;
        }

        private void initTitle(String title) {
            binding.tvTitle.setText(title);
        }

        List<SystemDataBean> SystemDataList = new ArrayList<>();

        @SuppressLint("RtlHardcoded")
        private void initItem(int type) {
            List<SystemDataBean> systemDatatList = onProgressChangedListener.getSystemDatatList(type);
            if (!systemDatatList.isEmpty()) {
                this.SystemDataList = systemDatatList;
            }
            SystemDataAdapter adapter = new SystemDataAdapter(context, this.SystemDataList);

            adapter.setOnSwitchChangeListener((item, position, noToggleSwitch) -> {
                onProgressChangedListener.setSystemData(item, position, noToggleSwitch, type);
            });

            binding.rvData.setLayoutManager(new LinearLayoutManager(context));
            binding.rvData.setAdapter(adapter);
        }


        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        List<SystemDataBean> getSystemDatatList(int type);

        int setSystemData(SystemDataBean item, int position, NoToggleSwitch noToggleSwitch, int type);
    }
}
