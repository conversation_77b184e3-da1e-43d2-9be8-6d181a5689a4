package com.bitech.vehiclesettings.view.connect

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.content.DialogInterface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.Button
import android.widget.ImageView
import android.widget.Switch
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.adapter.BtPairedListAdapter
import com.bitech.vehiclesettings.adapter.BtScanListAdapter
import com.bitech.vehiclesettings.adapter.NoCrashLinearLayoutManager
import com.bitech.vehiclesettings.base.kt.BaseDialogFragment
import com.bitech.vehiclesettings.bean.BtDeviceBean
import com.bitech.vehiclesettings.common.PackageConstants
import com.bitech.vehiclesettings.databinding.FragmentBtBinding
import com.bitech.vehiclesettings.manager.CarBtManager
import com.bitech.vehiclesettings.manager.CarDmManager
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.HideSoftInputCallback
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.PhoneLink
import com.bitech.vehiclesettings.utils.TextUtil
import com.bitech.vehiclesettings.view.dialog.ConfirmDialog
import com.bitech.vehiclesettings.view.dialog.DMConfirmDialog
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.bitech.vehiclesettings.viewmodel.BtViewModel
import com.chery.adapter.androidauto.connection.devicelist.ConnectState
import com.chery.adapter.common.config.IConstant
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.client.bind.VDThreadType
import com.chery.ivi.vdb.client.listener.VDNotifyListener
import com.chery.ivi.vdb.event.VDEvent
import com.chery.ivi.vdb.event.base.VDKey
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink
import com.chery.ivi.vdb.event.id.phonelink.VDValuePhoneLink
import com.chery.ivi.vdb.event.id.phonelink.bean.VDLinkDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList


/**
 * @ClassName: BtFragment
 * 
 * @Date:  2024/1/18 14:26
 * @Description: 蓝牙页面Fragment.
 **/
class BtFragment : BaseDialogFragment<FragmentBtBinding, BtViewModel>(), View.OnClickListener,
    BtPairedListAdapter.OnBtItemClickIconCallback, HideSoftInputCallback {

    // 页面内部所有TextView集合
    private val textViews = mutableListOf<TextView>()

    // 蓝牙名称
    private var btName: String? = null

    // 蓝牙扫描列表适配器
    private lateinit var btScanListAdapter: BtScanListAdapter

    // 蓝牙配对列表适配器
    private lateinit var btPairedListAdapter: BtPairedListAdapter

    // 蓝牙页面提示弹窗
    private var confirmDialog: ConfirmDialog? = null

    // 自定义Toast
    private var toast: SettingsToast? = null

    // 是否需要蓝牙自动连接
    private var isAutoConnected = false

    private var phoneLinkName = ""

    // 内部NestedScrollView相对于屏幕顶部的距离
    private var nestedScrollViewTop = -1

    private var isToOpenBluetoothAndFound  = false

    // 在 BtFragment 类中添加
    private var isHandlingBluetoothOperation = false
    // 在 BtFragment 中添加
    private val handler = Handler(Looper.getMainLooper())

    private  var isSelfHidden =  false

    //互联状态类
    private val phoneLink = PhoneLink()
    /**
     * 视图绑定.
     *
     * @return getLayoutId
     */
    override fun getLayoutId(container: ViewGroup?): FragmentBtBinding {
        return FragmentBtBinding.bind(
                layoutInflater.inflate(
                    R.layout.fragment_bt,
                    container,
                    false
                )
            )
    }

    /**
     * viewModel绑定。
     *
     * @return BtViewModel
     */
    override fun getViewModel(): Class<BtViewModel> {
        return BtViewModel::class.java
    }

    /**
     * 初始化视图，onViewCreated()中调用.
     *
     */
    override fun initView() {
        LogUtil.d(TAG, "initView : ")
        // 初始化可搜索TextView
        initSearchTextView()
        // 初始化页面开关状态
        initSwStatusUi()
        btScanListAdapter = BtScanListAdapter(viewModel.getBtScanList()) { btDeviceBean ->
            // 点击扫描列表蓝牙,进行配对
            viewModel.startPairedBt(btDeviceBean)
        }
        btPairedListAdapter = BtPairedListAdapter(requireContext(),CopyOnWriteArrayList<BtDeviceBean>())
        btPairedListAdapter.setOnBtItemClickIconCallback(this)
        // 蓝牙扫描列表布局设置
        viewBinding.settingsCanUseDevicesRv.layoutManager = NoCrashLinearLayoutManager(requireContext())
        viewBinding.settingsCanUseDevicesRv.adapter = btScanListAdapter
        // 蓝牙配对列表布局设置
        viewBinding.settingsPairedDevicesRv.layoutManager = NoCrashLinearLayoutManager(requireContext())
        viewBinding.settingsPairedDevicesRv.adapter = btPairedListAdapter
        VDBus.getDefault().addSubscribe(VDEventPhoneLink.PHONE_STATE)
        VDBus.getDefault().subscribeCommit()
        VDBus.getDefault().registerVDNotifyListener(phoneLinkListener)
    }

    //互联监听
    private val phoneLinkListener = object : VDNotifyListener {
        override fun onVDNotify(event: VDEvent, threadType: Int) {
            if (threadType != VDThreadType.MAIN_THREAD) return
            when (event.id) {
                VDEventPhoneLink.PHONE_STATE -> {
                    val bundle = event.payload ?: return
                    phoneLink.status = bundle.getInt(VDKey.STATUS)
                    phoneLink.type = bundle.getInt(VDKey.TYPE)
                    phoneLink.isWireless =
                        bundle.getBoolean(VDKey.ENABLE)
                    Log.d(TAG, "onVDNotify: 手机互联监听状态" + phoneLink.status)
                    if (phoneLink.status > 0) {
                        viewBinding.btPhonelinkPaired.visibility = View.VISIBLE
                    } else if (phoneLink.status == 0) {
                        viewBinding.btPhonelinkPaired.visibility = View.GONE
                        Log.d(TAG, "onVDNotify: 手机互联监听状态:互联已断开")
                    }
                    if (phoneLink.type == PhoneLink.TYPE_CARPLAY) {
                        phoneLink.typeName = PhoneLink.TYPE_CARPLAY_NAME
                    } else if (phoneLink.type == PhoneLink.TYPE_CARLINK) {
                        phoneLink.typeName = PhoneLink.TYPE_CARLINK_NAME
                    } else if (phoneLink.type == PhoneLink.TYPE_HICAR) {
                        phoneLink.typeName = PhoneLink.TYPE_HICAR_NAME
                    }
                    // TODO: 根据 status / type / isWireless 做业务
                }
            }
        }
    }
    /**
     * 初始化各类监听，onViewCreated()中调用.
     *
     */
    @SuppressLint("ClickableViewAccessibility")
    override fun intiListener() {
        LogUtil.d(TAG, "intiListener : ")
        viewBinding.apply {
            // 页面内容点击事件监听
            settingsBtContentCl.setOnClickListener(this@BtFragment)
            // 蓝牙开关
            settingBtSw.isEnabled = true
            settingBtSw.setOnClickListener {
                val isChecked = settingBtSw.isChecked
                val state = CarBtManager.instance.getBluetoothStatus()
                LogUtil.d(TAG, "intiListener : bt switch is = $isChecked , state = $state")
                if (isChecked) {
                    // 打开蓝牙
                    settingBtSw.isEnabled = false
                    isHandlingBluetoothOperation = true
                    viewModel.setBluetoothStatus(true)
                    handler.postDelayed({
                        isHandlingBluetoothOperation = false
                        settingBtSw.isChecked=viewModel.getBluetoothStatus()
                    }, 2000) // 2秒后自动重置
                } else {
                    if (viewModel.hasConnectDeviceAa()) {
                        // 当前连接的AA，进行弹窗提示
                        // 蓝牙开关复位
                        viewBinding.settingBtSw.isChecked = !viewBinding.settingBtSw.isChecked
                         showDisConnectAaDialog(null)
                    } else {
                         // 关闭蓝牙
                         settingBtSw.isEnabled = false
                         viewModel.setBluetoothStatus(false)
                    }
                }
            }
            // 配对列表滑动事件监听处理.
            settingsPairedDevicesRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    // 解决与ScanScrollView的滑动冲突事件
                    setBtScrollViewCanScroll(newState)
                }
            })
            // 扫描列表滑动事件监听处理.
            settingsCanUseDevicesRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    // 解决与ScanScrollView的滑动冲突事件
                    setBtScrollViewCanScroll(newState)
                }
            })
            // 搜索图标监听
            settingsBtStartScanIv.setOnClickListener(this@BtFragment)
            // 互联的蓝牙icon图标监听
            btIcon.setOnClickListener {
                val event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
                if (event != null) {
                    val bundle = event.payload
                    val devicePhoneLink = bundle.getParcelable<VDLinkDevice>(VDKey.INFO)
                    val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                    val newDevice =
                        BtDeviceBean(bluetoothAdapter!!.getRemoteDevice(devicePhoneLink?.btAddress))
                    viewModel.connectDevice(newDevice)
                }
            }
            btPhonelinkPaired.setOnClickListener {
                showDeletePhoneLinkDeviceDialog()
            }
        }
    }

    /**
     * 删除互联设备时提示弹窗显示.
     *
     * @param deleteDevice 待删除的设备.
     */
    private fun showDeletePhoneLinkDeviceDialog() {
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                // 移除已配对的蓝牙设备
                val event1 = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
                if (event1 != null) {
                    val bundle = event1.payload
                    val type = bundle.getInt(VDKey.TYPE)
                    val devicePhoneLink = bundle.getParcelable<VDLinkDevice>(VDKey.INFO)
                    val payload = Bundle()
                    if (type == PhoneLink.TYPE_CARLINK) {
                        payload.putInt(
                            VDKey.TYPE,
                            VDValuePhoneLink.ServerId.CARLINK
                        )
                    } else if (type == PhoneLink.TYPE_CARPLAY) {
                        payload.putInt(
                            VDKey.TYPE,
                            VDValuePhoneLink.ServerId.CARPLAY
                        )
                    } else if (type == PhoneLink.TYPE_HICAR) {
                        payload.putInt(
                            VDKey.TYPE,
                            VDValuePhoneLink.ServerId.HICAR
                        )
                    }
                    payload.putParcelable(VDKey.DATA, devicePhoneLink) //(VDLinkDevice)device,要断开的设备
                    val event = VDEvent(VDEventPhoneLink.DISCONNECT_DEVICE, payload)
                    VDBus.getDefault().set(event)
                }
                showSelf()
                cancelDialog()
            }

            override fun onCancelClick() {
                // 取消按钮被点击
                showSelf()
                cancelDialog()
            }
        })
        confirmDialog?.setOnDismissListener(object : DialogInterface.OnDismissListener {
            override fun onDismiss(dialog: DialogInterface?) {
                showSelf()
            }
        })
        val event1 = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
        if (event1 != null) {
            val bundle = event1.payload
            val devicePhoneLink = bundle.getParcelable<VDLinkDevice>(VDKey.INFO)
            phoneLinkName = devicePhoneLink?.name.toString()
        }
        confirmDialog?.setTips(
            resources.getString(
                R.string.bt_device_disconnect_for_phonelink2,
                phoneLinkName,
                phoneLink.typeName
            )
        )
        confirmDialog?.setCancelBtnText(resources.getString(R.string.bt_device_disconnect_for_phonelink_cancel))
        confirmDialog?.show()
        hideSelf()
    }

    /**
     * 初始化MutableLiveData数据订阅,onCreate()中调用.
     *
     */
    override fun initObserve() {
        LogUtil.d(TAG, "initObserve : ")
        viewModel.apply {
            // 蓝牙开关订阅
            bluetoothStatusLiveData.observe(this@BtFragment) {
                LogUtil.d(TAG, "initObserve : bt switch = $it")
                // 更新设备蓝牙开关状态及对应的UI显示
                viewBinding.settingBtSw.isEnabled = true
                btSwitchUiUpdate(it)
                if(isToOpenBluetoothAndFound){
                    isToOpenBluetoothAndFound = false
                    // viewModel.setBtFoundStatus(true)
                }
            }
            // 蓝牙名称订阅
            btNameLiveData.observe(this@BtFragment) {
                LogUtil.d(TAG, "initObserve : bt name = $it")
                // 蓝牙名称更新
                if (it != null) {
                    btNameUiUpdate(it)
                }
            }
            btScanAnimationLiveData.observe(this@BtFragment) {
                LogUtil.d(TAG, "initObserve : bt scan show = $it")
                // 设置扫描动画可见性
                scanBtIconUiUpdate(it)
            }
            btDevicesLiveData.observe(this@BtFragment) {
                LogUtil.d(TAG, "initObserve : bt scan device size = ${it.size}")
                // 扫描列表订阅,扫描列表数据及UI更新
                scanBtListDataUpdate(it)
            }
            btPairedDevLiveData.observe(this@BtFragment) {
                LogUtil.d(TAG, "initObserve : bt paired device size = ${it.size}")
                // 蓝牙配对列表订阅，蓝牙配对列表数据及UI更新
                pairedBtListDataUpdate(it)
            }
            // 搜索关键词订阅
            searchKeywordLiveData.observe(this@BtFragment) {
                // 更新搜索关键词Ui
                updateSearchKeywordUi(it)
            }
        }
    }

    /**
     * 初始化各类数据，onStart()中调用.
     *
     */
    override fun initData() {
        isShow = true
        LogUtil.d(TAG, "initData : ")
        viewModel.getBtScanList()
        viewModel.getBtPairedList()
        // 获取搜索关键词
        viewModel.getSearchKeyword()
        CarBtManager.instance.isBtSettingUI = true
        // 注册应用状态监听
        requireActivity().application.registerActivityLifecycleCallbacks(appSwitchListener)
    }

    override fun onResume() {
        isToOpenBluetoothAndFound = (activity as MainActivity).bundle?.getBoolean(PackageConstants.Settings.PAGE_BLUETOOTH_AND_FOUND_OPEN_KEY,false) ?: false
        LogUtil.d(TAG, "onResume : isToOpenBluetoothAndFound = $isToOpenBluetoothAndFound")
        if(isToOpenBluetoothAndFound){
            (activity as MainActivity).bundle?.clear()
            //蓝牙已是打开状态，直接打开蓝牙可被发现开关
            if(CarBtManager.instance.getBluetoothStatus()){
                if(!viewModel.getBtFoundStatus()){
                     viewModel.setBtFoundStatus(true)
                }
                isToOpenBluetoothAndFound = false
            }else{
                //先打开蓝牙，待蓝牙完全打开后再打开蓝牙可被发现开关
                CarBtManager.instance.setBluetoothStatus(true)
            }
        }
        super.onResume()
    }

    override fun onStop() {
        // 停止自动设备扫描
        viewModel.stopScanBtDevice()
        CarBtManager.instance.isBtSettingUI = false
        // 注销监听器
        requireActivity().application.unregisterActivityLifecycleCallbacks(appSwitchListener)
        super.onStop()
        isShow = false
    }

    /**
     * 移除MutableLiveData数据订阅，onDestroy()中调用.
     *
     */
    override fun removeObserve() {
        LogUtil.d(TAG, "removeObserve : ")
        viewModel.apply {
            // 移除订阅
            searchKeywordLiveData.removeObservers(this@BtFragment)
            btPairedDevLiveData.removeObservers(this@BtFragment)
            btDevicesLiveData.removeObservers(this@BtFragment)
            btScanAnimationLiveData.removeObservers(this@BtFragment)
            btFoundTimerLiveData.removeObservers(this@BtFragment)
            btNameLiveData.removeObservers(this@BtFragment)
            btCanFoundStatusLiveData.removeObservers(this@BtFragment)
            bluetoothStatusLiveData.removeObservers(this@BtFragment)
        }
    }

    /**
     * 初始化可搜索TextView数据.
     *
     */
    private fun initSearchTextView() {
        LogUtil.d(TAG, "initSearchTextView : ")
        // 初始化页面内TextView集合
        textViews.addAll(TextUtil.getAllTextViewInFragment(viewBinding.root))
        // 构建页面内搜索菜单相关数据
        Contacts.btMenuBean.secondMenuList.forEach { secondMenu ->
            val textView = textViews.find { TextUtils.equals(it.text, secondMenu.secondMenuName) }
            if (textView != null) {
                // 二级菜单选项在页面TextView找到对应，则更新滑动距离和与之对应的TextView
                secondMenu.nameTextView = textView
                secondMenu.nameTextView!!.viewTreeObserver.addOnGlobalLayoutListener(object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        if (nestedScrollViewTop == -1) {
                            nestedScrollViewTop =
                                TextUtil.viewToTopScreenDistance(viewBinding.settingsBtSsv)
                        }
                        // 设置滑动到对应TextView需要滑动的距离
                        secondMenu.scrollDistance =
                            TextUtil.viewToTopScreenDistance(textView) - nestedScrollViewTop
                        // 移除监听器，确保只调用一次
                        secondMenu.nameTextView!!.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }
                })
            }
        }
    }

    /**
     * 清空可搜索TextView数据.
     *
     */
    private fun clearSearchTextView(){
        LogUtil.d(TAG, "clearSearchTextView : ")
        Contacts.btMenuBean.secondMenuList.forEach { secondMenu ->
            secondMenu.nameTextView = null
        }
    }

    /**
     * 初始化页面开关状态.
     *
     */
    @SuppressLint("SetTextI18n")
    private fun initSwStatusUi() {
        LogUtil.d(TAG, "initSwStatusUi : ")
        // 蓝牙开关状态
//        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
//        viewBinding.settingBtSw.isChecked = bluetoothAdapter.isEnabled
        // 更新蓝牙开关UI
        initBtSwitchUi(viewModel.getBluetoothStatus())
        // 初始化蓝牙名称
        btName = viewModel.getBtName()
        viewBinding.settingBtNameEtTv.text = "可被发现为“$btName”"
    }

    /**
     * 蓝牙页面点击事件监听.
     *
     * @param view
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.settings_bt_start_scan_iv -> {
                // 主动搜索蓝牙可用设备
                LogUtil.d(TAG, "onClick : start scan is click!")
                viewModel.stopAutoConnectTimerTask()
                viewModel.startScanBtDevice(isAutoScan = true, isHand = true)
            }
        }
    }

    /*-----------------------------蓝牙配对列表各控件点击事件监听回调-----------------------------------*/

    /**
     * 点击配对列表蓝牙断开图标时.
     *
     * @param beDevice BtDeviceBean
     */
    override fun onBtDisconnected(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "onBtDisconnected : disconnect device = ${beDevice.device.name}")
        // 断开当前的蓝牙连接，显示断开提示弹窗
        hideSelf()
        showDisConnectDialog(beDevice) {
            viewModel.disconnectDevice(beDevice, Contacts.BT_CONNECT_ALL)
        }
    }

    fun disconnectForPhoneLink(beDevice: BtDeviceBean, type: Int) {
        LogUtil.d(TAG, "onBtDisconnected : disconnect device = ${beDevice.device.name}")
        // 断开当前的蓝牙连接，显示断开提示弹窗
        showDisConnectForPhoneLinkDialog(beDevice, type) {
            viewModel.disconnectDevice(beDevice, Contacts.BT_CONNECT_ALL)
        }
    }

    /**
     * 点击配对列表item进行蓝牙连接时.
     *
     * @param beDevice BtDeviceBean
     */
    override fun onBtConnect(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "onBtConnect : connect device = ${beDevice.device.name}")
        // 进行蓝牙连接
        viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_ALL)
    }

    /**
     * 配对列表点击删除图标时.
     *
     * @param beDevice BtDeviceBean
     */
    override fun onBtDeleteDevice(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "onBtDeleteDevice : delete device = ${beDevice.device}")
        //弹出蓝牙设置弹窗
        showSetBluetoothDialog(beDevice)
        hideSelf()
    }
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private fun showSetBluetoothDialog(beDevice: BtDeviceBean) {
        // 创建一个自定义的 Dialog
        val dialog = context?.let { Dialog(it, R.style.AppTheme_Dialog) }
        dialog?.setContentView(R.layout.dialog_set_bluetooth) // 设置自定义布局
        val btPreferencesSw= dialog?.findViewById<Switch>(R.id.setting_bt_preferences_sw)
        val btMediaSw= dialog?.findViewById<Switch>(R.id.setting_bt_media_sw)
        val btMediaImg= dialog?.findViewById<ImageView>(R.id.setting_bt_media_img)
        val btnDisconnect = dialog?.findViewById<Button>(R.id.btn_disconnect)

        for (btDevice in viewModel.getBtPairedList()) {
            Log.d(TAG, "showSetBluetoothDialog: zhc6whu:"+btDevice.device.name+"是否是常用设备"+beDevice.isPreferencesDevice)
        }

        //初始化常用设备switch开关
        btPreferencesSw?.isChecked= beDevice.isPreferencesDevice

        btMediaSw?.isChecked = beDevice.isAlwaysPlayPhoneMedia

        //btPreferencesSw设置监听
        btPreferencesSw?.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                //设置为常用设备
                btPairedListAdapter.setPairedList(viewModel.getBtPairedList())
                //把列表中的每一个设备的isPreferencesDevice都设为false
                for (btDevice in viewModel.getBtPairedList()) {
                    btDevice.isPreferencesDevice = false
                }
                beDevice.isPreferencesDevice = true
            } else {
                //不设置为常用设备
                beDevice.isPreferencesDevice = false
            }
        }
        btMediaSw?.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                //设置为始终播放手机音频
                beDevice.isAlwaysPlayPhoneMedia = true
                Settings.System.putInt(context?.contentResolver,"settings_phone_always_play_audio",1)//1表示On
                viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_A2DP)
            } else {
                //不设置为始终播放手机音频
                beDevice.isAlwaysPlayPhoneMedia = false
                Settings.System.putInt(context?.contentResolver,"settings_phone_always_play_audio",0)//0表示off
                viewModel.disconnectDevice(beDevice, Contacts.BT_CONNECT_A2DP)
            }
        }
        btMediaImg?.setOnClickListener{
            val tipsDialog = context?.let { Dialog(it, R.style.AppTheme_Dialog) }
            tipsDialog?.setContentView(R.layout.dialog_bt_media_tips)
            tipsDialog?.show()
        }
        dialog?.setOnDismissListener {
            showSelf()
            // 当对话框关闭时（包括点击外部关闭），刷新上一级弹窗
            btPairedListAdapter.setPairedList(viewModel.getBtPairedList())
        }
        btnDisconnect?.setOnClickListener {
            dialog.setOnDismissListener(null)
            // 显示删除设备提示确认弹窗
            showDeleteDeviceDialog(beDevice)
            dialog.dismiss()
            hideSelf()
        }
        dialog?.show()
        hideSelf()
    }

    fun hideSelf() {
        val dialog = dialog
        if (dialog != null && dialog.isShowing) {
            dialog.hide()
            isSelfHidden = true
        }
        Log.d(TAG, "hideSelf: zhc6whu: is hide")
    }

    // 显示自身
    fun showSelf() {
        val dialog = dialog
        if (dialog != null && !dialog.isShowing && isSelfHidden) {
            dialog.show()
            isSelfHidden = false
        }
        Log.d(TAG, "showSelf: zhc6whu: is show")
    }

    /**
     * 从carPlay或AA切换蓝牙.
     *
     * @param beDevice 蓝牙设备
     */
    override fun onCarplayAaToBtDevice(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "onCarplayToBtDevice : switch device = ${beDevice.device}")
        if (beDevice.cPConnectedState == IConstant.ConnectState.CONNECTED) {
            // 断开当前的cp连接，显示断开提示弹窗
            showDisConnectDialog(beDevice) {
                //T13JSUPPLY-645 主动断开cpaa需回连蓝牙
                viewModel.disconnectDevice(beDevice, Contacts.BT_CONNECT_CP, true)
                // 弹窗提示
                showToast(getString(R.string.dm_cp_disconnected_tips))
            }
        } else if (beDevice.aAConnectedState == ConnectState.CONNECTED.state) {
            // 断开当前的aa连接，显示断开提示弹窗
            showDisConnectDialog(beDevice) {
                //T13JSUPPLY-645 主动断开cpaa需回连蓝牙
                viewModel.disconnectDevice(beDevice, Contacts.BT_CONNECT_AA, true)
                // 弹窗提示
                showToast(getString(R.string.dm_aa_disconnected_tips))
            }
        }
    }

    /**
     * 点击配对列表未高亮的CarPlay图标或已连接状态下，点击item时..
     *
     * @param beDevice BtDeviceBean
     */
    override fun onBtPhoneLink(beDevice: BtDeviceBean,type: Int) {
        LogUtil.d(TAG, "onBtCarPlay : device = ${beDevice.device}")
        // 判断当前carPlay是否已连接，未连接，则进行连接
        disconnectForPhoneLink(beDevice, type)
    }

    /**
     * 点击配对列表未高亮的AA图标或已连接状态下，点击item时.
     *
     * @param beDevice BtDeviceBean
     */
    override fun onBtAndroidAuto(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "onBtAndroidAuto : device = ${beDevice.device}")
        // 判断当前AA是否已连接，若未连接，则进行连接
        if (beDevice.aAConnectedState != ConnectState.CONNECTED.state) {
            // 支持AA，则进行连接
            if (beDevice.isSupportWirelessAA) {
                // 连接AA，且未连接，则进行连接
                startConnectAndroidAuto(beDevice)
            } else {
                LogUtil.d(TAG, "onBtAndroidAuto : bt is not support AA!")
            }
        } else {
            // 已连接状态，支持AA情况下，点击则直接打开AA
            if (beDevice.isSupportWirelessAA) {
                // 判断当前AA是否已连接，已连接，则直接打开
                viewModel.openAndroidAuto(beDevice)
            } else {
                LogUtil.d(TAG, "onBtAndroidAuto : bt is not support AA!")
            }
        }
    }

    /**
     * 进行蓝牙音乐的连接.
     *
     * @param beDevice 蓝牙设备
     */
    override fun onBtConnectMusic(beDevice: BtDeviceBean) {
        LogUtil.i(TAG, "onBtConnectMusic : beDevice = ${beDevice.device}")
        if (beDevice.cPConnectedState == IConstant.ConnectState.CONNECTED) {
            // 当前设备已连接CP，则显示断开当前CP连接弹窗,只进行蓝牙音乐的连接
            showDisConnectCarPlayDialog(beDevice, Contacts.BT_CONNECT_A2DP)
        } else if (beDevice.aAConnectedState == ConnectState.CONNECTED.state) {
            // 当前设备已连接AA，则显示断开当前AA连接弹窗,只进行蓝牙音乐的连接
            showDisConnectAaDialog(beDevice, Contacts.BT_CONNECT_A2DP)
        } else {
            // 其他情况,均可直接进行蓝牙音乐连接
            viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_A2DP)
        }
    }

    /**
     * 断开蓝牙音乐的连接.
     *
     * @param beDevice 蓝牙设备
     */
    override fun onBtDisconnectMusic(beDevice: BtDeviceBean) {
        LogUtil.i(TAG, "onBtDisconnectMusic : beDevice = ${beDevice.device}")
        // 断开蓝牙音乐的连接
        viewModel.disconnectDevice(beDevice, Contacts.BT_CONNECT_A2DP)
    }

    /**
     * 进行蓝牙电话的连接.
     *
     * @param beDevice 蓝牙设备
     */
    override fun onBtConnectPhone(beDevice: BtDeviceBean) {
        LogUtil.i(TAG, "onBtConnectPhone : beDevice = ${beDevice.device}")
        if (beDevice.cPConnectedState == IConstant.ConnectState.CONNECTED) {
            // 当前设备CP已连接,则显示断开当前CP连接弹窗,只进行蓝牙电话的连接
            showDisConnectCarPlayDialog(beDevice, Contacts.BT_CONNECT_HFP)
        } else {
            // 当前设备不存在CP连接,则点击进行蓝牙电话连接
            viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_HFP)
        }
    }

    /**
     * 断开蓝牙电话的连接.
     *
     * @param beDevice 蓝牙设备
     */
    override fun onBtDisconnectPhone(beDevice: BtDeviceBean) {
        LogUtil.i(TAG, "onBtDisconnectPhone : beDevice = ${beDevice.device}")
        if (beDevice.aAConnectedState != ConnectState.CONNECTED.state) {
            // AA未连接，则断开蓝牙电话的连接
            viewModel.disconnectDevice(beDevice, Contacts.BT_CONNECT_HFP)
        } else {
            // AA已连接，不做响应
            LogUtil.d(TAG, "onBtDisconnectPhone : android auto is connected!")
        }
    }

    /*-------------------------------蓝牙配对列表各控件点击事件监听回调---------------------------------*/
    /**
     * 连接CarPlay.
     *
     * @param beDevice 设备
     */
    private fun startConnectCarPlay(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "startConnectCarPlay : beDevice = ${beDevice.device.name}")
        if (viewModel.hasConnectDeviceAa()) {
            if (viewModel.getConnectedDevices().size == 2
                && !viewModel.hasExistConnectedDevice(beDevice)
            ) {
                // 当前连接了两个设备,并且待连接设备不在已连接列表中，则进行仲裁连接AA切换CP
                viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_AA_TO_CP)
            } else {
                // 当前存在AA连接，则进行弹窗提示
                showConnectDeviceCpDialog(beDevice)
            }
        } else {
            // 当前不存在AA连接，则直接进行CP连接
            viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_CP)
        }
    }

    /**
     * 连接AndroidAuto.
     *
     * @param beDevice 设备
     */
    private fun startConnectAndroidAuto(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "startConnectAndroidAuto : beDevice = ${beDevice.device.name}")
        if (viewModel.hasConnectDeviceCp()) {
            if (viewModel.getConnectedDevices().size == 2
                && !viewModel.hasExistConnectedDevice(beDevice)
            ) {
                // 当前连接了两个设备,并且待连接设备不在已连接列表中，则进行仲裁连接CP切换AA
                viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_CP_TO_AA)
            } else {
                // 当前存在Cp连接，则进行弹窗提示
                showConnectAaDeviceDialog(beDevice)
            }
        } else {
            // 当前不存在CP连接，则直接进行AA连接
            viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_AA)
        }
    }

    /**
     * 设置ScanScrollView是否可以滑动.
     *
     * @param state recycleView滑动状态
     */
    private fun setBtScrollViewCanScroll(state: Int) {
        LogUtil.d(TAG, "setBtScrollViewCanScroll : state = $state")
        when (state) {
            RecyclerView.SCROLL_STATE_DRAGGING -> {
                viewBinding.settingsBtSsv.setIntercepted(true)
            }

            RecyclerView.SCROLL_STATE_IDLE -> {
                viewBinding.settingsBtSsv.setIntercepted(false)
            }
        }
    }

    /**
     * 显示提示Toast.
     *
     * @param message 提示信息
     */
    private fun showToast(message: String) {
        SettingsToast.showToast(message)
    }

    /**
     * 显示AA已连接确认弹窗，确认后切换CP.
     *
     * @param beDevice 蓝牙设备
     */
    private fun showConnectDeviceCpDialog(beDevice: BtDeviceBean) {
        // 构建AA切换CP弹窗
        val connectDeviceDialogCp = DMConfirmDialog(requireContext())
        connectDeviceDialogCp.setTips(getString(R.string.dm_aa_to_cp_tips))
        // 添加监听
        connectDeviceDialogCp.setDialogClickCallback(object :
            DMConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "onConfirmClick : ")
                // AA切换CP
                viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_AA_TO_CP)
            }

            override fun onCancelClick() {
                LogUtil.d(TAG, "onCancelClick : ")
            }
        })
        connectDeviceDialogCp.onShow()
    }

    /**
     * 显示CP已连接确认弹窗，确认后切换AA.
     *
     * @param beDevice 蓝牙设备
     */
    private fun showConnectAaDeviceDialog(beDevice: BtDeviceBean) {
        // 构建CP切换AA弹窗
        val connectDeviceDialogCp = DMConfirmDialog(requireContext())
        connectDeviceDialogCp.setTips(getString(R.string.dm_cp_to_aa_tips))
        // 添加监听
        connectDeviceDialogCp.setDialogClickCallback(object :
            DMConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "onConfirmClick : ")
                // CP切换AA
                viewModel.connectDevice(beDevice, Contacts.BT_CONNECT_CP_TO_AA)
            }

            override fun onCancelClick() {
                LogUtil.d(TAG, "onCancelClick : ")
            }
        })
        connectDeviceDialogCp.onShow()
    }

    /**
     * 显示断开当前CarPlay的连接提示弹窗
     *
     * @param btDevice 待连接的蓝牙设备
     * @param connectType 连接类型，连接蓝牙音乐、电话还是全部
     */
    private fun showDisConnectCarPlayDialog(
        btDevice: BtDeviceBean,
        connectType: Int = Contacts.BT_CONNECT_ALL
    ) {
        // 连接对象carPlay
        val carPlayBean = viewModel.getCarPlayBean()
        LogUtil.i(TAG, "showDisConnectCarPlayDialog : carPlayBean = $carPlayBean")
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "onConfirmClick : dis connect carplay!")
                if (carPlayBean != null) {
                    // 断开无线CarPlay的连接
                    viewModel.disconnectDevice(carPlayBean, Contacts.BT_CONNECT_CP, false)
                }
                // 开始连接蓝牙
                viewModel.connectDevice(btDevice, connectType)
                cancelDialog()
            }

            override fun onCancelClick() {
                LogUtil.d(TAG, "onCancelClick : ")
                // 取消按钮被点击
                cancelDialog()
            }
        })
        // 设置提示语
        confirmDialog?.setTips(
            resources.getString(R.string.bt_device_disconnect_cp_tips, carPlayBean?.device?.name)
        )
        confirmDialog?.show()
    }

    /**
     * 显示断开当前AA的连接提示弹窗
     *
     * @param btDevice 待配对或连接的蓝牙设备
     * @param connectType 连接类型，连接蓝牙音乐、电话还是全部
     */
    private fun showDisConnectAaDialog(
        btDevice: BtDeviceBean?,
        connectType: Int = Contacts.BT_CONNECT_ALL
    ) {
        // AA连接设备
        val androidAutoBean = viewModel.getAndroidAutoBean()
        LogUtil.i(TAG, "showDisConnectAaDialog : androidAutoBean = $androidAutoBean")
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "onConfirmClick : dis connect android auto!")
                if (androidAutoBean != null) {
                    // 断开无线AA的连接
                    viewModel.disconnectDevice(androidAutoBean, Contacts.BT_CONNECT_AA, false)
                }
                if (btDevice != null) {
                    // 开始连接蓝牙
                    viewModel.connectDevice(btDevice, connectType)
                } else {
                    // 关闭蓝牙
                    viewModel.setBluetoothStatus(false)
                }
                cancelDialog()
            }

            override fun onCancelClick() {
                LogUtil.d(TAG, "onCancelClick : ")
                if (btDevice == null) {
                    // 蓝牙开关复位
                    //viewBinding.settingBtSw.isChecked = !viewBinding.settingBtSw.isChecked
                }
                // 取消按钮被点击
                cancelDialog()
            }
        })
        if (btDevice == null) {
            // 设置提示语
            confirmDialog?.setTips(getString(R.string.bt_switcher_close_aa_tips))
        } else {
            // 设置提示语
            confirmDialog?.setTips(
                resources.getString(
                    R.string.bt_device_disconnect_aa_tips,
                    androidAutoBean?.device?.name
                )
            )
        }
        confirmDialog?.show()
    }

    /**
     * 断开设备时提示弹窗.
     * @param disconnectDevice 待断开的设备
     */
    private fun showDisConnectDialog(disconnectDevice: BtDeviceBean,onConfirmOperation :(()->Unit)?) {
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "showDisConnectDialog : disconnect!")
                onConfirmOperation?.invoke()
                showSelf()
                cancelDialog()
            }

            override fun onCancelClick() {
                // 取消按钮被点击
                showSelf()
                cancelDialog()
            }
        })
        confirmDialog?.setOnDismissListener(object : DialogInterface.OnDismissListener {
            override fun onDismiss(dialog: DialogInterface?) {
                showSelf()
            }
        })
        val name = disconnectDevice.device.name ?: disconnectDevice.wiredName
        LogUtil.d(TAG, "showDisConnectDialog : device.name = ${disconnectDevice.device.name} , wiredName = ${disconnectDevice.wiredName}")
        confirmDialog?.setTips(
            resources.getString(R.string.bt_device_disconnect, name)
        )
        hideSelf()
        confirmDialog?.show()
    }

    /**
     * 断开设备时提示弹窗.
     *
     * @param disconnectDevice 待断开的设备
     */
    private fun showDisConnectForPhoneLinkDialog(
        disconnectDevice: BtDeviceBean,
        type: Int,
        onConfirmOperation: (() -> Unit)?
    ) {
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "showDisConnectDialog : disconnect!")
                onConfirmOperation?.invoke()
                val vdLinkDevice = VDLinkDevice()
                vdLinkDevice.btAddress = disconnectDevice.device.address
                when (type) {
                    1 -> {
                        vdLinkDevice.type = VDValuePhoneLink.DeviceType.CARPLAY
                    }

                    2 -> {
                        vdLinkDevice.type = VDValuePhoneLink.DeviceType.CARLINK
                    }

                    3 -> {
                        vdLinkDevice.type = VDValuePhoneLink.DeviceType.HICAR
                    }
                }
                vdLinkDevice.isWireless = true
                val payload = Bundle()
                when (type) {
                    1 -> {
                        payload.putInt(
                            VDKey.TYPE,
                            VDValuePhoneLink.ServerId.CARPLAY
                        ) //互联服务ID,VDValuePhoneLink.ServerId.COMMON
                    }

                    2 -> {
                        payload.putInt(
                            VDKey.TYPE,
                            VDValuePhoneLink.ServerId.CARLINK
                        ) //互联服务ID,VDValuePhoneLink.ServerId.COMMON
                    }

                    3 -> {
                        payload.putInt(
                            VDKey.TYPE,
                            VDValuePhoneLink.ServerId.HICAR
                        ) //互联服务ID,VDValuePhoneLink.ServerId.COMMON
                    }
                }
                payload.putParcelable(VDKey.DATA, vdLinkDevice) //(VDLinkDevice)device,要连接的设备
                val event = VDEvent(VDEventPhoneLink.CONNECT_DEVICE, payload)
                Log.d(TAG, "onBtPhoneLink: " + event)
                VDBus.getDefault().set(event)
                showSelf()
                cancelDialog()
            }

            override fun onCancelClick() {
                // 取消按钮被点击
                showSelf()
                cancelDialog()
            }
        })
        confirmDialog?.setOnDismissListener(object : DialogInterface.OnDismissListener {
            override fun onDismiss(dialog: DialogInterface?) {
                showSelf()
            }
        })
        val name = disconnectDevice.device.name ?: disconnectDevice.wiredName
        LogUtil.d(
            TAG,
            "showDisConnectDialog : device.name = ${disconnectDevice.device.name} , wiredName = ${disconnectDevice.wiredName}"
        )
        var phoneLinkType = ""
        if (type == 2) {
            phoneLinkType = "ICCOA Carlink"
        } else if (type == 1) {
            phoneLinkType = "Apple CarPlay"
        } else if (type == 3) {
            phoneLinkType = "HUAWEI HiCar"
        }
        confirmDialog?.setTips(
            resources.getString(R.string.bt_device_disconnect_for_phonelink1, phoneLinkType)
        )
        hideSelf()
        confirmDialog?.show()
    }

    /**
     * 删除设备时提示弹窗显示.
     *
     * @param deleteDevice 待删除的设备.
     */
    private fun showDeleteDeviceDialog(deleteDevice: BtDeviceBean) {
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                // 移除已配对的蓝牙设备
                viewModel.removeBond(deleteDevice)
                showSelf()
                cancelDialog()
            }

            override fun onCancelClick() {
                // 取消按钮被点击
                showSelf()
                cancelDialog()
            }
        })
        confirmDialog?.setOnDismissListener(object : DialogInterface.OnDismissListener {
            override fun onDismiss(dialog: DialogInterface?) {
                showSelf()
            }
        })
        confirmDialog?.setTips(
            resources.getString(R.string.bt_device_delete, deleteDevice.device.name)
        )
        confirmDialog?.show()
        hideSelf()
    }

    /**
     * 关闭当前提示蓝牙弹窗.
     *
     */
    private fun cancelDialog() {
        LogUtil.d(TAG, "cancelDialog : ")
        if (confirmDialog != null) {
            confirmDialog?.dismiss()
            confirmDialog = null
        }
    }

    /**
     * 更新蓝牙开关状态及对应状态的UI显示.
     *
     * @param isOpenBtSwitch 是否打开蓝牙开关.
     */
    private fun btSwitchUiUpdate(isOpenBtSwitch: Boolean) {
        viewBinding.settingBtSw.isChecked = isOpenBtSwitch
        if (isOpenBtSwitch) {
            // isAutoConnected = true
            // 蓝牙开关打开界面显示
            onBtSwitchOnUiShow()
        } else {
            // 蓝牙开关关闭界面显示
            onBtSwitchOffUiShow()
        }
    }

    /**
     * 更新蓝牙开关状态及对应状态的UI显示.
     *
     * @param isOpenBtSwitch 是否打开蓝牙开关.
     */
    private fun initBtSwitchUi(isOpenBtSwitch: Boolean) {
        viewBinding.settingBtSw.isChecked = viewModel.getBluetoothStatus()
        if (isOpenBtSwitch) {
            //进入tab需要发起自动连接
//            isAutoConnected = true
            // 蓝牙开关打开界面显示
            onBtSwitchOnUiShow()
        } else {
            // 蓝牙开关关闭界面显示
            onBtSwitchOffUiShow()
        }
    }

    /**
     * 更新蓝牙名称.
     *
     * @param btNameString 蓝牙名称
     */
    @SuppressLint("SetTextI18n")
    private fun btNameUiUpdate(btNameString: String) {
        viewBinding.settingBtNameEtTv.text = "可被发现为“$btNameString”"
        btName = btNameString
    }

    /**
     * 蓝牙配对列表更新.
     *
     * @param btPairedList 蓝牙配对列表
     */
    private fun pairedBtListDataUpdate(btPairedList: CopyOnWriteArrayList<BtDeviceBean>) {
        LogUtil.i(TAG, "pairedBtListDataUpdate : size = ${btPairedList.size} ")
        Log.d(
            TAG,
            "pairedBtListDataUpdate: btPairedList :" + btPairedList.size + " getPhoneLinkStatus " + viewModel.getPhoneLinkStatus()
        )
        if (btPairedList.size == 0 && !viewModel.getPhoneLinkStatus()) {
            // 已配对列表隐藏
            viewBinding.apply {
                settingsPairedDevicesCl.visibility = View.GONE
                settingBtPairedTv.visibility = View.GONE
            }
        } else {
            // 已配对列表显示
            viewBinding.apply {
                settingsPairedDevicesCl.visibility = View.VISIBLE
                settingBtPairedTv.visibility = View.VISIBLE
            }
            LogUtil.i(TAG, "pairedBtListDataUpdate : isAutoConnected = $isAutoConnected")
            if (isAutoConnected) {
                isAutoConnected = false
                // 开始自动连接
                GlobalScope.launch (Dispatchers.IO){
                    delay(300)
                    viewModel.autoConnectDevice()
                }
            }
        }
        btPairedListAdapter.setPairedList(btPairedList)
    }

    /**
     * 更新蓝牙扫描列表数据.
     *
     * @param btScanList 蓝牙扫描列表
     */
    private fun scanBtListDataUpdate(btScanList: CopyOnWriteArrayList<BtDeviceBean>) {
        viewBinding.settingsCanUseDevicesRv.visibility = View.VISIBLE
        btScanListAdapter.setScanList(btScanList)
    }

    /**
     * 蓝牙扫描动画图标是否展示.
     *
     * @param isOpenScanIcon 是否打开蓝牙扫描图标.
     */
    private fun scanBtIconUiUpdate(isOpenScanIcon: Boolean) {
        if (isOpenScanIcon) {
            viewBinding.settingsBtScanPb.visibility = View.VISIBLE
        } else {
            viewBinding.settingsBtScanPb.visibility = View.GONE
        }
    }

    /**
     * 当蓝牙开关开启时UI显示.
     *
     */
    private fun onBtSwitchOnUiShow() {
        LogUtil.d(TAG, "onBtSwitchOnUiShow ")
        viewBinding.apply {
            // 可用设备显示
            settingsCanUseDevicesCl.visibility = View.VISIBLE
            settingsBtUseDevicesTv.visibility = View.VISIBLE
            settingsBtStartScanIv.visibility = View.VISIBLE
            // 文言取消置灰
            settingBtNameEtTv.visibility = View.VISIBLE
        }
        btPhoneLinkUiShow()
        //T13JSUPPLY-252  cp正在连接，不发起自动扫描
        if(CarDmManager.instance.hasConnectingDeviceCp()){
            LogUtil.d(TAG, "hasConnectingDeviceCp dont to start scan ")
        }else{
            // 开始扫描
            viewModel.startScanBtDevice(isAutoScan = true, isHand = false)
        }
    }

    /**
     * 蓝牙开关关闭时UI显示.
     *
     */
    private fun onBtSwitchOffUiShow() {
        LogUtil.d(TAG, "onBtSwitchOffUiShow ")
        isAutoConnected = false
        // 断开当前连接
        viewModel.disconnectDevice()
        // 开关关闭，关闭自动连接
        viewModel.stopConnectDevice()
        // 关闭扫描
        viewModel.stopScanBtDevice()
        viewBinding.apply {
            // 可用设备列表隐藏
            settingsCanUseDevicesCl.visibility = View.GONE
            settingsBtUseDevicesTv.visibility = View.GONE
            settingsBtScanPb.visibility = View.GONE
            settingsBtStartScanIv.visibility = View.GONE
            // 已配对列表隐藏，需要判断是否连接了有线cp
            val pairedList = viewModel.btPairedDevLiveData.value?.filter {
                it.carPlayLinkType == Contacts.CP_CONNECT_WIRED && it.cPConnectedState == IConstant.ConnectState.CONNECTED
            }
            //若连接了有线cp，不隐藏，只显示有线cp设备，过滤其他设备
            if(pairedList != null && pairedList.isNotEmpty()){
                viewModel.btPairedDevLiveData.postValue(CopyOnWriteArrayList(pairedList))
                settingsPairedDevicesCl.visibility = View.VISIBLE
                settingBtPairedTv.visibility = View.VISIBLE
            }else{
                settingsPairedDevicesCl.visibility = View.GONE
                settingBtPairedTv.visibility = View.GONE
            }
            // 文言置灰
            settingBtNameEtTv.visibility = View.GONE
        }
    }

    /**
     * 当蓝牙开关开启时UI显示.
     *
     */
    private fun btPhoneLinkUiShow() {
        val event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
        if (event != null) {
            val bundle = event.payload
            val status = bundle.getInt(VDKey.STATUS)
            val type = bundle.getInt(VDKey.TYPE)
            val isWireless = bundle.getBoolean(VDKey.ENABLE)
            val devicePhoneLink = bundle.getParcelable<VDLinkDevice>(VDKey.INFO)
            val name = devicePhoneLink?.name ?: ""
            LogUtil.d(
                TAG,
                "BtPhoneLinkUiShow " + "status: $status, type: $type, isWireless: $isWireless,name:$name,macadress: ${devicePhoneLink?.btAddress}"
            )
            if (status > 0) {
                viewBinding.btPhonelinkPaired.visibility = View.VISIBLE
                viewBinding.btPhonelinkNameTv.text = name
                viewBinding.btConnectedStateTv.visibility = View.VISIBLE
                viewBinding.btConnectedStateTv.text = when (status) {
                    1 -> getString(R.string.bt_device_connecting)
                    2 -> getString(R.string.bt_device_connected)
                    3 -> getString(R.string.bt_device_connected)
                    4 -> getString(R.string.bt_device_connected)
                    else -> ""
                }
                if (type == 8) {
                    viewBinding.btIconIv.setImageResource(R.mipmap.icon_carlink)
                } else if (type == 1) {
                    viewBinding.btIconIv.setImageResource(R.mipmap.icon_carplay)
                } else if (type == 7) {
                    viewBinding.btIconIv.setImageResource(R.mipmap.icon_hicar)
                }
                if (isWireless) {
                    viewBinding.btDeleteIv.visibility = View.VISIBLE
                } else {
                    viewBinding.btDeleteIv.visibility = View.GONE
                }
            } else {
                viewBinding.btPhonelinkPaired.visibility = View.GONE
            }
        }
    }

    /**
     * 更新搜索关键词Ui.
     *
     * @param keyword
     */
    private fun updateSearchKeywordUi(keyword: String) {
        LogUtil.i(TAG, "updateSearchKeywordUi : keyword = $keyword")
        // 在页面菜单集合中进行遍历，找到搜索结果对应的TextView
        val secondMenu = Contacts.btMenuBean.secondMenuList.find {
            TextUtils.equals(it.nameTextView?.text, keyword)
        }
        if (secondMenu != null) {
            // 计算滑动距离，向上还是向下滑动
            val scrollDistance = secondMenu.scrollDistance - viewBinding.settingsBtSsv.scrollY - 80
            LogUtil.i(TAG, "updateSearchKeywordUi : scrollDistance = $scrollDistance")
            // 滑动对应搜索结果TextView控件的位置
            viewBinding.settingsBtSsv.fling(scrollDistance)
            viewBinding.settingsBtSsv.smoothScrollBy(0, scrollDistance)
        }
    }

    // 添加应用切换监听器
    private val appSwitchListener = object : Application.ActivityLifecycleCallbacks {
        override fun onActivityPaused(activity: Activity) {
            if (activity == <EMAIL> && isAdded) {
                // 新增：忽略蓝牙操作引起的暂停
                if (isHandlingBluetoothOperation) {
                    Log.d(TAG, "Ignoring pause during BT operation")
                    return
                }
                dismiss()
            }
        }
        override fun onActivityResumed(activity: Activity) {}
        override fun onActivityStarted(activity: Activity) {}
        override fun onActivityDestroyed(activity: Activity) {}
        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
        override fun onActivityStopped(activity: Activity) {}
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
    }

    override fun onDestroyView() {
        LogUtil.d(TAG, "onDestroyView : ")
        clearSearchTextView()
        super.onDestroyView()
    }

    override fun onDestroy() {
        cancelDialog()
        super.onDestroy()
    }

    override fun onPause() {
        super.onPause()
        // 失去焦点时立即关闭
        if (isResumed) {
            dismiss()
        }
    }

    override fun getDialogTag(): String {
        return "BtFragment"
    }

    companion object {
        // 日志标志位
        private const val TAG = "BtFragment"
        var isShow = false;
    }

    override fun onHideSoftInput() {
        
    }
}
