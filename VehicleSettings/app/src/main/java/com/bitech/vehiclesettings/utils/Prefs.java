package com.bitech.vehiclesettings.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.provider.Settings;
import android.util.Log;

import com.bitech.vehiclesettings.MyApplication;
import com.google.gson.Gson;

import java.time.LocalDateTime;

/**
 * 数据存储
 */
public class Prefs {
    private static volatile Prefs instance;
    private SharedPreferences sp;
    private Gson gson = new Gson();

    private Prefs(Context context) {
        sp = context.getSharedPreferences(PrefsConst.SETTINGS_NAME, Context.MODE_PRIVATE);
    }

    public static void init(Context context) {
        if (instance == null) {
            synchronized (Prefs.class) {
                if (instance == null) {
                    instance = new Prefs(context.getApplicationContext());
                }
            }
        }
    }

    // 基础类型存储（自动类型推断）
    public static <T> void put(String key, T value) {
        if (instance == null) {
            throw new IllegalStateException("Call Prefs.init() first");
        }

        SharedPreferences.Editor editor = instance.sp.edit();
        if (value instanceof String) {
            editor.putString(key, (String) value);
        } else if (value instanceof Integer) {
            editor.putInt(key, (Integer) value);
        } else if (value instanceof Boolean) {
            editor.putBoolean(key, (Boolean) value);
        } else if (value instanceof Float) {
            editor.putFloat(key, (Float) value);
        } else if (value instanceof Long) {
            editor.putLong(key, (Long) value);
        } else if (value instanceof LocalDateTime) {
            editor.putString(key, value.toString());
        } else {
            editor.putString(key, instance.gson.toJson(value));
        }
        editor.apply();
    }

    // 通用读取方法（支持对象反序列化）
    public static <T> T get(String key, T defValue) {
        if (instance == null) {
            throw new IllegalStateException("Call Prefs.init() first");
        }

        // 如果默认值是 LocalDateTime 类型，特殊处理（先按字符串读取，再转换）
        if (defValue instanceof LocalDateTime) {
            String stored = instance.sp.getString(key, null);
            if (stored == null) {
                return defValue;
            }
            try {
                return (T) LocalDateTime.parse(stored);
            } catch (Exception e) {
                return defValue;
            }
        }

        Object value = instance.sp.getAll().get(key);
        if (value == null || "null".equalsIgnoreCase(String.valueOf(value))) {
            return defValue;
        }

        if (defValue.getClass().isInstance(value)) {
            return (T) value;
        }
        return instance.gson.fromJson((String) value, (Class<T>) defValue.getClass());
    }

    // 快捷方法扩展
    public static String getString(String key) {
        return get(key, "");
    }

    public static int getInt(String key) {
        return get(key, 0);
    }

    public static boolean getBool(String key) {
        return get(key, false);
    }

    // 删除指定 key
    public static void remove(String key) {
        if (instance == null) {
            throw new IllegalStateException("Call Prefs.init() first");
        }
        instance.sp.edit().remove(key).apply();
    }

    // 清除所有
    public static void clear() {
        if (instance == null) {
            throw new IllegalStateException("Call Prefs.init() first");
        }
        instance.sp.edit().clear().apply();
    }

    public static int rtnStatus(String topic, int status, int defaultValue) {
        if (status >= 0) {
            Prefs.put(topic, status);
        } else {
            // 异常处理
            Prefs.put(topic, defaultValue);
            status = Prefs.get(topic, defaultValue);
        }
        return status;
    }

    public static void setGlobalValue(String globalName, int value) {
        Settings.Global.putInt(MyApplication.getContext().getContentResolver(), globalName, value);
    }

    public static void setSystemValue(String globalName, int value) {
        Settings.System.putInt(MyApplication.getContext().getContentResolver(), globalName, value);
    }

    public static void setSystemFloat(String globalName, float value) {
        Settings.System.putFloat(MyApplication.getContext().getContentResolver(), globalName, value);
    }

    public static float getSystemFloat(String globalName, float defaultValue) {
        try {
            return Settings.System.getFloat(MyApplication.getContext().getContentResolver(), globalName);
        } catch (Settings.SettingNotFoundException e) {
            return defaultValue;
        }
    }


    public static int getGlobalValue(String globalName, int defaultValue) {
        try {
            return Settings.Global.getInt(MyApplication.getContext().getContentResolver(), globalName);
        } catch (Settings.SettingNotFoundException e) {
            return defaultValue;
        }
    }

    public static int getSystemValue(String globalName, int defaultValue) {
        int anInt = Settings.System.getInt(MyApplication.getContext().getContentResolver(), globalName, defaultValue);
        Log.d("Prefs", "getSystemValue: " + anInt);
        return anInt;
    }

    public static void registerListener(SharedPreferences.OnSharedPreferenceChangeListener listener) {
        instance.sp.registerOnSharedPreferenceChangeListener(listener);
    }

    public static void unregisterListener(SharedPreferences.OnSharedPreferenceChangeListener listener) {
        instance.sp.unregisterOnSharedPreferenceChangeListener(listener);
    }
}
