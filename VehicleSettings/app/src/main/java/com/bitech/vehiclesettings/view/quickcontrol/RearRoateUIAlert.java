package com.bitech.vehiclesettings.view.quickcontrol;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertQRearRoateBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.view.recognition.PrivacyPolicyUIAlert;

public class RearRoateUIAlert extends BaseDialog {
    private static final String TAG = RearRoateUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public RearRoateUIAlert(@NonNull Context context) {
        super(context);
    }

    public RearRoateUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected RearRoateUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        RearRoateUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private boolean cameraConfirmFlag = false;
        private boolean cameraCancelFlag = false;
        protected DialogAlertQRearRoateBinding binding;

        private PrivacyPolicyUIAlert.Builder privacyPolicyUIAlert;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private RearRoateUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }


        /**
         * Create the custom dialog
         */
        public RearRoateUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new RearRoateUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertQRearRoateBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 794;
            window.setAttributes(layoutParams);

            initHudRoate();

            initTips();

            initButton();

            return dialog;
        }

        private void initButton() {
            binding.tvCameraConfirm.setOnClickListener(v -> {
                onProgressChangedListener.onConfirm(binding.isbIvHudRoate.getSeekBar());
                EToast.showToast(context, context.getString(R.string.str_system_data_setting_progress), 2000, false);
            });
            binding.tvCameraCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });
        }

        public void confirmSuccess() {
            EToast.showToast(context, context.getString(R.string.str_system_data_setting_success), 2000, false);
            if (dialog != null && dialog.isShowing()) {
                dialog.dismiss();
            }
        }

        public void confirmFail() {
            EToast.showToast(context, context.getString(R.string.str_system_data_setting_error), 2000, false);
//            if (dialog != null && dialog.isShowing()) {
//                dialog.dismiss();
//            }
        }

        private void initTips() {
            binding.ivTips.setOnClickListener(v -> {
                dialog.registerChildDialog();
                DetailsUIAlert detailsUIAlert  = new DetailsUIAlert.Builder(context)
                        .create(context.getString(R.string.str_carsetting_high), context.getString(R.string.str_carsetting_high_tips), 1176, 396, Gravity.CENTER);
                detailsUIAlert.show();
                detailsUIAlert.setOnDismissListener(dialogInterface -> dialog.unregisterChildDialog());
            });
        }

        private void initHudRoate() {
            updateHudRoateUI(onProgressChangedListener.getInitStatus());

            binding.isbIvHudRoate.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int i, boolean b) {
                    onProgressChangedListener.onProgressChanged(binding, seekBar, i, b);
                }

                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {
                }

                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {
                }
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public void updateHudRoateUI(int sbHudRoate) {
            onProgressChangedListener.updateHudRoateUI(sbHudRoate, binding);
        }

        public void closeDialog() {
            if (dialog != null && dialog.isShowing()) {
                dialog.dismiss();
            }
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onProgressChanged(DialogAlertQRearRoateBinding binding, SeekBar seekBar, int progress, boolean fromUser);
        void updateHudRoateUI(int sbHudRoate, DialogAlertQRearRoateBinding binding);
        int getInitStatus();
        void onConfirm(SeekBar seekBar);
    }
}
