package com.bitech.vehiclesettings.carapi.constants;

public class CarCondition {
    /**
     * 保养提醒 - 接收
     */
    public static class MaintainRemind {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 保养提醒 - 发送
     */
    public static class MaintainRemind_Set {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 雨刮维修模式信号 - 发送
     * 0x0:Not Active
     * 0x1:Exit
     * 0x2:Entrance
     */
    public static class ICC_MaintenanceMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int EXIT = 0x1;
        public static final int ENTRANCE = 0x2;
    }

    /**
     * 雨刮维修模式信号 - 发送
     * 0x0:Not Active
     * 0x1:Front Wiper
     * 0x2:Rear Wiper
     * 0x3:Both Wiper
     */
    public static class ICC_WiperID {
        public static final int NOT_ACTIVE = 0x0;
        public static final int FRONT_WIPER = 0x1;
        public static final int REAR_WIPER = 0x2;
        public static final int BOTH_WIPER = 0x3;
    }

    /**
     * 雨刮维修开关当前状态
     * 0x0:Not Active
     * 0x1:Front Wiper
     * 0x2:Rear Wiper
     * 0x3:Both Wiper
     */
    public static class FLZCU_WipeMaintenanceSWSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int FRONT_WIPER = 0x1;
        public static final int REAR_WIPER = 0x2;
        public static final int BOTH_WIPER = 0x3;
    }

    /**
     * 雨刮维修模式禁用原因
     * 0x0:Not Active
     * 0x1:Inhibition Mode
     * 0x2:Gear P Only
     * 0x3:Both
     */
    public static class FLZCU_WipeMntnModeFailCause {
        public static final int NOT_ACTIVE = 0x0;
        public static final int INHIBITION_MODE = 0x1;
        public static final int GEAR_P_ONLY = 0x2;
        public static final int BOTH = 0x3;
    }

    /**
     * 遮阳帘状态
     * 0x0:stopped
     * 0x1:opening slide
     * 0x2:closing slide
     * 0x3:reversing (AP/Stall)
     * 0x4:moving uninitialized
     * 0x5:full close
     * 0x6:full open
     * 0x7:reserved
     */
    public static class SRFR_Movement {
        public static final int STOPPED = 0x0;
        public static final int OPENING_SLIDE = 0x1;
        public static final int CLOSING_SLIDE = 0x2;
        public static final int REVERSING = 0x3;
        public static final int MOVING_UNINITIALIZED = 0x4;
        public static final int FULL_CLOSE = 0x5;
        public static final int FULL_OPEN = 0x6;
        public static final int RESERVED = 0x7;
    }

    /**
     * 遮阳帘学习状态
     * 0x0:no teach run
     * 0x1:teach run successful
     */
    public static class SRFR_TeachRun {
        public static final int NO_TEACH_RUN = 0x0;
        public static final int TEACH_RUN_SUCCESSFUL = 0x1;
    }

    /**
     * 左前胎压警告
     * 0x0:Normal
     * 0x1:Rapid Air Loss(Reserved)
     * 0x2:Low Pressure
     * 0x3:Low Batter
     * 0x4:High Pressure(Reserved)
     * 0x5:High Temperature
     * 0x6:Error Mode
     * 0x7:Not used(Reserved)
     * 下面是mega发的对应值
     * 0->0 Normal
     * 1->8 快速漏气
     * 2->1 低/高压
     * 3->4 低电量
     * 4->1 低/高压
     * 5->2 高温
     * 6->128 故障
     */
    public static class TirePositionWarning_LHFTire {
        //        public static final int NORMAL = 0x0;
//        public static final int RAPID_AIR_LOSS = 0x1;
//        public static final int LOW_PRESSURE = 0x2;
//        public static final int LOW_BATTER = 0x3;
//        public static final int HIGH_PRESSURE = 0x4;
//        public static final int HIGH_TEMPERATURE = 0x5;
//        public static final int ERROR_MODE = 0x6;
//        public static final int NOT_USED = 0x7;
        public static final int NORMAL = 0x0;
        public static final int RAPID_AIR_LOSS = 0x8;
        public static final int LOW_PRESSURE = 0x1;
        public static final int LOW_BATTER = 0x4;
        public static final int HIGH_PRESSURE = 0x1;
        public static final int HIGH_TEMPERATURE = 0x2;
        public static final int ERROR_MODE = 128;
        public static final int NOT_USED = 0x7;
    }

    /**
     * 右前胎压警告
     * 0x0:Normal
     * 0x1:Rapid Air Loss(Reserved)
     * 0x2:Low Pressure
     * 0x3:Low Batter
     * 0x4:High Pressure(Reserved)
     * 0x5:High Temperature
     * 0x6:Error Mode
     * 0x7:Not used(Reserved)
     * 下面是mega发的对应值
     * 0->0 Normal
     * 1->8 快速漏气
     * 2->1 低/高压
     * 3->4 低电量
     * 4->1 低/高压
     * 5->2 高温
     * 6->128 故障
     */
    public static class TirePositionWarning_RHFTire{
//        public static final int NORMAL = 0x0;
//        public static final int RAPID_AIR_LOSS = 0x1;
//        public static final int LOW_PRESSURE = 0x2;
//        public static final int LOW_BATTER = 0x3;
//        public static final int HIGH_PRESSURE = 0x4;
//        public static final int HIGH_TEMPERATURE = 0x5;
//        public static final int ERROR_MODE = 0x6;
//        public static final int NOT_USED = 0x7;
        public static final int NORMAL = 0x0;
        public static final int RAPID_AIR_LOSS = 0x8;
        public static final int LOW_PRESSURE = 0x1;
        public static final int LOW_BATTER = 0x4;
        public static final int HIGH_PRESSURE = 0x1;
        public static final int HIGH_TEMPERATURE = 0x2;
        public static final int ERROR_MODE = 128;
        public static final int NOT_USED = 0x7;
    }
    /**
     * 左后胎压警告
     * 0x0:Normal
     * 0x1:Rapid Air Loss(Reserved)
     * 0x2:Low Pressure
     * 0x3:Low Batter
     * 0x4:High Pressure(Reserved)
     * 0x5:High Temperature
     * 0x6:Error Mode
     * 0x7:Not used(Reserved)
     * 下面是mega发的对应值
     * 0->0 Normal
     * 1->8 快速漏气
     * 2->1 低/高压
     * 3->4 低电量
     * 4->1 低/高压
     * 5->2 高温
     * 6->128 故障
     */
    public static class TirePositionWarning_LHRTire{
        //        public static final int NORMAL = 0x0;
//        public static final int RAPID_AIR_LOSS = 0x1;
//        public static final int LOW_PRESSURE = 0x2;
//        public static final int LOW_BATTER = 0x3;
//        public static final int HIGH_PRESSURE = 0x4;
//        public static final int HIGH_TEMPERATURE = 0x5;
//        public static final int ERROR_MODE = 0x6;
//        public static final int NOT_USED = 0x7;
        public static final int NORMAL = 0x0;
        public static final int RAPID_AIR_LOSS = 0x8;
        public static final int LOW_PRESSURE = 0x1;
        public static final int LOW_BATTER = 0x4;
        public static final int HIGH_PRESSURE = 0x1;
        public static final int HIGH_TEMPERATURE = 0x2;
        public static final int ERROR_MODE = 128;
        public static final int NOT_USED = 0x7;
    }
    /**
     * 右后胎压警告
     * 0x0:Normal
     * 0x1:Rapid Air Loss(Reserved)
     * 0x2:Low Pressure
     * 0x3:Low Batter
     * 0x4:High Pressure(Reserved)
     * 0x5:High Temperature
     * 0x6:Error Mode
     * 0x7:Not used(Reserved)
     * 下面是mega发的对应值
     * 0->0 Normal
     * 1->8 快速漏气
     * 2->1 低/高压
     * 3->4 低电量
     * 4->1 低/高压
     * 5->2 高温
     * 6->128 故障
     */
    public static class TirePositionWarning_RHRTire{
        //        public static final int NORMAL = 0x0;
//        public static final int RAPID_AIR_LOSS = 0x1;
//        public static final int LOW_PRESSURE = 0x2;
//        public static final int LOW_BATTER = 0x3;
//        public static final int HIGH_PRESSURE = 0x4;
//        public static final int HIGH_TEMPERATURE = 0x5;
//        public static final int ERROR_MODE = 0x6;
//        public static final int NOT_USED = 0x7;
        public static final int NORMAL = 0x0;
        public static final int RAPID_AIR_LOSS = 0x8;
        public static final int LOW_PRESSURE = 0x1;
        public static final int LOW_BATTER = 0x4;
        public static final int HIGH_PRESSURE = 0x1;
        public static final int HIGH_TEMPERATURE = 0x2;
        public static final int ERROR_MODE = 128;
        public static final int NOT_USED = 0x7;
    }
}
