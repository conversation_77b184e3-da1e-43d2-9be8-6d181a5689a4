package com.bitech.vehiclesettings.service.voice

import android.content.Context
import android.util.Log
import com.bitech.platformlib.manager.ConditionManager
import com.bitech.platformlib.manager.NewEnergyManager
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.fragment.ConditionFragment
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.DialogNavigationUtils
import com.bitech.vehiclesettings.view.condition.NextMaintenanceUIAlert
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P


class ConditionControl() {
    private var conditionManager: ConditionManager = ConditionManager.getInstance()
    private var newEnergyManager: NewEnergyManager = NewEnergyManager.getInstance()
    private val context: Context = MyApplication.getInstance()
    //单回复提示语id
    private fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }
    private fun sendResultCode(respondId: String, mValue: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }
    //特殊提示语id
    private fun sendResultCode(respondId: String, mValue: String, mUnique: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        param.unique = mUnique
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    /**
     * 重置累计里程
     */
    fun resetMileageAccrual(){
        sendResultCode(VDVRRespondID.reset_cumulative_mileage_1)
    }

    //打开/关闭香氛迎宾
    fun setFragranceWelcome(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_fragrance_welcome_3)
        }  else {
            sendResultCode(VDVRRespondID.close_fragrance_welcome_3)
        }
    }
    //打开/关闭香氛
    fun setFragrance(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_fragrance_3)
        } else {
            sendResultCode(VDVRRespondID.close_fragrance_3)
        }
    }
    //查看目前的香氛类型
    fun getFragranceType(){
        sendResultCode(VDVRRespondID.View__current_fragrance_types_4)
    }
    //查询香氛余量
    fun getFragranceLeft(){
        sendResultCode(VDVRRespondID.query_amount_of_fragrance_5)
    }
    //切换香氛模式，无指定值
    fun setRandomFragranceMode() {
        sendResultCode(VDVRRespondID.switch_fragrance_mode_5)
    }
    //切换香氛模式，指定值
    fun setModeFragrance() {
        sendResultCode(VDVRRespondID.switch_fragrance_to_xxx_type_3)
    }
    //香氛浓度(风速)调高具体数值
    fun FragranceConcentrationUp() {
        sendResultCode(VDVRRespondID.raise_fragrance_intensity_by_number_5)
    }
    //香氛浓度(风速)调低具体数值
    fun FragranceConcentrationDown() {
        sendResultCode(VDVRRespondID.lower_fragrance_intensity_by_number_5)
    }

    /**
     * 香氛浓度(风速)调到具体数值
     */
    fun setFragranceIntensityNumber(value: String) {
        if(value != ""){
            sendResultCode(VDVRRespondID.adjust_fragrance_intensity_to_number_5)
        }
    }

    /**
     * 香氛浓度(风速)调高/低一点/调到高中低挡,最高,最低
     */
    fun setFragranceIntensityGears(value: String) {
        if(value != ""){
            when(value.toInt()){
                ConditionConstant.AR_PARAM_High -> sendResultCode(VDVRRespondID.raise_fragrance_intensity_little_5)
                ConditionConstant.AR_PARAM_Low -> sendResultCode(VDVRRespondID.lower_fragrance_intensity_little_5)
                ConditionConstant.AR_PARAM_Middle_High -> sendResultCode(VDVRRespondID.adjust_fragrance_intensity_to_gear_5)
                ConditionConstant.AR_PARAM_Middle -> sendResultCode(VDVRRespondID.adjust_fragrance_intensity_to_gear_5)
                ConditionConstant.AR_PARAM_Middle_Low -> sendResultCode(VDVRRespondID.adjust_fragrance_intensity_to_gear_5)
                ConditionConstant.AR_PARAM_Max -> sendResultCode(VDVRRespondID.adjust_fragrance_intensity_to_max_5)
                ConditionConstant.AR_PARAM_Min -> sendResultCode(VDVRRespondID.adjust_fragrance_intensity_to_min_5)
                else -> sendResultCode(VDVRRespondID.adjust_fragrance_intensity_to_gear_5)
            }
        }
    }

    /**
     * 打开or关闭空气进化自动运行
     * @param flag:true打开，false关闭
     */
    fun openOrcloseAirpurification(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_automatic_of_air_purification_1)
        } else {
            sendResultCode(VDVRRespondID.close_automatic_of_air_purification_1)
        }
    }

    /**
     * 打开or关闭加湿功能
     * @param flag:true打开，false关闭
     */
    fun openOrcloseHumidificationfunction(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_humidifier_1)
        } else {
            sendResultCode(VDVRRespondID.close_humidifier_1)
        }
    }

    /**
     * 关联主题
     * @param flag:true打开，false关闭
     */
    fun relatedtopics(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_atmosphere_lamp_related_to_the_large_screen_UI_mode_1)
        } else {
            sendResultCode(VDVRRespondID.close_atmosphere_lamp_related_to_the_large_screen_UI_mode_1)
        }
    }

    /**
     * 关联驾驶模式
     *  flag true打开，false关闭
     */
    fun relatedLinkedDrivingModes(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_atmosphere_lamp_related_to_drive_mode_1)
        } else {
            sendResultCode(VDVRRespondID.close_atmosphere_lamp_related_to_drive_mode_1)
        }
    }

    /**
     * 胎压查询
     */
    fun queryTirePressure() {
        if (conditionManager?.getLeftFrontTireKpaNum() == Integer.MIN_VALUE && conditionManager?.getRightFrontTireKpaNum() == Integer.MIN_VALUE && conditionManager?.getLeftRearTireKpaNum() == Integer.MIN_VALUE && conditionManager?.getRightRearTireKpaNum() == Integer.MIN_VALUE) {
            sendResultCode(VDVRRespondID.vehicleInfo_8)
        } else {
            if ((conditionManager?.getLeftFrontTireStatus() == 0 || conditionManager?.getLeftFrontTireStatus() == 7) && (conditionManager?.getRightFrontTireStatus() == 0 || conditionManager?.getRightFrontTireStatus() == 7) && (conditionManager?.getLeftRearTireStatus() == 0 || conditionManager?.getLeftRearTireStatus() == 7) && (conditionManager?.getRightRearTireStatus() == 0 || conditionManager?.getRightRearTireStatus() == 7)) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    CarSettingConstant.CLOSE_SCREEN,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.vehicleInfo_6)
            } else {
                var number1 = CarSettingConstant.TIRE_PRESSURE_NORMAL
                var number2 = CarSettingConstant.TIRE_PRESSURE_NORMAL
                var number3 = CarSettingConstant.TIRE_PRESSURE_NORMAL
                var number4 = CarSettingConstant.TIRE_PRESSURE_NORMAL
                if (conditionManager?.getLeftFrontTireStatus() != CarSettingConstant.TIRE_PRESSURE_SIGNAL_0 && conditionManager?.getLeftFrontTireStatus() != CarSettingConstant.TIRE_PRESSURE_SIGNAL_7) number1 =
                    CarSettingConstant.TIRE_PRESSURE_ABNORMAL
                if (conditionManager?.getRightFrontTireStatus() != CarSettingConstant.TIRE_PRESSURE_SIGNAL_0 && conditionManager?.getRightFrontTireStatus() != CarSettingConstant.TIRE_PRESSURE_SIGNAL_7) number2 =
                    CarSettingConstant.TIRE_PRESSURE_ABNORMAL
                if (conditionManager?.getLeftRearTireStatus() != CarSettingConstant.TIRE_PRESSURE_SIGNAL_0 && conditionManager?.getLeftRearTireStatus() != CarSettingConstant.TIRE_PRESSURE_SIGNAL_7) number3 =
                    CarSettingConstant.TIRE_PRESSURE_ABNORMAL
                if (conditionManager?.getRightRearTireStatus() != CarSettingConstant.TIRE_PRESSURE_SIGNAL_0 && conditionManager?.getRightRearTireStatus() != CarSettingConstant.TIRE_PRESSURE_SIGNAL_7) number4 =
                    CarSettingConstant.TIRE_PRESSURE_ABNORMAL
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    CarSettingConstant.CLOSE_SCREEN,
                    CommonConst.DIALOG_OPEN
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左前"
                )
                if (number2 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "右前"
                )
                if (number3 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左后"
                )
                if (number4 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "右后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number2 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左前-右前"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number3 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左前-左后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number4 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左前-右后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number2 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "右前-左后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number2 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "右前-右后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number2 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左后-右后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number2 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number3 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左前-右前-左后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number2 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number4 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左前-右前-右后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number3 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number4 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "左前-左后-右后"
                )
                if (number2 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number3 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL && number4 == CarSettingConstant.TIRE_PRESSURE_ABNORMAL) sendResultCode(
                    VDVRRespondID.vehicleInfo_7,
                    "右前-左后-右后"
                )
                if (number1 == CarSettingConstant.TIRE_PRESSURE_NORMAL && number2 == CarSettingConstant.TIRE_PRESSURE_NORMAL && number3 == CarSettingConstant.TIRE_PRESSURE_NORMAL && number4 == CarSettingConstant.TIRE_PRESSURE_NORMAL) {
                    //todo 回复语ID缺失
//                    sendResultCode(VDVRRespondID.vehicleInfo_7_1)
                }
            }
        }
    }

    /**
     * 查询剩余油量
     */
    fun queryFuelLevel() {
        //TODO sendResultCode(VDVRRespondID.vehicleInfo_17_2)
        val fuelPercent = newEnergyManager.fuelPercent
        val enduranceFuelCurrentMode = newEnergyManager.enduranceFuelCurrentMode
        //油量可查，且小于1/8
        if (fuelPercent < ConditionConstant.FUEL_LOW_PERCENT && enduranceFuelCurrentMode < ConditionConstant.FUEL_LOW_MILEAGE) {
            sendResultCode(
                VDVRRespondID.vehicleInfo_17_1,
                "${fuelPercent}-${enduranceFuelCurrentMode}"
            )
        } else {
            sendResultCode(
                VDVRRespondID.vehicleInfo_17,
                "${fuelPercent}-${enduranceFuelCurrentMode}"
            )
        }
    }

    fun queryMaintenanceInfo() {
        val days = conditionManager.reMaintainTime / ConditionConstant.SECONDS_PER_DAY //剩余保养时间
        val reMaintainMileage = conditionManager.reMaintainMileage
        sendResultCode(
            VDVRRespondID.vehicleInfo_18,
            "${reMaintainMileage}-${days}"
        )
    }

    /**
     * 打开/关闭车辆保养界面
     */
    fun setVehicleMaintainPage(isOpen: Boolean) {
        if (isOpen) {
            if (!NextMaintenanceUIAlert.isShow) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    ConditionFragment.DIALOG_ALERT_CONDITION_NEXT_MAINTENANCE,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.vehicleInfo_19)
            }
        } else {
            DialogNavigationUtils.launchMainActivity(
                context,
                MainActivity.MainTabIndex.CONDITION,
                ConditionFragment.DIALOG_ALERT_CONDITION_NEXT_MAINTENANCE,
                CommonConst.DIALOG_CLOSE
            )
            sendResultCode(VDVRRespondID.vehicleInfo_20)
        }
    }

    object ConditionConstant {
        const val AR_PARAM_High: Int = 0
        const val AR_PARAM_Low: Int = 1
        const val AR_PARAM_Middle: Int = 2
        const val AR_PARAM_Middle_High: Int = 3
        const val AR_PARAM_Middle_Low: Int = 4
        const val AR_PARAM_Max: Int = 5
        const val AR_PARAM_Min: Int = 6
        const val FUEL_LOW_PERCENT: Double = 12.5
        const val FUEL_LOW_MILEAGE: Int = 50
        const val SECONDS_PER_DAY: Int = 86400
    }
}
