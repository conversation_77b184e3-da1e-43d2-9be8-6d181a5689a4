package com.bitech.vehiclesettings.service.earlywarning;

import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

/**
 * 车道偏离预警触发
 */
public class LdwService {
    private static final String TAG = LdwService.class.getSimpleName();
    private static volatile LdwService instance;
    private static final long TARGET_INTERVAL_MS = 250;
    private Handler mHandler;
    private Runnable mTaskRunnable;
    private volatile boolean isRunning = false;
    private long mLastExecutionTime;
    int count = 0;

    public static LdwService getInstance() {
        if (instance == null) {
            synchronized (LdwService.class) {
                if (instance == null) {
                    instance = new LdwService();
                }
            }
        }
        return instance;
    }

    public LdwService() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public void start(int type, int sigal) {
        if (isRunning) {
            return;
        }
        isRunning = true;
        mLastExecutionTime = SystemClock.elapsedRealtime();
        mTaskRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isRunning) {
                    return;
                }
                // 1. 执行任务
                Log.d(TAG, "[warn] 车道偏移辅助 start ");
                doTask(type, sigal);
                // 2. 计算下一次执行时间（动态补偿误差）
                long currentTime = SystemClock.elapsedRealtime();
                long elapsed = currentTime - mLastExecutionTime;
                long nextDelay = Math.max(0, TARGET_INTERVAL_MS - elapsed);
                // 3. 递归调度下一次任务
                if (count == 1) {
                    mHandler.postDelayed(this, nextDelay - 200);
                } else {
                    mHandler.postDelayed(this, nextDelay);
                }
                mLastExecutionTime = currentTime + nextDelay;
            }
        };
        mHandler.post(mTaskRunnable);
    }

    public void stop() {
        if (mTaskRunnable != null) {
            Log.d(TAG, "[warn] 车道偏移辅助 stop ");
            mHandler.removeCallbacks(mTaskRunnable);
        }
        isRunning = false;
    }

    private void doTask(int type, int signal) {
        Log.d(TAG, "[warn] 车道偏移辅助 doTask type:" + type + ",siganl:" + signal);
    }

}
