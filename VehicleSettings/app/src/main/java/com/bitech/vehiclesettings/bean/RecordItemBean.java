package com.bitech.vehiclesettings.bean;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class RecordItemBean implements Comparable<RecordItemBean> {
    private final int appIcon;
    private final String appName;
    private final int visitCount;
    private final String lastAccessTime; // 存储格式: "2024年03月31日 14:30"

    private static final SimpleDateFormat DATE_FORMAT =
            new SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.CHINA);

    public RecordItemBean(int appIcon, String appName, int visitCount, String lastAccessTime) {
        this.appIcon = appIcon;
        this.appName = appName;
        this.visitCount = visitCount;
        this.lastAccessTime = lastAccessTime;
    }

    public int getAppIcon() {
        return appIcon;
    }

    public String getAppName() {
        return appName;
    }

    public int getVisitCount() {
        return visitCount;
    }

    public String getLastAccessTime() {
        return lastAccessTime;
    }

    /**
     * 获取 lastAccessTime 对应的 Date 对象（用于排序）
     */
    public Date getParsedDate() {
        try {
            return DATE_FORMAT.parse(lastAccessTime);
        } catch (ParseException e) {
            e.printStackTrace();
            return new Date(0); // 如果解析失败，返回一个极小值
        }
    }

    /**
     * 使 RecordItemBean 按 lastAccessTime 进行降序排序
     */
    @Override
    public int compareTo(RecordItemBean other) {
        return other.getParsedDate().compareTo(this.getParsedDate()); // 降序排列
    }
}
