package com.bitech.vehiclesettings.broadcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.manager.CarBtManager
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.view.dialog.ConfirmDialog
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.VDEvent
import com.chery.ivi.vdb.event.base.VDKey
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink
import com.chery.ivi.vdb.event.id.phonelink.VDValuePhoneLink
import com.chery.ivi.vdb.event.id.phonelink.bean.VDLinkDevice

class UsbStateReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "UsbStateReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        when (action) {
            UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
                device?.let {
                    LogUtil.d(TAG, "USB device attached: ${it.deviceName}")
                    // 判断是否为苹果设备
                    val isAppleDevice = it.vendorId == 0x05AC // 苹果的Vendor ID
                    LogUtil.d(TAG, "isAppleDevice: $isAppleDevice")
                    if (isAppleDevice) {
                        openCarplayDialog(intent)
                    }
                }
            }

            UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
                device?.let {
                    LogUtil.d(TAG, "USB device detached: ${it.deviceName}")
                }
            }
        }
    }

    private fun openCarplayDialog(intent: Intent) {
        Handler(Looper.getMainLooper()).post {
            val event1 =
                VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
            val carBtManager = CarBtManager.instance
            if (event1 != null) {
                val bundle = event1.payload
                val devicePhoneLink = bundle.getParcelable<VDLinkDevice>(VDKey.INFO)
                val dialog = ConfirmDialog(MyApplication.getContext()).apply {
                    if (devicePhoneLink != null) {
                        val name = devicePhoneLink.name
                        setTips(
                            MyApplication.getContext()
                                .getString(R.string.bt_cp_phone_wire_tips, name)
                        )
                    } else if (carBtManager.getConnectedDevices().size > 0) {
                        val btName = carBtManager.getCurrentConnectedBluetoothDeviceName()
                        setTips(
                            MyApplication.getContext()
                                .getString(R.string.bt_cp_phone_wire_tips, btName)
                        )
                    } else {
                        setTips(
                            MyApplication.getContext()
                                .getString(R.string.bt_cp_phone_wire_tips1)
                        )
                    }
                }
                // 倒计时初始值
                val countDownSeconds = 10
                val handler = Handler(Looper.getMainLooper())
                // 真正执行的倒计时 Runnable
                val countDownTask = object : Runnable {
                    var left = countDownSeconds
                    override fun run() {
                        if (left <= 0) {
                            // 时间到了，自动关闭
                            if (dialog.isShowing) {
                                dialog.dismiss()
                            }
                            return
                        }

                        // 更新按钮文本
                        dialog.setCancelBtnText("取消（${left}）")
                        left--
                        handler.postDelayed(this, 1000)
                    }
                }

                // 启动倒计时
                handler.post(countDownTask)

                dialog.setDialogClickCallback(object :
                    ConfirmDialog.OnConfirmDialogClickCallback {
                    override fun onConfirmClick() {
                        // 提前结束倒计时
                        handler.removeCallbacks(countDownTask)
                        val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
                        device?.let {
                            val serial = device.serialNumber
                            val vdLinkDevice = VDLinkDevice().apply {
                                type = VDValuePhoneLink.DeviceType.CARPLAY
                                isWireless = false
                                serialNum = serial
                            }
                            val payload = Bundle().apply {
                                putInt(VDKey.TYPE, VDValuePhoneLink.ServerId.CARPLAY)
                                putParcelable(VDKey.DATA, vdLinkDevice)
                            }
                            val event =
                                VDEvent(VDEventPhoneLink.CONNECT_DEVICE, payload)
                            VDBus.getDefault().set(event)
                        }
                        dialog.dismiss()
                    }

                    override fun onCancelClick() {
                        // 用户主动点取消也停止倒计时
                        handler.removeCallbacks(countDownTask)
                        dialog.dismiss()
                    }
                })
                dialog.show()
            }
        }
    }
}