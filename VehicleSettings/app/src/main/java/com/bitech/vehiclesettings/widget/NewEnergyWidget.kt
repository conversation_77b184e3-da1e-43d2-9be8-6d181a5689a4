package com.bitech.vehiclesettings.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.RemoteViews
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.service.NewEnergyLifeCycle
import com.lion.api.module.navi.NaviProxy
import com.lion.api.module.navi.NaviValue
import com.lion.api.module.navi.bean.OperatorPoiBean


/**
 * 新能源Widget
 */
class NewEnergyWidget : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        super.onUpdate(context, appWidgetManager, appWidgetIds)
        Log.d(TAG, "onUpdate")
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onEnabled(context: Context?) {
        super.onEnabled(context)
        Log.d(TAG, "onEnabled")
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: Bundle?
    ) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions)
        Log.d(TAG, "onAppWidgetOptionsChanged")
        updateAppWidget(context, appWidgetManager, appWidgetId)
    }

    override fun onDeleted(context: Context?, appWidgetIds: IntArray?) {
        super.onDeleted(context, appWidgetIds)
        Log.d(TAG, "onDeleted")
    }

    override fun onDisabled(context: Context?) {
        super.onDisabled(context)
        Log.d(TAG, "onDisabled")
    }

    override fun onRestored(context: Context?, oldWidgetIds: IntArray?, newWidgetIds: IntArray?) {
        super.onRestored(context, oldWidgetIds, newWidgetIds)
        Log.d(TAG, "onRestored")
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        if (ACTION_QUERY_CHARGING == intent.action) {
            Log.d(
                TAG,
                "onReceive ACTION_QUERY_CHARGING widgetId= " + intent.getIntExtra(
                    AppWidgetManager.EXTRA_APPWIDGET_ID,
                    -1
                )
            )
            try {
                NaviProxy.getInstance().operatorPoiByType(
                    OperatorPoiBean(
                        NaviValue.OperatorPoiType.KEYWORD,
                        "充电桩"
                    )
                );
            } catch (e: Exception) {
                Log.e(TAG, "导航搜索异常: " + e.message)

                val targetIntent =
                    context.packageManager.getLaunchIntentForPackage("com.lion.appfwk.navi")
                context.startActivity(targetIntent)
            }
        } else if (ACTION_NEW_ENERGY_WIDGET_REFRESH == intent.action) {
            Log.d(TAG, "onReceive ACTION_NEW_ENERGY_WIDGET_REFRESH")

            val views = RemoteViews(context.packageName, R.layout.widget_new_energy)
            views.setTextViewText(
                R.id.tvRemainingCapacity,
                NewEnergyLifeCycle.mCurrentSOC.get().toString() + "%"
            )
            views.setTextViewText(
                R.id.tvPureElectricEndurance,
                NewEnergyLifeCycle.mEnduranceElectric.get().toString() + "km"
            )
            val appWidgetManager = AppWidgetManager.getInstance(context)
            val componentName = ComponentName(context, NewEnergyWidget::class.java)
            appWidgetManager.updateAppWidget(componentName, views)
        }
    }

    companion object {

        internal fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            Log.d(TAG, "updateAppWidget")

            val views = RemoteViews(context.packageName, R.layout.widget_new_energy)
            views.setTextViewText(
                R.id.tvRemainingCapacity,
                NewEnergyLifeCycle.mCurrentSOC.get().toString() + "%"
            )
            views.setTextViewText(R.id.tvPureElectricEndurance,
                NewEnergyLifeCycle.mEnduranceElectric.get().toString() + "km")

            val intent = Intent(context, NewEnergyWidget::class.java).apply {
                action = ACTION_QUERY_CHARGING
                putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
            }
            val pendingIntent = PendingIntent.getBroadcast(
                context, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.tvSearchCharingStation, pendingIntent)

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }

        const val ACTION_QUERY_CHARGING = "com.bitech.vehiclesettings.widget.search_charging_stations"
        const val ACTION_NEW_ENERGY_WIDGET_REFRESH =
            "com.bitech.vehiclesettings.widget.new_energy_widget_refresh"

        const val TAG = "VehicleSettingWidget"
    }

}