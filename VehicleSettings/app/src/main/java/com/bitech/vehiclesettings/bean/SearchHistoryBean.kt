package com.bitech.vehiclesettings.bean

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * @ClassName: SearchHistoryBean
 * 
 * @Date:  2024/4/15 9:19
 * @Description: 搜索历史实体类Bean.
 **/
@Entity(tableName = "searchHistory")
data class SearchHistoryBean(@PrimaryKey(autoGenerate = false) var historyResult: String) :
    Comparable<SearchHistoryBean> {

    // 搜索时间
    var searchTime: Long = 0L

    // 搜索结果对应的一级菜单id
    var firstMenuId = -1

    /**
     * 自定义搜索历史排序方式.
     *
     * @param other 待对比的搜索历史
     * @return Int <0,this对象>other对象；=0,this对象=other对象；>0.this对象>other对象
     */
    override fun compareTo(other: SearchHistoryBean): Int {
        // 按照时间的先后顺序进行排序，后搜索的排前面
        return (other.searchTime - this.searchTime).toInt()
    }
}
