package com.bitech.vehiclesettings.view.light;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.ColorInt;

public class EdgeGradientView extends View {
    private Paint paint = new Paint();
    private int gradientWidth = 100; // 渐变区域宽度（像素）
    private int gradientColor = Color.RED; // 基础颜色

    public EdgeGradientView(Context context) {
        super(context);
        init();
    }
    public EdgeGradientView(Context context, AttributeSet attrs) {
        super(context, attrs);
        // 解析自定义属性
        init();
    }
    private void init() {
        // 设置混合模式实现遮罩
         paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_OUT));
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        // 左边缘渐变
        LinearGradient leftGradient = new LinearGradient(
                0, 0, gradientWidth, 0,
                new int[]{Color.TRANSPARENT, gradientColor},
                new float[]{0f, 1f}, Shader.TileMode.CLAMP
        );

        // 右边缘渐变
        LinearGradient rightGradient = new LinearGradient(
                getWidth() - gradientWidth, 0, getWidth(), 0,
                new int[]{gradientColor, Color.TRANSPARENT},
                new float[]{0f, 1f}, Shader.TileMode.CLAMP
        );

        // 绘制左右遮罩
        paint.setShader(leftGradient);
        canvas.drawRect(0, 0, gradientWidth, getHeight(), paint);

        paint.setShader(rightGradient);
        canvas.drawRect(getWidth() - gradientWidth, 0, getWidth(), getHeight(), paint);


   }

    // 动态调整参数
    public void setGradientWidth(int width) {
        this.gradientWidth = width;
        invalidate();
    }

    public void setGradientColor(@ColorInt int color) {
        this.gradientColor = color;
        invalidate();
    }
}
