package com.bitech.vehiclesettings.view.common;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import com.bitech.vehiclesettings.R;

public class SlideToConfirmView extends View {
    private static final String TAG = "SlideToConfirmView";

    // 默认资源ID
    private int DEFAULT_THUMB_RES = R.mipmap.ic_seekbar_thumb;
    private int DEFAULT_SUCCESS_THUMB_RES = R.mipmap.ic_seekbar_thumb_success;

    // 画笔
    private Paint backgroundPaint;
    private Paint progressPaint;
    private Paint textPaint;
    private int seekbarColor = Color.GREEN;

    // 状态和进度
    private float progress = 0;
    private float thumbPosition = 0;
    private boolean isDragging = false;
    private boolean isConfirmed = false;

    // 文字相关
    private String text = "滑动来确认";
    private String successText = "验证成功";
    private int textColor = Color.DKGRAY;
    private int successTextColor = Color.WHITE;

    // 图形相关
    private RectF progressRect = new RectF();
    private float cornerRadius = 0;
    private int thumbWidth = 0;
    private int thumbHeight = 0;
    private float thumbMargin = 0;
    private float progressPadding = 0;

    // 位图资源
    private Bitmap thumbBitmap;
    private Bitmap successThumbBitmap;
    private Bitmap currentThumbBitmap;
    private Rect thumbRect = new Rect();

    // 动画
    private ValueAnimator resetAnimator;

    // 监听器
    private OnConfirmListener confirmListener;

    public SlideToConfirmView(Context context) {
        super(context);
        init(context, null);
    }

    public SlideToConfirmView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public SlideToConfirmView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    public void setSeekbarColor(int color) {
        seekbarColor = color;
        progressPaint.setColor(color); // 关键：更新画笔颜色
        invalidate(); // 触发重绘
    }

    public void setSeekBarThumbSuccess(int successThumbResId) {
        // 1. 回收旧的 Bitmap（避免内存泄漏）
        if (successThumbBitmap != null && !successThumbBitmap.isRecycled()) {
            successThumbBitmap.recycle();
        }

        // 2. 加载新的 Bitmap
        BitmapFactory.Options options = new BitmapFactory.Options();
        if (thumbWidth > 0 && thumbHeight > 0) {
            options.inSampleSize = calculateSampleSize(successThumbResId, thumbWidth, thumbHeight);
        }
        successThumbBitmap = BitmapFactory.decodeResource(getResources(), successThumbResId, options);

        // 3. 按需缩放
        if (thumbWidth > 0 && thumbHeight > 0 && successThumbBitmap != null) {
            successThumbBitmap = Bitmap.createScaledBitmap(
                    successThumbBitmap, thumbWidth, thumbHeight, true
            );
        }

        // 4. 如果是确认状态，立即更新当前 Thumb
        if (isConfirmed) {
            currentThumbBitmap = successThumbBitmap;
            invalidate();
        }
    }

    /**
     * 计算 BitmapFactory.Options.inSampleSize，确保加载的图片不超过目标尺寸
     * @param resId     图片资源ID
     * @param reqWidth  目标宽度（像素）
     * @param reqHeight 目标高度（像素）
     * @return 合适的 inSampleSize（2的幂次方，如 1, 2, 4, 8...）
     */
    private int calculateSampleSize(int resId, int reqWidth, int reqHeight) {
        if (reqWidth <= 0 || reqHeight <= 0) {
            return 1; // 无需缩放
        }

        // 1. 获取原始图片尺寸
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true; // 只解析边界，不加载像素
        BitmapFactory.decodeResource(getResources(), resId, options);
        int width = options.outWidth;
        int height = options.outHeight;

        // 2. 计算缩放比例
        int inSampleSize = 1;
        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;

            // 逐步尝试 2 的幂次方缩放，直到满足条件
            while ((halfHeight / inSampleSize) >= reqHeight
                    && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        return inSampleSize;
    }

    private void init(Context context, AttributeSet attrs) {
        try {
            // 初始化默认值
            int thumbResId = DEFAULT_THUMB_RES;
            int successThumbResId = DEFAULT_SUCCESS_THUMB_RES;

            // 从属性集读取自定义属性
            if (attrs != null) {
                TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.SlideToConfirmView);

                // 读取基本属性
                text = a.getString(R.styleable.SlideToConfirmView_text) != null ?
                        a.getString(R.styleable.SlideToConfirmView_text) : text;
                successText = a.getString(R.styleable.SlideToConfirmView_successText) != null ?
                        a.getString(R.styleable.SlideToConfirmView_successText) : successText;
                textColor = a.getColor(R.styleable.SlideToConfirmView_textColor, textColor);
                successTextColor = a.getColor(R.styleable.SlideToConfirmView_successTextColor, successTextColor);

                // 颜色属性
                int backgroundColor = a.getColor(R.styleable.SlideToConfirmView_backgroundColor, Color.LTGRAY);
                int progressColor = a.getColor(R.styleable.SlideToConfirmView_progressColor, seekbarColor);

                // 资源ID
                thumbResId = a.getResourceId(R.styleable.SlideToConfirmView_thumbImage, thumbResId);
                successThumbResId = a.getResourceId(R.styleable.SlideToConfirmView_successThumbImage, successThumbResId);

                // 尺寸属性
                float textSize = a.getDimension(R.styleable.SlideToConfirmView_textSize, 40);
                cornerRadius = a.getDimension(R.styleable.SlideToConfirmView_cornerRadius, 0);
                thumbWidth = a.getDimensionPixelSize(R.styleable.SlideToConfirmView_thumbWidth, 0);
                thumbHeight = a.getDimensionPixelSize(R.styleable.SlideToConfirmView_thumbHeight, 0);
                thumbMargin = a.getDimension(R.styleable.SlideToConfirmView_thumbMargin,
                        TypedValue.applyDimension(
                                TypedValue.COMPLEX_UNIT_DIP,
                                4,
                                getResources().getDisplayMetrics()));
                progressPadding = a.getDimension(R.styleable.SlideToConfirmView_progressPadding, 0);

                a.recycle();

                // 初始化画笔
                backgroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
                backgroundPaint.setColor(backgroundColor);

                progressPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
                progressPaint.setColor(progressColor);

                textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
                textPaint.setColor(textColor);
                textPaint.setTextSize(textSize);
                textPaint.setTextAlign(Paint.Align.CENTER);
            } else {
                // 默认画笔设置
                backgroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
                backgroundPaint.setColor(Color.LTGRAY);

                progressPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
                progressPaint.setColor(Color.GREEN);

                textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
                textPaint.setColor(textColor);
                textPaint.setTextSize(40);
                textPaint.setTextAlign(Paint.Align.CENTER);
            }

            // 初始化位图资源
            initThumbBitmaps(thumbResId, successThumbResId);
            currentThumbBitmap = thumbBitmap;

            // 初始化动画
            resetAnimator = ValueAnimator.ofFloat(0, 0);
            resetAnimator.setDuration(300);
            resetAnimator.setInterpolator(new DecelerateInterpolator());
            resetAnimator.addUpdateListener(animation -> {
                progress = (float) animation.getAnimatedValue();
                invalidate();
            });

            // 启用离屏缓冲
            setLayerType(LAYER_TYPE_SOFTWARE, null);

        } catch (Exception e) {
            Log.e(TAG, "Initialization failed", e);
            throw new RuntimeException("SlideToConfirmView init failed: " + e.getMessage());
        }
    }

    private void initThumbBitmaps(int thumbResId, int successThumbResId) {
        try {
            // 加载原始位图
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeResource(getResources(), thumbResId, options);

            // 计算缩放比例
            int inSampleSize = 1;
            if (thumbWidth > 0 && thumbHeight > 0) {
                while (options.outWidth / inSampleSize > thumbWidth ||
                        options.outHeight / inSampleSize > thumbHeight) {
                    inSampleSize *= 2;
                }
            }

            // 实际加载位图
            options.inJustDecodeBounds = false;
            options.inSampleSize = inSampleSize;

            Bitmap originalThumb = BitmapFactory.decodeResource(getResources(), thumbResId, options);
            Bitmap originalSuccessThumb = BitmapFactory.decodeResource(getResources(), successThumbResId, options);

            if (originalThumb == null || originalSuccessThumb == null) {
                throw new IllegalArgumentException("Failed to load thumb images");
            }

            // 按需缩放
            if (thumbWidth > 0 && thumbHeight > 0) {
                thumbBitmap = Bitmap.createScaledBitmap(originalThumb, thumbWidth, thumbHeight, true);
                successThumbBitmap = Bitmap.createScaledBitmap(originalSuccessThumb, thumbWidth, thumbHeight, true);

                // 回收原始位图
                if (originalThumb != thumbBitmap) originalThumb.recycle();
                if (originalSuccessThumb != successThumbBitmap) originalSuccessThumb.recycle();
            } else {
                thumbBitmap = originalThumb;
                successThumbBitmap = originalSuccessThumb;
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to load thumb images", e);
            // 使用默认颜色绘制圆形作为回退
            thumbBitmap = createDefaultThumb(thumbWidth > 0 ? thumbWidth : 48,
                    thumbHeight > 0 ? thumbHeight : 48,
                    Color.WHITE);
            successThumbBitmap = createDefaultThumb(thumbWidth > 0 ? thumbWidth : 48,
                    thumbHeight > 0 ? thumbHeight : 48,
                    Color.GREEN);
        }
    }

    private Bitmap createDefaultThumb(int width, int height, int color) {
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setColor(color);
        canvas.drawCircle(width / 2f, height / 2f, Math.min(width, height) / 2f, paint);
        return bitmap;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (cornerRadius == 0) {
            cornerRadius = h / 2f;
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        float width = getWidth();
        float height = getHeight();

        // 1. 绘制背景（全尺寸）
        canvas.drawRoundRect(
                0, 0, width, height,
                cornerRadius, cornerRadius,
                backgroundPaint
        );

        // 2. 绘制进度条（全尺寸）
        progressRect.set(0, 0, width * progress, height);
        canvas.drawRoundRect(progressRect, cornerRadius, cornerRadius, progressPaint);

        // 3. 计算滑块位置（添加左右边距约束）
        float thumbRange = width - 2 * thumbMargin; // 可滑动范围
        thumbPosition = thumbMargin + (thumbRange * progress);

        // 边界检查
        float thumbHalfWidth = currentThumbBitmap.getWidth() / 2f;
        thumbPosition = Math.max(thumbMargin + thumbHalfWidth,
                Math.min(thumbPosition,
                        width - thumbMargin - thumbHalfWidth));

        // 4. 绘制滑块（垂直居中）
        float thumbTop = (height - currentThumbBitmap.getHeight()) / 2;
        thumbRect.set(
                (int)(thumbPosition - thumbHalfWidth),
                (int)thumbTop,
                (int)(thumbPosition + thumbHalfWidth),
                (int)(thumbTop + currentThumbBitmap.getHeight())
        );
        canvas.drawBitmap(currentThumbBitmap, null, thumbRect, null);

        // 5. 绘制文字（不受边距影响）
        float textY = height / 2 - (textPaint.descent() + textPaint.ascent()) / 2;
        textPaint.setColor(isConfirmed ? successTextColor : textColor);
        canvas.drawText(isConfirmed ? successText : text, width / 2, textY, textPaint);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (isConfirmed) return false;

        float x = event.getX();
        float width = getWidth();
        float thumbRange = width - 2 * thumbMargin; // 可滑动范围
        float thumbHalfWidth = currentThumbBitmap.getWidth() / 2f;

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 检查是否点击了滑块（考虑边距）
                if (x >= thumbPosition - thumbHalfWidth &&
                        x <= thumbPosition + thumbHalfWidth) {
                    isDragging = true;
                    return true;
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (isDragging) {
                    // 计算progress时映射到可滑动范围
                    progress = (x - thumbMargin) / thumbRange;
                    progress = Math.max(0, Math.min(1, progress));
                    invalidate();
                    return true;
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (isDragging) {
                    isDragging = false;
                    if (progress >= 0.95f) {
                        confirm();
                    } else {
                        startResetAnimation();
                    }
                    return true;
                }
                break;
        }

        return super.onTouchEvent(event);
    }

    private void confirm() {
        isConfirmed = true;
        currentThumbBitmap = successThumbBitmap;
        progress = 1;
        invalidate();
        if (confirmListener != null) {
            confirmListener.onConfirm();
        }
    }

    private void startResetAnimation() {
        if (resetAnimator.isRunning()) {
            resetAnimator.cancel();
        }
        resetAnimator.setFloatValues(progress, 0);
        resetAnimator.start();
    }

    public void reset() {
        isConfirmed = false;
        currentThumbBitmap = thumbBitmap;
        progress = 0;
        invalidate();
    }

    public void setThumbSize(int width, int height) {
        this.thumbWidth = width;
        this.thumbHeight = height;
        initThumbBitmaps(DEFAULT_THUMB_RES, DEFAULT_SUCCESS_THUMB_RES);
        invalidate();
    }

    public void setThumbMargin(float margin) {
        this.thumbMargin = margin;
        invalidate();
    }

    public void setProgressPadding(float padding) {
        this.progressPadding = padding;
        invalidate();
    }

    /**
     * 设置成功状态显示文本
     * @param text 要显示的文字
     */
    public void setSuccessText(CharSequence text) {
        this.successText = text != null ? text.toString() : "";
        invalidate(); // 触发重绘
    }

    /**
     * 设置成功状态显示文本（资源ID形式）
     * @param resId 字符串资源ID
     */
    public void setSuccessText(@StringRes int resId) {
        setSuccessText(getContext().getString(resId));
    }

    public void setOnConfirmListener(OnConfirmListener listener) {
        this.confirmListener = listener;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (resetAnimator != null) {
            resetAnimator.cancel();
        }
        releaseBitmaps();
    }

    private void releaseBitmaps() {
        if (thumbBitmap != null && !thumbBitmap.isRecycled()) {
            thumbBitmap.recycle();
            thumbBitmap = null;
        }
        if (successThumbBitmap != null && !successThumbBitmap.isRecycled()) {
            successThumbBitmap.recycle();
            successThumbBitmap = null;
        }
        currentThumbBitmap = null;
    }

    public interface OnConfirmListener {
        void onConfirm();
    }
}