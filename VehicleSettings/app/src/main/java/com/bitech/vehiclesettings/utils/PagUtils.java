package com.bitech.vehiclesettings.utils;

import android.content.Context;
import android.util.Log;

import org.libpag.PAGFile;
import org.libpag.PAGView;

public class PagUtils {
    private static final String TAG = PagUtils.class.getSimpleName();

    public static void playPagView(Context context, String assets, PAGView pagView) {
        try {
            if (pagView != null) {
                PAGFile pagFile = PAGFile.Load(context.getAssets(), assets);
                pagView.setComposition(pagFile);
                pagView.setRepeatCount(1);
                pagView.play();
            }
        } catch (Exception e) {
            Log.d(TAG, "playPagView error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
