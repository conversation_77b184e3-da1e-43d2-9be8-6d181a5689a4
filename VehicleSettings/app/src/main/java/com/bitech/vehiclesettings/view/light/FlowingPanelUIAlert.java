package com.bitech.vehiclesettings.view.light;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.bitech.vehiclesettings.R;

/**
 * 对话框
 */
public class FlowingPanelUIAlert extends Dialog {
    private static final String TAG = FlowingPanelUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public FlowingPanelUIAlert(Context context) {
        super(context);
    }

    public FlowingPanelUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected FlowingPanelUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static FlowingPanelUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(FlowingPanelUIAlert.onProgressChangedListener onProgressChangedListener) {
        FlowingPanelUIAlert.onProgressChangedListener = onProgressChangedListener;
    }


    public static class Builder {

        private final Context context;
        private boolean isCan = true;

        private FlowingPanelUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public FlowingPanelUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new FlowingPanelUIAlert(context,
                        R.style.Dialog);
            }
            layout = View.inflate(context, R.layout.dialog_alert_l_flowing_panel, null);

            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1520;
            layoutParams.height = 952;
            window.setAttributes(layoutParams);


            return dialog;
        }


    }


    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }

}
