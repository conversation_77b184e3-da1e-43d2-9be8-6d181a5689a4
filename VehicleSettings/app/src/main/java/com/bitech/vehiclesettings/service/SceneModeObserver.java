package com.bitech.vehiclesettings.service;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.provider.Settings;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.bean.atmosphere.AmbLigBean;
import com.bitech.vehiclesettings.service.atmosphere.BreathingEffectService;
import com.bitech.vehiclesettings.service.atmosphere.GradientEffectService;
import com.bitech.vehiclesettings.utils.PrefsConst;

public class SceneModeObserver extends ContentObserver {
    private static final String TAG = SceneModeObserver.class.getSimpleName();
    private final Context mContext;
    private final String mSettingKey;
    private GradientEffectService gradualChangeService;
    private BreathingEffectService breathingEffectService;
    private LightManager carServer ;

    public SceneModeObserver(Context context, String settingKey, Handler vHandler) {
        super(vHandler);
        mContext = context;
        mSettingKey = settingKey;
        carServer = (LightManager) BitechCar.getInstance().getServiceManager(mContext,BitechCar.CAR_LIGHT_MANAGER);
    }


    @Override
    public void onChange(boolean selfChange, Uri uri) {

        if ((PrefsConst.GlobalValue.PREFIX + PrefsConst.GlobalValue.L_SCENEMODE_ATMOSPHERE).equals(uri.toString())) {
            // 情景模式关联接口调用 单色
            sceneModeAtmosphere();
        }
    }

    private void sceneModeAtmosphere() {
        int value = Settings.Global.getInt(mContext.getContentResolver(), mSettingKey, -1);
        int colorIndex = value;
        AmbLigBean ambLigBean = new AmbLigBean().setAll();
        ambLigBean.setAmbLigBriAdj(0);
        ambLigBean.setAmbLigColorAdj(colorIndex);
        ambLigBean.setAmbLigFadeINorOUTStepTi(0);
        ambLigBean.setAmbLigFlngModSel(3);
        ambLigBean.setDymAmbLigLevelSig(0);
        ambLigBean.setDymAmbLigDirSig(0);
        ambLigBean.setAmbModLocSig(0);
        ambLigBean.setAmbLigModRepOrdIdeSig(0);
        ambLigBean.setAmbLigModTimCabSig(0);
        // 发送颜色
        carServer.setLightAmbLightCan(ambLigBean);
    }


}
