package com.bitech.vehiclesettings.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.SystemDataBean;
import com.bitech.vehiclesettings.bean.SystemDataBean;
import com.bitech.vehiclesettings.view.common.NoToggleSwitch;

import java.util.List;

public class SystemDataAdapter extends RecyclerView.Adapter<SystemDataAdapter.PermissionViewHolder> {

    private final Context context;
    private final List<SystemDataBean> items;
    private OnItemClickListener itemClickListener;
    private OnSwitchChangeListener switchChangeListener;

    public interface OnItemClickListener {
        void onItemClick(SystemDataBean item, int position);
    }

    public interface OnSwitchChangeListener {
        void onSwitchChanged(SystemDataBean item, int position, NoToggleSwitch noToggleSwitch);
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.itemClickListener = listener;
    }

    public void setOnSwitchChangeListener(OnSwitchChangeListener listener) {
        this.switchChangeListener = listener;
    }

    public SystemDataAdapter(Context context, List<SystemDataBean> items) {
        this.context = context;
        this.items = items;
    }

    @NonNull
    @Override
    public PermissionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_s_data, parent, false);
        return new PermissionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PermissionViewHolder holder, int position) {
        SystemDataBean item = items.get(position);

        holder.tvTitle.setText(item.getTitle());
        holder.tvContent.setText(item.getContent());
        holder.swSwitch.setChecked(item.isChecked()); // 假设你的 SystemDataBean 有 isChecked()

        holder.itemView.setOnClickListener(v -> {
            if (itemClickListener != null && holder.getAdapterPosition() != RecyclerView.NO_POSITION) {
                itemClickListener.onItemClick(item, holder.getAdapterPosition());
            }
        });

/*        holder.swSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (switchChangeListener != null && holder.getAdapterPosition() != RecyclerView.NO_POSITION) {
                switchChangeListener.onSwitchChanged(item, holder.getAdapterPosition(), isChecked);
            }
        });*/
        holder.swSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (switchChangeListener != null && holder.getAdapterPosition() != RecyclerView.NO_POSITION) {
                switchChangeListener.onSwitchChanged(item, holder.getAdapterPosition(), holder.swSwitch);
            }
        });
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public static class PermissionViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        TextView tvContent;
        @SuppressLint("UseSwitchCompatOrMaterialCode")
        NoToggleSwitch swSwitch;

        public PermissionViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvContent = itemView.findViewById(R.id.tv_content);
            swSwitch = itemView.findViewById(R.id.sw_switch);
        }
    }
}


