package com.bitech.vehiclesettings.adapter;


import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.platformlib.bean.ColorBean;
import com.bitech.vehiclesettings.R;

import java.util.List;

public class SuggestColorAdapter extends RecyclerView.Adapter {
    private List<ColorBean> data;
    private Context context;
    private static SuggestColorAdapter.onItemClickListener onItemClickListener;

    public static void clearListener() {
        if (onItemClickListener != null) {
            onItemClickListener = null;
        }
    }

    public SuggestColorAdapter(Context context, List<ColorBean> data) {
        this.context = context;
        this.data = data;
    }

    public static void setOnItemClickListener(SuggestColorAdapter.onItemClickListener onItemClickListener) {
        SuggestColorAdapter.onItemClickListener = onItemClickListener;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_color_rv_list, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        VH vh = (VH) holder;
        ColorBean colorBean = data.get(position);
        vh.viewMask.setSelected(colorBean.isSelected);
        vh.view.setBackgroundResource(colorBean.resBgId);
        vh.itemV.setOnClickListener(view -> {
            onItemClickListener.onClick(position);
        });
        //vh.view.setImageDrawable(context.getResources().getDrawable(colorBean.resBgId, context.getTheme()));
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return data.size();
    }

    class VH extends RecyclerView.ViewHolder {

        public ImageView view;
        public View viewMask;
        public View itemV;

        public VH(@NonNull View itemView) {
            super(itemView);
            view = itemView.findViewById(R.id.view);
            viewMask = itemView.findViewById(R.id.view_mask);
            itemV = itemView;
        }
    }

    public interface onItemClickListener {
        void onClick(int position);
    }
}

