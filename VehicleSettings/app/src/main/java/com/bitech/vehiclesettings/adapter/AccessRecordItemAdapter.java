package com.bitech.vehiclesettings.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.RecordItemBean;

import java.util.ArrayList;
import java.util.List;

public class AccessRecordItemAdapter extends RecyclerView.Adapter<AccessRecordItemAdapter.ViewHolder> {
    private final List<RecordItemBean> records;

    public AccessRecordItemAdapter(List<RecordItemBean> records) {
        this.records = new ArrayList<>(records); // 避免直接修改原数据
    }

    public void updateData(List<RecordItemBean> newRecords) {
        this.records.clear();
        this.records.addAll(newRecords);
        notifyDataSetChanged(); // 刷新 UI
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_access_record, parent, false);
        return new ViewHolder(view);
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Context context = holder.itemView.getContext();
        RecordItemBean record = records.get(position);
        holder.icon.setImageResource(record.getAppIcon());
        holder.title.setText(record.getAppName());
        holder.detail.setText(context.getString(R.string.str_system_permission_5) + record.getVisitCount() + context.getString(R.string.str_system_permission_6) + record.getLastAccessTime());
    }

    @Override
    public int getItemCount() {
        return records.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView icon;
        TextView title, detail;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            icon = itemView.findViewById(R.id.iv_record);
            title = itemView.findViewById(R.id.tv_record_title);
            detail = itemView.findViewById(R.id.tv_record_detail);
        }
    }
}

