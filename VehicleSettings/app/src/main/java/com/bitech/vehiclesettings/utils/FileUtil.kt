package com.bitech.vehiclesettings.utils

import android.content.Context
import java.util.Locale

/**
 * @ClassName: FileUtil
 * 
 * @Date:  2024/5/14 15:59
 * @Description: 文件操作工具类.
 **/
object FileUtil {

    // 日志标志位
    private const val TAG = "FileUtil"

    // 单位
    private const val B = "B"
    private const val KB = "KB"
    private const val MB = "MB"
    private const val GB = "GB"
    private const val TB = "TB"
    private const val PB = "PB"

    // 转换格式
    private const val FORMAT_0 = "%.0f"
    private const val FORMAT_1 = "%.1f"
    private const val FORMAT_2 = "%.2f"

    /**
     * 格式化文件大小，并以字符串形式返回。
     *
     * @param context 上下文环境
     * @param roundedBytes 已经四舍五入的文件大小（字节）
     * @param shorter 是否使用更短的形式（如：1.5 MB 而不是 1.50 MB）
     * @param locale 用于本地化的地区设置
     * @return 格式化后的文件大小字符串
     */
    fun formatFileSize(
        context: Context?,
        roundedBytes: Long,
        shorter: Boolean,
        locale: Locale
    ): String {
        LogUtil.i(TAG, "formatFileSize : roundedBytes = $roundedBytes")
        if (context == null) {
            return ""
        }
        //统一转换为GB
        var result = roundedBytes.toFloat()
        var suffix = B
       // if (result > 900) {
            suffix = KB
            result /= 1024
      //  }
      //  if (result > 900) {
            suffix = MB
            result /= 1024
       // }
       // if (result > 900) {
            suffix = GB
            result /= 1024
       // }
      /*  if (result > 900) {
            suffix = TB
            result /= 1024
        }
        if (result > 900) {
            suffix = PB
            result /= 1024
        }*/
        val value: String = when {
            result < 1 -> {
                String.format(locale, FORMAT_2, result)
            }

            result < 10 -> {
                if (shorter) {
                    String.format(locale, FORMAT_1, result)
                } else {
                    String.format(locale, FORMAT_2, result)
                }
            }

            result < 100 -> {
                if (shorter) {
                    String.format(locale, FORMAT_0, result)
                } else {
                    String.format(locale, FORMAT_2, result)
                }
            }

            else -> {
                String.format(locale, FORMAT_0, result)
            }
        }
        return "$value$suffix"
    }
}
