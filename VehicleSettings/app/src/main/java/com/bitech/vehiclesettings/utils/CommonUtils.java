package com.bitech.vehiclesettings.utils;

import android.util.Log;

public class CommonUtils {
    private static long lastClickTime = 0;
    private static final int FAST_CLICK_DELAY_TIME = 200;

    public static int BoolToInt(boolean flag) {
        if (flag) {
            return 1;
        }
        return 0;
    }

    public static boolean IntToBool(int flag) {
        if (flag == 0) {
            return false;
        }
        return true;
    }

    /**
     * 防止重复点击
     *
     * @return
     */
    public static boolean isFastDoubleClick() {
        long currentTime = System.currentTimeMillis();
        if ((currentTime - lastClickTime < FAST_CLICK_DELAY_TIME) && (currentTime > lastClickTime)) {
            return true;
        }
        lastClickTime = currentTime;
        return false;
    }

    /**
     * 防止重复点击
     *
     * @param delayTime
     * @return
     */
    public static boolean isFastDoubleClick(int delayTime) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastClickTime < FAST_CLICK_DELAY_TIME) {
            return true;
        }
        lastClickTime = currentTime;
        return false;
    }

    public static Integer ObjectToInteger(Object obj) {
        Integer val = null;
        boolean result = obj instanceof Integer;
        try {
            if (obj instanceof Integer) {
                val = (Integer) obj;
            } else {
                if (obj != null) {
                    val = Integer.parseInt(String.valueOf(obj));
                }
            }

        } catch (Exception e) {
            Log.e("CommonUtils", "get ObjectToInteger error", e);
        }
        return val;
    }

}
