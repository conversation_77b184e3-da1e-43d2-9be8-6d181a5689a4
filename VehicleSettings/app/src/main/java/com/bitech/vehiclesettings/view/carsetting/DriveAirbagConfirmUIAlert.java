package com.bitech.vehiclesettings.view.carsetting;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.SegmentItemBean;
import com.bitech.vehiclesettings.databinding.DialogAlertCDriveAirbagBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertCDriveAirbagConfirmBinding;
import com.bitech.vehiclesettings.view.common.SlideToConfirmView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class DriveAirbagConfirmUIAlert extends BaseDialog {
    private static final String TAG = DriveAirbagConfirmUIAlert.class.getSimpleName();
    private static DriveAirbagConfirmUIAlert.onProgressChangedListener onProgressChangedListener;

    public DriveAirbagConfirmUIAlert(@NonNull Context context) {
        super(context);
    }

    public DriveAirbagConfirmUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected DriveAirbagConfirmUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static DriveAirbagConfirmUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(DriveAirbagConfirmUIAlert.onProgressChangedListener onProgressChangedListener) {
        DriveAirbagConfirmUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertCDriveAirbagConfirmBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private DriveAirbagConfirmUIAlert dialog = null;
        private View layout;
        int type;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder(Context context, int type) {
            this.context = context;
            this.type = type;
        }

        public DriveAirbagConfirmUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public DriveAirbagConfirmUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new DriveAirbagConfirmUIAlert(context,
                        R.style.Dialog);
            binding = DialogAlertCDriveAirbagConfirmBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128;
            layoutParams.height = 508;
            window.setAttributes(layoutParams);

            initText();

            initSeekbar();

            return dialog;
        }

        private void initSeekbar() {
            binding.slideView.setSeekbarColor(type == 0 ? context.getColor(R.color.bg_seekbar_confirm) : context.getColor(R.color.bg_seekbar_cancel));
            binding.slideView.setSeekBarThumbSuccess(type == 0 ? R.mipmap.ic_seekbar_thumb_success : R.mipmap.ic_seekbar_thumb_success_close);
            binding.slideView.setOnConfirmListener(new SlideToConfirmView.OnConfirmListener() {

                @Override
                public void onConfirm() {
                    onProgressChangedListener.onConfirm(dialog, type);
                }
            });
        }

        private void initText() {
            binding.tvTitle.setText(context.getResources().getString(
                    R.string.str_carsetting_safe_confirm_title
            ));
            binding.tvContent.setText(context.getResources().getString(
                    type == 0 ? R.string.str_carsetting_safe_confirm_sub_title_open : R.string.str_carsetting_safe_confirm_sub_title_close
            ));
            binding.slideView.setSuccessText(context.getResources().getString(
                    type == 0 ? R.string.str_carsetting_safe_confirm_seekbar_open :
                    R.string.str_carsetting_safe_confirm_seekbar_close
            ));
        }


    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onConfirm(DriveAirbagConfirmUIAlert dialog, int type);
    }
}
