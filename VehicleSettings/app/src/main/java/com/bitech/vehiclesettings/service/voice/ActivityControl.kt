package com.bitech.vehiclesettings.service.voice

import android.content.Context
import android.net.Uri
import android.util.Log
import com.bitech.platformlib.manager.DrivingManager
import com.bitech.platformlib.utils.MsgUtil
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarDriving
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.fragment.DisplayFragment
import com.bitech.vehiclesettings.fragment.DrivingFragment
import com.bitech.vehiclesettings.fragment.LightBaseFragment
import com.bitech.vehiclesettings.fragment.NewEnergyFragment.Companion.ENDURANCE_INTERFACE
import com.bitech.vehiclesettings.fragment.QuickControlFragment
import com.bitech.vehiclesettings.fragment.SystemFragment
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.DialogNavigationUtils
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import com.bitech.vehiclesettings.view.display.WallpaperUIAlert
import com.bitech.vehiclesettings.view.driving.PersonalizedSettingUIAlert
import com.bitech.vehiclesettings.view.newenergy.CommonOptionsDialog
import com.bitech.vehiclesettings.view.quickcontrol.RearMirrorUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.SteeringWheelUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.WiperSensUIAlert
import com.bitech.vehiclesettings.view.system.GestureNavigationUIAlert
import com.bitech.vehiclesettings.view.system.InstrumentFuelUnitUIAlert
import com.bitech.vehiclesettings.view.system.PowerConsumptionUnitUIAlert
import com.bitech.vehiclesettings.view.system.PrivacyStatementUIAlert
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P


class ActivityControl {
    private val context: Context = MyApplication.getInstance()
    private var mDrivingManager: DrivingManager?= DrivingManager.getInstance()

    //单回复提示语id
    private fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    private fun sendResultCode(respondId: String, mValue: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    //特殊提示语id
    private fun sendResultCode(respondId: String, mValue: String, mUnique: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        param.unique = mUnique
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    /**
     * 打开车内灯光
     * @parameter flag true 打开 false 关闭
     * @return null
     */
    fun setInsideLight(flag: Boolean) {
        // 定义inOrOut 0为车内 1为车外
        val inOrOut = Prefs.get(PrefsConst.SELECT_TAB, 0)
        if (flag) {
            // 判断车内灯光界面是否打开
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT
                && inOrOut == 0
            ) {
                sendResultCode(VDVRRespondID.open_interior_lighting_setting_interface_2)
            } else {
                // 拉起界面
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_interior_lighting_setting_interface_1)
            }
        } else {
            // 判断车内灯光是否关闭
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT) {
                // 关闭界面
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_interior_lighting_setting_interface_1)
            } else {
                sendResultCode(VDVRRespondID.close_interior_lighting_setting_interface_2)
            }
        }
    }

    /**
     * 车外灯光设置界面
     * @parameter flag true 打开 false 关闭
     * @return null
     */
    fun setOutsideLight(flag: Boolean) {
        // 定义inOrOut 0为车内 1为车外
        val inOrOut = Prefs.get(PrefsConst.SELECT_TAB, 0);
        if (flag) {
            // 判断车外灯光是否打开
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT
                && inOrOut == 1
            ) {
//                sendResultCode(VDVRRespondID.open_exterior_lighting_setting_interface_2)
            } else {
                // 拉起界面
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_OUT,
                    CommonConst.DIALOG_OPEN
                )
//                sendResultCode(VDVRRespondID.open_exterior_lighting_setting_interface_1)
            }
        } else {
            // 判断车外灯光是否关闭
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == 1) {
                // 关闭界面
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_OUT,
                    CommonConst.DIALOG_CLOSE
                )
//                sendResultCode(VDVRRespondID.close_exterior_lighting_setting_interface_1)
            } else {
//                sendResultCode(VDVRRespondID.close_exterior_lighting_setting_interface_2)
            }
        }
    }

    /**
     * 打开充放电管理状态
     * @parameter flag true 打开 false 关闭
     * @return null
     */
    fun setChargeManagement(flag: Boolean) {
        if (flag) {
            // 判断是否为充放电管理界面
            if (MainActivity.getCurrentMainTabIndex() != MainActivity.MainTabIndex.NEW_ENERGY) {
                // 拉起界面
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.NEW_ENERGY,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_charge_discharge_set_interface_1)
            } else {
                sendResultCode(VDVRRespondID.open_charge_discharge_set_interface_2)
            }
        }
    }

    /**
     * 打开车窗控制界面
     * @parameter flag true 打开 false 关闭
     * @return null
     */
    fun setWindowControl(flag: Boolean) {
        if (flag) {
            if (!QuickControlFragment.llToolsIsInvisible) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.QUICK_CONTROL,
                    QuickControlFragment.QucikControl_CarWindow,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_window_control_interface_1)
            } else {
                sendResultCode(VDVRRespondID.open_the_window_control_interface_2)
            }
        } else {
            if (QuickControlFragment.llToolsIsInvisible) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.QUICK_CONTROL,
                    QuickControlFragment.QucikControl_CarWindow,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_the_window_control_interface_1)
            } else {
                sendResultCode(VDVRRespondID.close_the_window_control_interface_2)
            }
        }
    }

    /**
     * 打开显示模式界面
     * @parameter flag true 显示 false 关闭
     * @return null
     */
    fun switchPage(flag: Boolean){
        if (flag) {
            // 判断是否为显示模式界面
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.DISPLAY){
                sendResultCode(VDVRRespondID.open_the_display_settings_interface_2)
            }else{
                // 拉起界面
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.DISPLAY,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_display_settings_interface_1)
            }
        }else{
            // 判断是否为显示模式界面
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.DISPLAY){
                // 关闭界面
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.DISPLAY,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_the_display_settings_interface_1)
            }else{
                sendResultCode(VDVRRespondID.close_the_display_settings_interface_2)
            }
        }
    }

    /**
     * 打开壁纸显示界面
     * @parameter flag true 显示 false 关闭
     * @return null
     */
    fun switchPageWallpaper(flag: Boolean){
        if (flag) {
            // 判断是否为壁纸显示界面
            if(!WallpaperUIAlert.isShow) {
                // 显示壁纸设置界面
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.DISPLAY,
                    DisplayFragment.WallpaperType,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_wallpaper_settings_interface_1)
            }else{
                sendResultCode(VDVRRespondID.open_the_wallpaper_settings_interface_2)
            }
        }
    }

    /**
     * 打开驾驶模式设置界面
     * @param flag true:打开
     */
    fun openDriveModeSetting(flag: Boolean) {
        if (flag) {
            //判断当前的tab是否为DRIVE
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.DRIVE) {
                sendResultCode(VDVRRespondID.open_the_driving_mode_settings_interface_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.DRIVE,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_driving_mode_settings_interface_1)
            }
        }
    }

    /**
     * 打开驾驶模式个性化设置界面
     * @param flag true:打开
     */
    fun openThemePersonalizationSetting(flag: Boolean) {
        val status = mDrivingManager?.getDriveMode()
        if (flag) {
            if(status == Constant.Drive_Person_MODE){
                if (PersonalizedSettingUIAlert.isShow) {
                    sendResultCode(VDVRRespondID.open_the_driving_mode_personalization_settings_interface_2)
                } else {
                    DialogNavigationUtils.launchMainActivity(
                        context,
                        MainActivity.MainTabIndex.DRIVE,
                        DrivingFragment.DRIVE_PERSONALIZE_DIALOG,
                        CommonConst.DIALOG_OPEN
                    )
                    sendResultCode(VDVRRespondID.open_the_driving_mode_personalization_settings_interface_1)
                }
            }
        }
    }


    /**
     * 车灯(灯光)设置界面开关
     * @param flag true:打开 false:关闭
     */
    fun lightingSetting(flag: Boolean) {
        if (flag) {
            //判断当前的tab是否为LIGHT
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT) {
                sendResultCode(VDVRRespondID.open_the_headlight_settings_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_headlight_settings_1)
            }
        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_the_headlight_settings_2)
            } else (
                    sendResultCode(VDVRRespondID.close_the_headlight_settings_1)
                    )
        }
    }


    /**
     * 打开方向盘调节界面
     * @param flag true:打开
     */
    fun openSteeringWheelAdjustment(flag: Boolean) {
        if(flag){
            if(SteeringWheelUIAlert.isShow == false) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.QUICK_CONTROL,
                    QuickControlFragment.REAR_steering_Wheel_ADJUST_UI_ALERT,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_steering_wheel_adjustment_1)
            }
            else {
                sendResultCode(VDVRRespondID.open_the_steering_wheel_adjustment_2)
            }
        }
    }


    /**
     * 打开方向盘自定义界面
     * @param flag true:打开
     */
    fun openCustomSteeringWheel(flag: Boolean) {
        if (flag) {
            if (SteeringWheelUIAlert.isShow == false) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.QUICK_CONTROL,
                    QuickControlFragment.REAR_steering_Wheel_ADJUST_UI_ALERT,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_steering_wheel_customization_interface_1)
            } else {
                sendResultCode(VDVRRespondID.open_the_steering_wheel_customization_interface_2)
            }
        }
    }


    /**
     * 打开后视镜调节界面
     * @param flag true:打开
     */
    fun openMirrorAdjustment(flag: Boolean) {
        if(flag){
            if(RearMirrorUIAlert.isShow == false) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.QUICK_CONTROL,
                    QuickControlFragment.REAR_MIRROR_UI_ALERT,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_rearview_mirror_adjustment_interface_1)
            }
        }
    }


    /**
     * 打开雨刮灵敏度调节界面
     * @param flag true:打开
     */
    fun openInLightSensitivityAdjustment(flag: Boolean) {
        if (flag) {
            if (WiperSensUIAlert.isShow == false) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.QUICK_CONTROL,
                    QuickControlFragment.RAIN_SCOOP_SENSITIVITY_UI_ALERT,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_windshield_wiper_sensitivity_adjustment_interface_1)
            }
            else{
                sendResultCode(VDVRRespondID.open_the_windshield_wiper_sensitivity_adjustment_interface_2)
            }
        }
    }


    /**
     * 下电开关
     */
    fun setPowerReset(flag: Boolean) {
        val uri = Uri.parse(CarSettingConstant.Negative_Creen)
        MyApplication.getContext().contentResolver.call(
            uri,
            CarSettingConstant.OPEN_NEGATIVE_SCREEN,
            null,
            null
        )
        sendResultCode(VDVRRespondID.open_power_switch_2)
    }

    /**氛围灯设置界面
     * @param flag
     */
    fun openLightUI(flag: Boolean) {
        var inOrOut = Prefs.get(PrefsConst.SELECT_TAB, CarSettingConstant.Car_Light_Type_Inside);
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == CarSettingConstant.Car_Light_Type_Inside) {
                sendResultCode(VDVRRespondID.open_ambient_light_setting_interface_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_ambient_light_setting_interface_1)
            }

        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == CarSettingConstant.Car_Light_Type_Inside) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_ambient_light_setting_interface_1)
            } else {
                sendResultCode(VDVRRespondID.close_ambient_light_setting_interface_2)
            }
        }
    }

    /**氛围灯颜色设置界面
     * @param flag
     */
    fun openInLightColorUI(flag: Boolean) {
        var inOrOut = Prefs.get(PrefsConst.SELECT_TAB, CarSettingConstant.Car_Light_Type_Inside);
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == CarSettingConstant.Car_Light_Type_Inside) {
                sendResultCode(VDVRRespondID.open_ambient_light_color_setting_interface_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_ambient_light_color_setting_interface_1)
            }

        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == 0) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_ambient_light_setting_interface_1)
            } else {
                sendResultCode(VDVRRespondID.close_ambient_light_setting_interface_2)
            }
        }
    }

    /**打开氛围灯音乐律动设置界面
     * @param flag
     */
    fun openInLightMusicUI(flag: Boolean) {
        var inOrOut = Prefs.get(PrefsConst.SELECT_TAB, CarSettingConstant.Car_Light_Type_Inside);
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == CarSettingConstant.Car_Light_Type_Inside) {
                sendResultCode(VDVRRespondID.open_ambient_light_music_rhythm_setting_interface_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_ambient_light_music_rhythm_setting_interface_1)
            }

        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == CarSettingConstant.Car_Light_Type_Inside) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_ambient_light_setting_interface_1)
            } else {
                sendResultCode(VDVRRespondID.close_ambient_light_setting_interface_2)
            }
        }
    }

    /**打开氛围灯亮度设置界面
     * @param flag
     */
    fun openInLightBrightUI(flag: Boolean) {
        var inOrOut = Prefs.get(PrefsConst.SELECT_TAB, CarSettingConstant.Car_Light_Type_Inside);
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == CarSettingConstant.Car_Light_Type_Inside) {
                sendResultCode(VDVRRespondID.open_ambient_light_brightness_setting_interface_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_ambient_light_brightness_setting_interface_1)
            }
        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.LIGHT && inOrOut == CarSettingConstant.Car_Light_Type_Inside) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_IN,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_ambient_light_setting_interface_1)
            } else {
                sendResultCode(VDVRRespondID.close_ambient_light_setting_interface_2)
            }
        }
    }

    /**
     * 切换仪表电耗单位
     * @param flag true:打开
     */
    fun switchPowerConsumptionUnit(flag: Boolean){
        if(flag){
            if(PowerConsumptionUnitUIAlert.isShow){
                sendResultCode(VDVRRespondID.set_electricity_consumption_unit_2)
            }else{
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.SYSTEM,
                    SystemFragment.POWER_CONSUMPTION_UNIT,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.set_electricity_consumption_unit_2)
            }
        }
    }

    /**
     * 切换油量显示单位
     * @param flag true:打开
     */
    fun switchFuelDisplayUnit(flag: Boolean){
        if(flag){
            if(InstrumentFuelUnitUIAlert.isShow){
                sendResultCode(VDVRRespondID.set_capacity_display_unit_11)
            }else{
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.SYSTEM,
                    SystemFragment.GAUGE_FUEL_CONSUMPTION,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.set_capacity_display_unit_11)
            }
        }
    }

    /**
     * 打开or关闭手势导航界面
     * @param flag true:打开 false:关闭
     */
    fun setGestureNavigationInterface(flag: Boolean) {
        if (flag) {
            if (GestureNavigationUIAlert.isShow) {
                sendResultCode(VDVRRespondID.Gesture_navigation_interface_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.SYSTEM,
                    SystemFragment.GESTURE_NAVIGATION,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.Gesture_navigation_interface_1)
            }
        } else {
            if (GestureNavigationUIAlert.isShow) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.SYSTEM,
                    SystemFragment.GESTURE_NAVIGATION,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.Gesture_navigation_interface_4)
            } else {
                sendResultCode(VDVRRespondID.Gesture_navigation_interface_3)
            }
        }
    }

    /**
     * 打开or关闭续航工况显示模式
     * @param flag true:打开 false:关闭
     */
    fun setEnduranceDisplay(flag: Boolean) {
        if (flag) {
            if (CommonOptionsDialog.isShowingDialog == CommonOptionsDialog.TYPE_RANGE_CONDITION_DISPLAY) {
                sendResultCode(VDVRRespondID.Range_condition_display_mode_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.NEW_ENERGY,
                    ENDURANCE_INTERFACE,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.Range_condition_display_mode_1)
            }
        } else {
            if (CommonOptionsDialog.isShowingDialog == CommonOptionsDialog.TYPE_RANGE_CONDITION_DISPLAY) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.NEW_ENERGY,
                    ENDURANCE_INTERFACE,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.Range_condition_display_mode_4)
            } else {
                sendResultCode(VDVRRespondID.Range_condition_display_mode_3)
            }
        }
    }

    /**
     * 打开大灯设置界面
     * @param flag true:打开
     */
    fun openSetInsideLight(flag: Boolean) {
        val inOrOut = Prefs.get(PrefsConst.SELECT_TAB, 0);
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() != MainActivity.MainTabIndex.LIGHT
                || inOrOut == 0
            ) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_OUT,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_headlight_settings_interface_1)
            } else {
                sendResultCode(VDVRRespondID.open_the_headlight_settings_interface_2)
            }
        }
    }

    /**
     * 打开大灯高度设置界面
     * @param flag true:打开
     */
    fun openLightHeightInterface(flag: Boolean) {
        val inOrOut = Prefs.get(PrefsConst.SELECT_TAB, 0);
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() != MainActivity.MainTabIndex.LIGHT
                || inOrOut == 0
            ) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.LIGHT,
                    LightBaseFragment.LIGHT_SETECT_OUT,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_headlight_height_settings_interface_1)
            } else {
                sendResultCode(VDVRRespondID.open_the_headlight_height_settings_interface_2)
            }
        }
    }

    /**
     * 打开or关闭系统设置界面
     * @param flag true:打开 false:关闭
     */
    fun setSystemSettings(flag: Boolean){
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.SYSTEM) {
                sendResultCode(VDVRRespondID.open_the_system_setting_2)
            }else{
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.SYSTEM,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_system_setting_1)
            }
        }else{
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.SYSTEM) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.SYSTEM,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_the_system_setting_1)
            }else{

                sendResultCode(VDVRRespondID.close_the_system_setting_2)
            }
        }
    }

    /**
     * 打开恢复出厂设置界面
     */
    fun setSystemReset() {
        if(mDrivingManager?.getGearPosition() == CarDriving.VCU_PRNDGearAct.P){
            DialogNavigationUtils.launchMainActivity(
                context,
                MainActivity.MainTabIndex.SYSTEM,
                SystemFragment.RESET_EXIT_SETTING,
                CommonConst.DIALOG_OPEN
            )
            sendResultCode(VDVRRespondID.restore_factory_set_1)
        }
    }

    /**
     * 打开修改车机设备名称界面
     */
    fun setSystemInfo(flag: Boolean) {
        if(flag){
            DialogNavigationUtils.launchMainActivity(
                context,
                MainActivity.MainTabIndex.SYSTEM,
                SystemFragment.DEVICE_INFORMATION,
                CommonConst.DIALOG_OPEN
            )
            sendResultCode(VDVRRespondID.Modif_name_of_the_vehicle_equipment_1)
        }
    }

    /**
     * 打开or关闭便捷控制界面
     * @param flag true:打开  false:关闭
     */
    //打开/关闭便捷控制界面
    fun setConvenientControl(flag: Boolean) {
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() != MainActivity.MainTabIndex.QUICK_CONTROL) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.QUICK_CONTROL,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_convenient_control_interface_1)
            } else {
                sendResultCode(VDVRRespondID.open_the_convenient_control_interface_2)
            }

        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.QUICK_CONTROL) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.QUICK_CONTROL,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_the_convenient_control_interface_2)
            } else {
                sendResultCode(VDVRRespondID.close_the_convenient_control_interface_1)
            }
        }
    }

    /**
     * 打开or关闭驾驶设置界面
     * @param flag true:打开 false:关闭
     */
    fun setDrivingSetting(flag: Boolean) {
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() != MainActivity.MainTabIndex.DRIVE) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.DRIVE,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_driving_settings_interface_1)
            } else {
                sendResultCode(VDVRRespondID.open_the_driving_settings_interface_2)
            }
        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.DRIVE) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.DRIVE,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_the_driving_settings_interface_2)
            } else {
                sendResultCode(VDVRRespondID.close_the_driving_settings_interface_1)
            }
        }
    }

    /**
     * 打开/关闭胎压查询界面
     * @param flag true:打开 false:关闭
     */
    fun setTirePressureQuery(flag: Boolean) {
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() != MainActivity.MainTabIndex.CONDITION) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.Open_tire_pressure_query_interface_1)
            } else {
                sendResultCode(VDVRRespondID.Open_tire_pressure_query_interface_2)
            }
        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.CONDITION) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_tire_pressure_query_interface_2)
            } else {
                sendResultCode(VDVRRespondID.close_tire_pressure_query_interface_1)
            }
        }
    }

    /**声音设置界面
     * @param flag
     * @param position
     */
    fun setVoiceSettingUI(flag: Boolean, position: String) {
        if (flag) {
            if (MainActivity.getCurrentMainTabIndex() != MainActivity.MainTabIndex.VOICE) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.VOICE,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_sound_settings_interface_1)
            } else {
                sendResultCode(VDVRRespondID.open_the_sound_settings_interface_2)
            }
        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.VOICE) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.VOICE,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_the_sound_settings_interface_1)
            } else {
                sendResultCode(VDVRRespondID.close_the_sound_settings_interface_2)
            }
        }
    }

    /**打开隐私设置界面
     * @param flag
     */
    fun setPrivacySettingsUI(flag: Boolean) {
        if (true) {
            if (PrivacyStatementUIAlert.isShow) {
                sendResultCode(VDVRRespondID.open_the_privacy_settings_interface_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.SYSTEM,
                    SystemFragment.PRIVACY_SETTING,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_the_privacy_settings_interface_1)
            }
        }
    }

    /**驻车保电
     * @param flag
     */
    fun parkingPower(flag: Boolean) {
        val uri = Uri.parse(CarSettingConstant.NEGATIVE_CREEN)
        val resultBundle =
            MyApplication.getContext().contentResolver.call(
                uri,
                CarSettingConstant.NEGATIVE_SCREEN_STATUS,
                null,
                null
            )
        val state = resultBundle?.getBoolean(CarSettingConstant.NEGATIVE_SCREEN_STATUS_OPEN, false)
        if (flag) {
            if (state == true) {
                MyApplication.getContext().contentResolver.call(
                    uri,
                    CarSettingConstant.OPEN_NEGATIVE_SCREEN,
                    null,
                    null
                )
                sendResultCode(VDVRRespondID.open_parking_and_power_protection_2)
            } else {
                MyApplication.getContext().contentResolver.call(
                    uri,
                    CarSettingConstant.OPEN_NEGATIVE_SCREEN,
                    null,
                    null
                )
                sendResultCode(VDVRRespondID.open_parking_and_power_protection_1)
            }
        } else {
            if (state == true) {
                MyApplication.getContext().contentResolver.call(
                    uri,
                    CarSettingConstant.OPEN_NEGATIVE_SCREEN,
                    null,
                    null
                )
                sendResultCode(VDVRRespondID.close_parking_and_power_protection_2)
            } else {
                MyApplication.getContext().contentResolver.call(
                    uri,
                    CarSettingConstant.OPEN_NEGATIVE_SCREEN,
                    null,
                    null
                )
                sendResultCode(VDVRRespondID.close_parking_and_power_protection_1)
            }
        }
    }

    /**
     * 屏保
     * @param flag
     */
    fun setScreenSaver(flag: Boolean) {
        if (flag) {
            MsgUtil.getInstance().setSignalVal(
                CarSettingConstant.SCREEN_SAVER_TOPIC,
                CarSettingConstant.OPEN_SCREEN_SAVER
            )
            try {
                Thread.sleep(CarSettingConstant.SCREEN_SAVER_TIME)
            } catch (e: InterruptedException) {
                return
            }
            MsgUtil.getInstance().setSignalVal(
                CarSettingConstant.SCREEN_SAVER_TOPIC,
                CarSettingConstant.CLOSE_SCREEN_SAVER
            )
        } else {
            return
        }
    }

    object Constant {
        const val Drive_Person_MODE: Int = 0x6
    }
}