package com.bitech.vehiclesettings.presenter.display;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.os.Parcelable;
import android.os.RemoteException;
import android.util.Log;

import com.bitech.vehicle3D.VehicleServiceManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.carapi.constants.CarWallpaper;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.wallpaper3D.aidl.IPropertyCallback;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.client.bind.VDThreadType;
import com.chery.ivi.vdb.client.listener.VDGetListener;
import com.chery.ivi.vdb.client.listener.VDNotifyListener;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.id.wallpaper.VDEventWallpaper;
import com.chery.ivi.vdb.event.id.wallpaper.VDWallpaperInfo;

import java.util.ArrayList;

public class WallpaperPresenter {
    public static final int WALLPAPER_STATE_3D = 1;
    public static final int WALLPAPER_STATE_2D = 0;
    public static final int MAX_STATIC_SELECTION = 20;
    public static final String TAG = WallpaperPresenter.class.getName();

    public static void setWallpaperState(int state) {
        Log.d(TAG, "设置壁纸模式: " + state);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.WALLPAPER_STATE, state);
        Prefs.put(PrefsConst.GlobalValue.WALLPAPER_STATE, state);
    }

    public static int getWallpaperState() {
        int globalValue = Prefs.getGlobalValue(PrefsConst.GlobalValue.WALLPAPER_STATE, WallpaperPresenter.WALLPAPER_STATE_2D);
        int wallpaperState = Prefs.get(PrefsConst.GlobalValue.WALLPAPER_STATE, WallpaperPresenter.WALLPAPER_STATE_2D);
        Log.d(TAG, "获取壁纸模式: " + wallpaperState);
        return wallpaperState;
    }

    public static void switch3D() {
        switchWallpaperType(true);
    }

    public static void switch3DWithoutMemory() {
        switchWallpaperTypeWithoutMemory(true);
    }

    public static void switch2D() {
        switchWallpaperType(false);
    }

    /**
     * 3D->2D:type=2 ,selected=false
     * 2D->3D:type=2 ,selected=true
     *
     * @param selected
     */
    private static void switchWallpaperType(boolean selected) {
        VDWallpaperInfo vdWallpaperInfo = new VDWallpaperInfo(-1, CarWallpaper.WallpaperStyle.CAR_MODEL_3D, -1, null, -1, -1, -1,
                selected, -1, -1, null, false, false);
        addWallpaper(vdWallpaperInfo);
        setWallpaperState(CommonUtils.BoolToInt(selected));
    }
    private static void switchWallpaperTypeWithoutMemory(boolean selected) {
        VDWallpaperInfo vdWallpaperInfo = new VDWallpaperInfo(-1, CarWallpaper.WallpaperStyle.CAR_MODEL_3D, -1, null, -1, -1, -1,
                selected, -1, -1, null, false, false);
        addWallpaper(vdWallpaperInfo);
    }

    public void addWallpaperSubscribe(VDNotifyListener mVDNotifyListener) {
        VDBus.getDefault().addSubscribe(VDEventWallpaper.WALLPAPER_GET_CURRENT_LIST, VDThreadType.CHILD_THREAD);
        VDBus.getDefault().registerVDNotifyListener(mVDNotifyListener);//客户端订阅列表
        VDBus.getDefault().subscribeCommit();//订阅提交到服务端
    }

    public static ArrayList<VDWallpaperInfo> getPRSETWallpapers() {

        VDEvent vdEvent = VDBus.getDefault().getOnce(VDEventWallpaper.WALLPAPER_GET_CURRENT_LIST);
        if (vdEvent == null) {
            Log.d(TAG, "VDEvent null");
            EToast.showToast(MyApplication.getContext(), "未获取到壁纸列表", 0, false);
            return new ArrayList<>();
        }
        ArrayList<VDWallpaperInfo> vdWallpaperInfos = new ArrayList<>();
        ArrayList<VDWallpaperInfo> parcelableArrayList = vdEvent.getPayload().getParcelableArrayList(VDEventWallpaper.KEY_WALLPAPER_LIST);
        assert parcelableArrayList != null;
        for (VDWallpaperInfo wallpaperInfo : parcelableArrayList) {
            if (wallpaperInfo == null) continue;
            if (wallpaperInfo.type == CarWallpaper.Type.PRESET) {
                Log.d(TAG, "预设壁纸: " + wallpaperInfo);
                vdWallpaperInfos.add(wallpaperInfo);
            }
        }

        return vdWallpaperInfos;
    }

    public static int getPRSETWallpapersSelectedSize() {
        VDEvent vdEvent = VDBus.getDefault().getOnce(VDEventWallpaper.WALLPAPER_GET_CURRENT_LIST);
        if (vdEvent == null) {
            Log.d(TAG, "VDEvent null");
            EToast.showToast(MyApplication.getContext(), "未获取到壁纸列表", 0, false);
            return 0;
        }
        ArrayList<VDWallpaperInfo> vdWallpaperInfos = new ArrayList<>();
        ArrayList<VDWallpaperInfo> parcelableArrayList = vdEvent.getPayload().getParcelableArrayList(VDEventWallpaper.KEY_WALLPAPER_LIST);
        assert parcelableArrayList != null;
        for (VDWallpaperInfo wallpaperInfo : parcelableArrayList) {
            if (wallpaperInfo == null) continue;
            if (wallpaperInfo.type == CarWallpaper.Type.PRESET && wallpaperInfo.selected) {
                Log.d(TAG, "预设壁纸: " + wallpaperInfo);
                vdWallpaperInfos.add(wallpaperInfo);
            }
        }

        return vdWallpaperInfos.size();
    }

    public static ArrayList<VDWallpaperInfo> getGalleryWallpapers() {

        VDEvent vdEvent = VDBus.getDefault().getOnce(VDEventWallpaper.WALLPAPER_GET_CURRENT_LIST);
        if (vdEvent == null) {
            Log.d(TAG, "VDEvent null");
            EToast.showToast(MyApplication.getContext(), "未获取到壁纸列表", 0, false);
            return new ArrayList<>();
        }
        ArrayList<VDWallpaperInfo> vdWallpaperInfos = new ArrayList<>();
        ArrayList<VDWallpaperInfo> parcelableArrayList = vdEvent.getPayload().getParcelableArrayList(VDEventWallpaper.KEY_WALLPAPER_LIST);
        for (VDWallpaperInfo wallpaperInfo : parcelableArrayList) {
            if (wallpaperInfo == null) continue;
            if (wallpaperInfo.type != CarWallpaper.Type.PRESET) {
                Log.d(TAG, "非预设壁纸: " + wallpaperInfo);
                vdWallpaperInfos.add(wallpaperInfo);
            }
        }

        return vdWallpaperInfos;
    }
    public static int getGalleryWallpapersSelectedSize() {

        VDEvent vdEvent = VDBus.getDefault().getOnce(VDEventWallpaper.WALLPAPER_GET_CURRENT_LIST);
        if (vdEvent == null) {
            Log.d(TAG, "VDEvent null");
            EToast.showToast(MyApplication.getContext(), "未获取到壁纸列表", 0, false);
            return 0;
        }
        ArrayList<VDWallpaperInfo> vdWallpaperInfos = new ArrayList<>();
        ArrayList<VDWallpaperInfo> parcelableArrayList = vdEvent.getPayload().getParcelableArrayList(VDEventWallpaper.KEY_WALLPAPER_LIST);
        for (VDWallpaperInfo wallpaperInfo : parcelableArrayList) {
            if (wallpaperInfo == null) continue;
            if (wallpaperInfo.type != CarWallpaper.Type.PRESET) {
                Log.d(TAG, "非预设壁纸: " + wallpaperInfo);
                vdWallpaperInfos.add(wallpaperInfo);
            }
        }

        return vdWallpaperInfos.size();
    }

    public static void setCurrentStaticWallpapers(int[] wallpapers) {

    }

    public static void hidePreWallpaper(VDWallpaperInfo wallpaperInfo) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(VDEventWallpaper.KEY_CUR_WALLPAPER_OBJ, wallpaperInfo);
        VDEvent vdEvent = new VDEvent(2140942, bundle);
        VDBus.getDefault().set(vdEvent);
    }

    public static void deleteWallpaper(VDWallpaperInfo wallpaperInfo) {
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(VDEventWallpaper.KEY_WALLPAPER_LIST, new ArrayList<>() {{
            add(wallpaperInfo);
        }});
        VDEvent vdEvent = new VDEvent(VDEventWallpaper.WALLPAPER_DELETE_OBJECTS, bundle);
        VDBus.getDefault().set(vdEvent);
    }


    public static void addWallpapers(ArrayList<VDWallpaperInfo> vdWallpaperInfos) {
        Bundle bundle1 = new Bundle();
        bundle1.putParcelableArrayList(VDEventWallpaper.KEY_WALLPAPER_LIST, vdWallpaperInfos);
        VDEvent vdEvent = new VDEvent(VDEventWallpaper.WALLPAPER_ADD_OBJECTS, bundle1);
        VDBus.getDefault().set(vdEvent);
    }

    // 设置单个壁纸
    public static void addWallpaper(VDWallpaperInfo vdWallpaperInfo) {
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(VDEventWallpaper.KEY_WALLPAPER_LIST, new ArrayList<VDWallpaperInfo>() {{
            add(vdWallpaperInfo);
        }});
        VDEvent vdEvent = new VDEvent(VDEventWallpaper.WALLPAPER_ADD_OBJECTS, bundle);
//        bundle.putParcelable(VDEventWallpaper.KEY_WALLPAPER_LIST, vdWallpaperInfo);
//        VDEvent vdEvent = new VDEvent(VDEventWallpaper.WALLPAPER_ADD_OBJECTS, bundle);
        VDBus.getDefault().set(vdEvent);
    }

    public static void setWallpaper(VDWallpaperInfo vdWallpaperInfo) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(VDEventWallpaper.KEY_CUR_WALLPAPER_OBJ, vdWallpaperInfo);
        VDEvent vdEvent = new VDEvent(VDEventWallpaper.WALLPAPER_SET_OBJECT, bundle);
        VDBus.getDefault().set(vdEvent);
    }

    public static void setCurrentSceneWallpaper(int type) {
        setCurrentSceneWallpaper(type, true);
    }

    public static void setCurrentSceneWallpaper(int type, boolean memory) {
        int preSet = Prefs.getGlobalValue(PrefsConst.GlobalValue.WALLPAPER_STATE_3D, CarWallpaper.Type3D.CAR_3D);// 上一次设置的3D壁纸类型
        Log.d(TAG, "上一次壁纸为：" + preSet);
        if (memory) {
            Prefs.setGlobalValue(PrefsConst.GlobalValue.WALLPAPER_STATE_3D_PRE, type);
        }
        Log.d(TAG, "保存壁纸为：" + type);
        if (memory) {
            Prefs.setGlobalValue(PrefsConst.GlobalValue.WALLPAPER_STATE_3D, type);
        }

        // 封装数据
        Bundle data = new Bundle();
        data.putInt("WallpaperState", type);
        Log.d(TAG, "切换壁纸：" + "--" + type);
        VehicleServiceManager.getClientManager().to3DWPSetProperty(801, data, new IPropertyCallback.Stub() {
            @Override
            public void onSuccess(Bundle result) throws RemoteException {
                Log.d(TAG, "切换壁纸：" + "success");
            }

            @Override
            public void onError(Bundle result) throws RemoteException {
                Log.d(TAG, "切换壁纸：" + "error");
            }
        });
    }

    public static void set3DWallpaper() {
        // 封装数据
        Bundle data = new Bundle();
        data.putInt("WallpaperState", CarWallpaper.Type3D.CAR_3D);
        Log.d(TAG, "切换壁纸：" + "--" + CarWallpaper.Type3D.CAR_3D);
        VehicleServiceManager.getClientManager().to3DWPSetProperty(801, data, new IPropertyCallback.Stub() {
            @Override
            public void onSuccess(Bundle result) throws RemoteException {
                Log.d(TAG, "切换壁纸：" + "success");
            }

            @Override
            public void onError(Bundle result) throws RemoteException {
                Log.d(TAG, "切换壁纸：" + "error");
            }
        });
    }

    // 场景化壁纸
    public static int getCurrentSceneWallpaper() {
        int currentSceneWallpaper = Prefs.getGlobalValue(PrefsConst.GlobalValue.WALLPAPER_STATE_3D, CarWallpaper.Type3D.CAR_3D);
        Log.d(TAG, "当前3D壁纸: " + currentSceneWallpaper);
        return currentSceneWallpaper;
    }

    public static void setWallpaper(Context context, Uri wallpaper) {
        VDEvent vdEvent = VDBus.getDefault().getOnce(VDEventWallpaper.WALLPAPER_GET_CURRENT_LIST);
        ArrayList<Parcelable> parcelableArrayList = vdEvent.getPayload().getParcelableArrayList(VDEventWallpaper.KEY_WALLPAPER_LIST);
        for (Parcelable parcelable : parcelableArrayList) {
        }

        VDBus.getDefault().get(VDEventWallpaper.WALLPAPER_GET_CURRENT_LIST, VDThreadType.MAIN_THREAD, new VDGetListener() {
            public void onVDGet(VDEvent event, int threadType) {

            }
        });

        VDEvent event = new VDEvent(VDEventWallpaper.WALLPAPER_SET_OBJECT);
        Bundle bundle = new Bundle();

        event.setPayload(bundle);
        VDBus.getDefault().set(event);
    }

}
