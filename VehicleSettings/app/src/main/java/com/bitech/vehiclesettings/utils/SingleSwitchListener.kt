package com.bitech.vehiclesettings.utils

import android.os.SystemClock
import android.util.Log
import android.widget.CompoundButton

/**
 * 开关防抖
 */
abstract class SingleSwitchListener : CompoundButton.OnCheckedChangeListener {
    private var lastClickTime = 0L
    private val MIN_INTERVAL = 500

    final override fun onCheckedChanged(buttonView: CompoundButton, isChecked: Boolean) {
        val interval = SystemClock.uptimeMillis() - lastClickTime
        if (interval < MIN_INTERVAL) {
            Log.i("SingleSwitchListener", "onCheckedChanged too fast: $interval")
            buttonView.isChecked = !isChecked
            return
        }
        lastClickTime = SystemClock.uptimeMillis()
        onSingleCheckedChanged(buttonView, isChecked)
    }

    abstract fun onSingleCheckedChanged(buttonView: CompoundButton, isChecked: Boolean)
}