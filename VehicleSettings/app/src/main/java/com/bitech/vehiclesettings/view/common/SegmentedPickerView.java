package com.bitech.vehiclesettings.view.common;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.SegmentItemBean;
import com.bitech.vehiclesettings.utils.ElasticAnimationUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 自定义分段选择器，可自定义宽高、选项内容、选中动画、背景等
 */
public class SegmentedPickerView extends FrameLayout {

    // 判断是否是初始化
    private boolean isInit = true;
    // 选项容器：水平排列所有选项
    private LinearLayout containerLL;
    // 指示器View：选中背景框（在最底层）
    private View indicatorView;
    // 添加一个变量来跟踪当前选中的View
    private View currentlySelectedView;
    // 当前选中的index
    public int currentIndex = 0;
    // 选项列表
    private final List<SegmentItemBean> items = new ArrayList<>();
    // 保存选项对应的TextView，方便切换颜色
    private final List<TextView> itemTextViews = new ArrayList<>();
    // 保存选项对应的ImageView，方便切换图片
    private final List<ImageView> itemImageViews = new ArrayList<>();
    // 外部容器
    FrameLayout outerContainer;

    // 自定义宽高
    private int customWidth = LayoutParams.MATCH_PARENT;
    private int customHeight = LayoutParams.WRAP_CONTENT;

    // 自定义边距
//    private int customMargin = 3;
    private int indicatorMarginPx = 0;
    private int fontSize;

    // 自定义图片大小
    private int imageWidthPx = 48;  // 默认 0 表示使用 drawable 内在尺寸
    private int imageHeightPx = 48;

    private boolean hasImage = false;
    private boolean enableFlag = true;

    // 是否允许切换
    private boolean changeable = true;
    private boolean selectedListenFlag = true;

    public boolean isChangeable() {
        return changeable;
    }

    public void setChangeable(boolean changeable) {
        this.changeable = changeable;
    }

    public void setSelectedListen(boolean b) {
        this.selectedListenFlag = b;
    }

    // 回调接口
    public interface OnItemSelectedListener {
        void onItemSelected(int index, String text);

        default void onItemClicked(int index, String text){}
    }

    private OnItemSelectedListener onItemSelectedListener;

    public void setOnItemSelectedListener(OnItemSelectedListener listener) {
        this.onItemSelectedListener = listener;
    }

    // 构造函数
    public SegmentedPickerView(Context context) {
        this(context, null);
    }

    public SegmentedPickerView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SegmentedPickerView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        parseAttrs(context, attrs);
        init(context);
    }

    // 获取选中的index
    public int getSelectedIndex() {
        return currentIndex;
    }

    /**
     * 解析自定义属性
     */
    private void parseAttrs(Context context, AttributeSet attrs) {
        if (attrs == null) return;
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.SegmentedPickerView);
        customWidth = a.getDimensionPixelSize(
                R.styleable.SegmentedPickerView_pickerWidth,
                LayoutParams.MATCH_PARENT
        );
        customHeight = a.getDimensionPixelSize(
                R.styleable.SegmentedPickerView_pickerHeight,
                LayoutParams.WRAP_CONTENT
        );
        imageWidthPx = a.getDimensionPixelSize(
                R.styleable.SegmentedPickerView_imageWidth,
                LayoutParams.WRAP_CONTENT
        );
        imageHeightPx = a.getDimensionPixelSize(
                R.styleable.SegmentedPickerView_imageHeight,
                LayoutParams.WRAP_CONTENT
        );
        if (a.hasValue(R.styleable.SegmentedPickerView_marginSize)) {
            indicatorMarginPx = a.getDimensionPixelSize(
                    R.styleable.SegmentedPickerView_marginSize,
                    LayoutParams.WRAP_CONTENT
            );
        }
        float fontScale = context.getResources().getConfiguration().fontScale;
        fontSize = (int) (a.getDimensionPixelSize(R.styleable.SegmentedPickerView_pickerFontSize, 36) * fontScale);
        a.recycle();
        setLayoutParams(new LayoutParams(customWidth, customHeight));
    }

    private int dpToPx(Context context, int dp) {
        float density = context.getResources().getDisplayMetrics().density;
        return (int) (dp * density + 0.5f);
    }

    /**
     * 初始化子View：先添加指示器(在底层)，再添加选项容器(在上层)
     */
    private void init(Context context) {
        // 整体背景
        setBackgroundResource(R.drawable.shape_bg_spv);

        // ============== 1. 指示器 ==============
        indicatorView = new View(context);
        indicatorView.setBackgroundResource(R.drawable.shape_bg_open_n);

        // 初始设置宽高为0，后面布局完成后会更新
        LayoutParams indicatorParams = new LayoutParams(0, 0);
        // 先把指示器加进来，保证它在最底层
        addView(indicatorView, indicatorParams);

        // 添加 margin，让指示器不要贴边
        indicatorMarginPx = indicatorMarginPx / 2; // 可以改成你想要的值，比如 8
        indicatorParams.setMargins(indicatorMarginPx, indicatorMarginPx, indicatorMarginPx, indicatorMarginPx);

        // ============== 2. 选项容器 ==============
        containerLL = new LinearLayout(context);
        containerLL.setOrientation(LinearLayout.HORIZONTAL);
        containerLL.setGravity(Gravity.CENTER_VERTICAL);
        // 容器本身匹配父容器宽高，让内部选项撑满
        LayoutParams containerParams = new LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT
        );
        // 后添加容器，确保它在指示器之上
        addView(containerLL, containerParams);

        // 布局完成后，默认选中第0项，并让指示器更新到正确位置
        getViewTreeObserver().addOnGlobalLayoutListener(
                new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        if (!items.isEmpty() && containerLL.getChildCount() == items.size()) {
                            moveIndicatorTo(currentIndex, false);
                            getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        }
                    }
                }
        );
    }

    /**
     * 设置选项列表
     */
    public void setItems(String... newItems) {
        ArrayList<SegmentItemBean> arrayList = new ArrayList<>();
        for (String item : newItems) {
            arrayList.add(new SegmentItemBean(item));
        }
        hasImage = false;
        setItems(arrayList);
    }

    public void setItems(int... newItemIds) {
        ArrayList<SegmentItemBean> arrayList = new ArrayList<>();
        for (int itemId : newItemIds) {
            arrayList.add(new SegmentItemBean(getResources().getString(itemId)));
        }
        hasImage = false;
        setItems(arrayList);
    }

    public void setItemsRepeatClick(int... newItemIds) {
        ArrayList<SegmentItemBean> arrayList = new ArrayList<>();
        for (int itemId : newItemIds) {
            arrayList.add(new SegmentItemBean(getResources().getString(itemId)));
        }
        hasImage = false;
        setItemsRepeatClick(arrayList);
    }

    public void setItems(SegmentItemBean... segmentItems) {
        hasImage = true;
        setItems(new ArrayList<>(Arrays.asList(segmentItems)));
    }

    // 修改setItems方法
    @SuppressLint("ClickableViewAccessibility")
    public void setItems(List<SegmentItemBean> newItems) {
        int previousIndex = currentIndex;
        items.clear();
        items.addAll(newItems);

        containerLL.removeAllViews();
        itemTextViews.clear();
        if (hasImage) itemImageViews.clear();

        for (int i = 0; i < items.size(); i++) {
            final int index = i;
            final String text = items.get(i).getText();
            int imageResId = items.get(i).getImageResId();
            // 第一层容器：均分宽度的FrameLayout
            outerContainer = new FrameLayout(getContext());
            LinearLayout.LayoutParams outerLp = new LinearLayout.LayoutParams(
                    0,
                    LayoutParams.MATCH_PARENT,
                    1f
            );
            outerContainer.setLayoutParams(outerLp);

            // 第二层容器：包裹内容的LinearLayout（水平排列图片和文字）
            LinearLayout innerContainer = new LinearLayout(getContext());
            innerContainer.setOrientation(LinearLayout.HORIZONTAL);
            innerContainer.setGravity(Gravity.CENTER);
            FrameLayout.LayoutParams innerLp = new FrameLayout.LayoutParams(
                    LayoutParams.WRAP_CONTENT,
                    LayoutParams.WRAP_CONTENT
            );
            innerLp.gravity = Gravity.CENTER;
            innerContainer.setLayoutParams(innerLp);

            // 图片View
            if (hasImage) {
                ImageView imageView = new ImageView(getContext());
                imageView.setImageResource(imageResId);
                if (items.get(i).isDisImage()) {
                    LinearLayout.LayoutParams ivLp = new LinearLayout.LayoutParams(
                            imageWidthPx > 0 ? imageWidthPx : LayoutParams.WRAP_CONTENT,
                            imageHeightPx > 0 ? imageHeightPx : LayoutParams.WRAP_CONTENT
                    );
                    ivLp.setMargins(0, 0, dpToPx(getContext(), 4), 0);

                    innerContainer.addView(imageView, ivLp);
                }
                itemImageViews.add(imageView);
                updateImageViews();

            }

            // 文字View
            TextView tv = new TextView(getContext());
            tv.setText(text);
            tv.setGravity(Gravity.CENTER);
            tv.setIncludeFontPadding(false);
            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, fontSize);
            tv.setTextColor(getResources().getColor(R.color.black));
            innerContainer.addView(tv);

            // 设置触摸监听器
            outerContainer.setOnTouchListener((v, event) -> {
                Log.d("TAG", "outerContainer.setOnTouchListener setItems: " + event);
                if (!enableFlag || !isEnabled()) {
                    v.setPressed(false); // 强制取消按压状态
                    return false;
                }
                if (index == currentIndex) {
                    // 当前选中项：传递事件给indicatorView
                    indicatorView.dispatchTouchEvent(event);
                }
                return false; // 继续传递事件
            });

            // 使用applyLinkedElasticEffect方法绑定动效
            if (enableFlag) {
                if (index == currentIndex) {
                    currentlySelectedView = outerContainer;
                    ElasticAnimationUtil.applyLinkedElasticEffect(outerContainer, indicatorView, true);
                } else {
                    ElasticAnimationUtil.applyElasticTouchEffect(outerContainer);
                }
            }

            // 点击事件
/*            tv.setOnClickListener(v -> {
                if (index != currentIndex) {
                    setSelectedIndex(index, true);
                }
            });*/
            outerContainer.setOnClickListener(v -> {
                Log.d("TAG", "outerContainer.setOnTouchListener setItems: " + v);
                if (!enableFlag) {
                    return; // 禁用状态下直接返回
                }
                if (index != currentIndex) {
//                    Log.d("SegmentedPickerView", "index: " + index + "currentIndex: " + currentIndex);
                    if (!changeable) {
                        // 只触发监听事件，不执行任何UI变化
                        if (onItemSelectedListener != null) {
                            onItemSelectedListener.onItemSelected(currentIndex,
                                    items.get(currentIndex).getText());
                        }
                        return;
                    }

                    onItemSelectedListener.onItemClicked(index, items.get(index).getText());
                    // 切换前取消所有动效
                    ElasticAnimationUtil.cancelAnimations(currentlySelectedView);
                    ElasticAnimationUtil.cancelAnimations(indicatorView);

                    // 更新选中状态
                    setSelectedIndex(index, true);

                    // 重新绑定动效
                    currentlySelectedView = outerContainer;
//                    ElasticAnimationUtil.applyLinkedElasticEffect(outerContainer, indicatorView, true);
                }
            });

            outerContainer.addView(innerContainer);
            containerLL.addView(outerContainer);
            itemTextViews.add(tv);
        }

        currentIndex = (previousIndex < items.size()) ? previousIndex : 0;
        updateTextColors();
    }


    // 修改setItems方法可以重复点击
    @SuppressLint("ClickableViewAccessibility")
    public void setItemsRepeatClick(List<SegmentItemBean> newItems) {
        int previousIndex = currentIndex;
        items.clear();
        items.addAll(newItems);

        containerLL.removeAllViews();
        itemTextViews.clear();
        if (hasImage) itemImageViews.clear();

        for (int i = 0; i < items.size(); i++) {
            final int index = i;
            final String text = items.get(i).getText();
            int imageResId = items.get(i).getImageResId();

            // 第一层容器：均分宽度的FrameLayout
            outerContainer = new FrameLayout(getContext());
            LinearLayout.LayoutParams outerLp = new LinearLayout.LayoutParams(
                    0,
                    LayoutParams.MATCH_PARENT,
                    1f
            );
            outerContainer.setLayoutParams(outerLp);

            // 第二层容器：包裹内容的LinearLayout（水平排列图片和文字）
            LinearLayout innerContainer = new LinearLayout(getContext());
            innerContainer.setOrientation(LinearLayout.HORIZONTAL);
            innerContainer.setGravity(Gravity.CENTER);
            FrameLayout.LayoutParams innerLp = new FrameLayout.LayoutParams(
                    LayoutParams.WRAP_CONTENT,
                    LayoutParams.WRAP_CONTENT
            );
            innerLp.gravity = Gravity.CENTER;
            innerContainer.setLayoutParams(innerLp);

            // 图片View
            if (hasImage) {
                ImageView imageView = new ImageView(getContext());
                imageView.setImageResource(imageResId);
                if (items.get(i).isDisImage()) {
                    LinearLayout.LayoutParams ivLp = new LinearLayout.LayoutParams(
                            imageWidthPx > 0 ? imageWidthPx : LayoutParams.WRAP_CONTENT,
                            imageHeightPx > 0 ? imageHeightPx : LayoutParams.WRAP_CONTENT
                    );
                    ivLp.setMargins(0, 0, dpToPx(getContext(), 4), 0);
                    innerContainer.addView(imageView, ivLp);
                }
                itemImageViews.add(imageView);
                updateImageViews();
            }

            // 文字View
            TextView tv = new TextView(getContext());
            tv.setText(text);
            tv.setGravity(Gravity.CENTER);
            tv.setIncludeFontPadding(false);
            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, fontSize);
            tv.setTextColor(getResources().getColor(R.color.black));
            innerContainer.addView(tv);

            // 设置触摸监听器
            outerContainer.setOnTouchListener((v, event) -> {
                if (!enableFlag || !isEnabled()) {
                    v.setPressed(false); // 强制取消按压状态
                    return false;
                }
                if (index == currentIndex) {
                    // 当前选中项：传递事件给indicatorView
                    indicatorView.dispatchTouchEvent(event);
                }
                return false; // 继续传递事件
            });

            // 使用applyLinkedElasticEffect方法绑定动效
            if (enableFlag) {
                if (index == currentIndex) {
                    currentlySelectedView = outerContainer;
                    ElasticAnimationUtil.applyLinkedElasticEffect(outerContainer, indicatorView, true);
                } else {
                    ElasticAnimationUtil.applyElasticTouchEffect(outerContainer);
                }
            }

            // 修改后的点击事件 - 允许重复点击当前选中项
            outerContainer.setOnClickListener(v -> {
                if (!enableFlag) {
                    return; // 禁用状态下直接返回
                }

                // 无论是否是当前选中项都执行以下逻辑
                if (!changeable) {
                    // 只触发监听事件，不执行任何UI变化
                    if (onItemSelectedListener != null) {
                        onItemSelectedListener.onItemSelected(index, text);
                    }
                    return;
                }

                // 如果是切换到了其他项
                if (index != currentIndex) {
                    // 切换前取消所有动效
                    ElasticAnimationUtil.cancelAnimations(currentlySelectedView);
                    ElasticAnimationUtil.cancelAnimations(indicatorView);

                    // 更新选中状态
                    setSelectedIndex(index, true);

                    // 重新绑定动效
                    currentlySelectedView = outerContainer;
                    ElasticAnimationUtil.applyLinkedElasticEffect(outerContainer, indicatorView, true);
                } else {
                    // 当前项被重复点击，只触发回调
                    if (onItemSelectedListener != null) {
                        onItemSelectedListener.onItemSelected(index, text);
                    }

                    ElasticAnimationUtil.cancelAnimations(currentlySelectedView);
                    ElasticAnimationUtil.cancelAnimations(indicatorView);
                    ElasticAnimationUtil.applyLinkedElasticEffect(currentlySelectedView, indicatorView, true);
                }
            });

            outerContainer.addView(innerContainer);
            containerLL.addView(outerContainer);
            itemTextViews.add(tv);
        }

        currentIndex = (previousIndex < items.size()) ? previousIndex : 0;
        updateTextColors();
    }

    /**
     * 设置选中项
     */
    public void setSelectedIndex(int newIndex, boolean animate) {
        if (!enableFlag) {
            // 禁用状态下直接返回，不执行任何操作
            return;
        }

        if (newIndex < 0 || newIndex >= items.size()) return;
        if (newIndex == currentIndex) return;

        // 无论changeable为何值，都触发事件
        if (onItemSelectedListener != null && selectedListenFlag) {
            onItemSelectedListener.onItemSelected(newIndex, items.get(newIndex).getText());
        }

        // 只有changeable为true时才实际切换
        if (changeable) {
            int previousIndex = currentIndex;
            currentIndex = newIndex;

            // 更新动效绑定
            View previousSelected = containerLL.getChildAt(previousIndex);
            View newSelected = containerLL.getChildAt(currentIndex);

            if (previousSelected != null) {
                ElasticAnimationUtil.cancelAnimations(previousSelected);
                ElasticAnimationUtil.applyElasticTouchEffect(previousSelected);
            }
            if (newSelected != null) {
                currentlySelectedView = newSelected;
                ElasticAnimationUtil.cancelAnimations(newSelected);
                ElasticAnimationUtil.cancelAnimations(indicatorView);
                ElasticAnimationUtil.applyLinkedElasticEffect(newSelected, indicatorView, true);
            }

            moveIndicatorTo(newIndex, animate);
        }
    }

    /**
     * 移动指示器到对应选项
     */
    private void moveIndicatorTo(final int index, boolean animate) {
        try {
            // 1. 检查 containerLL 是否为空
            if (containerLL == null) {
                Log.e("SegmentControl", "containerLL is null");
                return;
            }

            // 2. 检查 index 是否越界
            if (index < 0 || index >= containerLL.getChildCount()) {
                Log.e("SegmentControl", "Invalid index: " + index);
                return;
            }

            // 3. 获取 targetView 并判空
            View targetView = containerLL.getChildAt(index);
            if (targetView == null) {
                Log.e("SegmentControl", "targetView is null at index: " + index);
                return;
            }

            // 4. 获取目标位置和宽高
            final int targetLeft = targetView.getLeft();
            final int targetTop = targetView.getTop();
            final int targetWidth = targetView.getWidth();
            final int targetHeight = targetView.getHeight();

            // 5. 计算考虑 margin 后的尺寸
            final int newWidth = targetWidth - 4 * indicatorMarginPx;
            final int newHeight = targetHeight - 4 * indicatorMarginPx;
            final float newTranslationX = targetLeft + indicatorMarginPx;
            final float newTranslationY = targetTop + indicatorMarginPx;

            // 6. 检查 indicatorView 是否为空
            if (indicatorView == null) {
                Log.e("SegmentControl", "indicatorView is null");
                return;
            }

            if (animate) {
                // 先将所有选择框的颜色全部设置为默认颜色
                resetTextColors();
                if (hasImage) {
                    resetImageViews();
                }

                // 创建动画集合
                AnimatorSet animatorSet = new AnimatorSet();

                // 平移动画
                ObjectAnimator translationAnimatorX = ObjectAnimator.ofFloat(
                        indicatorView, "translationX",
                        indicatorView.getTranslationX(),
                        newTranslationX
                );
                ObjectAnimator translationAnimatorY = ObjectAnimator.ofFloat(
                        indicatorView, "translationY",
                        indicatorView.getTranslationY(),
                        newTranslationY
                );

                // 尺寸动画
                final int startWidth = indicatorView.getWidth();
                final int startHeight = indicatorView.getHeight();
                ValueAnimator dimensionAnimator = ValueAnimator.ofFloat(0f, 1f);
                dimensionAnimator.addUpdateListener(animation -> {
                    float fraction = (float) animation.getAnimatedValue();
                    int animWidth = (int) (startWidth + (newWidth - startWidth) * fraction);
                    int animHeight = (int) (startHeight + (newHeight - startHeight) * fraction);
                    LayoutParams params = (LayoutParams) indicatorView.getLayoutParams();
                    params.width = animWidth;
                    params.height = animHeight;
                    indicatorView.setLayoutParams(params);
                });

                // 设置动画集合
                animatorSet.playTogether(translationAnimatorX, translationAnimatorY, dimensionAnimator);
                animatorSet.setDuration(300);
                animatorSet.setInterpolator(new DecelerateInterpolator());

                // 添加动画结束监听
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        updateVisualState(index); // 提取公共方法
                    }
                });

                animatorSet.start();
            } else {
                indicatorView.setTranslationX(newTranslationX);
                indicatorView.setTranslationY(newTranslationY);
                LayoutParams params = (LayoutParams) indicatorView.getLayoutParams();
                params.width = newWidth;
                params.height = newHeight;
                indicatorView.setLayoutParams(params);

                // 非动画模式下立即更新视觉状态
                updateVisualState(index);
            }
        } catch (Exception e) {
            Log.e("SegmentControl", "moveIndicatorTo failed: " + e.getMessage(), e);
        }
    }

    /**
     * 更新文本和图片的视觉状态
     */
    private void updateVisualState(int index) {
        updateTextColors();
        if (hasImage) {
            updateImageViews();
        }
    }

    /**
     * 更新选项字体颜色：选中项白色，未选中项黑色
     */
    private void resetTextColors() {
        for (int i = 0; i < itemTextViews.size(); i++) {
            itemTextViews.get(i).setTextColor(getResources().getColor(R.color.black));
        }
    }

    private void updateTextColors() {
        for (int i = 0; i < itemTextViews.size(); i++) {
            if (i == currentIndex) {
                itemTextViews.get(i).setTextColor(getResources().getColor(R.color.white));
            } else {
                itemTextViews.get(i).setTextColor(getResources().getColor(R.color.black));
            }
        }
    }

    private void resetImageViews() {
        for (int i = 0; i < itemImageViews.size(); i++) {
            itemImageViews.get(i).setImageResource(items.get(i).getImageResId());
        }
    }

    private void updateImageViews() {
        for (int i = 0; i < itemImageViews.size(); i++) {
            if (i == currentIndex) {

                itemImageViews.get(i).setImageResource(items.get(i).getImageSelectedResId());


            } else {

                itemImageViews.get(i).setImageResource(items.get(i).getImageResId());


            }
        }
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        // 页面切换回来后布局可能重新完成，这里确保位置刷新
        post(() -> moveIndicatorTo(currentIndex, false));
    }


    // ====== 若需要在代码中动态修改宽高，可加上以下两种set方法 ======
    public void setPickerWidth(int widthPx) {
        customWidth = widthPx;
        LayoutParams params = (LayoutParams) getLayoutParams();
        params.width = customWidth;
        setLayoutParams(params);
    }

    public void setPickerHeight(int heightPx) {
        customHeight = heightPx;
        LayoutParams params = (LayoutParams) getLayoutParams();
        params.height = customHeight;
        setLayoutParams(params);
    }

    // 自定义指示器边距
    public void setIndicatorMargin(int marginPx) {
//        customMargin = marginPx;
        moveIndicatorTo(currentIndex, false);
    }

    public void setSelIndicator(int selIndex) {
        moveIndicatorTo(selIndex, false);
    }

    // 自定义图片大小
    public void setImageSize(int widthPx, int heightPx) {
        this.imageWidthPx = widthPx;
        this.imageHeightPx = heightPx;
        // 如果需要，刷新一下所有选项（可选）
        // updateItems();
    }

    // 在 SegmentedPickerView.java 中添加
    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);

        // 1. 设置透明度
        setAlpha(enabled ? 1.0f : 0.3f);

        // 2. 禁用所有子View交互
        for (int i = 0; i < containerLL.getChildCount(); i++) {
            View child = containerLL.getChildAt(i);
            child.setEnabled(enabled);
            child.setClickable(enabled);
            child.setFocusable(enabled);
            child.setBackground(null); // 移除可能存在的波纹效果
        }

        // 3. 取消所有动画
        ElasticAnimationUtil.cancelAnimations(indicatorView);
        for (int i = 0; i < containerLL.getChildCount(); i++) {
            ElasticAnimationUtil.cancelAnimations(containerLL.getChildAt(i));
        }

        // 4. 禁用触摸事件（关键！）
        containerLL.setOnTouchListener((v, event) -> !enabled);
        indicatorView.setOnTouchListener((v, event) -> !enabled);

        // 5. 更新全局标志
        enableFlag = enabled;
    }

}
