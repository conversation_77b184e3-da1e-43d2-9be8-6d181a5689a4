package com.bitech.vehiclesettings.view.common;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.text.TextUtils;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertComfirmBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

/**
 * FileName: ComfirmUIAlert
 * Author: LIC4WHU
 * Date: 2025/4/10 15:09
 * Description:通用对话框
 */
public class ComfirmUIAlert extends BaseDialog {

    private static onComfirmListener onComfirmListener;
    private onComfirmListener onConfirmListener2;
    private String mFunction;

    public ComfirmUIAlert(Context context) {
        super(context);
    }

    public ComfirmUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected ComfirmUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static void setOnComfirmListener(ComfirmUIAlert.onComfirmListener onComfirmListener) {
        ComfirmUIAlert.onComfirmListener = onComfirmListener;
    }

    public void setOnConfirmListener2(onComfirmListener listener, String function) {
        this.onConfirmListener2 = listener;
        this.mFunction = function;
    }


    public static class Builder implements View.OnClickListener {

        private final Context context;
        private boolean isCan = true;
        private String mTitle, mContent, mConfirm, mCancel;
        private ComfirmUIAlert dialog = null;
        private DialogAlertComfirmBinding binding;
        private boolean globalAlert = false;
        private String mFunction;
        private onComfirmListener mListener;

        public Builder(Context context) {
            this.context = context;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public Builder setTitle(String title) {
            this.mTitle = title;
            return this;
        }

        public Builder setContent(String content) {
            this.mContent = content;
            return this;
        }

        public Builder setConfirmText(String confirm) {
            this.mConfirm = confirm;
            return this;
        }

        public Builder setCancelText(String cancel) {
            this.mCancel = cancel;
            return this;
        }

        public Builder setFunction(String function) {
            this.mFunction = function;
            return this;
        }

        // 添加设置监听器方法
        public Builder setOnConfirmListener2(onComfirmListener listener) {
            this.mListener = listener;
            return this;
        }

        public void setGlobalAlert(boolean globalAlert) {
            this.globalAlert = globalAlert;
        }

        /**
         * Create the custom dialog
         */
        public ComfirmUIAlert create(int width, int height) {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new ComfirmUIAlert(context,
                        R.style.Dialog);
            }
            // 设置dialog的bind
            context.setTheme(Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue));
            binding = DialogAlertComfirmBinding.inflate(LayoutInflater.from(context));
            binding.tvTitle.setText(mTitle);
            binding.tvContent.setText(mContent);
            binding.tvConfirm.setText(TextUtils.isEmpty(mConfirm) ? context.getString(R.string.str_confirm2) : mConfirm);
            binding.tvCancel.setText(TextUtils.isEmpty(mCancel) ? context.getString(R.string.str_cancel) : mCancel);

            // 设置监听器和参数
            if (mListener != null) {
                dialog.setOnConfirmListener2(mListener, mFunction);
            }

            BindingUtil.bindClicks(this, binding.tvConfirm, binding.tvCancel);
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = width;
            layoutParams.height = height;
            layoutParams.type = globalAlert ? WindowManager.LayoutParams.TYPE_SYSTEM_ALERT :
                    WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;

            window.setAttributes(layoutParams);
            return dialog;
        }

        @SuppressLint("NonConstantResourceId")
        @Override
        public void onClick(View view) {
            switch (view.getId()) {
                case R.id.tv_confirm:
                    if (onComfirmListener != null) {


                        onComfirmListener.onComfirm("vehicle_power_off");
                        dialog.cancel();
                    }
                    if (dialog != null && dialog.onConfirmListener2 != null) {
                        dialog.onConfirmListener2.onComfirm(dialog.mFunction);
                        dialog.dismiss();
                    }
                    break;
                case R.id.tv_cancel:
                    if (dialog != null) {
                        dialog.cancel();
                    }
                    break;
            }
        }
    }

    public interface onComfirmListener {
        void onComfirm(String function);
    }

}
