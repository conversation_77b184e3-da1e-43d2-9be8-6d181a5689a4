package com.bitech.vehiclesettings.view.common;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.pdf.PdfRenderer;
import android.os.ParcelFileDescriptor;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertPdfDetailsBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class PdfDetailsUIAlert extends BaseDialog {
    private static final String TAG = PdfDetailsUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public PdfDetailsUIAlert(@NonNull Context context) {
        super(context);
    }

    public PdfDetailsUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PdfDetailsUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        PdfDetailsUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertPdfDetailsBinding binding;
        private boolean isBlueOpen = false;
        public PdfDetailsUIAlert dialog = null;
        private View layout;
        WindowManager.LayoutParams layoutParams;

        public Builder(Context context) {
            this.context = context;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public PdfDetailsUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }

        public PdfDetailsUIAlert create() {
            return create("标题", "", 1128, 428);
        }

        public PdfDetailsUIAlert create(String title, String contentPath, int width, int height) {
            if (dialog == null)
                dialog = new PdfDetailsUIAlert(context, R.style.Dialog);
            dialog.setCancelable(isCan);

            binding = DialogAlertPdfDetailsBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            layoutParams = window.getAttributes();
            setSize(width, height);
            window.setAttributes(layoutParams);
            // 设置显示文本
            setText(title, contentPath);
            // 默认正文不可滚动
            setScrollable(false);
            return dialog;
        }

        public void setPadding(int size) {
            binding.scrollView.setPadding(size, 0, size, 0);
        }

        public void setScrollable(boolean flag) {
            binding.scrollView.setScrollable(flag);
        }

        public void setSize(int width, int height) {
            layoutParams.width = width;
            layoutParams.height = height;
        }

        @SuppressLint("ClickableViewAccessibility")
        public void setText(String title, String assetPath) {
            binding.tvTitle.setText(title);

            if (assetPath == null || assetPath.isEmpty()) {
                Toast.makeText(context, "PDF路径为空", Toast.LENGTH_SHORT).show();
                return;
            }

            File file = new File(context.getCacheDir(), "temp.pdf");

            try (InputStream input = context.getAssets().open(assetPath);
                 OutputStream output = new FileOutputStream(file)) {

                byte[] buffer = new byte[1024];
                int read;
                while ((read = input.read(buffer)) != -1) {
                    output.write(buffer, 0, read);
                }

            } catch (IOException e) {
                e.printStackTrace();
                Toast.makeText(context, "复制PDF文件出错", Toast.LENGTH_SHORT).show();
                return;
            }

            try (ParcelFileDescriptor fileDescriptor = ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY);
                 PdfRenderer pdfRenderer = new PdfRenderer(fileDescriptor)) {

                binding.pdfContainer.removeAllViews(); // 清空容器，防止复用时叠加

                int pageCount = pdfRenderer.getPageCount();
                DisplayMetrics metrics = context.getResources().getDisplayMetrics();
                float density = metrics.densityDpi / 160f; // 获取设备的密度比例

                // 定义要裁剪的页眉页脚高度（单位：dp）
                final int HEADER_CROP_DP = 80; // 去掉20dp的页眉
                final int FOOTER_CROP_DP = 80; // 去掉30dp的页脚

                // 转换为像素值
                final int headerCropPx = (int) (HEADER_CROP_DP * density);
                final int footerCropPx = (int) (FOOTER_CROP_DP * density);

                for (int i = 0; i < pageCount; i++) {
                    PdfRenderer.Page page = pdfRenderer.openPage(i);

                    int pageWidth = Math.max(page.getWidth(), 1);
                    int pageHeight = Math.max(page.getHeight(), 1);

                    // 根据屏幕 DPI 调整渲染的宽高
                    int adjustedWidth = (int) (pageWidth * density);
                    int adjustedHeight = (int) (pageHeight * density);

                    // 创建原始Bitmap
                    Bitmap originalBitmap = Bitmap.createBitmap(adjustedWidth, adjustedHeight, Bitmap.Config.ARGB_8888);
                    page.render(originalBitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY);
                    page.close();

                    // 裁剪页眉页脚
                    Bitmap finalBitmap;
                    try {
                        int croppedHeight = originalBitmap.getHeight() - headerCropPx - footerCropPx;
                        if (croppedHeight > 0) {
                            finalBitmap = Bitmap.createBitmap(
                                    originalBitmap,
                                    0,                          // 起始X坐标
                                    headerCropPx,               // 起始Y坐标（跳过页眉）
                                    originalBitmap.getWidth(),  // 宽度不变
                                    croppedHeight               // 新高度
                            );
                            originalBitmap.recycle(); // 释放原bitmap
                        } else {
                            finalBitmap = originalBitmap;
                        }
                    } catch (IllegalArgumentException e) {
                        // 如果裁剪参数不合法，使用原图
                        finalBitmap = originalBitmap;
                    }

                    // 创建支持缩放的ImageView
                    ImageView imageView = new ImageView(context);
                    imageView.setImageBitmap(finalBitmap);
                    imageView.setAdjustViewBounds(true);
                    imageView.setScaleType(ImageView.ScaleType.FIT_CENTER);

                    // 启用缩放
                    imageView.setOnTouchListener(new ZoomTouchListener(context));

                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                            LinearLayout.LayoutParams.MATCH_PARENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT
                    );
                    params.topMargin = 0; // 减小页面间距
                    imageView.setLayoutParams(params);

                    binding.pdfContainer.addView(imageView);
                }

            } catch (IOException e) {
                e.printStackTrace();
                Toast.makeText(context, "加载PDF失败", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }
}
