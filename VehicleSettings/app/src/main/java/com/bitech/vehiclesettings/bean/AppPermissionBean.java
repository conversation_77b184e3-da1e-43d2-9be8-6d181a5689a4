package com.bitech.vehiclesettings.bean;

import com.iflytek.adapter.GsonUtil;

import org.json.JSONException;

public class AppPermissionBean {
    private final String appName;           // 应用名
    private final String packageName;       // 包名（唯一标识）
    private final PermissionType type;      // 哪个权限（摄像头/麦克/位置）
    private AppPermissionLevel level;       // 授权级别

    public AppPermissionBean(
        String appName,
        String packageName,
        PermissionType type,
        AppPermissionLevel level
    ) {
        this.appName = appName;
        this.packageName = packageName;
        this.type = type;
        this.level = level;
    }

    // --- Getter & Setter ---
    public String getAppName() { return appName; }
    public String getPackageName() { return packageName; }
    public PermissionType getType() { return type; }
    public AppPermissionLevel getLevel() { return level; }

    public void setLevel(AppPermissionLevel level) {
        this.level = level;
    }

    /** 用于在 UI 上显示中文描述 */
    public String getLevelLabel() {
        switch (level) {
            case ONE_TIME:   return "每次使用访问";
            case WHILE_USING:return "仅使用期间允许";
            case DENIED:     return "禁用";
            default:         return "";
        }
    }

    public String toJson() throws JSONException {
        return GsonUtil.toJson(this);
    }

    @Override
    public String toString() {
        return "AppPermissionBean{" +
                "appName='" + appName + '\'' +
                ", packageName='" + packageName + '\'' +
                ", type=" + type +
                ", level=" + level +
                '}';
    }
}
