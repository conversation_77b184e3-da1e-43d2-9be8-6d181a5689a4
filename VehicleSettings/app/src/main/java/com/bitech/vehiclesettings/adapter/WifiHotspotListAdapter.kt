package com.bitech.vehiclesettings.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.WifiHotspotBean
import com.bitech.vehiclesettings.databinding.ItemHotspotConnectedDevicesBinding
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @ClassName: WifiHotspotListAdapter
 * 
 * @Date:  2024/2/5 17:22
 * @Description: WIFI热点已连接列表适配器.
 **/
class WifiHotspotListAdapter(private var hotspotConnectedList: CopyOnWriteArrayList<WifiHotspotBean>) :
    RecyclerView.Adapter<WifiHotspotListAdapter.HotspotViewHolder>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): HotspotViewHolder {
        // 视图绑定
        val binding = ItemHotspotConnectedDevicesBinding.bind(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_hotspot_connected_devices, parent, false)
            )
        return HotspotViewHolder(binding)
    }

    override fun onBindViewHolder(holder: HotspotViewHolder, position: Int) {
        if (hotspotConnectedList[position].hotspotName.isBlank()) {
            // 设置热点设备mac地址
            holder.hotspotDeviceName.text = hotspotConnectedList[position].hotspotMac
        } else {
            // 设置热点设备名称
            holder.hotspotDeviceName.text = hotspotConnectedList[position].hotspotName
        }
    }

    override fun getItemCount(): Int {
        // 列表item数量
        return hotspotConnectedList.size
    }

    /**
     * 设置热点已连接设备列表数据.
     *
     * @param hotspotList 已连接设备列表数据
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setHotspotConnectedList(hotspotList: CopyOnWriteArrayList<WifiHotspotBean>) {
        hotspotConnectedList = hotspotList
        notifyDataSetChanged()
    }

    /**
     * @ClassName: HotspotViewHolder
     * 
     * @Date:  2024/2/5 17:27
     * @Description: 热点已连接设备列表Adapter对应的ViewHolder.
     **/
    inner class HotspotViewHolder(binding: ItemHotspotConnectedDevicesBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val hotspotDeviceName = binding.wifiHotspotDeviceNameTv
    }
}
