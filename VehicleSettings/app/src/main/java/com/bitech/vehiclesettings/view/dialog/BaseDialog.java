package com.bitech.vehiclesettings.view.dialog;

import android.app.Application;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.ComponentCallbacks2;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public abstract class BaseDialog extends Dialog {
    private static final String TAG = "BaseDialog";

    private static final Set<BaseDialog> sAllDialogs =
            Collections.synchronizedSet(new HashSet<>());
    private static boolean sCallbackRegistered = false;

    private int childDialogCount = 0;
    private boolean isDismissing = false;
    public static final String ACTION_CLOSE_DIALOG_BAR = "action_close_dialog_bar";
    public static final String ACTION_SWIPE_FROM_LEFT = "global_swipe_from_left";
    public static final String ACTION_SWIPE_FROM_RIGHT = "global_swipe_from_right";
    public static final String ACTION_GO_HOME = "action.GO_HOME";
    public static final String ACTION_GO_PHONE = "action.GO_PHONE";
    public static final String ACTION_GO_APP_LIST = "action.GO_APP_LIST";
    public static final String ACTION_GO_NAVI = "action.GO_NAVI";
    public static final String ACTION_GO_MUSIC = "action.GO_MUSIC";
    public static final String ACTION_CLOSE_DIALOG = "mega_action_close_dialog";
    private static final String[] SYSTEM_UI_ACTIONS = {
            ACTION_CLOSE_DIALOG_BAR,
            ACTION_SWIPE_FROM_LEFT,
            ACTION_SWIPE_FROM_RIGHT,
            ACTION_GO_HOME,
            ACTION_GO_PHONE,
            ACTION_GO_APP_LIST,
            ACTION_GO_NAVI,
            ACTION_GO_MUSIC,
            ACTION_CLOSE_DIALOG
    };
    private boolean autoDismissEnabled = true;
    private BroadcastReceiver systemUiReceiver;

    public BaseDialog(@NonNull Context context) {
        super(context);
        ensureCallbacks(context.getApplicationContext());
    }

    public BaseDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        ensureCallbacks(context.getApplicationContext());
    }

    protected BaseDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        ensureCallbacks(context.getApplicationContext());
    }

    /**
     * 设置是否允许自动关闭弹窗
     * @param enabled true表示允许自动关闭，false表示禁止自动关闭
     */
    public void setAutoDismissEnabled(boolean enabled) {
        this.autoDismissEnabled = enabled;
        Log.d(TAG, "Auto dismiss " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * 获取当前自动关闭状态
     * @return true表示允许自动关闭，false表示禁止自动关闭
     */
    public boolean isAutoDismissEnabled() {
        return autoDismissEnabled;
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        // 当焦点丢失且无子弹窗且允许自动关闭时关闭
        if (!hasFocus && !isDismissing && childDialogCount == 0 && autoDismissEnabled) {
            Log.d(TAG, "Window focus lost, dismissing dialog");
            dismiss();
        }
    }

    private static synchronized void ensureCallbacks(Context appContext) {
        if (!sCallbackRegistered && appContext instanceof Application) {
            Application app = (Application) appContext;
            app.registerComponentCallbacks(new ComponentCallbacks2() {
                @Override
                public void onTrimMemory(int level) {
                    if (level == ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
                        Log.d(TAG, "App in background: dismiss all dialogs");
                        synchronized (sAllDialogs) {
                            for (BaseDialog dlg : new HashSet<>(sAllDialogs)) {
                                dlg.dismissImmediately();
                            }
                        }
                    }
                }
                @Override public void onConfigurationChanged(Configuration newConfig) {}
                @Override public void onLowMemory() {}
            });
            sCallbackRegistered = true;
        }
    }

    @Override
    public void show() {
        super.show();
        sAllDialogs.add(this);
        registerSystemUiReceiver();
        Log.d(TAG, "Dialog shown, added to global list");
    }

    @Override
    public void dismiss() {
        dismissImmediately();
    }

    private void dismissImmediately() {
        if (isDismissing) return;
        isDismissing = true;
        try {
            if (getWindow() != null) getWindow().setWindowAnimations(0);
            super.dismiss();
            Log.d(TAG, "Dialog dismissed immediately");
        } catch (Throwable t) {
            Log.w(TAG, "Error in dismissImmediately", t);
        } finally {
            isDismissing = false;
            unregisterSystemUiReceiver();
            sAllDialogs.remove(this);
        }
    }

    public void registerChildDialog() {
        childDialogCount++;
        Log.d(TAG, "Child dialog registered, count=" + childDialogCount);
    }

    public void unregisterChildDialog() {
        if (childDialogCount > 0) {
            childDialogCount--;
            Log.d(TAG, "Child dialog unregistered, count=" + childDialogCount);
        }
    }

    private void registerSystemUiReceiver() {
        if (systemUiReceiver != null) return;

        systemUiReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                for (String targetAction : SYSTEM_UI_ACTIONS) {
                    if (targetAction.equals(action)) {
                        Log.d(TAG, "Received action " + action + ", dismissing dialog");
                        dismiss();
                        break;
                    }
                }
            }
        };

        IntentFilter filter = new IntentFilter();
        for (String action : SYSTEM_UI_ACTIONS) {
            filter.addAction(action);
        }

        getContext().registerReceiver(systemUiReceiver, filter);
        Log.d(TAG, "System UI receiver registered");
    }

    private void unregisterSystemUiReceiver() {
        if (systemUiReceiver != null) {
            try {
                getContext().unregisterReceiver(systemUiReceiver);
                Log.d(TAG, "System UI receiver unregistered");
            } catch (IllegalArgumentException e) {
                Log.w(TAG, "Receiver not registered", e);
            } finally {
                systemUiReceiver = null;
            }
        }
    }
}
