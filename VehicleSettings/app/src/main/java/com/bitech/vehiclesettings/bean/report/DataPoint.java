package com.bitech.vehiclesettings.bean.report;

import androidx.annotation.NonNull;

import java.util.ArrayList;

public class DataPoint {
    private String id;
    private String eventId;
    private long timestamp;
    private String supplierCode;
    private String platformCode;
    private int nodeType;

    private ArrayList<Content> content;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public int getNodeType() {
        return nodeType;
    }

    public void setNodeType(int nodeType) {
        this.nodeType = nodeType;
    }

    public ArrayList<Content> getContent() {
        return content;
    }

    public void setContent(ArrayList<Content> content) {
        this.content = content;
    }

    @NonNull
    @Override
    public String toString() {
        return "DataPoint{" +
                "id='" + id + '\'' +
                ", eventId='" + eventId + '\'' +
                ", timestamp=" + timestamp +
                ", supplierCode='" + supplierCode + '\'' +
                ", platformCode='" + platformCode + '\'' +
                ", nodeType=" + nodeType +
                ", content=" + content +
                '}';
    }

}
