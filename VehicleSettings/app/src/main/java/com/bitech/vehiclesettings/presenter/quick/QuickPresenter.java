package com.bitech.vehiclesettings.presenter.quick;

import android.content.Context;
import android.provider.Settings;
import android.util.Log;
import android.widget.SeekBar;
import android.widget.TextView;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.platformlib.manager.SystemManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl;
import com.bitech.vehiclesettings.common.SiganlConstans;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.id.vehicle.VDEventVehicle;
import com.chery.ivi.vdb.event.id.vehicle.VDKeyVehicle;

public class QuickPresenter<T> implements QuickPresenterListener {
    private static final String TAG = QuickPresenter.class.getSimpleName();
    private Context mContext;
    private T data;

    private QuickManager quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public QuickPresenter(Context context) {
        this.mContext = context;
    }

    /**
     * 设置中控锁
     *
     * @param status
     */
    @Override
    public void setCentralLocking(int status) {
        //显⽰名称：中控锁 开关设置：解锁/闭锁 开关默认值：解锁
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击中控锁软开关解锁
        //2. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击中控锁软开关闭锁
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_CenterLockSwt = 0x1: UNLOCK，然后发送 0x0:Not
        //Active 给 FLZCU，⼤屏中控锁设置显⽰为解锁，计时 2s 若检测到状态反馈信号 LHFDoorLockSts
        //= 0x1:Unlocked，则⼤屏中控锁设置显⽰保持为解锁状态， 否则弹回闭锁状态；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_CenterLockSwt = 0x2:LOCK，然后发送 0x0:Not Active
        //给 FLZCU，⼤屏中控锁设置显⽰为闭锁，计时 2s 若检测到状态反馈信号 LHFDoorLockSts =
        //0x0:Locked || 0x2:Superlocked，则⼤屏中控锁设置显⽰保持为闭锁状态，否则弹回解锁状态；
        //3. ICC 收到 LHFDoorLockSts =0x3:Unknown，⼤屏显⽰中控锁设置显⽰根据上次状态显⽰不变

        // ICC -> FLZCU  信号名：ICC_CenterLockSwt
        // 0x0:Not Active
        // 0x1: UNLOCK
        // 0x2:LOCK
        // 0x3:Reserved

        // FLZCU -> ICC 信号名：LHFDoorLockSts
        // 0x0:Not Locked
        // 0x1:Unlocked
        // 0x2:Superlocked
        // 0x3:Unknown
        Log.d(TAG, "setCentralLocking: 发送中控锁:" + status);
        if (MsgUtil.getInstance().supportPowerMode()) {
            int singlValue = 0;
            if (status == 0) {
                singlValue = 0x1;
            } else if (status == 1) {
                singlValue = 0x2;
            }
            // 2.调用接口 {"extension":{"raw_value":0},"relative":false,"time":1451636086904,"valid":true,"value":0}
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_CENTRALLOCK_SET, singlValue);
            quickManager.setCenterLock(singlValue);
        }
    }

    @Override
    public int getCentralLocking() {
        // 2.调用接口
//        int singlValue = MsgUtil.getInstance().getSignlVal(SiganlConstans.VehicleDoor_CentralLock);
        int singlValue = quickManager.getCenterLock();
        Log.d(TAG, "getCentralLocking: 获取中控锁:" + singlValue);
        int status = 0;
        if (singlValue == 0x1) {
            status = 0;
        } else if (singlValue == 0x0 || singlValue == 0x2) {
            status = 1;
        }
//        Prefs.put(PrefsConst.Q_CENTER_LOCK, status);
        return Prefs.rtnStatus(PrefsConst.Q_CENTER_LOCK, status, PrefsConst.DefaultValue.Q_CENTER_LOCK);
//        return status;
    }

    @Override
    public void setRearTailGate(int status) {
        //显⽰名称：后尾⻔（软开关） 开关设置：开启/关闭 开关默认值：关闭
        //配置：PLG 配置
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 后备箱关闭，在⼤屏⻓按后备箱开关 500ms；
        //2. 后备箱打开，在⼤屏⻓按后备箱开关 500ms；
        //执⾏输出（1||2）
        //1. 若触发条件为 1， 开关显⽰为⾼亮（开启状态），ICC 持续发送六帧 ICC_TrunkSW=0x1:ON，之
        //后发送 0x0:Not Active 给 PLG，计时 6.5s 若接收到状态反馈信号 PLG_LatchSts =0x0 :Open ||
        //0x1:Secondary，则后备箱开关保持⾼亮，否则开关弹回⾮⾼亮；
        //2. 若触发条件为 2， 开关显⽰为⾮⾼亮（关闭状态），ICC 持续发送六帧 ICC_TrunkSW=0x2:OFF，
        //之后发送 0x0:Not Active 给 PLG，计时 6.5s 若接收到状态反馈信号 PLG_LatchSts
        //=0x2:Latched，则后备箱开关保持⾮⾼亮，否则开关弹回⾼亮；

        // ICC -> PLG 信号名：ICC_TrunkSW
        // 0x0:Not Active
        // 0x1:ON
        // 0x2:OFF
        // 0x3:Reserved

        // PLG -> ICC 信号名：PLG_LatchSts
        // 0x0:Open
        // 0x1:Secondary
        // 0x2:Latched
        // 0x3:Initializing
        Log.d(TAG, "后尾门: " + status);
        // 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)
        if (MsgUtil.getInstance().supportPowerMode()) {
            int singlValue = 0;
            if (status == 1) {
                // 后备箱打开
                singlValue = 1;
            } else {
                // 后背箱关闭
                singlValue = 2;
            }
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_TAILGATESWREEVPLG_SET, singlValue);
            quickManager.setRearTailGate(singlValue);
        }
    }

    @Override
    public int getRearTailGate() {
        //计时 6.5s
        // 反馈信号 PLG_LatchSts =0x0 :Open || 0x1:Secondary，则后备箱开关保持高亮
        // 反馈信号 PLG_LatchSts =0x2:Latched，则后备箱开关保持非高亮，否则开关弹回高亮
//        int status = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEDOOR_TAILGATESWREEVPLG);
        int status = quickManager.getRearTailGate();
//        int status = 0;
        if (status == 0 || status == 1) {
            status = 1;
        } else if (status == 2) {
            status = 0;
        }

        Log.d(TAG, "getCentralLocking: 获取后尾门:" + status);
        return Prefs.rtnStatus(PrefsConst.Q_REAR_TAIL_GATE, status, PrefsConst.DefaultValue.Q_REAR_TAIL_GATE);
    }

    @Override
    public void setRearMirror(int status) {
        //显⽰名称：后视镜折叠 开关设置：折叠/展开 开关默认值：展开
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 点击⼤屏上的软开关使其展开；
        //2. 点击⼤屏上的软开关使其折叠；
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_RearMirrorFoldCmd =0x2: Unfold ，然后发送 0x0:Not
        //Active 给 FLZCU， FLZCU 控制开启外后视镜展开，计时 2s 若接收到 状态反馈信号
        //RearViewFoldSts=0x2: Unfold，则外后视镜折叠开关保持展开，否则开关弹回折叠；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_RearMirrorFoldCmd =0x1: Fold ，然后发送 0x0:Not
        //Active 给 FLZCU，FLZCU 控制外后视镜折叠，计时 2s 若接收到状态反馈信号 RearViewFoldSts
        //=0x1: Fold，则外后视镜折叠开关保持折叠，否则开关弹回展开；

        // ICC -> FLZCU 信号名：ICC_RearMirrorFoldCmd
        // 0x0:Not Active
        // 0x1:Fold
        // 0x2:Unfold

        // FLZCU -> ICC 信号名：RearViewFoldSts
        // 0x0:invalid
        // 0x1:Fold
        // 0x2:Unfold

        // 后视镜折叠 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)； 开关默认值：展开
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "后视镜折叠: " + status);
            if (status == 1) {
                // ICC 连续发送三帧 ICC_RearMirrorFoldCmd =0x1: Fold ，然后发送 0x0:Not  Active 给 FLZCU，FLZCU 控制外后视镜折叠，
                // 计时 2s 若接收到状态反馈信号 RearViewFoldSts =0x1: Fold，则外后视镜折叠开关保持折叠，否则开关弹回展开
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWFOLD_SET, 1);
                quickManager.setRearMirror(1);
            } else {
                //ICC 连续发送三帧 ICC_RearMirrorFoldCmd =0x2: Unfold ，然后发送 0x0:Not Active 给 FLZCU，
                // FLZCU 控制开启外后视镜展开，计时 2s 若接收到 状态反馈信号 RearViewFoldSts=0x2: Unfold，则外后视镜折叠开关保持展开，否则开关弹回折叠；
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWFOLD_SET, 2);
                quickManager.setRearMirror(2);
            }
        }
    }

    @Override
    public int getRearMirror() {
        // 反馈信号 RearViewFoldSts =0x1: Fold
//        int status = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_REARVIEWFOLD);
        int status = quickManager.getRearMirror();
        if (status == 1) {
            status = 1;
        } else if (status == 2) {
            // 展开
            status = 0;
        }
        Log.d(TAG, "getCentralLocking: 获取后视镜折叠状态:" + status);
        return Prefs.rtnStatus(PrefsConst.Q_REAR_MIRROR_FOLD, status, PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD);
    }

    @Override
    public void setWindow(int status) {
        //   前置条件：（1&2&3）
        //   1.	电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //   2.	当一键四门车窗关闭按键激活可用 （激活可用：前置条件3）；
        //   3.	四车窗开启比例不全为 0
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (status) {
                case 0:
                    //  ICC_WindowCmd =0xE:All Windows close 发送给 FZCU， FZCU 控制四车窗均关闭；
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEWINDOW_WINDOWVENTILATE_SET, 0xE);
                    quickManager.setWindowVentilate(0xE);
                    break;
                case 1:
                    // ICC 发送一键四车窗开启指令 ICC_WindowCmd ==0xD:All Windows open 发送给 FZCU， FZCU 控制四车窗均开启
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEWINDOW_WINDOWVENTILATE_SET, 0xD);
                    quickManager.setWindowVentilate(0xD);
                    break;
                case 2:
                    // ICC 发送一键四车窗透气指令 ICC_WindowCmd ===0xF:All Windows comfort 发送给 FZCU， FZCU 控制四门车窗打开到 20%位置
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEWINDOW_WINDOWVENTILATE_SET, 0xF);
                    quickManager.setWindowVentilate(0xF);
                    break;
            }
        }
//        Prefs.put(PrefsConst.Q_WINDOW, status);
    }

    @Override
    public void setWindowState(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.setWindowVentilate(state);
        }
//        Prefs.put(PrefsConst.Q_WINDOW, state);
    }

    @Override
    public void setFLWindow(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.setFLWindow(state);
        }
//        Prefs.put(PrefsConst.Q_WINDOW_FL, state);
    }

    @Override
    public void setFRWindow(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.setFRWindow(state);
        }
//        Prefs.put(PrefsConst.Q_WINDOW_FR, state);
    }

    @Override
    public void setRLWindow(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.setRLWindow(state);
        }
//        Prefs.put(PrefsConst.Q_WINDOW_RL, state);
    }

    @Override
    public void setRRWindow(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.setRRWindow(state);
        }
//        Prefs.put(PrefsConst.Q_WINDOW_RR, state);
    }

    @Override
    public int getWindow() {
        int status;
        // 四个车窗开启比例
//        int statusFL = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEWINDOW_WINDOW_FRONTLEFT);
        int statusFL = quickManager.getFLWindow();
//        int statusFR = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEWINDOW_WINDOW_FRONTRIGHT);
        int statusFR = quickManager.getFRWindow();
//        int statusRR = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEWINDOW_WINDOW_REARRIGHT);
        int statusRR = quickManager.getRLWindow();
//        int statusRL = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEWINDOW_WINDOW_REARLEFT);
        int statusRL = quickManager.getRRWindow();
        if (statusFL == 0x14 && statusFR == 0x14 && statusRR == 0x14 && statusRL == 0x14) {
            status = 2;
//            Log.d(TAG, "getWindow: 获取车窗状态:" + status);
            // 20%位置，透气
//            return status;
        } else if (statusFL == 0x0 && statusFR == 0x0 && statusRR == 0x0 && statusRL == 0x0) {
            status = 0;
        } else if (statusFL == 0x64 && statusFR == 0x64 && statusRR == 0x64 && statusRL == 0x64) {
            status = 1;
        } else {
            status = -1;
        }

        Log.d(TAG, "getWindow: 获取车窗状态:" + status);
        return status;
    }

    @Override
    public int getFLWindow() {
        return quickManager.getFLWindow();
    }

    @Override
    public int getFRWindow() {
        return quickManager.getFRWindow();
    }

    @Override
    public int getRRWindow() {
        return quickManager.getRRWindow();
    }

    @Override
    public int getRLWindow() {
        return quickManager.getRLWindow();
    }

    @Override
    public int getFLDoor() {
        return quickManager.getFLDoor();
    }

    @Override
    public int getFRDoor() {
        return quickManager.getFRDoor();
    }

    @Override
    public int getRLDoor() {
        return quickManager.getRLDoor();
    }

    @Override
    public int getRRDoor() {
        return quickManager.getRRDoor();
    }

    @Override
    public void setWindowLock(int state) {
        // 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setWindowLock: 设置车窗锁状态: " + state);
            switch (state) {
                case 0:
                    // ICC 连续发送三帧 ICC_WinInhbSwt =0x1:Inhibit，然后发送 0x0:Not  Active 给 FLZCU，
                    // 大屏车窗锁设置显示为闭锁，计时 2s 若检测到状态反馈信号 FLZCU_WindowInhibitSts = 0x1:Inhibit，则大屏车窗锁设置显示 保持为锁上状态，否则弹回打开状态
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_WINDOWLOCKSTATUS_SET, 0x1);
                    quickManager.setWindowLock(0x2);
                    break;
                case 1:
                    // ICC 连续发送三帧 ICC_WinInhbSwt =0x2:Permit，然后发送 0x0:Not  Active 给 FLZCU，
                    // 大屏车窗锁设置显示为解锁，计时2s 若检测到状态反馈信号 FLZCU_WindowInhibitSts = 0x0:Permit，则大屏车窗锁设置显示保持为打开状态， 否则弹回锁上状态
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_WINDOWLOCKSTATUS_SET, 0x2);
                    quickManager.setWindowLock(0x1);
                    break;
            }
        }
    }

    @Override
    public int getWindowLock() {
        // 计时2s 若检测到状态反馈信号 FLZCU_WindowInhibitSts = 0x0:Permit
//        int status = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEDOOR_WINDOWLOCKSTATUS);
        int status = quickManager.getWindowLock();
        Log.d(TAG, "getWindowLock: 获取车窗锁状态:" + status);
        if (status == CarQuickControl.GetWindowLockSts.PERMIT) {
            status = CarQuickControl.ButtonSts.OFF;
        } else if (status == CarQuickControl.GetWindowLockSts.INHIBIT) {
            status = CarQuickControl.ButtonSts.ON;
        } else {
            status = Integer.MIN_VALUE;
        }
        return status;
    }

    @Override
    public void setLockCarSunRoofShadeClose(int state) {
//        前置条件（a）
//        1. 电源模式(0x49D:FLZCU_9_PowerMode=0x1:Comfortable/0x2:ON) ；
//        触发条件（a）
//        a. 点击关闭锁车遮阳帘设置项；
//        b. 点击开启锁车遮阳帘设置项
//        执行输出（a&b）
//        a. 若触发条件 a，ICC 发送 ICC_LockCarSunRoofShadeCloseSw= 0x2:OFF；
//        b. 若触发条件 b，ICC 发送 ICC_LockCarSunRoofShadeCloseSw= 0x1:ON；
//        备注：
//        1. 出厂默认状态要求：出厂默认锁车关闭前后遮阳帘功能为disable状态；
//        2. 记忆要求：对于设置结果要能下电/软件重启情况下存储记忆（存EE）
//        3. FRZCU同时接收0x4D4，并记忆设置状态。
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log .d(TAG, "setLockCarSunRoofShadeClose: 设置锁车遮阳帘状态: " + state);
            switch (state) {
                case 0:
                    // ICC 发送 ICC_LockCarSunRoofShadeCloseSw= 0x2:OFF；
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_LOCKCARSUNROOFSHADECLOSE_SET, 0x2);
                    quickManager.setLockCarSunRoofShadeClose(0x2);
                    break;
                case 1:
                    // ICC 发送 ICC_LockCarSunRoofShadeCloseSw= 0x1:ON；
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_LOCKCARSUNROOFSHADECLOSE_SET, 0x1);
                    quickManager.setLockCarSunRoofShadeClose(0x1);
                    break;
            }
        }
    }

    @Override
    public int getLockCarSunRoofShadeClose() {
//        int signalVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_LOCKCARSUNROOFSHADECLOSE);
        int signalVal = quickManager.getLockCarSunRoofShadeClose();
        int status = 0;
        if (signalVal == 0x2) {
            status = 0;
        } else if (signalVal == 0x1) {
            status = 1;
        }
        Log.d(TAG, "getLockCarSunRoofShadeClose: 获取锁车遮阳帘状态:" + status);
//        Prefs.put(PrefsConst.Q_LOCK_CAR_SUNROOF_SHADE, status);
        return status;
    }

    @Override
    public void setDefenseReminder(int status) {
        //显⽰名称：设防提⽰ 开关设置：灯光、灯光和喇叭 开关默认值：灯光和喇叭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. ⽤⼾在⼤屏上设置设防提⽰为灯光提⽰；
        //2. ⽤⼾在⼤屏上设置设防提⽰为灯光和喇叭；
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧，ICC_lockSetSwitchSts = 0x2:Only Lighting，然后发送
        //0x0:Not Active 给 FLZCU，⼤屏设防提⽰显⽰为灯光提⽰，计时 2s 若检测到状态反馈信号
        //FLZCU_AlarmWarnSetSW = 0x2:Only Lighting，则⼤屏设防提⽰显⽰保持为灯光提⽰状态， 否则
        //回弹；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_lockSetSwitchSts = 0x3：Sound and Lighting，然后发
        //送 0x0:Not Active 给 FLZCU，⼤屏设防提⽰显⽰为灯光和喇叭，计时 2s 若检测到状态反馈信号
        //FLZCU_AlarmWarnSetSW = 0x3：Sound and Lighting，则⼤屏设防提⽰显⽰保持为灯光和喇叭
        //状态，否则回弹；

        // ICC -> FLZCU 信号名：ICC_lockSetSwitchSts
        // 0x0:Not Active
        // 0x1:OFF
        // 0x2:Only Lighting
        // 0x3:Sound and Lighting
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_UNLOCKALARMWARN_SET, 0x2);
                quickManager.setDefenseReminder(0x2);
            } else if (status == 1) {
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_UNLOCKALARMWARN_SET, 0x3);
                quickManager.setDefenseReminder(0x3);
            }
        }
    }

    @Override
    public int getDefenseReminder() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_UNLOCKALARMWARN);
        int signlVal = quickManager.getDefenseReminder();
        Log.d(TAG, "getDefenseReminder: 获取设防提⽰:" + signlVal);
        int status = 0;
        if (signlVal == 0x2) {
            status = 0;
        } else if (signlVal == 0x3) {
            status = 1;
        }
//        Prefs.put(PrefsConst.Q_DEFENSE_REMINDER, status);
//        return Prefs.rtnStatus(PrefsConst.C_DEFENSE_REMINDER, status, PrefsConst.DefaultValue.C_DEFENSE_REMINDER);
        return status;
    }

    @Override
    public void setLeftChildLock(int status) {
        //显⽰名称：左⼉童锁 开关设置：解锁/闭锁 开关默认值：解锁
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-⻋辆控制界⾯提供左⼉童锁选项：⽤⼾点击左⼉童锁开关按键闭锁；
        //2. 在中控屏⻋辆设置-⻋辆控制界⾯提供左⼉童锁选项：⽤⼾点击左⼉童锁开关按键解锁；
        //执⾏输出（1||2）
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_ChildLockSW = 0x2:ChildLock_RL 给FLZCU，接着发送
        //0x0:Not Active，⼤屏左⼉童锁显⽰为闭锁，FLZCU 控制左后⼉童锁闭锁，计时 2s 若接收到状态反
        //馈信号 RL_ChildrenProtectSwitch =0x0:Locked，则左⼉童锁开关保持闭锁（开关⾼亮），否则
        //开关弹回⾮⾼亮；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ChildLockSW = 0x2:ChildLock_RL 给 FLZCU，接着发送
        //0x0:Not Active，⼤屏左⼉童锁显⽰为解锁，FZCU控制左后⼉童锁解锁，计时2s 若接收到状态反馈
        //信号 RL_ChildrenProtectSwitch=0x1:Unlocked，则左后⼉童锁开关保持解锁（开关⾮⾼亮），否
        //则开关弹回⾼亮；

        // ICC -> FLZCU/FRZCU 信号名：ICC_ChildLockSW
        // 0x0:Not Active
        // 0x1:Window
        // 0x2:ChildLock_RL
        // 0x3:ChildLock_RR

        // FLZCU -> ICC 信号名：RL_ChildrenProtectSwitch
        // 0x0:Locked
        // 0x1:Unlocked
        // 0x2:Superlocked

        // FRZCU -> ICC 信号名：RR_ChildrenProtectSwitch
        // 0x0:Locked
        // 0x1:Unlocked
        // 0x2:Superlocked
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setLeftChildLock: 设置左⼉童锁:" + status);
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_CHILDLOCKSWITCH_SET, 0x2);
            quickManager.setLeftChildLock(0x2);
        }
    }

    @Override
    public int getLeftChildLock() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEDOOR_CHILDLOCK_REARLEFT);
        int signlVal = quickManager.getLeftChildLock();
        Log.d(TAG, "getLeftChildLock: 获取左⼉童锁:" + signlVal);
        int status = 0;
        if (signlVal == 0x0) {
            status = 1;
        } else if (signlVal == 0x1) {
            status = 0;
        }
//        Prefs.put(PrefsConst.Q_LEFT_CHILD_LOCK, status);
//        return Prefs.rtnStatus(PrefsConst.C_LEFT_CHILD_LOCK, status, PrefsConst.DefaultValue.C_LEFT_CHILD_LOCK);
        return status;
    }

    @Override
    public void setRightChildLock(int status) {
        //显⽰名称：右⼉童锁 开关设置：解锁/闭锁 开关默认值：解锁
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击右⼉童锁开关按键闭锁；
        //2. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击右⼉童锁开关按键解锁；
        //执⾏输出（1||2）
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_ChildLockSW = 0x3:ChildLock_RL 给 FRZCU，接着发送
        //0x0:Not Active，⼤屏右⼉童锁显⽰为闭锁，FRZCU 控制右后⼉童锁闭锁，计时 2s 若接收到状态
        //反馈信号 RR_ChildrenProtectSwitch =0x0:Locked，则右⼉童锁开关保持闭锁（开关⾼亮），否
        //则开关弹回⾮⾼亮；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ChildLockSW = 0x3:ChildLock_RL 给 FRZCU，接着发送
        //0x0:Not Active，⼤屏右⼉童锁显⽰为解锁，FRZCU控制右后⼉童锁解锁，计时2s 若接收到状态反
        //馈信号 RR_ChildrenProtectSwitch=0x1:Unlocked，则右⼉童锁开关保持解锁（开关⾮⾼亮），
        //否则开关弹回⾼亮；


        if (MsgUtil.getInstance().supportPowerMode()) {
             Log.d(TAG, "setRightChildLock: 设置右⼉童锁:" + status);
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_CHILDLOCKSWITCH_SET, 0x3);
            quickManager.setRightChildLock(0x3);
        }
    }

    @Override
    public int getRightChildLock() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEDOOR_CHILDLOCK_REARRIGHT);
        int signlVal = quickManager.getRightChildLock();
        Log.d(TAG, "getRightChildLock: 获取右⼉童锁:" + signlVal);
        int status = 0;
        if (signlVal == 0x0) {
            status = 1;
        } else if (signlVal == 0x1) {
            status = 0;
        }
//        Prefs.put(PrefsConst.Q_RIGHT_CHILD_LOCK, status);
//        return Prefs.rtnStatus(PrefsConst.C_RIGHT_CHILD_LOCK, status, PrefsConst.DefaultValue.C_RIGHT_CHILD_LOCK);
        return status;
    }

    @Override
    public void setAutomaticLocking(int status) {
        //显⽰名称：⾃动落锁 开关设置：开启/关闭 开关默认值：开启
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在⼤屏上打开⾃动落锁开关
        //2. 在⼤屏上关闭⾃动落锁开关
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_AutolockSts =0x1:Autolock mode 给 FLZCU，接着发 送
        //0x0:Not active ，FLZCU 控制⾃动落锁功能开启，计时 2s 若接收到状态反馈 信号
        //FLZCU_AutolockSts=0x1:Autolock mode，则⾃动落锁开关保持开启， 否则开关弹回关闭；
        //2. 若触发条件为 2，ICC 发送 ICC_AutolockSts=0x2:Not autolock mode 给 FLZCU，FLZCU 控制⾃
        //动落锁功能关闭, 计时 2s 若接收到状态反馈信号 FLZCU_AutolockSts=0x0:Not autolock mode，
        //则⾃动落锁开关保持关闭，否则开关弹回开启；
        //3. 若未进⾏开关设置，⾃动落锁开关接收FLZCU_AutolockSts对应信号显⽰对应开关状态，。

        // ICC -> FLZCU 信号名：ICC_AutolockSts
        // 0x0:Not Active
        // 0x1:Autolock mode
        // 0x2:Not autolock mode
        // 0x3:Not Used

        // FLZCU -> ICC 信号名：FLZCU_AutolockSts
        // 0x0:Not autolock mode
        // 0x1:Autolock mode
        if (MsgUtil.getInstance().supportPowerMode()) {
             Log.d(TAG, "setAutomaticLocking: 设置⾃动落锁:" + status);
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AUTOMATICLOCKED_SET, 0x2);
                    quickManager.setAutomaticLocking(0x2);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AUTOMATICLOCKED_SET, 0x1);
                    quickManager.setAutomaticLocking(0x1);
                    break;
            }
        }
    }

    @Override
    public int getAutomaticLocking() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_AUTOMATICLOCKED);
        int signlVal = quickManager.getAutomaticLocking();
        Log.d(TAG, "getAutomaticLocking: 获取⾃动落锁:" + signlVal);
        int status = 0;
        if (signlVal == 0x0) {
            status = 0;
        } else if (signlVal == 0x1) {
            status = 1;
        }
//        Prefs.put(PrefsConst.Q_AUTOMATIC_LOCKING, status);
//        return Prefs.rtnStatus(PrefsConst.C_AUTOMATIC_LOCKING, status, PrefsConst.DefaultValue.C_AUTOMATIC_LOCKING);
        return status;
    }

    @Override
    public void setAutomaticParkingUnlock(int status) {
        //显⽰名称：驻⻋⾃动解锁 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件： （1||2）
        //1. ⽤⼾在⼤屏上设置驻⻋⾃动解锁功能开关开启；
        //2. ⽤⼾在⼤屏上设置驻⻋⾃动解锁功能开关关闭；
        //执⾏输出：
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_ParkUnlockEnable=0x2:Enable 给 FLZCU，之后发送
        //0x0:Not Active，计时 2s 若检测到状态反馈信号 FLZCU_ParkUnlockEnableFb=0x1:Enable，则开
        //关保持开启，否则开关弹回关闭；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ParkUnlockEnable=0x1:Disable 给 FLZCU，之后发送
        //0x0:Not Active，计时 2s 若检测到状态反馈信号 FLZCU_ParkUnlockEnableFb= 0x0:Disable，则
        //开关保持关闭，否则开关弹回开启；

        // ICC -> FLZCU 信号名：ICC_ParkUnlockEnable
        // 0x0:Not Active
        // 0x1:Disable
        // 0x2:Enable
        // 0x3:Reserved

        // FLZCU -> ICC 信号名：FLZCU_ParkUnlockEnableFb
        // 0x0:Disable
        // 0x1:Enable
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AUTOMATICUNLOCKED_SET, 0x1);
                    quickManager.setAutomaticParkingUnlock(0x1);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AUTOMATICUNLOCKED_SET, 0x2);
                    quickManager.setAutomaticParkingUnlock(0x2);
                    break;
            }
        }
    }

    @Override
    public int getAutomaticParkingUnlock() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_AUTOMATICUNLOCKED);
        int signlVal = quickManager.getAutomaticParkingUnlock();
        int status = 0;
        if (signlVal == 0x0) {
            status = 0;
        } else if (signlVal == 0x1) {
            status = 1;
        }
//        Prefs.put(PrefsConst.Q_AUTOMATIC_PARKING_UNLOCK, status);
//        return Prefs.rtnStatus(PrefsConst.C_AUTOMATIC_PARKING_UNLOCK, status, PrefsConst.DefaultValue.C_AUTOMATIC_PARKING_UNLOCK);
        return status;
    }

    @Override
    public void setSunshade(int state) {
        // 前置条件（a|(a&b)）
        //a. 电源模式(0x49D:FLZCU_9_PowerMode=0x1:Comfortable/0x2:ON)
        //b. 前遮阳帘处于关闭或其他状态时，(0x4D2: FRZCU_FShadSts=0x1:close或0x5:otherposition)。
        if (MsgUtil.getInstance().supportPowerMode()) {

            // 获取遮阳帘的状态 "0x0:Not Active
            //0x1:Autoopen
            //0x2:AutoClose
            //0x3:Stop"
            if (state == 0) {
                // 前排 0x0:Not Active
                //0x1:Close
                //0x2:Open
                //0x3:Closing
                //0x4:Opening
                //0x5:Other Position
                //0x6~0xF:Reserved
//                int fstatus = MsgUtil.getInstance().getSignlVal(SiganlConstans.FLZCU_RShadSts);
                int fstatus = quickManager.getSunshadeFront();
                Log.d(TAG, "setSunshade: 设置前遮阳帘的状态:" + fstatus);
                if (fstatus == 0x1 || fstatus == 0x5) {
                    // 打开 Autoopen
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.ICC_FShadReq, 0x1);
                  quickManager.setSunshadeFront(0x1);
                } else if (fstatus == 0x3 || fstatus == 0x4) {
                    // stop
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.ICC_FShadReq, 0x3);
                    quickManager.setSunshadeFront(0x3);
                } else if (fstatus == 0x2) {
                    // ICC_FShadReq=0x2:Autoclose至FRZCU;
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.ICC_FShadReq, 0x2);
                    quickManager.setSunshadeFront(0x2);
                } else if (fstatus == 0x0) {
                    // ICC_FShadReq=0x0:Not Active
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.ICC_FShadReq, 0x0);
                    quickManager.setSunshadeFront(0x0);
                }
            } else if (state == 1) {
                // 后排
//                int rstatus = MsgUtil.getInstance().getSignlVal(SiganlConstans.FRZCU_FShadSts);
                int rstatus = quickManager.getSunshadeRear();
                Log.d(TAG, "setSunshade: 设置后遮阳帘的状态:" + rstatus);
                if (rstatus == 0x1 || rstatus == 0x5) {
                    // 打开 Autoopen
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.ICC_RShadReq, 0x1);
                    quickManager.setSunshadeRear(0x1);
                } else if (rstatus == 0x3 || rstatus == 0x4) {
                    // stop
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.ICC_RShadReq, 0x3);
                    quickManager.setSunshadeRear(0x3);
                } else if (rstatus == 0x2) {
                    // ICC_FShadReq=0x2:Autoclose至FRZCU;
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.ICC_RShadReq, 0x2);
                    quickManager.setSunshadeRear(0x2);
                } else if (rstatus == 0x0) {
                    // ICC_FShadReq=0x0:Not Active
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.ICC_RShadReq, 0x0);
                    quickManager.setSunshadeRear(0x0);
                }
            }


        }
    }

    @Override
    public int getSunshade() {
        // TODO 获取遮阳帘
//        int fstatus = MsgUtil.getInstance().getSignlVal(SiganlConstans.FLZCU_RShadSts);
        int fstatus = quickManager.getSunshadeFront();
//        int rstatus = MsgUtil.getInstance().getSignlVal(SiganlConstans.FRZCU_FShadSts);
        int rstatus = quickManager.getSunshadeRear();
        return -1;
    }

    @Override
    public void setSunshadeFront(int status) {
//        int fstatus = quickManager.getSunshadeFront();
//        Log.d(TAG, "setSunshade: 设置前遮阳帘的状态:" + fstatus);
//        if (fstatus == 0x1 || fstatus == 0x5) {
//            quickManager.setSunshadeFront(0x1);
//        } else if (fstatus == 0x3 || fstatus == 0x4) {
//            quickManager.setSunshadeFront(0x3);
//        } else if (fstatus == 0x2) {
//            quickManager.setSunshadeFront(0x2);
//        } else if (fstatus == 0x0) {
//            quickManager.setSunshadeFront(0x0);
//        }
        quickManager.setSunshadeFront(status == 1 ? 0x1 : 0x2);
    }

    @Override
    public int getSunshadeFront() {
        return quickManager.getSunshadeFront();
    }

    @Override
    public void setSunshadeRear(int status) {
//        int rstatus = quickManager.getSunshadeRear();
//        Log.d(TAG, "setSunshade: 设置后遮阳帘的状态:" + rstatus);
//        if (rstatus == 0x1 || rstatus == 0x5) {
//            quickManager.setSunshadeRear(0x1);
//        } else if (rstatus == 0x3 || rstatus == 0x4) {
//            quickManager.setSunshadeRear(0x3);
//        } else if (rstatus == 0x2) {
//            quickManager.setSunshadeRear(0x2);
//        } else if (rstatus == 0x0) {
//            quickManager.setSunshadeRear(0x0);
//        }
        quickManager.setSunshadeRear(status == 1 ? 0x1 : 0x2);
    }

    @Override
    public int getSunshadeRear() {
        return quickManager.getSunshadeRear();
    }

    @Override
    public void setAutoTail(int state) {
        // ICC_SpoilerCtrlCmd 设置电动尾翼 BODYINFO_SPOILERSWITCH_SET
        // "0x0:Not Active
        //0x1:Auto
        //0x2:Open Level1
        //0x3:Open Level2
        //0x4:Close
        //0x5~0x7:Reserved"
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
                    quickManager.setSpoilerSwitch(0x1);
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_SPOILERSWITCH_SET, 0x1);
                    break;
                case 1:
                    quickManager.setSpoilerSwitch(0x2);
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_SPOILERSWITCH_SET, 0x2);
                    break;
                case 2:
                    quickManager.setSpoilerSwitch(0x3);
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_SPOILERSWITCH_SET, 0x3);
                    break;
                case 3:
                    quickManager.setSpoilerSwitch(0x4);
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_SPOILERSWITCH_SET, 0x4);
                    break;
            }
        }
        Prefs.put(PrefsConst.Q_AUTO_TAIL, state);
    }

    @Override
    public int getAutoTail() {
        // 获取电动尾翼
        //0x0:Not Active
        //0x1:Auto
        //0x2:Open Level1
        //0x3:Open Level2
        //0x4:Close
        //0x5~0x7:Reserved"	电动尾翼控制设置反馈
//        int status = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_SPOILERSWITCH);
        int status = quickManager.getSpoilerSwitch();
        if (status == 0x1) {
            status = 0;
        } else if (status == 0x2) {
            status = 1;
        } else if (status == 0x3) {
            status = 2;
        } else if (status == 0x4) {
            status = 3;
        }
        Log.d(TAG, "电动尾翼控制设置反馈getAutoTail: " + status);
        return Prefs.rtnStatus(PrefsConst.Q_AUTO_TAIL, status, 0);
    }

    @Override
    public void setSkyWindow(int state) {
        // 前置条件：VehicleDoor/WindowLockStatus/Set
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {

        }
    }

    @Override
    public int getSkyWindow() {
        // todo 获取天窗 与需求确认，无此需求
        return 0;
    }

    @Override
    public void setApproachingUnlock(int state) {
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
                    // 大屏显示感应靠近解锁开关为关闭（非高亮），ICC 连续发送 三帧 ICC_UIROpenSetCmd=0x2: Off，后发送 0x0:Not Active 给 FLZCU，
                    // 两 秒后若收到的状态反馈信号 FLZCU_UIROpenStas= 0x0:OFF，则开关状态保持 不变，若收到 0x1:ON（反馈信号与设置信号值不一致）则开关状态弹回开启
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_APPROVEUNLOCK_SET, 0x2);
                    quickManager.setApproachingUnlock(0x2);
                    break;
                case 1:
                    // 大屏显示感应靠近解锁开关为开启（高亮），ICC 连续发送三 帧 ICC_UIROpenSetCmd=0x1: On，然后发送 0x0:Not Active 给 FLZCU，
                    // 两 秒后判断收到的状态反馈信号，若 FLZCU_UIROpenStas= 0x1:ON，则开关状 态保持不变，若收到 0x0:OFF（反馈信号与设置信号值不一致）则开关状态弹回关闭
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_APPROVEUNLOCK_SET, 0x1);
                    quickManager.setApproachingUnlock(0x1);
                    break;
            }
        }
    }

    @Override
    public int getApproachingUnlock() {
        //  FLZCU_UIROpenStas= 0x1:ON FLZCU_UIROpenStas= 0x0:OFF，
//        int status = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_APPROVEUNLOCK);
        int status = quickManager.getApproachingUnlock();
        if (status == 0x0) {
            status = 0;
        }
        if (status == 0x1) {
            status = 1;
        }
        return Prefs.rtnStatus(PrefsConst.Q_APPROACHING_UNLOCK, status, PrefsConst.DefaultValue.Q_APPROACHING_UNLOCK);
    }

    @Override
    public void setDepartureLocking(int state) {
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；

        // 若触发条件为 1，ICC 连续发送三帧 ICC_WALOpenSetCmd=0x1: On 给 FLZCU，之后发送0x0:Not Active，计时 2s
        // 若检测到状态反馈信号 FLZCU_WALOpenStas=0x1:ON，则开关保持开启，否则开关弹回关闭；

        // 若触发条件为 2，ICC 连续发送三帧 ICC_WALOpenSetCmd=0x2: Off 给 FLZCU 之后发送 0x0:NotActive，计时 2s
        // 若检测到状态反馈信号 FLZCU_WALOpenStas =0x0:OFF，则开关保持关闭，否则开关弹回开启；
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AWAYLOCK_SET, 0x2);
                    quickManager.setDepartureLocking(0x2);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AWAYLOCK_SET, 0x1);
                    quickManager.setDepartureLocking(0x1);
                    break;
            }
        }
    }

    @Override
    public int getDepartureLocking() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_AWAYLOCK);
        int signlVal = quickManager.getDepartureLocking();
        if (signlVal == 0x0) {
            signlVal = 0;
        } else if (signlVal == 0x1) {
            signlVal = 1;
        }
        return Prefs.rtnStatus(PrefsConst.Q_DEPARTURE_LOCKING, signlVal, PrefsConst.DefaultValue.Q_DEPARTURE_LOCKING);
    }

    @Override
    public void setLockAutoRaiseWindow(int status) {
        //显⽰名称：锁⻋⾃动升窗 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-点击开
        //2. 在中控屏⻋辆设置-点击关
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC 发送 ICC_LockCarWinCloseSw = 0x1：ON； ，然后发送
        //0x0:Not Active 给 FLZCU，⼤屏锁⻋升窗显⽰为开，计时 2s 若检测到状态反馈信号
        //FLZCU_LockCarWinCloseFb = 0x0:Enable，则⼤屏锁⻋升窗显⽰保持为开状态， 否则弹回关状
        //态；
        //2. 若触发条件为 2，ICC 连续发送三帧ICC_LockCarWinCloseSw= 0x2：OFF，然后发送 0x0:Not
        //Active 给 FLZCU，⼤屏锁⻋升窗显⽰为关，计时 2s 若检测到状态反馈信号
        //FLZCU_LockCarWinCloseFb = 0x1:Disable，则⼤屏锁⻋升窗显⽰保持为关状态，否则弹回开状
        //态；

        // ICC -> FLZCU 信号名：ICC_LockCarWinCloseSw
        // 0x0:Not Active
        // 0x1:ON
        // 0x2:OFF
        // 0x3:Reserved

        // FLZCU -> ICC 信号名：FLZCU_LockCarWinCloseFb
        // 0x0:Enable
        // 0x1:Disable
        Log.d(TAG, "setLockAutoRaiseWindow: 设置锁⻋⾃动升窗开关状态:" + status);
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_LOCKCARWINDOWCLOSE_SET, 0x2);
                    quickManager.setLockAutoRaiseWindow(CarQuickControl.SetLockAutoRaiseWindowSts.OFF);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_LOCKCARWINDOWCLOSE_SET, 0x1);
                    quickManager.setLockAutoRaiseWindow(CarQuickControl.SetLockAutoRaiseWindowSts.ON);
                    break;
            }
        }
    }

    @Override
    public int getLockAutoRaiseWindow() {
        int signlVal = quickManager.getLockAutoRaiseWindow();
        int status = 0;
        if (signlVal == CarQuickControl.GetLockAutoRaiseWindowSts.DISABLE) {
            status = CarQuickControl.ButtonSts.OFF;
        } else if (signlVal == CarQuickControl.GetLockAutoRaiseWindowSts.ENABLE) {
            status = CarQuickControl.ButtonSts.ON;
        }
        Log.d(TAG, "getLockAutoRaiseWindow: 获取锁⻋⾃动升窗开关状态:" + status);
        Prefs.put(PrefsConst.Q_LOCK_AUTO_RAISE_WINDOW, status);
        return status;
    }

    @Override
    public void setAutoRearMirrorFold(int state) {
        //显⽰名称：外后视镜⾃动折叠 开关设置：开启/关闭 开关默认值：开启
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 点击⼤屏上的软开关使外后视镜⾃动折叠功能开启；
        //2. 点击⼤屏上的软开关使外后视镜⾃动折叠功能关闭；
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_AutoFoldSts=0x1:Autofold mode 给 FLZCU， FLZCU控
        //制外后视镜⾃动折叠功能开启，之后发送 0x0:Not active，计时 2s 若检测到 状态反馈信号
        //FLZCU_AutoFoldSts=0x1:Autofold mode，则开关保持开启， 否则开关弹回关闭；
        //2. 若触发条件为 2，ICC 发送 ICC_AutoFoldSts=0x2:Not autofold mode 给 FLZCU， FLZCU控制外
        //后视镜⾃动折叠功能关闭，之后发送 0x0:Not active，计时 2s 若检测到状态 反馈信号
        //FLZCU_AutoFoldSts =0x0:Not autofold mode，则开关保持关闭， 否则开关弹回开启；

        // ICC -> FLZCU 信号名：IIC_AutoFoldSts
        // 0x0：Not Active
        // 0x1：Autofold mode
        // 0x2：Not autofold mode
        // 0x3：Not used

        // FLZCU -> ICC 信号名：FLZCU_AutoFoldSts
        // 0x0：Not Autofold
        // 0x1：Autofold
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWAUTOFOLD_SET, 0x2);
                    quickManager.setAutoRearMirrorFold(0x2);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWAUTOFOLD_SET, 0x1);
                    quickManager.setAutoRearMirrorFold(0x1);
                    break;
            }
        }
    }

    @Override
    public int getAutoRearMirrorFold() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_REARVIEWAUTOFOLD);
        int signlVal = quickManager.getAutoRearMirrorFold();
        int status = 0;
        if (signlVal == 0x0) {
            status = 0;
        } else if (signlVal == 0x1) {
            status = 1;
        }
        Prefs.put(PrefsConst.Q_AUTO_REAR_MIRROR_FOLD, status);
//        return Prefs.rtnStatus(PrefsConst.Q_AUTO_REAR_MIRROR_FOLD, signlVal, PrefsConst.DefaultValue.Q_AUTO_REAR_MIRROR_FOLD);
        return status;
    }


    @Override
    public void setAutoHotRearMirror(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            //若触发条件为 1， ICC 连续发送三帧 ICC_AutoHeatingset =0x1:ON 给 FLZCU， FLZCU 控制开启雨天自动加热外后视镜功能，接着发送 0x0:Not Active，计时 2s 若接收到状态 反馈信号FLZCU_AutoHeatingFb = 0x1:Open，则雨天自动加热外后视镜开关保持开启，否则开关弹回关闭；2. 若触发条件为 2，ICC 连续发送三帧 ICC_AutoHeatingset=0x2:OFF 给FLZCU， FLZCU 控制关闭 雨天自动加热外后视镜功能，接着发送 0x0:Not Active，计时 2s 若接收到状态 反馈信号FLZCU_AutoHeatingFb = 0x0:Close，则雨天自动加热外后视镜开关保持关闭，否则开关弹回开启；
            switch (state) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWRAINAUTOHEAT_SET, 0x2);
                    quickManager.setAutoHotRearMirror(0x2);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWRAINAUTOHEAT_SET, 0x1);
                    quickManager.setAutoHotRearMirror(0x1);
                    break;
            }
        }
    }

    @Override
    public int getAutoHotRearMirror() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_REARVIEWRAINAUTOHEAT);
        int signlVal = quickManager.getAutoHotRearMirror();
        int status  =0;
        if (signlVal == 0x0) {
            status = 0;
        } else if (signlVal == 0x1) {
            status = 1;
        }
        Prefs.put(PrefsConst.Q_AUTO_HOT_REAR_MIRROR, status);
//        return Prefs.rtnStatus(PrefsConst.Q_AUTO_HOT_REAR_MIRROR, signlVal, PrefsConst.DefaultValue.Q_AUTO_HOT_REAR_MIRROR);
        return status;
    }

    @Override
    public void setSeatPortable(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.AC_SEATEASYACCESS_SET, 0x2);
                    quickManager.setSeatPortable(0x2);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.AC_SEATEASYACCESS_SET, 0x1);
                    quickManager.setSeatPortable(0x1);
                    break;
            }
        }
    }

    @Override
    public int getSeatPortable() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.AC_SEATEASYACCESS);
        int signlVal = quickManager.getSeatPortable();
        if (signlVal == 0x1) {
            signlVal = 1;
        } else if (signlVal == 0x2) {
            signlVal = 0;
        }
        return Prefs.rtnStatus(PrefsConst.Q_SEAT_PORTABLE, signlVal, PrefsConst.DefaultValue.Q_SEAT_PORTABLE);
    }


    @Override
    public void sendMirrorAdjuse() {
        // ICC 发送 ICC_MultiplexSignalStatusSet = 0x1:Mirror 给 MFS；
        if (MsgUtil.getInstance().supportPowerMode()) {
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_MFSSWITCHMODE_SET, 0x1);
            quickManager.setRearMirrorAdjust(0x1);
        }
        Log.d(TAG, "sendMirrorAdjuse: 发送后视镜调节命令:" + 0x1);
    }

    @Override
    public int getMirrorAdjuseStatus() {
        // ICC 收到方向盘自定义开关状态反馈信号 MFS_3_MultiplexSignalStatus= 0x1 :Mirror 时，大屏弹出方向盘调节外后视镜操作指引弹窗
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_MFSSWITCHMODE);
        int signlVal = quickManager.getRearMirrorAdjust();
        Log.d(TAG, "sendMirrorAdjuse: 获取后视镜调节:" + signlVal);
        if (signlVal == 0x1) {
            // 弹出
            signlVal = 1;
        } else {
            signlVal = 0;
        }
        return signlVal;
    }

    /**
     * 倒车时后视镜自动调节
     *
     * @param state
     */
    @Override
    public void setBackAutoRearMirrorAdjust(int state) {
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
                    // 1.若触发条件 1，ICC 连续发送三帧 ICC_ReverseExtMirrorSts = 0x1: OFF 给 FLZCU，之后发送 0x0:Not active，
                    // 计时 2s 若检测到状态反馈信号 FLZCU_RvsExtMirrFbSts =0x0:OFF，则设置项保持关闭，否则设置项根据收到 的真实状态反馈显示
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWFLIP_SET, 0x1);
                    quickManager.setBackRearAdjust(0x1);
                    break;
                case 1:
                    // 4.	若触发条件 4，ICC 连续发送三帧 ICC_ReverseExtMirrorSts = 0x4:Both sides 给 FLZCU，之后发送 0x0:Not active，、
                    // 计时 2s 若检测到状态反馈信号 FLZCU_RvsExtMirrFbSts =0x3:Both Sides，则设置项保持关闭，否则设置项 根据收到的真实状态反馈显示
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWFLIP_SET, 0x4);
                    quickManager.setBackRearAdjust(0x4);
                    break;
                case 2:
                    // 2.	若触发条件 2，ICC 连续发送三帧 ICC_ReverseExtMirrorSts =0x3:Only left  side 给FLZCU，之后发送 0x0:Not active，
                    // 计时 2s 若检测到状态反馈信号 FLZCU_RvsExtMirrFbSts =0x2:Only Left Side，则设置项保持关闭，否则设置 项根据收到的真实状态反馈显示
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWFLIP_SET, 0x3);
                    quickManager.setBackRearAdjust(0x3);
                    break;
                case 3:
                    // 3.	若触发条件 3，ICC 连续发送三帧 ICC_ReverseExtMirrorSts =0x2:Only right  side 给 FLZCU，之后发送 0x0:Not active，
                    // 计时 2s 若检测到状态反馈信号 FLZCU_RvsExtMirrFbSts =0x1:Only Right Side，则设置项保持关闭，否则设 置项根据收到的真实状态反馈显示
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_REARVIEWFLIP_SET, 0x2);
                    quickManager.setBackRearAdjust(0x2);
                    break;
            }
        }

    }

    @Override
    public int getBackAutoRearMirrorAdjust() {
//        int status = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_REARVIEWFLIP);
        int status = quickManager.getBackRearAdjust();
        // 0 == OFF
        // 1 == Right
        // 2 == Left
        // 3 == Both
        if (status == 0x0) {
            status = 0;
        } else if (status == 0x3) {
            status = 1;
        } else if (status == 0x2) {
            status = 2;
        } else if (status == 0x1) {
            status = 3;
        }
        Prefs.put(PrefsConst.Q_BACK_AUTO_REAR_MIRROR_ADJUSE, status);
//        return Prefs.rtnStatus(PrefsConst.Q_BACK_AUTO_REAR_MIRROR_ADJUSE, status, PrefsConst.DefaultValue.Q_BACK_AUTO_REAR_MIRROR_ADJUSE);
        return status;
    }

    // 车辆下电
    @Override
    public void setVehiclePowerOff(int state) {
        if (state == 1) {
            //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
            if (MsgUtil.getInstance().supportPowerMode()) {
                // 发送下线 BODYINFO_POWEROFF_SET
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_POWEROFF_SET, 0x1);
                quickManager.setVehiclePowerOff(0x1);
                Log.d(TAG, "setVehiclePowerOff: 车辆下电 发送下线命令:" + 0x1);
            }
        }
    }

    @Override
    public int getRefuelSmallDoor() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.EXTENDEDRANGE_OILLIDSWITCH);
        int signlVal = quickManager.getOilLidSwitch();
        Log.d(TAG, "getRefuelSmallDoor: 获取加油小门:" + signlVal);
        if (signlVal == 0x0) {
            signlVal = 0;
        } else if (signlVal == 0x1) {
            signlVal = 1;
        }
        return Prefs.rtnStatus(PrefsConst.Q_REFUEL_SMALL_DOOR, signlVal, PrefsConst.DefaultValue.Q_REFUEL_SMALL_DOOR);
    }

    @Override
    public void setRefuelSmallDoor(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.EXTENDEDRANGE_OILLIDSWITCH_SET, state);
            quickManager.setOilLidSwitch(state);
        }
        Log.d(TAG, "setRefuelSmallDoor: 设置加油小门:" + state);
    }

    // 后排屏锁设置
    @Override
    public void setRearScreenControl(int status) {
        Log.d(TAG, "setRearScreenControl: 后屏控制锁: " + status);
        if (status == 1) { // 开启
            quickManager.setRearScreenControl(0x1);
        }else if (status == 0) { // 关闭
            quickManager.setRearScreenControl(0x0);
        }
    }

    // 后排屏锁状态获取
    @Override
    public int getRearScreenControl() {
        int signlVal = quickManager.getRearScreenControl();
        int status = 0;
        if (signlVal == 0x1) {
            status = 1;
        }else if (signlVal == 0x0) {
            status = 0;
        }
        return status;
    }

    public int getVehiclePowerOff() {
//        int status = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_REARVIEWFLIP);
        int status = quickManager.getVehiclePowerOff();
        return Prefs.rtnStatus(PrefsConst.Q_VECHICE_POWER_OFF, status, PrefsConst.DefaultValue.Q_VECHICE_POWER_OFF);
    }

    /**
     * 设置雨刮器灵敏度
     *
     * @param status
     */
    @Override
    public void setWiperSens(int status) {
        //显⽰名称：⾬刮灵敏度 开关设置： 低、标准、⾼、最⾼ 开关默认值：⾼
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2||3||4）
        //1. 在⼤屏上点击⾬刮灵敏调节设置项为 低；
        //2. 在⼤屏上点击⾬刮灵敏调节设置项为 标准；
        //3. 在⼤屏上点击⾬刮灵敏调节设置项为 ⾼；
        //4. 在⼤屏上点击⾬刮灵敏调节设置项为 最⾼；
        //执⾏输出（1||2||3||4）
        //1. 若触发条件 1，ICC 连续发送三帧 ICC_WiprSnvty = 0x1:Level 1，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 低，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x1:Level 1，则⾬刮灵敏调节设置 项保持 level1，否则开关根据收
        //到的信号显⽰对应状态；
        //2. 若触发条件 2，ICC 连续发送三帧 ICC_WiprSnvty = 0x2:Level 2 ，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 标准，计时2s若检测到状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x2:Level 2，则⾬刮灵敏调节设置 项保持 level2，否则开关根据收
        //到的信号显⽰对应状态；
        //3. 若触发条件 3，ICC 连续发送三帧 ICC_WiprSnvty = 0x3:Level 3，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 ⾼，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x3:Level 3，则⾬刮灵敏调节设置 项保持 level3，否则开关根据收
        //到的信号显⽰对应状态；
        //4. 若触发条件 4，ICC 连续发送三帧 ICC_WiprSnvty = 0x4:Level 4，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 最⾼，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x4:Level4，则⾬刮灵敏调节设置 项保持 level4，否则开关根据收到
        //的信号显⽰对应状态；
        //5. 若未进⾏开关设置，FLZCU_WipeSensitivitySts对应等级信号发⽣变化，⾬刮灵敏调节设置为对应
        //等级。

        // ICC -> FLZCU 信号名：ICC_WiprSnvty
        // 0x0:Not Active
        // 0x1:Level 1
        // 0x2:Level 2
        // 0x3:Level 3
        // 0x4:Level 4
        // 0x5:Reserved
        // 0x6:Reserved

        // FLZCU -> ICC 信号名：FLZCU_WipeSensitivitySts
        // 0x0:Not Active
        // 0x1:Level 1
        // 0x2:Level 2
        // 0x3:Level 3
        // 0x4:Level 4
        // 0x5~0x7:Reserved
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setCentralLocking: 发送雨刮器灵敏度:" + status);
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY_SET, 0x1);
                    quickManager.setWiperSensitivity(0x1);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY_SET, 0x2);
                    quickManager.setWiperSensitivity(0x2);
                    break;
                case 2:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY_SET, 0x3);
                    quickManager.setWiperSensitivity(0x3);
                    break;
                case 3:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY_SET, 0x4);
                    quickManager.setWiperSensitivity(0x4);
                    break;
            }
        }
    }

    @Override
    public int getWiperSens() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY);
        int signlVal = quickManager.getWiperSensitivity();

        Log.d(TAG, "setCentralLocking: 获取雨刮器灵敏度:" + signlVal);
        int status = 3;
        if (signlVal == 0x1) {
            status = 0;
        } else if (signlVal == 0x2) {
            status = 1;
        } else if (signlVal == 0x3) {
            status = 2;
        } else if (signlVal == 0x4) {
            status = 3;
        }
        Prefs.put(PrefsConst.Q_WIPER_SENS, status);
//        return Prefs.rtnStatus(PrefsConst.C_WIPER_SENS, status, PrefsConst.DefaultValue.C_WIPER_SENS);
        return status;
    }

    @Override
    public void setHudRoate(int status) {
        int value = status + 50;
        //显⽰名称：后尾⻔开启⾼度设置 开关设置：50%~100% 开关默认值：95%
        //配置：PLG 配置
        //前置条件：
        //1. 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件
        //1. 在中控屏设置后尾⻔开启⾼度（50%~95%），ICC 发送 ICC_Set_PLGOperateSts 给 PLG，⾄少发
        //送三帧之后发送 0x0:Not Active 给 FLZCU；
        //执⾏输出（1||2||3）
        //1. ICC 收到 PLG_Set_PLGOperateStsFb≤50，后尾⻔开启⾼度显⽰ 50%；
        //2. ICC 收到 PLG_Set_PLGOperateStsFb= 51-95，后尾⻔开启⾼度显⽰对应信号值 51%~95% ；
        //3. ICC 收到 PLG_Set_PLGOperateStsFb≥95，后尾⻔开启⾼度显⽰ 95%；
        //备注：
        //1. ICC显⽰界⾯设置为⽤⼾选择的状态，并在2秒后同步总线反馈的状态，否则返回上⼀设置值。

        // ICC -> PLG 信号名：ICC_Set_PLGOperateSts
        // 0x0:Not Active
        // 0x~0x65:0%~100%
        Log.d(TAG, "setHudRoate: 设置后尾门高度:" + value);
        if (MsgUtil.getInstance().supportPowerMode()) {
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_TRUNKDOOROPENDEGREE_SET, value);
            quickManager.setHudRoate(value);
        }
    }

    @Override
    public int getHudRoate() {
//        int value = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEDOOR_TRUNKDOOROPENDEGREE);
        int value = quickManager.getHudRoate();
        Log.d(TAG, "getHudRoate: 获取后尾门高度:" + value);

        value = value < 50  ? 50 : Math.min(value, 100);
        int status = value - 50;
        Prefs.put(PrefsConst.Q_REAR_TAILGATE_ROATE, status);
//        return Prefs.rtnStatus(PrefsConst.Q_REAR_TAILGATE_ROATE, status, PrefsConst.DefaultValue.Q_REAR_TAILGATE_ROATE);
        return status;
    }

    @Override
    public void setDriveAirBag(int status) {
        // 显示名称：副驾安全气囊 开关设置：开启/关闭 开关默认值：开启
        // 开关设置：
        // 前置条件：
        // 电源模式:ON档，(信号：FLZCU_9_PowerMode=ON)；
        // 触发条件：（1||2）
        // 1. 在中控屏车辆设置-：副驾安全气囊，点击打开
        // 2. 在中控屏车辆设置-：副驾安全气囊，点击关闭
        // 执行输出：（1||2）
        // 1. 若触发条件为 1， ICC 连续发送三帧 ICC_PABSetCmd = 0x2: PAB ON Cmd 给 ACU，ACU 控制副
        // 驾气囊 PAB 开启，接着发送 0x0:Not Active ，计时 2s 若收到状态反馈
        // ABM_1_PABSetSts=0x1:PAB ON，则副驾气囊屏蔽开关保持开启；
        // 2. 若触发条件为 2， 弹框提醒“您是否确定要停用前排乘客安全气囊？”，点击确认后ICC发送
        //         ICC_PABSetCmd = 0x1: PAB OFF Cmd 给 ACU，ACU 控制副驾气囊 PAB 关闭，接着发送 0x0:NotActive ，
        // 计时 2s 若收到状态反馈ABM_1_PABSetSts=0x0:PAB OFF，则副驾气囊屏蔽开关保持关闭
        // 副驾气囊 PAB 状态栏图标显示
        // 前置条件：
        // 电源状态 ON 档(信号：FLZCU_9_PowerMode=0x2:ON)；
        // 触发条件：（1||2||3||4）
        // 1. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x0: 'PAB ON' lamp on”；
        // 2. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x1: 'PAB OFF' lamp on”；
        // 3. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x2: no lamp on”；
        // 4. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x3: Both lamp on”；
        // 执行输出：（1||2||3||4）
        // 1. 若触发条件 1，点亮副驾安全气囊开启指示灯，并在指示灯上方显示 “Passenger Airbag”字样；
        // 2. 若触发条件 2，点亮副驾安全气囊关闭指示灯 ，并在指示灯上方显示 “Passenger Airbag”字样 ；
        // 3. 若触发条件 3，熄灭副驾安全气囊指示灯，同时不再显示“Passenger Airbag”字样。
        // 4. 若触发条件 4，同时点亮副驾安全气囊开启和关闭指示灯，同时不再显示“Passenger Airbag”字样；
        // 异常处理：失去通讯，保持当前状态。
        // 信号描述：
        // ICC -> ACU 信号名：ICC_PABSetCmd
        // 0x0:NotActive
        // 0x1:PAB OFF
        // 0x2:PAB ON
        //
        // ACU -> ICC 信号名：ABM_1_PABSetSts
        // 0x0:PAB OFF
        // 0x1:PAB ON
        //
        // ACU -> ICC 信号名：ACU_1_PsngrBagSts
        // 0x0:'PAB ON' lamp on
        // 0x1:'PAB OFF' lamp on
        // 0x2:no pamp on
        // 0x3:Both lamp on
        // todo 驾驶安全气囊
        // TODO 在线配置 仪表
        Log.d(TAG, "setSwDriveAirBag: 开关驾驶安全气囊:" + status);
        // 电源模式:ON档，(信号：FLZCU_9_PowerMode=ON)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 1) {
                // ICC 连续发送三帧 ICC_PABSetCmd = 0x2
                Log.d(TAG, "setSwDriveAirBag: 开启");
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_PASSENGERAIRBAG_SET, 0x2);
                quickManager.setDriveAirbag(0x2);
            } else if (status == 0) {
                // ICC发送 ICC_PABSetCmd = 0x1:
                Log.d(TAG, "setSwDriveAirBag: 关闭");
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_PASSENGERAIRBAG_SET, 0x1);
                quickManager.setDriveAirbag(0x1);
            }
        }
    }

    @Override
    public int getDriveAirBag() {
        int signalVal = quickManager.getDriveAirbag();
//        int signalVal = MsgUtil.getInstance().getSignalVal(SiganlConstans.BODYINFO_PASSENGERAIRBAG);
        int status = 0;
        if (signalVal == 0x0) {
            status = 0;
        } else if (signalVal == 0x1) {
            status = 1;
        }
        Prefs.put(PrefsConst.Q_DRIVE_AIR_BAG, status);
        return status;
    }


    @Override
    public int getCarSpeed() {
        int signalVal = quickManager.getCarSpeed();
        return signalVal;
    }

    @Override
    public void setCarSpeed(int v) {
        // TODO 在线配置
        Log.d(TAG, "setCarSpeed: 设置车速:" + v);
        if (v == -1) {
            Log.d(TAG, "setCarSpeed: 车速设置失败");
            return;
        }
        Prefs.put(PrefsConst.C_CAR_SPEED, v);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_CAR_SPEED, v);
        Log.d(TAG, "setCarSpeed: 车速设置成功" + v);
    }

    @Override
    public void setCustomButton(int state) {
        // 设置自定义按键类型
        // 1 车辆设置, 2 DVR, 3 HUD, 4 方向盘调节HUD, 5 后视镜调节, 6 音源切换, 7 返回 8 AVM 9 全息影像 10 车外声音APP
        Prefs.put(PrefsConst.C_CUSTOM_BUTTON, state);
        int customStatus = 1;
        if (state == 0) {
            // 行车记录仪抓拍
            customStatus = 2;
        } else if (state == 1) {
            // AVM进入/退出
            customStatus = 8;
        } else if (state == 2) {
            // 方向盘调节HUD
            customStatus = 3;
        } else if (state == 3) {
            // 后视镜调节
            customStatus = 5;
        } else if (state == 4) {
            // 音源切换
            customStatus = 6;
        } else if (state == 5) {
            // 方向盘调节
            customStatus = 4;
        } else if (state == 6) {
            // 全息影像
            customStatus = 9;
        }
        Log.d(TAG, "setCustomButton: 设置自定义按键类型: " + state + " === " + customStatus);
        Settings.System.putInt(mContext.getContentResolver(), "custom_input_event_1", customStatus);
    }

    @Override
    public int getCustomButton() {
        int status = Prefs.get(PrefsConst.C_CUSTOM_BUTTON, PrefsConst.DefaultValue.C_CUSTOM_BUTTON);
        Log.d(TAG, "getCustomButton: 获取自定义按键类型 " + status);
        return status;
    }

    NewEnergyManager newEnergyManager = (NewEnergyManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_ENERGY_MANAGER);
    // 获取P档状态
    public int getPowerMode() {
        // 0x0:Init
        // 0x1:P
        // 0x2:R
        // 0x3:N
        // 0x4:D
        // 0x5:Reserved
        // 0x6:Reserved
        // 0x7:Reserved
        int signalVal = newEnergyManager.getDrivingInfoGear();
        int status = 0;
        if (signalVal == 0x1) {
            status = 0;
        } else if (signalVal == 0x2) {
            status = 1;
        }
        Prefs.put(PrefsConst.D_INFO_GEAR, status);
        return status;
    }
}
