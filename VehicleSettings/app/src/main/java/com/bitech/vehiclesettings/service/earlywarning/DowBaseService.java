package com.bitech.vehiclesettings.service.earlywarning;

import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.bean.atmosphere.AmbLigBean;
import com.bitech.vehiclesettings.utils.CommonConst;


/**
 * DOW 提示
 */
public class DowBaseService {
    private static final String TAG = DowBaseService.class.getSimpleName();
    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
    int count = 0;

    public void doTask(int type, int sign) {
        Log.d(TAG, "[warn] DOW提示right doTask: ");
        if (count == 0) {
            count++;
            // 熄灭
            AmbLigBean ambLigBean = getAmbLigDown(CarLight.AmbLigColorAdj.lin_1);
            if (type == CommonConst.Dow.all) {
                ambLigBean.setAmbModLocSig(CarLight.AmbModLocSig.NOT_ACTIVE);
            } else if (type == CommonConst.Dow.left) {
                ambLigBean.setAmbModLocSig(CarLight.AmbModLocSig.MODE1_1);
            } else if (type == CommonConst.Dow.right) {
                ambLigBean.setAmbModLocSig(CarLight.AmbModLocSig.MODE1_2);
            }
            carServer.setLightAmbLightCan(ambLigBean);
        } else {
            // 点亮
            AmbLigBean ambLigBean = getAmbLigUp(CarLight.AmbLigColorAdj.lin_1);
            if (type == CommonConst.Dow.all) {
                ambLigBean.setAmbModLocSig(CarLight.AmbModLocSig.NOT_ACTIVE);
            } else if (type == CommonConst.Dow.left) {
                ambLigBean.setAmbModLocSig(CarLight.AmbModLocSig.MODE1_1);
            } else if (type == CommonConst.Dow.right) {
                ambLigBean.setAmbModLocSig(CarLight.AmbModLocSig.MODE1_2);
            }
            carServer.setLightAmbLightCan(ambLigBean);
            if (count >= 4) {
                count = 0;
            }
        }
    }


    /**
     * 渐亮
     */
    private AmbLigBean getAmbLigUp(int colorIdx) {
        AmbLigBean up1 = new AmbLigBean().setRear();
        up1.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_5).setAmbLigColorAdj(colorIdx).setAmbModLocSig(CarLight.AmbModLocSig.NOT_ACTIVE);
        return up1;
    }

    /**
     * 渐灭
     */
    private AmbLigBean getAmbLigDown(int colorIdx) {
        AmbLigBean down1 = new AmbLigBean().setRear();
        down1.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0).setAmbLigColorAdj(colorIdx).setAmbModLocSig(CarLight.AmbModLocSig.NOT_ACTIVE);
        return down1;
    }
}
