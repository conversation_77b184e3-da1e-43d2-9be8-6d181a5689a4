package com.bitech.vehiclesettings.utils;

import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.carapi.constants.CarDisplay;

public class SendICUTopicsUtil {

    public static class Topics {
        public static final String Vehiclesettings_ShowLyrics_SET = "Vehiclesettings/ShowLyrics/Set";// 仪表显示歌词
        public static final String Vehiclesettings_ShowLyrics = "Vehiclesettings/ShowLyrics";// 仪表显示歌词
        public static final String Vehiclesettings_AMPM_SET = "Vehiclesettings/AMPM/Set";// 时间显示格式（12小时制，24小时制）
        public static final String Vehiclesettings_AMPM = "Vehiclesettings/AMPM";// 时间显示格式（12小时制，24小时制）
        public static final String SystemSetting_TIMECHANGE_SET = "SystemSetting/TimeChange/Set";// 时间变化
        public static final String SystemSetting_TIMECHANGE = "SystemSetting/TimeChange";// 时间
        public static final String Vehiclesettings_Language_SET = "Vehiclesettings/Language/Set";// 语言设置（中文/英文）
        public static final String Vehiclesettings_Language = "Vehiclesettings/Language";// 语言设置（中文/英文）
        public static final String Vehiclesettings_DayNightMode_SET = "Vehiclesettings/DayNightMode/Set";// 主题设置（白天/黑夜）
        public static final String Vehiclesettings_DayNightMode = "Vehiclesettings/DayNightMode";// 主题设置（白天/黑夜）
        public static final String Vehiclesettings_DayNightMode_SET_MCU = "uartrpc_svc/service_0x23/req";// 主题设置（白天/黑夜）---MCU
    }

    static MsgUtil msgUtil = MsgUtil.getInstance();

    public static void sendDayNightTopic(int mode) {
        // 发送Topic至ICU
        SendICUTopicsUtil.sendTopics(SendICUTopicsUtil.Topics.Vehiclesettings_DayNightMode_SET, mode);
        // 发送Topic至MCU
        SendICUTopicsUtil.sendBytes2MCU(mode);
    }

    public static void sendTopics(String topic, int value) {
        msgUtil.setSignlVal(topic, value);
    }

    public static void sendObjectTopics(String topic, Object value) {
        msgUtil.setObjectSignalVal(topic, value);
    }

    /**
     * 向MCU发送数据
     * MCU 23服务 01src 功能
     * TODO
     */
    public static void sendBytes2MCU(int mode) {
        byte[] bytes = new byte[3];
        bytes[0] = 0x23;
        bytes[1] = 0x01;
        if (mode == CarDisplay.NIGHT) {
            bytes[2] = 0x02;
        } else {
            bytes[2] = 0x01;
        }
        msgUtil.send(Topics.Vehiclesettings_DayNightMode_SET_MCU, bytes);
    }
}
