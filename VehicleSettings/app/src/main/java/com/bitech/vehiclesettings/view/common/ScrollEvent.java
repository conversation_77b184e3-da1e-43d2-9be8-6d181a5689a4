package com.bitech.vehiclesettings.view.common;


/**
 * 事件数据
 *
 * <AUTHOR>
 */
public class ScrollEvent {
    // 传递滚动距离。可以根据需要添加更多字段。例如：滚动距离等。
    public final float scrollY;

    public String actionIsscrolling;

    public ScrollEvent(float scrollY, String actionIsscrolling) {
        // 传递滚动距离。可以根据需要添加更多字段。例如：滚动距离等。
        this.scrollY = scrollY;
        this.actionIsscrolling = actionIsscrolling;
    }
}
