package com.bitech.vehiclesettings.database

import androidx.room.Database
import androidx.room.RoomDatabase
import com.bitech.vehiclesettings.bean.BtPairedBean
import com.bitech.vehiclesettings.bean.SearchHistoryBean
import com.bitech.vehiclesettings.bean.WifiHotspotBean

/**
 * @ClassName: SettingDatabase
 * 
 * @Date:  2024/1/29 17:28
 * @Description: 设置数据库管理对象.
 **/
@Database(
    entities = [BtPairedBean::class, WifiHotspotBean::class,
        SearchHistoryBean::class],
    version = 4,
    exportSchema = false
)
abstract class SettingDatabase : RoomDatabase() {
    /**
     * 蓝牙设备管理数据Dao对象.
     *
     * @return BtDeviceDao
     */
    abstract fun btDeviceDao(): BtDeviceDao

    /**
     * wifi热点设备管理数据Dao对象.
     *
     * @return WifiHotspotDao
     */
    abstract fun wifiHotspotDao(): WifiHotspotDao

    /**
     * 搜索历史Dao对象.
     *
     * @return SearchHistoryDao
     */
    abstract fun searchHistoryDao(): SearchHistoryDao
}