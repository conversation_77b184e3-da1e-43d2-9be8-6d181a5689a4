package com.bitech.vehiclesettings.view.driving;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertDTractionModeBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class TractionModeUIAlert extends BaseDialog {
    private static final String TAG = TractionModeUIAlert.class.getSimpleName();
    private static TractionModeUIAlert.onProgressChangedListener onProgressChangedListener;

    private static Integer isConfirm = 0;


    public TractionModeUIAlert(@NonNull Context context) {
        super(context);
    }

    public TractionModeUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected TractionModeUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static TractionModeUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(TractionModeUIAlert.onProgressChangedListener onProgressChangedListener) {
        TractionModeUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private TractionModeUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertDTractionModeBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private TractionModeUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public TractionModeUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public TractionModeUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new TractionModeUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertDTractionModeBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            window.setAttributes(layoutParams);
            layoutParams.width = 1176;
            layoutParams.height = 502;
            binding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onProgressChangedListener.onConfirm();
                    isConfirm = 1;
                    dialog.dismiss();
                }

            });

            binding.tvCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
            return dialog;
        }

        public void dismiss() {
            dialog.dismiss();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
        if (isConfirm == 1) {
            isConfirm = 0;
        }else {
            onProgressChangedListener.onCancel();
        }
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        public void onConfirm();
        public void onCancel();
    }
}
