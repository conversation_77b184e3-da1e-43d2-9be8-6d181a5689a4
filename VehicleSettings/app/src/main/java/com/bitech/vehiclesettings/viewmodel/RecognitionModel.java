package com.bitech.vehiclesettings.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

public class RecognitionModel extends ViewModel {
    // 舱内摄像头
    private final MutableLiveData<Integer> camera = new MutableLiveData<>();
    // 疲劳检测
    private final MutableLiveData<Integer> fatigue = new MutableLiveData<>();
    // 视线分心
    private final MutableLiveData<Integer> distraction = new MutableLiveData<>();
    // 打电话提醒
    private final MutableLiveData<Integer> call = new MutableLiveData<>();
    // 喝水提醒
    private final MutableLiveData<Integer> drink = new MutableLiveData<>();
    // 座椅加热
    private final MutableLiveData<Integer> seatHeat = new MutableLiveData<>();
    // 座椅通风
    private final MutableLiveData<Integer> seatVentilation = new MutableLiveData<>();
    // 视线解锁
    private final MutableLiveData<Integer> sightUnlock = new MutableLiveData<>();
    // 个性化问候
    private final MutableLiveData<Integer> greet = new MutableLiveData<>();
    // 抽烟关怀
    private final MutableLiveData<Integer> smoke = new MutableLiveData<>();

    public MutableLiveData<Integer> getCamera() {
        return camera;
    }

    public void setCamera(Integer status) {
        camera.postValue(status);
    }

    public MutableLiveData<Integer> getFatigue() {
        return fatigue;
    }

    public void setFatigue(Integer status) {
        fatigue.postValue(status);
    }

    public MutableLiveData<Integer> getDistraction() {
        return distraction;
    }

    public void setDistraction(Integer status) {
        distraction.postValue(status);
    }

    public MutableLiveData<Integer> getCall() {
        return call;
    }

    public void setCall(Integer status) {
        call.postValue(status);
    }

    public MutableLiveData<Integer> getDrink() {
        return drink;
    }

    public void setDrink(Integer status) {
        drink.postValue(status);
    }

    public MutableLiveData<Integer> getSeatHeat() {
        return seatHeat;
    }

    public void setSeatHeat(Integer status) {
        seatHeat.postValue(status);
    }

    public MutableLiveData<Integer> getSeatVentilation() {
        return seatVentilation;
    }

    public void setSeatVentilation(Integer status) {
        seatVentilation.postValue(status);
    }

    public MutableLiveData<Integer> getSightUnlock() {
        return sightUnlock;
    }

    public void setSightUnlock(Integer status) {
        sightUnlock.postValue(status);
    }

    public MutableLiveData<Integer> getGreet() {
        return greet;
    }

    public void setGreet(Integer status) {
        greet.postValue(status);
    }

    public MutableLiveData<Integer> getSmoke() {
        return smoke;
    }

    public void setSmoke(Integer status) {
        smoke.postValue(status);
    }
}
