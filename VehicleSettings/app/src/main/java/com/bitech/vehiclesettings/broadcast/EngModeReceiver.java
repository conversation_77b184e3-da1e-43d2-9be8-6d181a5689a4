package com.bitech.vehiclesettings.broadcast;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.provider.ReceiverAction;

public class EngModeReceiver extends BroadcastReceiver {
    private static final String TAG = SliceReceiver.class.getName();
    private SystemPresenter systemPresenter;
    @Override
    public void onReceive(Context context, Intent intent) {
        switch (intent.getAction()) {
            case ReceiverAction.ACTION_ENG_MODE_RESET_SYSTEM:
                systemPresenter = new SystemPresenter<>(context);
                systemPresenter.factoryReset();
                break;
        }
    }
}
