package com.bitech.vehiclesettings.view.display;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class ResetDisplayUIAlert extends BaseDialog {
    private static final String TAG = ResetDisplayUIAlert.class.getSimpleName();
    public ResetDisplayUIAlert(Context context) {
        super(context);
    }
    public ResetDisplayUIAlert(Context context, int theme) {
        super(context, theme);
    }
    protected ResetDisplayUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder {
        public interface OnDialogClickListener {
            void onConfirm();

            void onCancel();
        }

        private final Context context;
        private boolean isCan = true;
        private ResetDisplayUIAlert dialog = null;
        private View layout;
        private OnDialogClickListener onDialogClickListener;

        public Builder(Context context) {
            this.context = context;
        }


        public ResetDisplayUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public ResetDisplayUIAlert.Builder setOnDialogClickListener(ResetDisplayUIAlert.Builder.OnDialogClickListener listener) {
            this.onDialogClickListener = listener;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public ResetDisplayUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new ResetDisplayUIAlert(context, R.style.Dialog);
            layout = View.inflate(context, R.layout.dialog_alert_display_reset, null);
            dialog.setCanceledOnTouchOutside(true);  // 允许点击外部区域关闭
            dialog.setCancelable(true);              // 允许按返回键关闭

            Button confirmBtn = layout.findViewById(R.id.btn_confirm);
            confirmBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onDialogClickListener != null) {
                        onDialogClickListener.onConfirm();
                    }
                    dialog.dismiss();
                }
            });
            Button cancelBtn = layout.findViewById(R.id.btn_cancel);
            cancelBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onDialogClickListener != null) {
                        onDialogClickListener.onCancel();
                    }
                    dialog.dismiss();
                }
            });
            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);
            return dialog;
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }


}
