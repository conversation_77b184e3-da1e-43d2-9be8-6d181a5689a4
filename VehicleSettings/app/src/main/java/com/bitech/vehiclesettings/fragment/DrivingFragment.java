package com.bitech.vehiclesettings.fragment;

import android.content.ContentResolver;
import android.content.ContentUris;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;

import com.bitech.vehicle3D.VehicleServiceManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.bean.report.Content;
import com.bitech.vehiclesettings.bean.report.DataPoint;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.carapi.constants.Car3DModel;
import com.bitech.vehiclesettings.carapi.constants.CarDMSConstant;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.databinding.FragmentDrivingBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.service.DataPointReportLifeCycle;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectHelper;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.ThreeDModelUtil;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.driving.ComfortBrakingUIAlert;
import com.bitech.vehiclesettings.view.driving.PersonalizedSettingUIAlert;
import com.bitech.vehiclesettings.view.driving.TiredReminderUIAlert;
import com.bitech.vehiclesettings.view.driving.TractionModeUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.RegularWashUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.WashCarModeUIAlert;
import com.bitech.vehiclesettings.view.recognition.CameraSwitchOffUIAlert;
import com.bitech.vehiclesettings.view.recognition.CameraSwitchOnUIAlert;
import com.bitech.vehiclesettings.viewmodel.DrivingViewModel;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;
import com.iflytek.autofly.icvp.sdk.ICVPManager;
import com.iflytek.autofly.icvp.sdk.IICVPManager;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.lion.datapoint.log.LogDataUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DrivingFragment extends BaseFragment<FragmentDrivingBinding> implements View.OnClickListener, SafeHandlerCallback {

    private static final String TAG = DrivingFragment.class.getSimpleName();
    private ArrayList<Content> dataList;
    private PersonalizedSettingUIAlert.Builder personalizedSettingUIAlert;
    private DetailsUIAlert.Builder detailUIAlert;
    private ComfortBrakingUIAlert.Builder comfortBrakingUIAlert;
    private TractionModeUIAlert.Builder tractionModeUIAlert;
    private TiredReminderUIAlert.Builder tiredReminderUIAlert;
    private WashCarModeUIAlert.Builder washCarModeUIAlertBuilder; // 洗车模式
    RegularWashUIAlert regularWashUIAlert;//语音打开洗车模式
    private RegularWashUIAlert.Builder regularWashUIAlertBuilder;
    private CameraSwitchOnUIAlert.Builder cameraSwitchOnUIAlert;
    private CameraSwitchOffUIAlert.Builder cameraSwitchOffUIAlert;
    private volatile boolean isActive;
    private SafeHandler drivingHandler;
    private DrivingViewModel viewModel;
    private ContentResolver cr = null;
    private long driverStatus = 0;
    private long fatigueDetectionStatus = 0;
    private long distractionStatus = 0;
    private long callStatus = 0;

    public static final int DRIVE_PERSONALIZE_DIALOG = 10;
    public static final int DRIVE_WASH_MODE_DIALOG = 11;
    public static final int DRIVE_WASH_MODE_START_DIALOG = 12;
    private MainActViewModel mainActViewModel;

    // 车辆模式背景图片
    private final List<Integer> drivingModeImages = Arrays.asList(
            R.mipmap.img_set_drivemode_ev,
            R.mipmap.img_set_drivemode_eco,
            R.mipmap.img_set_drivemode_comfort,
            R.mipmap.img_set_drivemode_gt,
            R.mipmap.img_set_drivemode_snow,
            R.mipmap.img_set_drivemode_custom
    );

    private Integer[] driveModes;
    private Integer[] driveReqs;

    private List<LinearLayout> driveModeViews;

    private IICVPManager icvpManager;

    // 打开自定义提示窗口
    private void openTipsDialog(String title, String content, int width, int height, int gravity) {
        // 如果对话框已存在且正在显示，则不创建新的
        if (detailUIAlert != null && detailUIAlert.isShowing()) {
            return;
        }

        if (detailUIAlert == null) {
            detailUIAlert = new DetailsUIAlert.Builder(mContext);
        }
        detailUIAlert.create(title, content, width, height, gravity, com.bitech.base.R.dimen.font_24px).show();
        detailUIAlert.setPadding(120);
        detailUIAlert.setScrollable(true);
    }

    // 牵引模式是否可用
    private void updateModeAvailableUI(int status) {
        if (status == 1) {
            GrayEffectUtils.applyGrayEffect(binding.llTractionMode);
        } else {
            GrayEffectUtils.removeGrayEffect(binding.llTractionMode);
        }
    }

    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        icvpManager = ICVPManager.getInstance();
        Log.d(TAG, "onCreate");
        isActive = true;
        drivingHandler = new SafeHandler(this);
        registerDMSListener();
    }

    ContentObserver observer = new ContentObserver(null) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            Log.d(TAG, "ContentObserver-->onChange" + selfChange + " , uri: " + uri.toString());
            String path = uri.getPath();
            if (!TextUtils.isEmpty(path)) {
                switch (path) {
                    case CarDMSConstant.KEY_SAFETY_FATIGUE_STATUS:
                        fatigueDetectionStatus = ContentUris.parseId(uri);
                        updateFatigueUI();
                        Log.d(TAG, "mSettingsContentObserver.onChange key_safety_fatigue_status status:" + uri);
                        break;
                    case CarDMSConstant.KEY_SAFETY_DISTRACTION_DRIVER_STATUS:
                        distractionStatus = ContentUris.parseId(uri);
                        updateDistractionDriverUI();
                        Log.d(TAG, "mSettingsContentObserver.onChange key_safety_fatigue_status status:" + uri);
                        break;
                    case CarDMSConstant.KEY_SAFETY_DISTRACTION_CALLS_STATUS:
                        callStatus = ContentUris.parseId(uri);
                        updateDistractionCallsUI();
                        Log.d(TAG, "mSettingsContentObserver.onChange key_safety_fatigue_status status:" + uri);
                        break;
                    default:
                        break;
                }
            }
        }
    };

    public void registerDMSListener() {
        cr = MyApplication.getContext().getContentResolver();

        // 创建统一的 ContentObserver
        ContentObserver combinedObserver = new ContentObserver(new Handler()) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                Log.d(TAG, "ContentObserver.onChange: uri=" + uri);

                // 检查是否是 provider_created 的通知
                if (uri != null && uri.toString().contains("provider_created")) {
                    Log.d(TAG, "Smart scenes app initialization completed");
                    // 处理初始化完成的通知
                } else {
                    // 处理常规设置变化的通知
                    // 收到智能场景设置变化监听回调，获取场景开关状态
                    querySettings();
                }
            }
        };

        // 注册监听，使用 notifyForDescendants=true 来监听所有子路径
        cr.registerContentObserver(Uri.parse("content://com.lion.smartscenes/settings/"), true, combinedObserver);

        // 初始查询设置
        querySettings();

        driverStatus = Prefs.get(PrefsConst.D_DMS_OPEN_STATUS, CarDriving.DMS_OPEN_Status.OFF);
    }

    private void querySettings() {
        Uri uri = Uri.parse("content://com.lion.smartscenes/settings/");
        Cursor cursor = cr.query(uri, null, null, null, null);
        if (cursor != null) {
            try {
                if (cursor.moveToNext()) {
                    int keySafetyFatigueStatus = cursor.getColumnIndex(CarDMSConstant.KEY_SAFETY_FATIGUE_STATUS);
                    int keySafetyDistractionDriverStatus = cursor.getColumnIndex(CarDMSConstant.KEY_SAFETY_DISTRACTION_DRIVER_STATUS);
                    int keySafetyDistractionCallsStatus = cursor.getColumnIndex(CarDMSConstant.KEY_SAFETY_DISTRACTION_CALLS_STATUS);
                    this.fatigueDetectionStatus = cursor.getInt(keySafetyFatigueStatus);
                    this.distractionStatus = cursor.getInt(keySafetyDistractionDriverStatus);
                    this.callStatus = cursor.getInt(keySafetyDistractionCallsStatus);
                }
            } finally {
                cursor.close();
            }
        }
    }

    @Override
    protected void initObserve() {
        initObserver();
    }

    @Override
    protected void initData() {
        viewModel.initData();
        driveModes = new Integer[]{
                CarDriving.VCC_1_DriveMode.ECO,
                CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID,
                CarDriving.VCC_1_DriveMode.NORMAL,
                CarDriving.VCC_1_DriveMode.SPORT,
                CarDriving.VCC_1_DriveMode.SNOW,
                CarDriving.VCC_1_DriveMode.INDIVIDUAL
        };
        driveReqs = new Integer[]{
                CarDriving.ICC_DriveModeSet_Req.ECO,
                CarDriving.ICC_DriveModeSet_Req.ENERGY_SAVING_HYBRID,
                CarDriving.ICC_DriveModeSet_Req.NORMAL,
                CarDriving.ICC_DriveModeSet_Req.SPORT,
                CarDriving.ICC_DriveModeSet_Req.RAIN_SNOW,
                CarDriving.ICC_DriveModeSet_Req.INDIVIDUAL
        };
    }

    @Override
    public void onResume() {
        super.onResume();
        // 手动触发一次滚动监听
        binding.scrollView.post(() -> {
            int scrollY = binding.scrollView.getScrollY();
            MainActivity activity = (MainActivity) getActivity();
            if (activity != null) {
                activity.getBinding().ivModel.handleScroll(scrollY);
            }
        });
    }

    /**
     * 滚动监听
     */
    private void scrollListener() {
        binding.scrollView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            MainActivity activity = (MainActivity) getActivity();
            if (activity != null) {
                activity.getBinding().ivModel.handleScroll(scrollY);
            }
        });
    }

    @Override
    protected FragmentDrivingBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentDrivingBinding.inflate(inflater, container, false);
        return binding;
    }

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null || binding.scrollView == null) {
            return; // 防止 NPE
        }
        loadPageAnim(binding.scrollView, currentPosition, position);
    }

    @Override
    protected void initView() {
        binding.tvPersonalizedTips.setVisibility(View.GONE);
        driveModeViews = Arrays.asList(
                binding.llDriveModeEv,
                binding.llDriveModeEco,
                binding.llDriveModeComfort,
                binding.llDriveModeGt,
                binding.llDriveModeSnow,
                binding.llDriveModeCustom
        );
    }

    @Override
    protected void setListener() {
        // 滚动事件监听
        scrollListener();
        BindingUtil.bindClicks(this,
                binding.ivExtremePureElectric,
                binding.ivCarModeTips,
                binding.ivComfortBraking,
                binding.ivTractionMode,
                binding.llComfortBraking,
                binding.swExtremePureElectric,
                binding.swSteepSlopeDescent,
                binding.swTractionMode,
                binding.llDriveModeSnow,
                binding.llDriveModeComfort,
                binding.llDriveModeCustom,
                binding.llDriveModeEv,
                binding.llDriveModeGt,
                binding.llDriveModeEco,
                // 洗车模式
                binding.rlWashCar
        );

        for (int i = 0; i < driveModeViews.size(); i++) {
            final int modeIndex = i;
            driveModeViews.get(i).setOnClickListener(v -> {
                Integer powerMode = viewModel.powerModeLiveData.getValue();
                if (modeIndex == 0 && !(powerMode == CarDriving.FLZCU_9_PowerMode.ON || powerMode == CarDriving.FLZCU_9_PowerMode.COMFORTABLE)) {
                    return;
                } else if (modeIndex != 0 && powerMode != CarDriving.FLZCU_9_PowerMode.ON) {
                    EToast.showToast(mContext, getString(R.string.str_driving_power_mode_on), 0, false);
                    return;
                }

                updateCarModeUI(driveModes[modeIndex]);
                viewModel.setCarMode(driveReqs[modeIndex]);

                if (driveModes[modeIndex] == CarDriving.VCC_1_DriveMode.INDIVIDUAL &&
                        viewModel.carModeLiveData.getValue() == CarDriving.VCC_1_DriveMode.INDIVIDUAL) {
                    if (personalizedSettingUIAlert == null) {
                        personalizedSettingUIAlert = new PersonalizedSettingUIAlert.Builder(mContext);
                    }
                    if (!personalizedSettingUIAlert.isShowing()) {
                        personalizedSettingUIAlert.create().show();
                    }
                }
                drivingHandler.sendMessageDelayed(MessageConst.DRIVING_CAR_MODE);
            });
        }

        // 极致纯电
        binding.swExtremePureElectric.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setExtremePureSignal(isChecked);
            drivingHandler.sendMessageDelayed(MessageConst.QUICK_EXTREME_PURE_ELECTRIC);
        });
        // 陡坡缓降
        binding.swSteepSlopeDescent.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setSteepSlopeDescent(isChecked);
            drivingHandler.sendMessageDelayed(MessageConst.STEEP_SLOPE_DESCENT);
        });
        // 陡坡缓降蒙层
        binding.vwHdc.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EToast.showToast(mContext, getString(R.string.str_driving_power_mode_on), 0, false);
            }
        });
        // 车身稳定控制
        binding.swBodyStabilityControl.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setBodyStabilityControl(isChecked);
            drivingHandler.sendMessageDelayed(MessageConst.BODY_STABILITY_CONTROL);
        });
        // 自动驻车
        binding.swAutomaticParking.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setAutoParking(isChecked);
            drivingHandler.sendMessageDelayed(MessageConst.AUTO_PARKING);
        });

        // EPB软开关 - 驻车制动
        binding.swParkingBrake.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                viewModel.setParkingBrake(CarDriving.EPBSetCmd.APPLY);
            } else {
                if (viewModel.parkingBrakeLiveData.getValue() == CarDriving.EPBActrSt.APPLIED) {
                    int brakePedal = viewModel.brakePedalLiveData.getValue();
                    Log.d(TAG, "brakePedal: " + brakePedal);
                    if (brakePedal != CarDriving.BrakePedalSts.APPLIED) {
                        Log.d(TAG, "未触发驻车制动");
                        EToast.showToast(buttonView.getContext(), "解除驻⻋制动需踩下制动踏板", 0, false);
                        binding.swParkingBrake.setChecked(true);
                        return;
                    }
                }
                viewModel.setParkingBrake(CarDriving.EPBSetCmd.RELEASE);
            }
            drivingHandler.sendMessageDelayed(MessageConst.PARKING_BRAKE);
        });

        // 舒适制动开关
        binding.swComfortBraking.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setComfortBraking(isChecked);
            drivingHandler.sendMessageDelayed(MessageConst.COMFORT_BRAKING);
        });

        // 舒适制动阴影
        binding.vwComfort.setOnClickListener(v -> {
            if (viewModel.powerModeLiveData.getValue() != CarDriving.FLZCU_9_PowerMode.ON) {
                EToast.showToast(mContext, getString(R.string.str_driving_power_mode_on), 0, false);
            } else {
                EToast.showToast(mContext, getString(R.string.str_driving_comfort_fail), 0, false);
            }
        });

        // 舒适制动弹窗
        ComfortBrakingUIAlert.setOnProgressChangedListener(new ComfortBrakingUIAlert.onProgressChangedListener() {
            @Override
            public void onSwitch(boolean isChecked) {
                viewModel.setComfortBraking(isChecked);
                drivingHandler.sendMessageDelayed(MessageConst.COMFORT_BRAKING);
            }

            @Override
            public void onComfortBrakingRank(int status) {
                // 判断是否为舒适制动开启状态
                if (viewModel.comfortBrakingLiveData.getValue() == CarDriving.CST_Status.STANDBY || viewModel.comfortBrakingLiveData.getValue() == CarDriving.CST_Status.ACTIVE) {
                    viewModel.setComfortBrakingRank(status);
                    drivingHandler.sendMessageDelayed(MessageConst.COMFORT_BRAKING_RANK);
                }
            }

            @Override
            public int getComfortBraking() {
                return viewModel.comfortBrakingLiveData.getValue();
            }

            @Override
            public int getComfortBrakingRank() {
                return viewModel.comfortBrakingRankLiveData.getValue();
            }

        });

        // 悬架智能预瞄
        binding.swIntelligentSuspensionPreview.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setIntelligentSuspensionAiming(isChecked);
            drivingHandler.sendMessageDelayed(MessageConst.INTEL_SUSPENSION_AIMING);
        });

        // 牵引模式
        binding.swTractionMode.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                if (viewModel.tractionModeLiveData.getValue() == CarDriving.VCU_TowingMode.ACTIVE)
                    return;
                tractionModeUIAlert = new TractionModeUIAlert.Builder(mContext);
                tractionModeUIAlert.create().show();
            } else {
                viewModel.setTractionMode(CarDriving.ICC_TowingMode.OFF);
                if (viewModel.tractionModeLiveData.getValue() != CarDriving.VCU_TowingMode.NOT_ACTIVE) {
                    EToast.showToast(mContext, getString(R.string.str_driving_traction_mode_exit), 2000, false);
                    drivingHandler.sendMessageDelayed(MessageConst.TRACTION_MODE);
                } else {
                    // 开启牵引模式失败原因
                    int sign = viewModel.tractionModeFailReasonLiveData.getValue();
                    if (sign == CarDriving.VCU_DrvGearShiftFailureIndcn.EPB_CAN_NOT_RELEASE) {
                        EToast.showToast(mContext, getString(R.string.str_driving_car_scene_assist_4_fail_reason_1), 2000, false);
                    } else if (sign == CarDriving.VCU_DrvGearShiftFailureIndcn.RAKE_PEDAL_APPLIED
                    ) {
                        EToast.showToast(mContext, getString(R.string.str_driving_car_scene_assist_4_fail_reason_2), 2000, false);
                    }
                }
            }
        });

        // 个性化设置
        PersonalizedSettingUIAlert.setOnProgressChangedListener(new PersonalizedSettingUIAlert.onProgressChangedListener() {
            // 驾驶模式
            @Override
            public void onDrivingMode(int status) {
                viewModel.setDrivingMode(status);
                drivingHandler.sendMessageDelayed(MessageConst.DRIVING_DRIVING_MODE);
            }

            // 保电电量
            @Override
            public void setPowerProtection(int status) {
                viewModel.setPowerProtection(status);
                drivingHandler.sendMessageDelayed(MessageConst.DRIVING_POWER_PROTECTION);
            }

            // 转向模式
            @Override
            public void setSwerveMode(int signalVal) {
                viewModel.setSwerveMode(signalVal);
                updateSwerveModeUI(signalVal == CarDriving.ICC_SteeringMode.NORMAL ?
                        CarDriving.FLZCU_SteeringMode.NORMAL : CarDriving.FLZCU_SteeringMode.SPORT);
                drivingHandler.sendMessageDelayed(MessageConst.DRIVING_SWERVE_MODE);
            }

            // 悬架模式
            @Override
            public void setSuspensionMode(int signalVal) {
                updateSuspensionModeUI(signalVal == CarDriving.ICC_SuspensionMode.SOFT ? CarDriving.FLZCU_SuspensionDamping.SOFT : CarDriving.FLZCU_SuspensionDamping.HARD);
                viewModel.setSuspensionMode(signalVal);
                drivingHandler.sendMessageDelayed(MessageConst.DRIVING_SUSPENSION_MODE);
            }

            // 制动模式
            @Override
            public void setBrakingMode(int signalVal) {
                updateBrakingModeUI(signalVal == CarDriving.ICC_BrakePedalFeelMode.COMFORTABLE ?
                        CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE : CarDriving.FLZCU_BrakePedalFeelMode.SPORT);
                viewModel.setBrakingMode(signalVal);
                drivingHandler.sendMessageDelayed(MessageConst.DRIVING_BRAKING_MODE);
            }

            @Override
            public int getCarMode() {
                return viewModel.drivingModeLiveData.getValue();
            }

            @Override
            public Integer getSuspensionMode() {
                return viewModel.suspensionModeLiveData.getValue();
            }

            @Override
            public Integer getSwerveMode() {
                return viewModel.swerveModeLiveData.getValue();
            }

            @Override
            public Integer getBrakingMode() {
                return viewModel.brakingModeLiveData.getValue();
            }

            @Override
            public Integer getPowerProtection() {
                return viewModel.powerProtectionLiveData.getValue();
            }
        });

        // 牵引模式弹窗
        TractionModeUIAlert.setOnProgressChangedListener(new TractionModeUIAlert.onProgressChangedListener() {
            @Override
            public void onConfirm() {
                viewModel.setTractionMode(CarDriving.ICC_TowingMode.ON);
                drivingHandler.sendMessageDelayed(MessageConst.TRACTION_MODE);
            }

            @Override
            public void onCancel() {
                viewModel.getTractionMode();
            }
        });

        // 疲劳分心提醒
        binding.rlLockTips.setOnClickListener(v -> {
            tiredReminderUIAlert = new TiredReminderUIAlert.Builder(mContext, cr);
            tiredReminderUIAlert.create().show();
        });
        TiredReminderUIAlert.setOnProgressChangedListener(new TiredReminderUIAlert.onProgressChangedListener() {

            @Override
            public void openCameraOpenDialog(TiredReminderUIAlert dialog) {
                if (cameraSwitchOnUIAlert != null && cameraSwitchOnUIAlert.isShowing()) {
                    return;
                }
                if (cameraSwitchOnUIAlert == null) {
                    cameraSwitchOnUIAlert = new CameraSwitchOnUIAlert.Builder(mContext);
                }
                CameraSwitchOnUIAlert subDialog = cameraSwitchOnUIAlert.create();
                subDialog.setOnDismissListener(d -> {
                    if (dialog != null) {
                        dialog.unregisterChildDialog();
                    }
                });
                subDialog.show();
            }

            @Override
            public void openCameraCloseDialog(TiredReminderUIAlert dialog) {
                if (cameraSwitchOffUIAlert != null && cameraSwitchOffUIAlert.isShowing()) {
                    return;
                }
                if (cameraSwitchOffUIAlert == null) {
                    cameraSwitchOffUIAlert = new CameraSwitchOffUIAlert.Builder(mContext);
                }
                CameraSwitchOffUIAlert subDialog = cameraSwitchOffUIAlert.create();
                subDialog.setOnDismissListener(d -> {
                    if (dialog != null) {
                        dialog.unregisterChildDialog();
                    }
                });
                subDialog.show();
            }

            @Override
            public long initSwitch(int index) {
                Log.d(TAG, "疲劳分心初始化: " + index);
                return switch (index) {
                    case 0 -> driverStatus;
                    case 1 -> fatigueDetectionStatus;
                    case 2 -> distractionStatus;
                    case 3 -> callStatus;
                    default -> 0;
                };
            }

            @Override
            public void setDMSStatus(int status) {
                driverStatus = status;
                Prefs.put(PrefsConst.D_DMS_OPEN_STATUS, status);
            }
        });
        CameraSwitchOnUIAlert.setOnProgressChangedListener(str -> {
            int status = CommonUtils.BoolToInt("open".equals(str));
            Log.d(TAG, "摄像头开关: " + status);
            ICVPManager.enableDmsCameraSw(true);
            Prefs.put(PrefsConst.R_DMS_CAMERA_STATUS, status);
            Prefs.setGlobalValue(PrefsConst.GlobalValue.R_DMS_CAMERA_STATUS, status);
            drivingHandler.sendMessageDelayed(MessageConst.RECOGNITION_DMS_CAMERA);
        });
        CameraSwitchOffUIAlert.setOnProgressChangedListener(str -> {
            int status = CommonUtils.BoolToInt("open".equals(str));
            Log.d(TAG, "摄像头开关: " + status);
            ICVPManager.enableDmsCameraSw(false);
            Prefs.put(PrefsConst.R_DMS_CAMERA_STATUS, status);
            Prefs.setGlobalValue(PrefsConst.GlobalValue.R_DMS_CAMERA_STATUS, status);
            drivingHandler.sendMessageDelayed(MessageConst.RECOGNITION_DMS_CAMERA);
        });

        // 洗车模式
        binding.rlWashCar.setOnClickListener(v -> {
            if (viewModel.cleanModeStatusLiveData.getValue() == CarDriving.FLZCU_CleanModeStatus.ON) {
                if (regularWashUIAlertBuilder == null) {
                    regularWashUIAlertBuilder = new RegularWashUIAlert.Builder(mContext);
                }
                RegularWashUIAlert regularWashUIAlert = regularWashUIAlertBuilder.create();
                if (regularWashUIAlert != null && !regularWashUIAlert.isShowing()) {
                    regularWashUIAlert.show();
                }
            } else {
                washCarModeUIAlertBuilder = new WashCarModeUIAlert.Builder(mContext);
                washCarModeUIAlertBuilder.create().show();
            }
        });

        // 极致纯电蒙层
        binding.vwExtremePureElectric.setOnClickListener(v -> {
            EToast.showToast(mContext, getString(R.string.str_driving_power_mode_on), 0, false);
        });

        // 车身稳定控制
        binding.vwBodyStabilityControl.setOnClickListener(v -> {
            EToast.showToast(mContext, getString(R.string.str_driving_power_mode_on), 0, false);
        });

        // 自动驻车
        binding.vwAutoParking.setOnClickListener(v -> {
            EToast.showToast(mContext, getString(R.string.str_driving_power_mode_on), 0, false);
        });

        // 驻车制动
        binding.vwParkingBrake.setOnClickListener(v -> {
            EToast.showToast(mContext, getString(R.string.str_driving_power_mode_on), 0, false);
        });

        // 悬架智能预瞄
        binding.vwIntelligentSuspensionPreview.setOnClickListener(v -> {
            EToast.showToast(mContext, getString(R.string.str_driving_power_mode_on), 0, false);
        });
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_extreme_pure_electric) {
            openTipsDialog(getString(R.string.str_driving_car_mode_7), getString(R.string.str_driving_extreme_pure_electric_info), 1176, 396, Gravity.LEFT);
        } else if (id == R.id.iv_car_mode_tips) {
            openTipsDialog(getString(R.string.str_driving_car_mode), getString(R.string.str_driving_car_mode_tips), 1176, 802, Gravity.LEFT);
        } else if (id == R.id.iv_comfort_braking) {
            openTipsDialog(getString(R.string.str_driving_car_pedal_control_1), getString(R.string.str_comfort_braking_tips), 1128, 378, Gravity.CENTER);
        } else if (id == R.id.iv_traction_mode) {
            openTipsDialog(getString(R.string.str_driving_car_scene_assist_4), getString(R.string.str_traction_mode_tips), 1584, 856, Gravity.LEFT);
        } else if (id == R.id.ll_comfort_braking) {
            comfortBrakingUIAlert = new ComfortBrakingUIAlert.Builder(requireContext());
            comfortBrakingUIAlert.create().show();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // 清理 ViewBinding
        if (binding != null) {
            binding = null;
        }

        // 安全取消注册 ContentObserver
        try {
            if (cr != null && observer != null) {
                cr.unregisterContentObserver(observer);
                observer = null; // 防止重复取消注册
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to unregister ContentObserver", e);
        }
    }

    private void initObserver() {
        mainActViewModel = new ViewModelProvider(requireActivity()).get(MainActViewModel.class);
        processTargetDialogEvent(mainActViewModel.getTargetDialogLiveEvent().getValue());
        mainActViewModel.getTargetDialogLiveEvent().observe(getViewLifecycleOwner(), this::processTargetDialogEvent);
        viewModel = new ViewModelProvider(this).get(DrivingViewModel.class);
        // 车辆模式
        viewModel.carModeLiveData.observe(getViewLifecycleOwner(), state -> {
            updateCarModeUI(state);
        });
        // 驻车制动
        viewModel.parkingBrakeLiveData.observe(getViewLifecycleOwner(), state -> {
            updateParkingBrakeUI(state);
            SliceReceiver.notifyChange(ProviderURI.EPB);
        });
        // 舒适制动
        viewModel.comfortBrakingLiveData.observe(getViewLifecycleOwner(), state -> {
            updateComfortBrakingUI(state);
        });
        // 自动驻车
        viewModel.autoParkingLiveData.observe(getViewLifecycleOwner(), state -> {
            updateAutoParkingUI(state);
            SliceReceiver.notifyChange(ProviderURI.AUTO_HOLD);
        });
        // 极致纯电
        viewModel.extremePureElectricLiveData.observe(getViewLifecycleOwner(), state -> {
            updateExtremePureElectricUI(state);
        });
        // 车身稳定控制
        viewModel.bodyStabilityControlLiveData.observe(getViewLifecycleOwner(), state -> {
            updateBodyStabilityControlUI(state);
            SliceReceiver.notifyChange(ProviderURI.ESP);
        });
        // 陡坡缓降
        viewModel.steepSlopeDescentLiveData.observe(getViewLifecycleOwner(), state -> {
            updateSteepSlopeDescentUI(state);
            SliceReceiver.notifyChange(ProviderURI.HDC);
        });
        // 悬架智能预瞄
        viewModel.intelligentSuspensionAimingLiveData.observe(getViewLifecycleOwner(), state -> {
            updateIntelligentSuspensionAimingUI(state);
        });
        viewModel.comfortBrakingLiveData.observe(getViewLifecycleOwner(), state -> {
            updateComfortBrakingUI(state);
        });
        // 舒适制动-舒适制动等级
        viewModel.comfortBrakingRankLiveData.observe(getViewLifecycleOwner(), state -> {
            updateComfortBrakingRankUI(state);
        });
        // 个性化-驾驶模式
        viewModel.drivingModeLiveData.observe(getViewLifecycleOwner(), state -> {
            updateDrivingModeUI(state);
        });
        // 个性化-悬挂模式
        viewModel.suspensionModeLiveData.observe(getViewLifecycleOwner(), state -> {
            updateSuspensionModeUI(state);
        });
        // 个性化-转向模式
        viewModel.swerveModeLiveData.observe(getViewLifecycleOwner(), state -> {
            updateSwerveModeUI(state);
        });
        // 个性化-制动模式
        viewModel.brakingModeLiveData.observe(getViewLifecycleOwner(), state -> {
            updateBrakingModeUI(state);
        });
        // 个性化-保电电量
        viewModel.powerProtectionLiveData.observe(getViewLifecycleOwner(), state -> {
            updatePowerProtectionUI(state);
        });
        // 牵引模式
        viewModel.tractionModeLiveData.observe(getViewLifecycleOwner(), state -> {
            updateTractionModeUI(state);
        });
        // 牵引模式置灰信号
        viewModel.tractionModeAvailableLiveData.observe(getViewLifecycleOwner(), state -> {
            updateModeAvailableUI(state);
        });
        viewModel.powerModeLiveData.observe(getViewLifecycleOwner(), status -> {
            // 电源模式更换
            updatePowerModeUI(status);
        });
        viewModel.vehicleSpeedLiveData.observe(getViewLifecycleOwner(), status -> {
            // 车速
            updateVehicleSpeedUI(status);
        });
        viewModel.gearPositionLiveData.observe(getViewLifecycleOwner(), status -> {
            // 档位
            updateGearPositionUI(status);
        });
        // 洗车模式 - 自动重上锁禁用
        viewModel.autoReLockInhibitLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateAutoReLockInhibitUI(signalVal);
        });
        // 洗车模式 - 外后视镜
        viewModel.outRearMirrorLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateOutRearMirrorUI(signalVal);
        });
        // 洗车模式 - 自动雨刮
        viewModel.autoWipingInhibitLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateAutoWipingInhibitUI(signalVal);
        });
        // 洗车模式 - 远离上锁
        viewModel.onLeaveLockInhibitLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateOnLeaveLockInhibitUI(signalVal);
        });
        // 洗车模式 - 电释放 - 外部自动关门
        viewModel.outAutoOpenLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateOutAutoOpen(signalVal);
        });
        // 洗车模式 - 空调内循环
        viewModel.autoCirculationModeDisplayStsLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateAutoCirculationModeDisplayStsUI(signalVal);
        });
        // 驾驶 - 方控键调节车辆模式
        viewModel.keyInputEventLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            if (signalVal == CarDriving.DrivingKeyInputEvent.SHORT_PRESS) {
                drivingHandler.removeMessages(MessageConst.DRIVING_MODE_KEY_EVENT_SERVICE);
                updateCarModeUIByInputEventShortPress();
            } else if (signalVal == CarDriving.DrivingKeyInputEvent.LONG_PRESS) {
                drivingHandler.sendMessageDelayed(MessageConst.DRIVING_MODE_KEY_EVENT_SERVICE, 1200);
            }
        });
        viewModel.driveStateLiveData.observe(getViewLifecycleOwner(), driveState -> {
            updateDriveState(driveState);
        });
        // 配置字 - 驾驶模式配置字
        viewModel.drivingModeConfigLiveData.observe(getViewLifecycleOwner(), drivingModeConfig -> {
            updateDrivingModeConfigUI(drivingModeConfig);
        });
    }

    private void updateDrivingModeConfigUI(Integer drivingModeConfig) {
        Log.d(TAG, "updateDrivingModeConfigUI: 配置字-驾驶模式配置字" + drivingModeConfig);
        if (drivingModeConfig == 0x9) {
            binding.llCarModeTitle.setVisibility(View.VISIBLE);
            binding.rlCarMode.setVisibility(View.VISIBLE);
        } else {
            binding.llCarModeTitle.setVisibility(View.VISIBLE);
            binding.rlCarMode.setVisibility(View.VISIBLE);
        }
    }

    //region UI更新

    private void updateCarModeUI(int signalVal) {
        int state = 0;
        if (signalVal == CarDriving.VCC_1_DriveMode.ECO) {      // 纯电优先
            state = 0;
            ThreeDModelUtil.setDriveModeState(Car3DModel.DriveMode.EV, null);
        } else if (signalVal == CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID) { // 节能混动
            state = 1;
            ThreeDModelUtil.setDriveModeState(Car3DModel.DriveMode.ECO, null);
        } else if (signalVal == CarDriving.VCC_1_DriveMode.NORMAL) { // 舒适混动
            state = 2;
            ThreeDModelUtil.setDriveModeState(Car3DModel.DriveMode.COMFORT, null);
        } else if (signalVal == CarDriving.VCC_1_DriveMode.SPORT) { // 风云GT
            state = 3;
            ThreeDModelUtil.setDriveModeState(Car3DModel.DriveMode.GT, null);
        } else if (signalVal == CarDriving.VCC_1_DriveMode.SNOW) { // 雨雪模式
            state = 4;
            ThreeDModelUtil.setDriveModeState(Car3DModel.DriveMode.SNOW, null);
        } else if (signalVal == CarDriving.VCC_1_DriveMode.INDIVIDUAL) { // 个性化设置
            state = 5;
            ThreeDModelUtil.setDriveModeState(Car3DModel.DriveMode.CUSTOM, null);
        }
        binding.tvPersonalizedTips.setVisibility(View.GONE);
        for (int i = 0; i < driveModeViews.size(); i++) {
            if (i == state) {
                driveModeViews.get(i).setBackgroundResource(drivingModeImages.get(i));
                TextView tv = (TextView) driveModeViews.get(i).getChildAt(0);
                tv.setTextColor(ContextCompat.getColor(mContext, R.color.white));
                TextView tv_2 = (TextView) driveModeViews.get(i).getChildAt(1);
                tv_2.setTextColor(ContextCompat.getColor(mContext, R.color.white_transparent_60));
            } else {
                driveModeViews.get(i).setBackgroundResource(R.drawable.shape_bg_white);
                TextView tv = (TextView) driveModeViews.get(i).getChildAt(0);
                tv.setTextColor(ContextCompat.getColor(mContext, R.color.black));
                TextView tv_2 = (TextView) driveModeViews.get(i).getChildAt(1);
                tv_2.setTextColor(ContextCompat.getColor(mContext, R.color.black_transparent_60));
            }
        }
        if (state != 5 && personalizedSettingUIAlert != null && personalizedSettingUIAlert.isShowing()) {
            personalizedSettingUIAlert.dismiss();
        }
        if (state == 0) {
            if (viewModel.powerModeLiveData.getValue() == CarDriving.FLZCU_9_PowerMode.ON) {
                GrayEffectUtils.removeGrayEffect(binding.llExtremePureElectric);
            }
        } else if (state == 1) {
            GrayEffectUtils.applyGrayEffect(binding.llExtremePureElectric);
        } else if (state == 2) {
            GrayEffectUtils.applyGrayEffect(binding.llExtremePureElectric);
        } else if (state == 3) {
            GrayEffectUtils.applyGrayEffect(binding.llExtremePureElectric);
        } else if (state == 4) {
            GrayEffectUtils.applyGrayEffect(binding.llExtremePureElectric);
        } else if (state == 5) {
            GrayEffectUtils.applyGrayEffect(binding.llExtremePureElectric);
            binding.tvPersonalizedTips.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 方控键切换车辆模式 - 短按
     */
    private void updateCarModeUIByInputEventShortPress() {
        int carMode = viewModel.carModeLiveData.getValue();
        switch (carMode) {
            case CarDriving.VCC_1_DriveMode.ECO:
                updateCarModeUI(CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID);
                break;
            case CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID:
                updateCarModeUI(CarDriving.VCC_1_DriveMode.NORMAL);
                break;
            case CarDriving.VCC_1_DriveMode.NORMAL:
                updateCarModeUI(CarDriving.VCC_1_DriveMode.SPORT);
                break;
            case CarDriving.VCC_1_DriveMode.SPORT:
                updateCarModeUI(CarDriving.VCC_1_DriveMode.SNOW);
                break;
            case CarDriving.VCC_1_DriveMode.SNOW:
                updateCarModeUI(CarDriving.VCC_1_DriveMode.INDIVIDUAL);
                break;
            case CarDriving.VCC_1_DriveMode.INDIVIDUAL:
                updateCarModeUI(CarDriving.VCC_1_DriveMode.ECO);
                break;
        }
        drivingHandler.sendMessageDelayed(MessageConst.DRIVING_CAR_MODE);
    }


    /**
     * 方控键切换车辆模式 - 长按
     */
    private void updateCarModeUIByInputEventLongPress() {
        updateCarModeUI(CarDriving.VCC_1_DriveMode.SPORT);
        drivingHandler.sendMessageDelayed(MessageConst.DRIVING_CAR_MODE);
    }

    /**
     * 疲劳检测
     */
    private void updateFatigueUI() {
        if (tiredReminderUIAlert != null && tiredReminderUIAlert.isShowing()) {
            tiredReminderUIAlert.updateFatigueUI(fatigueDetectionStatus);
        }
    }

    /**
     * 视线分心提醒
     */
    private void updateDistractionDriverUI() {
        if (tiredReminderUIAlert != null && tiredReminderUIAlert.isShowing()) {
            tiredReminderUIAlert.updateDistractionDriverUI(distractionStatus);
        }
    }

    /**
     * 主驾打电话提醒
     */
    private void updateDistractionCallsUI() {
        if (tiredReminderUIAlert != null && tiredReminderUIAlert.isShowing()) {
            tiredReminderUIAlert.updateDistractionCallsUI(callStatus);
        }
    }

    /**
     * 洗车模式 - 空调内循环
     *
     * @param signalVal
     */
    private void updateAutoCirculationModeDisplayStsUI(Integer signalVal) {
        if (regularWashUIAlertBuilder != null) {
            regularWashUIAlertBuilder.updateAirLoop(signalVal);
        }
    }

    /**
     * 洗车模式 - 电释放 - 外部自动关门
     *
     * @param signalVal
     */
    private void updateOutAutoOpen(Integer signalVal) {
        if (regularWashUIAlertBuilder != null) {
            regularWashUIAlertBuilder.updateOutAutoOpen(signalVal);
        }
    }

    /**
     * 洗车模式 - 远离上锁
     *
     * @param signalVal
     */
    private void updateOnLeaveLockInhibitUI(Integer signalVal) {
        if (regularWashUIAlertBuilder != null) {
            regularWashUIAlertBuilder.updateAvoidLock(signalVal);
        }
    }

    /**
     * 洗车模式 - 自动雨刮
     *
     * @param signalVal
     */
    private void updateAutoWipingInhibitUI(Integer signalVal) {
        if (regularWashUIAlertBuilder != null) {
            regularWashUIAlertBuilder.updateAutoWipingInhibit(signalVal);
        }
    }

    /**
     * 洗车模式 - 外后视镜
     *
     * @param signalVal
     */
    private void updateOutRearMirrorUI(Integer signalVal) {
        if (regularWashUIAlertBuilder != null) {
            regularWashUIAlertBuilder.updateOutRearMirror(signalVal);
        }
    }

    /**
     * 洗车模式 - 自动重上锁禁用
     *
     * @param signalVal
     */
    private void updateAutoReLockInhibitUI(Integer signalVal) {
        if (regularWashUIAlertBuilder != null) {
            regularWashUIAlertBuilder.updateAutoCloseLock(signalVal);
        }
    }

    private void updateGearPositionUI(Integer signalVal) {

    }

    /**
     * 车速改变UI状态
     *
     * @param signalVal
     */
    private void updateVehicleSpeedUI(Integer signalVal) {
        // 车速更改洗车模式
        if (washCarModeUIAlertBuilder != null) {
            washCarModeUIAlertBuilder.updateCarWashMode();
        }
        // 车速自动退出洗车模式
        if (viewModel.cleanModeStatusLiveData.getValue() == CarDriving.FLZCU_CleanModeStatus.ON && signalVal >= 15) {
            viewModel.setCleanModeStatus(CarDriving.ICC_CleanMode.OFF);
        }
    }


    private void updatePowerModeUI(Integer signalVal) {
        // 电源模式 change 陡坡缓降
        if (signalVal == CarDriving.FLZCU_9_PowerMode.ON) {
            GrayEffectHelper.clearGrayScale(binding.llSteepSlopeDescent);
            binding.vwHdc.setVisibility(View.GONE);
        } else {
            GrayEffectHelper.setGrayScale(binding.llSteepSlopeDescent, 0.5f);
            binding.vwHdc.setVisibility(View.VISIBLE);
        }

        // 电源模式 change 驻车制动
        if (signalVal == CarDriving.FLZCU_9_PowerMode.OFF || signalVal == CarDriving.FLZCU_9_PowerMode.COMFORTABLE) {
            GrayEffectUtils.applyGrayEffect(binding.llParkingBrake);
        } else {
            GrayEffectUtils.removeGrayEffect(binding.llParkingBrake);
        }

        // 车辆模式
        if (signalVal != CarDriving.FLZCU_9_PowerMode.ON) {
            for (View driveModeView : driveModeViews) {
                GrayEffectHelper.setGrayScale(driveModeView, 0.5f);
            }
            // 纯电优先不需要置灰
            GrayEffectHelper.clearGrayScale(driveModeViews.get(0));
        } else if (signalVal == CarDriving.FLZCU_9_PowerMode.ON) {
            for (View driveModeView : driveModeViews) {
                GrayEffectHelper.clearGrayScale(driveModeView);
            }
        }

        // 极致纯电
        if (signalVal != CarDriving.FLZCU_9_PowerMode.ON) {
            GrayEffectHelper.setGrayScale(binding.llExtremePureElectric, 0.5f);
            binding.vwExtremePureElectric.setVisibility(View.VISIBLE);
        } else {
            GrayEffectHelper.clearGrayScale(binding.llExtremePureElectric);
            binding.vwExtremePureElectric.setVisibility(View.GONE);
        }
        // 车身稳定控制
        if (signalVal != CarDriving.FLZCU_9_PowerMode.ON) {
            GrayEffectHelper.setGrayScale(binding.llBodyStabilityControl, 0.5f);
            binding.vwBodyStabilityControl.setVisibility(View.VISIBLE);
        } else {
            GrayEffectHelper.clearGrayScale(binding.llBodyStabilityControl);
            binding.vwBodyStabilityControl.setVisibility(View.GONE);
        }

        // 自动驻车
        if (signalVal != CarDriving.FLZCU_9_PowerMode.ON) {
            GrayEffectHelper.setGrayScale(binding.llAutoParking, 0.5f);
            binding.vwAutoParking.setVisibility(View.VISIBLE);
        } else {
            GrayEffectHelper.clearGrayScale(binding.llAutoParking);
            binding.vwAutoParking.setVisibility(View.GONE);
        }

        // 驻车制动
        if (signalVal != CarDriving.FLZCU_9_PowerMode.ON) {
            GrayEffectHelper.setGrayScale(binding.llParkingBrake, 0.5f);
            binding.vwParkingBrake.setVisibility(View.VISIBLE);
        } else {
            GrayEffectHelper.clearGrayScale(binding.llParkingBrake);
            binding.vwParkingBrake.setVisibility(View.GONE);
        }

        // 舒适制动
        if (signalVal != CarDriving.FLZCU_9_PowerMode.ON) {
            GrayEffectHelper.setGrayScale(binding.llComfortBraking, 0.5f);
            GrayEffectHelper.setEnableExcludeViews(binding.llComfortBraking, false, Arrays.asList(binding.ivComfortBraking));
            binding.vwComfort.setVisibility(View.VISIBLE);
        } else {
            GrayEffectHelper.clearGrayScale(binding.llComfortBraking);
            binding.vwComfort.setVisibility(View.GONE);
            GrayEffectHelper.setEnableExcludeViews(binding.llComfortBraking, true, null);
        }

        // 悬架智能预瞄
        if (signalVal != CarDriving.FLZCU_9_PowerMode.ON) {
            GrayEffectHelper.setGrayScale(binding.llIntelligentSuspensionPreview, 0.5f);
            binding.vwIntelligentSuspensionPreview.setVisibility(View.VISIBLE);
        } else {
            GrayEffectHelper.clearGrayScale(binding.llIntelligentSuspensionPreview);
            binding.vwIntelligentSuspensionPreview.setVisibility(View.GONE);
        }
    }

    private void updateDriveState(Integer signalVal) {
        if (tiredReminderUIAlert != null) {
            tiredReminderUIAlert.getBinding().swDriverState.setChecked(signalVal == 1);
            tiredReminderUIAlert.operateClickable(signalVal == 1);
        }
    }

    private void updateTractionModeUI(Integer signalVal) {
        binding.swTractionMode.setChecked(signalVal == 1);
        Log.d(TAG, "tractionMode: " + signalVal);
    }

    private void updatePowerProtectionUI(Integer signalVal) {
        if (personalizedSettingUIAlert != null) {
            personalizedSettingUIAlert.updatePowerProtectionUI(signalVal);
        }
    }

    private void updateBrakingModeUI(Integer signalVal) {
        if (personalizedSettingUIAlert != null) {
            personalizedSettingUIAlert.updateBrakingModeUI(signalVal);
        }
    }

    private void updateSwerveModeUI(Integer signalVal) {
        if (personalizedSettingUIAlert != null) {
            personalizedSettingUIAlert.updateSwerveModeUI(signalVal);
        }
    }

    private void updateSuspensionModeUI(Integer signalVal) {
        if (personalizedSettingUIAlert != null) {
            personalizedSettingUIAlert.updateSuspensionModeUI(signalVal);
        }
    }

    private void updateDrivingModeUI(Integer signalVal) {
        if (personalizedSettingUIAlert != null) {
            personalizedSettingUIAlert.updateDrivingModeUI(signalVal, true);
        }
    }

    private void updateComfortBrakingRankUI(Integer signalVal) {
        if (comfortBrakingUIAlert != null) {
            comfortBrakingUIAlert.updateComfortBrakingRankUI(signalVal);
        }
    }

    private void updateIntelligentSuspensionAimingUI(Integer signalVal) {
//        ASU_1_PreviewContFb=0x1: Active，悬架智能预瞄功能开启
//        ASU_1_PreviewContFb=0x0: Inactive，悬架智能预瞄功能关闭；
        binding.swIntelligentSuspensionPreview.setChecked(signalVal == 1);
    }

    private void updateParkingBrakeUI(Integer signalVal) {
        if (signalVal == CarDriving.EPBActrSt.APPLIED) {
            binding.swParkingBrake.setChecked(true);
        } else if (signalVal == CarDriving.EPBActrSt.RELEASED || signalVal == CarDriving.EPBActrSt.COMPLETELY_RELEASED) {
            binding.swParkingBrake.setChecked(false);
        }
    }

    private void updateBodyStabilityControlUI(Integer signalVal) {
        if (signalVal == CarDriving.ESPSwitchStatus.ON) {
            binding.swBodyStabilityControl.setChecked(true);
        } else if (signalVal == CarDriving.ESPSwitchStatus.OFF) {
            binding.swBodyStabilityControl.setChecked(false);
        }
    }

    private void updateSteepSlopeDescentUI(Integer signalVal) {
        // 陡坡缓降
        if (signalVal == CarDriving.HDCCtrlSts.NOT_ACTIVE || signalVal == CarDriving.HDCCtrlSts.ON_ACTIVE_BRAKING) {
            binding.swSteepSlopeDescent.setChecked(true);
        } else if (signalVal == CarDriving.HDCCtrlSts.OFF) {
            binding.swSteepSlopeDescent.setChecked(false);
        }
    }

    private void updateExtremePureElectricUI(Integer signalVal) {
        // 极致纯电
        binding.swExtremePureElectric.setChecked(signalVal == 1);
    }

    private void updateAutoParkingUI(Integer signalVal) {
        if (signalVal == CarDriving.AVHSts.STANDBY || signalVal == CarDriving.AVHSts.ACTIVE) {
            binding.swAutomaticParking.setChecked(true);
        } else {
            binding.swAutomaticParking.setChecked(false);
        }
    }

    private void updateComfortBrakingUI(int signalVal) {
        Log.d(TAG, "updateComfortBrakingUI: " + signalVal);
        binding.vwComfort.setVisibility(View.GONE);
        GrayEffectHelper.clearGrayScale(binding.llComfortBraking);
        if (viewModel.powerModeLiveData.getValue() != CarDriving.FLZCU_9_PowerMode.ON) {
            binding.vwComfort.setVisibility(View.VISIBLE);
            GrayEffectHelper.setGrayScale(binding.llComfortBraking, 0.5f);
            GrayEffectHelper.setEnable(binding.llComfortBraking, false);
        }
        if (signalVal == CarDriving.CST_Status.STANDBY || signalVal == CarDriving.CST_Status.ACTIVE) {
            binding.swComfortBraking.setChecked(true);
        } else if (signalVal == CarDriving.CST_Status.FAILURE) {
            binding.vwComfort.setVisibility(View.VISIBLE);
            GrayEffectHelper.setGrayScale(binding.llComfortBraking, 0.5f);
            GrayEffectHelper.setEnable(binding.llComfortBraking, false);
        } else {
            binding.swComfortBraking.setChecked(false);
        }
        if (comfortBrakingUIAlert != null) {
            comfortBrakingUIAlert.updateComfortBrakingUI(signalVal);
            comfortBrakingUIAlert.updateComfortBrakingRankUI(viewModel.comfortBrakingRankLiveData.getValue());
        }
    }
    // endregion

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.DRIVING_CAR_MODE: // 车辆模式
                viewModel.getCarMode();
                break;
            // 极致纯电
            case MessageConst.QUICK_EXTREME_PURE_ELECTRIC:
                viewModel.getExtremePureSignal();
                break;
            // 舒适制动
            case MessageConst.COMFORT_BRAKING:
                viewModel.getComfortBraking();
                break;
            // 自动驻车
            case MessageConst.AUTO_PARKING:
                viewModel.getAutoParking();
                break;
            // 驻车制动
            case MessageConst.PARKING_BRAKE:
                viewModel.getParkingBrake();
                break;
            // 车身稳定控制
            case MessageConst.BODY_STABILITY_CONTROL:
                viewModel.getBodyStabilityControl();
                break;
            // 陡坡缓降
            case MessageConst.STEEP_SLOPE_DESCENT:
                viewModel.getSteepSlopeDescent();
                break;
            // 悬架智能预瞄
            case MessageConst.INTEL_SUSPENSION_AIMING:
                viewModel.getIntelligentSuspensionAiming();
                break;
            // 舒适制动等级
            case MessageConst.COMFORT_BRAKING_RANK:
                viewModel.getComfortBrakingRank();
                break;
            // 个性化-驾驶模式
            case MessageConst.DRIVING_DRIVING_MODE:
                viewModel.getDrivingMode();
                break;
            // 个性化-悬挂模式
            case MessageConst.DRIVING_SUSPENSION_MODE:
                viewModel.getSuspensionMode();
                break;
            // 个性化-转向模式
            case MessageConst.DRIVING_SWERVE_MODE:
                viewModel.getSwerveMode();
                break;
            // 个性化-制动模式
            case MessageConst.DRIVING_BRAKING_MODE:
                viewModel.getBrakingMode();
                break;
            // 个性化-保电电量
            case MessageConst.DRIVING_POWER_PROTECTION:
                viewModel.getPowerProtection();
                break;
            // 牵引模式
            case MessageConst.TRACTION_MODE:
                viewModel.getTractionMode();
                break;
            case MessageConst.RECOGNITION_DMS_CAMERA:
                int cameraStatus = viewModel.driveStateLiveData.getValue() == 1 ? 0 : 1;
                Log.d("RecognitionFragment", "摄像头状态:" + cameraStatus);
                if (cameraStatus == viewModel.driveStateLiveData.getValue()) {
                    Log.d(TAG, "摄像头设置失败");
                    viewModel.driveStateLiveData.postValue(cameraStatus);
                    if (cameraStatus == 1) {
                        if (cameraSwitchOffUIAlert != null) {
                            cameraSwitchOffUIAlert.openFail();
                        }
                    } else if (cameraStatus == 0) {
                        if (cameraSwitchOnUIAlert != null) {
                            cameraSwitchOnUIAlert.openFail();
                        }
                    }
                } else {
                    Log.d(TAG, "摄像头设置成功");
                    if (cameraSwitchOnUIAlert != null && cameraSwitchOnUIAlert.isShowing()) {
                        cameraSwitchOnUIAlert.openSuccess();
                        cameraStatus = 1;
                    }
                    if (cameraSwitchOffUIAlert != null && cameraSwitchOffUIAlert.isShowing()) {
                        cameraSwitchOffUIAlert.openSuccess();
                        cameraStatus = 0;
                    }
                    viewModel.driveStateLiveData.postValue(cameraStatus);
                }
                break;
                // 方控键切换车辆模式UI更新
                case MessageConst.DRIVING_MODE_KEY_EVENT_SERVICE:
                    updateCarModeUIByInputEventLongPress();
                    break;
            default:
                break;
        }
    }

    @Override
    public boolean isActive() {
        return isActive;
    }

    private void processTargetDialogEvent(TargetDialogInfo targetDialog) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= " + targetDialog);
        if (targetDialog == null) {
            return;
        }
        //具体Tab索引
        if (targetDialog.getTargetTab() == MainActivity.MainTabIndex.DRIVE) {
            switch (targetDialog.getTargetDialog()) {
                //常量-具体窗口
                case DRIVE_PERSONALIZE_DIALOG:
                    //常量-操作
                    if (targetDialog.getOperation() == 1) {
                        if (personalizedSettingUIAlert == null) {
                            personalizedSettingUIAlert = new PersonalizedSettingUIAlert.Builder(mContext);
                        }
                        personalizedSettingUIAlert.create().show();
                    } else {
                        if (personalizedSettingUIAlert != null) {
                            personalizedSettingUIAlert.dismiss();
                        }
                    }
                    break;
                case DRIVE_WASH_MODE_DIALOG:
                    if (targetDialog.getOperation() == 1) {
                        if (washCarModeUIAlertBuilder == null) {
                            washCarModeUIAlertBuilder = new WashCarModeUIAlert.Builder(mContext);
                        }
                        washCarModeUIAlertBuilder.create().show();
                    } else {
                        if (washCarModeUIAlertBuilder != null) {
                            washCarModeUIAlertBuilder.dismiss();
                        }
                    }
                    break;
                case DRIVE_WASH_MODE_START_DIALOG:
                    if (regularWashUIAlertBuilder == null) {
                        regularWashUIAlertBuilder = new RegularWashUIAlert.Builder(getContext());
                    }
                    if (targetDialog.getOperation() == 1) {
                        if (regularWashUIAlertBuilder != null) {
                            regularWashUIAlert = regularWashUIAlertBuilder.create();
                            if (regularWashUIAlert != null && !regularWashUIAlert.isShowing()) {
                                regularWashUIAlert.show();
                            }
                        }
                    } else {
                        if (regularWashUIAlert != null) {
                            regularWashUIAlert.dismiss();
                        }
                    }
                    break;
            }
        }
    }

    @Override
    public void onStop() {
        if (personalizedSettingUIAlert != null) {
            personalizedSettingUIAlert.dismiss();
        }
        if (washCarModeUIAlertBuilder != null) {
            washCarModeUIAlertBuilder.dismiss();
        }
        if (regularWashUIAlert != null) {
            regularWashUIAlert.dismiss();
        }
        super.onStop();
    }

    @Override
    public void onPause() {
        // 3d车模返回车控视角
        VehicleServiceManager.startCameraStatusPollingTask(Car3DModel.ControlModelState.CONTROL_MODEL_STATE_ID, Car3DModel.ControlModelState.CONTROL);
        super.onPause();
        Log.d(TAG, "onPause: ");
        // 埋点上报数据
        DataPoint dataPoint = new DataPoint();
        dataPoint.setId(CommonConst.DataPoint.id);
        dataPoint.setEventId(CommonConst.EventId.Drive_Set);
        dataPoint.setTimestamp(System.currentTimeMillis());
        dataPoint.setSupplierCode(CommonConst.DataPoint.supplierCode);
        dataPoint.setPlatformCode(CommonConst.DataPoint.platformCode);
        dataPoint.setNodeType(LogDataUtil.NODE_TYPE_ADD_OPERATION);
        // 上报数据驾驶页面
        ArrayList<Content> list = getData();
        if (!list.isEmpty()) {
            dataPoint.setContent(list);
        }

        LiveEventBus
                .get(DataPointReportLifeCycle.KEY_DATA_POINT)
                .post(dataPoint);
    }

    //region 埋点数据上报
    private ArrayList<Content> getData() {
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        // 驾驶 - 牵引模式
        dataList.add(reportData(CommonConst.CodeId.ZB141505, CommonConst.Att.TractionModeSw, String.valueOf(viewModel.tractionModeLiveData.getValue() == CarDriving.VCU_TowingMode.ACTIVE ? 1 : 0)));
        // 驾驶 - 车辆模式
        String carModePointData = "1";
        switch (viewModel.carModeLiveData.getValue()) {
            case CarDriving.VCC_1_DriveMode.ECO:
                if (viewModel.extremePureElectricLiveData.getValue() == CarDriving.ForcedEVMode.ACTIVE) {
                    carModePointData = "7";
                } else {
                    carModePointData = "1";
                }
                break;
            case CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID:
                carModePointData = "2";
                break;
            case CarDriving.VCC_1_DriveMode.NORMAL:
                carModePointData = "3";
                break;
            case CarDriving.VCC_1_DriveMode.SPORT:
                carModePointData = "4";
                break;
            case CarDriving.VCC_1_DriveMode.SNOW:
                carModePointData = "5";
                break;
            case CarDriving.VCC_1_DriveMode.INDIVIDUAL:
                carModePointData = "6";
                break;
        }
        dataList.add(reportData(CommonConst.CodeId.ZB141506, CommonConst.Att.VehicleModeSet, carModePointData));
        // 驾驶 - 个性化 - 驾驶模式
        String drivingModePointData = "1";
        switch (viewModel.drivingModeLiveData.getValue()) {
            case CarDriving.FLZCU_PropulsionMode.COMFORTABLE:
                drivingModePointData = "1";
                break;
            case CarDriving.FLZCU_PropulsionMode.NORMAL:
                drivingModePointData = "2";
                break;
            case CarDriving.FLZCU_PropulsionMode.SPORT:
                drivingModePointData = "3";
                break;
            case CarDriving.FLZCU_PropulsionMode.SNOW:
                drivingModePointData = "4";
        }
        dataList.add(reportData(CommonConst.CodeId.ZB141506, CommonConst.Att.DriveModeSet, drivingModePointData));
        // 驾驶 - 个性化 - 悬架模式
        dataList.add(reportData(CommonConst.CodeId.ZB141506, CommonConst.Att.SuspensionMode, String.valueOf(viewModel.suspensionModeLiveData.getValue() == CarDriving.FLZCU_SuspensionDamping.SOFT ? 1 : 2)));
        // 驾驶 - 个性化 - 转向模式
        dataList.add(reportData(CommonConst.CodeId.ZB141506, CommonConst.Att.SteerModeSet, String.valueOf(viewModel.swerveModeLiveData.getValue() == CarDriving.FLZCU_SteeringMode.NORMAL ? 1 : 2)));
        // 驾驶 - 个性化 - 制动模式
        dataList.add(reportData(CommonConst.CodeId.ZB141506, CommonConst.Att.BreakModeSet, String.valueOf(viewModel.brakingModeLiveData.getValue() == CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE ? 1 : 2)));
        // 驾驶 - 个性化 - 保电电量
        dataList.add(reportData(CommonConst.CodeId.ZB141506, CommonConst.Att.PowCSet, String.valueOf(viewModel.powerProtectionLiveData.getValue() + "%")));
        // 驾驶 - 极致纯电
        dataList.add(reportData(CommonConst.CodeId.ZB141507, CommonConst.Att.UltElectricSw, String.valueOf(viewModel.extremePureElectricLiveData.getValue() == CarDriving.ForcedEVMode.ACTIVE ? 1 : 0)));
        // 驾驶 - 舒适制动
        String comfortBrakingPointData = "0";
        if (viewModel.comfortBrakingLiveData.getValue() != null) {
            Integer signalVal = viewModel.comfortBrakingLiveData.getValue();
            if (signalVal == CarDriving.CST_Status.ACTIVE || signalVal == CarDriving.CST_Status.STANDBY) {
                comfortBrakingPointData = "1";
            } else if (signalVal == CarDriving.CST_Status.DISABLED) {
                comfortBrakingPointData = "0";
            }
        }
        dataList.add(reportData(CommonConst.CodeId.ZB141508, CommonConst.Att.ComBreakSw, comfortBrakingPointData));
        // 驾驶 - 舒适制动等级
        String comfortBrakingRankPointData = String.valueOf(CarDriving.CST_SensitivitySts.HIGH);
        if (viewModel.comfortBrakingRankLiveData.getValue() != null) {
            Integer rank = viewModel.comfortBrakingRankLiveData.getValue();
            if (rank == CarDriving.CST_SensitivitySts.LOW) {
                comfortBrakingRankPointData = "1";
            } else if (rank == CarDriving.CST_SensitivitySts.MEDIUM) {
                comfortBrakingRankPointData = "2";
            } else if (rank == CarDriving.CST_SensitivitySts.HIGH) {
                comfortBrakingRankPointData = "3";
            }
        }
        dataList.add(reportData(CommonConst.CodeId.ZB141508, CommonConst.Att.ComBreakSet, comfortBrakingRankPointData));
        // 驾驶 - 自动驻车
        String autoParkingPointData = String.valueOf(CarDriving.AVHSts.OFF);
        if (viewModel.autoParkingLiveData.getValue() != null) {
            Integer status = viewModel.autoParkingLiveData.getValue();
            if (status == CarDriving.AVHSts.STANDBY || status == CarDriving.AVHSts.ACTIVE) {
                autoParkingPointData = "1";
            } else if (status == CarDriving.AVHSts.OFF) {
                autoParkingPointData = "0";
            }
        }
        dataList.add(reportData(CommonConst.CodeId.ZB141509, CommonConst.Att.ATPSw, autoParkingPointData));
        // 驾驶 - 驻车制动
        String parkingBrakePointData = String.valueOf(CarDriving.EPBActrSt.RELEASED);
        if (viewModel.parkingBrakeLiveData.getValue() != null) {
            Integer status = viewModel.parkingBrakeLiveData.getValue();
            if (status == CarDriving.EPBActrSt.COMPLETELY_RELEASED || status == CarDriving.EPBActrSt.RELEASED) {
                parkingBrakePointData = "0";
            } else if (status == CarDriving.EPBActrSt.APPLIED) {
                parkingBrakePointData = "1";
            }
        }
        dataList.add(reportData(CommonConst.CodeId.ZB141510, CommonConst.Att.ParBraSw, parkingBrakePointData));
        // 驾驶 - 车身稳定控制
        dataList.add(reportData(CommonConst.CodeId.ZB141511, CommonConst.Att.VSCSw, String.valueOf(viewModel.bodyStabilityControlLiveData.getValue() == CarDriving.ESPSwitchStatus.ON ? 1 : 0)));
        // 驾驶 - 陡坡缓降
        dataList.add(reportData(CommonConst.CodeId.ZB141512, CommonConst.Att.SSDSw, String.valueOf(viewModel.steepSlopeDescentLiveData.getValue() == CarDriving.HDCCtrlSts.OFF ? 0 : 1)));
        return dataList;
    }

    private Content reportData(String attributeId, String locationId, String attributeValue) {
        Content content = new Content();
        content.setAttributeId(attributeId);
        content.setLocationId(locationId);
        content.setAttributeValue(attributeValue);
        return content;
    }
    //endregion
}