package com.bitech.vehiclesettings.view.connect;

import static com.bitech.platformlib.bean.Topics.Connect.PHONE_CHARGE_STATUS;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.interfaces.connect.IConnectManagerListener;
import com.bitech.platformlib.manager.ConnectManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.carapi.constants.CarConnect;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectWirelessChargingBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectWirelessChargingLauncherBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class WirelessChargingLauncherUIAlert extends BaseDialog {
    private static final String TAG = WirelessChargingLauncherUIAlert.class.getSimpleName();
    private static WirelessChargingLauncherUIAlert.onProgressChangedListener onProgressChangedListener;

    public WirelessChargingLauncherUIAlert(@NonNull Context context) {
        super(context);
    }

    public WirelessChargingLauncherUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected WirelessChargingLauncherUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static WirelessChargingLauncherUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(WirelessChargingLauncherUIAlert.onProgressChangedListener onProgressChangedListener) {
        WirelessChargingLauncherUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private WirelessChargingLauncherUIAlert.OnDialogResultListener listener;

    public static class Builder implements SafeHandlerCallback {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConnectWirelessChargingLauncherBinding binding;
        boolean globalAlert = false;
        private boolean isActive;
        private SafeHandler handler;
        public boolean isBlueOpen() {
            return isBlueOpen;
        }
        private final Handler mainHandler = new Handler(Looper.getMainLooper());

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        Integer chargeStatus = CarConnect.CWC_workingSts.CWC_ON;
        Integer phoneChargeStatus = CarConnect.CWC_ChargingSts.NO_CHARGING;

        ChargingRemindLauncherUIAlert.Builder chargingRemindLauncherUIAlert;


        private boolean isBlueOpen = false;
        private WirelessChargingLauncherUIAlert dialog = null;
        public Builder(Context context) {
            this.context = context;
        }

        private final ConnectManager connectManager = (ConnectManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_CONNECT_MANAGER);

        public WirelessChargingLauncherUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setGlobalAlert(boolean b){
            globalAlert = b;
        }
        /**
         * Create the custom dialog
         */
        public void addCallback() {
            connectManager.addCallback(TAG, new IConnectManagerListener() {
                // 无线充电状态
                @Override
                public void onFrontChargingChanged(int signVal) {
                    if (signVal == Integer.MIN_VALUE) return;
                    chargeStatus = signVal;
                    mainHandler.post(() -> {
                        updateFrontCharging(chargeStatus);
                    });
                }
                // 手机充电状态
                @Override
                public void onPhoneChargeChanged(int signVal) {
                    if (signVal == Integer.MIN_VALUE) return;
                    phoneChargeStatus = signVal;
                    mainHandler.post(() -> {
                        updatePhoneCharging(phoneChargeStatus);
                    });
                }
            });
            connectManager.registerListener();
        }

        private void updatePhoneCharging(Integer signalVal) {
            if (chargeStatus == 0) return;
            if (signalVal == CarConnect.CWC_ChargingSts.NO_CHARGING) {
                binding.tvChargeStatus.setText(context.getString(R.string.str_str_wireless_charging_open));
                binding.ivChargeStatus.setBackgroundResource(R.drawable.img_charge_status_1);
            } else if (signalVal == CarConnect.CWC_ChargingSts.CHARGING) {
                binding.tvChargeStatus.setText(context.getString(R.string.str_connect_charging));
                binding.ivChargeStatus.setBackgroundResource(R.drawable.img_charge_status_2);
            } else if (signalVal == CarConnect.CWC_ChargingSts.CHARGING_COMPLETED) {
                binding.tvChargeStatus.setText(context.getString(R.string.str_wireless_charging_ok_launcher));
                binding.ivChargeStatus.setBackgroundResource(R.drawable.img_charge_status_3);
            } else if (signalVal == CarConnect.CWC_ChargingSts.CHARGING_FAULT) {
                binding.tvChargeStatus.setText(context.getString(R.string.str_wireless_charging_error_launcher));
                binding.ivChargeStatus.setBackgroundResource(R.drawable.img_charge_status_4);
            }
        }

        // 更新无线充电状态
        private void updateFrontCharging(int signalVal) {
            if (signalVal == CarConnect.CWC_workingSts.CWC_OFF) {
                binding.swFront.setChecked(false);
                binding.tvChargeStatus.setText(context.getString(R.string.str_str_wireless_charging_close));
                binding.ivChargeStatus.setBackgroundResource(R.drawable.img_charge_status_0);
            }else if (signalVal == CarConnect.CWC_workingSts.CWC_ON) {
                binding.swFront.setChecked(true);
                phoneChargeStatus = connectManager.getPhoneChargeStatus();
                updatePhoneCharging(phoneChargeStatus);
            }
        }

        public WirelessChargingLauncherUIAlert create() {
            // instantiate the dialog with the custom Theme
            int themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue);
            Context themedContext = new ContextThemeWrapper(context, themeId);
            if (dialog == null)
                dialog = new WirelessChargingLauncherUIAlert(themedContext, R.style.Dialog);
            // 设置dialog的bind
            binding = DialogAlertConnectWirelessChargingLauncherBinding.inflate(LayoutInflater.from(themedContext));
            context.setTheme(Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue));
            dialog.setContentView(binding.getRoot());
            dialog.setCancelable(isCan);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            // 不让背景变暗
            window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            layoutParams.dimAmount = 0f;
            layoutParams.type = globalAlert ?WindowManager.LayoutParams.TYPE_SYSTEM_ALERT: WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            layoutParams.width = 644;
            layoutParams.height = 384;
            window.setAttributes(layoutParams);
            handler = new SafeHandler(this);
            initData();
            isActive = true;
            initListener();
            addCallback();
            return dialog;
        }

        private void initListener() {
            binding.ivChargeTips.setOnClickListener(v -> {
                // 修改：保存 tipsDialog 的引用
                WirelessChargingTipsLauncherUIAlert.Builder detailsUIAlert =
                        new WirelessChargingTipsLauncherUIAlert.Builder(context);
                detailsUIAlert.setGlobalAlert(true);
                WirelessChargingTipsLauncherUIAlert wirelessChargingTipsLauncherUIAlert = detailsUIAlert.create(); // 保存实例
                wirelessChargingTipsLauncherUIAlert.show();
                dialog.dismiss();
            });
            binding.swFront.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked) {
                    if (chargeStatus == CarConnect.CWC_workingSts.CWC_OFF) {
                        connectManager.setWirelessCharge(CarConnect.ICC_CWCWorkingStsSet.ON);
                        handler.sendMessageDelayed(MessageConst.CONNECT_WIRELESS_CHARGING_LAUNCHER);
                    }
                }else if (!isChecked) {
                    if (chargeStatus == CarConnect.CWC_workingSts.CWC_ON) {
                        connectManager.setWirelessCharge(CarConnect.ICC_CWCWorkingStsSet.OFF);
                        handler.sendMessageDelayed(MessageConst.CONNECT_WIRELESS_CHARGING_LAUNCHER);
                    }
                }
            });
        }

        public void setPosition(int x, int y) {
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            // 设置对齐方式为左上角
            layoutParams.gravity = Gravity.TOP | Gravity.START;
            // 设置偏移量（相对于 gravity）
            layoutParams.x = x;
            layoutParams.y = y;
        }

        private void initData() {
            int value = connectManager.getWirelessCharge();
            chargeStatus = value == Integer.MIN_VALUE ? CarConnect.CWC_workingSts.CWC_ON : value;
            updateFrontCharging(chargeStatus);

            value = connectManager.getPhoneChargeStatus();
            phoneChargeStatus = value == Integer.MIN_VALUE ? CarConnect.CWC_ChargingSts.NO_CHARGING : value;
            updatePhoneCharging(phoneChargeStatus);
        }

        @Override
        public void handleSafeMessage(Message msg) {
            switch (msg.what) {
                case MessageConst.CONNECT_WIRELESS_CHARGING_LAUNCHER:
                    // 获得前排充电状态
                    int value = connectManager.getWirelessCharge();
                    chargeStatus = value == Integer.MIN_VALUE ? chargeStatus : value;
                    updateFrontCharging(chargeStatus);
                    break;
            }
        }

        @Override
        public boolean isActive() {
            return isActive;
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        // 发送弹窗关闭广播
        Intent dismissIntent = new Intent("com.chery.systemui.action.WIRELESS_CHARGE_DIALOG_DISMISS");
        getContext().sendBroadcast(dismissIntent);
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
    }
}
