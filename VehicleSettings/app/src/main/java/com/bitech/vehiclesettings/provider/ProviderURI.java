package com.bitech.vehiclesettings.provider;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

public class ProviderURI {
    public static String PREFIX = "content://";

    // 车辆下电
    public static final String POWER_OFF = "com.chery.carsettings.provider.slice.panel.power.function";
    // 屏幕清洁
    public static final String CLEAN_SCREEN = "com.chery.carsettings.provider.slice.panel.clean_screen.function";
    // 亮度
    public static final String LIGHT = "com.chery.carsettings.provider.slice.panel.brightness.seekbar";
    // 声音
    public static final String VOICE = "com.chery.carsettings.provider.slice.panel.sound.seekbar";
    // 加油口解锁
    public static final String UNLOCK_FUEL_PORT = "com.chery.carsettings.provider.slice.panel.oil.switch";
    // 息屏
    public static final String LOCK_SCREEN = "com.chery.carsettings.provider.slice.panel.lock_screen.function";
    // 驻车制动
    public static final String EPB = "com.chery.carsettings.provider.slice.panel.epb.switch";
    // ESP
    public static final String ESP = "com.chery.carsettings.provider.slice.panel.esp.switch";
    // 陡坡缓降
    public static final String HDC = "com.chery.carsettings.provider.slice.panel.hdc.switch";
    // 自动驻车
    public static final String AUTO_HOLD = "com.chery.carsettings.provider.slice.panel.auto_hold.switch";
    // 车外低速模拟音
    public static final String AVAS = "com.chery.phev.provider.slice.panel.passerby_protect_sound.switch";
    // 预约充电
    public static final String BOOK_CHARGE = "com.chery.phev.provider.slice.panel.book_charge.switch";
    // 雨刮灵敏度
    public static final String WIPER_LEVEL = "com.chery.carsettings.provider.slice.panel.wiper_level.function";
    // 中控锁
    public static final String CENTER_LOCK = "com.chery.carsettings.provider.slice.panel.central_locking.switch";
    // 后视镜折叠
    public static final String MIRROR_FOLD = "com.chery.carsettings.provider.slice.panel.mirror_fold";
    // 后视镜调节
    public static final String MIRROR_ADJUST = "com.chery.carsettings.provider.slice.panel.mirror_adjust.function";
    // 遮阳帘
    public static final String SUNSHADE = "com.chery.carsettings.provider.slice.panel.sunshade.function";
    // 后尾门
    public static final String TAILGATE = "com.chery.carsettings.provider.slice.panel.tailgate.function";
    // 车窗锁
    public static final String WINDOW_LOCK = "com.chery.carsettings.provider.slice.panel.window_lock.switch";
    // 儿童锁
    public static final String CHILD_LOCK = "com.chery.carsettings.provider.slice.panel.child_lock.switch";
    // 驻车保电
    public static final String BATTERY_LIFE = "com.chery.carsettings.provider.slice.panel.battery_life.switch";
    // 氛围灯
    public static final String LIGHT_MODE = "com.chery.carsettings.provider.slice.panel.light_mode.switch";
    // 热点
    public static final String BROADCAST = "com.chery.carsettings.provider.broadcast.tether";
    // 显示模式
    public static final String DISPLAY_MODE = "com.chery.carsettings.provider.slice.panel.display_mode.switch";
    // 车窗
    public static final String WINDOW_MODE = "com.chery.carsettings.provider.slice.panel.window_mode.switch";
    // 大灯调节
    public static final String HEAD_LAMP = "com.chery.carsettings.provider.slice.panel.headlamp";


    public static List<String> getAllUrisUsingReflection() throws IllegalAccessException {
        List<String> uris = new ArrayList<>();
        Field[] fields = ProviderURI.class.getFields();
        for (Field field : fields) {
            if (field.getType() == String.class &&
                    Modifier.isStatic(field.getModifiers()) &&
                    Modifier.isFinal(field.getModifiers())) {
                uris.add(PREFIX + field.get(null));
            }
        }
        return uris;
    }
}
