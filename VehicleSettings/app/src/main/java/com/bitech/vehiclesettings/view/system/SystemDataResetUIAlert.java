package com.bitech.vehiclesettings.view.system;

import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.LinearInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSVerifyInfoBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.lion.os.sdk.accountInfo.AccountInfoManager;
import com.lion.os.sdk.accountInfo.bean.UserInfo;
import com.lion.os.sdk.accountInfo.configs.AccountConfig;
import com.lion.os.sdk.accountInfo.listener.VerificationCodeListener;

import java.util.Locale;

public class SystemDataResetUIAlert extends Dialog {
    private static final String TAG = SystemDataResetUIAlert.class.getSimpleName();
    private static OnProgressChangedListener onProgressChangedListener;
    public static boolean isShow = false;

    public SystemDataResetUIAlert(@NonNull Context context) {
        super(context);
        initWindow();
    }

    public SystemDataResetUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        initWindow();
    }

    protected SystemDataResetUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        initWindow();
    }

    private void initWindow() {
        Window window = getWindow();
        if (window != null) {
            // 设置为系统级窗口（覆盖状态栏和 Dock）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
            } else {
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }

            // 设置窗口参数（全屏 + 覆盖系统 UI）
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;
            params.flags = WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                    | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                    | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    | WindowManager.LayoutParams.FLAG_DIM_BEHIND; // 背景变暗

            params.dimAmount = 0.6f; // 背景变暗程度
            window.setAttributes(params);
        }
    }

    public static OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = false;
        private DialogAlertSVerifyInfoBinding binding;
        private String phoneNumber;
        private boolean isBlueOpen = false;
        public SystemDataResetUIAlert dialog;
        private CountDownTimer countDownTimer;
        private float alphaScale = 0.3f;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCancelable(boolean isCancelable) {
            this.isCancelable = isCancelable;
            return this;
        }

        public Builder setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
            return this;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public SystemDataResetUIAlert create() {
            dialog = new SystemDataResetUIAlert(context, R.style.Dialog);
            binding = DialogAlertSVerifyInfoBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1176; // 固定宽度（可根据需求调整）
                layoutParams.height = 774; // 固定高度
                layoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR;
                layoutParams.flags = WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                        | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                        | WindowManager.LayoutParams.FLAG_DIM_BEHIND;
                window.setAttributes(layoutParams);
            }

            initViews();
            setupListeners();
            loadAdminInfo();

            return dialog;
        }

        private void initViews() {
            binding.ivVerifyCodeLine.setVisibility(View.GONE);
            binding.ivPhoneClear.setVisibility(View.GONE);
        }

        private void setupListeners() {
            binding.ivPhoneClear.setOnClickListener(v ->
                    binding.dialogPhoneInputEt.setText(""));

            binding.dialogVerifyCodeInputEt.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                    boolean verifyCodeHasText = charSequence.length() > 0;
                    boolean phoneHasText = binding.dialogPhoneInputEt.getText().toString().trim().length() > 0;
                    binding.ivVerifyCodeLine.setVisibility(verifyCodeHasText ? View.VISIBLE : View.GONE);
                    if (verifyCodeHasText || phoneHasText) {
                        GrayEffectUtils.removeGrayEffect(binding.tvSysResetConfirm);
                    } else {
                        GrayEffectUtils.applyGrayEffect(binding.tvSysResetConfirm, alphaScale);
                    }
                }
            });

            binding.dialogPhoneInputEt.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                    boolean verifyCodeHasText = binding.dialogVerifyCodeInputEt.getText().toString().trim().length() > 0;
                    boolean phoneHasText = charSequence.length() > 0;
                    binding.ivPhoneClear.setVisibility(charSequence.length() > 0 ? View.VISIBLE : View.GONE);

                    if (verifyCodeHasText || phoneHasText) {
                        GrayEffectUtils.removeGrayEffect(binding.tvSysResetConfirm);
                        GrayEffectUtils.removeGrayEffect(binding.tvGetVerifyCode);
                    } else {
                        GrayEffectUtils.applyGrayEffect(binding.tvSysResetConfirm, alphaScale);
                        GrayEffectUtils.applyGrayEffect(binding.tvGetVerifyCode);
                    }
                }
            });

            binding.tvGetVerifyCode.setOnClickListener(v -> {
                String phone = binding.dialogPhoneInputEt.getText().toString().trim();
                if (phone.length() != 11 || !phone.matches("\\d+")) {
                    EToast.showToast(context, "请输入正确的手机号", 0, false);
                    return;
                }
                AccountInfoManager.getInstance().queryVerificationCode(phone, AccountConfig.CodeType.FACTORY_RESET, new VerificationCodeListener() {
                    @Override
                    public void onQuerySuccess() {
                        Log.d("TAG", "获取验证码成功");
                        startCountDown();
                    }

                    @Override
                    public void onQueryFailed(String errorCode, String message) {
                        EToast.showToast(context, message, 0, false);
                    }
                });
            });

            binding.tvSysResetConfirm.setOnClickListener(view -> {
                String verifyCode = binding.dialogVerifyCodeInputEt.getText().toString().trim();
                if (verifyCode.isEmpty()) {
                    EToast.showToast(context, "请输入验证码", 0, false);
                    return;
                }
                AccountInfoManager.getInstance().verifyVerificationCode(phoneNumber, verifyCode, AccountConfig.CodeType.FACTORY_RESET, new VerificationCodeListener() {
                    @Override
                    public void onVerifySuccess() {
                        performFactoryReset();
                    }

                    @Override
                    public void onVerifyFailed(String errorCode, String message) {
                        EToast.showToast(context, message, 0, false);
                    }
                });
            });

            binding.tvSysResetCancel.setOnClickListener(v -> {
                if (countDownTimer != null) {
                    countDownTimer.cancel();
                }
                dialog.dismiss();
            });
        }

        private void loadAdminInfo() {
            UserInfo userinfo = AccountInfoManager.getInstance().getUserInfo(context);
            if (userinfo != null && userinfo.getCarOwner() != null && userinfo.getCarOwner().equals("1")) {
                binding.dialogPhoneInputEt.setText(userinfo.getMobile());
            } else {
                GrayEffectUtils.applyGrayEffect(binding.tvGetVerifyCode, 0.3f);
                GrayEffectUtils.applyGrayEffect(binding.tvSysResetConfirm, 0.3f);
            }
        }

        private void startCountDown() {
            final int countDownTime = 60;
            binding.tvGetVerifyCode.setEnabled(false);
            countDownTimer = new CountDownTimer(countDownTime * 1000, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    binding.tvGetVerifyCode.setText(
                            String.format(Locale.getDefault(), "获取验证码  (%ds)", millisUntilFinished / 1000)
                    );
                }

                @Override
                public void onFinish() {
                    GrayEffectUtils.removeGrayEffect(binding.tvGetVerifyCode);
                    binding.tvGetVerifyCode.setText("获取验证码");
                }
            }.start();
        }

        private void performFactoryReset() {
            binding.ivResetConfirm.setVisibility(View.VISIBLE);
            ObjectAnimator rotationAnimator = ObjectAnimator.ofFloat(
                    binding.ivResetConfirm,
                    "rotation",
                    0f, 360f
            );
            rotationAnimator.setDuration(7000);
            rotationAnimator.setRepeatCount(ObjectAnimator.INFINITE);
            rotationAnimator.setInterpolator(new LinearInterpolator());
            rotationAnimator.start();
            if (onProgressChangedListener != null) {
                onProgressChangedListener.factoryReset();
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface OnProgressChangedListener {
        void onSwitch(boolean flag);

        void factoryReset();
    }

    private abstract static class SimpleTextWatcher implements TextWatcher {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }
}