package com.bitech.vehiclesettings.bean;

import androidx.annotation.NonNull;

public class SystemPermission {
    private PermissionType type;
    private boolean enabled;           // 总开关：true=打开，false=关闭
    private SystemScope scope;         // 生效范围

    public SystemPermission(PermissionType type, boolean enabled, SystemScope scope) {
        this.type = type;
        this.enabled = enabled;
        this.scope = scope;
    }

    // --- Get<PERSON> & Setter ---
    public PermissionType getType() { return type; }
    public boolean isEnabled() { return enabled; }
    public SystemScope getScope() { return scope; }

    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    public void setScope(SystemScope scope) { this.scope = scope; }

    @NonNull
    @Override
    public String toString() {
        return "SystemPermission{" +
                "type=" + type +
                ", enabled=" + enabled +
                ", scope=" + scope +
                '}';
    }
}
