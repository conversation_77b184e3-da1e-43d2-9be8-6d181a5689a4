package com.bitech.vehiclesettings.view.connect

import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.content.SharedPreferences
import android.net.wifi.WifiManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.inputmethod.EditorInfo
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bitech.base.utils.Util.showInputMethod
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.adapter.WifiConnectedListAdapter
import com.bitech.vehiclesettings.adapter.WifiHotspotListAdapter
import com.bitech.vehiclesettings.base.kt.BaseDialogFragment
import com.bitech.vehiclesettings.bean.WifiDeviceBean
import com.bitech.vehiclesettings.bean.WifiHotspotBean
import com.bitech.vehiclesettings.databinding.FragmentHotpotKtBinding
import com.bitech.vehiclesettings.manager.CarBtManager
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.EToast
import com.bitech.vehiclesettings.utils.GrayEffectUtils
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.TextUtil
import com.bitech.vehiclesettings.view.common.DetailsUIAlert
import com.bitech.vehiclesettings.view.dialog.ConfirmDialog
import com.bitech.vehiclesettings.view.dialog.WifiConnectedInputDialog
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.bitech.vehiclesettings.viewmodel.WifiViewModel
import java.util.concurrent.CopyOnWriteArrayList


/**
 * @ClassName: WifiFragment
 *
 * @Date:  2024/1/19 12:22
 * @Description: WIFI页面Fragment.
 **/
class HotpotFragment : BaseDialogFragment<FragmentHotpotKtBinding, WifiViewModel>(), View.OnClickListener,
    WifiConnectedListAdapter.OnWifiItemClickIconCallback {

    // 页面内部所有TextView集合
    private val textViews = mutableListOf<TextView>()

    // 热点连接设备列表适配器
    private var hotspotListAdapter: WifiHotspotListAdapter? = null

    // WIFI已连接列表适配器
    private var wifiConnectedListAdapter: WifiConnectedListAdapter? = null

    // 自定义Toast
    private var toast: SettingsToast? = null

    // WIFI连接密码输入弹窗
    private var wifiConnectedInputDialog: WifiConnectedInputDialog? = null

    // wifi页面提示弹窗
    private var confirmDialog: ConfirmDialog? = null

    // 内部NestedScrollView相对于屏幕顶部的距离
    private var nestedScrollViewTop = -1

    //密码隐藏可见性
    private var isPasswordVisible = false
    private var isUserTouching = false
    private lateinit var prefs: SharedPreferences
    //是否首次运行
    private val PREFS_NAME = "AppPrefs"
    private val KEY_FIRST_RUN = "isFirstRun"
    private var carBtManager = CarBtManager.instance
    private var wifiManager: WifiManager? = null
    private var detailUIAlert: DetailsUIAlert.Builder? = null
    /**
     * 视图绑定.
     *
     * @return xml文件id
     */
    override fun getLayoutId(container: ViewGroup?): FragmentHotpotKtBinding {
        return FragmentHotpotKtBinding.bind(
            layoutInflater.inflate(
                R.layout.fragment_hotpot_kt,
                container,
                false
            )
        )

    }

    /**
     * viewModel绑定.
     *
     * @return viewModel类名
     */
    override fun getViewModel(): Class<WifiViewModel> {
        return WifiViewModel::class.java
    }

    /**
     * liveData订阅.
     *
     */
    override fun initObserve() {
        LogUtil.d(TAG, "initObserve : ")
        viewModel.apply {
            // 热点开关状态订阅
            hotspotSwitchLiveData.observe(this@HotpotFragment) {
                // 更新热点开关状态Ui
                updateHotspotStatusUi(it)
            }
            // 热点名称及密码订阅
            hotspotNameLiveData.observe(this@HotpotFragment) {
                LogUtil.d(TAG, "hotspotNameLiveData : name = $it")
                // 设置WIFI热点名称
                viewBinding.settingsHotspotNameEt.setText(it)
                viewBinding.settingsHotspotNameEtTv.text = it
                // 更新热点名称编辑视图
                hotspotNameEditIvVisible(true)
            }
            hotspotPasswordLiveData.observe(this@HotpotFragment) {
                LogUtil.d(TAG, "hotspotPasswordLiveData : password = $it")
                // 设置WIFI热点密码
                viewBinding.settingsHotspotPasswordEt.setText(it)
            }
            // 热点密码输入错误订阅
            hotspotPasswordErrorLD.observe(this@HotpotFragment) {
                LogUtil.d(TAG, "hotspotPasswordErrorLD : password is error = $it")
                if (it) {
                    // 显示密码长度提示Toast
                    EToast.showToast(requireContext(), getString(R.string.wifi_hotspot_not_length), Toast.LENGTH_SHORT,false)
                }
            }
            // 热点连接设备列表订阅
            hotspotConnectedListLiveData.observe(this@HotpotFragment) {
                LogUtil.d(TAG, "wifiConnectedErrorLiveData :zhc6whu:更新热点连接设备列表= $it")
                // 更新热点已连接列表UI
                updateHotspotConnectedListUi(it)
            }
            // WIFI连接错误订阅
            wifiConnectedResultLiveData.observe(this@HotpotFragment) {
                LogUtil.d(TAG, "wifiConnectedErrorLiveData : error code = $it")
                // 根据WIFI错误类型，进行对应错误提示
                showWifiConnectedErrorTips(it)
            }
            // 搜索关键词订阅
            searchKeywordLiveData.observe(this@HotpotFragment) {
                // 更新搜索关键词Ui
                updateSearchKeywordUi(it)
            }
        }
    }

    /**
     *相关视图初始化.
     *
     */
    override fun initView() {
        LogUtil.d(TAG, "initView : ")
        // 初始化可搜索TextView
        initSearchTextView()
        // 注册监听器
        requireActivity().application.registerActivityLifecycleCallbacks(appSwitchListener)
    }

    /**
     * 控件监听注册.
     *
     */
    override fun intiListener() {
        LogUtil.d(TAG, "intiListener : ")
        // WIFI页面相关控件监听。
        viewBinding.apply {
            // 热点开关监听
            settingsHotspotSw.setOnCheckedChangeListener { _, isChecked ->
                LogUtil.d(TAG, "settingsHotspotSw : isChecked = $isChecked")
                settingsHotspotSw.isEnabled = false
                settingsHotspotCompatibleSw.isEnabled = false
                // 打开或关闭热点
                if (isChecked) {
                    // 打开热点
                    viewModel.setHotspotState(true)
                    onHotspotSwitchOnUiShow()
                } else {
                    // 关闭热点
                    onHotspotSwitchOffUiShow()
                    // 判断当前CarPlay是否连接
                    if (viewModel.hasConnectDeviceCp()) {
                        // CP已连接，显示断开热点提示弹窗
                        showCloseHotSpotDialog()
                    } else if (viewModel.hasConnectDeviceAa()) {
                        // AA已连接，显示断开热点提示弹窗
                        showAaCloseHotSpotDialog()
                    } else {
                        // 未连接，则直接进行断开
                        viewModel.setHotspotState(false)
                    }
                }
                Handler(Looper.getMainLooper()).postDelayed({
                    settingsHotspotSw.isEnabled = true
                    settingsHotspotCompatibleSw.isEnabled = true
                }, 1500)
            }
            // 热点名称编辑框监听
            settingsHotspotNameEt.setOnEditorActionListener(object :
                TextView.OnEditorActionListener {
                override fun onEditorAction(
                    v: TextView?,
                    actionId: Int,
                    event: KeyEvent?
                ): Boolean {
                    LogUtil.d(TAG, "settingsHotspotNameEt : actionId = $actionId")
                    if (actionId == EditorInfo.IME_ACTION_DONE) {
                        // 设置WIFI热点名称
                        viewModel.updateHotspotName(settingsHotspotNameEt.text.toString())
                        return true
                    }
                    return false
                }
            })
            // 热点密码编辑框监听
            settingsHotspotPasswordEt.setOnEditorActionListener(object :
                TextView.OnEditorActionListener {
                override fun onEditorAction(
                    v: TextView?,
                    actionId: Int,
                    event: KeyEvent?
                ): Boolean {
                    LogUtil.d(TAG, "settingsHotspotPasswordEt : actionId = $actionId")
                    if (actionId == EditorInfo.IME_ACTION_DONE) {
                        // 设置WIFI热点密码
                        viewModel
                            .updateHotspotPassword(settingsHotspotPasswordEt.text.toString())
                        // 更新热点密码编辑视图
                        hotspotPasswordEditIvVisible(true)
                        return true
                    }
                    return false
                }
            })
            // 热点扫描列表滑动事件监听处理.
            settingsConnectedHotspotRv.addOnScrollListener(object :
                RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    // 解决与ScanScrollView的滑动冲突事件
                    setBtScrollViewCanScroll(newState)
                }
            })
            //热点兼容性
            settingsHotspotCompatibleSw.setOnTouchListener { _, event ->
                if (event.action == MotionEvent.ACTION_DOWN) {
                    isUserTouching = true
                }
                false
            }
            settingsHotspotCompatibleSw.setOnCheckedChangeListener { _, isChecked ->
                LogUtil.d(TAG, "settingsHotspotSw : isChecked = $isChecked")
                if(isUserTouching){
                    settingsHotspotSw.isEnabled = false
                    settingsHotspotCompatibleSw.isEnabled = false
                    if (settingsHotspotCompatibleSw.isChecked) {
                        viewModel.set5GBand(false)
                        viewModel.updateHotspot()
                        Log.d(TAG, "intiListener: zhc6whu 热点兼容性 切换到2.4GHz")
                    }else{
                        viewModel.set5GBand(true)
                        viewModel.updateHotspot()
                        Log.d(TAG, "intiListener: zhc6whu 热点兼容性 切换到5GHz")
                    }
                    Handler(Looper.getMainLooper()).postDelayed({
                        settingsHotspotSw.isEnabled = true
                        settingsHotspotCompatibleSw.isEnabled = true
                    }, 1500) // 1 秒后恢复可点击状态
                }
            }
            imgHotspotDetail.setOnClickListener {
                showHotspotDetailDialog()
            }
            // 设置页面图标点击事件监听
            settingsHotspotPasswordEditIb.setOnClickListener(this@HotpotFragment)
        }
    }

    //显示2.4G解释弹窗
    private fun showHotspotDetailDialog() {
        // 如果对话框已存在且正在显示，则不创建新的
        if (detailUIAlert != null && detailUIAlert!!.isShowing) {
            return
        }

        if (detailUIAlert == null) {
            detailUIAlert = DetailsUIAlert.Builder(requireContext())
        }
        detailUIAlert!!.create(getString(R.string.str_str_hotspot_detail_title),getString(R.string.str_str_hotspot_detail_content), 1176, 396,
            Gravity.LEFT).show()
        detailUIAlert!!.setPadding(120)
        detailUIAlert!!.setTextSize(com.bitech.base.R.dimen.font_24px)
    }

    /**
     * 相关数据初始化.
     *
     */
    override fun initData() {
        LogUtil.d(TAG, "initData : ")
        // 初始化页面开关状态
        initSwStatusUi()
        // 初始化页面数据
        viewModel.initData()
        //获取热点名
        viewBinding.settingsHotspotNameEtTv.text = viewModel.getWifiHotspotName()
        // 初始化热点列表适配器
        hotspotListAdapter = WifiHotspotListAdapter(viewModel.getHotspotConnectedList())
        // 初始化已连接WIFI列表适配器
        wifiConnectedListAdapter =
            WifiConnectedListAdapter(requireContext(), viewModel.getWifiConnectedList())
        wifiConnectedListAdapter!!.setOnBtItemClickIconCallback(this)
        viewBinding.apply {
            // 设置热点连接设备列表布局及适配器
            settingsConnectedHotspotRv.layoutManager = LinearLayoutManager(requireContext())
            settingsConnectedHotspotRv.adapter = hotspotListAdapter
        }
    }

    override fun onStop() {
        // 在退出的时候，停止WIFI扫描
        viewModel.stopWifiScan()
        // 注销已连接列表适配器监听
        wifiConnectedListAdapter!!.setOnBtItemClickIconCallback(null)
        // 注销监听器
        requireActivity().application.unregisterActivityLifecycleCallbacks(appSwitchListener)
        isShow = false;
        super.onStop()
    }

    override fun getDialogTag(): String {
        return "HotpotFragment"
    }

    override fun onStart() {
        isShow = true
        Log.d(TAG,"界面状态为+ ${isShow}")
        super.onStart()
    }

    /**
     * 订阅移除.
     *
     */
    override fun removeObserve() {
        LogUtil.d(TAG, "removeObserve : ")
        viewModel.apply {
            // 注销订阅
            searchKeywordLiveData.removeObservers(this@HotpotFragment)
            wifiConnectedResultLiveData.removeObservers(this@HotpotFragment)
            wifiConnectedListLiveData.removeObservers(this@HotpotFragment)
            wifiScanningState.removeObservers(this@HotpotFragment)
            wifiScanListLiveData.removeObservers(this@HotpotFragment)
            hotspotConnectedListLiveData.removeObservers(this@HotpotFragment)
            hotspotPasswordErrorLD.removeObservers(this@HotpotFragment)
            hotspotPasswordLiveData.removeObservers(this@HotpotFragment)
            hotspotNameLiveData.removeObservers(this@HotpotFragment)
            networkNotSwitchLiveData.removeObservers(this@HotpotFragment)
            hotspotSwitchLiveData.removeObservers(this@HotpotFragment)
            wifiSwitchLiveData.removeObservers(this@HotpotFragment)
        }
        cancelDialog()
    }

    /**
     * 初始化可搜索TextView数据.
     *
     */
    private fun initSearchTextView() {
        LogUtil.d(TAG, "initSearchTextView : ")
        // 初始化页面内TextView集合
        textViews.addAll(TextUtil.getAllTextViewInFragment(viewBinding.root))
        // 构建页面内搜索菜单相关数据
        Contacts.wifiMenuBean.secondMenuList.forEach { secondMenu ->
            val textView = textViews.find { TextUtils.equals(it.text, secondMenu.secondMenuName) }
            if (textView != null) {
                // 二级菜单选项在页面TextView找到对应，则更新滑动距离和与之对应的TextView
                secondMenu.nameTextView = textView
                secondMenu.nameTextView!!.viewTreeObserver.addOnGlobalLayoutListener(object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        if (nestedScrollViewTop == -1) {
                            nestedScrollViewTop =
                                TextUtil.viewToTopScreenDistance(viewBinding.settingsWifiSsv)
                        }
                        // 设置滑动到对应TextView需要滑动的距离
                        secondMenu.scrollDistance =
                            TextUtil.viewToTopScreenDistance(textView) - nestedScrollViewTop
                        // 移除监听器，确保只调用一次
                        secondMenu.nameTextView!!.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }
                })
            }
        }
    }

    /**
     * 清空可搜索TextView数据.
     *
     */
    private fun clearSearchTextView() {
        LogUtil.d(TAG, "clearSearchTextView : ")
        Contacts.wifiMenuBean.secondMenuList.forEach { secondMenu ->
            secondMenu.nameTextView = null
        }
    }

    /**
     * 初始化页面开关状态.
     *
     */
    private fun initSwStatusUi() {
        LogUtil.d(TAG, "initSwStatusUi : ")
        // 更新热点开关状态Ui
        updateHotspotStatusUi(viewModel.getHotspotState())
        // 热点已连接列表更新
        updateHotspotConnectedListUi(viewModel.getHotspotConnectedList())
        // 热点名称
        hotspotNameNotEditUi(false)
        // 热点密码
        hotspotPasswordNotEditUi(false)
        viewBinding.settingsHotspotCompatibleSw.isChecked = !viewModel.get5GBand()
        Log.d(TAG, "initSwStatusUi: zhc6whu:热点兼容性：${viewModel.get5GBand()}")
    }

    /**
     * WIFI页面点击事件监听.
     *
     * @param view
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.settings_hotspot_edit_ib -> {
                // 热点名称编辑图标被点击
                LogUtil.d(TAG, "onClick : name edit iv is click!")
                // 更新UI
                hotspotNameEditIvVisible(false)
                if (viewBinding.settingsHotspotPasswordEt.isFocusable) {
                    // 设置热点密码为非编辑状态
                    hotspotPasswordNotEditUi(false)
                }
            }

            R.id.settings_hotspot_clear_ib -> {
                // 热点名称清空图标被点击
                LogUtil.d(TAG, "onClick : name clear iv is click!")
                // 清空输入的名称
                viewBinding.settingsHotspotNameEt.setText("")
            }

            R.id.settings_hotspot_password_edit_ib -> {
                // 热点密码编辑图标被点击
                //打开更新热点密码的弹窗
                showUpdateHotspotPasswordDialog()
                LogUtil.d(TAG, "onClick : password edit iv is click!")
                if (viewBinding.settingsHotspotNameEt.isFocusable) {
                    // 设置热点名称为非编辑状态
                    hotspotNameNotEditUi(false)
                }
            }

            R.id.settings_hotspot_password_clear_ib -> {
                // 热点密码清空图标被点击
                LogUtil.d(TAG, "onClick : password clear iv is click!")
                // 清空输入的密码
                viewBinding.settingsHotspotPasswordEt.setText("")
            }
        }
    }

    /*----------------------------WIFI已连接列表各控件点击事件监听回调----------------------------------*/
    /**
     * 点击item 删除图标时回调.
     *
     * @param wifiDevice wifi对象
     */
    override fun onWifiDeleteDevice(wifiDevice: WifiDeviceBean) {
        LogUtil.d(TAG, "onWifiDeleteDevice : start delete wifi = ${wifiDevice.wifiSSID}")
        // 显示确认删除弹窗
    }

    /**
     * 点击item 进行连接时回调.
     *
     * @param wifiDevice wifi对象
     */
    override fun onWifiConnected(wifiDevice: WifiDeviceBean) {
        LogUtil.d(TAG, "onWifiConnected : start connected wifi = ${wifiDevice.wifiSSID}")
    }

    /**
     * 点击item 进行断开连接时回调.
     *
     * @param wifiDevice wifi对象
     */
    override fun onWifiDisconnected(wifiDevice: WifiDeviceBean) {
        LogUtil.d(TAG, "onWifiDisconnected : start disconnected wifi = ${wifiDevice.wifiSSID}")
        // 显示断开连接弹窗
    }
    /*----------------------------WIFI已连接列表各控件点击事件监听回调----------------------------------*/

    /**
     * 设置ScanScrollView是否可以滑动.
     *
     * @param state recycleView滑动状态
     */
    private fun setBtScrollViewCanScroll(state: Int) {
        when (state) {
            RecyclerView.SCROLL_STATE_DRAGGING -> {
                viewBinding.settingsWifiSsv.setIntercepted(true)
            }

            RecyclerView.SCROLL_STATE_IDLE -> {
                viewBinding.settingsWifiSsv.setIntercepted(false)
            }
        }
    }

    /**
     * 显示提示Toast.
     *
     * @param message 提示信息
     */
    private fun showToast(message: String) {
        SettingsToast.showToast(message)
    }

    /**
     * 显示WIFI错误提示.
     *
     * @param errorCode 错误码
     */
    private fun showWifiConnectedErrorTips(errorCode: Int) {
        when (errorCode) {
            Contacts.WIFI_PASSWORD_ERROR -> {
                // WIFI密码错误
                EToast.showToast(context, getString(R.string.wifi_password_error_tips), Toast.LENGTH_SHORT, false)
            }

            Contacts.WIFI_CONNECTED_TIMEOUT -> {
                // WIFI连接超时
                EToast.showToast(context, getString(R.string.wifi_connected_timeout_tips), Toast.LENGTH_SHORT, false)
            }

            Contacts.WIFI_CONNECTED_FAIL -> {
                // WIFI连接失败
                EToast.showToast(context, getString(R.string.wifi_connected_fail_tips), Toast.LENGTH_SHORT, false)
            }
        }
    }

    /**
     * 显示CarPlay连接时，热点关闭提示弹窗.
     *
     */
    private fun showCloseHotSpotDialog() {
        cancelDialog()
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "onConfirmClick : close!")
                // 需要关闭热点
                Contacts.isNeedCloseHotspot = true
                // 断开连接的CP设备
                viewModel.disconnectDeviceCp()
                cancelDialog()
            }

            override fun onCancelClick() {
                LogUtil.d(TAG, "onCancelClick : ")
                // 取消按钮被点击,开关复原
                viewBinding.settingsHotspotSw.isChecked = !viewBinding.settingsHotspotSw.isChecked
                cancelDialog()
            }
        })
        confirmDialog?.setTips(getString(R.string.wifi_hotspot_switcher_close_cp_tips))
        confirmDialog?.show()
    }

    /**
     * 显示AndroidAuto连接时，热点关闭提示弹窗.
     *
     */
    private fun showAaCloseHotSpotDialog() {
        cancelDialog()
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "onConfirmClick : close!")
                // 需要关闭热点
                Contacts.isNeedCloseHotspot = true
                // 断开连接的AA设备
                viewModel.disconnectDeviceAa()
                cancelDialog()
            }

            override fun onCancelClick() {
                LogUtil.d(TAG, "onCancelClick : ")
                // 取消按钮被点击,开关复原
                viewBinding.settingsHotspotSw.isChecked = !viewBinding.settingsHotspotSw.isChecked
                cancelDialog()
            }
        })
        confirmDialog?.setTips(getString(R.string.wifi_hotspot_switcher_close_aa_tips))
        confirmDialog?.show()
    }

    private fun showUpdateHotspotPasswordDialog() {
        // 创建一个自定义的 Dialog
        val hotspotDialog = context?.let { Dialog(it, R.style.AppTheme_Dialog) }
        hotspotDialog?.setContentView(R.layout.dialog_hotspot_password_input) // 设置自定义布局
        val wifiPasswordEditText = hotspotDialog?.findViewById<EditText>(R.id.dialog_hotspot_password_input_et)
        val wifiPasswordCleanBtn = hotspotDialog?.findViewById<ImageView>(R.id.dialog_wifi_icon_iv)
        val connectButton = hotspotDialog?.findViewById<Button>(R.id.dialog_hotspot_connected_btn)
        val cancelButton = hotspotDialog?.findViewById<Button>(R.id.dialog_hotspot_cancel_btn)
// 在初始化wifiPasswordEditText后添加过滤器
        wifiPasswordEditText?.setText(viewModel.getWifiHotspotPassword())
        wifiPasswordEditText?.filters = arrayOf(InputFilter.LengthFilter(16))
        connectButton?.setBackgroundResource(R.drawable.shape_bg_blue)
        // 监听密码输入框的文本变化
        wifiPasswordEditText?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                // 根据密码长度设置按钮颜色
                val passwordLength = s?.length ?: 0
                if (passwordLength >= 8) {
                    // 密码长度大于等于位8，按钮设置为蓝色
                    GrayEffectUtils.removeGrayEffect(connectButton)
                } else {
                    // 密码长度小于8位
                    GrayEffectUtils.applyGrayEffect(connectButton)
                }
            }

            override fun afterTextChanged(s: Editable?) {}
        })

        wifiPasswordCleanBtn?.setOnClickListener{
            wifiPasswordEditText?.setText("")
        }

        // 连接按钮点击事件
        connectButton?.setOnClickListener {
            if (wifiPasswordEditText?.text.toString().length < 8) {
                // 密码长度小于8位
                EToast.showToast(context, getString(R.string.hotspot_password_tips), Toast.LENGTH_SHORT, false)
            }else{
                viewModel.updateHotspotPassword(wifiPasswordEditText?.text.toString())
                Log.d(TAG, "showUpdateHotspotPasswordDialog: zhc6whu:热点已更新")
                hotspotDialog.dismiss() // 关闭弹窗
                dismiss()
            }
        }
        // 取消按钮点击事件
        cancelButton?.setOnClickListener {
            Log.d(TAG, "showUpdateHotspotPasswordDialog: zhc6whu:弹窗已关闭")
            hotspotDialog.dismiss() // 关闭弹窗
        }
        // 显示弹窗
        hotspotDialog?.show()
        wifiPasswordEditText?.requestFocus()
        showInputMethod(context, wifiPasswordEditText)
    }


    /**
     * 关闭当前提示WIFI弹窗.
     *
     */
    private fun cancelDialog() {
        LogUtil.d(TAG, "cancelDialog : ")
        if (confirmDialog != null) {
            confirmDialog?.dismiss()
            confirmDialog = null
        }
    }

    /**
     * 更新热点开关状态对应的Ui.
     *
     * @param isChecked 是否选中
     */
    private fun updateHotspotStatusUi(isChecked: Boolean) {
        LogUtil.i(TAG, "updateHotspotStatusUi : hotspot switch state = $isChecked")
        // 设置热点开关状态
        viewBinding.settingsHotspotSw.isChecked = isChecked
        Log.d(TAG, "updateHotspotStatusUi: 打开")
        if (isChecked) {
            Log.d(TAG, "updateHotspotStatusUi: 打开显示")
            // 热点开关打开时，界面UI显示
            onHotspotSwitchOnUiShow()
        } else {
            Log.d(TAG, "updateHotspotStatusUi: 打开隐藏")
            // 热点开关关闭时，界面UI显示
            onHotspotSwitchOffUiShow()
        }
    }

    /**
     * 更新热点已连接列表Ui.
     *
     * @param hotspotList 热点已连接列表
     */
    private fun updateHotspotConnectedListUi(hotspotList: CopyOnWriteArrayList<WifiHotspotBean>) {
        LogUtil.d(TAG, "updateHotspotConnectedListUi :zhc6whu hotspot size = ${hotspotList.size}")
        if (hotspotList.size == 0) {
            // 隐藏热点连接列表
            hotspotListIsVisible(false, hotspotList)
        } else {
            // 显示热点连接列表
            hotspotListIsVisible(true, hotspotList)
        }
    }

    /**
     * 热点设备连接列表是否可见.
     *
     * @param isVisible 可见性
     * @param hotspotList 设备连接列表
     */
    private fun hotspotListIsVisible(
        isVisible: Boolean,
        hotspotList: CopyOnWriteArrayList<WifiHotspotBean>
    ) {
        if (isVisible) {
            viewBinding.apply {
                // 热点设备连接列表可见
                settingsHotspotConnectedTv.visibility = View.VISIBLE
                settingsHotspotCountTv.visibility = View.VISIBLE
                settingsHotspotConnectedCl.visibility = View.VISIBLE
                // 设置热点连接列表的台数
                settingsHotspotCountTv.text = resources
                    .getString(R.string.wifi_hotspot_connected_count, hotspotList.size.toString())
                // 更新热点连接列表适配器数据
                hotspotListAdapter?.setHotspotConnectedList(hotspotList)
            }
        } else {
            viewBinding.apply {
                // 热点设备连接列表不可见
                settingsHotspotConnectedTv.visibility = View.GONE
                settingsHotspotCountTv.visibility = View.GONE
                settingsHotspotConnectedCl.visibility = View.GONE
            }
        }
    }

    /**
     * 热点名称编辑图标是否显示.
     *
     * @param isVisible Boolean
     */
    private fun hotspotNameEditIvVisible(isVisible: Boolean) {
        if (isVisible) {
            viewBinding.apply {
                // 设置名称编辑框不可聚焦
                settingsHotspotNameEt.isFocusableInTouchMode = false
                settingsHotspotNameEt.isFocusable = false
                settingsHotspotNameEt.clearFocus()
                // 隐藏热点名称文本,显示热点名称编辑框
                settingsHotspotNameEt.visibility = View.GONE
                settingsHotspotNameEtTv.visibility = View.VISIBLE
            }
            // 隐藏系统软键盘
            viewModel.hideSoftInputFromWindow(viewBinding.settingsHotspotNameEt)
        } else {
            viewBinding.apply {
                // 隐藏热点名称文本,显示热点名称编辑框
                settingsHotspotNameEt.visibility = View.VISIBLE
                settingsHotspotNameEtTv.visibility = View.GONE
                // 设置名称编辑框可聚焦
                settingsHotspotNameEt.isFocusableInTouchMode = true
                settingsHotspotNameEt.isFocusable = true
                settingsHotspotNameEt.requestFocus()
                settingsHotspotNameEt.setSelection(settingsHotspotNameEt.text.length)
            }
            // 显示系统软键盘
            viewModel.showSoftInputFromWindow(viewBinding.settingsHotspotNameEt)
        }
    }

    /**
     * 热点名称修改为非编辑状态
     *
     * @param isHideSoftInput 是否需要隐藏软键盘
     */
    private fun hotspotNameNotEditUi(isHideSoftInput: Boolean) {
        viewBinding.apply {
            // 设置名称编辑框不可聚焦
            settingsHotspotNameEt.isFocusableInTouchMode = false
            settingsHotspotNameEt.isFocusable = false
            settingsHotspotNameEt.clearFocus()
            // 隐藏热点名称文本,显示热点名称编辑框
            settingsHotspotNameEt.setText(viewModel.getWifiHotspotName())
            settingsHotspotNameEt.visibility = View.GONE
            settingsHotspotNameEtTv.visibility = View.VISIBLE
            settingsHotspotNameEtTv.text = viewModel.getWifiHotspotName()
        }
        if (isHideSoftInput) {
            // 隐藏系统软键盘
            viewModel.hideSoftInputFromWindow(viewBinding.settingsHotspotNameEt)
        }
    }

    /**
     * 热点密码编辑图标是否显示.
     *
     * @param isVisible Boolean
     */
    private fun hotspotPasswordEditIvVisible(isVisible: Boolean) {
        if (isVisible) {
            viewBinding.apply {
                // 显示热点密码并隐藏密码清空图标
                settingsHotspotPasswordEditIb.visibility = View.VISIBLE
                // 设置密码编辑框不可聚焦
                settingsHotspotPasswordEt.isFocusableInTouchMode = false
                settingsHotspotPasswordEt.isFocusable = false
                settingsHotspotPasswordEt.clearFocus()
            }
            // 隐藏系统软键盘
            viewModel.hideSoftInputFromWindow(viewBinding.settingsHotspotPasswordEt)
        } else {
            viewBinding.apply {
                // 隐藏热点密码并显示密码清空图标
                settingsHotspotPasswordEditIb.visibility = View.INVISIBLE
                // 设置密码编辑框可聚焦
                settingsHotspotPasswordEt.isFocusableInTouchMode = true
                settingsHotspotPasswordEt.isFocusable = true
                settingsHotspotPasswordEt.requestFocus()
                settingsHotspotPasswordEt.setSelection(settingsHotspotPasswordEt.text.length)
            }
            // 显示系统软键盘
            viewModel.showSoftInputFromWindow(viewBinding.settingsHotspotPasswordEt)
        }
    }

    /**
     * 修改热点密码输入框为非编辑状态.
     *
     * @param isHideSoftInput 是否需要隐藏软件盘
     */
    private fun hotspotPasswordNotEditUi(isHideSoftInput: Boolean) {
        viewBinding.apply {
            // 显示热点密码并隐藏密码清空图标
            settingsHotspotPasswordEditIb.visibility = View.VISIBLE
            // 设置密码编辑框不可聚焦
            settingsHotspotPasswordEt.isFocusableInTouchMode = false
            settingsHotspotPasswordEt.isFocusable = false
            settingsHotspotPasswordEt.clearFocus()
            // 热点密码
            viewBinding.settingsHotspotPasswordEt.setText(viewModel.getWifiHotspotPassword())
        }
        if (isHideSoftInput) {
            // 隐藏系统软键盘
            viewModel.hideSoftInputFromWindow(viewBinding.settingsHotspotPasswordEt)
        }
    }

    /**
     * 热点开关打开时UI显示.
     *
     */
    private fun onHotspotSwitchOnUiShow() {
        viewBinding.apply {
            // 显示热点名称及密码
            settingsHotspotEditCl.visibility = View.VISIBLE
            // 显示热点已连接设备
            showHotspotConnectedList()
        }
    }

    /**
     * 热点开关关闭时UI显示.
     *
     */
    private fun onHotspotSwitchOffUiShow() {
        viewBinding.apply {
            // 隐藏热点名称及密码
            settingsHotspotEditCl.visibility = View.GONE
            // 隐藏热点已连接设备
            hideHotspotConnectedList()
        }
    }

    /**
     * 显示热点设备已连接列表.
     *
     */
    private fun showHotspotConnectedList() {
        viewBinding.apply {
            // 显示热点已连接设备
            settingsHotspotConnectedTv.visibility = View.VISIBLE
            settingsHotspotCountTv.visibility = View.VISIBLE
            settingsHotspotConnectedCl.visibility = View.VISIBLE
        }
        // 热点已连接列表更新
        if (viewModel.getHotspotConnectedList().size == 0) {
            // 隐藏热点连接列表
            hotspotListIsVisible(false, viewModel.getHotspotConnectedList())
        } else {
            // 显示热点连接列表
            hotspotListIsVisible(true, viewModel.getHotspotConnectedList())
        }
        // 更新热点列表
        viewModel.updateHotspotConnectedList()
    }


    /**
     * 隐藏热点已连接设备列表.
     *
     */
    private fun hideHotspotConnectedList() {
        viewBinding.apply {
            // 隐藏热点已连接设备
            settingsHotspotConnectedTv.visibility = View.GONE
            settingsHotspotCountTv.visibility = View.GONE
            settingsHotspotConnectedCl.visibility = View.GONE
        }
    }

    //随机密码生成
    private fun generatePassword(): String {
        // 定义字符集
        val uppercaseLetters = 'A'..'Z'
        val lowercaseLetters = 'a'..'z'
        val numbers = '0'..'9'
        val specialChars = listOf('!', '@', '#', '$', '%', '^', '&', '*')

        // 确保每种类型至少有一个字符
        val passwordChars = mutableListOf<Char>().apply {
            add(uppercaseLetters.random())
            add(lowercaseLetters.random())
            add(numbers.random())
            add(specialChars.random())

            // 添加剩余的4个随机字符（从所有允许的字符中选择）
            repeat(4) {
                val allChars = uppercaseLetters + lowercaseLetters + numbers + specialChars
                add(allChars.random())
            }
        }

        // 打乱字符顺序并转换为字符串
        return passwordChars.shuffled().joinToString("")
    }
    /**
     * 更新搜索关键词Ui.
     *
     * @param keyword
     */
    private fun updateSearchKeywordUi(keyword: String) {
        LogUtil.i(TAG, "updateSearchKeywordUi : keyword = $keyword")
        // 在页面菜单集合中进行遍历，找到搜索结果对应的TextView
        val secondMenu = Contacts.wifiMenuBean.secondMenuList.find {
            TextUtils.equals(it.nameTextView?.text, keyword)
        }
        if (secondMenu != null) {
            // 计算滑动距离，向上还是向下滑动
            val scrollDistance =
                secondMenu.scrollDistance - viewBinding.settingsWifiSsv.scrollY - 80
            LogUtil.i(TAG, "updateSearchKeywordUi : scrollDistance = $scrollDistance")
            // 滑动对应搜索结果TextView控件的位置
            viewBinding.settingsWifiSsv.fling(scrollDistance)
            viewBinding.settingsWifiSsv.smoothScrollBy(0, scrollDistance)
        }
    }

    // 添加应用切换监听器
    private val appSwitchListener = object : Application.ActivityLifecycleCallbacks {
        override fun onActivityPaused(activity: Activity) {
            if (activity == <EMAIL> && isAdded) {
                dismiss() // 应用切后台时关闭
            }
        }
        override fun onActivityResumed(activity: Activity) {}
        override fun onActivityStarted(activity: Activity) {}
        override fun onActivityDestroyed(activity: Activity) {}
        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
        override fun onActivityStopped(activity: Activity) {}
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
    }
    override fun onDestroyView() {
        LogUtil.d(TAG, "onDestroyView : ")
        clearSearchTextView()
        super.onDestroyView()
    }

    companion object {
        // 日志标志位
        private const val TAG = "HotpotFragment"
        var isShow = false;
    }
}
