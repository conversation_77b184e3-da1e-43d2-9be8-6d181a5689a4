package com.bitech.vehiclesettings.viewmodel;

import android.util.Log;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.platformlib.interfaces.display.IDisplayManagerListener;
import com.bitech.platformlib.manager.DisplayManager;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class DisplayViewModel extends ViewModel {
    public static final String TAG = DisplayViewModel.class.getName() + "wzh2whu";
    DisplayManager displayManager = DisplayManager.getInstance();
    // 分屏
    public final MutableLiveData<Integer> fp = new MutableLiveData<>();
    // 视频限制
    public final MutableLiveData<Integer> videoLimit = new MutableLiveData<>();
    // 中控屏
    public static final MutableLiveData<Integer> zkp = new MutableLiveData<>();
    // apc
    public final MutableLiveData<Integer> apcLevelLiveData = new MutableLiveData<>();
    public final MutableLiveData<Integer> backLightLiveData = new MutableLiveData<>();

    private final ExecutorService executor = Executors.newCachedThreadPool();

    public static MutableLiveData<Integer> getZkp() {
        return zkp;
    }

    public static void setZkp(Integer status) {
        zkp.postValue(status);
    }

    public MutableLiveData<Integer> getFp() {
        return fp;
    }

    public void setFp(Integer status) {
        this.fp.postValue(status);
    }

    public MutableLiveData<Integer> getVideoLimit() {
        return videoLimit;
    }

    public void setVideoLimit(Integer status) {
        this.videoLimit.postValue(status);
    }

    public void getApcLevel() {
        int signal = displayManager.getApcLevel();
        checkAPCLevelSignal(signal);
    }

    public void getBackLight() {
        int signal = displayManager.getBackLight();
        checkAPCLevelSignal(signal);
    }

    public void initData() {
        addCallback();
        executor.execute(() -> {
            getApcLevel();
            getBackLight();
        });
    }

    private void addCallback() {
        displayManager.addCallback(TAG, new IDisplayManagerListener() {
            @Override
            public void getAPCCallback(int signal) {
                Log.d(TAG, "display--onApcLevelLimit: " + signal);
                checkAPCLevelSignal(signal);
            }

            @Override
            public void getBacklightModeCallback(int signal) {
                Log.d(TAG, "display--onApcLevelLimit: " + signal);
                backLightLiveData.postValue(signal);
            }

            @Override
            public void getSystemColorCallback(int status) {

            }
        });
        displayManager.registerListener();
    }

    private void checkAPCLevelSignal(int signal) {
        if (signal == CarNewEnergy.ApcLevelLimit.LEVEL_4 || signal == CarNewEnergy.ApcLevelLimit.LEVEL_5) {
            apcLevelLiveData.postValue(signal);
        } else {
            Log.d(TAG, "invalid signal: " + signal);
            Integer value = apcLevelLiveData.getValue();
            apcLevelLiveData.postValue(value == null ? CarNewEnergy.ApcLevelLimit.LEVEL_0 : value);
        }
    }
}
