package com.bitech.vehiclesettings.service.earlywarning;

import android.util.Log;

import com.bitech.platformlib.constants.CarLight;
import com.bitech.vehiclesettings.service.atmosphere.EffectDiapatcher;
import com.bitech.vehiclesettings.utils.CommonConst;

public class WarnDispatcher {
    private static final String TAG = WarnDispatcher.class.getSimpleName();
    private static int priority;
    private static volatile WarnDispatcher instance;

    private DoorOpenService doorOpenService = DoorOpenService.getInstance();
    private DowLeftService dowService = DowLeftService.getInstance();
    private FcwService fcwService = FcwService.getInstance();
    private LdwService ldwService = LdwService.getInstance();
    private ReverseRadarService reverseRadarService = ReverseRadarService.getInstance();
    private TrwService trwService = TrwService.getInstance();

    public static WarnDispatcher getInstance() {
        if (instance == null) {
            synchronized (WarnDispatcher.class) {
                if (instance == null) {
                    instance = new WarnDispatcher();
                }
            }
        }
        return instance;
    }

    public static int getPriority() {
        return priority;
    }

    /**
     * 调度
     * 级由高到低分别为：前方碰撞预警、车道偏离预警、DOW 预警、转向提醒、倒车雷达辅助、极致节能、下电熄灭、开门迎宾、语音助手、整车场景定义
     * （K 歌\影院\小憩\灯舞\极致节能等）、其他功能。
     */
    public void dispatch(int priority, int type, int... values) {
        Log.d(TAG, "[warn]dispatch priority: " + priority + ",type:" + type + ",values:" + String.valueOf(values));
        switch (priority) {
            case CommonConst.priority.priority_1:
                int value = 0;
                if (values != null && (values.length > 0)) {
                    value = values[0];
                } else {
                    return;
                }
                if (checkPriority(priority, CommonConst.priority.priority_1)) {
                    // 其他功能 预留 门开预警
                    if (value == CommonConst.CLOSE) {
                        resetPrio();
                        return;
                    }
                    // 1.中断当前氛围灯信号发送
                    EffectDiapatcher.getInstance().triggered();
                    // 2.根据类型处理氛围灯预警。判断当前氛围灯运行优先级
                    switch (type) {
                        case CommonConst.doorOpenWarn.fLWarn:
                            DoorOpenService.getInstance().fLWarn(value);
                            break;
                        case CommonConst.doorOpenWarn.fRWarn:
                            DoorOpenService.getInstance().fRWarn(value);
                            break;
                        case CommonConst.doorOpenWarn.rLWarn:
                            DoorOpenService.getInstance().rLWarn(value);
                            break;
                        case CommonConst.doorOpenWarn.rRWarn:
                            DoorOpenService.getInstance().rRWarn(value);
                            break;
                    }
                }
                break;
            case CommonConst.priority.priority_2:
                if (checkPriority(priority, CommonConst.priority.priority_2)) {
                    // 整车场景定义 预留
                }
                break;
            case CommonConst.priority.priority_3:
                if (checkPriority(priority, CommonConst.priority.priority_3)) {
                    // 语音助手 预留
                }
                break;
            case CommonConst.priority.priority_4:
                if (checkPriority(priority, CommonConst.priority.priority_4)) {
                    // 开门迎宾
                    Log.d(TAG, "dispatch: ");
                }
                break;
            case CommonConst.priority.priority_5:
                if (checkPriority(priority, CommonConst.priority.priority_5)) {
                    // 下电熄灭
                }
                break;
            case CommonConst.priority.priority_6:
                if (checkPriority(priority, CommonConst.priority.priority_6)) {
                    // 极致节能
                }
                break;
            case CommonConst.priority.priority_7:
                if (checkPriority(priority, CommonConst.priority.priority_7)) {
                    // 倒车雷达辅助
                    int rValue = 0;
                    if (values != null && (values.length > 0)) {
                        rValue = values[0];
                    } else {
                        return;
                    }
                    if (rValue != CarLight.PdcWarningType.BUZZER_OFF) {
                        EffectDiapatcher.getInstance().triggered();
                        ReverseRadarService.getInstance().start();
                    } else {
                        resetPrio();
                        ReverseRadarService.getInstance().stop();
                        EffectDiapatcher.getInstance().resumeTriggered();
                    }
                }
                break;
            case CommonConst.priority.priority_8:
                if (checkPriority(priority, CommonConst.priority.priority_8)) {
                    // 转向提醒
                    int rValue = 0;
                    if (values != null && (values.length > 0)) {
                        rValue = values[0];
                    } else {
                        return;
                    }
                    if (rValue == CarLight.DOWWarn.RLCR_1_DOWWarn_1 || rValue == CarLight.DOWWarn.RLCR_1_DOWWarn_2) {
                        EffectDiapatcher.getInstance().triggered();
                        DowLeftService.getInstance().start(rValue, rValue);
                    } else {
                        resetPrio();
                        ReverseRadarService.getInstance().stop();
                        EffectDiapatcher.getInstance().resumeTriggered();
                    }
                }
                break;
            case CommonConst.priority.priority_9:
                if (checkPriority(priority, CommonConst.priority.priority_9)) {
                    // DOW 预警 left
                    int dowValue = 0;
                    if (values != null && (values.length > 0)) {
                        dowValue = values[0];
                    } else {
                        return;
                    }
                    if (dowValue == CarLight.DOWWarn.RLCR_1_DOWWarn_1 || dowValue == CarLight.DOWWarn.RLCR_1_DOWWarn_2) {
                        EffectDiapatcher.getInstance().triggered();
                        DowLeftService.getInstance().start(type, dowValue);
                    } else {
                        resetPrio();
                        DowLeftService.getInstance().stop();
                        EffectDiapatcher.getInstance().resumeTriggered();
                    }
                }
                break;
            case CommonConst.priority.priority_10:
                if (checkPriority(priority, CommonConst.priority.priority_9)) {
                    // DOW 预警
                    int dowValue = 0;
                    if (values != null && (values.length > 0)) {
                        dowValue = values[0];
                    } else {
                        return;
                    }
                    if (dowValue == CarLight.DOWWarn.RRCR_1_DOWWarn_1 || dowValue == CarLight.DOWWarn.RRCR_1_DOWWarn_2) {
                        EffectDiapatcher.getInstance().triggered();
                        DowRightService.getInstance().start(type, dowValue);
                    } else {
                        resetPrio();
                        DowRightService.getInstance().stop();
                        EffectDiapatcher.getInstance().resumeTriggered();
                    }
                }
                break;
            case CommonConst.priority.priority_11:
                // 车道偏离预警触发
                if (checkPriority(priority, CommonConst.priority.priority_11)) {
                    int ldwValue = 0;
                    if (values != null && (values.length > 0)) {
                        ldwValue = values[0];
                    } else {
                        return;
                    }
                    if (ldwValue == CarLight.TrackingSt.WARNING) {
                        EffectDiapatcher.getInstance().triggered();
                        LdwService.getInstance().start(type, ldwValue);
                    } else {
                        resetPrio();
                        LdwService.getInstance().stop();
                        EffectDiapatcher.getInstance().resumeTriggered();
                    }
                }
                break;
            case CommonConst.priority.priority_12:
                if (checkPriority(priority, CommonConst.priority.priority_12)) {
                    // 前方碰撞预警
                    int fcwValue = 0;
                    if (values != null && (values.length > 0)) {
                        fcwValue = values[0];
                    } else {
                        return;
                    }
                    if (fcwValue == CarLight.FcwAcitveSt.LEVEL_2 || fcwValue == CarLight.FcwAcitveSt.LEVEL_3) {
                        EffectDiapatcher.getInstance().triggered();
                        FcwService.getInstance().start(fcwValue);
                    } else {
                        resetPrio();
                        FcwService.getInstance().stop();
                        EffectDiapatcher.getInstance().resumeTriggered();
                    }
                }
                break;
            default:
                break;
        }
    }

    private boolean checkPriority(int srcPri, int desPri) {
        if (srcPri <= desPri) {
            priority = desPri;
            return true;
        }
        return false;
    }

    private void resetPrio() {
        priority = 0;
        EffectDiapatcher.getInstance().resumeTriggered();

    }

}
