package com.bitech.vehiclesettings.presenter.light;

import static com.bitech.vehiclesettings.MyApplication.getContext;

import android.content.Context;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;

public class LightOutPresenter implements LightOutPresenterListener {
    private static final String TAG = LightOutPresenter.class.getSimpleName();
    private LightInBean lightInBean;
    private Context mContext;

    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(getContext(), BitechCar.CAR_LIGHT_MANAGER);

    public LightOutPresenter(Context context) {
        this.mContext = context;
    }


    /**
     * @param state
     */
    @Override
    public void setLampControl(int state) {

        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
                    carServer.setLightMainSwitchSts(CarLight.LightMainSwitchSts.OFF);
                    break;
                case 1:
                    // “位置灯”挡位
                    carServer.setLightMainSwitchSts(CarLight.LightMainSwitchSts.POSITION_LAMP_SWITCH);
                    break;
                case 2:
                    carServer.setLightMainSwitchSts(CarLight.LightMainSwitchSts.LOW_BEAM_SWITCH);
                    break;
                case 3:
                    carServer.setLightMainSwitchSts(CarLight.LightMainSwitchSts.AUTO);
                    break;
                default:
                    break;
            }

        }
//        Prefs.put(PrefsConst.L_LAMP_CONTROL, state);
    }

    /**
     * @return
     */
    @Override
    public int getLampControl() {
        int status = carServer.getLightMainSwitchSts();
        int rtnStatus = lampControlChange(status);
        Log.d(TAG, "getLampControl: " + rtnStatus + ",信号：" + status);
        return rtnStatus;
    }

    @Override
    public int lampControlChange(int status) {
        int pos = PrefsConst.DefaultValue.L_LAMP_CONTROL;
        switch (status) {
            case CarLight.LightMainSwitchSts.OFF:
                pos = 0;
                break;
            case CarLight.LightMainSwitchSts.AUTO:
                pos = 3;
                break;
            case CarLight.LightMainSwitchSts.POSITION_LAMP_SWITCH:
                pos = 1;
                break;
            case CarLight.LightMainSwitchSts.LOW_BEAM_SWITCH:
                pos = 2;
                break;
            default:
                break;
        }
        return pos;
    }

    @Override
    public void setLampHigh(int state) {
        // 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
                    carServer.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_3);
                    break;
                case 1:
                    carServer.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_2);
                    break;
                case 2:

                    carServer.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_1);
                    break;
                case 3:
                    carServer.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_0);
                    break;
            }
        }
    }


    @Override
    public int getLampHigh() {
        //  获取
        int status = carServer.getLowBeamHighSts();
        int vswLightHigh = hightChange(status);
        return vswLightHigh;
    }

    @Override
    public int hightChange(int status) {
        int vswLightHigh = 0;
        switch (status) {
            case CarLight.LowBeamHighSts.LEVEL_0:
                vswLightHigh = 3;
                break;
            case CarLight.LowBeamHighSts.LEVEL_1:
                vswLightHigh = 2;
                break;
            case CarLight.LowBeamHighSts.LEVEL_2:
                vswLightHigh = 1;
                break;
            case CarLight.LowBeamHighSts.LEVEL_3:
                vswLightHigh = 0;
                break;
        }
        return vswLightHigh;
    }

    @Override
    public void setRearFogLamp(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (state) {
                case 0:
                    carServer.setRearFogSw(CarLight.RearFogSw.OFF);
                    break;
                case 1:
                    carServer.setRearFogSw(CarLight.RearFogSw.ON);
                    break;
            }
        }
    }


    @Override
    public int getRearFogLamp() {
        int status = carServer.getRearFogLightSts();
        return status;
    }

    /**
     * @param status "0x0:Not Active
     *               0x1:0s
     *               0x2:10s
     *               0x3:20s
     *               0x4:30s
     *               0x5:60s
     *               0x6~0x7:Not Used      "
     */
    @Override
    public void setLampDelay(int status) {
        switch (status) {
            case 0:
                carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_0S);
                break;
            case 1:
                carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_10S);
                break;
            case 2:
                carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_20S);
                break;
            case 3:
                carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_30S);
                break;
            case 4:
                carServer.setLowBeamDelayOff(CarLight.BeamDelaySts.DELAY_60S);
                break;
        }
    }

    /**
     * @return
     */
    @Override
    public int getLampDelay() {
        int status = carServer.getBeamDelaySts();
        int vswLampDelay = delayChange(status);
        return vswLampDelay;
    }

    @Override
    public int delayChange(int status) {
        int vswLampDelay = 0;
        switch (status) {
            case CarLight.BeamDelaySts.NOT_ACTIVE:
                break;
            case CarLight.BeamDelaySts.DELAY_0S:
                vswLampDelay = 0;
                break;
            case CarLight.BeamDelaySts.DELAY_10S:
                vswLampDelay = 1;
                break;
            case CarLight.BeamDelaySts.DELAY_20S:
                vswLampDelay = 2;
                break;
            case CarLight.BeamDelaySts.DELAY_30S:
                vswLampDelay = 3;
                break;
            case CarLight.BeamDelaySts.DELAY_60S:
                vswLampDelay = 4;
                break;
        }
        return vswLampDelay;
    }

    /**
     * @param state
     */
    @Override
    public void setApproachingWelcome(int state) {
        carServer.setWelcomeOpenSetCmd(state);
    }

    /**
     * @return
     */
    @Override
    public int getApproachingWelcome() {
        int status = carServer.getWelcomeOpenStas();
        // L_APPROACHING_WELCOME
        return status;
    }


    /**
     * @param state
     */
    @Override
    public void setHighLowSwitch(int state) {
        // todo 需要智驾信号
        Prefs.put(PrefsConst.L_HIGH_LOW_SWITCH, state);
    }

    /**
     * IHCfunctionSts
     *
     * @return
     */
    @Override
    public int getHighLowSwitch() {
        // todo 需要智驾信号
        return Prefs.get(PrefsConst.L_HIGH_LOW_SWITCH, PrefsConst.DefaultValue.L_HIGH_LOW_SWITCH);
    }

    /**
     * @param state
     */
    @Override
    public void setIntelligentWelcome(int state) {
        // 设置只能迎宾灯信号，无返回信号 LiShowMod
        if (state == 1) {
            carServer.setLiShowMod(CarLight.LiShowMod.Mode1);
        } else {
            carServer.setLiShowMod(CarLight.LiShowMod.OFF);
        }
        // 与MCU对接接口
        carServer.intelligentWelcomeLight(state);
        Prefs.put(PrefsConst.L_INTELLIGENT_WELCOME, state);
    }

    /**
     * @return
     */
    @Override
    public int getIntelligentWelcome() {
        return Prefs.get(PrefsConst.L_INTELLIGENT_WELCOME, PrefsConst.DefaultValue.L_INTELLIGENT_WELCOME);
    }


}
