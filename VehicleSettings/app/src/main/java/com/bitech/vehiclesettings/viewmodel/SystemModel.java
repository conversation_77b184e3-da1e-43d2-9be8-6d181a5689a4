package com.bitech.vehiclesettings.viewmodel;

import android.util.Log;

import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.system.ISystemManagerListener;
import com.bitech.platformlib.manager.SystemManager;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SystemModel extends ViewModel {
    public static final String TAG = "SystemViewModel";
    // 分析与改进
    private final MediatorLiveData<Integer> analysis = new MediatorLiveData<>();
    // 自动校准
    private final MediatorLiveData<Integer> autoCalibration = new MediatorLiveData<>();
    // 摄像头权限授权
    private final MediatorLiveData<Integer> permissionSwitchCamera = new MediatorLiveData<>();
    // 麦克风权限授权
    private final MediatorLiveData<Integer> permissionSwitchMicrophone = new MediatorLiveData<>();
    // 位置权限授权
    private final MediatorLiveData<Integer> permissionSwitchLocation = new MediatorLiveData<>();
    // 油耗单位
    private final MediatorLiveData<Integer> fuelUnit = new MediatorLiveData<>();
    // 胎压单位
    private final MediatorLiveData<Integer> tirePressureUnit = new MediatorLiveData<>();
    // 电耗单位
    private final MediatorLiveData<Integer> powerConsumptionUnit = new MediatorLiveData<>();
    // 权限选项
    private final MediatorLiveData<Integer> permission = new MediatorLiveData<>();
    // 系统软件版本
    private final MutableLiveData<String> systemSoftwareVersion = new MutableLiveData<>();
    // 系统硬件版本
    private final MutableLiveData<String> systemHardwareVersion = new MutableLiveData<>();
    // TBox软件版本
    private final MutableLiveData<String> tBoxSoftwareVersion = new MutableLiveData<>();
    // TBox硬件版本
    private final MutableLiveData<String> tBoxHardwareVersion = new MutableLiveData<>();
    // 设备名称
    private final MutableLiveData<String> deviceInfo = new MutableLiveData<>();
    public MutableLiveData<String> gearPosition() {
        return tBoxSoftwareVersion;
    }

    public MutableLiveData<String> getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String info) {
        deviceInfo.postValue(info);
    }

    public void setAnalysis(Integer status) {
        analysis.postValue(status);
    }

    public MediatorLiveData<Integer> getAnalysis() {
        return analysis;
    }

    public MediatorLiveData<Integer> getAutoCalibration() {
        return autoCalibration;
    }

    public void setAutoCalibration(Integer status) {
        autoCalibration.postValue(status);
    }

    public MediatorLiveData<Integer> getPermissionSwitchCamera() {
        return permissionSwitchCamera;
    }

    public void setPermissionSwitchCamera(Integer status) {
        permissionSwitchCamera.postValue(status);
    }

    public MediatorLiveData<Integer> getPermissionSwitchMicrophone() {
        return permissionSwitchMicrophone;
    }

    public void setPermissionSwitchMicrophone(Integer status) {
        permissionSwitchMicrophone.postValue(status);
    }

    public MediatorLiveData<Integer> getPermissionSwitchLocation() {
        return permissionSwitchLocation;
    }

    public void setPermissionSwitchLocation(Integer status) {
        permissionSwitchLocation.postValue(status);
    }

    public MediatorLiveData<Integer> getFuelUnit() {
        return fuelUnit;
    }

    public void setFuelUnit(Integer status) {
        fuelUnit.postValue(status);
    }

    public MediatorLiveData<Integer> getTirePressureUnit() {
        return tirePressureUnit;
    }

    public void setTirePressureUnit(Integer status) {
        tirePressureUnit.postValue(status);
    }

    public MediatorLiveData<Integer> getPowerConsumptionUnit() {
        return powerConsumptionUnit;
    }

    public void setPowerConsumptionUnit(Integer status) {
        powerConsumptionUnit.postValue(status);
    }

    public MediatorLiveData<Integer> getPermission() {
        return permission;
    }

    public void setPermission(Integer status) {
        permission.postValue(status);
    }

    public MutableLiveData<String> getSystemSoftwareVersion() {
        return systemSoftwareVersion;
    }

    public void setSystemSoftwareVersion(String version) {
        systemSoftwareVersion.postValue(version);
    }

    public MutableLiveData<String> getSystemHardwareVersion() {
        return systemHardwareVersion;
    }

    public void setSystemHardwareVersion(String version) {
        systemHardwareVersion.postValue(version);
    }


    // 挡位
    public final MutableLiveData<Integer> gearPositionLiveData = new MutableLiveData<>(0);

    // 平台化接口
    private final SystemManager systemManager = (SystemManager) BitechCar.getInstance()
            .getServiceManager(BitechCar.CAR_SYSTEM_MANAGER);
    // 开启线程池
    private final ExecutorService executor = Executors.newCachedThreadPool();

    public void initData() {
        addCallBack();
        executor.execute(() -> {
            getGearPosition();
        });
    }

    /**
     * 获取档位
     */
    public void getGearPosition() {
        int signalVal = systemManager.getVCU_PRNDGearAct();
        if (signalVal == gearPositionLiveData.getValue()) return;
        gearPositionLiveData.postValue(signalVal);
    }

    private void addCallBack() {
        systemManager.addCallback(TAG, new ISystemManagerListener() {
            @Override
            public void callBackGearPosition(int signalVal) {
                Log.d(TAG, "档位 callBackGearPosition: " + signalVal);
                gearPositionLiveData.postValue(signalVal);
            }
        });
        systemManager.registerListener();
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        executor.shutdown();
    }
}
