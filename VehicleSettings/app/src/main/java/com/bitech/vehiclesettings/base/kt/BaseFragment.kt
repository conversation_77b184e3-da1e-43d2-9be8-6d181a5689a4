package com.bitech.vehiclesettings.base.kt

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: BaseFragment
 **/
abstract class BaseFragment<V : ViewDataBinding, VM : ViewModel> : Fragment() {

    // 定义fragment viewDataBinding对象，并延迟初始化
    protected var viewBinding: V? = null

    // 定义fragment viewModel对象，并延迟初始化
    protected lateinit var viewModel: VM

    // 日志标志位
    private val tag = javaClass.simpleName

    private var mIsFragmentVisibleFirst = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(tag, "onCreate : ${hashCode()}")
        // 在fragment创建时初始化ViewModel
        viewModel = ViewModelProvider(this)[getViewModel()]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        LogUtil.d(tag, "onCreateView : ${hashCode()}")
        // 在fragment视图创建时处初始化viewDataBinding
        viewBinding = getLayoutId(container)
        // 返回构建的fragment视图
        return viewBinding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        LogUtil.d(tag, "onViewCreated : ${hashCode()}")
        // 在视图被创建后，进行视图与ViewModel的绑定，绑定是否实现取决于子类是否重写该方法
        bindViewModel(viewBinding!!, viewModel)
        // 初始化视图
        initView()
        // 初始化监听
        intiListener()
        // 初始化viewModel订阅
        initObserve()
        // 初始化数据
        if (mIsFragmentVisibleFirst) {
            mIsFragmentVisibleFirst = false
            initData()
        }
    }

    override fun onStart() {
        super.onStart()
        LogUtil.d(tag, "onStart : ${hashCode()}")
    }

    override fun onResume() {
        super.onResume()
        LogUtil.d(tag, "onResume : ${hashCode()}")
    }

    override fun onPause() {
        LogUtil.d(tag, "onPause : ${hashCode()}")
        super.onPause()
    }

    override fun onStop() {
        LogUtil.d(tag, "onStop : ${hashCode()}")
        super.onStop()
    }

    override fun onDestroyView() {
        LogUtil.d(tag, "onDestroyView : ${hashCode()}")
        super.onDestroyView()
        viewBinding?.unbind()
        viewBinding = null
    }

    override fun onDestroy() {
        LogUtil.d(tag, "onDestroy : ${hashCode()}")
        // 取消viewModel订阅
        removeObserve()
        super.onDestroy()
    }

    /**
     * 实现fragment之间的跳转.
     *
     * @param tabNameId  选项菜单名称ID
     */
    fun toFragment(tabNameId: Int) {
//        activity.fragmentToFragment(tabNameId)
    }

    /**
     * 获取布局资源文件id,抽象方法，后续Activity需实现该方法绑定视图文件.
     *
     * @return 布局资源文件id
     */
    abstract fun getLayoutId(container: ViewGroup?): V

    /**
     * 获取ViewModel类，抽象方法，后续ViewModel需继承实现该方法返回ViewModel本身.
     *
     * @return
     */
    abstract fun getViewModel(): Class<VM>

    /**
     * 初始化fragment视图控件.
     *
     */
    protected abstract fun initView()

    /**
     * 初始化fragment控件监听.
     *
     */
    protected abstract fun intiListener()

    /**
     * 初始化fragment创建时数据.
     *
     */
    protected abstract fun initData()

    /**
     * 初始化viewModel订阅.
     *
     */
    protected abstract fun initObserve()

    /**
     * 取消viewModel订阅.
     *
     */
    protected abstract fun removeObserve()

    /**
     * viewDataBinding与ViewModel的双向绑定.
     */
    open fun bindViewModel(viewBinding: V, viewModel: VM) {
        // TODO: 子类可以重写该方法完成绑定,只有需要绑定的时候实现该方法就行.
    }
}
