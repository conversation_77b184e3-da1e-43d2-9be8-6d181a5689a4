package com.bitech.vehiclesettings.viewmodel

import android.content.Context
import android.database.ContentObserver
import android.net.NetworkInfo
import android.net.Uri
import android.net.wifi.ScanResult
import android.net.wifi.SupplicantState
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.base.kt.BaseViewModel
import com.bitech.vehiclesettings.bean.WifiDeviceBean
import com.bitech.vehiclesettings.bean.WifiHotspotBean
import com.bitech.vehiclesettings.broadcast.SliceReceiver
import com.bitech.vehiclesettings.broadcast.WifiReceiver
import com.bitech.vehiclesettings.common.SettingConstans
import com.bitech.vehiclesettings.manager.CarDmManager
import com.bitech.vehiclesettings.manager.CarWifiManager
import com.bitech.vehiclesettings.manager.CountDownTimeManager
import com.bitech.vehiclesettings.provider.ProviderURI
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.EToast
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.view.connect.WifiFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Timer
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @ClassName: WifiViewModel
 *
 * @Description: WIFI页面Fragment对应的view-model.
 **/
class WifiViewModel : BaseViewModel(), WifiReceiver.WifiStateListener {

    // WIFI管理对象
    private var carWifiManager = CarWifiManager.instance

    private val wifiFragment = WifiFragment()

    // 设备管理对象
    private var carDmManager = CarDmManager.instance

    // ContentResolver对象
    private var contentResolver = MyApplication.getContext().contentResolver

    // WIFI广播接收器对象
    private var wifiReceiver: WifiReceiver? = null

    // WIFI连接超时倒计时管理对象
    private var wifiConnectedTimerTask: CountDownTimeManager? = null

    // 系统软键盘对象
    private lateinit var immInputManager: InputMethodManager

    // 网络通知系统属性uri对象
    private lateinit var networkUri: Uri

    // 当前连接的WIFI名称
    private var wifiSsid = ""

    // wifi连接过程中上一次的状态
    private var wifiConnectionPreState = NetworkInfo.State.DISCONNECTED

    // 当前连接网络WIFI的NetWorkId
    private var wifiNetWorkId = -1

    // WIFI连接类型,0->从扫描列表连接，1->从已连接列表连接
    private var wifiConnectedType = -1

    // 当前是否存在网络处于连接中状态
    private var isWifiConnecting = false

    // 是否因为修改账号和密码导致的热点关闭
    private var isCloseHotspotForUpdateHotspot = false

    // 热点连接设备列表
    private var hotspotConnectedList = CopyOnWriteArrayList<WifiHotspotBean>()

    // WIFI可用扫描列表
    private var wifiScanList = CopyOnWriteArrayList<WifiDeviceBean>()

    // WIFI已连接WIFI列表
    private var wifiConnectedList = CopyOnWriteArrayList<WifiDeviceBean>()

    // WIFI开关状态LiveData
    var wifiSwitchLiveData = MutableLiveData<Boolean>()

    // 热点开关状态LiveData
    var hotspotSwitchLiveData = MutableLiveData<Boolean>()

    // 热点名称LiveData
    var hotspotNameLiveData = MutableLiveData<String>()

    // 热点密码LiveData
    var hotspotPasswordLiveData = MutableLiveData<String>()

    //  热点密码修改长度不足八位
    var hotspotPasswordErrorLD = MutableLiveData<Boolean>()

    // 网络通知开关状态LiveData
    var networkNotSwitchLiveData = MutableLiveData<Boolean>()

    // 热点连接列表LiveData
    var hotspotConnectedListLiveData = MutableLiveData<CopyOnWriteArrayList<WifiHotspotBean>>()

    // WIFI可用扫描列表LiveData
    var wifiScanListLiveData = MutableLiveData<CopyOnWriteArrayList<WifiDeviceBean>>()

    // WIFI已连接列表LiveData
    var wifiConnectedListLiveData = MutableLiveData<CopyOnWriteArrayList<WifiDeviceBean>>()

    // WIFI列表扫描状态,true扫描中，false停止扫描
    var wifiScanningState = MutableLiveData<Boolean>()

    // WIFI连接结果
    var wifiConnectedResultLiveData = MutableLiveData<Int>()

    // 搜索关键词LiveData
    var searchKeywordLiveData = MutableLiveData<String>()

    // handler对象
    var handler: Handler? = Handler(Looper.getMainLooper())

    // ContentObserver订阅对象
    private val networkNoticeObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
        override fun onChange(selfChange: Boolean, uri: Uri?) {
            super.onChange(selfChange, uri)
            when (uri) {
                networkUri -> {
                    val networkValue = Settings.System.getString(
                        MyApplication.getContext().contentResolver,
                        Contacts.NETWORK_NOTIFICATION_KEY
                    )
                    LogUtil.d(TAG, "networkNoticeObserver : network = $networkValue")
                    networkNotSwitchLiveData.postValue(
                        TextUtils.equals(
                            networkValue,
                            CarWifiManager.NETWORK_NOTIFICATION_OPEN
                        )
                    )
                }

                Settings.System.getUriFor(Contacts.SETTINGS_SEARCH_KEYWORD_KEY) -> {
                    // 搜索关键词发生改变时,获取搜索关键词
                    getSearchKeyword()
                }
            }
        }
    }

    init {
        viewModelScope.launch(Dispatchers.Default) {
            LogUtil.d(TAG, "init : ")
            // 初始化系统软件盘
            immInputManager = MyApplication.getContext()
                .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            // 注册WIFI广播监听
            wifiReceiver = WifiReceiver()
            wifiReceiver!!.registerWifiStateListener(
                <EMAIL>,
                this@WifiViewModel
            )
            // 注册网络通知属性监听
            networkUri = Settings.System.getUriFor(Contacts.NETWORK_NOTIFICATION_KEY)
            contentResolver.registerContentObserver(
                networkUri,
                true,
                networkNoticeObserver
            )
            // 注册ContentObserver订阅
            contentResolver.registerContentObserver(
                Settings.System.getUriFor(Contacts.SETTINGS_SEARCH_KEYWORD_KEY),
                true,
                networkNoticeObserver
            )
        }
    }

    /**
     * 初始化页面初始数据.
     *
     */
    fun initData() {
        LogUtil.d(TAG, "initData : ")
        // 获取搜索关键词
        getSearchKeyword()
    }

    /**
     * 是否支持WIFI
     *
     * @return
     */
    fun isSupportWifi(): Boolean {
        return true
    }

    /**
     * 获取搜索关键词.
     *
     */
    private fun getSearchKeyword() {
        if(contentResolver == null){
            LogUtil.e(TAG, "getSearchKeyword : contentResolver == null")
            return
        }
        viewModelScope.launch(Dispatchers.Default) {
            val keyword =
                Settings.System.getString(contentResolver, Contacts.SETTINGS_SEARCH_KEYWORD_KEY)
            // 判断搜索关键词是否在WIFI页面内,只需在二级菜单中搜索该项
            val wifiMenuBean =
                Contacts.wifiMenuBean.secondMenuList.find {
                    TextUtils.equals(it.secondMenuName, keyword)
                }
            if (wifiMenuBean != null) {
                LogUtil.i(TAG, "getSearchKeyword : keyword = $keyword")
                // 在WIFI的页面内，则定位到页面内控件关键词位置
                searchKeywordLiveData.postValue(keyword)
                // 重置搜索关键词
                Settings.System.putString(
                    contentResolver,
                    Contacts.SETTINGS_SEARCH_KEYWORD_KEY,
                    Contacts.SETTINGS_SEARCH_KEYWORD_DEFAULT
                )
            }
        }
    }

    //判断是否是曾经连接过的wifi
    fun isWifiConnected(wifiDeviceBean: WifiDeviceBean): Boolean {
        return carWifiManager.isWifiConnected(wifiDeviceBean)
    }

    /**
     * 连接WIFI.
     *
     * @param wifiDeviceBean WIFI对象
     * @param type 连接方式：0->从WIFI扫描列表进行连接，1->从已连接列表进行连接
     */
    fun connectedWifi(wifiDeviceBean: WifiDeviceBean, type: Int) {
        viewModelScope.launch(Dispatchers.Default) {
            // 扫描一次
            carWifiManager.startWifiScan()
            if (isWifiOnline(wifiDeviceBean)) {
                isWifiConnecting = true
                wifiConnectedType = type
                // 开始连接计时
                startWifiConnectedTimerTask()
                when (type) {
                    Contacts.WIFI_CONNECTED_FROM_SCAN_LIST -> {
                        // 如从WIFI扫描列表中进行连接，则更新扫描列表对象密码
                        Log.d(TAG, "connectedWifi: zhc6whu"+wifiScanList.find { it.wifiSSID == wifiDeviceBean.wifiSSID })
                        val wifiBean = wifiScanList.find { it.wifiSSID == wifiDeviceBean.wifiSSID }
                        if (wifiBean != null) {
                            LogUtil.d(
                                TAG,
                                "connectedWifi : password = ${wifiDeviceBean.wifiPassword}"
                            )
                            // 先寻找当前已连接的WIFI
                            val wifiConnected =
                                wifiConnectedList.find {
                                    "\"" + it.wifiSSID + "\"" ==
                                            carWifiManager.getConnectionInfo()?.ssid
                                }
                            if (wifiConnected != null) {
                                // 若当前存在已连接的WIFI,连接前先设置断开状态
                                wifiConnected.wifiConnectedState = NetworkInfo.State.DISCONNECTED
                                updateWifiConnectedDeviceList()
                            }
                            wifiBean.wifiConnectedState = NetworkInfo.State.CONNECTING
                            updateWifiScanDeviceList()
                            wifiBean.wifiPassword = wifiDeviceBean.wifiPassword
                            wifiBean.wifiConfiguration =
                                carWifiManager.createWifiConfiguration(wifiBean)
                            // 连接WIFI
                            carWifiManager.connectedWifi(
                                wifiBean,
                                Contacts.WIFI_CONNECTED_FROM_SCAN_LIST
                            )
                        }
                    }

                    Contacts.WIFI_CONNECTED_FROM_CONNECTED_LIST -> {
                        // 若从已连接列表进行连接，则直接连接
                        val wifiConnectedBean =
                            wifiConnectedList.find { it.wifiSSID == wifiDeviceBean.wifiSSID }
                        if (wifiConnectedBean != null) {
                            LogUtil.d(TAG, "connectedWifi : name = ${wifiDeviceBean.wifiSSID}")
                            // 先寻找当前已连接的WIFI
                            val wifiConnected =
                                wifiConnectedList.find {
                                    "\"" + it.wifiSSID + "\"" ==
                                            carWifiManager.getConnectionInfo()?.ssid
                                }
                            if (wifiConnected != null) {
                                // 若当前存在已连接的WIFI,连接前先设置断开状态
                                wifiConnected.wifiConnectedState = NetworkInfo.State.DISCONNECTED
                            }
                            wifiConnectedBean.wifiConnectedState = NetworkInfo.State.CONNECTING
                            updateWifiConnectedDeviceList()
                            // 连接WIFI
                            carWifiManager.connectedWifi(
                                wifiDeviceBean,
                                Contacts.WIFI_CONNECTED_FROM_CONNECTED_LIST
                            )
                        }
                    }
                }
            } else {
                // WIFI不在线，无法接入网络
                wifiConnectedResultLiveData.postValue(Contacts.WIFI_CONNECTED_FAIL)
            }
        }
    }

    /**
     * 判断当前是否存在
     *
     * @return
     */
    fun isWifiConnecting(): Boolean {
        LogUtil.d(TAG, "isWifiConnecting : is wifi connecting = $isWifiConnecting")
        return isWifiConnecting
    }

    /**
     * 断开当前网络连接.
     *
     * @param wifiDeviceBean 待断开的网络.
     */
    fun disconnectedWifi(wifiDeviceBean: WifiDeviceBean) {
        LogUtil.d(TAG, "disconnectedWifi : name = ${wifiDeviceBean.wifiSSID}")
        viewModelScope.launch(Dispatchers.Default) {
            // 断开当前已连接的WIFI
            carWifiManager.disconnectedWifi(wifiDeviceBean)
        }
    }

    /**
     * 删除WIFI配置信息(忽略网络)
     *
     * @param wifiDeviceBean 待删除的WIFI配置
     */
    fun deleteWifiConfig(wifiDeviceBean: WifiDeviceBean) {
        LogUtil.d(TAG, "deleteWifiConfig : name = ${wifiDeviceBean.wifiSSID}")
        viewModelScope.launch(Dispatchers.Default) {
            // 删除WIFI配置信息
            carWifiManager.deleteWifiConfig(wifiDeviceBean)
            // 从已连接列表移除该配置信息
            val wifiConnectedBean =
                wifiConnectedList.find { it.wifiSSID == wifiDeviceBean.wifiSSID }
            if (wifiConnectedBean != null) {
                wifiConnectedList.remove(wifiConnectedBean)
            }
            // 更新Wifi已连接列表
            updateWifiConnectedDeviceList()
            updateWifiScanList()
            Handler(Looper.getMainLooper()).postDelayed({
                updateWifiScanList()
            }, 2000L)   // 2000 ms = 2 s
        }
    }

    /**
     * 开始WIFI扫描.
     *
     */
    fun startWifiScan(first:Boolean) {
        // 开始WIFI扫描
        carWifiManager.startWifiScan(wifiScanningState)
        //当前是否已经连接了wifi
        if (carWifiManager.getConnectionInfo()?.ssid == "<unknown ssid>") {
            if (first&& carWifiManager.getWifiScanResult()?.size!! >1){
                Handler(Looper.getMainLooper()).postDelayed({
                    EToast.showToast(MyApplication.getContext(), "检测到多个新网络可连接", Toast.LENGTH_SHORT, false)
                }, 1000L)   // 2000 ms = 2 s
                Log.d(TAG, "startWifiScan:"+carWifiManager.getWifiScanResult())
            }
            if (first&& carWifiManager.getWifiScanResult()?.size!! ==1){
                val scanResults = carWifiManager.getWifiScanResult()
                scanResults?.forEach { result ->
                    // 提取 SSID 并去掉引号
                    val ssid = result.SSID.replace("\"", "")
                    LogUtil.d(TAG, "WiFi Name: $ssid")
                    EToast.showToast(MyApplication.getContext(), "检测到${ssid}可连接", Toast.LENGTH_SHORT, false)
                }
                Log.d(TAG, "startWifiScan: zhc6whu:"+carWifiManager.getWifiScanResult())
            }
        }
        wifiScanList.clear()
        carWifiManager.startWifiScan(wifiScanningState)
    }

    /**
     * 停止WIFI扫描.
     *
     */
    fun stopWifiScan() {
        // 停止WIFI扫描
        LogUtil.d(TAG, "stopWifiScan")
        // 停止WIFI扫描
        carWifiManager.stopWifiScan(wifiScanningState)
    }

    /**
     * 获取WIFI扫描列表.
     *
     * @return WIFI扫描列表
     */
    fun getWifiScanList(): CopyOnWriteArrayList<WifiDeviceBean> {
        LogUtil.d(TAG, "getWifiScanList : ")
        wifiScanListLiveData.postValue(wifiScanList)
        return wifiScanList
    }

    /**
     * 获取WIFI已连接列表.
     *
     * @return WIFI已连列表
     */
    fun getWifiConnectedList(): CopyOnWriteArrayList<WifiDeviceBean> {
        LogUtil.d(TAG, "getWifiConnectedList : ")
        // 获取WIFI配置列表并解析
        getWifiConfigList()
        return wifiConnectedList
    }

    /**
     * 更新热点已连接设备列表.
     *
     */
    fun updateHotspotConnectedList() {
        LogUtil.d(TAG, "updateHotspotConnectedList : ")
        viewModelScope.launch(Dispatchers.IO) {
            // 从数据库读取热点已连接列表数据
            val allHotspotDevices =
                MyApplication.settingDataBase.wifiHotspotDao().getAllHotspotDevices()
            Log.d(TAG, "updateHotspotConnectedList zhc6whu 热点列表更新: "+MyApplication.settingDataBase.wifiHotspotDao().getAllHotspotDevices())
            allHotspotDevices?.forEach { wifiHotspotBean ->
                if (!hotspotConnectedList.contains(wifiHotspotBean)) {
                    // 热点已连接设备列表在列表中不存在，则添加到列表
                    hotspotConnectedList.add(wifiHotspotBean)
                }
            }
            // 更新热点已连接列表数据
            hotspotConnectedListLiveData.postValue(hotspotConnectedList)
        }
    }

    /**
     * 获取热点连接设备列表.
     *
     * @return
     */
    fun getHotspotConnectedList(): CopyOnWriteArrayList<WifiHotspotBean> {
        LogUtil.d(TAG, "getHotspotConnectedList zhc6whu 热点列表: $hotspotConnectedList")
        return hotspotConnectedList
    }

    /**
     * 当前是否存在Cp连接.
     *
     * @return 状态
     */
    fun hasConnectDeviceCp(): Boolean {
        val hasConnectDeviceCp = carDmManager.hasConnectDeviceCp()
        LogUtil.i(TAG, "hasConnectDeviceCp : hasConnectDeviceCp = $hasConnectDeviceCp")
        return hasConnectDeviceCp
    }

    /**
     * 当前是否存在AA连接.
     *
     * @return 状态
     */
    fun hasConnectDeviceAa(): Boolean {
        val hasConnectDeviceAa = carDmManager.hasConnectDeviceAa()
        LogUtil.i(TAG, "hasConnectDeviceAa : hasConnectDeviceAa = $hasConnectDeviceAa")
        return hasConnectDeviceAa
    }

    /**
     * 断开连接的CP设备.
     *
     */
    fun disconnectDeviceCp() {
        LogUtil.d(TAG, "connectDeviceCp : ")
        carDmManager.disconnectDeviceCp()
    }

    /**
     * 断开连接的AA设备.
     *
     */
    fun disconnectDeviceAa() {
        LogUtil.d(TAG, "disconnectDeviceAa : ")
        carDmManager.disconnectDeviceAa()
    }

    /**
     * 根据WIFI开关状态，设置系统WIFI状态.
     *
     * @param switchState 设置页面WIFI开关状态.
     */
    fun setWifiState(switchState: Boolean) {
        LogUtil.d(TAG, "setWifiState : switch state = $switchState")
        carWifiManager.setWifiState(switchState)
    }

    /**
     * 获取WIFI状态.
     *
     * @return Boolean WIFI开关状态
     */
    fun getWifiState(): Boolean {
        val state = carWifiManager.getWifiState()
        LogUtil.d(TAG, "getWifiState : state = $state")
        wifiSwitchLiveData.postValue(state)
        return state
    }

    /**
     * 根据热点开关状态，设置系统热点状态.
     *
     * @param switchState 设置页面的热点开关状态.
     */
    fun setHotspotState(switchState: Boolean) {
        LogUtil.d(TAG, "setHotspotState : switch state = $switchState")
        viewModelScope.launch(Dispatchers.Default) {
            if (switchState) {
                // 开启热点
                carWifiManager.openWifiHotspot()
            } else {
                // 关闭热点
                carWifiManager.closeWifiHotspot()
            }
        }
    }

    /**
     * 获取热点状态.
     *
     * @return Boolean 热点开关状态
     */
    fun getHotspotState(): Boolean {
        val state = carWifiManager.getHotspotState()
        LogUtil.d(TAG, "getHotspotState : switch state = $state")
        hotspotSwitchLiveData.postValue(state)
        return state
    }

    /**
     * 获取WIFI热点名称.
     *
     * @return String 名称
     */
    fun getWifiHotspotName(): String {
        val hotspotName = carWifiManager.getWifiHotspotSSID()
        LogUtil.d(TAG, "getWifiHotspot : hotspotName = $hotspotName")
        val hotspotNameNew=carWifiManager.getWifiHotspotSSID()
        return hotspotNameNew
    }

    /**
     * 获取WIFI热点密码.
     *
     * @return String 密码
     */
    fun getWifiHotspotPassword(): String {
        val hotspotPassword = carWifiManager.getWifiHotspotPassword()
        LogUtil.d(TAG, "getWifiHotspot : hotspotPassword = $hotspotPassword")
        return hotspotPassword
    }

    /**
     * 更新热点名称.
     *
     * @param hotspotName 热点名称
     */
    fun updateHotspotName(hotspotName: String) {
        LogUtil.d(TAG, "updateHotspotInfo : name = $hotspotName")
        viewModelScope.launch(Dispatchers.Default) {
            if (TextUtils.equals(hotspotName, carWifiManager.getWifiHotspotSSID())
                || hotspotName.isBlank()
            ) {
                // 热点名称没改动或热点名称为空字符串，则保持之前的名称不变
                hotspotNameLiveData.postValue(carWifiManager.getWifiHotspotSSID())
            } else {
                // 更新热点信息
                carWifiManager.updateHotspotInfo(
                    hotspotName,
                    carWifiManager.getWifiHotspotPassword()
                )
                hotspotNameLiveData.postValue(hotspotName)
                isCloseHotspotForUpdateHotspot = true
                clearAllHotspotDevices()
            }
        }
    }

    /**
     * 更新热点密码.
     * @param hotspotPassword 热点密码
     */
    fun updateHotspotPassword(hotspotPassword: String) {
        LogUtil.d(TAG, "updateHotspotInfo : password = $hotspotPassword")
        viewModelScope.launch(Dispatchers.Default) {
            if (TextUtils.equals(hotspotPassword, carWifiManager.getWifiHotspotPassword())) {
                // 热点密码没有改动，则保留之前的密码
                hotspotPasswordLiveData.postValue(carWifiManager.getWifiHotspotPassword())
            } else if (hotspotPassword.length in 1..7) {
                // 密码长度不足8为，保留之前的密码
                hotspotPasswordLiveData.postValue(carWifiManager.getWifiHotspotPassword())
                hotspotPasswordErrorLD.postValue(true)
            }//加上 判断，是否包含大小写字母、阿拉伯数字、特殊字符这四个条件
            else if (!checkPasswordComplexity(hotspotPassword)){
                // 密码复杂度不足，保留之前的密码
                hotspotPasswordLiveData.postValue(carWifiManager.getWifiHotspotPassword())
                hotspotPasswordErrorLD.postValue(true)
            }
            else{
                isCloseHotspotForUpdateHotspot = true
                // 更新密码
                carWifiManager.updateHotspotInfo(
                    carWifiManager.getWifiHotspotSSID(),
                    hotspotPassword
                )
                clearAllHotspotDevices()
                Log.d(TAG, "updateHotspotPassword zhc6whu 更新密码: $isCloseHotspotForUpdateHotspot")
            }
        }
    }

    /**
     * 检查密码复杂度
     * @param password 密码
     * @return 是否符合复杂度要求（至少包含大小写字母、数字、特殊字符中的3种）
     */
    private fun checkPasswordComplexity(password: String): Boolean {
        // 定义特殊字符集合
        val specialChars = setOf('!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '_', '+', '=', '{', '}', '[', ']', '|', '\\', ':', ';', '"', '\'', '<', '>', ',', '.', '?', '/')

        var hasUpperCase = false
        var hasLowerCase = false
        var hasDigit = false
        var hasSpecialChar = false

        for (char in password) {
            when {
                char.isUpperCase() -> hasUpperCase = true
                char.isLowerCase() -> hasLowerCase = true
                char.isDigit() -> hasDigit = true
                specialChars.contains(char) -> hasSpecialChar = true
            }
        }

        // 四种必须全部满足
        return hasUpperCase && hasLowerCase && hasDigit && hasSpecialChar
    }

    /**
     * 根据网络开关状态，设置网络开关通知状态.
     *
     * @param switchState 网络通知开关状态
     */
    fun setNetWorkNotification(switchState: Boolean) {
        LogUtil.d(TAG, "setNetWorkNotification : switchState = $switchState")
        carWifiManager.setNetWorkNotification(switchState)
    }

    /**
     * 获取网络开关状态.
     *
     * @return Boolean
     */
    fun getNetWorkNotification(): Boolean {
        val state = carWifiManager.getNetWorkNotification()
        LogUtil.d(TAG, "getNetWorkNotification : state =$state")
        networkNotSwitchLiveData.postValue(state)
        return state
    }

    /**
     * 隐藏软键盘.
     *
     * @param view 焦点视图
     */
    fun hideSoftInputFromWindow(view: View) {
        LogUtil.i(TAG, "hideSoftInputFromWindow : ")
        immInputManager.hideSoftInputFromWindow(view.windowToken, 0)
    }

    /**
     * 显示软键盘.
     *
     * @param editText 编辑控件
     */
    fun showSoftInputFromWindow(editText: EditText) {
        LogUtil.i(TAG, "showSoftInputFromWindow : ")
        immInputManager.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT)
    }

    /**
     * 将接入热点的设备添加至热点连接列表.
     *
     * @param hotspotMac 接入热点设备MAC地址
     */
    private fun addHotspotList(hotspotMac: String,hotspotName: String) {
        viewModelScope.launch(Dispatchers.Default) {
            // 获取接入热点设备名称
            val hotspotConnectedName = hotspotName
            LogUtil.d(TAG, "addHotspotList : hotspotConnectedName = $hotspotConnectedName")
            // 构建接入热点设备对象
            val wifiHotspotBean = WifiHotspotBean(hotspotMac)
            // 设置接入设备名称
            wifiHotspotBean.hotspotName = hotspotConnectedName
            viewModelScope.launch(Dispatchers.IO) {
                val dao = MyApplication.settingDataBase.wifiHotspotDao()
                // 先查是否已存在
                val existing = dao.getDeviceByMac(hotspotMac)
                // 添加至数据库
                if (existing == null) {
                    MyApplication.settingDataBase
                        .wifiHotspotDao().insertHotspotDevice(wifiHotspotBean)
                }
            }
            // 添加至热点已连接列表
            hotspotConnectedList.add(wifiHotspotBean)
            // 刷新列表数据
            hotspotConnectedListLiveData.postValue(hotspotConnectedList)
            SliceReceiver.notifyChange(ProviderURI.BROADCAST)
        }
    }

    /**
     * 将断开热点的设备添加至热点连接列表.
     *
     * @param hotspotMac 断开热点设备MAC地址
     */
    private fun removeHotspotList(hotspotMac: String) {
        viewModelScope.launch(Dispatchers.Default) {
            // 获取接入热点设备名称
            val hotspotConnectedName = carWifiManager.getConnectedHotspotDevices(hotspotMac)
            LogUtil.d(TAG, "removeHotspotList : hotspotConnectedName = $hotspotConnectedName")
            // 构建断开热点设备对象
            val wifiHotspotBean = WifiHotspotBean(hotspotMac)
            // 设置断开设备名称
            wifiHotspotBean.hotspotName = hotspotConnectedName
            viewModelScope.launch(Dispatchers.IO) {
                // 从数据库移除
                MyApplication.settingDataBase
                    .wifiHotspotDao().deleteHotspotDevice(wifiHotspotBean)
            }
            // 从热点已连接列表移除该设备
            hotspotConnectedList.remove(wifiHotspotBean)
            // 刷新列表数据
            hotspotConnectedListLiveData.postValue(hotspotConnectedList)
        }
    }

    /**
     * 一键清空已连接热点设备列表
     */
    fun clearAllHotspotDevices() {
        viewModelScope.launch(Dispatchers.IO) {
            // 1. 数据库层：删除整张表
            MyApplication.settingDataBase
                .wifiHotspotDao()
                .deleteAllHotspotDevice()   // 你需要在 DAO 里补充这个方法

            // 2. 内存层：清空列表并通知 UI
            withContext(Dispatchers.Main) {    // 切回主线程只是为了 postValue
                hotspotConnectedList.clear()
                hotspotConnectedListLiveData.value = hotspotConnectedList   // 或 postValue
            }
        }
    }

    /**
     * 清空热点已连接列表.
     *
     */
    private fun clearHotspotConnectedList() {
        LogUtil.d(TAG, "clearHotspotConnectedList : ")
        viewModelScope.launch(Dispatchers.IO) {
            // 清空热点已连接数据库表内容
            MyApplication.settingDataBase.wifiHotspotDao().deleteAllHotspotDevice()
            // 清空已连接设备列表
            hotspotConnectedList.clear()
        }
    }

    /**
     * 刷新扫描列表数据.
     *
     */
    fun updateWifiScanDeviceList() {
        LogUtil.d(
            TAG,
            "updateWifiScanDeviceList : 更新后数据wifiScanList size = ${wifiScanList.size} "
        )
        // 对wifi扫描列表排序
        wifiScanList.sort()
        wifiScanListLiveData.postValue(wifiScanList)
    }

    /**
     * 刷新已连接WIFI列表数据.
     *
     */
    fun updateWifiConnectedDeviceList() {
        LogUtil.d(
            TAG,
            "updateWifiConnectedDeviceList :wifiConnectedList connected size = ${wifiConnectedList.size} "
        )
        // 对已连接WIFI列表进行排序
        wifiConnectedList.sort()
        wifiConnectedListLiveData.postValue(wifiConnectedList)
    }

    /**
     * 获取WIFI配置列表，并解析为WIFI已连接列表
     *
     */
    private fun getWifiConfigList() {
        viewModelScope.launch(Dispatchers.Default) {
            // 先清空WIFI已连接列表
            wifiConnectedList.clear()
            val wifiInfo = carWifiManager.getConnectionInfo()
            if (wifiInfo != null) {
                // 获取系统中保存的所有 WiFi 配置信息
                val wifiConfigurationList = carWifiManager.getWifiConfigList()
                wifiConfigurationList?.forEach { wifiConfiguration ->
                    // 检查当前配置是否是正在连接的WiFi
                    if (wifiConfiguration.SSID == wifiInfo.ssid) {
                        // 将当前连接的WiFi配置转换为WifiDeviceBean
                        val wifiDeviceBean = wifiConfigurationToWifiDeviceBean(wifiConfiguration)
                        // 更新当前连接的WiFi状态
                        if (wifiDeviceBean != null) {
                            updateCurrentConnectedWifi(wifiDeviceBean)
                        }
                    }
                }
            }
            //getWifiConfigList方法用于获取系统中保存的所有 WiFi 配置信息，并将结果存储在wifiConfigurationList中。
//            val wifiConfigurationList = carWifiManager.getWifiConfigList()
//            wifiConfigurationList?.forEach { wifiConfiguration ->
//                if (wifiConfiguration.BSSID != null) {
//                    wifiConnectedList.add(wifiConfigurationToWifiDeviceBean(wifiConfiguration))
//                }
//            }
            // 更新已连接WIFI列表
            updateWifiConnectedDeviceList()
        }
    }
    /**
     * 更新当前正在连接的WiFi状态
     */
    private fun updateCurrentConnectedWifi(wifiDeviceBean: WifiDeviceBean) {
        // 查找当前连接的WiFi是否已在列表中
        val existingWifi = wifiConnectedList.find { it.wifiSSID == wifiDeviceBean.wifiSSID }
        if (existingWifi != null) {
            // 如果存在，更新其状态
            existingWifi.wifiConnectedState = wifiDeviceBean.wifiConnectedState
            existingWifi.wifiNetWorkId = wifiDeviceBean.wifiNetWorkId
            existingWifi.wifiCapabilities = wifiDeviceBean.wifiCapabilities
            existingWifi.wifiPassword = wifiDeviceBean.wifiPassword
        } else {
            // 如果不存在，添加到列表
            wifiConnectedList.add(wifiDeviceBean)
        }
        // 更新已连接WIFI列表
        updateWifiConnectedDeviceList()
    }
    /**
     * 将WIFI配置文件解析为WIFI实体对象.
     *
     * @param wifiConfiguration wifi配置文件
     * @return WIFI实体对象
     */
    private fun wifiConfigurationToWifiDeviceBean(wifiConfiguration: WifiConfiguration)
            : WifiDeviceBean? {
        val wifiInfo = carWifiManager.getConnectionInfo()
        val bssid = wifiInfo?.bssid
        Log.d(TAG, "showAddWifiDialog:zhc6whu: bssid3为$bssid")
//        val wifiDeviceBean = WifiDeviceBean(wifiConfiguration.BSSID)
        val wifiDeviceBean = carWifiManager.getConnectionInfo()?.bssid?.let { WifiDeviceBean(it) }
        wifiNetWorkId = wifiConfiguration.networkId
        wifiDeviceBean?.wifiSSID = wifiConfiguration.SSID.replace("\"", "")
        wifiDeviceBean?.wifiNetWorkId = wifiConfiguration.networkId
        wifiDeviceBean?.wifiConfiguration = wifiConfiguration
        // 设置连接状态
        if (wifiInfo != null) {
            LogUtil.d(TAG, "getWifiConfigList : wifiInfo = ${wifiInfo.ssid}")
            if (TextUtils.equals(wifiConfiguration.SSID, wifiInfo.ssid)) {
                // 与当前连接的网络信息一致，则对当前已连接的网络进行状态设置
                when (wifiInfo.supplicantState) {
                    SupplicantState.DISCONNECTED, SupplicantState.INTERFACE_DISABLED,
                    SupplicantState.INACTIVE, SupplicantState.SCANNING,
                    SupplicantState.DORMANT, SupplicantState.UNINITIALIZED,
                    SupplicantState.INVALID -> {
                        // 已断开
                        wifiDeviceBean?.wifiConnectedState =
                            NetworkInfo.State.DISCONNECTED
                    }

                    SupplicantState.COMPLETED -> {
                        // 已连接
                        wifiDeviceBean?.wifiConnectedState = NetworkInfo.State.CONNECTED
                        wifiSsid = wifiConfiguration.SSID
                    }

                    SupplicantState.AUTHENTICATING, SupplicantState.ASSOCIATED,
                    SupplicantState.FOUR_WAY_HANDSHAKE,
                    SupplicantState.GROUP_HANDSHAKE -> {
                        // 连接中
                        wifiDeviceBean?.wifiConnectedState = NetworkInfo.State.CONNECTING
                        wifiSsid = wifiConfiguration.SSID
                    }

                    else -> {
                        wifiDeviceBean?.wifiConnectedState =
                            NetworkInfo.State.DISCONNECTED
                    }
                }
            } else {
                // 未发现当前连接对象，则全为未连接
                wifiDeviceBean?.wifiConnectedState = NetworkInfo.State.DISCONNECTED
            }
        } else {
            // 没获取到对象，则说明当前无连接，则状态全为DISCONNECTED
            wifiDeviceBean?.wifiConnectedState = NetworkInfo.State.DISCONNECTED
        }
        if (wifiConfiguration.wepKeys[0] != null) {
            wifiDeviceBean?.wifiCapabilities = Contacts.WIFI_CAP_WEP
        } else {
            if (wifiConfiguration.allowedKeyManagement
                    .get(WifiConfiguration.KeyMgmt.NONE)
            ) {
                // 无密码
                wifiDeviceBean?.wifiPassword = ""
                wifiDeviceBean?.wifiCapabilities = ""
            } else if (wifiConfiguration.allowedKeyManagement
                    .get(WifiConfiguration.KeyMgmt.WPA_PSK)
            ) {
                // WPA加密类型
                wifiDeviceBean?.wifiCapabilities = Contacts.WIFI_CAP_WPA
                // 修改完善加密类型
//            } else if (wifiConfiguration.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.SAE)) {
//                // 混合加密类型
//                wifiDeviceBean.wifiCapabilities = Contacts.WIFI_CAP_WPA2_3
            } else if (wifiConfiguration.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.WPA_EAP)) {
                // EAP加密类型
                wifiDeviceBean?.wifiCapabilities = Contacts.WIFI_CAP_EAP
            } else {
                // 其他类型
                wifiDeviceBean?.wifiCapabilities = Contacts.WIFI_CAP_EAP
            }
        }
        LogUtil.d(
            TAG,
            "getWifiConfigList : ssid = ${wifiDeviceBean?.wifiSSID} , " +
                    "password = ${wifiDeviceBean?.wifiPassword} , " +
                    "networkId = ${wifiDeviceBean?.wifiNetWorkId} , " +
                    "CAP = ${wifiDeviceBean?.wifiCapabilities} " +
                    "state = ${wifiDeviceBean?.wifiConnectedState}"
        )
        return wifiDeviceBean
    }

    fun setIsAddWifi(isAddWifi: Boolean) {
        carWifiManager.isAddWifi = isAddWifi
    }

    /**
     * 更新WIFI列表中已连接WIFI状态.
     *
     * @param wifiInfo 当前连接的网络信息
     * @param networkInfo 已连接WIFI信息
     */
    private fun updateWifiListState(wifiInfo: WifiInfo, networkInfo: NetworkInfo) {
        if (networkInfo.state == NetworkInfo.State.CONNECTING) {
            // 记录当前连接的ssid，用于解决在WIFI断开时，wifiInfo获取到的ssid为未知的现象
            wifiSsid = wifiInfo.ssid
            wifiNetWorkId = wifiInfo.networkId
            isWifiConnecting = true
        } else if (networkInfo.state == NetworkInfo.State.CONNECTED) {
            // WIFI连接已完成，停止连接计时
            stopWifiConnectedTimerTask()
            wifiConnectedResultLiveData.postValue(Contacts.WIFI_CONNECTED_COMPLETE)
            if (carWifiManager.isAddWifi){
                EToast.showToast(MyApplication.getContext(), "添加网络成功", Toast.LENGTH_SHORT, false)
            }else{
                Handler(Looper.getMainLooper()).postDelayed({
                    EToast.showToast(MyApplication.getContext(), "Wi-Fi已连接", Toast.LENGTH_SHORT, false)
                }, 1000L)   // 2000 ms = 2 s
            }
        }else if (networkInfo.state == NetworkInfo.State.DISCONNECTED) {
            // 当前没有WIFI连接，清空已连接列表
            wifiConnectedList.clear()
            updateWifiConnectedDeviceList()
        }
        if (wifiConnectionPreState == NetworkInfo.State.CONNECTING
            && networkInfo.state == NetworkInfo.State.DISCONNECTED
        ) {
            // 停止连接计时
            stopWifiConnectedTimerTask()
            // 上一次是连接中状态，现在是断开连接状态表明连接错误
            wifiConnectionError()
        }
        LogUtil.d(
            TAG, "updateWifiListState : connectionInfo name = $wifiSsid" +
                    ", state = ${networkInfo.state}"
        )
        // 更新WIFI已连接列表状态
        updateWifiConnectionListState(networkInfo)
        // 更新WIFI扫描列表状态
        updateWifiScanListState(networkInfo)
        wifiConnectionPreState = networkInfo.state
    }

    /**
     * WIFI连接错误时处理措施.
     */
    private fun wifiConnectionError() {
        wifiConnectedResultLiveData.postValue(Contacts.WIFI_CONNECTED_FAIL)
        if (wifiConnectedType == Contacts.WIFI_CONNECTED_FROM_SCAN_LIST) {
            LogUtil.d(TAG, "updateWifiListState : scan list unable to join the network ")
            // 如果从扫描列表进行连接，则移除连接的网络配置
            carWifiManager.deleteWifiConfig(wifiNetWorkId)
            // 从扫描列表中移除该网络
            wifiScanList.removeIf {
                "\"" + it.wifiSSID + "\"" == wifiSsid
            }
            updateWifiScanDeviceList()
        } else {
            LogUtil.d(TAG, "updateWifiListState : connecting list unable to join the network ")
            // 从已连接列表进行连接,则禁用该网络
            carWifiManager.disconnectedWifi(wifiNetWorkId)
        }
    }

    /**
     * 更新已连接列表WIFI状态.
     *
     * @param networkInfo WIFI状态信息
     */
    fun updateWifiConnectionListState(networkInfo: NetworkInfo) {
        // 在WIFI扫描列表中找出该WIFI信息对象
        val wifiDeviceBean = wifiScanList.find { "\"" + it.wifiSSID + "\"" == wifiSsid }
        if (wifiDeviceBean != null) {
            // 更新WIFI扫描列表中的状态
            wifiDeviceBean.wifiConnectedState = networkInfo.state
            wifiDeviceBean.wifiNetWorkId = wifiNetWorkId
            if (wifiDeviceBean.wifiConnectedState == NetworkInfo.State.CONNECTED) {
                Log.d(TAG, "updateWifiConnectionListState: zhc6whu1")
                // 如果扫描列表中WIFI已连接，将该WIFI信息从扫描列表中移除，然后添加到已连接列表
                wifiScanList.remove(wifiDeviceBean)
                wifiConnectedList.add(wifiDeviceBean)
                // 刷新扫描列表数据
                updateWifiScanDeviceList()
                // 刷新已连接列表数据
                updateWifiConnectedDeviceList()
            } else if(wifiDeviceBean.wifiConnectedState == NetworkInfo.State.DISCONNECTED){
                Log.d(TAG, "updateWifiConnectionListState: zhc6whu2")
                wifiConnectedList.remove(wifiDeviceBean)
                wifiScanList.add(wifiDeviceBean)
                // 刷新扫描列表数据
                updateWifiScanDeviceList()
                // 刷新已连接列表数据
                updateWifiConnectedDeviceList()
            }else{
                Log.d(TAG, "updateWifiConnectionListState: zhc6whu3")
                // 刷新扫描列表数据
                updateWifiScanDeviceList()
            }
        }
    }

    /**
     * 更新WIFI扫描列表状态.
     *
     * @param networkInfo WIFI状态信息
     */
    private fun updateWifiScanListState(networkInfo: NetworkInfo) {
        // 在WIFI已连接列表中找出该对象
        val wifiConnectedBean =
            wifiConnectedList.find { "\"" + it.wifiSSID + "\"" == wifiSsid }
        if (wifiConnectedBean != null) {
            // 更新WIFI已连接列表中的状态
            wifiConnectedBean.wifiConnectedState = networkInfo.state
            // 更新已连接列表
            updateWifiConnectedDeviceList()
        }
    }

    /**
     * 更新WIFI扫描列表结果.
     *
     */
    private fun updateWifiScanList() {
        viewModelScope.launch(Dispatchers.Default) {
            // 获取WIFI扫描列表
            val wifiScanResults = carWifiManager.getWifiScanResult()
            if (!wifiScanResults.isNullOrEmpty()) {
                LogUtil.d(
                    TAG, "updateWifiScanList : size = ${wifiScanResults.size} , wifiScanList " +
                            "= ${wifiScanResults.toTypedArray().contentToString()}"
                )
                wifiScanResults.forEach { scanResult ->
                    // 对WIFI扫描结果进行解析
                    val wifiDeviceBean = analyzeScanResult(scanResult)
                    // 将解析的结果添加到扫描列表
                    addScanToWifiList(wifiDeviceBean)
                }
            } else {
                // 刷新WIFI可用列表数据
                updateWifiScanDeviceList()
            }
        }
    }

    /**
     * 将扫描对象添加到列表.
     *
     * @param wifiDeviceBean WIFI扫描对象.
     */
    private fun addScanToWifiList(wifiDeviceBean: WifiDeviceBean) {
        val wifiConnectedBean = wifiConnectedList.find {
            (it.wifiSSID == wifiDeviceBean.wifiSSID)
        }
        // 该WIFI信息在WIFI已连接列表中已存在，则不进行添加；不存在，则添加
        if (wifiConnectedBean == null) {
            // 查询在WIFI扫描列表中是否有名称相同的对象
            val wifiBean = wifiScanList.find {
                (it.wifiSSID == wifiDeviceBean.wifiSSID)
            }
            if (wifiBean == null) {
                // WIFI名称不相同，则添加
                wifiScanList.add(wifiDeviceBean)
                // 更新wifi扫描列表
                updateWifiScanDeviceList()
            } else {
                if (wifiBean.wifiLevel < wifiDeviceBean.wifiLevel) {
                    // 列表中存在名称相同，则保留信号较强的WIFI
                    wifiBean.wifiLevel = wifiDeviceBean.wifiLevel
                    // 更新wifi扫描列表
                    updateWifiScanDeviceList()
                }
            }
        }
    }

    /**
     * 对扫描结果进行解析.
     *
     * @param scanResult 扫描结果对象.
     * @return WifiDeviceBean
     */
    private fun analyzeScanResult(scanResult: ScanResult): WifiDeviceBean {
        // 对WIFI扫描结果进行解析
        val wifiDeviceBean = WifiDeviceBean(scanResult.BSSID)
        wifiDeviceBean.wifiSSID = scanResult.SSID
        wifiDeviceBean.wifiCapabilities = scanResult.capabilities
        wifiDeviceBean.wifiLevel = scanResult.level
        wifiDeviceBean.wifiScanTime = scanResult.timestamp
//        if(scanResult.SSID!=""){
//            Log.d(TAG, "wifi扫描结果: WiFi名称"+scanResult.SSID+"加密类型："+scanResult.capabilities+"wifi强度"+scanResult.level)
//        }
        return wifiDeviceBean
    }

    fun set5GBand(band:Boolean){
        isCloseHotspotForUpdateHotspot=true
        carWifiManager.set5GBand(band)
        Log.d(TAG, "set5GBand zhc6whu updateHotspotPassword: $isCloseHotspotForUpdateHotspot")
    }

    fun get5GBand():Boolean{
        Log.d(TAG, "get5GBand: zhc6whu:热点兼容性:"+carWifiManager.get5GBand())
        return carWifiManager.get5GBand()
    }

    fun updateHotspot(){
        carWifiManager.updateHotspotInfo(getWifiHotspotName(),getWifiHotspotPassword())
        clearAllHotspotDevices()
    }

    /**
     * 判断当前WIFI是否在线.
     *
     * @return Boolean
     */
    fun isWifiOnline(wifiDeviceBean: WifiDeviceBean): Boolean {
        val onlineWifi = carWifiManager.getWifiScanResult()?.find {
            it.SSID == wifiDeviceBean.wifiSSID
        }
        return onlineWifi != null
    }

    /**
     * 开始连接倒计时任务.
     *
     */
    private fun startWifiConnectedTimerTask() {
        LogUtil.d(TAG, "startWifiConnectedTimerTask : ")
        wifiConnectedTimerTask = CountDownTimeManager(WIFI_CONNECTED_TIME) { count ->
            if (count == 0) {
                // 连接超时，关闭倒计时
                stopWifiConnectedTimerTask()
                if (wifiConnectedType == Contacts.WIFI_CONNECTED_FROM_SCAN_LIST) {
                    // 从WIFI配置中移除该网络信息,若从扫描列表进行连接
                    carWifiManager.deleteWifiConfig(wifiNetWorkId)
                } else {
                    // 从已连接列表进行连接,则禁用该网络
                    carWifiManager.disconnectedWifi(wifiNetWorkId)
                }
                // WIFI连接超时更新
                wifiConnectedResultLiveData.postValue(Contacts.WIFI_CONNECTED_TIMEOUT)
            }
        }
        // 间隔1秒，开始计时
        Timer().schedule(wifiConnectedTimerTask, 0, PERIOD)
    }

    /**
     * 停止连接倒计时任务.
     *
     */
    private fun stopWifiConnectedTimerTask() {
        LogUtil.d(TAG, "stopWifiConnectedTimerTask : ")
        isWifiConnecting = false
        wifiConnectedTimerTask?.cancel()
        wifiConnectedTimerTask = null
    }

    /*--------------------------------WIFI广播事件相关状态监听回调------------------------------------*/

    /**
     * WIFI状态改变时回调.
     *
     * @param state wifi对应状态.
     */
    override fun onWifiStateChange(state: Int) {
        LogUtil.d(TAG, "onWifiStateChange : wifi state = $state")
        when (state) {
            WifiManager.WIFI_STATE_ENABLED -> {
                // WIFI开关打开
                wifiSwitchLiveData.postValue(true)
            }

            WifiManager.WIFI_STATE_DISABLED -> {
                // WIFI开关关闭
                wifiSwitchLiveData.postValue(false)
            }
        }
    }

    /**
     * WIFI热点状态改变时回调.
     *
     * @param state 热点对应状态.
     */
    override fun onWifiHotspotStateChange(state: Int) {
        LogUtil.d(TAG, "onWifiHotSpotStateChange : hotspot state = $state")
        when (state) {
            WifiManager.WIFI_AP_STATE_ENABLED -> {
                LogUtil.d(TAG, "onWifiHotSpotStateChange : hotspot state = WifiManager.WIFI_AP_STATE_ENABLED")
                // 热点已开启
                hotspotSwitchLiveData.postValue(true)
                // 记忆热点开关状态
                Settings.System.putInt(
                    MyApplication.getContext().contentResolver,
                    SettingConstans.HOT_SPOT_STATUS_KEY,
                    WifiManager.WIFI_AP_STATE_ENABLED
                )
            }

            WifiManager.WIFI_AP_STATE_DISABLED -> {
                LogUtil.d(TAG, "updateHotspotPassword onWifiHotSpotStateChange : hotspot state = WifiManager.WIFI_AP_STATE_DISABLED")
                // 记忆热点开关状态
                Settings.System.putInt(
                    MyApplication.getContext().contentResolver,
                    SettingConstans.HOT_SPOT_STATUS_KEY,
                    WifiManager.WIFI_AP_STATE_DISABLED
                )
                Log.d(TAG, "updateHotspotPassword:判断当前是否需要重新打开热点 $isCloseHotspotForUpdateHotspot")
                if (isCloseHotspotForUpdateHotspot) {
                    Log.d(TAG, "updateHotspotPassword:更新前状态 $isCloseHotspotForUpdateHotspot")
                    isCloseHotspotForUpdateHotspot = false
                    Log.d(TAG, "updateHotspotPassword:更新后状态 $isCloseHotspotForUpdateHotspot")
                    // 如果因为修改热点名称和密码导致的热点关闭，关闭后，再次打开热点
                    carWifiManager.openWifiHotspot()
                } else {
                    // 清空热点已连接列表
                    clearHotspotConnectedList()
                    // 热点已关闭
                    hotspotSwitchLiveData.postValue(false)
                }
            }

            WifiManager.WIFI_AP_STATE_FAILED -> {
                LogUtil.d(TAG, "onWifiHotSpotStateChange : hotspot state = WifiManager.WIFI_AP_STATE_FAILED")
                // 清空热点已连接列表
                clearHotspotConnectedList()
                // 热点打开失败,开关复位
                hotspotSwitchLiveData.postValue(false)
                // 记忆热点开关状态
                Settings.System.putInt(
                    MyApplication.getContext().contentResolver,
                    SettingConstans.HOT_SPOT_STATUS_KEY,
                    WifiManager.WIFI_AP_STATE_DISABLED
                )
            }
        }
    }

    /**
     * 当有设备接入热点时.
     *
     * @param hotspotMac 接入设备的MAC地址
     */
    override fun onWifiHotspotJoin(hotspotMac: String, hotspotName: String) {
        LogUtil.d(TAG, "onWifiHotspotJoin : hotspotMac = $hotspotMac")
        // 将热点设备添加到接入热点列表
        addHotspotList(hotspotMac, hotspotName)
    }

    /**
     * 当有设备断开与热点的连接时.
     *
     * @param hotspotMac 断开设备的MAC地址
     */
    override fun onWifiHotspotLeave(hotspotMac: String) {
        LogUtil.d(TAG, "onWifiHotspotLeave : hotspotMac = $hotspotMac")
        // 将热点设备从接入热点列表移除
        removeHotspotList(hotspotMac)
    }

    /**
     * WIFI扫描列表可用时回调.
     *
     */
    override fun onWifiScanResult() {
        LogUtil.d(TAG, "onWifiScanResult : wifi scan result!")
        if (isSupportWifi()) {
            // 更新WIFI可用列表数据
            updateWifiScanList()
        }
    }

    /**
     * 网络状态发生改变时回调.
     *
     * @param wifiInfo 当前连接的网络信息
     * @param networkInfo 网络状态信息
     */
    override fun onNetworkStateChange(wifiInfo: WifiInfo, networkInfo: NetworkInfo) {
        LogUtil.d(TAG, "onNetworkStateChange :zhc6whu wifi connected state = ${networkInfo.state}")
        getWifiConnectedList()
        // 根据网络连接状态，更新WIFI列表相关状态
        startWifiScan(false)
        updateWifiListState(wifiInfo, networkInfo)
    }

    /**
     * WIFI密码错误时回调.
     *
     */
    override fun onWifiPasswordError() {
        if (wifiConnectedType == Contacts.WIFI_CONNECTED_FROM_SCAN_LIST) {
            // 若从扫描列表进行连接，密码错误后，从WIFI配置中移除该网络信息
            carWifiManager.deleteWifiConfig(wifiNetWorkId)
        }
        // WIFI密码错误更新
        wifiConnectedResultLiveData.postValue(Contacts.WIFI_PASSWORD_ERROR)
        Log.d(TAG, "onWifiPasswordError: zhc6whu:密码错误")
    }
    /*--------------------------------WIFI广播事件相关状态监听回调------------------------------------*/

    /**
     * WifiViewModel销毁时调用.
     *
     */
    override fun onCleared() {
        LogUtil.d(TAG, "onCleared : ")
        // 移除WIFI监听广播
        wifiReceiver?.unregisterWifiStateListener(javaClass.simpleName)
        wifiReceiver = null
        // 注销网络通知Observer
        contentResolver.unregisterContentObserver(networkNoticeObserver)
        contentResolver = null
        // 注销网络通知系统属性
        MyApplication.getContext().contentResolver.unregisterContentObserver(networkNoticeObserver)
        super.onCleared()
    }

    companion object {
        // 日志标志位
        private const val TAG = "WifiViewModel"

        // 计时器间隔
        private const val PERIOD = 1000L

        // WIFI连接倒计时
        private const val WIFI_CONNECTED_TIME = 20

        // 单例对象
        val instance: WifiViewModel by lazy(LazyThreadSafetyMode.PUBLICATION) {
            WifiViewModel()
        }
    }
}
