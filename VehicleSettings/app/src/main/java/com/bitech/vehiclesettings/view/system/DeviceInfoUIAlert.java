package com.bitech.vehiclesettings.view.system;

import android.app.Dialog;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.DialogInterface;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSDeviceInfoBinding;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class DeviceInfoUIAlert extends BaseDialog {
    private static final String TAG = DeviceInfoUIAlert.class.getSimpleName();
    private static DeviceInfoUIAlert.OnProgressChangedListener onProgressChangedListener;

    public DeviceInfoUIAlert(@NonNull Context context) {
        super(context);
    }

    public DeviceInfoUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    public static boolean isShow = false;
    protected DeviceInfoUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static DeviceInfoUIAlert.OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(DeviceInfoUIAlert.OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = true;
        private DialogAlertSDeviceInfoBinding binding;
        public DeviceInfoUIAlert dialog;
        private String originalDeviceName;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder(Context context, String originalDeviceName) {
            this.context = context;
            this.originalDeviceName = originalDeviceName;
        }

        public DeviceInfoUIAlert create() {
            dialog = new DeviceInfoUIAlert(context, R.style.Dialog);
            binding = DialogAlertSDeviceInfoBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            // 1. 设置弹窗尺寸
            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1128;
                layoutParams.height = 600;
                window.setAttributes(layoutParams);

                // 2. 设置透明背景避免点击关闭
                window.setBackgroundDrawableResource(android.R.color.transparent);
            }

            // 3. 初始化UI状态
            initUI();
            setupListeners();

            // 4. 自动弹出键盘
            showKeyboard();

            return dialog;
        }

        private void initUI() {
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            String name = bluetoothAdapter.getName();
            // 设置初始设备名称
            binding.dialogDeviceInfoEt.setText(name);
            // 将光标移到文本末尾
            binding.dialogDeviceInfoEt.setSelection(name.length());

            // 初始状态检查
            updateUIState(binding.dialogDeviceInfoEt.getText().toString());
        }

        private void setupListeners() {
            // 确定按钮点击
            binding.btnConfirm.setOnClickListener(v -> {
                String newName = binding.dialogDeviceInfoEt.getText().toString().trim();
                Log.d(TAG, "newName: " + newName);
                if (!newName.isEmpty()) {
                    onProgressChangedListener.setDeviceInfo(newName);
                    hideKeyboard();
                    dialog.dismiss();
                }
            });

            // 取消按钮点击
            binding.btnCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    hideKeyboard();
                    dialog.dismiss();
                }
            });
            // 清除按钮点击
            binding.ivClear.setOnClickListener(v -> {
                binding.dialogDeviceInfoEt.setText("");
                updateUIState("");
            });

            // 输入框文本变化监听
            binding.dialogDeviceInfoEt.addTextChangedListener(new TextWatcher() {
                private boolean isProcessing = false;
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    String input = s.toString();
                    try {
                        byte[] bytes = input.getBytes("UTF-8");
                        if (bytes.length > 32) {
                            // 截断到最多32字节
                            String truncated = truncateToMaxBytes(input, 32);
                            isProcessing = true;
                            binding.dialogDeviceInfoEt.setText(truncated);
                            binding.dialogDeviceInfoEt.setSelection(truncated.length());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    updateUIState(s.toString());
                }

                @Override
                public void afterTextChanged(Editable s) {
                    isProcessing = false;
                }
            });

            // 点击空白区域处理
            binding.getRoot().setOnTouchListener((v, event) -> {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    hideKeyboard();
                    binding.dialogDeviceInfoEt.clearFocus();
                    return true;
                }
                return false;
            });
        }

        private void updateUIState(String currentText) {
            // 更新清除按钮可见性
            binding.ivClear.setVisibility(currentText.isEmpty() ? View.GONE : View.VISIBLE);

            // 更新确认按钮状态
            if (currentText.isEmpty()) {
                GrayEffectUtils.applyGrayEffect(binding.btnConfirm, 0.3f);
            } else {
                GrayEffectUtils.removeGrayEffect(binding.btnConfirm);
            }

        }

        private void showKeyboard() {
            binding.dialogDeviceInfoEt.postDelayed(() -> {
                binding.dialogDeviceInfoEt.requestFocus();
                InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(binding.dialogDeviceInfoEt, InputMethodManager.SHOW_IMPLICIT);
            }, 100);
        }

        private void hideKeyboard() {
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(binding.dialogDeviceInfoEt.getWindowToken(), 0);
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        // 辅助方法：截断字符串，使其总字节数不超过 maxBytes
        private String truncateToMaxBytes(String str, int maxBytes) throws Exception {
            byte[] bytes = str.getBytes("UTF-8");
            if (bytes.length <= maxBytes) return str;

            // 从前往后累加字节数直到超过限制
            int len = 0;
            int charCount = 0;
            byte[] resultBytes = new byte[maxBytes];
            for (int i = 0; i < str.length(); i++) {
                char c = str.charAt(i);
                byte[] charBytes = String.valueOf(c).getBytes("UTF-8");
                if (len + charBytes.length > maxBytes) break;
                System.arraycopy(charBytes, 0, resultBytes, len, charBytes.length);
                len += charBytes.length;
                charCount++;
            }

            return new String(resultBytes, 0, len, "UTF-8");
        }

        /**
         * 获取有效子字符串（不超过32字节）
         */
        private String getValidSubstring(String text) {
            int byteCount = 0;
            int endIndex = 0;
            for (int i = 0; i < text.length(); i++) {
                char c = text.charAt(i);
                int charBytes = (c >= 0x4E00 && c <= 0x9FA5) ? 3 : 1;
                if (byteCount + charBytes > 32) {
                    break;
                }
                byteCount += charBytes;
                endIndex = i + 1;
            }
            return text.substring(0, endIndex);
        }
    }


    @Override
    public void dismiss() {
        unregisterReceiver(getContext());
        super.dismiss();
    }

    private void unregisterReceiver(Context context) {
        // Implementation for unregistering receivers
    }

    public interface OnProgressChangedListener {
        void setDeviceInfo(String info);
    }
    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }
}
