package com.bitech.vehiclesettings.common;

/**
 * 提供页面管理PageManager所需要的参数常量
 * TODO use PackageConstants instead
 */
public final class PageConstants {
    private PageConstants() {
    }

    /**
     * 系统设置参数常量
     */
    public static final class SystemSettings {
        public static final String ACTION_OPEN = "com.chery.setting.ACTION_OPEN_SYSTEM_SETTING";
        public static final String ACTION_DELETE_DATA = "com.chery.setting.ACTION_DELETE_DATA";
        public static final String ACTION_SHOW_COVER = "com.chery.setting.ACTION_SHOW_COVER";
        public static final String ACTION_DELETE_WALLPAPER = "com.chery.setting.ACTION_DELETE_WALLPAPER";
        public static final String ACTION_DELETE_DATA_SUCCESS = "com.chery.setting.ACTION_DELETE_DATA_SUCCESS";
        public static final String PAGE_ID_KEY = "SettingMenuID";
        public static final String IS_FINISH_SELF = "is_finish_self";
        public static final String REFRESH_LANGUAGE_REASON = "refresh_language_reason";
        public static final String VR_SET_LANGUAGE = "vr_set_language";
        public static final String TIME_ZONE = "time_zone";
        public static final String SCROLL_ACTION = "scroll_action";
        public static final String SCROLL_ACTION_CUSTOME_BTN = "scroll_action_custome_btn";
        public static final String ACTION_SCROLL_TO_CUSTOME_BTN = "action_scroll_to_custome_btn";

        // 声音设置中车速音量设置模式-Default（SVC_LEVEL_MIDDLE）
        public static final int SVC_LEVEL_OFF = 0;
        public static final int SVC_LEVEL_LOW = 1;
        public static final int SVC_LEVEL_MIDDLE = 2;
        public static final int SVC_LEVEL_HIGH = 3;
        // 主题设置是否刷新界面KEY
        public static final String REFRESH_THEME = "refresh_theme";
        // 中控屏亮度自动KEY
        public static final String BRIGHTNESS_CENTER_AUTO = "brightness_center_auto";
        // 仪表屏亮度自动KEY
        public static final String BRIGHTNESS_METER_AUTO = "brightness_meter_auto";
        // 显示模式设置KEY
        public static final String BRIGHTNESS_MODE = "brightness_mode";
        // 显示模式-白天
        public static final int BRIGHTNESS_MODE_DAY = 1;
        // 显示模式黑夜
        public static final int BRIGHTNESS_MODE_NIGHT = 2;
        // 显示模式自动
        public static final int BRIGHTNESS_MODE_AUTO = 3;
        // 中控屏幕亮度等级-白天KEY
        public static final String BRIGHTNESS_PROCESS_DAY = "brightness_process_day";
        // 中控屏幕亮度等级-黑夜KEY
        public static final String BRIGHTNESS_PROCESS_NIGHT = "brightness_process_night";
        // 仪表亮度等级-白天KEY
        public static final String METER_BRIGHTNESS_PROCESS_DAY = "meter_brightness_process_day";
        // 仪表亮度等级-黑夜KEY
        public static final String METER_BRIGHTNESS_PROCESS_NIGHT = "meter_brightness_process_night";
        // 中控屏幕亮度等级默认值-白天
        public static final int BRIGHTNESS_DAY_PROCESS_DEFAULT = 8;
        // 中控屏幕亮度等级默认值-黑夜
        public static final int BRIGHTNESS_NIGHT_PROCESS_DEFAULT = 3;
        // 仪表屏幕亮度等级默认值-白天
        public static final int METER_BRIGHTNESS_DAY_PROCESS_DEFAULT = 10;
        // 仪表屏幕亮度等级默认值-黑夜
        public static final int METER_BRIGHTNESS_NIGHT_PROCESS_DEFAULT = 6;


        // 显示亮度模式-浅色黑夜
        public static final int LIGHT_NIGHT_MODE = 1;
        // 显示亮度模式-浅色白天
        public static final int LIGHT_DAY_MODE = 2;
        // 显示亮度模式-深色黑夜
        public static final int DARK_NIGHT_MODE = 3;
        // 显示亮度模式-深色白天
        public static final int DARK_DAY_MODE = 4;

        // 中控屏幕亮度等级-浅色黑夜KEY
        public static final String BRIGHTNESS_PROCESS_LIGHT_NIGHT = "brightness_process_light_night";
        // 中控屏幕亮度等级-浅色白天KEY
        public static final String BRIGHTNESS_PROCESS_LIGHT_DAY = "brightness_process_light_day";
        // 中控屏幕亮度等级-深色黑夜KEY
        public static final String BRIGHTNESS_PROCESS_DARK_NIGHT = "brightness_process_dark_night";
        // 中控屏幕亮度等级-深色白天KEY
        public static final String BRIGHTNESS_PROCESS_DARK_DAY = "brightness_process_dark_day";

        // 仪表屏幕亮度等级-浅色黑夜KEY
        public static final String METER_BRIGHTNESS_PROCESS_LIGHT_NIGHT = "meter_brightness_process_light_night";
        // 仪表屏幕亮度等级-浅色白天KEY
        public static final String METER_BRIGHTNESS_PROCESS_LIGHT_DAY = "meter_brightness_process_light_day";
        // 仪表屏幕亮度等级-深色黑夜KEY
        public static final String METER_BRIGHTNESS_PROCESS_DARK_NIGHT = "meter_brightness_process_dark_night";
        // 仪表屏幕亮度等级-深色白天KEY
        public static final String METER_BRIGHTNESS_PROCESS_DARK_DAY = "meter_brightness_process_dark_day";

        // 亮度等级默认值-8
        public static final int BRIGHTNESS_PROCESS_DEFAULT_EIGHT = 8;
        // 亮度等级默认值-3
        public static final int BRIGHTNESS_PROCESS_DEFAULT_THREE = 3;



        public static final int BRIGHTNESS_DAY_DEFAULT = 255;
        public static final int BRIGHTNESS_NIGHT_DEFAULT = 77;             //255*0.3
        // 其他模块亮度修改
        public static final String BRIGHTNESS_CHANGED_BY_OTHER = "brightness_changed_by_other";
        public static final String CALL_BROADCAST = "call_broadcast";
        public static final String FINISH_BROADCAST = "finish_broadcast";
        // 视频限制状态，取值为open/close
        public static final String VIDEO_SPEED_LIMITED = "video_speed_limited";
        public static final String OPEN = "open";
        public static final String CLOSE = "close";
        //仪表油耗单位
        public static final String INSTRUMENT_FUEL_UNIT_KEY = "instrument_fuel_unit";
        public static final class InstrumentFuelUnitId {
            //km/L
            public static final int KML = 1;
            //L/100km-默认值
            public static final int L100KM = 0;
            //mpg
            public static final int MPG = 2;
        }
        //电耗单位
        public static final String ELECTRICITY_UNIT_KEY= "electricity_unit";
        public static final class ElectricityUnitId {
            //wh/km-默认值
            public static final int WH = 0;
            //kwh/100km
            public static final int KWH = 1;
        }
        //单位
        public static final String UNIT_KEY= "unit";
        public static final String PERSIST_UNIT= "persist.sys.unit";
        public static final class UnitId {
            //英制mi
            public static final int MI = 1;
            //公制km-默认值
            public static final int KM = 0;
        }
        //处理avm画面出现还能通过方控切换上下一曲的场景
        public static final String AVM_FOR_MEDIA = "avm_for_media";
        public static final int IN_AVM = 1;
        public static final int OUT_OFF_AVM = 0;

        public static final String TIME_ZONE_DEFUALT = "Etc/GMT-3";
        public static final String CUSTOM_BTN_FUN = "custom_btn_fun";
        public static final String FUN1 = "fun1";
        public static final String FUN2 = "fun2";
        public static final String FUN3 = "fun3";
        public static final String FUN4 = "fun4";
        public static final String FUN5 = "fun5";
        public static final String FUN6 = "fun6";
        public static final String FUN7 = "fun7";

        public static final String USE_GPS_TIME = "use_gps_time";
        public static final String LAST_CONFIG_LANGUAGE = "last_config_language";
        // 12h制
        public static final String HOUR12 = "12";
        // 24h制
        public static final String HOUR24 = "24";
        // ICM 12h制
        public static final int TIME_12H = 0x02;
        // ICM 24h制
        public static final int TIME_24H = 0x01;
        // 日期显示格式
        public static final String DATETIME_SHOW_TYPE_KEY = "datetime_show_type";
        public static final int DATETIME_SHOW_TYPE_YEAR = 0;
        public static final int DATETIME_SHOW_TYPE_MONTH = 1;
        public static final int DATETIME_SHOW_TYPE_DAY = 2;
        // 语言设置是否刷新界面KEY
        public static final String REFRESH_LANGUAGE = "refresh_language";
        public static final String TRUE = "1";
        public static final String FALSE = "0";
        public static final int SOUND_STAGE_OFF = 0;
        public static final int SOUND_STAGE_ALL = 1; //（Default）
        public static final int SOUND_STAGE_DRIVER = 2;

        public static final int BALANCE_MAX = 14;
        public static final int BALANCE_CENTER = 7;
        public static final int ZERO = 0;

        public static final int DEFAULT_MEDIA = 15;
        public static final int DEFAULT_BT_MUSIC = 18;
        public static final int DEFAULT_PHONE = 12;
        public static final int DEFAULT_NAV = 5;
        public static final int DEFAULT_VR = 5;
        public static final int DEFAULT_CHIME = 5;
        public static final int DEFAULT_RINGTONE = 12;


        public static final class PageId {
            public static final String PAGE_SHORTCUT_CONTROL_SETTING = "0";
            public static final String PAGE_VEHICLE_SETUP_KEY_VALUE = "1";
            public static final String PAGE_ASSISTANCE_DRIVER_KEY_VALUE = "2";
            public static final String PAGE_AMBIENT_LIGHT_KEY_VALUE = "3";
            public static final String PAGE_BLUETOOTH_SETTING = "5";
            public static final String PAGE_WIFI_SETTING = "6";
            public static final String PAGE_SOUND_SETTING = "7";
            public static final String PAGE_DISPLAY_SETTING = "9";
            public static final String PAGE_SYSTEM_SETTING = "10";
            public static final String PAGE_SPEECH_SETTING = "8";
            public static final String PAGE_VOLUME_SETTING = "7";
            public static final String PAGE_EQ_SETTING = "8";

        }

        public static final class LanguageId {
            public static final String RU = "ru";
            public static final String PT = "pt";
            public static final String ES = "es";
            public static final String AR = "ar";
            public static final String TH = "th";
            public static final String IN = "in";
            public static final String TR = "tr";
            public static final String ZH = "zh";
            public static final String EN = "en";
            public static final String IT = "it";
            public static final String UKR = "uk";
            public static final String HE = "iw";
            public static final String DE = "de";
            public static final String PL = "pl";
            public static final String KK = "kk";
            public static final String FR = "fr";
        }
    }

    /**
     * 车辆设置参数常量
     */
    public static final class VehicleSettings {
        public static final String ACTION_OPEN =
                "com.chery.vehiclesetting.ACTION_OPEN_VEHICLE_SETTING";
        // 驾驶模式记忆状态KEY
        public static final String REMEMBER_DRIVER_MODE_STATUS = "remember_driver_mode_switch";
        // 驾驶模式KEY
        public static final String REMEMBER_DRIVER_MODE = "remember_driver_mode";
        public static final String PAGE_ID_KEY = "CarSettingMenuID";

        // RATE
        public static final Float RATE = 0F;
        public static final int MUSIC_LIGHT_DISABLE = 0;
        public static final int MUSIC_LIGHT_ENABLE = 1;//（Default）

        public static final class PageId {
            public static final String PAGE_QUICK_CONTROL = "0";
            public static final String PAGE_SMART_KEY_SETTING = "1";
            public static final String PAGE_LIGHT_SETTING = "4";
            public static final String PAGE_HVAC_SETTING = "5";
            public static final String PAGE_DRIVE_ASSISTANT_SETTING = "2";
            public static final String PAGE_BASIC_SETTING = "8";
            public static final String PAGE_PERSONALIZED_SETTING = "9";
        }
    }

    /**
     * Constants used to build {@link android.content.Intent}s.
     */
    public static class DialerIntents {
        /**
         * Intent action for TelecomActivity to show a tabbed page.
         */
        public static final String ACTION_SHOW_PAGE = "com.chery.dialer.ACTION_SHOW_PAGE";
        /**
         * Intent extra for TelecomActivity to show a tabbed page.
         */
        public static final String EXTRA_SHOW_PAGE = "com.chery.dialer.EXTRA_SHOW_PAGE";
        /**
         * Intent extra flag to mark unread missed calls as read.
         */
        public static final String EXTRA_ACTION_READ_MISSED =
                "com.chery.dialer.EXTRA_ACTION_READ_MISSED";
        /**
         * Intent extra flag to show incoming call.
         */
        public static final String EXTRA_SHOW_INCOMING_CALL = "show_incoming_call";
        /**
         * Intent extra flag to call dialpad number.
         */
        public static final String EXTRA_CALL_NUMBER = "call_dialpad_number";

        public static final class PageId {
            public static final String CALL_HISTORY = "CALL_HISTORY";
            public static final String CONTACTS = "CONTACTS";
            public static final String DIAL_PAD = "DIAL_PAD";
        }
    }

    public static final class Media {
        public static final String ACTION_OPEN = "com.chery.media.ACTION_OPEN";

        public static final String MEDIA_PAGE_ID_KEY = "MediaPageId";

        public static final class MediaPageId {
            public static final String PAGE_NONE = "PAGE_NONE";
            public static final String PAGE_MENU = "PAGE_MENU";
            public static final String PAGE_RADIO_PLAYER = "PAGE_RADIO_PLAYER";
            public static final String PAGE_BT_MUSIC_PLAYER = "PAGE_BT_MUSIC_PLAYER";
            public static final String PAGE_USB_MUSIC_LIST = "PAGE_USB_MUSIC_LIST";
            public static final String PAGE_USB_MUSIC_PLAYER = "PAGE_USB_MUSIC_PLAYER";
            public static final String PAGE_LOCAL_MUSIC_LIST = "PAGE_LOCAL_MUSIC_LIST";
            public static final String PAGE_LOCAL_MUSIC_PLAYER = "PAGE_LOCAL_MUSIC_PLAYER";
            public static final String PAGE_DAB_PLAYER = "PAGE_DAB_PLAYER";
        }

        public static final String VIDEO_PAGE_ID_KEY = "VideoPageId";

        public static final class VideoPageId {
            public static final String PAGE_NONE = "PAGE_NONE";
            public static final String PAGE_MAIN = "PAGE_MAIN";
            public static final String PAGE_PLAYER = "PAGE_PLAYER";
        }

        public static final String VIDEO_PAGE_ID_USB_PATH = "USB_VIDEO_Path";

        public static final String PICTURE_PAGE_ID_KEY = "PicturePageId";

        public static final class PicturePageId {
            public static final String PAGE_NONE = "PAGE_NONE";
            public static final String PAGE_MAIN = "PAGE_MAIN";
            public static final String PAGE_VIEWER = "PAGE_PLAYER";
            public static final String PAGE_LOCAL = "PAGE_LOCAL";
        }

        public static final String WALLPAPER_PAGE_ID_KEY = "WallpaperPageId";
        public static final String WALLPAPER_PAGE_ID_USB_PATH = "USB_PicturePath";
        public static final String WALLPAPER_PAGE_ID_LOCAL_PATH = "Local_PicturePath";

        public static final class WallpaperPageId {
            public static final String PAGE_NONE = "PAGE_NONE";
            public static final String PAGE_MAIN = "PAGE_MAIN";
            public static final String PAGE_VIEWER = "PAGE_PLAYER";
        }

        public static final String USB_TYPE_KEY = "UsbType";

        public static final class UsbType {
            public static final String USB_NONE = "USB_NONE";
            public static final String USB1 = "USB1";
            public static final String USB2 = "USB2";
        }

        public static final String USB_CLASSIFY_KEY = "ClassifyType";

        public static final class ClassifyType {
            public static final String USB_VIDEO = "USB_VIDEO";
            public static final String USB_PICTURE = "USB_PICTURE";
        }
    }

    /**
     * 用户手册参数常量
     */
    public static final class ManualBook {
        public static final String PAGE_SESSION_KEY = "section";
        public static final String BACK_HOME = "BACK_HOME";

        public static final class PageId {
            public static final String LIST = "NONE"; // 列表页
            public static final String BRIEF = "BRIEF"; // 简介  core_functions
            public static final String CENTRAL_CONTROL_BUTTON = "CENTRAL_CONTROL_BUTTON";//中间控制按钮 control_button
            public static final String SWC_BUTTON = "SWC_BUTTON";//方控按键 swc_button
            public static final String LAUNCHER = "LAUNCHER"; // 主页 home_page
            public static final String CONTROL_CENTER = "CONTROL_CENTER"; //  负一屏快捷菜单 control_center
            public static final String BT_SETTING = "BT_SETTING"; // 车辆中心-蓝牙设置 车辆中心首页
            public static final String SOUND_SETTING = "SOUND_SETTING"; // 车辆中心-声音设置
            public static final String DISPLAY_SETTING = "DISPLAY_SETTING"; // 车辆中心-显示设置
            public static final String SYSTEM_SETTING = "SYSTEM_SETTING"; // 系统设置
            public static final String MOBILE_INTERCONNECT = "MOBILE_INTERCONNECT"; // 手机互联
            public static final String SHORTCUT = "SHORTCUT";// 车辆中心-快捷控制
            public static final String VEHICLE_CENTER = "VEHICLE_CENTER"; //车辆中心-车辆设置
            public static final String ASSIS_DRIVE_SETTING = "ASSIS_DRIVE_SETTING"; // 车辆中心-辅助驾驶设置
            public static final String LIGHT_SETTING = "LIGHT_SETTING"; // 车辆中心-氛围灯
            public static final String PHONE = "PHONE"; // 电话 mobile_phone
            public static final String PICTURE = "PICTURE";//图库 视频 images
            public static final String MEDIA = "MEDIA"; // 多媒体 media
            public static final String AIR_CONDITIONING = "AIR_CONDITIONING"; //空调 air_conditioning
            public static final String AVM = "AVM";
            public static final String NAVI = "NAVI"; // 导航  map
            public static final String ANDROID_AUTO = "ANDROID_AUTO"; // Android Auto
            //TODO T13J目前仅支持上方页面id跳转

            public static final String VR_FUNCTION = "VR_FUNCTION"; // 语音识别
            public static final String RADIO = "RADIO"; // 收音机
            public static final String USB_MUSIC = "USB_MUSIC"; // USB音乐
            public static final String USB_VIDEO = "USB_VIDEO"; // USB视频
            public static final String USB_PICTURE = "USB_PICTURE"; // USB图片
            public static final String VR_MEDIA_COMMAND = "VR_MEDIA_COMMAND"; // 语音媒体识别
            public static final String VR_VEHICLE_COMMAND = "VR_VEHICLE_COMMAND"; // 语音车辆识别
            public static final String WIFI_SETTING = "WIFI_SETTING"; // WIFI设置
            public static final String VR_SETTING = "VR_SETTING"; // 语音设置
            public static final String SMART_KEY_SETTING = "SMART_KEY_SETTING"; // 智能钥匙设置
            public static final String PERSONALITY_SETTING = "PERSONALITY_SETTING"; // 个性化设置
            public static final String BASE_INFO_SETTING = "BASE_INFO_SETTING"; // 车辆基本信息
            public static final String AC_SETTING = "AC_SETTING"; // 空调设置
            public static final String CARPLAY = "CARPLAY"; // CarPlay
            public static final String AIR_AUTO = "AIR_AUTO"; // 自动空调
            public static final String AIR_ELECTRIC = "AIR_ELECTRIC"; // 电动空调
            public static final String ATTENTION = "ATTENTION"; // 注意事项
            public static final String MEDIA_FPRMAT = "MEDIA_FPRMAT"; // 媒体格式

        }
    }
}
