package com.bitech.vehiclesettings.presenter.condition;

import android.content.Context;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.manager.CarSettingManager;
import com.bitech.platformlib.manager.ConditionManager;
import com.bitech.platformlib.manager.PowerManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.common.SiganlConstans;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;

public class ConditionPresenter implements ConditionPresenterListener{
    private static final String TAG = ConditionPresenter.class.getSimpleName();
    private Context mContext;
    private ConditionPresenterListener listener;

    ConditionManager conditionManager = (ConditionManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_CONDITION_MANAGER);
    CarSettingManager carSettingManager = (CarSettingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_SETTING_MANAGER);
    PowerManager powerManager = (PowerManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_POWER_MANAGER);
    public ConditionPresenter(Context context) {
        this.mContext = context;
    }

    @Override
    public void setWiperRepairMode(int status) {
        //显⽰名称：⾬刮维修模式 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在⼤屏上点击⾬刮维修模式开关按键使打开
        //2. 在⼤屏上点击⾬刮维修模式开关按键使其关闭
        //执⾏输出（1||2）MaintenanceMode/WiperMode/Set
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_MaintenanceMode =0x2:Entrance & ICC_WiperID
        //=0x3:Both Wiper，然后发送 0x0:Not Active 给 FLZCU，FLZCU 控制进⼊⾬刮 维修模式，计时 2s
        //若检测到状态反馈信号 FLZCU_WipeMaintenanceSWSts=0x3:Both Wiper，则⾬刮维修模式开关
        //保 持开启，否则开关弹回关闭；
        //2. 若触发条件为 1，ICC 连续发送三帧 ICC_MaintenanceMode =0x1:Exit & ICC_WiperID =0x3:Both
        //Wiper，然后发送 0x0:Not Active 给 FLZCU，FLZCU 控制退出⾬刮 维修模式，计时 2s 若检测到状
        //态反馈信号 FLZCU_WipeMaintenanceSWSts≠0x3:Both Wiper，则⾬刮维修模式开关保 持关闭，
        //否则开关弹回开启；；
        //3. ICC 收到 FLZCU_WipeMaintenanceSWSts = 0x3:Both Wiper，⼤屏显⽰⾬刮维修模式开启
        //ICC 收到 FLZCU_WipeMaintenanceSWSts =0x2:Rear Wiper||0x1:Front Wiper||0x0:Not
        //Active，⼤屏显⽰⾬刮维修模式关闭

        // ICC -> FLZCU 信号名：ICC_MaintenanceMode
        // 0x0:Not Active
        // 0x1:Exit
        // 0x2:Entrance

        // FLZCU -> ICC 信号名：FLZCU_WipeMaintenanceSWSts
        // 0x0:Not Active
        // 0x1:Front Wiper
        // 0x2:Rear Wiper
        // 0x3:Both Wiper
        if (MsgUtil.getInstance().supportPowerMode()) {
            MsgUtil.getInstance().setSignlVal(SiganlConstans.MAINTENANCEMODE_WIPERID_SET, 0x3);
            switch (status) {
                case 0:
                    MsgUtil.getInstance().setSignlVal(SiganlConstans.MAINTENANCEMODE_WIPERMODE_SET, 0x1);
                    break;
                case 1:
                    MsgUtil.getInstance().setSignlVal(SiganlConstans.MAINTENANCEMODE_WIPERMODE_SET, 0x2);
                    break;
            }
        }
    }

    @Override
    public int getWiperRepairMode() {
        int signalVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.MAINTENANCEMODE_WIPERMODE);
        int status = 0;
        if (signalVal == 0x0 || signalVal == 0x1 || signalVal == 0x2) {
            status = 0;
        } else if (signalVal == 0x3) {
            status = 1;
        }
        Prefs.put(PrefsConst.C_WIPER_REPAIR_MODE, status);
//        return Prefs.rtnStatus(PrefsConst.C_WIPER_REPAIR_MODE, status, PrefsConst.DefaultValue.C_WIPER_REPAIR_MODE);
        return status;
    }

    // 复位保养里程
    @Override
    public void setMaintainReset() {
        // TODO 在线配置
        Log.d(TAG, "setSwMaintainReset: 保养里程复位");
        if (MsgUtil.getInstance().supportPowerMode()) {
            conditionManager.setMaintainResetSet(0x1);
        }
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_KM, 999);
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_DAY, 120);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_KM, 999);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_DAY, 120);
    }

    // 保养复位提醒
    @Override
    public void setMaintainRemind(int state) {
        if (state == 1) {
            conditionManager.setMaintainTipSet(0x1);
        }else if (state == 0) {
            conditionManager.setMaintainTipSet(0x0);
        }
    }

    @Override
    public int getMaintainRemind() {
        int signVal = conditionManager.getMaintainTip();
        int state = 0;
        if (signVal == 0x1) {
            state = 1;
        }else if (signVal == 0x0) {
            state = 0;
        }
        Prefs.put(PrefsConst.C_MAINTAIN_TIPS, state);
        return state;
    }

    @Override
    public int getMaintainKm() {
        // TODO 在线配置
        // 获取保养里程
        Log.d(TAG, "getSwMaintainKm: 获取保养里程");
        int km = conditionManager.getReMaintainMileage();
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_KM, km);
        Log.d(TAG, "getSwMaintainKm: 获取保养里程:" + km);
        return km;
    }

    @Override
    public void setMaintainKm(int km) {
        // TODO 在线配置
        Log.d(TAG, "setSwMaintainKm: 设置保养里程:" + km);
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_KM, km);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_KM, km);
        Log.d(TAG, "点击成功：" + km);
    }

    @Override
    public int getMaintainDays() {
        // TODO 在线配置
        int day = conditionManager.getReMaintainTime();
//        int day = Prefs.getGlobalValue(mContext, PrefsConst.GlobalValue.C_MAINTAIN_RESET_DAY);
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_DAY, day);
        Log.d(TAG, "getSwMaintainDays: 获取保养天数:" + day);
        return day;
    }

    @Override
    public void setMaintainDays(int day) {
        // TODO 在线配置
        Log.d(TAG, "setSwMaintainDays: 设置保养天数:" + day);
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_DAY, day);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_DAY, day);
        Log.d(TAG, "点击成功：" + day);
    }

    @Override
    public int getWiperRepairStatus() {
        return carSettingManager.getWiperModeAvailable();
    }

    @Override
    public int getPowerMode() {
        int signVal = conditionManager.getPowerMode();
        return signVal;
    }

    @Override
    public int getSunShadeCurtainStopped() {
        int signVal = conditionManager.getSunShadeCurtainStopped();
        int status = 0;
        if (signVal == 0x0 || signVal == 0x5 || signVal == 0x6) {
            status = 1;
        }
        return status;
    }
}
