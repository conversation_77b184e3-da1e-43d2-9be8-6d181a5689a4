package com.bitech.vehiclesettings.carapi.constants;

public class CarAvm {

    /**
     * 档位信息状态
     */
    public static class GBPositoionDisplay {
        public static final int NOT_DISPLAY = 0x0;
        public static final int DISPLAY_P = 0x1;
        public static final int DISPLAY_R = 0x2;
        public static final int DISPLAY_N = 0x3;
        public static final int DISPLAY_D = 0x4;
        public static final int DISPLAY_M1 = 0x5;
        public static final int DISPLAY_M2 = 0x6;
        public static final int DISPLAY_M3 = 0x7;
        public static final int DISPLAY_M4 = 0x8;
        public static final int DISPLAY_M5 = 0x9;
        public static final int DISPLAY_M6 = 0xA;
        public static final int DISPLAY_M7 = 0xB;
        public static final int LOW = 0xC;
        public static final int DISPLAY_M1_PLUS = 0xD;
        public static final int DISPLAY_M2_PLUS = 0xE;
        public static final int DISPLAY_M3_PLUS = 0xF;
        public static final int DISPLAY_M4_PLUS = 0x10;
        public static final int DISPLAY_M5_PLUS = 0x11;
        public static final int DISPLAY_M6_PLUS = 0x12;
        public static final int DISPLAY_M7_PLUS = 0x13;
        public static final int DISPLAY_M2_MINUS = 0x14;
        public static final int DISPLAY_M3_MINUS = 0x15;
        public static final int DISPLAY_M4_MINUS = 0x16;
        public static final int DISPLAY_M5_MINUS = 0x17;
        public static final int DISPLAY_M6_MINUS = 0x18;
        public static final int DISPLAY_M7_MINUS = 0x19;
        public static final int DISPLAY_M8 = 0x1A;
        public static final int DISPLAY_M9 = 0x1B;
    }

    /**
     * 手动挡档位信息
     */
    public static class ReverseGearSts {
        public static final int OFF = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 雷达泊车（回调）
     */
    public static class RadarWorkSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    public static class IHU_9_FrntRadarSwt {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 前舱盖回调信号.
     */
    public static class BCM_4_HoodSts {
        public static final int CLOSE = 0x0;
        public static final int OPEN = 0x1;
    }
}
