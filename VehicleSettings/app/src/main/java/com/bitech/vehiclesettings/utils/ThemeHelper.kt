package ezy.demo.theme

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.graphics.Color
import android.view.View
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.BindingAdapter
import androidx.databinding.BindingMethod
import androidx.databinding.BindingMethods
import androidx.lifecycle.MutableLiveData
import com.bitech.vehiclesettings.R

data class Theme(
    val themeColor: Int,
    val background: Int,
)

object Themes {
    val COLOR1 = Theme(R.color.blue, R.color.bg_solid_color_1)
    val COLOR2 = Theme(R.color.color_1, R.color.bg_solid_color_1)
    val COLOR3 = Theme(R.color.color_2, R.color.bg_solid_color_1)
    val COLOR4 = Theme(R.color.color_3, R.color.bg_solid_color_1)
    val COLOR5 = Theme(R.color.color_4, R.color.bg_solid_color_1)
    val COLOR6 = Theme(R.color.color_5, R.color.bg_solid_color_1)
}

object AppTheme {
    val theme = MutableLiveData<ColorStateList>()
    val background = MutableLiveData<ColorStateList>()

    init {
        update(Themes.COLOR1)
    }

    fun update(theme: Theme) {
        this.theme.value = ColorStateList.valueOf(theme.themeColor)
        background.value = ColorStateList.valueOf(theme.background)
    }
}

@SuppressLint("RestrictedApi")
@BindingMethods(
    BindingMethod(type = ImageView::class, attribute = "tint", method = "setImageTintList")
)
object ThemeAdapter {
    @BindingAdapter("background")
    @JvmStatic
    fun adaptBackground(view: View, value: ColorStateList?) {
        view.setBackgroundColor(Color.WHITE)
        view.backgroundTintList = value
    }

    @BindingAdapter("drawableTint")
    @JvmStatic
    fun adaptDrawableTint(view: TextView, value: ColorStateList?) {
        if (view is AppCompatTextView) {
            view.supportCompoundDrawablesTintList = value
        }
    }

    @BindingAdapter("android:progressBackgroundTint")
    @JvmStatic
    fun adaptProgressBackgroundTint(view: SeekBar, value: ColorStateList?) {
        view.progressBackgroundTintList = value
    }
}