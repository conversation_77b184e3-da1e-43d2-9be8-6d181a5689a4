package com.bitech.vehiclesettings.adapter;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.bitech.vehiclesettings.R;

import java.util.Arrays;
import java.util.List;

public class EquipmentColorAdapter extends RecyclerView.Adapter<EquipmentColorAdapter.ColorViewHolder> {

    private List<Integer> colorList = Arrays.asList(
            R.mipmap.ic_new_equipment_color_1,
            R.mipmap.ic_new_equipment_color_2,
            R.mipmap.ic_new_equipment_color_3,
            R.mipmap.ic_new_equipment_color_4,
            R.mipmap.ic_new_equipment_color_5,
            R.mipmap.ic_new_equipment_color_6
    );;
    private int selectedPosition = RecyclerView.NO_POSITION;
    private OnItemClickListener onItemClickListener;

    public EquipmentColorAdapter() {}

    public static class ColorViewHolder extends RecyclerView.ViewHolder {
        ImageView colorImage;
        View parentLayout;

        public ColorViewHolder(@NonNull View itemView) {
            super(itemView);
            colorImage = itemView.findViewById(R.id.iv_new_equipment_color);
            parentLayout = itemView.findViewById(R.id.ll_new_equipment_color);
        }
    }

    @NonNull
    @Override
    public ColorViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_new_equipment_color, parent, false);
        return new ColorViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ColorViewHolder holder, int position) {
        int colorResId = colorList.get(position);
        holder.colorImage.setImageResource(colorResId);

        // ========================
        // 选中状态的 UI 切换
        // ========================
        if (position == selectedPosition) {
            // 选中状态：设置前景 + 调整尺寸
            holder.parentLayout.setForeground(holder.itemView.getContext().getDrawable(R.mipmap.ic_new_equipment_color_foreground));
            holder.colorImage.getLayoutParams().width = 108; // 72 dp = 108 px (HDPI)
            holder.colorImage.getLayoutParams().height = 108; // 72 dp = 108 px (HDPI)
            holder.colorImage.requestLayout();
        } else {
            // 未选中状态：清除前景 + 还原尺寸
            holder.parentLayout.setForeground(null);
            holder.colorImage.getLayoutParams().width = 96; // 64 dp = 96 px (HDPI)
            holder.colorImage.getLayoutParams().height = 96; // 64 dp = 96 px (HDPI)
            holder.colorImage.requestLayout();
        }

        // ========================
        // 点击事件：更新选中状态
        // ========================
        holder.itemView.setOnClickListener(v -> {
            if (selectedPosition != position) {
                int previousSelected = selectedPosition;
                selectedPosition = position;

                // 刷新选中和取消选中的 item
                notifyItemChanged(previousSelected);
                notifyItemChanged(selectedPosition);

                if (onItemClickListener != null) {
                    onItemClickListener.onItemClick(position);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return colorList.size();
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position);
    }
}
