package com.bitech.vehiclesettings.utils

import android.content.res.Resources
import android.view.View

/**
 * 获取 View 控件的 xml ID 名称。
 *
 */
fun View.getIdName(): String {
    val viewId = this.id
    return if (viewId != View.NO_ID) {
        try {
            this.resources.getResourceEntryName(viewId)
        } catch (e: Resources.NotFoundException) {
            "Unknown ID"
        }
    } else {
        "No ID"
    }
}