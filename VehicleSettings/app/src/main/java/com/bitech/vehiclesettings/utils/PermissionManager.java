package com.bitech.vehiclesettings.utils;

import android.Manifest;
import android.app.Activity;
import android.app.AppOpsManager;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.provider.Settings;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.AppPermissionBean;
import com.bitech.vehiclesettings.bean.AppPermissionLevel;
import com.bitech.vehiclesettings.bean.PermissionType;
import com.bitech.vehiclesettings.bean.RecordItemBean;
import com.bitech.vehiclesettings.bean.SystemPermission;
import com.bitech.vehiclesettings.bean.SystemScope;

import org.json.JSONException;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 通用权限管理工具类，封装系统级 & 应用级权限的获取与设置逻辑
 * 系统级全局开关需作为设备所有者或系统签名应用使用 DevicePolicyManager 或 AppOpsManager。
 */
public class PermissionManager {
    private static final String TAG = PermissionManager.class.getSimpleName();
    private static final String PREFS_NAME = "sys_perms_prefs";
    private static final String KEY_PREFIX = "sys_perm_";

    // SharedPreferences 获取
    private static SharedPreferences getPrefs(Context ctx) {
        return ctx.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * 获取单个系统级权限配置（App 内部面板）
     */
    public static SystemPermission getSystemPermission(Context ctx, PermissionType type) {
        SharedPreferences prefs = getPrefs(ctx);
        boolean enabled = prefs.getBoolean(KEY_PREFIX + type.name() + "_enabled", true);
        String scopeStr = prefs.getString(KEY_PREFIX + type.name() + "_scope", SystemScope.CURRENT_SESSION.name());
        SystemScope scope = SystemScope.valueOf(scopeStr);
        return new SystemPermission(type, enabled, scope);
    }

    /**
     * 保存/更新单个系统级权限配置（App 内部面板）
     */
    public static void setSystemPermission(Context ctx, SystemPermission perm) {
        SharedPreferences.Editor editor = getPrefs(ctx).edit();
        editor.putBoolean(KEY_PREFIX + perm.getType().name() + "_enabled", perm.isEnabled());
        editor.putString(KEY_PREFIX + perm.getType().name() + "_scope", perm.getScope().name());
        editor.apply();
    }

    /**
     * 获取所有三类系统级权限配置（App 内部面板）
     */
    public static List<SystemPermission> loadAllSystemPermissions(Context ctx) {
        List<SystemPermission> list = new ArrayList<>();
        for (PermissionType type : PermissionType.values()) {
            list.add(getSystemPermission(ctx, type));
        }
        return list;
    }


    /**
     * 只加载用户级（非系统/非预装）应用，并同步它们的运行时权限状态
     */
    public static List<AppPermissionBean> loadAllAppPermissions(Context ctx) {
        PackageManager pm = ctx.getPackageManager();
        AppOpsManager ops = (AppOpsManager) ctx.getSystemService(Context.APP_OPS_SERVICE);
        List<ApplicationInfo> apps = pm.getInstalledApplications(0);
        List<AppPermissionBean> result = new ArrayList<>();

        for (ApplicationInfo app : apps) {
            // —— 过滤掉所有系统/预装 应用 —— //
            boolean isSystemApp = (app.flags & ApplicationInfo.FLAG_SYSTEM) != 0;
            boolean isUpdatedSystemApp = (app.flags & ApplicationInfo.FLAG_UPDATED_SYSTEM_APP) != 0;
            boolean isInSystemPath = app.sourceDir != null
                    && (app.sourceDir.startsWith("/system/") || app.sourceDir.startsWith("/vendor/"));
            if (isSystemApp || isUpdatedSystemApp || isInSystemPath) {
                continue;
            }

            String pkg = app.packageName;
            PackageInfo pi;
            try {
                pi = pm.getPackageInfo(pkg, PackageManager.GET_PERMISSIONS);
            } catch (PackageManager.NameNotFoundException e) {
                continue;
            }
            String[] requested = pi.requestedPermissions;
            if (requested == null || requested.length == 0) {
                continue;
            }

            for (PermissionType type : PermissionType.values()) {
                // 收集可能的 Manifest.permission 值
                List<String> candidates = new ArrayList<>();
                if (type == PermissionType.LOCATION) {
                    candidates.add(Manifest.permission.ACCESS_FINE_LOCATION);
                    candidates.add(Manifest.permission.ACCESS_COARSE_LOCATION);
                } else {
                    candidates.add(mapToManifest(type));
                }

                // 找到它到底声明了哪条
                String manifestPerm = null;
                for (String p : candidates) {
                    if (arrayContains(requested, p)) {
                        manifestPerm = p;
                        break;
                    }
                }
                if (manifestPerm == null) {
                    continue;
                }

                // 1) 先用 PackageManager.checkPermission 看用户是否点了“允许”
                boolean pmGranted = pm.checkPermission(manifestPerm, pkg)
                        == PackageManager.PERMISSION_GRANTED;
                if (!pmGranted) {
                    result.add(buildBean(pm, app, type, AppPermissionLevel.DENIED));
                    continue;
                }

                // **LOCATION 类型特殊处理：只要不是 DENIED，就视为 WHILE_USING**
                if (type == PermissionType.LOCATION) {
                    result.add(buildBean(pm, app, type, AppPermissionLevel.WHILE_USING));
                    continue;
                }

                // 2) 非 LOCATION，沿用 AppOps 检测
                String opStr = AppOpsManager.permissionToOp(manifestPerm);
                int mode = AppOpsManager.MODE_ALLOWED;
                if (ops != null && opStr != null) {
                    mode = ops.checkOpNoThrow(opStr, app.uid, pkg);
                }
                AppPermissionLevel level = (mode == AppOpsManager.MODE_ALLOWED
                        || mode == AppOpsManager.MODE_DEFAULT)
                        ? AppPermissionLevel.WHILE_USING
                        : AppPermissionLevel.DENIED;

                result.add(buildBean(pm, app, type, level));
            }
        }
        return result;
    }


    private static boolean arrayContains(String[] arr, String target) {
        for (String s : arr) if (target.equals(s)) return true;
        return false;
    }

    private static AppPermissionBean buildBean(PackageManager pm,
                                               ApplicationInfo app,
                                               PermissionType type,
                                               AppPermissionLevel level) {
        String label = pm.getApplicationLabel(app).toString();
        return new AppPermissionBean(label, app.packageName, type, level);
    }

    /**
     * 对指定应用的权限进行申请或跳转设置
     */
    public static void requestAppPermission(Activity activity, AppPermissionBean bean, int requestCode) {
        String androidPerm = mapToManifest(bean.getType());
        if (!bean.getLevel().equals(AppPermissionLevel.DENIED)
                && ActivityCompat.shouldShowRequestPermissionRationale(activity, androidPerm)) {
            ActivityCompat.requestPermissions(
                    activity,
                    new String[]{androidPerm},
                    requestCode
            );
        } else {
            openAppSettings(activity, bean.getPackageName());
        }
    }

    /**
     * 打开系统的应用详情设置页
     */
    public static void openAppSettings(Context ctx, String packageName) {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        intent.setData(Uri.fromParts("package", packageName, null));
        ctx.startActivity(intent);
    }

    /**
     * 静默修改单个应用的运行时权限（Device Owner 专用）
     * 尝试修改权限并返回状态码：
     * 0 = 修改失败（需要跳转设置页或提示用户）
     * 1 = 修改成功
     *
     * @param ctx            上下文（确保非 null，最好用 Activity 或 ApplicationContext）
     * @param adminComponent 已激活的 DeviceAdminReceiver ComponentName
     * @param packageName    目标应用包名
     * @param type           要修改的权限类型（CAMERA / MICROPHONE / LOCATION）
     * @param grant          true=授予，false=撤销
     * @return 状态码（0=失败，1=成功）
     */
    public static int setAppPermissionState(Context ctx,
                                            ComponentName adminComponent,
                                            String packageName,
                                            PermissionType type,
                                            boolean grant) {
        final String TAG = "PermissionManager";
        DevicePolicyManager dpm = (DevicePolicyManager) ctx.getSystemService(Context.DEVICE_POLICY_SERVICE);

        if (dpm == null) {
            Log.e(TAG, "【错误】无法获取 DevicePolicyManager，操作终止");
            // 🔥 添加自动跳转
            jumpToAppSettings(ctx, packageName);
            return 0;
        }

        boolean isDeviceOwner = dpm.isDeviceOwnerApp(ctx.getPackageName());
        Log.d(TAG, "当前应用是否是 Device Owner: " + isDeviceOwner);

        // 1. 检查是否激活为设备所有者
        boolean isAdmin = dpm.isAdminActive(adminComponent);
        Log.d(TAG, "Device Owner 激活状态: " + isAdmin + "，Component=" + adminComponent);
        if (!isAdmin || !isDeviceOwner) {
            Log.e(TAG, "【失败】App 未被设为 Device Owner，无法静默修改权限");
            // 🔥 添加自动跳转
            jumpToAppSettings(ctx, packageName);
            return 0;
        }

        // 2. 确定要操作的运行时权限常量
        String perm = null;
        switch (type) {
            case CAMERA:
                perm = Manifest.permission.CAMERA;
                break;
            case MICROPHONE:
                perm = Manifest.permission.RECORD_AUDIO;
                break;
            case LOCATION:
                perm = Manifest.permission.ACCESS_FINE_LOCATION;
                break;
            default:
                Log.e(TAG, "【失败】未知 PermissionType: " + type);
                // 🔥 添加自动跳转
                jumpToAppSettings(ctx, packageName);
                return 0;
        }
        Log.d(TAG, "权限映射: " + type + " -> " + perm);

        // 3. 调用 DevicePolicyManager.setPermissionGrantState
        int grantState = grant
                ? DevicePolicyManager.PERMISSION_GRANT_STATE_GRANTED
                : DevicePolicyManager.PERMISSION_GRANT_STATE_DENIED;
        Log.d(TAG, "调用 setPermissionGrantState: package=" + packageName
                + ", perm=" + perm + ", grantState=" + grantState);

        boolean result = dpm.setPermissionGrantState(
                adminComponent, packageName, perm, grantState
        );
        Log.i(TAG, "setPermissionGrantState 返回: " + result);

        // 4. 验证当前权限状态
        int currentState = dpm.getPermissionGrantState(
                adminComponent, packageName, perm
        );
        Log.i(TAG, "getPermissionGrantState 返回: " + currentState);

        boolean isPermissionCorrect = (grant && currentState == DevicePolicyManager.PERMISSION_GRANT_STATE_GRANTED)
                || (!grant && currentState == DevicePolicyManager.PERMISSION_GRANT_STATE_DENIED);

        if (isPermissionCorrect) {
            Log.i(TAG, "【成功】权限已正确修改");
            return 1;
        } else {
            Log.e(TAG, "【失败】权限修改未生效，需要引导用户手动修改");
            // 🔥 添加自动跳转
            jumpToAppSettings(ctx, packageName);
            return 0;
        }
    }

    /**
     * 🔥 辅助方法：跳转到指定应用的系统设置页面，引导用户手动修改权限
     */
    private static void jumpToAppSettings(Context ctx, String packageName) {
        try {
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", packageName, null);
            intent.setData(uri);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            ctx.startActivity(intent);
            Log.i("PermissionManager", "已跳转到 " + packageName + " 的系统设置页面，请用户手动修改权限");
        } catch (Exception e) {
            Log.e("PermissionManager", "跳转设置页面失败: " + e.getMessage());
        }
    }


    // 内部工具：将自定义类型映射到系统 Manifest 常量
    private static String mapToManifest(PermissionType type) {
        switch (type) {
            case CAMERA:
                return android.Manifest.permission.CAMERA;
            case MICROPHONE:
                return android.Manifest.permission.RECORD_AUDIO;
            case LOCATION:
                return android.Manifest.permission.ACCESS_FINE_LOCATION;
            default:
                throw new IllegalArgumentException("Unknown PermissionType");
        }
    }

    private static final Executor EXECUTOR = Executors.newSingleThreadExecutor();

    /**
     * 反射获取历史权限访问记录
     *
     * @param ctx         系统 Context
     * @param packageName 目标包名
     * @param type        CAMERA/MICROPHONE/LOCATION
     * @param beginTime   起始时间 ms
     * @param endTime     结束时间 ms
     * @return RecordItemBean 列表
     */
    public static List<RecordItemBean> getHistoricalPermissionRecordsReflection(
            Context ctx,
            String packageName,
            PermissionType type,
            long beginTime,
            long endTime
    ) {
        List<RecordItemBean> result = new ArrayList<>();
        try {
            // 1. 拿到 AppOpsManager 实例
            AppOpsManager ops = ctx.getSystemService(AppOpsManager.class);

            // 2. 反射拿到 HistoricalOpsRequest.Builder
            Class<?> reqClass = Class.forName("android.app.AppOpsManager$HistoricalOpsRequest");
            Class<?> builderClass = Class.forName(
                    "android.app.AppOpsManager$HistoricalOpsRequest$Builder"
            );
            Constructor<?> ctor = builderClass.getDeclaredConstructor();
            Object builder = ctor.newInstance();

            // 3. setBeginTime, setEndTime
            Method mBegin = builderClass.getMethod("setBeginTime", long.class);
            Method mEnd = builderClass.getMethod("setEndTime", long.class);
            mBegin.invoke(builder, beginTime);
            mEnd.invoke(builder, endTime);

            // 4. setOpNames
            String opStr = mapToOpStr(type);
            Method mOpNames = builderClass.getMethod("setOpNames", String[].class);
            mOpNames.invoke(builder, new Object[]{new String[]{opStr}});

            // 5. setFlags（全部：HISTORY_FLAG_AGGREGATE | HISTORY_FLAG_DISCRETE）
            //    反射取常量
            Field fAgg = reqClass.getDeclaredField("HISTORY_FLAG_AGGREGATE");
            Field fDisc = reqClass.getDeclaredField("HISTORY_FLAG_DISCRETE");
            int flagAgg = fAgg.getInt(null);
            int flagDisc = fDisc.getInt(null);
            Method mFlags = builderClass.getMethod("setFlags", int.class);
            mFlags.invoke(builder, flagAgg | flagDisc);

            // 6. build()
            Method mBuild = builderClass.getMethod("build");
            Object request = mBuild.invoke(builder);

            // 7. 反射调用 getHistoricalOps(request, Executor, Consumer)
            Method mGetHist = AppOpsManager.class.getMethod(
                    "getHistoricalOps",
                    reqClass,
                    Executor.class,
                    Class.forName("java.util.function.Consumer")
            );

            // 8. 构造 Consumer 回调 handler
            Object consumer = Proxy.newProxyInstance(
                    PermissionManager.class.getClassLoader(),
                    new Class[]{Class.forName("java.util.function.Consumer")},
                    (proxy, method, args) -> {
                        // method==accept，args[0] 是 HistoricalOps 对象
                        Object historicalOps = args[0];
                        // 9. 遍历 HistoricalPackageOps
                        Method mGetPkgs = historicalOps.getClass()
                                .getMethod("getHistoricalPackageOps");
                        List<?> pkgList = (List<?>) mGetPkgs.invoke(historicalOps);
                        for (Object pkgOps : pkgList) {
                            // pkgOps.getPackageName()
                            Method mPkgName = pkgOps.getClass().getMethod("getPackageName");
                            String pkg = (String) mPkgName.invoke(pkgOps);
                            if (!packageName.equals(pkg)) continue;

                            // pkgOps.getHistoricalOpsList()
                            Method mGetOps = pkgOps.getClass()
                                    .getMethod("getHistoricalOpsList");
                            List<?> opsList = (List<?>) mGetOps.invoke(pkgOps);

                            int totalCount = 0;
                            long lastTime = 0;
                            for (Object opEntry : opsList) {
                                // opEntry.getAccessCount(), getLastAccessTime()
                                Method mCount = opEntry.getClass()
                                        .getMethod("getAccessCount");
                                Method mLast = opEntry.getClass()
                                        .getMethod("getLastAccessTime");
                                totalCount += (int) mCount.invoke(opEntry);
                                lastTime = Math.max(lastTime, (long) mLast.invoke(opEntry));
                            }
                            if (totalCount <= 0) continue;

                            String label = "";
                            try {
                                label = ctx.getPackageManager()
                                        .getApplicationLabel(
                                                ctx.getPackageManager()
                                                        .getApplicationInfo(pkg, 0)
                                        ).toString();
                            } catch (Exception ignored) {
                            }

                            String timeStr = new SimpleDateFormat(
                                    "yyyy年MM月dd日 HH:mm", Locale.getDefault()
                            ).format(new Date(lastTime));

                            result.add(new RecordItemBean(
                                    R.mipmap.ic_new_equipment_color_1,
                                    label,
                                    totalCount,
                                    timeStr
                            ));
                        }
                        return null;
                    }
            );

            // 10. 发起调用
            mGetHist.invoke(ops, request, EXECUTOR, consumer);

            // 注意：由于是异步，这里 result 可能还在填充；生产环境建议用回调模式
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    // PermissionType → opStr
    private static String mapToOpStr(PermissionType type) {
        switch (type) {
            case CAMERA:
                return AppOpsManager.OPSTR_CAMERA;
            case MICROPHONE:
                return AppOpsManager.OPSTR_RECORD_AUDIO;
            case LOCATION:
                return AppOpsManager.OPSTR_FINE_LOCATION;
            default:
                return "";
        }
    }

    /**
     * 获取摄像头权限的全局开启状态
     *
     * @param context 上下文
     * @return true表示摄像头权限全局开启，false表示关闭
     */
    public static boolean isCameraPermissionEnabled(Context context) {
        SystemPermission permission = getSystemPermission(context, PermissionType.CAMERA);
        return permission.isEnabled();
    }

    /**
     * 获取位置权限的全局开启状态
     *
     * @param context 上下文
     * @return true表示位置权限全局开启，false表示关闭
     */
    public static boolean isLocationPermissionEnabled(Context context) {
        SystemPermission permission = getSystemPermission(context, PermissionType.LOCATION);
        return permission.isEnabled();
    }

    /**
     * 获取麦克风权限的全局开启状态
     *
     * @param context 上下文
     * @return true表示麦克风权限全局开启，false表示关闭
     */
    public static boolean isMicrophonePermissionEnabled(Context context) {
        SystemPermission permission = getSystemPermission(context, PermissionType.MICROPHONE);
        return permission.isEnabled();
    }

    /**
     * 获取拥有摄像头权限的应用列表
     *
     * @param context 上下文
     * @return 包含应用包名和权限状态的应用列表
     */
    public static List<String> getAppsWithCameraPermission(Context context) {
        return filterAppsByPermissionType(loadAllAppPermissions(context), PermissionType.CAMERA);
    }

    /**
     * 获取拥有位置权限的应用列表
     *
     * @param context 上下文
     * @return 包含应用包名和权限状态的应用列表
     */
    public static List<String> getAppsWithLocationPermission(Context context) {
        return filterAppsByPermissionType(loadAllAppPermissions(context), PermissionType.LOCATION);
    }

    /**
     * 获取拥有麦克风权限的应用列表
     *
     * @param context 上下文
     * @return 包含应用包名和权限状态的应用列表
     */
    public static List<String> getAppsWithMicrophonePermission(Context context) {
        return filterAppsByPermissionType(loadAllAppPermissions(context), PermissionType.MICROPHONE);
    }

    /**
     * 辅助方法：根据权限类型过滤应用列表
     */
    private static List<String> filterAppsByPermissionType(
            List<AppPermissionBean> allApps, PermissionType type) {
        List<String> filteredList = new ArrayList<>();
        for (AppPermissionBean app : allApps) {
            if (app.getType() == type) {
                try {
                    filteredList.add(app.toJson());
                } catch (JSONException e) {
                    Log.d(TAG, "filterAppsByPermissionType: " + e);
                }
            }
        }
        return filteredList;
    }

    private static final String CAMERA_ENABLED_STATUS = "cameraEnabled";
    private static final String LOCATION_ENABLED_STATUS = "locationEnabled";
    private static final String MICROPHONE_ENABLED_STATUS = "microphoneEnabled";
    public static final String CAMERA_APPS_STATUS = "cameraApps";
    public static final String LOCATION_APPS_STATUS = "locationApps";
    public static final String MICROPHONE_APPS_STATUS = "microphoneApps";

    /**
     * 获取所有权限状态和应用列表的汇总信息
     *
     * @param context 上下文
     * @return 包含所有权限状态和应用列表的Map
     */
    public static Map<String, Object> getAllPermissionsSummary(Context context) {
        Map<String, Object> summary = new HashMap<>();

        // 添加权限全局状态
        summary.put(CAMERA_ENABLED_STATUS, isCameraPermissionEnabled(context));
        summary.put(LOCATION_ENABLED_STATUS, isLocationPermissionEnabled(context));
        summary.put(MICROPHONE_ENABLED_STATUS, isMicrophonePermissionEnabled(context));

        // 添加应用列表
        summary.put(CAMERA_APPS_STATUS, getAppsWithCameraPermission(context));
        summary.put(LOCATION_APPS_STATUS, getAppsWithLocationPermission(context));
        summary.put(MICROPHONE_APPS_STATUS, getAppsWithMicrophonePermission(context));

        return summary;
    }
}
