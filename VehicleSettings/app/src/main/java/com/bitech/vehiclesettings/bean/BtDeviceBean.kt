package com.bitech.vehiclesettings.bean

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothProfile
import com.bitech.vehiclesettings.utils.Contacts
import com.chery.adapter.androidauto.connection.devicelist.ConnectState
import com.chery.adapter.androidauto.connection.devicelist.ConnectType
import com.chery.adapter.common.config.IConstant

/**
 * @ClassName: BtDeviceBean
 * 
 * @Date:  2024/1/23 9:18
 * @Description: 蓝牙设备属性Bean.
 **/
data class BtDeviceBean(var device: BluetoothDevice) : Comparable<BtDeviceBean> {

    // fsp状态
    var hfpState = BluetoothProfile.STATE_DISCONNECTED

    // 前fsp状态
    var preHfpState = BluetoothProfile.STATE_DISCONNECTED

    // A2DP状态
    var a2dpState = BluetoothProfile.STATE_DISCONNECTED

    // 前A2DP状态
    var preA2dpState = BluetoothProfile.STATE_DISCONNECTED

    //是否是常用设备
    var isPreferencesDevice = false

    //是否始终播放手机媒体音频
    var isAlwaysPlayPhoneMedia = false

    // 蓝牙绑定状态
    var bondState = BluetoothDevice.BOND_NONE

    // 是否支持无线CarPlay
    var isSupportWirelessCP = false

    // CarPlay连接类型（0->无线，1->有线,-1->无效）
    var carPlayLinkType = Contacts.CP_CONNECT_INVALID

    // 有线连接的设备名称
    var wiredName = ""

    // CarPlay连接状态
    var cPConnectedState = IConstant.ConnectState.IDLE

    // 是否支持AA
    var isSupportWirelessAA = false

    // AndroidAuto连接类型（CONNECT_WIRELESS->无线，CONNECT_WIRED->有线）
    var androidAutoLinkType = ConnectType.CONNECT_INVALID.type

    // AA连接状态
    var aAConnectedState = ConnectState.DISCONNECTED.state

    // 设备配对时间
    var pairedTime = 0L

    /**
     * 自定义排序对比方法
     *
     * @param other 待对比的蓝牙设备对象.
     * @return <0,this对象>other对象；=0,this对象=other对象；>0.this对象>other对象
     */
    override fun compareTo(other: BtDeviceBean): Int {
        // 当前存在AA或CP已连接或连接中，排最首位，其他情况下，当前存在蓝牙连接中或已连接，排首位，其他情况按照排队时间前后顺序排列
        var comparison = 0
        /*comparison = compareToCpOrAaConnect(other) - compareToCpOrAaConnect(this)
        if(comparison != 0){
            return comparison
        }

        comparison = compareToBtConnect(other) - compareToBtConnect(this)
        if(comparison != 0){
            return comparison
        }*/

        //T13JSUPPLY-695 排序问题
        comparison = compareToConnect(other) - compareToConnect(this)

        if(comparison != 0){
            return comparison
        }
        comparison = other.pairedTime.compareTo(this.pairedTime)

        return comparison
    }

    private fun compareToCpOrAaConnect(btDeviceBean: BtDeviceBean):Int{
        var state = 0
        if(btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED || btDeviceBean.aAConnectedState == ConnectState.CONNECTED.state){
            state = 6
        }else if(btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTING || btDeviceBean.aAConnectedState == ConnectState.CONNECTING.state){
            state = 3
        }
        return state
    }

    private fun compareToBtConnect(btDeviceBean: BtDeviceBean):Int{
        var state = 0
        if(btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED && btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED){
            state = 5
        }else if(btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED || btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED){
            state = 4
        }else if(btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTING || btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTING ){
            state = 3
        }
        return state
    }

    private fun compareToConnect(btDeviceBean: BtDeviceBean):Int{
        var state = 0
        if(btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED || btDeviceBean.aAConnectedState == ConnectState.CONNECTED.state){
            state = 6
        }else if(btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTING || btDeviceBean.aAConnectedState == ConnectState.CONNECTING.state){
            state = 3
        }
        if(btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED && btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED){
            state = 6
        }else if(btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED || btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED){
            state = 6
        }else if(btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTING || btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTING ){
            state = 3
        }
        return state
    }

    /**
     * 对象比对重写
     *
     * @param other other
     * @return Boolean
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass)
            return false
        other as BtDeviceBean
        if (device.address != other.device.address)
            return false
        return true
    }

    override fun hashCode(): Int {
        return 31 * device.address.hashCode()
    }

    override fun toString(): String {
        return "BtDeviceBean(name=${device.name}, address=${device.address}, hfpState=$hfpState, a2dpState=$a2dpState, carPlayLinkType=$carPlayLinkType, cPConnectedState=$cPConnectedState, androidAutoLinkType=$androidAutoLinkType, aAConnectedState=$aAConnectedState, pairedTime=$pairedTime)"
    }
}
