package com.bitech.vehiclesettings.view.negative;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.databinding.DialogAlertNParkPowerBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.view.system.TimeSettingUIAlert;

public class ParkPowerCtrlUIAlert extends BaseDialog {
    public ParkPowerCtrlUIAlert(@NonNull Context context) {
        super(context);
    }

    public ParkPowerCtrlUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ParkPowerCtrlUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertNParkPowerBinding binding;
        private ParkPowerTimer powerTimer;
        private Handler handler;
        private Runnable autoDismissTask;
        NewEnergyManager newEnergyManager = NewEnergyManager.getInstance();

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private ParkPowerCtrlUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public ParkPowerCtrlUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public ParkPowerCtrlUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new ParkPowerCtrlUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertNParkPowerBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            handler = new Handler(Looper.getMainLooper());
            autoDismissTask = new Runnable() {
                @Override
                public void run() {
                    if (dialog != null && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                }
            };

            // 设置窗口消失监听
            dialog.setOnDismissListener(d -> {
                handler.removeCallbacks(autoDismissTask);
            });
            // 获取对话框的Window对象
            init();
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 960;
            layoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
            window.setAttributes(layoutParams);
            return dialog;
        }

        @SuppressLint("ClickableViewAccessibility")
        public void init() {
            powerTimer = new ParkPowerTimer(handler, newEnergyManager);

            binding.npSetHour.setMinValue(0);
            binding.npSetHour.setMaxValue(8);
            String[] hours = new String[9];
            hours[0] = context.getString(R.string.str_perpetual);
            for (int i = 1; i < 9; i++) {
                hours[i] = String.format("%2d  小时", i);
            }
            binding.npSetHour.setDisplayedValues(hours);
            binding.npSetHour.setValue(0);
            binding.npSetHour.setOnTouchListener(new View.OnTouchListener() {

                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    resetAutoDismissTimer();
                    return false;
                }
            });

            binding.getRoot().setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    resetAutoDismissTimer();
                    return false;
                }
            });
            binding.btnConfirm.setOnClickListener(v -> {
                handler.removeCallbacks(autoDismissTask);

                newEnergyManager.setParkPowerStatus(CarNewEnergy.ParkPowerSet.ON);
                // 获取用户选择的小时数
                int selectedHours = binding.npSetHour.getValue();
                powerTimer.schedulePowerOff(selectedHours);

                this.dismiss();
            });


            binding.btnCancel.setOnClickListener(v -> {
                this.dismiss();
            });

            // 开始5秒倒计时
            resetAutoDismissTimer();

        }

        private void resetAutoDismissTimer() {
            if (handler != null && autoDismissTask != null) {
                handler.removeCallbacks(autoDismissTask);
                handler.postDelayed(autoDismissTask, 5000);
            }
        }

        private void dismiss() {
            handler.removeCallbacks(autoDismissTask);
            dialog.dismiss();
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }


}
