package com.bitech.vehiclesettings.utils;

import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bitech.vehicle3D.VehicleServiceManager;
import com.bitech.vehiclesettings.carapi.constants.Car3DModel;
import com.bitech.wallpaper3D.aidl.IPropertyCallback;

public class ThreeDModelUtil {

    private static final String TAG = "ThreeDModelUtil";

    public interface OnResultCallback {
        void onResult(boolean success);
    }

    public static void set3DModelEffects(int id, String name, int status, OnResultCallback callback) {
        Bundle data = new Bundle();
        data.putInt(name, status);
        VehicleServiceManager.getClientManager().to3DWPSetProperty(id, data, new IPropertyCallback.Stub() {
            @Override
            public void onSuccess(Bundle result) {
                if (callback != null) callback.onResult(true);
            }

            @Override
            public void onError(Bundle result) {
                if (callback != null) callback.onResult(false);
            }
        });
//        activity.set3DModelEffects(id, name, status);
    }

    public static void setFrontLeftDoor(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.LEFT_FRONT_DOOR, Car3DModel.CarControlName.LEFT_FRONT_DOOR, status, callback);
    }

    public static void setFrontRightDoor(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.RIGHT_FRONT_DOOR, Car3DModel.CarControlName.RIGHT_FRONT_DOOR, status, callback);
    }

    public static void setRearLeftDoor(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.LEFT_REAR_DOOR, Car3DModel.CarControlName.LEFT_REAR_DOOR, status, callback);
    }

    public static void setRearRightDoor(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.RIGHT_REAR_DOOR, Car3DModel.CarControlName.RIGHT_REAR_DOOR, status, callback);
    }

    public static void setFrontLeftWindow(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.LEFT_FRONT_WINDOW, Car3DModel.CarControlName.LEFT_FRONT_WINDOW, status, callback);
    }

    public static void setFrontRightWindow(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.RIGHT_FRONT_WINDOW, Car3DModel.CarControlName.RIGHT_FRONT_WINDOW, status, callback);
    }

    public static void setRearLeftWindow(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.LEFT_REAR_WINDOW, Car3DModel.CarControlName.LEFT_REAR_WINDOW, status, callback);
    }

    public static void setRearRightWindow(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.RIGHT_REAR_WINDOW, Car3DModel.CarControlName.RIGHT_REAR_WINDOW, status, callback);
    }

    public static void setAllWindowState(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.ALL_WINDOW, Car3DModel.CarControlName.ALL_WINDOW, status, callback);
    }

    public static void setTrunkLidState(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.TRUNK_LID, Car3DModel.CarControlName.TRUNK_LID, status, callback);
    }

    public static void setRearMirrorState(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.L_MIRROR, Car3DModel.CarControlName.L_MIRROR, status, callback);
        set3DModelEffects(Car3DModel.CarControlId.R_MIRROR, Car3DModel.CarControlName.R_MIRROR, status, callback);
    }

    public static void setHoodState(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.CarControlId.HOOD, Car3DModel.CarControlName.HOOD, status, callback);
    }

    public static void setCameraState(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.ControlModelState.CONTROL_MODEL_STATE_ID, Car3DModel.ControlModelState.CONTROL_MODEL_STATE_NAME, status, callback);
    }

    public static void set3DWallpaper(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.WallpaperState.WALLPAPER_STATE_ID, Car3DModel.WallpaperState.WALLPAPER_STATE_NAME, status, callback);
    }

    public static void setDriveModeState(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.DriveMode.DRIVE_MODE_ID, Car3DModel.DriveMode.DRIVE_MODE_NAME, status, callback);
    }

    public static void setRenderState(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.RenderState.RENDER_STATE_ID, Car3DModel.RenderState.RENDER_STATE_NAME, status, callback);
    }

    public static void setChargeMode(int status, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.ChargeMode.CHARGE_MODE_ID, Car3DModel.ChargeMode.CHARGE_MODE_NAME, status, callback);
    }

    public static void setBatteryState(int percent, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.BatteryPercent.BATTERY_STATE_ID, Car3DModel.BatteryPercent.PERCENT, percent, callback);
    }

    public static void setChargeState(String power, String current, String voltage, OnResultCallback callback) {
        Bundle data = new Bundle();
        data.putString(Car3DModel.ChargePower.POWER, power);
        data.putString(Car3DModel.ChargePower.CURRENT, current);
        data.putString(Car3DModel.ChargePower.VOLTAGE, voltage);
        VehicleServiceManager.getClientManager().to3DWPSetProperty(Car3DModel.ChargePower.CHARGE_STATE_ID, data, new IPropertyCallback.Stub() {
            @Override
            public void onSuccess(Bundle result) {
                Log.d(TAG, " charge state update success: " + power + " " + current + " " + voltage);
                if (callback != null) callback.onResult(true);
            }

            @Override
            public void onError(Bundle result) {
                Log.d(TAG, " charge state update error: " + power + " " + current + " " + voltage);
                if (callback != null) callback.onResult(false);
            }
        });
    }

    public static void setDischargeState(String maxPower, String currentPower, OnResultCallback callback) {
        Bundle data = new Bundle();
        data.putString(Car3DModel.DischargePower.MAX_POWER_NAME, maxPower);
        data.putString(Car3DModel.DischargePower.CURRENT_POWER, currentPower);
        VehicleServiceManager.getClientManager().to3DWPSetProperty(Car3DModel.DischargePower.DISCHARGE_STATE_ID, data, new IPropertyCallback.Stub() {
            @Override
            public void onSuccess(Bundle result) {
                Log.d(TAG, " discharge state update success: " + maxPower + " " + currentPower);
                if (callback != null) callback.onResult(true);
            }

            @Override
            public void onError(Bundle result) {
                Log.d(TAG, " discharge state update error: " + maxPower + " " + currentPower);
                if (callback != null) callback.onResult(false);
            }
        });
    }

    public static void setChargeRemainTime(int time, OnResultCallback callback) {
        set3DModelEffects(Car3DModel.ChargeRemainTime.CHARGE_REMAIN_TIME_ID, Car3DModel.ChargeRemainTime.CHARGE_REMAIN_TIME_NAME, time, callback);
    }

    public static void setWeatherTimeState(int weather, int time, OnResultCallback callback) {
        Bundle data = new Bundle();
        data.putInt(Car3DModel.WeatherTime.WEATHER_NAME, weather);
        data.putInt(Car3DModel.WeatherTime.TIME_NAME, time);
        VehicleServiceManager.getClientManager().to3DWPSetProperty(Car3DModel.WeatherTime.WEATHER_TIME_ID, data, new IPropertyCallback.Stub() {
            @Override
            public void onSuccess(Bundle result) {
                Log.d(TAG, "天气时间状态更新成功: " + weather + " " + time);
                if (callback != null) callback.onResult(true);
            }

            @Override
            public void onError(Bundle result) {
                Log.d(TAG, "天气时间状态更新失败: " + weather + " " + time);
                if (callback != null) callback.onResult(false);
            }
        });
    }

    public static void setMusicWallpaper(MusicWallpaper musicWallpaper, OnResultCallback callback) {
        Bundle data = new Bundle();
        data.putFloat(Car3DModel.MusicWallpaper.LOUDNESS, musicWallpaper.getLoudness());
        data.putFloat(Car3DModel.MusicWallpaper.FREQUENCY, musicWallpaper.getFrequency());
        data.putFloat(Car3DModel.MusicWallpaper.COLOR1R, musicWallpaper.getColor1R());
        data.putFloat(Car3DModel.MusicWallpaper.COLOR1G, musicWallpaper.getColor1G());
        data.putFloat(Car3DModel.MusicWallpaper.COLOR1B, musicWallpaper.getColor1B());
        data.putFloat(Car3DModel.MusicWallpaper.COLOR1A, musicWallpaper.getColor1A());
        data.putFloat(Car3DModel.MusicWallpaper.COLOR2R, musicWallpaper.getColor2R());
        data.putFloat(Car3DModel.MusicWallpaper.COLOR2G, musicWallpaper.getColor2G());
        data.putFloat(Car3DModel.MusicWallpaper.COLOR2B, musicWallpaper.getColor2B());
        data.putFloat(Car3DModel.MusicWallpaper.COLOR2A, musicWallpaper.getColor2A());

        VehicleServiceManager.getClientManager().to3DWPSetProperty(Car3DModel.MusicWallpaper.MUSIC_WALLPAPER_ID, data, new IPropertyCallback.Stub() {
            @Override
            public void onSuccess(Bundle result) {
                if (callback != null) callback.onResult(true);
            }

            @Override
            public void onError(Bundle result) {
                if (callback != null) callback.onResult(false);
            }
        });
    }

    public static class MusicWallpaper {
        private float Loudness; // 响度
        private float Frequency;    // 频率
        private float Color1R; // RGB颜色
        private float Color1G;
        private float Color1B;
        private float Color1A;
        private float Color2R; // RGB颜色
        private float Color2G;
        private float Color2B;
        private float Color2A;

        public MusicWallpaper() {
            Loudness = 0.0f;
            Frequency = 0.0f;
            Color1R = 0.0f;
            Color1G = 0.0f;
            Color1B = 0.0f;
            Color1A = 0.0f;
            Color2R = 0.0f;
            Color2G = 0.0f;
            Color2B = 0.0f;
            Color2A = 0.0f;
        }

        public float getLoudness() {
            return Loudness;
        }

        public void setLoudness(float loudness) {
            Loudness = loudness;
        }

        public float getFrequency() {
            return Frequency;
        }

        public void setFrequency(float frequency) {
            Frequency = frequency;
        }

        public float getColor1R() {
            return Color1R;
        }

        public void setColor1R(float color1R) {
            Color1R = color1R;
        }

        public float getColor1G() {
            return Color1G;
        }

        public void setColor1G(float color1G) {
            Color1G = color1G;
        }

        public float getColor1B() {
            return Color1B;
        }

        public void setColor1B(float color1B) {
            Color1B = color1B;
        }

        public float getColor1A() {
            return Color1A;
        }

        public void setColor1A(float color1A) {
            Color1A = color1A;
        }

        public float getColor2R() {
            return Color2R;
        }

        public void setColor2R(float color2R) {
            Color2R = color2R;
        }

        public float getColor2G() {
            return Color2G;
        }

        public void setColor2G(float color2G) {
            Color2G = color2G;
        }

        public float getColor2B() {
            return Color2B;
        }

        public void setColor2B(float color2B) {
            Color2B = color2B;
        }

        public float getColor2A() {
            return Color2A;
        }

        public void setColor2A(float color2A) {
            Color2A = color2A;
        }

        @NonNull
        @Override
        public String toString() {
            return "MusicWallpaper{" +
                    "Loudness=" + Loudness +
                    ", Frequency=" + Frequency +
                    ", Color1R=" + Color1R +
                    ", Color1G=" + Color1G +
                    ", Color1B=" + Color1B +
                    ", Color1A=" + Color1A +
                    ", Color2R=" + Color2R +
                    ", Color2G=" + Color2G +
                    ", Color2B=" + Color2B +
                    ", Color2A=" + Color2A +
                    '}';
        }
    }
}
