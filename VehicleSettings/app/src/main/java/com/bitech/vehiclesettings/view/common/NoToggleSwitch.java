package com.bitech.vehiclesettings.view.common;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.appcompat.widget.SwitchCompat;

public class NoToggleSwitch extends SwitchCompat {

    public NoToggleSwitch(Context context) {
        super(context);
    }

    public NoToggleSwitch(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public NoToggleSwitch(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void toggle() {
        // 禁止内部自动切换状态
    }

    @Override
    public void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        // 确保宽度至少为64dp
        int width = MeasureSpec.getSize(widthMeasureSpec);
        int minWidth = (int) getResources().getDimension(com.bitech.vehiclesettings.R.dimen.switch_min_width); // 64dp对应的像素值

        if (width < minWidth) {
            widthMeasureSpec = MeasureSpec.makeMeasureSpec(minWidth, MeasureSpec.EXACTLY);
        }

        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        setChecked(isChecked());
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_UP) {
            // 仅触发点击事件，不自动切换状态
            if (isEnabled() && isClickable()) {
                performClick(); // 手动触发点击
            }
        }
        return true; // 拦截但我们手动触发点击
    }

    @Override
    public boolean performClick() {
        // 手动调用监听器
        return super.performClick();
    }
}