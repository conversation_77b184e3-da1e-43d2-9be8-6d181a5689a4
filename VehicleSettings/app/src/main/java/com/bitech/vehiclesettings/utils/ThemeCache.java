package com.bitech.vehiclesettings.utils;

import android.content.Context;
import android.graphics.Color;
import android.preference.PreferenceManager;

import androidx.annotation.ColorInt;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.ColorUtils;

import com.bitech.vehiclesettings.R;

public class ThemeCache {
    private static int sCurrentColor = -1;
    private static int sCurrentDarkColor = -1;

    public static void setCurrentColor(@ColorInt int color) {
        sCurrentColor = color;
        sCurrentDarkColor = ColorUtils.blendARGB(color, Color.BLACK, 0.2f);
    }

    public static int getCurrentColor(Context context) {
        if (sCurrentColor == -1) {
            sCurrentColor = PreferenceManager.getDefaultSharedPreferences(context)
                .getInt("selected_primary_color", 
                       ContextCompat.getColor(context, R.color.system_color_blue));
        }
        return sCurrentColor;
    }

    public static int getCurrentDarkColor(Context context) {
        if (sCurrentDarkColor == -1) {
            getCurrentColor(context); // 确保主色已初始化
        }
        return sCurrentDarkColor;
    }
}