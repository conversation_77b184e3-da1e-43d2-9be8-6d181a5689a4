package com.bitech.vehiclesettings.bean;

public class ScanDevice {
    /**
     * 蓝牙名称.
     */
    private String name;
    /**
     * mac.
     */
    private String mac;
    /**
     * RSSI.
     */
    private String rssi;
    /**
     * 设备类型
     */
    private int btType;
    /**
     * 设备状态
     */
    private int bondState;

    /**
     * 别名.
     */
    private String alias;

    private boolean isConnected;



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getRssi() {
        return rssi;
    }

    public void setRssi(String rssi) {
        this.rssi = rssi;
    }

    public int getBtType() {
        return btType;
    }

    public void setBtType(int btType) {
        this.btType = btType;
    }

    public int getBondState() {
        return bondState;
    }

    public void setBondState(int bondState) {
        this.bondState = bondState;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    // 构造方法、Getter和Setter
    public boolean isConnected() {
        return isConnected;
    }

    public void setConnected(boolean connected) {
        isConnected = connected;
    }
}
