package com.bitech.vehiclesettings.service.atmosphere.rhythm;

import android.media.audiofx.Visualizer;
import android.util.Log;

/**
 * 可视化.
 */
public class VisualizerManager implements Visualizer.OnDataCaptureListener {
    // 单例实现（双重检查锁定）
    private static volatile VisualizerManager instance;
    private Visualizer visualizer;
    private AudioVisualConverter audioVisualConverter = new AudioVisualConverter();
    private boolean isInitialized = false;
    private MusicDataListener musicDataListener;

    // 私有构造器
    private VisualizerManager() {
    }

    public static VisualizerManager getInstance() {
        if (instance == null) {
            synchronized (VisualizerManager.class) {
                if (instance == null) {
                    instance = new VisualizerManager();
                }
            }
        }
        return instance;
    }

    // 初始化Visualizer（带完整异常处理）
    public void initVisualizer() {
        if (isInitialized) {
            return;
        }

        try {
            // 音频会话ID（需根据实际业务调整）
            visualizer = new Visualizer(0);
            visualizer.setEnabled(false);
            int captureSize = Visualizer.getCaptureSizeRange()[1];
            int captureRate = (int) (Visualizer.getMaxCaptureRate() * 0.75f);

            visualizer.setCaptureSize(captureSize);
            visualizer.setDataCaptureListener(
                    this,
                    captureRate,
                    true,  // 波形数据
                    true   // FFT数据
            );
            visualizer.setScalingMode(Visualizer.SCALING_MODE_NORMALIZED);
            visualizer.setEnabled(AppUtils.isMusicModel());

            isInitialized = true;
        } catch (RuntimeException e) {
            Log.e("VisualizerManager", "初始化失败，请检查权限或音频会话ID", e);
            //releaseVisualizer();
        }
    }

    // 注册监听器（带线程安全处理）
    public synchronized void registerMusicDataListener(MusicDataListener listener) {
        if (musicDataListener != null) {
            musicDataListener.onForceDestroy();
        }
        musicDataListener = listener;
    }

    // 启用/禁用Visualizer（带空检查）
    public void setVisualizerEnabled(boolean enable) {
        if (visualizer != null) {
            try {
                visualizer.setEnabled(enable);
            } catch (IllegalStateException e) {
                Log.w("VisualizerManager", "Visualizer状态异常: " + e.getMessage());
            }
        }
    }

    // 波形数据处理（优化计算逻辑）
    @Override
    public void onWaveFormDataCapture(Visualizer visualizer, byte[] waveform, int samplingRate) {
        if (waveform == null || waveform.length < 2) {
            return;
        }

        float[] magnitudes = new float[waveform.length / 2];
        int maxIndex = 0;
        float maxMagnitude = Float.MIN_VALUE;

        for (int i = 0; i < magnitudes.length; i++) {
            final float magnitude = (float) Math.hypot(
                    waveform[2 * i],
                    waveform[2 * i + 1]
            );
            magnitudes[i] = magnitude;

            if (magnitude > maxMagnitude) {
                maxMagnitude = magnitude;
                maxIndex = i;
            }
        }

        final int effectiveSize = Visualizer.getMaxCaptureRate() * 3 / 4;
        final double currentFrequency = maxIndex * (effectiveSize / (double) waveform.length);

        if (musicDataListener != null) {
            musicDataListener.onWaveChanged(currentFrequency);
        }
    }

    // FFT数据处理（完整实现）
    @Override
    public void onFftDataCapture(Visualizer visualizer, byte[] fft, int samplingRate) {
        if (fft == null || fft.length < 2) {
            return;
        }

        final int range = fft.length / 2;
        float[] magnitudes = new float[range];
        float maxMagnitude = Float.MIN_VALUE;

        for (int i = 0; i < range; i++) {
            magnitudes[i] = (float) Math.hypot(fft[2 * i], fft[2 * i + 1]);
            if (magnitudes[i] > maxMagnitude) {
                maxMagnitude = magnitudes[i];
            }
        }

        final float minDB = 1.0f;
        final float currentDB = (maxMagnitude > minDB) ?
                (float) (20 * Math.log10(maxMagnitude)) : 0;

        if (musicDataListener != null) {
            musicDataListener.onFftChanged(magnitudes, currentDB);
        }
    }

    // 资源释放（重要！）
    public void release() {
        if (visualizer != null) {
            try {
                visualizer.release();
            } catch (IllegalStateException e) {
                Log.w("VisualizerManager", "资源释放异常: " + e.getMessage());
            }
            visualizer = null;
            isInitialized = false;
        }
    }

    // 回调接口定义
    public interface MusicDataListener {
        void onWaveChanged(double frequency);

        void onFftChanged(float[] magnitudes, float currentDB);

        void onForceDestroy();
    }

    // 工具类静态方法
    private static class AppUtils {
        static boolean isMusicModel() {
            // 根据实际业务实现
            return true;
        }
    }

    // 音频转换类（空实现）
    private static class AudioVisualConverter {
    }
}
