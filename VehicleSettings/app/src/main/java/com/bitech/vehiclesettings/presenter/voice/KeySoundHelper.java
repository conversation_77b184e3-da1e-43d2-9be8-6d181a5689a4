package com.bitech.vehiclesettings.presenter.voice;

import android.content.ContentResolver;
import android.content.Context;
import android.media.AudioManager;
import android.os.Handler;
import android.provider.Settings;
import android.database.ContentObserver;
import android.net.Uri;
import android.util.Log;

public class KeySoundHelper {

    private static final String TAG = "KeySoundHelper";

    public static void refreshKeySound(Context context) {
        AudioManager am = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        am.unloadSoundEffects(); // 清除缓存
        am.loadSoundEffects();   // 重新加载系统音效
    }

    /**
     * 启用或关闭系统按键音
     *
     * @param context Context
     * @param enabled  true 开启，false 关闭
     */
    public static boolean setKeySoundEnabled(Context context, boolean enabled) {
        try {
            Settings.System.putInt(context.getContentResolver(),
                    Settings.System.SOUND_EFFECTS_ENABLED, enabled ? 1 : 0);
            refreshKeySound(context);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 查询当前是否启用按键音
     *
     * @param context Context
     * @return true：启用，false：关闭
     */
    public static boolean isKeySoundEnabled(Context context) {
        try {
            return Settings.System.getInt(context.getContentResolver(),
                    Settings.System.SOUND_EFFECTS_ENABLED, 1) == 1;
        } catch (Exception e) {
            Log.e(TAG, "Failed to get key sound setting", e);
            return false;
        }
    }

    /**
     * 监听系统按键音设置的变化
     */
    public static void registerKeySoundObserver(Context context, OnKeySoundChangedListener listener) {
        Uri uri = Settings.System.getUriFor(Settings.System.SOUND_EFFECTS_ENABLED);
        ContentResolver resolver = context.getContentResolver();

        resolver.registerContentObserver(uri, false, new ContentObserver(new Handler()) {
            @Override
            public void onChange(boolean selfChange) {
                boolean enabled = isKeySoundEnabled(context);
                Log.d(TAG, "Key sound changed: " + enabled);
                if (listener != null) {
                    listener.onKeySoundChanged(enabled);
                }
            }
        });
    }

    public interface OnKeySoundChangedListener {
        void onKeySoundChanged(boolean enabled);
    }
}
