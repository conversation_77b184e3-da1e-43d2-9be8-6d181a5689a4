package com.bitech.vehiclesettings.service.voice

import android.content.Context
import android.util.Log
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.manager.DrivingManager
import com.bitech.platformlib.manager.NewEnergyManager
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarAvm
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy.Reset
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.fragment.ConditionFragment
import com.bitech.vehiclesettings.fragment.DrivingFragment
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.DialogNavigationUtils
import com.bitech.vehiclesettings.view.driving.PersonalizedSettingUIAlert
import com.bitech.vehiclesettings.view.newenergy.EnergyConsumptionListDialog
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P
import org.json.JSONObject
import java.time.LocalTime
import kotlin.random.Random


class NewEnergyControl(private val context: Context) {

    private val carNewEnergyManager: NewEnergyManager =
        BitechCar.getInstance().getServiceManager(BitechCar.CAR_ENERGY_MANAGER) as NewEnergyManager

    private var mDrivingManager: DrivingManager? = DrivingManager.getInstance()

    /**
     * 打开/关闭 电耗信息
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setElectricityInfo(voiceCommandState: Boolean) {
        // TODO 判判断车辆是否支持该功能 close_power_consumption_information_3
        if (voiceCommandState) {
            if (EnergyConsumptionListDialog.isShow) {
                sendResultCode(VDVRRespondID.open_power_consumption_information_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    ConditionFragment.ENERGY_CONSUMPTION_LIST_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_power_consumption_information_1)
            }
        } else {
            if (EnergyConsumptionListDialog.isShow) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    ConditionFragment.ENERGY_CONSUMPTION_LIST_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_power_consumption_information_2)
            } else {
                sendResultCode(VDVRRespondID.close_power_consumption_information_1)
            }
        }
    }

    /**
     * 打开/关闭 能耗清单
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setEnergyList(voiceCommandState: Boolean) {
        //同打开能量信息
        setEnergyInfoVisible(voiceCommandState)
    }

    /**
     * 打开/关闭 油耗信息显示
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setOilInfo(voiceCommandState: Boolean) {
        // 判断车辆是否是纯电车型 open_oil_consumption_2 close_oil_consumption_2
        if (voiceCommandState) {
            if (EnergyConsumptionListDialog.isShow) {
                sendResultCode(VDVRRespondID.open_oil_consumption_3)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    ConditionFragment.ENERGY_CONSUMPTION_LIST_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_oil_consumption_1)
            }
        } else {
            if (EnergyConsumptionListDialog.isShow) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    ConditionFragment.ENERGY_CONSUMPTION_LIST_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_oil_consumption_1)
            } else {
                sendResultCode(VDVRRespondID.close_oil_consumption_3)
            }
        }
    }

    /**
     * 行驶里程显示调节
     * @param mode 0:纯电⾥程 1:燃油⾥程 2:总⾥程
     */
    fun setDriveMileageDisplay(mode: String) {
        //判断是否存在该配置 Adjustment_mileage_display_4
        /**
         * 信号映射
         * 0--2纯电里程
         * 1--1燃油里程
         * 2--0总里程
         */
        val newMode =
            if (mode.toInt() == 2) TOTAL_MODE else if (mode.toInt() == 0) PURE_ELECTRIC_MODE else OIL_MODE
        val oldMode = carNewEnergyManager.mileageDisplay

        if (newMode !in TOTAL_MODE..PURE_ELECTRIC_MODE) {
            sendResultCode(VDVRRespondID.Adjustment_mileage_display_3)
        } else {
            if (oldMode == newMode) {
                when (oldMode) {
                    TOTAL_MODE -> sendResultCode(
                        VDVRRespondID.Adjustment_mileage_display_2,
                        TOTAL_MODE_TEXT
                    )

                    OIL_MODE -> sendResultCode(
                        VDVRRespondID.Adjustment_mileage_display_2,
                        OIL_MODE_TEXT
                    )

                    PURE_ELECTRIC_MODE -> sendResultCode(
                        VDVRRespondID.Adjustment_mileage_display_2,
                        PURE_ELECTRIC_MODE_TEXT
                    )
                }
            } else {
                carNewEnergyManager.mileageDisplay = newMode
                sendResultCode(VDVRRespondID.Adjustment_mileage_display_1)
            }
        }
    }

    /**
     * 纯电续航显示
     * @param mode 0:电量百分比 1:电续航里程
     */
    fun setPureElectricMode(mode: String) {
        // 需要判断车辆是否存在该配置 Switch_pure_electric_range_display_mode_4

        val newMode = mode.toInt();
        val oldMode = carNewEnergyManager.pureElectricDisplay

        if (newMode !in BATTERY_PERCENTAGE..ELECTRIC_LIFESPAN_MILEAGE) {//没有该模式
            sendResultCode(VDVRRespondID.Switch_pure_electric_range_display_mode_3)
        } else {
            if (oldMode == newMode) {
                when (oldMode) {
                    BATTERY_PERCENTAGE -> sendResultCode(
                        VDVRRespondID.Switch_pure_electric_range_display_mode_2,
                        BATTERY_PERCENTAGE_TEXT
                    )

                    ELECTRIC_LIFESPAN_MILEAGE -> sendResultCode(
                        VDVRRespondID.Switch_pure_electric_range_display_mode_2,
                        ELECTRIC_LIFESPAN_MILEAGE_TEXT
                    )
                }
            } else {
                carNewEnergyManager.pureElectricDisplay = newMode
                sendResultCode(VDVRRespondID.Switch_pure_electric_range_display_mode_1)
            }
        }
    }

    /**
     * 能耗显示值重置清零
     */
    fun resetEnergyInfo() {
        carNewEnergyManager.setSelfClearing(Reset.ENABLE)
        sendResultCode(VDVRRespondID.energy_consumption_reset_1)
    }

    /**
     * 控制对外放电
     * @param voiceCommandState true-开启 false-关闭
     */
    fun setExternalPowerSupply(voiceCommandState: Boolean) {
        // 获取对外放电状态
        val dischargeStatus = carNewEnergyManager.dischargeSts
        //获取充电枪放电状态
        val chargeGunStatus = carNewEnergyManager.chargeGunStatus
        //获取当前电量
        val batteryLevel = carNewEnergyManager.batteryLevel
        //获取当前SOC下限值
        val dischargeSocLowThreshold = carNewEnergyManager.getDischargeSocLowThreshold(context)
        if (voiceCommandState) {
            when (dischargeStatus) {
                CarNewEnergy.V2LFunctionSts.ON -> {
                    sendResultCode(VDVRRespondID.open_external_power_supply_1)
                }

                CarNewEnergy.V2LFunctionSts.OFF -> {
                    //检查放电枪是否连接、当前SOC是否小于等于30、检查当前电量SOC是否小于放电下限值
                    if (chargeGunStatus != CarNewEnergy.SlowGunConnectSts.V2L
                        || batteryLevel <= 30
                        || batteryLevel <= dischargeSocLowThreshold
                    ) {
                        sendResultCode(VDVRRespondID.open_external_power_supply_4)
                    } else {
                        carNewEnergyManager.setDischargeSwitch(CarNewEnergy.V2LSwitchSts.ON)
                        sendResultCode(VDVRRespondID.open_external_power_supply_2)
                    }
                }
                // TODO: 配置留空
                //sendResultCode(VDVRRespondID.open_external_power_supply_3)
            }
        } else {
            when (dischargeStatus) {
                CarNewEnergy.V2LFunctionSts.ON -> {
                    carNewEnergyManager.setDischargeSwitch(CarNewEnergy.V2LSwitchSts.OFF)
                    sendResultCode(VDVRRespondID.close_external_device_power_supply_12)
                }

                CarNewEnergy.V2LFunctionSts.OFF -> {
                    sendResultCode(VDVRRespondID.close_external_device_power_supply_1)
                }
                // TODO: 配置留空
                //sendResultCode(VDVRRespondID.open_external_power_supply_3)
            }
        }
    }

    /**
     * 控制对外设备供电
     * @param voiceCommandState true-开启 false-关闭
     */
    fun setExternalDevicePowerSupply(voiceCommandState: Boolean) {
        // 获取对外放电状态
        val dischargeStatus = carNewEnergyManager.dischargeSts
        //获取充电枪放电状态
        val chargeGunStatus = carNewEnergyManager.chargeGunStatus
        //获取当前电量
//        val batteryLevel = carNewEnergyManager.batteryLevel
        if (voiceCommandState) {
            when (dischargeStatus) {
                CarNewEnergy.V2LFunctionSts.ON -> {
                    sendResultCode(VDVRRespondID.open_external_device_power_supply_3)
                }

                CarNewEnergy.V2LFunctionSts.OFF -> {
                    //检查放电枪是否连接
                    if (chargeGunStatus != CarNewEnergy.SlowGunConnectSts.V2L) {
                        sendResultCode(VDVRRespondID.open_external_power_supply_4)
                    } else {
                        carNewEnergyManager.setDischargeSwitch(CarNewEnergy.V2LSwitchSts.ON)
                        sendResultCode(VDVRRespondID.open_external_device_power_supply_2)
                    }
                }
                // TODO: 配置留空
                //sendResultCode(VDVRRespondID.open_external_power_supply_3)
            }
        } else {
            when (dischargeStatus) {
                CarNewEnergy.V2LFunctionSts.ON -> {
                    carNewEnergyManager.setDischargeSwitch(CarNewEnergy.V2LSwitchSts.OFF)
                    sendResultCode(VDVRRespondID.close_external_power_supply_1)
                }

                CarNewEnergy.V2LFunctionSts.OFF -> {
                    sendResultCode(VDVRRespondID.close_external_power_supply_2)
                }
                // TODO: 配置留空
                //sendResultCode(VDVRRespondID.open_external_power_supply_3)
            }
        }
    }

    /**
     * 控制对外车辆供电
     * @param voiceCommandState true-开启 false-关闭
     */
    fun setExternalVehiclePowerSupply(voiceCommandState: Boolean) {
        // 获取对外放电状态
        val dischargeStatus = carNewEnergyManager.dischargeSts
        //获取当前电量
//        val batteryLevel = carNewEnergyManager.batteryLevel
        if (voiceCommandState) {
            when (dischargeStatus) {
                CarNewEnergy.V2LFunctionSts.ON -> {
                    sendResultCode(VDVRRespondID.open_external_vehicle_power_supply_2)
                }

                CarNewEnergy.V2LFunctionSts.OFF -> {
                    carNewEnergyManager.setDischargeSwitch(CarNewEnergy.V2LSwitchSts.ON)
                    sendResultCode(VDVRRespondID.open_external_vehicle_power_supply_1)
                }
                // TODO: 配置留空
                //sendResultCode(VDVRRespondID.open_external_power_supply_3)
            }
        } else {
            when (dischargeStatus) {
                CarNewEnergy.V2LFunctionSts.ON -> {
                    carNewEnergyManager.setDischargeSwitch(CarNewEnergy.V2LSwitchSts.OFF)
                    sendResultCode(VDVRRespondID.close_external_vehicle_power_supply_1)
                }

                CarNewEnergy.V2LFunctionSts.OFF -> {
                    sendResultCode(VDVRRespondID.close_external_vehicle_power_supply_2)
                }
                // TODO: 配置留空
                //sendResultCode(VDVRRespondID.open_external_power_supply_3)
            }
        }
    }

    /**
     * 控制驻车发电
     * @param voiceCommandState true-开启 false-关闭
     */
    fun setParkingPowerGeneration(voiceCommandState: Boolean) {
        // TODO: 这里缺少开关置灰态
        //获取驻车发电状态
        val forceEVChargeStatus = carNewEnergyManager.forceEvChargeMode
        //获取挡位信息
        val gearStatus = carNewEnergyManager.drivingInfoGear
        if (voiceCommandState) {
            if (forceEVChargeStatus == CarNewEnergy.ForceChargeSts.NOT_ACTIVE) {
                sendResultCode(VDVRRespondID.open_Parking_power_generation_1)
            } else {
                if (gearStatus == CarAvm.GBPositoionDisplay.DISPLAY_P) {
                    if (forceEVChargeStatus == CarNewEnergy.ForceChargeSwitchSts.OFF) {
                        carNewEnergyManager.forceEvChargeMode = CarNewEnergy.ForceChargeSwitchSts.ON
                        sendResultCode(VDVRRespondID.open_Parking_power_generation_2)
                    } else if (forceEVChargeStatus == CarNewEnergy.ForceChargeSwitchSts.ON) {
                        sendResultCode(VDVRRespondID.open_Parking_power_generation_3)
                    }
                } else {
                    sendResultCode(VDVRRespondID.open_Parking_power_generation_1)
                }
            }

        } else {
            if (forceEVChargeStatus == CarNewEnergy.ForceChargeSts.NOT_ACTIVE) {
                sendResultCode(VDVRRespondID.close_Parking_power_generation_1)
            } else {
                if (gearStatus == CarAvm.GBPositoionDisplay.DISPLAY_P) {
                    if (forceEVChargeStatus == CarNewEnergy.ForceChargeSwitchSts.ON) {
                        carNewEnergyManager.forceEvChargeMode =
                            CarNewEnergy.ForceChargeSwitchSts.OFF
                        sendResultCode(VDVRRespondID.close_Parking_power_generation_2)
                    } else if (forceEVChargeStatus == CarNewEnergy.ForceChargeSwitchSts.OFF) {
                        sendResultCode(VDVRRespondID.close_Parking_power_generation_3)
                    }
                } else {
                    sendResultCode(VDVRRespondID.open_Parking_power_generation_1)
                }

            }
        }

    }

    /**
     * 控制预约充电
     * @param voiceCommandState true-开启 false-关闭
     */
    fun setReserveCharge(voiceCommandState: Boolean) {
        val bookChargeStatus = carNewEnergyManager.bookChargeSwitch
        if (voiceCommandState) {
            when (bookChargeStatus) {
                CarNewEnergy.BookChargeSts.OFF -> {
                    carNewEnergyManager.bookChargeSwitch = CarNewEnergy.BookChargeSts.ON
                    sendResultCode(VDVRRespondID.open_scheduled_charging_1)
                }

                CarNewEnergy.BookChargeSts.ON -> {
                    sendResultCode(VDVRRespondID.open_scheduled_charging_2)
                }
                // TODO: 判断与xx开关互斥留空
            }

        } else {
            when (bookChargeStatus) {
                CarNewEnergy.BookChargeSts.OFF -> {
                    sendResultCode(VDVRRespondID.close_scheduled_charging_2)
                }

                CarNewEnergy.BookChargeSts.ON -> {
                    carNewEnergyManager.bookChargeSwitch = CarNewEnergy.BookChargeSts.OFF
                    sendResultCode(VDVRRespondID.close_scheduled_charging_1)
                }
                // TODO: 判断与xx开关互斥留空
            }
        }
    }

    /**
     * 预约充电时间时长周期循环
     * @param value
     */
    fun setReserveChargeTime(value: String) {
        val root = JSONObject(value)
        val time = LocalTime.parse(root.optString("time"))
        //预约充电时长
        val chargingTime = root.optInt("value")
        //预约充电的开始时间-小时
        val bookChargeTimeHour = time.hour
        //预约充电的开始时间-分钟
        val bookChargeTimeMinute = time.minute
        //判断预约时间是否重复
        if (bookChargeTimeHour == carNewEnergyManager.bookChargeTimeHour &&
            bookChargeTimeMinute == carNewEnergyManager.bookChargeTimeMinutes &&
            chargingTime == carNewEnergyManager.bookChargeDuration
        ) {
            sendResultCode(VDVRRespondID.reservation_charging_time_duration_cycle_2)
        } else {
            //设置预约充电请求
            carNewEnergyManager.bookChargeSwitch = CarNewEnergy.BookChargeReq.BOOK_SET
            //设置预约充电开始时间-小时
            carNewEnergyManager.setBookChargeStartTimeHour(bookChargeTimeHour)
            //设置预约充电开始时间-分钟
            carNewEnergyManager.setBookChargeStartTimeMinute(bookChargeTimeMinute)
            //设置预约充电时长
            carNewEnergyManager.bookChargeDuration = chargingTime
            sendResultCode(VDVRRespondID.reservation_charging_time_duration_cycle_1)
        }
    }

    /**
     * 控制预约出行
     * @param voiceCommandState true-开启 false-关闭
     */
    fun setReservationTrip(voiceCommandState: Boolean) {
        val svTravelStatus = carNewEnergyManager.rsvTravelSts
        if (voiceCommandState) {
            if (svTravelStatus == CarNewEnergy.BookTravelSts.OFF) {
                carNewEnergyManager.setRsvTravel(CarNewEnergy.BookTravelSts.ON)
                sendResultCode(VDVRRespondID.Book_a_trip_1)
            } else if (svTravelStatus == CarNewEnergy.BookTravelSts.ON) {
                sendResultCode(VDVRRespondID.Book_a_trip_2)
            }
        } else {
            if (svTravelStatus == CarNewEnergy.BookTravelSts.OFF) {
                sendResultCode(VDVRRespondID.Book_a_trip_5)
            } else if (svTravelStatus == CarNewEnergy.BookTravelSts.ON) {
                carNewEnergyManager.setRsvTravel(CarNewEnergy.BookTravelSts.OFF)
                sendResultCode(VDVRRespondID.Book_a_trip_4)
            }
        }

    }

    /**
     * 控制充电
     * @param value 0-开始充电 1-暂停充电 2-结束充电
     */
    fun controlCharge(value: String) {
        //获取充电状态
        val bmsChargeStatus = carNewEnergyManager.bmsChargeSts
        when (value) {
            "0" -> {
                sendResultCode(VDVRRespondID.start_charging_1)
            }

            "1" -> {
                sendResultCode(VDVRRespondID.pause_charging_1)
            }

            "2" -> {
                carNewEnergyManager.setStopCharge(1)
                sendResultCode(VDVRRespondID.end_charging_1)
            }
        }
    }

    /**
     * 设置充电上限
     * @param value 百分比(80-100)
     */
    fun setMaxCharge(value: String) {
        val percentage = value.toInt()
        val highThreshold = carNewEnergyManager.chargeSocThreshold
        if (percentage in 80..100) {
            if (percentage != highThreshold) {
                carNewEnergyManager.chargeSocThreshold = percentage
                sendResultCode(VDVRRespondID.Charging_upper_limit_value_2)
            } else {
                sendResultCode(VDVRRespondID.Charging_upper_limit_value_3)
            }
        } else {
            sendResultCode(VDVRRespondID.Charging_upper_limit_value_4)
        }
    }

    /**
     * 设置放电下限
     * @param value 百分比(30-100)
     */
    fun setMinCharge(value: String) {
        val percentage = value.toInt()
        //获取当前soc下限值
        val lowThreshold = carNewEnergyManager.getDischargeSocLowThreshold(context)
        // TODO: 判断已上电，打开功能界面

        if (percentage in 30..100) {
            if (percentage != lowThreshold) {
                carNewEnergyManager.setDischargeSocThreshold(context, percentage)
                sendResultCode(VDVRRespondID.Lower_limit_of_discharge_2)
            } else {
                sendResultCode(VDVRRespondID.Lower_limit_of_discharge_3)
            }
        } else {
            sendResultCode(VDVRRespondID.Lower_limit_of_discharge_4)
        }
    }
    //    打开应急放电
    fun openEmergencyDischarge(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_emergency_discharge_1)
    }
//    对外车辆快速供电开关
    fun externalPowerSupply(flag: Boolean) {
        if ( flag){
            sendResultCode(VDVRRespondID.open_fast_power_supply_external_vehicles_1)
        }
        else{
            sendResultCode(VDVRRespondID.close_fast_power_supply_external_vehicles_3)
        }
    }
//    对外车辆慢速供电开关
    fun externalDevicePowerSupply(flag: Boolean) {
        if ( flag){
            sendResultCode(VDVRRespondID.open_slow_power_supply_external_vehicles_1)
        }
        else{
            sendResultCode(VDVRRespondID.close_slow_power_supply_external_vehicles_3)
        }
    }
//    立即充电开关
    fun chargingSwitch(flag: Boolean) {
        if ( flag){
            sendResultCode("open_immediate_charging_3")
        }
        else{
            sendResultCode("close_immediate_charging_3")
        }
    }

//    充电模式开关
    fun setChargeMode(flag: Boolean) {
        if ( flag){
            sendResultCode(VDVRRespondID.open_charging_mode_1)
        }
    else{
            sendResultCode(VDVRRespondID.close_charging_mode_1)
        }
    }
//    设置充电保温结束时间
    fun reserveChargeTime(value: String) {
        sendResultCode(VDVRRespondID.set_charging_warm_end_time_3)
    }
//    电池温度预控加热提醒开关
    fun temperatureReminders(flag: Boolean){
        if ( flag){
            sendResultCode(VDVRRespondID.open_battery_temperature_preheating_reminder_1)
        }
        else{
            sendResultCode(VDVRRespondID.close_battery_temperature_preheating_reminder_1)
        }
    }
//    智能上电开关
    fun smartPower(flag: Boolean) {
        if ( flag){
            sendResultCode(VDVRRespondID.open_intelligent_power_on_1)
        }
    else{
            sendResultCode(VDVRRespondID.close_intelligent_power_on_1)
        }
    }
//    智能下电开关
    fun smartDischarge(flag: Boolean) {
        if ( flag){
            sendResultCode(VDVRRespondID.open_intelligent_power_off_1)
        }
        else{
            sendResultCode(VDVRRespondID.close_intelligent_power_off_1)
        }
    }

    /**
     * 调整SOC目标值
     * "SOC目标值，又名保持电量；
     * 设置范围20%-80%
     * 不支持档位调节，仅支持百分比设置"
     * @param value 百分比(20-80)
     */
    fun adjustSOC(value: String) {
        // TODO: 保电模式暂时不支持
        sendResultCode("adjust_soc_target_to_number_4")
    }

    /**
     * 控制能量流显示
     * @param voiceCommandState true-显示 false-隐藏
     */
    fun setEnergyFlowVisible(voiceCommandState: Boolean) {
        if (voiceCommandState) {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.NEW_ENERGY) {
                sendResultCode(VDVRRespondID.open_energy_flow_chart_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.NEW_ENERGY,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_energy_flow_chart_1)
            }
        } else {
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.NEW_ENERGY) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.NEW_ENERGY,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_energy_flow_chart_1)
            } else {
                sendResultCode(VDVRRespondID.close_energy_flow_chart_2)
            }
        }

    }

    /**
     * 控制能量信息显示
     * @param voiceCommandState true-显示 false-隐藏
     */
    fun setEnergyInfoVisible(voiceCommandState: Boolean) {
        if (voiceCommandState) {
            if (EnergyConsumptionListDialog.isShow) {
                sendResultCode(VDVRRespondID.open_energy_information_2)
            } else {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    ConditionFragment.ENERGY_CONSUMPTION_LIST_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode(VDVRRespondID.open_energy_information_1)
            }

        } else {
            if (EnergyConsumptionListDialog.isShow) {
                DialogNavigationUtils.launchMainActivity(
                    context,
                    MainActivity.MainTabIndex.CONDITION,
                    ConditionFragment.ENERGY_CONSUMPTION_LIST_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode(VDVRRespondID.close_energy_information_2)
            } else {
                sendResultCode(VDVRRespondID.close_energy_information_1)
            }
        }
        //            val intent = Intent(MyApplication.getContext(), MainActivity::class.java).apply {
//                putExtra(CommonConst.TARGET_TAB, MainActivity.MainTabIndex.CONDITION)
//                putExtra(CommonConst.TARGET_DIALOG, 1)
//                putExtra(CommonConst.OPERATION, 0)
//                flags = Intent.FLAG_ACTIVITY_NEW_TASK
//            }
//            MyApplication.getContext().startActivity(intent)
    }

    /**
     * 打开or关闭充电枪防盗
     * @param flag true:打开 false:关闭
     */
    fun chargingCableBurglar(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_charging_gun_anti_theft_mode_1)
        } else {
            sendResultCode(VDVRRespondID.close_charging_gun_anti_theft_mode_1)
        }
    }

    /**
     * 打开or关闭用电保持
     * @param flag true:打开 false:关闭
     */
    fun openOrcloseHoldPower(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_power_retention_1)
        } else {
            sendResultCode(VDVRRespondID.close_power_retention_1)
        }
    }

    /**
     * 打开or关闭整车电源
     * @param flag true:打开 false:关闭
     */
    fun openOrcloseVehiclePowerSupply(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_vehicle_power_switch_1)
        } else {
            sendResultCode(VDVRRespondID.close_vehicle_power_switch_1)
        }
    }

    /**
     * SOC目标值(保电模式)调大具体数值
     */
    fun adjustSOCValueUp(value: String) {
        if (value != ""){
            if (mDrivingManager?.driveMode == DRIVE_PERSON_MODE) {
                if (PersonalizedSettingUIAlert.isShow) {
                    sendResultCode(VDVRRespondID.raise_soc_target_value_by_number_2)
                } else {
                    //打开个性化弹窗
                    DialogNavigationUtils.launchMainActivity(
                        context,
                        MainActivity.MainTabIndex.DRIVE,
                        DrivingFragment.DRIVE_PERSONALIZE_DIALOG,
                        CommonConst.DIALOG_OPEN
                    )
                    sendResultCode(VDVRRespondID.raise_soc_target_value_by_number_2)
                }
            }else{
                //todo 回复个性化模式还未开启
                sendResultCode("raise_soc_target_value_by_number_2")
            }
        }
    }


    // 单回复提示语id
    private fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    private fun sendResultCode(respondId: String, mValue: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    // 特殊提示语id
    private fun sendResultCode(respondId: String, mValue: String, mUnique: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        param.unique = mUnique
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }


    /**
     * 获取续航工况显示方式的设置（CLTC/WLTC/动态）
     * @param value 0:CLTC 1:WLTC 5:动态
     */
    /*设置续航工况为指定模式*/
    fun setEnduranceMode(value: String){
        when (value.toInt()) {
            AR_PARAM_CLTC -> {
                if (carNewEnergyManager.rangeConditionDisplay == CarSettingConstant.SIGNAL_CLTC) {
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_3)
                }else {
                    carNewEnergyManager.rangeConditionDisplay = CarSettingConstant.SIGNAL_CLTC;
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_2)
                }
            }

            AR_PARAM_WLTC -> {
                if (carNewEnergyManager.rangeConditionDisplay == CarSettingConstant.SIGNAL_WLTC) {
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_3)
                } else {
                    carNewEnergyManager.rangeConditionDisplay = CarSettingConstant.SIGNAL_WLTC;
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_2)
                }
            }

            AR_PARAM_DYNAMICS -> {
                if (carNewEnergyManager.rangeConditionDisplay == CarSettingConstant.SIGNAL_DYNAMICS) {
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_3)
                }else {
                    carNewEnergyManager.rangeConditionDisplay = CarSettingConstant.SIGNAL_DYNAMICS;
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_2)
                }
            }

            else -> {
                if (carNewEnergyManager.rangeConditionDisplay == CarSettingConstant.SIGNAL_DYNAMICS) {
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_3)
                } else {
                    carNewEnergyManager.rangeConditionDisplay = CarSettingConstant.SIGNAL_DYNAMICS;
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_2)
                }
            }
        }
    }

    /**
     * 切换续航工况显示模式无指定值
     * @param flag true:切换工况显示模式
     */
    fun switchEnduranceDisplay(flag: Boolean){
        val random: Int = (Random.nextInt(3))%3;
        if(flag){
            when(random){
                CarSettingConstant.SIGNAL_DYNAMICS -> {
                    carNewEnergyManager.rangeConditionDisplay = CarSettingConstant.SIGNAL_DYNAMICS
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_5)
                }

                CarSettingConstant.SIGNAL_CLTC -> {
                    carNewEnergyManager.rangeConditionDisplay = CarSettingConstant.SIGNAL_CLTC
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_5)
                }
                else -> {
                    carNewEnergyManager.rangeConditionDisplay = CarSettingConstant.SIGNAL_WLTC
                    sendResultCode(VDVRRespondID.Set_range_condition_display_mode_the_specified_mode_5)
                }
            }
        }
    }

    /**
     * SOC目标值调小具体数值
     */
    fun adjustDownSOC(value: String) {
        DialogNavigationUtils.launchMainActivity(
            context,
            MainActivity.MainTabIndex.NEW_ENERGY,
            CommonConst.INVALID_DIALOG,
            CommonConst.DIALOG_OPEN
        )
        sendResultCode(VDVRRespondID.raise_soc_target_value_by_number_2)
    }

    /**
     * SOC目标值调最大/最小
     */
    fun adjustModeSOC(value: String) {
        DialogNavigationUtils.launchMainActivity(
            context,
            MainActivity.MainTabIndex.NEW_ENERGY,
            CommonConst.INVALID_DIALOG,
            CommonConst.DIALOG_OPEN
        )
        sendResultCode("adjust_soc_target_to_max_2")
    }

    /**
     * SOC设置为指定模式
     */
    fun setSOCMode(value: String) {
        sendResultCode(VDVRRespondID.set_soc_mode)
    }

    /**
     * 充电曲线开关
     */
    fun setChargeCurve(isOpen: Boolean) {
        if (isOpen) {
            sendResultCode("open_charging_curve_4")
        } else {
            sendResultCode("close_charging_curve_3")
        }
    }

    /**
     * 能耗曲线开关
     */
    fun setEnergyCurve(isOpen: Boolean) {
        if (isOpen) {
            sendResultCode("open_energy_consumption_curve_3")
        } else {
            sendResultCode("close_energy_consumption_curve_3")
        }
    }

    /**
     * 电耗曲线开关
     */
    fun setElectricityCurve(isOpen: Boolean) {
        if (isOpen) {
            sendResultCode("open_power_consumption_curve_3")
        }
    }

    fun setOilCurve(isOpen: Boolean) {
        if (isOpen) {
            sendResultCode("open_fuel_consumption_curve_4")
        }
    }

    /**
     * 查询累计里程
     */
    fun queryTotalMileage(){
        var value = carNewEnergyManager.totalDriveDistance
        sendResultCode(VDVRRespondID.vehicleInfo_15,value.toString())
    }

    /**
     * 提取校验函数：判断数值是否有效
     */
    private fun isValueValid(value: Float): Boolean {
        return value >= 0 && value != Float.MIN_VALUE
    }

    /**
     * 处理单个参数：有效则用原值，无效则用0
     */
    private fun getValidValue(value: Float): String {
        return if (isValueValid(value)) value.toString() else "0"
    }

    /**
     * 查询本次行程信息
     */
    fun queryThisTripInfo(){
        val speed = carNewEnergyManager.avgSpeedStartup
        val fuel = carNewEnergyManager.energyFuelPer50Km
        val power = carNewEnergyManager.avgPowerConsumptionStartup
        // 分别处理每个参数的有效性
        val validSpeed = getValidValue(speed)
        val validFuel = getValidValue(fuel)
        val validPower = getValidValue(power)
        // 直接拼接结果
        sendResultCode(VDVRRespondID.vehicleInfo_16, "${validSpeed}-${validFuel}-${validPower}")
    }

    companion object {
        const val TAG = "NewEnergyControl"
        const val AR_PARAM_CLTC: Int = 0
        const val AR_PARAM_WLTC: Int = 1
        const val AR_PARAM_DYNAMICS: Int = 5
        const val DRIVE_PERSON_MODE: Int = 6

        //0:总⾥程
        const val TOTAL_MODE: Int = 0

        //1:燃油⾥程
        const val OIL_MODE: Int = 1

        //2:纯电⾥程
        const val PURE_ELECTRIC_MODE: Int = 2

        //总⾥程
        const val TOTAL_MODE_TEXT: String = "总里程"

        //燃油⾥程
        const val OIL_MODE_TEXT: String = "燃油里程"

        //纯电⾥程
        const val PURE_ELECTRIC_MODE_TEXT: String = "纯电里程"

        //0:电量百分比
        const val BATTERY_PERCENTAGE: Int = 0

        //1:电续航里程
        const val ELECTRIC_LIFESPAN_MILEAGE: Int = 1

        //电量百分比
        const val BATTERY_PERCENTAGE_TEXT: String = "电量百分比"

        //电续航里程
        const val ELECTRIC_LIFESPAN_MILEAGE_TEXT: String = "电续航里程"
    }

}