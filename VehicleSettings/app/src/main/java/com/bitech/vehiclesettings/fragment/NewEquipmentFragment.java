package com.bitech.vehiclesettings.fragment;

import android.graphics.Matrix;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.recyclerview.widget.GridLayoutManager;

import com.bitech.vehiclesettings.adapter.EquipmentColorAdapter;
import com.bitech.vehiclesettings.adapter.EquipmentHubAdapter;
import com.bitech.vehiclesettings.databinding.FragmentNewEquipmentBinding;

import java.util.List;

public class NewEquipmentFragment extends BaseFragment<FragmentNewEquipmentBinding> {
    private static final String TAG = NewEquipmentFragment.class.getSimpleName();

    private FragmentNewEquipmentBinding binding; // ViewBinding
    private EquipmentColorAdapter adapter;
    private List<Integer> colorList;

    @Override
    protected FragmentNewEquipmentBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentNewEquipmentBinding.inflate(inflater, container, false);
        return binding;
    }

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null) return;
        loadPageAnim(binding.scrollView, currentPosition, position);
    }

    @Override
    protected void initView() {
        initData();
        setupRecyclerView();
        setupHubRecyclerView();
//        binding.ivTestCar.post(() -> {
//            // 获取 ImageView 和图片的尺寸
//            int viewWidth = binding.ivTestCar.getWidth();
//            int viewHeight = binding.ivTestCar.getHeight();
//            Drawable drawable = binding.ivTestCar.getDrawable();
//            if (drawable == null) return;
//
//            int drawableWidth = drawable.getIntrinsicWidth();
//            int drawableHeight = drawable.getIntrinsicHeight();
//
//            // 计算缩放比例（例如放大 1.5 倍）
//            float scale = .5f;
//
//            // 创建缩放矩阵
//            Matrix matrix = new Matrix();
//            matrix.postScale(scale, scale);
//
//            // 将图片中心对齐 ImageView
//            float dx = (viewWidth - drawableWidth * scale) / 2;
//            float dy = (viewHeight - drawableHeight * scale) / 2;
//            matrix.postTranslate(dx, dy);
//
//            // 应用矩阵
//            binding.ivTestCar.setImageMatrix(matrix);
//        });
    }

    @Override
    protected void setListener() {
        if (adapter != null) {
            adapter.setOnItemClickListener(position -> {
            });
        }
    }

    @Override
    protected void initObserve() {

    }

    protected void initData() {
    }

    private void setupRecyclerView() {
        adapter = new EquipmentColorAdapter();

        // 使用 GridLayoutManager，设置 5 列
        binding.rvNewEquipmentRecyclerViewColors.setLayoutManager(new GridLayoutManager(getContext(), 5));
        binding.rvNewEquipmentRecyclerViewColors.setAdapter(adapter);
    }

    private void setupHubRecyclerView() {
        EquipmentHubAdapter hubAdapter = new EquipmentHubAdapter();
        binding.rvNewEquipmentRecyclerViewHubs.setLayoutManager(new GridLayoutManager(getContext(), 5));
        binding.rvNewEquipmentRecyclerViewHubs.setAdapter(hubAdapter);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (binding != null) {
            binding = null; // 释放 ViewBinding，防止内存泄漏
        }
    }
}
