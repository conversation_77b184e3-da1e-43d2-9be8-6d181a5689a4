package com.bitech.vehiclesettings.utils

import android.app.UiModeManager
import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R

/**
 * @Description: 文字操作工具类.
 **/
object TextUtil {

    // 日志标志位
    private const val TAG = "TextUtil"

    // 获取显示模式管理对象
    private val uiManager =
        MyApplication.getContext().getSystemService(Context.UI_MODE_SERVICE) as UiModeManager

    /**
     * 给文言中关键词上色.
     *
     * @param text 待渲染颜色文言
     * @param keyword 关键词
     * @return 上色文言
     */
    fun spannableString(text: String, keyword: String): SpannableString {
        // 存在关键词，给关键词上色
        val spannableString = SpannableString(text)
        // 关键词起始位置
        val startIndex = text.indexOf(keyword, 0, true)
        // 关键词结束位置
        val endIndex = startIndex + keyword.length
        // 获取渲染颜色
        val color = if (uiManager.nightMode == UiModeManager.MODE_NIGHT_YES) {
            // 黑夜模式
            ContextCompat.getColor(MyApplication.getContext(), R.color.color_2B7ACE)
        } else {
            // 白天模式
            ContextCompat.getColor(MyApplication.getContext(), R.color.color_3292F5)
        }
        // 关键词颜色渲染
        spannableString.setSpan(
            ForegroundColorSpan(color),
            startIndex,
            endIndex,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return spannableString
    }

    /**
     * 给文言设置为指定颜色.
     *
     * @param text textView文言
     * @param color 颜色
     * @return 上色文言
     */
    fun spannableString(text: String, color: Int): SpannableString {
        // 构建SpannableString对象
        val spannableString = SpannableString(text)
        // 给text进行颜色渲染
        spannableString.setSpan(
            ForegroundColorSpan(color),
            0,
            spannableString.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return spannableString
    }

    /**
     * 获取页面对应的所有TextView控件列表.
     *
     * @param view 页面视图
     * @return 控件列表
     */
    fun getAllTextViewInFragment(view: View): MutableList<TextView> {
        val textViews = mutableListOf<TextView>()
        if (view is ViewGroup) {
            // 当前是视图组，则再次进行遍历
            for (i in 0 until view.childCount) {
                val childView = view.getChildAt(i)
                textViews.addAll(getAllTextViewInFragment(childView))
            }
        } else if (view is TextView) {
            // 当前View是TextView，则添加到列表
            textViews.add(view)
        }
        return textViews
    }

    /**
     * 计算View到屏幕顶部的距离.
     *
     * @param view view
     * @return 到屏幕顶部的距离
     */
    fun viewToTopScreenDistance(view: View): Int {
        LogUtil.d(TAG, "viewToTopScreenDistance : view = ${view.id}")
        val intArray = IntArray(2)
        view.getLocationOnScreen(intArray)
        LogUtil.d(TAG, "viewToTopScreenDistance : distance = ${intArray[1]}")
        return intArray[1]
    }
}
