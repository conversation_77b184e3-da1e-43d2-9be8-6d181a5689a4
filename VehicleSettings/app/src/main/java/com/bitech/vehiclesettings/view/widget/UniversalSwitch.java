package com.bitech.vehiclesettings.view.widget;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.appcompat.widget.SwitchCompat;

import com.bitech.vehiclesettings.R;

public class UniversalSwitch extends SwitchCompat {
    private boolean isUserTouching = false;
    private OnUniversalSwitchChangeListener listener;

    public interface OnUniversalSwitchChangeListener {
        void onSwitchChangedByUser(UniversalSwitch switchView, boolean isChecked);
    }

    public UniversalSwitch(Context context) {
        super(context);
        init(null);
    }

    public UniversalSwitch(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public UniversalSwitch(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        // 读取XML中定义的属性
        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.UniversalSwitch);

            // 读取并设置背景
            if (a.hasValue(R.styleable.UniversalSwitch_android_background)) {
                setBackground(a.getDrawable(R.styleable.UniversalSwitch_android_background));
            }

            // 读取并设置thumb和track
            if (a.hasValue(R.styleable.UniversalSwitch_android_thumb)) {
                setThumbDrawable(a.getDrawable(R.styleable.UniversalSwitch_android_thumb));
            }

            if (a.hasValue(R.styleable.UniversalSwitch_android_track)) {
                setTrackDrawable(a.getDrawable(R.styleable.UniversalSwitch_android_track));
            }

            // 读取并设置最小宽度
            if (a.hasValue(R.styleable.UniversalSwitch_android_switchMinWidth)) {
                setSwitchMinWidth(a.getDimensionPixelSize(
                        R.styleable.UniversalSwitch_android_switchMinWidth,
                        getSwitchMinWidth()
                ));
            }

            // 读取并设置初始状态
            if (a.hasValue(R.styleable.UniversalSwitch_android_checked)) {
                setChecked(a.getBoolean(R.styleable.UniversalSwitch_android_checked, false));
            }

            a.recycle();
        }




        super.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                isUserTouching = true;
            }
            return false;
        });

        super.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isUserTouching) {
                isUserTouching = false;
                if (listener != null) {
                    listener.onSwitchChangedByUser(this, isChecked);
                }
            }
        });
    }

    public void setOnUniversalSwitchChangeListener(OnUniversalSwitchChangeListener listener) {
        this.listener = listener;
    }

    // 防止外部覆盖监听器
    @Override
    public void setOnCheckedChangeListener(OnCheckedChangeListener listener) {
        throw new UnsupportedOperationException("Use setOnUniversalSwitchChangeListener instead");
    }
}