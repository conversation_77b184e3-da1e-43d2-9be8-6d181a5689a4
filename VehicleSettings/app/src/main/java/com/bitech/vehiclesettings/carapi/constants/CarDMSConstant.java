package com.bitech.vehiclesettings.carapi.constants;

public class CarDMSConstant {
    // 安全驾驶 开关
    public static final String KEY_SAFETY_FATIGUE_STATUS = "key_safety_fatigue_status"; // 安全驾驶-疲劳缓解开关
    public static final String KEY_SAFETY_DISTRACTION_DRIVER_STATUS = "key_safety_distraction_driver_status"; // 安全驾驶-驾驶分心开关
    public static final String KEY_SAFETY_DISTRACTION_DRINK_STATUS = "key_safety_distraction_drink_status"; // 安全驾驶-喝水分心开关
    public static final String KEY_SAFETY_DISTRACTION_CALLS_STATUS = "key_safety_distraction_calls_status"; // 安全驾驶-打电话分心开关


    // 主动关怀 开关
    public static final String KEY_CARE_SMOKE_STATUS = "key_care_smoke_status"; // 主动关怀-抽烟模式开关
    public static final String KEY_CARE_SAY_HELLO_STATUS = "key_care_say_hello_status"; // 主动关怀-个性问候开关
    public static final String KEY_CARE_MOOD_PLAYLIST_STATUS = "key_care_mood_playlist_status"; // 主动关怀-情绪歌单开关
    public static final String KEY_CARE_MOOD_CAPTURE_STATUS = "key_care_mood_capture_status"; // 主动关怀-情绪抓拍开关 oms预留
    public static final String KEY_CARE_SLEEP_CARE_STATUS = "key_care_sleep_care_status"; // 主动关怀-睡眠关怀开关

    // 智能车控 开关
    public static final String KEY_CTRL_SCREEN_ON_STATUS = "key_ctrl_screen_on_status"; // 智能车控-视线亮屏开关
    public static final String KEY_CTRL_SEAT_HEAT_STATUS = "key_ctrl_seat_heat_status"; // 智能车控-智能座椅加热开关
    public static final String KEY_CTRL_SEAT_VENTILATION_STATUS = "key_ctrl_seat_ventilation_status"; // 智能车控-智能座椅通风开关
    public static final String KEY_CTRL_SMART_VOLUME_STATUS = "key_ctrl_smart_volume_status"; // 智能车控-智能音量(副驾打电话关怀)开关 oms预留
    public static final String KEY_CTRL_GESTURE_STATUS = "key_ctrl_gesture_status"; // 智能车控-⼿势识别开关 oms预留
    public static final String KEY_CTRL_SMART_SOUND_FIELD_STATUS = "key_ctrl_smart_sound_field_status"; // 智能车控-智能音场开关 oms预留

    public static final String KEY_GESTURE_HEART = "key_gesture_heart"; // 比心手势设置项
    public static final String KEY_GESTURE_LIKE = "key_gesture_like"; // 点赞手势设置项
/**
        - 以下是开关控制
- 开 1, 关 0
    //
    public static final int STATUS_OFF = 0;
    public static final int STATUS_ON = 1;

*//**
        - 以下是三种预设手势响应
- 导航回家 0, 导航去公司 1, 收藏当前歌曲 2
    //
    public static final int GESTURE_GO_HOME = 0;
    public static final int GESTURE_GO_COMPANY = 1;
    public static final int GESTURE_GO_FAVOURITE = 2;*/
}
