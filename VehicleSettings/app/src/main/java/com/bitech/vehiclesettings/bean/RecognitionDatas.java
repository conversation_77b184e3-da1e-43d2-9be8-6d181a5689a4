package com.bitech.vehiclesettings.bean;

public class RecognitionDatas extends BaseData{
    private String tag;

    /**
     * DMS摄像头
     */
    private boolean cameraFlag = false;
    /**
     * 疲劳检测
     */
    private boolean fatigueFlag = true;
    /**
     * 视线分心
     */
    private boolean distraction = true;
    /**
     * 打电话
     */
    private boolean callFlag = true;
    /**
     * 喝水
     */
    private boolean drinkFlag = true;
    /**
     * 座椅加热
     */
    private boolean seatHeatFlag = true;
    /**
     * 座椅通风
     */
    private boolean seatVentilationFlag = true;
    /**
     * 视线解锁
     */
    private boolean sightUnlockFlag = true;
    /**
     * 个性化问候
     */
    private boolean greetFlag = true;
    /**
     * 抽烟关怀
     */
    private boolean smokeFlag = true;

    @Override
    public String getTag() {
        return tag;
    }

    @Override
    public void setTag(String tag) {
        this.tag = tag;
    }

    public boolean isCameraFlag() {
        return cameraFlag;
    }

    public void setCameraFlag(boolean cameraFlag) {
        this.cameraFlag = cameraFlag;
    }

    public boolean isFatigueFlag() {
        return fatigueFlag;
    }

    public void setFatigueFlag(boolean fatigueFlag) {
        this.fatigueFlag = fatigueFlag;
    }

    public boolean isDistraction() {
        return distraction;
    }

    public void setDistraction(boolean distraction) {
        this.distraction = distraction;
    }

    public boolean isCallFlag() {
        return callFlag;
    }

    public void setCallFlag(boolean callFlag) {
        this.callFlag = callFlag;
    }

    public boolean isDrinkFlag() {
        return drinkFlag;
    }

    public void setDrinkFlag(boolean drinkFlag) {
        this.drinkFlag = drinkFlag;
    }

    public boolean isSeatHeatFlag() {
        return seatHeatFlag;
    }

    public void setSeatHeatFlag(boolean seatHeatFlag) {
        this.seatHeatFlag = seatHeatFlag;
    }

    public boolean isSeatVentilationFlag() {
        return seatVentilationFlag;
    }

    public void setSeatVentilationFlag(boolean seatVentilationFlag) {
        this.seatVentilationFlag = seatVentilationFlag;
    }

    public boolean isSightUnlockFlag() {
        return sightUnlockFlag;
    }

    public void setSightUnlockFlag(boolean sightUnlockFlag) {
        this.sightUnlockFlag = sightUnlockFlag;
    }

    public boolean isGreetFlag() {
        return greetFlag;
    }

    public void setGreetFlag(boolean greetFlag) {
        this.greetFlag = greetFlag;
    }

    public boolean isSmokeFlag() {
        return smokeFlag;
    }

    public void setSmokeFlag(boolean smokeFlag) {
        this.smokeFlag = smokeFlag;
    }
}
