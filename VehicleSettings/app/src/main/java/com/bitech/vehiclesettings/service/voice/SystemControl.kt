package com.bitech.vehiclesettings.service.voice

import android.app.ActivityManagerNative
import android.content.Context
import android.content.Intent
import android.os.RemoteException
import android.util.Log
import com.android.internal.app.LocalePicker
import com.bitech.platformlib.bean.Topics
import com.bitech.platformlib.manager.SystemManager
import com.bitech.platformlib.utils.MsgUtil
import com.bitech.vehiclesettings.bean.GlobalVar
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.presenter.system.SystemPresenter
import com.bitech.vehiclesettings.service.voice.SystemControl.SystemConstant.ACTION_VOICE_BCALL
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P
import java.util.Locale


internal class SystemControl(private val mContext: Context) {
    private var mSystemManager: SystemManager? = SystemManager.getInstance();
    private val TAG: String = "SystemControl"
    private val mSystemPresenter: SystemPresenter<Any> = SystemPresenter.getInstance()

    //单回复提示语id
    private fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }
    private fun sendResultCode(respondId: String, mValue: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }
    //特殊提示语id
    private fun sendResultCode(respondId: String, mValue: String, mUnique: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        param.unique = mUnique
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    /**
     * 设置系统时制
     * @param value 12:12小时制 24:24小时制
     */
    fun setTime(value: String){
        if(value!= ""){
            if (value.toInt() == SystemConstant.TIME_12) {
                if (mSystemPresenter.getTimeDisplay(mContext)) {
                    mSystemPresenter.setTimeDisplay(CarSettingConstant.TIME_DISPLAY_12, mContext)
                    sendResultCode(VDVRRespondID.set_system_tense_1,"12小时制")
                }else{
                    sendResultCode(VDVRRespondID.set_system_tense_2,"12小时制")
                }
            }
            if (value.toInt() == SystemConstant.TIME_24) {
                if (!mSystemPresenter.getTimeDisplay(mContext)) {
                    mSystemPresenter.setTimeDisplay(CarSettingConstant.TIME_DISPLAY_24, mContext)
                    sendResultCode(VDVRRespondID.set_system_tense_1,"24小时制")
                }else {
                    sendResultCode(VDVRRespondID.set_system_tense_2,"24小时制")
                }
            }
        }

    }

    /**
     * 打开or关闭自动校准日期时间
     * @param flag true:打开 false:关闭
     */
    fun open0rcloseCalibrationTime(flag: Boolean) {
        if (flag) {
            if (mSystemPresenter.getAutoCalibration() == CarSettingConstant.AUTO_CALIBRATION_FALSE) {
                mSystemPresenter.setAutoCalibration(CarSettingConstant.AUTO_CALIBRATION_TRUE)
                sendResultCode(VDVRRespondID.open_time_synchronization_1)
            } else {
                sendResultCode(VDVRRespondID.open_time_synchronization_2)
            }
        } else {
            if (mSystemPresenter.getAutoCalibration() == CarSettingConstant.AUTO_CALIBRATION_TRUE) {
                mSystemPresenter.setAutoCalibration(CarSettingConstant.AUTO_CALIBRATION_FALSE)
                sendResultCode(VDVRRespondID.close_time_synchronization_1)
            }else{
                sendResultCode(VDVRRespondID.close_time_synchronization_2)
            }
        }
    }

    /**
     * 更新系统语言
     */
    private fun updateLanguage(locale: Locale) {
        try {
            val config = ActivityManagerNative.getDefault().configuration
            config.locale = locale
            LocalePicker.updateLocale(config.locale)
            GlobalVar.setIsSetSystem(true)
        } catch (exception: RemoteException) {
            Log.e(TAG, "setSystemLanguage : exception = " + exception.message)
            exception.printStackTrace()
        }
    }

    /**
     * 设置系统语言
     * @param value 0:中文 1:英文
     */
    fun setSystemLanguage(value: String){
        // 获取当前语言
        var currentLocale = mContext.getResources().getConfiguration().getLocales().get(0);
        when (value.toInt()) {
            SystemConstant.LANGUAGE_CHINESE -> {
                if (Locale.ENGLISH.language == currentLocale.language) {
                    updateLanguage(Locale.SIMPLIFIED_CHINESE)
                    sendResultCode(VDVRRespondID.language_settings_1,"中文")
                }else if(Locale.SIMPLIFIED_CHINESE.language == currentLocale.language){
                    sendResultCode(VDVRRespondID.language_settings_2,"中文")
                }
            }

            SystemConstant.LANGUAGE_ENGLISH -> {
                if (Locale.SIMPLIFIED_CHINESE.language == currentLocale.language) {
                    updateLanguage(Locale.ENGLISH)
                    sendResultCode(VDVRRespondID.language_settings_1,"英文")
                }else if(Locale.ENGLISH.language == currentLocale.language){
                    sendResultCode(VDVRRespondID.language_settings_2,"英文")
                }
            }

            SystemConstant.LANGUAGE_DEFAULT -> {
                if (Locale.SIMPLIFIED_CHINESE.language == currentLocale.language) {
                    sendResultCode(VDVRRespondID.language_settings_2,"中文")
                }else if(Locale.ENGLISH.language == currentLocale.language){
                    updateLanguage(Locale.SIMPLIFIED_CHINESE)
                    sendResultCode(VDVRRespondID.language_settings_1,"中文")
                }
            }
            else -> {
                sendResultCode(VDVRRespondID.language_settings_3);
            }
        }
    }

    /**
     * 设置油耗显示单位
     * @param value 0:千米每升 1:升每百公里
     */
    fun  setFuelDisplayUnit(value: String){
        // FLZCU_9_PowerMode
        //0x0:Off
        //0x1:Comfortable
        //0x2:ON
        //0x3:Reserved"
        when (value.toInt()) {
            //千米每升
            SystemConstant.FUEL_UNIT_KML -> {
                if (mSystemManager?.getAverageFuelUnit() == CarSettingConstant.FUEL_UNIT_L100KM) {
                    //判断是否处于ON模式
                    if (MsgUtil.getInstance()
                            .getSignlVal(Topics.Power.POWER_MODE) != SystemConstant.POWER_MODE_ON
                    ) {
                        sendResultCode(VDVRRespondID.set_capacity_display_unit_4)
                    }else {
                        mSystemManager?.setAveragePowerUnit(CarSettingConstant.FUEL_UNIT_KML)
                        sendResultCode(VDVRRespondID.set_capacity_display_unit_1,"千米每升")
                    }
                } else if (mSystemManager?.getAverageFuelUnit() == CarSettingConstant.FUEL_UNIT_KML) {
                    sendResultCode(VDVRRespondID.set_capacity_display_unit_2,"千米每升")
                }else{
                    sendResultCode(VDVRRespondID.set_capacity_display_unit_3)
                }
            }
            //升每百公里
            SystemConstant.FUEL_UNIT_L100KM -> {
                if (mSystemManager?.getAverageFuelUnit() == CarSettingConstant.FUEL_UNIT_KML) {
                    //判断是否处于ON模式
                    if (MsgUtil.getInstance()
                            .getSignlVal(Topics.Power.POWER_MODE) != SystemConstant.POWER_MODE_ON
                    ) {
                        sendResultCode(VDVRRespondID.set_capacity_display_unit_4)
                    }else {
                        mSystemManager?.setAveragePowerUnit(CarSettingConstant.FUEL_UNIT_L100KM)
                        sendResultCode(VDVRRespondID.set_capacity_display_unit_1,"升每百公里")
                    }
                } else if (mSystemManager?.getAverageFuelUnit() == CarSettingConstant.FUEL_UNIT_L100KM) {
                    sendResultCode(VDVRRespondID.set_capacity_display_unit_2,"升每百公里")
                }else{
                    sendResultCode(VDVRRespondID.set_capacity_display_unit_3)
                }
            }

            else -> {
                sendResultCode(VDVRRespondID.set_capacity_display_unit_3)
            }
        }
    }

    fun setBCall() {
        val intent: Intent = Intent(ACTION_VOICE_BCALL)
        mContext.sendBroadcast(intent)
    }

    object SystemConstant {
        //时间格式
        const val TIME_12: Int = 12;
        const val TIME_24: Int = 24;

        //中文
        const val LANGUAGE_CHINESE: Int = 0;

        //英文
        const val LANGUAGE_ENGLISH: Int = 1;

        //默认语言
        const val LANGUAGE_DEFAULT: Int = 2;

        //油耗显示单位
        const val FUEL_UNIT_KML: Int = 1;
        const val FUEL_UNIT_L100KM: Int = 0;

        //电源模式为ON档
        const val POWER_MODE_ON: Int = 2;
        const val ACTION_VOICE_BCALL: String = "ACTION_VOICE_BCALL"
    }
}


