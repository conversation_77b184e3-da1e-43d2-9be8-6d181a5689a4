package com.bitech.vehiclesettings.view.quickcontrol;

import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.driving.IDrivingManagerListener;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.databinding.DialogBtRegularwashBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class RegularWashUIAlert extends BaseDialog {
    private static final String TAG = RegularWashUIAlert.class.getSimpleName();
    private static RegularWashUIAlert.onProgressChangedListener onProgressChangedListener;

    public RegularWashUIAlert(@NonNull Context context) {
        super(context);
    }

    public RegularWashUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected RegularWashUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static RegularWashUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(RegularWashUIAlert.onProgressChangedListener onProgressChangedListener) {
        RegularWashUIAlert.onProgressChangedListener = onProgressChangedListener;
    }


    private Builder builderRef;

    public void setBuilderRef(Builder builder) {
        this.builderRef = builder;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogBtRegularwashBinding binding;
        boolean globalAlert = false;
        private final Handler mainHandler = new Handler(Looper.getMainLooper());


        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private RegularWashUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }
        public void setGlobalAlert(boolean b){
            globalAlert = b;
        }
        public RegularWashUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setTitle(int title) {
            binding.tvTitle1.setText(title == 0 ? context.getString(R.string.regular_car_wash) : context.getString(R.string.conveyor_car_wash));
        }

        private final DrivingManager drivingManager = (DrivingManager) BitechCar.getInstance()
                .getServiceManager(BitechCar.CAR_DRIVING_MANAGER);

        // 档位
        private Integer gearPositionInfo = CarDriving.VCU_PRNDGearAct.INIT;

        // 四个车窗
        private Integer leftFrontCarWindow = 0;
        private Integer rightFrontCarWindow = 0;
        private Integer leftRearCarWindow = 0;
        private Integer rightRearCarWindow = 0;
        // 车速
        private Integer vehicleSpeed = 0;
        // 洗车模式状态
        private Integer carWashModeStatus = 0;
        // 后视镜状态
        private Integer rearMirrorStatus = 0;

        public void addCallback() {
            drivingManager.addCallback(TAG, new IDrivingManagerListener() {
                /**
                 * 洗车模式状态
                 */
                @Override
                public void onCarWashModeStatusChanged(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    carWashModeStatus = signalVal;
                    mainHandler.post(() -> {
                        updateCarModeStatus();
                    });
                }

                /**
                 * 车窗 - 左前
                 */
                @Override
                public void onLeftFrontWindowStateChanged(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    if (signalVal >= 0 && signalVal <= 100)
                        leftFrontCarWindow = signalVal;
                    else
                        leftFrontCarWindow = 0;
                    mainHandler.post(() -> {
                        updateCarWindow();
                    });
                }

                /**
                 * 车窗 - 右前
                 */
                @Override
                public void onRightFrontWindowStateChanged(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    if (signalVal >= 0 && signalVal <= 100)
                        rightFrontCarWindow = signalVal;
                    else
                        rightFrontCarWindow = 0;
                    mainHandler.post(() -> {
                        updateCarWindow();
                    });
                }

                /**
                 * 车窗 - 左后
                 */
                @Override
                public void onLeftRearWindowStateChanged(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    if (signalVal >= 0 && signalVal <= 100)
                        leftRearCarWindow = signalVal;
                    else
                        leftRearCarWindow = 0;
                    mainHandler.post(() -> {
                        updateCarWindow();
                    });
                }

                /**
                 * 车窗 - 右后
                 */
                @Override
                public void onRightRearWindowStateChanged(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    if (signalVal >= 0 && signalVal <= 100)
                        rightRearCarWindow = signalVal;
                    else
                        rightRearCarWindow = 0;
                    mainHandler.post(() -> {
                        updateCarWindow();
                    });
                }

                /**
                 * 洗车模式 - 自动重上锁
                 */
                @Override
                public void onAutoReLockInhibit(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    mainHandler.post(() -> {
                        updateAutoCloseLock(signalVal);
                    });
                }

                /**
                 * 洗车模式 - 后视镜折叠
                 */
                @Override
                public void onRearViewMirrorFoldStateChanged(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    rearMirrorStatus = signalVal;
                    mainHandler.post(() -> {
                        updateOutRearMirror(signalVal);
                    });
                }

                /**
                 * 洗车模式 - 自动雨刮
                 */
                @Override
                public void onAutoWiperInhibit(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    mainHandler.post(() -> {
                        updateAutoWipingInhibit(signalVal);
                    });
                }

                /**
                 * 洗车模式 - 远离上锁
                 */
                @Override
                public void onLeaveLockInhibit(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    mainHandler.post(() -> {
                        updateAvoidLock(signalVal);
                    });
                }
                /**
                 * 洗车模式 - 电动尾翼 - 需求取消
                 */
//            @Override
//            public void onSpoilerInhibit(int signalVal) {
//                sopilerInhibitLiveData.postValue(signalVal);
//            }

                /**
                 * 洗车模式 - 空调内循环
                 */
                @Override
                public void onAirCirculationModeChanged(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    mainHandler.post(() -> {
                        updateAirLoop(signalVal);
                    });
                }

                /**
                 * 洗车模式 - 电释放状态
                 */
                @Override
                public void onOutsideReleaseInhibit(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    mainHandler.post(() -> {
                        updateOutAutoOpen(signalVal);
                    });
                }
                /**
                 * 车速
                 */
                @Override
                public void getVehicleSpeed(int signalVal) {
                    Log.d(TAG, "getVehicleSpeed: " + signalVal);
                    if (signalVal == Integer.MIN_VALUE) return;
                    vehicleSpeed = signalVal;
                    mainHandler.post(() -> {
                        updateCleanWashModeBySpeed();
                    });
                }
            });
            drivingManager.registerListener();
        }

        private void updateCarModeStatus() {
            if (carWashModeStatus != 0x2) {
                dialog.dismiss();
            }
        }

        private void updateCleanWashModeBySpeed() {
            if (vehicleSpeed >= 15 && carWashModeStatus == 0x2) {
                drivingManager.setICC_CleanMode(0x1);
                EToast.showToast(context, "已成功退出洗车模式", 2000, false);
                dialog.dismiss();
            }
        }


        /**
         * Create the custom dialog
         */
        public RegularWashUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new RegularWashUIAlert(context,
                        R.style.Dialog);
            binding = DialogBtRegularwashBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1584;
            layoutParams.height = 1030;
            layoutParams.type = globalAlert ?WindowManager.LayoutParams.TYPE_SYSTEM_ALERT: WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            dialog.setBuilderRef(this);
            window.setAttributes(layoutParams);
            setListeners();
            initData();
            addCallback();
            return dialog;
        }

        private void initData() {
            // 获取档位来判断是常规模式还是传送带模式
            gearPositionInfo = drivingManager.getGearPosition();
            if (gearPositionInfo == CarDriving.VCU_PRNDGearAct.P) {
                setTitle(0);
            }else if (gearPositionInfo == CarDriving.VCU_PRNDGearAct.N) {
                setTitle(1);
            }
            // 初始化数据
            leftFrontCarWindow = drivingManager.getFLZCU_FLWinSts();
            rightFrontCarWindow = drivingManager.getFRZCU_FRWinSts();
            leftRearCarWindow = drivingManager.getFLZCU_RLWinSts();
            rightRearCarWindow = drivingManager.getFRZCU_RRWinSts();
            // 后视镜折叠
            int value = drivingManager.getRearViewFoldSts();
            rearMirrorStatus = value == Integer.MIN_VALUE ? CarDriving.RearViewFoldSts.INVALID : value;
            // 自动雨刮
            int autoWipingInhibit = drivingManager.getFLZCU_AutoWipingInhibit();
            // 自动闭锁
            int autoCloseLock = drivingManager.getFLZCU_RelockInhibit();
            // 远离上锁
            int leaveLockInhibit = drivingManager.getFLZCU_LeavelockInhibit();
            // 空调内循环
            int airLoop = drivingManager.getTMS_CirculationModeDisplaySts();
            // 外部自动开门
            int outAutoOpen = drivingManager.getFLZCU_OutsideReleaseInhibit();
            // 车窗状态
            updateCarWindow();

            // 天窗状态 -- 需求取消

            // 后视镜折叠
            updateOutRearMirror(rearMirrorStatus);

            // 自动雨刮
            updateAutoWipingInhibit(autoWipingInhibit);

            // 自动闭锁
            updateAutoCloseLock(autoCloseLock);

            // 远离上锁
            updateAvoidLock(leaveLockInhibit);

            // 自动尾翼 -- 不做
            // updateAutotail(onProgressChangedListener.getAutotail());

            // 空调内循环
            updateAirLoop(airLoop);

            // 外部自动开门
            updateOutAutoOpen(outAutoOpen);
        }

        public void setListeners() {
            binding.btnExit.setOnClickListener(v -> {
                int cleanModeStatus = drivingManager.getFLZCU_CleanModeStatus();
                if (cleanModeStatus == 0x2) {
                    drivingManager.setICC_CleanMode(0x1);
                    EToast.showToast(context, "已成功退出洗车模式", 2000, false);
                    dialog.dismiss();
                }
            });
            binding.rlRearMirror.setOnClickListener(v -> {
                if (rearMirrorStatus == CarDriving.RearViewFoldSts.UNFOLD || rearMirrorStatus == CarDriving.RearViewFoldSts.INVALID) {
                    drivingManager.setOutRearMirror(CarDriving.RearViewFoldCont.FOLD);
                }else if (rearMirrorStatus == CarDriving.RearViewFoldSts.FOLD) {
                    drivingManager.setOutRearMirror(CarDriving.RearViewFoldCont.UNFOLD);
                }
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        // 车窗
        public void updateCarWindow() {
            if (leftFrontCarWindow <= 1 && leftRearCarWindow <= 1 && rightFrontCarWindow <= 1 && rightRearCarWindow <= 1) {
                binding.ivWindow.setBackgroundResource(R.drawable.icon_set_yes);
                binding.tvWindowStatus.setText(context.getString(R.string.closed));
                binding.tvWindowStatus.setTextColor(ContextCompat.getColor(context, R.color.black));
            } else {
                binding.ivWindow.setBackgroundResource(R.drawable.icon_set_no);
                binding.tvWindowStatus.setText(context.getString(R.string.UnClosed));
                binding.tvWindowStatus.setTextColor(Color.RED);
            }
        }

        // 天窗 -- 不做
        public void updateSkyWindow(int signalVal) {

        }

        // 外后视镜
        public void updateOutRearMirror(int signalVal) {
            if (signalVal == CarDriving.RearViewFoldSts.FOLD) {
                binding.ivParkingMirror.setBackgroundResource(R.drawable.icon_set_yes);
                binding.tvParkingMirrorStatus.setText(context.getString(R.string.folded));
                binding.tvParkingMirrorStatus.setTextColor(ContextCompat.getColor(context, R.color.black));
                binding.tvUnfoldMirror.setText(context.getString(R.string.unfold_mirror));
            } else {
                binding.ivParkingMirror.setBackgroundResource(R.drawable.icon_set_no);
                binding.tvParkingMirrorStatus.setText(context.getString(R.string.unfolded));
                binding.tvUnfoldMirror.setText(context.getString(R.string.fold_mirror));
                binding.tvParkingMirrorStatus.setTextColor(Color.RED);
            }
        }

        // 自动雨刮
        public void updateAutoWipingInhibit(int signalVal) {
            if (signalVal == CarDriving.FLZCU_AutoWipingInhibit.INHIBIT) {
                binding.ivAutoWiper.setBackgroundResource(R.drawable.icon_set_yes);
                binding.tvAutoWiperStatus.setText(context.getString(R.string.disabled));
                binding.tvAutoWiperStatus.setTextColor(ContextCompat.getColor(context, R.color.black));
            } else {
                binding.ivAutoWiper.setBackgroundResource(R.drawable.icon_set_no);
                binding.tvAutoWiperStatus.setText(context.getString(R.string.not_disabled));
                binding.tvAutoWiperStatus.setTextColor(Color.RED);
            }
        }

        // 远离上锁
        public void updateAvoidLock(int signalVal) {
            if (signalVal == CarDriving.FLZCU_LeavelockInhibit.INHIBIT) {
                binding.ivRemoteLock.setBackgroundResource(R.drawable.icon_set_yes);
                binding.tvRemoteLockStatus.setText(context.getString(R.string.disabled));
                binding.tvRemoteLockStatus.setTextColor(ContextCompat.getColor(context, R.color.black));
            } else {
                binding.ivRemoteLock.setBackgroundResource(R.drawable.icon_set_no);
                binding.tvRemoteLockStatus.setText(context.getString(R.string.not_disabled));
                binding.tvRemoteLockStatus.setTextColor(Color.RED);
            }
        }

        // 自动闭锁
        public void updateAutoCloseLock(int signalVal) {
            if (signalVal == CarDriving.FLZCU_RelockInhibit.INHIBIT) {
                binding.ivAutoLock.setBackgroundResource(R.drawable.icon_set_yes);
                binding.tvAutoLockStatus.setText(context.getString(R.string.disabled));
                binding.tvAutoLockStatus.setTextColor(ContextCompat.getColor(context, R.color.black));
            } else {
                binding.ivAutoLock.setBackgroundResource(R.drawable.icon_set_no);
                binding.tvAutoLockStatus.setText(context.getString(R.string.not_disabled));
                binding.tvAutoLockStatus.setTextColor(Color.RED);
            }
        }

        // 外部自动开门 （电释放）
        public void updateOutAutoOpen(int signalVal) {
            if (signalVal == CarDriving.FLZCU_OutsideReleaseInhibit.INHIBIT) {
                binding.ivExternalAutoDoor.setBackgroundResource(R.drawable.icon_set_yes);
                binding.tvExternalAutoDoorStatus.setText(context.getString(R.string.disabled));
                binding.tvExternalAutoDoorStatus.setTextColor(ContextCompat.getColor(context, R.color.black));
            } else {
                binding.ivExternalAutoDoor.setBackgroundResource(R.drawable.icon_set_no);
                binding.tvExternalAutoDoorStatus.setText(context.getString(R.string.not_disabled));
                binding.tvExternalAutoDoorStatus.setTextColor(Color.RED);
            }
        }

        // 空调内循环
        public void updateAirLoop(int signalVal) {
            if (signalVal == CarDriving.TMS_CirculationModeDisplaySts.RECIRCULATION_MODE) {
                binding.ivAirConditioning.setBackgroundResource(R.drawable.icon_set_yes);
                binding.tvAirConditioningStatus.setText(context.getString(R.string.enabled));
                binding.tvAirConditioningStatus.setTextColor(ContextCompat.getColor(context, R.color.black));
            } else {
                binding.ivAirConditioning.setBackgroundResource(R.drawable.icon_set_no);
                binding.tvAirConditioningStatus.setText(context.getString(R.string.not_enabled));
                binding.tvAirConditioningStatus.setTextColor(Color.RED);
            }
        }

        // 电动尾翼 - 需求取消
        public void updateAutotail(int signalVal) {

        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
    }

    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;
}
