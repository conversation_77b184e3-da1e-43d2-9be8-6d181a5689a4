package com.bitech.vehiclesettings.view.connect;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ActivityChooserView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectChargingRemindBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class ChargingRemindUIAlert extends BaseDialog {
    private static final String TAG = ChargingRemindUIAlert.class.getSimpleName();
    private static ChargingRemindUIAlert.onProgressChangedListener onProgressChangedListener;


    public ChargingRemindUIAlert(@NonNull Context context) {
        super(context);
    }

    public ChargingRemindUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ChargingRemindUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static ChargingRemindUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(ChargingRemindUIAlert.onProgressChangedListener onProgressChangedListener) {
        ChargingRemindUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private ChargingRemindUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConnectChargingRemindBinding binding;
        DetailsUIAlert.Builder detailUIAlert;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private ChargingRemindUIAlert dialog = null;
        private View layout;
        public Builder(Context context) {
            this.context = context;
        }


        public ChargingRemindUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public ChargingRemindUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new ChargingRemindUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertConnectChargingRemindBinding.inflate(LayoutInflater.from(context));
            detailUIAlert = new DetailsUIAlert.Builder(context);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176; // 或者使用具体的像素值
            layoutParams.height = 688;
            window.setAttributes(layoutParams);

            binding.tvConfirm.setOnClickListener(v -> {
                if (onProgressChangedListener != null) {
                    int state = binding.rbNoReminder.isChecked() ? 1 : 0;
                    onProgressChangedListener.onSwitch(state);
                }
                dialog.dismiss();
            });

            return dialog;
        }

        public ChargingRemindUIAlert getDialog() {
            return dialog;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(int state);
    }
}

