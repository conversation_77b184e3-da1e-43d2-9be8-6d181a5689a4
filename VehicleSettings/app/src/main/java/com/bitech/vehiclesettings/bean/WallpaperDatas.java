package com.bitech.vehiclesettings.bean;

import com.bitech.vehiclesettings.R;

import java.util.ArrayList;
import java.util.List;

public class WallpaperDatas {

    public static class Scene {
        // TODO 待释放资源
        public static List<WallpaperBean> data = new ArrayList<WallpaperBean>() {
            {
                add(new WallpaperBean(R.mipmap.display_wp_3d, R.string.str_wrapper_3d));
                add(new WallpaperBean(R.mipmap.display_wp_cat, R.string.str_wrapper_cat));
//                add(new WallpaperBean(R.mipmap.display_wp_5g, R.string.str_wrapper_wg));
                add(new WallpaperBean(R.mipmap.display_wp_to_be_continue, R.string.str_wrapper_to_be_continue));
            }
        };
    }
}
