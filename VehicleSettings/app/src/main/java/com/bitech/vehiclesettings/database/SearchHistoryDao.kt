package com.bitech.vehiclesettings.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.bitech.vehiclesettings.bean.SearchHistoryBean

/**
 * @ClassName: SearchHistoryDao
 * 
 * @Date:  2024/4/15 9:40
 * @Description: 搜索历史对应的room数据库Dao.
 **/
@Dao
interface SearchHistoryDao {

    /**
     * 从数据库表中查询所有的搜索历史.
     *
     * @return 搜索历史列表
     */
    @Query("SELECT * FROM searchHistory")
    fun getAllSearchHistory(): MutableList<SearchHistoryBean>?

    /**
     * 将历史记录插入数据库.
     *
     * @param searchHistoryBean 历史搜索对象
     */
    @Transaction
    fun insertOrUpdateSearchHistory(searchHistoryBean: SearchHistoryBean) {
        if (getSearchHistory(searchHistoryBean.historyResult) != null) {
            // 更新数据库
            updateSearchHistory(searchHistoryBean)
        } else {
            // 插入数据库
            insertSearchHistory(searchHistoryBean)
        }
    }

    /**
     * 将搜索历史插入数据库.
     *
     * @param searchHistoryBean 搜索结果对象
     */
    @Insert
    fun insertSearchHistory(searchHistoryBean: SearchHistoryBean)

    /**
     * 更新历史记录.
     *
     * @param searchHistoryBean 历史记录对象
     */
    @Update
    fun updateSearchHistory(searchHistoryBean: SearchHistoryBean)

    /**
     * 根据搜索记录，获取搜索历史结果对象.
     *
     * @param historyResult 搜索结果对象
     * @return SearchHistoryBean
     */
    @Query("SELECT * FROM searchHistory WHERE historyResult = :historyResult")
    fun getSearchHistory(historyResult: String): SearchHistoryBean?

    /**
     * 删除表中对象.
     *
     * @param searchHistoryBean 历史记录对象
     */
    @Delete
    fun deleteBtDevice(searchHistoryBean: SearchHistoryBean)

    /**
     * 清空数据库中所有的搜索历史.
     *
     */
    @Query("DELETE FROM searchHistory")
    fun deleteAllSearchHistory()
}
