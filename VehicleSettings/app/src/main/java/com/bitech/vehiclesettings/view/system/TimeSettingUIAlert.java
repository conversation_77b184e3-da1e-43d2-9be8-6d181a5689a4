package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.common.SiganlConstans;
import com.bitech.vehiclesettings.databinding.DialogAlertSTimeBinding;
import com.bitech.vehiclesettings.presenter.system.SystemPresenterListener;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.GsonUtils;
import com.bitech.vehiclesettings.utils.SendICUTopicsUtil;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

public class TimeSettingUIAlert extends BaseDialog {
    private static final String TAG = TimeSettingUIAlert.class.getSimpleName();


    public TimeSettingUIAlert(@NonNull Context context) {
        super(context);
    }

    public TimeSettingUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected TimeSettingUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }


    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private TimeSettingUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSTimeBinding binding;
        SystemPresenterListener presenter;
        Configuration configuration;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private TimeSettingUIAlert dialog = null;

        public Builder(Context context, SystemPresenterListener presenter) {
            this.context = context;
            configuration = context.getResources().getConfiguration();
            this.presenter = presenter;
        }

        public TimeSettingUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public TimeSettingUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new TimeSettingUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSTimeBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            init();
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1584; // 或者使用具体的像素值
            layoutParams.height = 796;
            window.setAttributes(layoutParams);
            return dialog;
        }

        @SuppressLint("DefaultLocale")
        public void init() {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);

            binding.npYear.setMinValue(2000);
            binding.npYear.setMaxValue(2037);
            binding.npYear.setFormatter(String::valueOf);
            binding.npYear.setValue(year);

            binding.npMonth.setMinValue(1);
            binding.npMonth.setMaxValue(12);
            String[] months = new String[12];
            for (int i = 0; i < 12; i++) {
                months[i] = String.format("%02d", i + 1);
            }
            binding.npMonth.setDisplayedValues(months);
            binding.npMonth.setValue(month);

            binding.npDay.setMinValue(1);
            binding.npDay.setMaxValue(31);
            String[] days = new String[31];
            for (int i = 0; i < 31; i++) {
                days[i] = String.format("%02d", i + 1);
            }
            binding.npDay.setDisplayedValues(days);
            binding.npDay.setValue(day);


            binding.npMinute.setMinValue(0);
            binding.npMinute.setMaxValue(59);
            String[] minutes = new String[60];
            for (int i = 0; i < 60; i++) {
                minutes[i] = String.format("%02d", i);
            }
            binding.npMinute.setDisplayedValues(minutes);
            binding.npMinute.setValue(minute);

            binding.npAmPm.setMinValue(0);
            binding.npAmPm.setMaxValue(1);
            binding.npAmPm.setDisplayedValues(new String[]{context.getString(R.string.ne_am), context.getString(R.string.ne_pm)});
            boolean is24 = presenter.getTimeDisplay(context);

            binding.npHour.setMinValue(0);
            if (is24) {
                binding.npHour.setMaxValue(23);
                binding.npHour.setValue(hour);
                binding.npAmPm.setVisibility(View.INVISIBLE);
            } else {
                binding.npHour.setMaxValue(11);
                binding.npHour.setValue(hour - 12);
            }

            String[] hours = new String[24];
            for (int i = 0; i < 24; i++) {
                hours[i] = String.format("%02d", i);
            }
            binding.npHour.setDisplayedValues(hours);
            binding.npAmPm.setValue(hour < 12 ? 0 : 1);
            binding.btnConfirm.setOnClickListener(v -> {
                int y = binding.npYear.getValue();
                int m = binding.npMonth.getValue();
                int d = binding.npDay.getValue();
                int h = binding.npHour.getValue();
                int min = binding.npMinute.getValue();
                int ampm = binding.npAmPm.getValue();
                if (!is24) {
                    if(ampm == 1){
                        h += 12;
                    }
                }

                Calendar newCal = Calendar.getInstance();
                newCal.set(y, m - 1, d, h, min);
                long millis = newCal.getTimeInMillis();

                SystemClock.setCurrentTimeMillis(millis);
                Map<String, Object> map = new HashMap<>();
                map.put("extension", null);
                map.put("relative", false);
                map.put("valid", true);
                map.put("hour", h);
                map.put("minute", min);
                // 发送Topic至ICU
                SendICUTopicsUtil.sendObjectTopics(SendICUTopicsUtil.Topics.SystemSetting_TIMECHANGE_SET, map);
                dialog.dismiss();
            });

            binding.btnCancel.setOnClickListener(v -> dialog.dismiss());
        }


    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

}
