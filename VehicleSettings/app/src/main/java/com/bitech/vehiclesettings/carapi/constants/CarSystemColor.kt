package com.bitech.vehiclesettings.carapi.constants

import com.bitech.vehiclesettings.R

class CarSystemColor {
    object SystemColorValue {
        const val BLUE = "#3a76ef"
        const val PURPLE = "#7758a2"
        const val CYAN = "#57a2a2"
        const val GREEN = "#85a359"
        const val ORANGE = "#bd9c5c"
        const val RED = "#a3575e"
    }

    object SystemColorName {
        const val BLUE = "Blue"
        const val PURPLE = "Purple"
        const val CYAN = "Cyan"
        const val GREEN = "Green"
        const val ORANGE = "Orange"
        const val RED = "Red"
    }

    object SystemColorStyle {
        const val BLUE = R.style.OverlayThemeBlue
        const val PURPLE = R.style.OverlayThemePurple
        const val CYAN = R.style.OverlayThemeCyan
        const val GREEN = R.style.OverlayThemeGreen
        const val ORANGE = R.style.OverlayThemeOrange
        const val RED = R.style.OverlayThemeRed
    }
}