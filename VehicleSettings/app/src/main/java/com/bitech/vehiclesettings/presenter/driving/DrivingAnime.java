package com.bitech.vehiclesettings.presenter.driving;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.R;

public class DrivingAnime {
    private static final String TAG = DrivingPresenter.class.getSimpleName();
    private static final int ANIMATION_DURATION = 300;
    private static final int MARGIN_RIGHT = 8;

    private final Context context;

    public DrivingAnime(Context context) {
        this.context = context;
    }

    public void beginTranslateAndScale(View view, int from, int to, int startWidth, int endWidth) {
        view.setTranslationX(0);

        ObjectAnimator translationAnim = ObjectAnimator.ofFloat(view, "translationX", from, to);
        ValueAnimator widthAnim = createWidthAnimator(view, startWidth, endWidth);

        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(translationAnim, widthAnim);
        animatorSet.setDuration(ANIMATION_DURATION);
        animatorSet.start();
    }

    private ValueAnimator createWidthAnimator(View view, int start, int end) {
        ValueAnimator animator = ValueAnimator.ofInt(start, end);
        animator.addUpdateListener(animation -> {
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            );
            params.width = (int) animation.getAnimatedValue();
            params.setMargins(0, MARGIN_RIGHT, MARGIN_RIGHT, MARGIN_RIGHT);
            view.setLayoutParams(params);
        });
        return animator;
    }

    public void setTextView(boolean withDelay, TextView selected, TextView... unselected) {
        new Handler().postDelayed(() -> {
            selected.setTextColor(context.getColor(R.color.white));

            int unselectedColor = context.getColor(Util.isNight(context) ?
                    R.color.white : R.color.black);

            for (TextView textView : unselected) {
                textView.setTextColor(unselectedColor);
            }
        }, withDelay ? ANIMATION_DURATION : 0);
    }

    public void setBackground(View view, int resId) {
        view.setBackground(context.getDrawable(resId));
    }
}
