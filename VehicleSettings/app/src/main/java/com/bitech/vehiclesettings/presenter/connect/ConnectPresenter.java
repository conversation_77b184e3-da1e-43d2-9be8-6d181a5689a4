package com.bitech.vehiclesettings.presenter.connect;

import android.content.Context;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.CarSettingManager;
import com.bitech.platformlib.manager.ConnectManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.connect.ChargingRemindUIAlert;

public class ConnectPresenter implements ConnectPresenterListener {
    private static final String TAG = ConnectPresenter.class.getSimpleName();
    private Context mContext;
    private ConnectPresenterListener listener;

    ChargingRemindUIAlert.Builder builder;

    ConnectManager connectManager = (ConnectManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_CONNECT_MANAGER);
    CarSettingManager carSettingManager = (CarSettingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_SETTING_MANAGER);

    public ConnectPresenter(Context context) {
        this.mContext = context;
        builder = new ChargingRemindUIAlert.Builder(mContext);
    }

    // 获得5G网络状态
    @Override
    public int getMobileNetworkState() {
        // 1.获取状态值
        int state = Prefs.get(PrefsConst.CONNECT_5G, 0);
        // 2.调用接口
        int signVal = connectManager.getFiveGNetwork();
        if (signVal == 1) {
            state = 1;
        }else {
            state = 0;
        }
        return state;
    }

    @Override
    public void setMobileNetworkState(int state) {
        // 1.保存状态值
        Prefs.put(PrefsConst.CONNECT_5G, state);
        // 2.调用接口
        if (state == 0) {
            connectManager.setFiveGNetwork(0x0);
        } else if (state == 1) {
            connectManager.setFiveGNetwork(0x1);
        }
    }

    @Override
    public int getFrontChargingState() {
        int signVal = connectManager.getWirelessCharge();
        Log.d(TAG, "getFrontChargingState: 获取前置充电状态:" + signVal);
        int status = 0;
        if (signVal == 0) {
            status = 0;
        } else if (signVal == 1) {
            status = 1;
        } else {
            status = 0;
        }
        Prefs.put(PrefsConst.CONNECT_FRONT_CHARGING, status);
        return status;
    }

    @Override
    public void setFrontChargingState(int state) {
        // 电源模式:Comfort/ON档  CWC_workingSts
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (state == 0) {
                connectManager.setWirelessCharge(0x1);
            } else if (state == 1) {
                connectManager.setWirelessCharge(0x2);
            }
        }
    }

    @Override
    public int getBackChargingState() {

        return 0;
    }

    @Override
    public void setBackChargingState(int state) {
        // 电源模式:Comfort/ON档  CWC_workingSts
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (state == 0) {
                carSettingManager.setPhoneLeaveAlert(0x1);
            } else if (state == 1) {
                carSettingManager.setPhoneLeaveAlert(0x2);
            }
        }
    }

    @Override
    public int getForgetReminder() {
        int signVal = connectManager.getPhoneLeaveAlert();
        int status = 0;
        if (signVal == 0x0) {
            status = 0;
        } else if (signVal == 0x1) {
            status = 1;
        }
        return status;
    }

    @Override
    public void setForgetReminder(int state) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (state == 0) {
                carSettingManager.setPhoneLeaveAlert(0x1);
            } else if (state == 1) {
                carSettingManager.setPhoneLeaveAlert(0x2);
            }
        }
    }

    @Override
    public int getForgetReminderMemory() {
        return Prefs.get(PrefsConst.CONNECT_FORGET_REMIND_STATE, 0);
    }

    @Override
    public void setForgetReminderMemory(int state) {
        Prefs.put(PrefsConst.CONNECT_FORGET_REMIND_STATE, state);
    }

    // 充电温馨提醒
    @Override
    public int getChargingRemindState() {
        int status = Prefs.get(PrefsConst.CONNECT_CHARGING_REMIND_STATE, 0);
        return status;
    }

    @Override
    public void setChargingRemindState(int state) {
        Prefs.put(PrefsConst.CONNECT_CHARGING_REMIND_STATE, state);
    }

    @Override
    public int getHighTemperatureRemindState() {
        // TODO 缺少高温提醒接口
        return Prefs.get(PrefsConst.CONNECT_CHARGING_REMIND_STATE, 0);
    }

}
