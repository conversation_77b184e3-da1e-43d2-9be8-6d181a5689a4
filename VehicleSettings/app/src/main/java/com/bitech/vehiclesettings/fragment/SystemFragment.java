package com.bitech.vehiclesettings.fragment;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.os.Bundle;
import android.os.Message;
import android.os.StatFs;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.lifecycle.ViewModelProvider;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.AppPermissionBean;
import com.bitech.vehiclesettings.bean.PrivacyStatementBean;
import com.bitech.vehiclesettings.bean.SystemDataBean;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.bean.report.Content;
import com.bitech.vehiclesettings.bean.report.DataPoint;
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl;
import com.bitech.vehiclesettings.carapi.constants.CarSystem;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionSpecialBinding;
import com.bitech.vehiclesettings.databinding.FragmentSystemBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.presenter.system.SystemPresenterListener;
import com.bitech.vehiclesettings.service.DataPointReportLifeCycle;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.PermissionManager;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.ComfirmUIAlert;
import com.bitech.vehiclesettings.view.common.NoToggleSwitch;
import com.bitech.vehiclesettings.view.system.AccessRecordUIAlert;
import com.bitech.vehiclesettings.view.system.BasicPrivacyAgreementUIAlert;
import com.bitech.vehiclesettings.view.system.DatetimeSettingUIAlert;
import com.bitech.vehiclesettings.view.system.DeviceInfoUIAlert;
import com.bitech.vehiclesettings.view.system.GestureNavigationUIAlert;
import com.bitech.vehiclesettings.view.system.GotoEngineModeUIAlert;
import com.bitech.vehiclesettings.view.system.InstrumentFuelUnitUIAlert;
import com.bitech.vehiclesettings.view.system.InternationalPrivacyAgreementUIAlert;
import com.bitech.vehiclesettings.view.system.LanguageSettingUIAlert;
import com.bitech.vehiclesettings.view.system.PermissionAppAuthorizedItemUIAlert;
import com.bitech.vehiclesettings.view.system.PermissionAppAuthorizedMainUIAlert;
import com.bitech.vehiclesettings.view.system.PermissionAppAuthorizedUIAlert;
import com.bitech.vehiclesettings.view.system.PermissionAppUIAlert;
import com.bitech.vehiclesettings.view.system.PermissionSpecialOffUIAlert;
import com.bitech.vehiclesettings.view.system.PermissionSpecialUIAlert;
import com.bitech.vehiclesettings.view.system.PermissionUIAlert;
import com.bitech.vehiclesettings.view.system.PowerConsumptionUnitUIAlert;
import com.bitech.vehiclesettings.view.system.PrivacyStatementUIAlert;
import com.bitech.vehiclesettings.view.system.ServicePrivacyAgreementUIAlert;
import com.bitech.vehiclesettings.view.system.SystemDataResetNoUserUIAlert;
import com.bitech.vehiclesettings.view.system.SystemDataResetUIAlert;
import com.bitech.vehiclesettings.view.system.SystemDataUIAlert;
import com.bitech.vehiclesettings.view.system.SystemVideoUIAlert;
import com.bitech.vehiclesettings.view.system.TemperatureUnitUIAlert;
import com.bitech.vehiclesettings.view.system.TirePressureUnitUIAlert;
import com.bitech.vehiclesettings.view.system.UnitSettingUIAlert;
import com.bitech.vehiclesettings.view.system.UserFeedBackUIAlert;
import com.bitech.vehiclesettings.view.system.VersionMsgUIAlert;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;
import com.bitech.vehiclesettings.viewmodel.SystemModel;
import com.bitech.vehiclesettings.viewmodel.WifiViewModel;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.lion.datapoint.log.LogDataUtil;
import com.lion.os.sdk.accountInfo.AccountInfoManager;
import com.lion.os.sdk.accountInfo.bean.UserInfo;
import com.lion.os.sdk.accountInfo.listener.ServiceConnectListener;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SystemFragment extends BaseFragment<FragmentSystemBinding> implements View.OnClickListener, SafeHandlerCallback {
    private static final String TAG = SystemFragment.class.getSimpleName();
    private int clickCount = 0; // 点击计数
    private static final int MAX_CLICKS = 6; // 最大点击次数
    private ArrayList<Content> dataList;
    private long lastTime;
    SystemPresenterListener presenter;
    private SafeHandler systemHandler;

    private MainActViewModel mainActViewModel;

    public static final int POWER_CONSUMPTION_UNIT = 10;
    public static final int GAUGE_FUEL_CONSUMPTION = 11;
    public static final int GESTURE_NAVIGATION = 12;
    public static final int RESET_EXIT_SETTING = 13;
    public static final int DEVICE_INFORMATION = 14;
    public static final int PRIVACY_SETTING = 15;

    // Dialog
    private SystemDataResetUIAlert.Builder systemDataResetUIAlert;
    // 设备信息
    private DeviceInfoUIAlert.Builder deviceInfoUIAlert;
    // 视频播放
    private SystemVideoUIAlert.Builder systemVideoUIAlert;
    // 权限管理
    private PermissionUIAlert permissionDialog; // 保存Dialog实例
    private PermissionUIAlert.Builder permissionUIAlert;
    private PermissionAppUIAlert.Builder permissionCameraUIAlert, permissionMicrophoneUIAlert, permissionPositionUIAlert;
    private AccessRecordUIAlert.Builder accessRecordUIAlert;
    private PermissionSpecialUIAlert.Builder permissionSpecialUIAlert;
    // 隐私政策详情
    private PrivacyStatementUIAlert.Builder privacyStatementUIAlert;
    // 基础隐私协议
    private BasicPrivacyAgreementUIAlert.Builder basicPrivacyAgreementUIAlert;
    // 服务隐私协议
    ServicePrivacyAgreementUIAlert.Builder servicePrivacyAgreementUIAlert;
    // 实时数据
    SystemDataUIAlert.Builder realTimeDataUIAlert;
    // 诊断数据
    SystemDataUIAlert.Builder diagnosticDataUIAlert;
    // privacy statement
    InternationalPrivacyAgreementUIAlert.Builder internationalPrivacyAgreementUIAlert;
    // 仪表油耗单位
    private InstrumentFuelUnitUIAlert.Builder instrumentFuelUnitUIAlert;
    // 胎压单位
    private TirePressureUnitUIAlert.Builder tirePressureUnitUIAlert;
    // 电耗单位
    private PowerConsumptionUnitUIAlert.Builder powerConsumptionUnitUIAlert;
    // 单位设置
    private UnitSettingUIAlert.Builder unitSettingUIAlert;
    // 温度单位
    private TemperatureUnitUIAlert.Builder temperatureUnitUIAlert;
    // 手势导航
    private GestureNavigationUIAlert.Builder gestureNavigationUIAlert;
    // 跳转工程模式
    private GotoEngineModeUIAlert.Builder gotoEngineModeUIAlert;
    private DatetimeSettingUIAlert datetimeSettingUIAlert;
    private LanguageSettingUIAlert.Builder languageSettingUIAlert;
    private SystemDataResetNoUserUIAlert.Builder systemDataResetNoUserUIAlert;
    // 用户反馈
    private UserFeedBackUIAlert.Builder userFeedBackUIAlert;

    private SystemModel viewModel;

    private volatile boolean isActive;
    private boolean isServicePrivacyAgreement = false;
    private boolean hasShownPrivacyDialog = false;
    private boolean isFragmentFocus = false;
    private int instrumentFuelUnit;
    private int tirePressureUnit;
    private int powerConsumptionUnit;
    private ContentResolver contentResolver;
    // 仅授权本次行程
    private boolean[] isThisTimePermissionFlags = {false, false, false};

    private boolean isDialogShow = false;

    private List<PrivacyStatementBean> allPrivacyStatements;

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null) return;
        loadPageAnim(binding.scrollView, currentPosition, position);
    }

    public void setPresenter(SystemPresenter systemPresenter) {
        this.presenter = systemPresenter;
    }

    @Override
    protected FragmentSystemBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentSystemBinding.inflate(getLayoutInflater());
        return binding;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: " + this.hashCode()); // 检查是否为同一实例
        isActive = true;
        setPresenter(new SystemPresenter(getContext()));
        systemHandler = new SafeHandler(this);
        // 1. 先拿到所有应用的权限列表
        List<AppPermissionBean> allApps = PermissionManager.loadAllAppPermissions(mContext);

        // 2. 拆成「摄像头／麦克风／位置」三份
        List<AppPermissionBean> cameraList = new ArrayList<>();
        List<AppPermissionBean> microphoneList = new ArrayList<>();
        List<AppPermissionBean> locationList = new ArrayList<>();
        for (AppPermissionBean bean : allApps) {
            switch (bean.getType()) {
                case CAMERA:
                    cameraList.add(bean);
                    break;
                case MICROPHONE:
                    microphoneList.add(bean);
                    break;
                case LOCATION:
                    locationList.add(bean);
                    break;
            }
        }

        presenter = new SystemPresenter<>(
                mContext,
                cameraList,
                microphoneList,
                locationList
        );
        AccountInfoManager.getInstance().initialize(mContext, new ServiceConnectListener() {
            @Override
            public void onRemoteConnectStatus(boolean b) {
                if (b) {
                    Log.d(TAG, "onRemoteConnectStatus: " + b);
                }
            }
        });
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return getLayoutResId(inflater, container).getRoot();
    }

    protected void initObserve() {
        viewModel = new ViewModelProvider(this).get(SystemModel.class);
        viewModel.getAnalysis().observe(getViewLifecycleOwner(), this::switchAnalysis);
        //MainActivity的ViewModel
        mainActViewModel = new ViewModelProvider(requireActivity()).get(MainActViewModel.class);
        processTargetDialogEvent(mainActViewModel.getTargetDialogLiveEvent().getValue());
        mainActViewModel.getTargetDialogLiveEvent().observe(getViewLifecycleOwner(), this::processTargetDialogEvent);
        /**
         * 权限开启状态UI更新
         */
        // 设置摄像头授权开启状态UI更新
        viewModel.getPermissionSwitchCamera().observe(getViewLifecycleOwner(), this::updatePermissionSwitchCameraUI);
        // 设置麦克风授权开启状态UI更新
        viewModel.getPermissionSwitchMicrophone().observe(getViewLifecycleOwner(), this::updatePermissionSwitchMicrophoneUI);
        // 设置位置授权开启状态UI更新
        viewModel.getPermissionSwitchLocation().observe(getViewLifecycleOwner(), this::updatePermissionSwitchPositionUI);
        // 设置系统软件版本信息
        viewModel.getSystemSoftwareVersion().observe(getViewLifecycleOwner(), this::updateSystemSoftwareVersion);
        // 设置系统硬件版本信息
        viewModel.getSystemHardwareVersion().observe(getViewLifecycleOwner(), this::updateSystemHardwareVersion);
        // 设置仪表油耗单位
        viewModel.getFuelUnit().observe(getViewLifecycleOwner(), this::updateInstrumentFuelUnitUI);
        // 胎压单位
        viewModel.getTirePressureUnit().observe(getViewLifecycleOwner(), this::updateTirePressureUnitUI);
        // 电耗单位
        viewModel.getPowerConsumptionUnit().observe(getViewLifecycleOwner(), this::updatePowerConsumptionUnitUI);
        // 档位
        viewModel.gearPositionLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateGearPositionUI(signalVal);
        });
        // 获取设备信息
        viewModel.getDeviceInfo().observe(getViewLifecycleOwner(), this::updateDeviceInfo);
        // TODO 短解
        initData();
    }

    private void updateDeviceInfo(String deviceInfo) {
        binding.tvDeviceMsg.setText(deviceInfo);
    }

    private void updateGearPositionUI(Integer signalVal) {
        if (signalVal == CarSystem.VCU_PRNDGearAct.P) {
            GrayEffectUtils.removeGrayEffect(binding.rlRestoreSetting);
            binding.vwRestoreSetting.setVisibility(View.GONE);
        } else {
            GrayEffectUtils.applyGrayEffect(binding.rlRestoreSetting);
            binding.vwRestoreSetting.setVisibility(View.VISIBLE);
        }
    }


    /**
     * 设置热点名称
     */
    public void setHotName() {
        WifiViewModel wifiViewModel = new ViewModelProvider(this).get(WifiViewModel.class);
        wifiViewModel.updateHotspotName(presenter.getDeviceInfo());
    }

    private void getVersionInfo() {
        // TODO 获取系统信息
        presenter.getSystemSoftwareVersionInfo();
        presenter.getSystemHardwareVersionInfo();
    }

    @Override
    protected void initView() {
    }

    @Override
    public void onResume() {
        super.onResume();
        initServicePrivacyAgreement();
    }

    /**
     * Fragment 销毁时，做进一步清理，比如关闭对话框
     */
    @Override
    public void onDestroy() {
        isActive = false;
        super.onDestroy();
        // 如果对话框仍然显示，关闭它
        if (permissionUIAlert != null && permissionUIAlert.create().isShowing()) {
            permissionUIAlert.create().dismiss();
        }
        // 如果授权为本次运行，则退出时恢复为未授权
        if (isThisTimePermissionFlags[0]) {
            presenter.setSwitchPermission(0, 0);
        } else if (isThisTimePermissionFlags[1]) {
            presenter.setSwitchPermission(1, 0);
        } else if (isThisTimePermissionFlags[2]) {
            presenter.setSwitchPermission(2, 0);
        }
        // 释放 Presenter 的引用，若有相应的释放方法
        presenter.destroy();
        presenter = null;
        if (binding != null) {
            binding = null;
        }
    }

    @Override
    public boolean isActive() {
        return isActive;
    }


    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.SYSTEM_ANALYSIS:
                int swAnalysis = presenter.getSwAnalysis();
                if (swAnalysis != viewModel.getAnalysis().getValue()) {
                    viewModel.setAnalysis(swAnalysis);
                }
                break;
            case MessageConst.SYSTEM_PERMISSION_APP_SWITCH_CAMERA:
                // 摄像头权限
                int permissionSwitchCamera = presenter.getSwitchPermission(0);
                if (permissionSwitchCamera != viewModel.getPermissionSwitchCamera().getValue()) {
                    viewModel.setPermissionSwitchCamera(permissionSwitchCamera);
                }
                break;
            case MessageConst.SYSTEM_PERMISSION_APP_SWITCH_MICROPHONE:
                // 麦克风权限
                int permissionSwitchMicrophone = presenter.getSwitchPermission(1);
                if (permissionSwitchMicrophone != viewModel.getPermissionSwitchMicrophone().getValue()) {
                    viewModel.setPermissionSwitchMicrophone(permissionSwitchMicrophone);
                }
                break;
            case MessageConst.SYSTEM_PERMISSION_APP_SWITCH_LOCATION:
                // 位置权限
                int permissionSwitchLocation = presenter.getSwitchPermission(2);
                if (permissionSwitchLocation != viewModel.getPermissionSwitchLocation().getValue()) {
                    viewModel.setPermissionSwitchLocation(permissionSwitchLocation);
                }
            case MessageConst.SYSTEM_INSTRUMENT_FUEL_UNIT:
                // 仪表油耗单位
//                int instrumentFuelUnit = presenter.getInstrumentFuelUnit();
//                Log.d(TAG, "getInstrumentFuelUnit 获取仪表油耗单位:" + instrumentFuelUnit);
//                if (instrumentFuelUnit != viewModel.getFuelUnit().getValue()) {
//                    viewModel.setFuelUnit(instrumentFuelUnit);
//                    this.instrumentFuelUnit = instrumentFuelUnit;
//                }
                presenter.requestInstrumentFuelUnit();
                Log.d(TAG, "requestInstrumentFuelUnit 通知获取仪表油耗单位");
                break;
            case MessageConst.SYSTEM_TIRE_PRESSURE_UNIT:
                // 胎压单位
//                int tirePressureUnit = presenter.getTirePressureUnit();
//                if (tirePressureUnit != viewModel.getTirePressureUnit().getValue()) {
//                    viewModel.setTirePressureUnit(tirePressureUnit);
//                    this.tirePressureUnit = tirePressureUnit;
//                }
                presenter.requestTirePressureUnit();
                Log.d(TAG, "requestTirePressureUnit 通知获取胎压单位");
            case MessageConst.SYSTEM_POWER_CONSUMPTION_UNIT:
                // 电耗单位
//                int powerConsumptionUnit = presenter.getPowerConsumptionUnit();
//                if (powerConsumptionUnit != viewModel.getPowerConsumptionUnit().getValue()) {
//                    viewModel.setPowerConsumptionUnit(powerConsumptionUnit);
//                    this.powerConsumptionUnit = powerConsumptionUnit;
//                }
                presenter.requestPowerConsumptionUnit();
                Log.d(TAG, "requestPowerConsumptionUnit 通知获取电耗单位");
                break;
            default:
                break;
        }
    }


    @Override
    protected void setListener() {
        if (binding != null && binding.llVersionMsg != null) {
            // 安全操作
        } else {
            Log.e("SystemFragment", "Binding or llVersionMsg is null!");
        }
        BindingUtil.bindClicks(this, binding.llVersionMsg);
        BindingUtil.bindClicks(this, binding.swAnalysis);
        BindingUtil.bindClicks(this, binding.ivAnalysisTips);
        BindingUtil.bindClicks(this, binding.rlDeviceMsg, binding.rlStorageMsg, binding.rlRestoreSetting, binding.rlPermission, binding.rlPrivacyPolicy,
                binding.rlBasicPrivacy, binding.rlServicePrivacy, binding.rlRealTimeData, binding.rlDiagnosticData, binding.rlPrivacyStatement, binding.rlGestureNavigation,
                binding.rlLanguageSetting, binding.rlDatetimeSetting, binding.rlInstrumentFuelUnit, binding.rlTirePressureUnit, binding.rlPowerConsumptionUnit,
                binding.rlUnitSetting, binding.rlTemperatureSetting, binding.rlUserFeedback);
        /**
         * 权限管理
         */
        PermissionUIAlert.setOnProgressChangedListener(new PermissionUIAlert.onProgressChangedListener() {
            @Override
            public void openApp(int position) {
                openPermissionAppDialog(position);
            }

            @Override
            public void openPermissionSpecial() {
                openPermissionSpecialDialog();
            }

        });
        // 权限管理（摄像头、麦克风、位置）
        PermissionAppUIAlert.setOnProgressChangedListener(new PermissionAppUIAlert.onProgressChangedListener() {

            @Override
            public List<AppPermissionBean> updatePermissionList(int position) {
                return presenter.getPermissionAppList(position);
            }

            @Override
            public int onSwitch(int position) {
                int status = 0;
                if (position == 0) {
                    status = viewModel.getPermissionSwitchCamera().getValue();
                    if (status == 1) {
                        viewModel.setPermissionSwitchCamera(0);
                        presenter.setSwitchPermission(position, 0);
                        systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_CAMERA);
                    } else if (presenter.getPermissionDuring(position).isAfter(LocalDateTime.now()) || isThisTimePermissionFlags[position]) {
                        // 授权时间未过期，直接开启
                        viewModel.setPermissionSwitchCamera(1);
                        // 设置按钮开启
                        presenter.setSwitchPermission(position, 1);
                        // 更新授权应用列表
                        Log.d(TAG, "updatePermissionApp" + permissionUIAlert);
                        if (permissionCameraUIAlert != null) {
                            permissionCameraUIAlert.updatePermissionList();
                        }
                        // 发送信号
                        systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_CAMERA);
                        // 状态更改为开启
                        status = 1;
                    }
                } else if (position == 1) {
                    status = viewModel.getPermissionSwitchMicrophone().getValue();
                    if (status == 1) {
                        viewModel.setPermissionSwitchMicrophone(0);
                        presenter.setSwitchPermission(position, 0);
                        systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_MICROPHONE);
                    } else if (presenter.getPermissionDuring(position).isAfter(LocalDateTime.now()) || isThisTimePermissionFlags[position]) {
                        // 授权时间未过期，直接开启
                        viewModel.setPermissionSwitchMicrophone(1);
                        // 设置按钮开启
                        presenter.setSwitchPermission(position, 1);
                        // 更新授权应用列表
                        if (permissionMicrophoneUIAlert != null) {
                            permissionMicrophoneUIAlert.updatePermissionList();
                        }
                        // 发送信号
                        systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_MICROPHONE);
                        // 状态更改为开启
                        status = 1;
                    }
                } else if (position == 2) {
                    status = viewModel.getPermissionSwitchLocation().getValue();
                    if (status == 1) {
                        viewModel.setPermissionSwitchLocation(0);
                        presenter.setSwitchPermission(position, 0);
                        systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_LOCATION);
                    } else if (presenter.getPermissionDuring(position).isAfter(LocalDateTime.now()) || isThisTimePermissionFlags[position]) {
                        // 授权时间未过期，直接开启
                        viewModel.setPermissionSwitchLocation(1);
                        // 设置按钮开启
                        presenter.setSwitchPermission(position, 1);
                        // 更新授权应用列表
                        if (permissionPositionUIAlert != null) {
                            permissionPositionUIAlert.updatePermissionList();
                        }
                        // 发送信号
                        systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_LOCATION);
                        // 状态更改为开启
                        status = 1;
                    }
                }
                return status;
            }

            @Override
            public void initSwitch(int position) {
                if (position == 0) {
                    // 设置摄像头授权开启状态
                    viewModel.setPermissionSwitchCamera(Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA, 0));
                    // 更新授权应用列表
//                    openPermissionAppDialog(0);
                    permissionCameraUIAlert.updatePermissionList();
                } else if (position == 1) {
                    // 设置麦克风授权开启状态
                    viewModel.setPermissionSwitchMicrophone(Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE, 0));
                    // 更新授权应用列表
//                    openPermissionAppDialog(1);
                    permissionMicrophoneUIAlert.updatePermissionList();
                } else if (position == 2) {
                    // 设置位置授权开启状态
                    viewModel.setPermissionSwitchLocation(Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION, 0));
                    // 更新授权应用列表
//                    openPermissionAppDialog(2);
                    permissionPositionUIAlert.updatePermissionList();
                }
            }

            @Override
            public int getPermissionDuring(int position) {
                return presenter.getPermissionDuringStatus(position);
            }

            @Override
            public void updatePermissionApp(int position, int index, AppPermissionBean bean) {
                // 打开应用权限设置窗口
                PermissionAppAuthorizedItemUIAlert.Builder alert = new PermissionAppAuthorizedItemUIAlert.Builder(getContext(), position,
                        index, bean);
                alert.create().show();
            }

            @Override
            public void openPermissionApp() {
                openPermissionDialog();
            }

            @Override
            public PrivacyStatementBean getPrivacyStatementForLink(int index) {
                allPrivacyStatements = PrivacyStatementUIAlert.getOnProgressChangedListener().getPrivacyStatementList();
                if (allPrivacyStatements != null && index >= 0 && index < allPrivacyStatements.size()) {
                    return allPrivacyStatements.get(index); // 返回指定索引的列表项
                }
                return null;
            }
        });
        //  权限管理特殊权限
        PermissionSpecialUIAlert.setOnProgressChangedListener(new PermissionSpecialUIAlert.onProgressChangedListener() {
            @Override
            public void isSwitch(boolean isChecked) {
                int status = isChecked ? CarQuickControl.ButtonSts.OFF : CarQuickControl.ButtonSts.ON;
                viewModel.setAnalysis(status);
                presenter.setSwAnalysis(status);
                systemHandler.sendMessageDelayed(MessageConst.SYSTEM_ANALYSIS);
            }

            @Override
            public int getSwitch() {
                return presenter.getSwAnalysis();
            }

            @Override
            public void openCancel(DialogAlertSPermissionSpecialBinding b) {
                PermissionSpecialOffUIAlert.Builder confirmUIAlert = new PermissionSpecialOffUIAlert.Builder(getContext());
                confirmUIAlert.create().show();
            }
        });
        // 权限管理授权时间
        PermissionAppAuthorizedUIAlert.setOnProgressChangedListener(new PermissionAppAuthorizedUIAlert.onProgressChangedListener() {

            @Override
            public void setPermissionTime(int position, int status) {
                if (position == 0) {
                    viewModel.setPermissionSwitchCamera(1);
                    // 设置按钮状态
                    presenter.setSwitchPermission(position, 1);
                    // 设置授权时间
                    isThisTimePermissionFlags[position] = status == 1;
                    presenter.setPermissionDuring(position, status);
                    systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_CAMERA);
                } else if (position == 1) {
                    viewModel.setPermissionSwitchMicrophone(1);
                    presenter.setSwitchPermission(position, 1);
                    isThisTimePermissionFlags[position] = status == 1;
                    presenter.setPermissionDuring(position, status);
                    systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_MICROPHONE);
                } else if (position == 2) {
                    viewModel.setPermissionSwitchLocation(1);
                    presenter.setSwitchPermission(position, 1);
                    isThisTimePermissionFlags[position] = status == 1;
                    presenter.setPermissionDuring(position, status);
                    systemHandler.sendMessageDelayed(MessageConst.SYSTEM_PERMISSION_APP_SWITCH_LOCATION);
                }
            }

            @Override
            public void updatePermissionList(int position) {
                Log.d(TAG, "setPermissionTime: " + position);
                if (position == 0) {
                    if (permissionCameraUIAlert != null) {
                        permissionCameraUIAlert.updatePermissionList();
                    }
                } else if (position == 1) {
                    if (permissionMicrophoneUIAlert != null) {
                        permissionMicrophoneUIAlert.updatePermissionList();
                    }
                } else if (position == 2) {
                    if (permissionPositionUIAlert != null) {
                        permissionPositionUIAlert.updatePermissionList();
                    }
                }
            }

            @Override
            public void openPermissionDialog(int position) {
                openPermissionAppDialog(position);
            }
        });

        // 权限管理授权时间radio
        PermissionAppAuthorizedMainUIAlert.setOnProgressChangedListener(new PermissionAppAuthorizedMainUIAlert.onProgressChangedListener() {

            @Override
            public void setPermissionTime(int position, int status) {
                if (status == 1) {
                    isThisTimePermissionFlags[position] = true; // 直接通过 position 索引设置
                }
                presenter.setPermissionDuring(position, status);
            }

            @Override
            public int getStatus(int position) {
                return presenter.getPermissionDuringStatus(position);
            }

            @Override
            public void updateText(int position) {
                if (position == 0) {
                    if (permissionCameraUIAlert != null) {
                        permissionCameraUIAlert.switchText();
                    }
                } else if (position == 1) {
                    if (permissionMicrophoneUIAlert != null) {
                        permissionMicrophoneUIAlert.switchText();
                    }
                } else if (position == 2) {
                    if (permissionPositionUIAlert != null) {
                        permissionPositionUIAlert.switchText();
                    }
                }
            }

            @Override
            public void openPermissionDialog(int position) {
                openPermissionAppDialog(position);
            }
        });

        // 权限管理授权时间app radio
        PermissionAppAuthorizedItemUIAlert.setOnProgressChangedListener(new PermissionAppAuthorizedItemUIAlert.onProgressChangedListener() {
            @Override
            public void setPermissionTime(int position, int status) {

            }

            @Override
            public int getPermission(int position, int index) {
                return presenter.getPermissionApp(position, index).getLevel().ordinal();
            }

            @Override
            public int updateText(int position, int index, AppPermissionBean bean) {
                int status = presenter.setPermissionApp(position, index, bean);
                if (position == 0) {
                    if (permissionCameraUIAlert != null) {
                        permissionCameraUIAlert.updatePermissionList();
                    }
                } else if (position == 1) {
                    if (permissionMicrophoneUIAlert != null) {
                        permissionMicrophoneUIAlert.updatePermissionList();
                    }
                } else if (position == 2) {
                    if (permissionPositionUIAlert != null) {
                        permissionPositionUIAlert.updatePermissionList();
                    }
                }
                return status;
            }
        });

        AccessRecordUIAlert.setOnProgressChangedListener(new AccessRecordUIAlert.onProgressChangedListener() {
            @Override
            public void openPermissionUI() {
                openPermissionDialog();
            }
        });

        /**
         * 隐私政策详情
         */
        PrivacyStatementUIAlert.setOnProgressChangedListener(new PrivacyStatementUIAlert.onProgressChangedListener() {
            @Override
            public List<PrivacyStatementBean> getPrivacyStatementList() {
                return presenter.getPrivacyStatementList();
            }
        });

        /**
         * 服务隐私协议
         */
        ServicePrivacyAgreementUIAlert.setOnProgressChangedListener(new ServicePrivacyAgreementUIAlert.onProgressChangedListener() {
            @Override
            public int onAgree() {
                presenter.setServicePrivacyAgreement(1);
                isServicePrivacyAgreement = true;
                int result = presenter.getServicePrivacyAgreement();
                if (CommonUtils.IntToBool(result)) {
                    binding.tvServicePrivacyIsAgree.setText(getString(R.string.str_system_service_privacy_agreement_agree));
                } else {
                    EToast.showToast(mContext, getString(R.string.str_system_permission_service_privacy_statement_network_error), Toast.LENGTH_SHORT, false);
                }
                return result;
            }

            @Override
            public void onRefuse() {
                presenter.setServicePrivacyAgreement(0);
                isServicePrivacyAgreement = false;
                // 自动弹窗释放
                hasShownPrivacyDialog = false;

                binding.tvServicePrivacyIsAgree.setText(getString(R.string.str_system_service_privacy_agreement_refuse));
            }
        });
        /**
         * 系统管理数据
         */
        SystemDataUIAlert.setOnProgressChangedListener(new SystemDataUIAlert.onProgressChangedListener() {

            @Override
            public List<SystemDataBean> getSystemDatatList(int type) {
                List<SystemDataBean> systemDatatList = presenter.getSystemDatatList(type);
                // 获取数据错误，或者网络错误，toast显示提示
                if (systemDatatList.isEmpty()) {
                    EToast.showToast(mContext, getString(R.string.str_system_data_error), Toast.LENGTH_SHORT, false);
                }
                return systemDatatList;
            }

            @Override
            public int setSystemData(SystemDataBean item, int position, NoToggleSwitch noToggleSwitch, int type) {
                int status = presenter.setSystemData(type, position, !item.isChecked());
                if (status == 1) {
                    Log.d(TAG, "setSystemData: " + item.getTitle() + " " + item.isChecked());
                    EToast.showToast(mContext, item.getTitle() + getString(R.string.str_system_data_setting_success), Toast.LENGTH_SHORT, false);
                    // 成功修改presenter就修改item的选中状态
                    noToggleSwitch.setChecked(item.isChecked());
                } else {
                    EToast.showToast(mContext, item.getTitle() + getString(R.string.str_system_data_setting_error), Toast.LENGTH_SHORT, false);
                }
                return status;
            }
        });
        /**
         * privacy statement
         */
        InternationalPrivacyAgreementUIAlert.setOnProgressChangedListener(new InternationalPrivacyAgreementUIAlert.onProgressChangedListener() {
            @Override
            public int getDataStatus(int index) {
                int status = presenter.getSystemDataAcquisitionStatus(index);
                Log.d(TAG, "getDataStatus: " + status);
                return status;
            }

            @Override
            public void updateSwitch(NoToggleSwitch noToggleSwitch, int index) {
                ComfirmUIAlert.Builder confirmUIAlert = new ComfirmUIAlert.Builder(mContext);
                confirmUIAlert.setTitle("");
                confirmUIAlert.setContent(getString(R.string.str_system_international_privacy_statement_confirm_content));
                confirmUIAlert.setConfirmText(getString(R.string.str_system_international_privacy_statement_confirm_confirmed));
                confirmUIAlert.setCancelText(getString(R.string.str_system_international_privacy_statement_confirm_refused));
                confirmUIAlert.setOnConfirmListener2(function -> {
                    presenter.setSystemDataAcquisitionStatus(index, CommonUtils.BoolToInt(!noToggleSwitch.isChecked()));
                    Log.d(TAG, "updateSwitch: " + noToggleSwitch.isChecked());
                    noToggleSwitch.setChecked(!noToggleSwitch.isChecked());
                });
                confirmUIAlert.create(1128, 508).show();
            }
        });

        // 仪表油耗单位
        InstrumentFuelUnitUIAlert.setOnProgressChangedListener(new InstrumentFuelUnitUIAlert.OnProgressChangedListener() {
            @Override
            public int getInstrumentFuelUnit() {
                return instrumentFuelUnit;
            }

            @Override
            public void setInstrumentFuelUnit(int state) {
                presenter.setInstrumentFuelUnit(state);
            }

            @Override
            public void onSwitch(int index) {
                instrumentFuelUnit = index;
                viewModel.setFuelUnit(index);
                presenter.setInstrumentFuelUnit(index);
                systemHandler.sendMessageDelayed(MessageConst.SYSTEM_INSTRUMENT_FUEL_UNIT);
                Log.d(TAG, "仪表油耗单位: " + index);
            }
        });
        // 胎压单位
        TirePressureUnitUIAlert.setOnProgressChangedListener(new TirePressureUnitUIAlert.OnProgressChangedListener() {
            @Override
            public int getTirePressureUnit() {
                return tirePressureUnit;
            }

            @Override
            public void setTirePressureUnit(int state) {
                presenter.setTirePressureUnit(state);
            }

            @Override
            public void onSwitch(int index) {
                tirePressureUnit = index;
                viewModel.setTirePressureUnit(index);
                if (index == 0) {
                    presenter.setTirePressureUnit(1);
                } else if (index == 1) {
                    presenter.setTirePressureUnit(3);
                } else {
                    presenter.setTirePressureUnit(2);
                }
                systemHandler.sendMessageDelayed(MessageConst.SYSTEM_TIRE_PRESSURE_UNIT);
            }
        });
        // 电耗单位
        PowerConsumptionUnitUIAlert.setOnProgressChangedListener(new PowerConsumptionUnitUIAlert.OnProgressChangedListener() {
            @Override
            public int getPowerConsumptionUnit() {
                return powerConsumptionUnit;
            }

            @Override
            public void setPowerConsumptionUnit(int state) {
                presenter.setPowerConsumptionUnit(state);
            }

            @Override
            public void onSwitch(int index) {
                powerConsumptionUnit = index;
                viewModel.setPowerConsumptionUnit(index);
                presenter.setPowerConsumptionUnit(index);
                systemHandler.sendMessageDelayed(MessageConst.SYSTEM_POWER_CONSUMPTION_UNIT);
            }
        });
        // 单位设置
        UnitSettingUIAlert.setOnProgressChangedListener(new UnitSettingUIAlert.OnProgressChangedListener() {
            @Override
            public int getUnitSetting() {
                return presenter.getUnitSetting();
            }

            @Override
            public void setUnitSetting(int state) {
                presenter.setUnitSetting(state);
            }
        });
        // 温度单位
        TemperatureUnitUIAlert.setOnProgressChangedListener(new TemperatureUnitUIAlert.OnProgressChangedListener() {
            @Override
            public int getTemperatureUnit() {
                return presenter.getTemperatureUnit();
            }

            @Override
            public void setTemperatureUnit(int state) {
                presenter.setTemperatureUnit(state);
            }
        });
        // 设备名称
        DeviceInfoUIAlert.setOnProgressChangedListener(new DeviceInfoUIAlert.OnProgressChangedListener() {

            @Override
            public void setDeviceInfo(String info) {
                presenter.setDeviceInfo(info);
                binding.tvDeviceMsg.setText(info);
                Log.i(TAG, "binding= " + binding + "   tvDeviceMsg3= " + info);
                setHotName();
            }
        });
        // 恢复出厂设置 弹窗
        SystemDataResetUIAlert.setOnProgressChangedListener(new SystemDataResetUIAlert.OnProgressChangedListener() {
            @Override
            public void onSwitch(boolean flag) {

            }

            @Override
            public void factoryReset() {
                presenter.factoryReset();
            }
        });

        // 恢复出厂设置 未开卡弹窗
        SystemDataResetNoUserUIAlert.setOnProgressChangedListener(new SystemDataResetNoUserUIAlert.OnProgressChangedListener() {
            @Override
            public void factoryReset() {
                presenter.factoryReset();
            }
        });

        binding.vClick2EngineMode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (lastTime != 0 && ((System.currentTimeMillis() - lastTime) > 500)) {
                    lastTime = 0;
                    clickCount = 0;
                }
                if (clickCount < MAX_CLICKS) {
                    clickCount++;
                } else {
                    if (gotoEngineModeUIAlert == null) {
                        gotoEngineModeUIAlert = new GotoEngineModeUIAlert.Builder(mContext).setOnDialogResultListener(data -> {
                            if (data) {
                                presenter.gotoEngineMode();
                            } else {
                                EToast.show(mContext, "密码错误");
                            }
                        });
                    }
                    gotoEngineModeUIAlert.create().show();
                }
                lastTime = System.currentTimeMillis();
            }
        });
        // 恢复出厂设置
        binding.rlRestoreSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openSystemDataResetDialog();
            }
        });
        // 恢复出厂设置 蒙层
        binding.vwRestoreSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EToast.showToast(mContext, getString(R.string.str_system_international_privacy_statement_not_p_gear), 0, false);
            }
        });
        // 系统信息
        binding.llVersionMsg.setOnClickListener(v -> {
            openVersionInfoDialog();
        });
    }

    private void openVersionInfoDialog() {
        VersionMsgUIAlert versionMsgUIAlert = new VersionMsgUIAlert(mContext);
        versionMsgUIAlert.show();
    }

    private final ExecutorService executor = Executors.newCachedThreadPool();

    @Override
    protected void initData() {
//        Log.d(TAG, "initData called", new Throwable()); // 打印调用栈
        viewModel.initData();
        executor.execute(() -> {
            viewModel.setAnalysis(Prefs.get(PrefsConst.SYSTEM_ANALYSIS, 1));
            /**
             * 设置权限开启状态
             */
            // 设置摄像头授权开启状态
            viewModel.setPermissionSwitchCamera(Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA, 0));
            // 设置麦克风授权开启状态
            viewModel.setPermissionSwitchMicrophone(Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE, 0));
            // 设置位置授权开启状态
            viewModel.setPermissionSwitchLocation(Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION, 0));
            /**
             * 获取服务隐私协议同意情况
             */
            isServicePrivacyAgreement = CommonUtils.IntToBool(Prefs.get(PrefsConst.SYSTEM_SERVICE_PRIVACY_AGREEMENT, 0));
            /**
             * 单位设置
             */
            // 仪表单位
            viewModel.setFuelUnit(Prefs.get(PrefsConst.SYSTEM_INSTRUMENT_FUEL_UNIT, PrefsConst.DefaultValue.SYSTEM_INSTRUMENT_FUEL_UNIT));
            instrumentFuelUnit = Prefs.get(PrefsConst.SYSTEM_INSTRUMENT_FUEL_UNIT, PrefsConst.DefaultValue.SYSTEM_INSTRUMENT_FUEL_UNIT);
            // 胎压单位
            viewModel.setTirePressureUnit(Prefs.get(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, PrefsConst.DefaultValue.SYSTEM_TIRE_PRESSURE_UNIT));
            tirePressureUnit = Prefs.get(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, PrefsConst.DefaultValue.SYSTEM_TIRE_PRESSURE_UNIT);
            // 电耗单位
            viewModel.setPowerConsumptionUnit(Prefs.get(PrefsConst.SYSTEM_POWER_CONSUMPTION_UNIT, PrefsConst.DefaultValue.SYSTEM_POWER_CONSUMPTION_UNIT));
            powerConsumptionUnit = Prefs.get(PrefsConst.SYSTEM_POWER_CONSUMPTION_UNIT, PrefsConst.DefaultValue.SYSTEM_POWER_CONSUMPTION_UNIT);
            /**
             * 获取版本信息
             */
            getVersionInfo();
            viewModel.setSystemSoftwareVersion(Prefs.get(PrefsConst.SYSTEM_SYSTEM_SOFTWARE_VERSION, PrefsConst.DefaultValue.SYSTEM_SYSTEM_SOFTWARE_VERSION));
            viewModel.setSystemHardwareVersion(Prefs.get(PrefsConst.SYSTEM_SYSTEM_HARDWARE_VERSION, PrefsConst.DefaultValue.SYSTEM_SYSTEM_HARDWARE_VERSION));

            /**
             * 获取内存使用状况
             */
            getMemoryInfo();

//        //获取该设备名称

            TextView tvDeviceMsg = binding.tvDeviceMsg;
            Log.i(TAG, "binding= " + binding + "   tvDeviceMsg1= " + tvDeviceMsg);
            getActivity().runOnUiThread(() -> {
                tvDeviceMsg.setText(presenter.getDeviceInfo());
            });
        });
    }

    private void updatePowerConsumptionUnitUI(int powerConsumptionUnit) {
        if (powerConsumptionUnitUIAlert != null) {
            powerConsumptionUnitUIAlert.updatePowerConsumptionUnitUI(powerConsumptionUnit);
        }
    }

    private void updateTirePressureUnitUI(int tirePressureUnit) {
        if (tirePressureUnitUIAlert != null) {
            tirePressureUnitUIAlert.updateTirePressureUnitUI(tirePressureUnit);
        }
    }

    private void updateInstrumentFuelUnitUI(int instrumentFuelUnit) {
        if (instrumentFuelUnitUIAlert != null) {
            instrumentFuelUnitUIAlert.updateInstrumentFuelUnitUI(instrumentFuelUnit);
        }
    }

    private void updateSystemHardwareVersion(String s) {
        binding.tvSystemHardwareVersion.setText(s);
    }

    private void updateSystemSoftwareVersion(String s) {
        binding.tvSystemSoftwareVersion.setText(s);
    }

    private void initServicePrivacyAgreement() {
        // 设置服务隐私协议同意情况
        binding.tvServicePrivacyIsAgree.setText(
                isServicePrivacyAgreement ? getString(R.string.str_system_service_privacy_agreement_agree) : getString(R.string.str_system_service_privacy_agreement_refuse));
    }


    // 分析与改进点击UI
    private void switchAnalysis(int status) {
        binding.swAnalysis.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 摄像头权限开启状态
     *
     * @param status
     */
    private void updatePermissionSwitchCameraUI(Integer status) {
        if (permissionCameraUIAlert != null) {
            permissionCameraUIAlert.getBinding().swPermission.setChecked(status == 1);
        }
    }

    /**
     * 麦克风权限开启状态
     *
     * @param status
     */
    private void updatePermissionSwitchMicrophoneUI(Integer status) {
        if (permissionMicrophoneUIAlert != null) {
            permissionMicrophoneUIAlert.getBinding().swPermission.setChecked(status == 1);
        }
    }

    /**
     * 定位权限开启状态
     *
     * @param status
     */
    private void updatePermissionSwitchPositionUI(Integer status) {
        if (permissionPositionUIAlert != null) {
            permissionPositionUIAlert.getBinding().swPermission.setChecked(status == 1);
        }
    }

    private void openPermissionDialog() {
        if (permissionDialog != null && permissionDialog.isShowing()) {
            return;
        }

        if (permissionUIAlert == null) {
            permissionUIAlert = new PermissionUIAlert.Builder(mContext)
                    .setCancelable(true);
        }

        permissionDialog = permissionUIAlert.create();
        permissionDialog.show();

        isDialogShow = true;
        permissionDialog.setOnDismissListener(v -> {
            isDialogShow = false;
        });
    }

    private void openPermissionAppDialog(int position) {
        isDialogShow = true;
        if (position == 0) {
            if (permissionCameraUIAlert != null && permissionCameraUIAlert.isShowing() && isDialogShow) {
                return;
            }
            if (permissionCameraUIAlert == null) {
                permissionCameraUIAlert = new PermissionAppUIAlert.Builder(mContext, 0,
                        getString(R.string.str_system_permission_camera), getString(R.string.str_system_permission_camera_switch),
                        getString(R.string.str_system_permission_camera_link), getString(R.string.str_system_permission_camera_text_close), getString(R.string.str_system_permission_camera_text_open),
                        getString(R.string.str_system_permission_camera_app_close), getString(R.string.str_system_permission_camera_app_open));
            }
            permissionCameraUIAlert.create().show();
            permissionCameraUIAlert.dialog.setOnDismissListener(d -> {
                try {
                    if (permissionCameraUIAlert.isNormalDismiss()) {
                        openPermissionDialog();
                    }
                    permissionCameraUIAlert.setNormalDismiss(true);
                } catch (Exception e) {
                    EToast.showToast(getContext(), e.getMessage(), Toast.LENGTH_SHORT, true);
                }
            });
        } else if (position == 1) {
            if (permissionMicrophoneUIAlert != null && permissionMicrophoneUIAlert.isShowing() && isDialogShow) {
                return;
            }
            if (permissionMicrophoneUIAlert == null) {
                permissionMicrophoneUIAlert = new PermissionAppUIAlert.Builder(mContext, 1,
                        getString(R.string.str_system_permission_microphone), getString(R.string.str_system_permission_microphone_switch),
                        getString(R.string.str_system_permission_microphone_link), getString(R.string.str_system_permission_microphone_text_close), getString(R.string.str_system_permission_microphone_text_open),
                        getString(R.string.str_system_permission_microphone_app_close), getString(R.string.str_system_permission_microphone_app_open));

            }
            permissionMicrophoneUIAlert.create().show();
            permissionMicrophoneUIAlert.dialog.setOnDismissListener(d -> {
                try {
                    if (permissionMicrophoneUIAlert.isNormalDismiss()) {
                        openPermissionDialog();
                    }
                    permissionMicrophoneUIAlert.setNormalDismiss(true);
                } catch (Exception e) {
                    EToast.showToast(getContext(), e.getMessage(), Toast.LENGTH_SHORT, true);
                }
            });
        } else if (position == 2) {
            if (permissionPositionUIAlert != null && permissionPositionUIAlert.isShowing() && isDialogShow) {
                return;
            }
            if (permissionPositionUIAlert == null) {
                permissionPositionUIAlert = new PermissionAppUIAlert.Builder(mContext, 2,
                        getString(R.string.str_system_permission_location), getString(R.string.str_system_permission_location_switch),
                        getString(R.string.str_system_permission_location_link), getString(R.string.str_system_permission_location_text_close), getString(R.string.str_system_permission_location_text_open),
                        getString(R.string.str_system_permission_location_app_close), getString(R.string.str_system_permission_location_app_open));

            }
            permissionPositionUIAlert.create().show();
            permissionPositionUIAlert.dialog.setOnDismissListener(d -> {
                try {
                    if (permissionPositionUIAlert.isNormalDismiss()) {
                        openPermissionDialog();
                    }
                    permissionPositionUIAlert.setNormalDismiss(true);
                } catch (Exception e) {
                    EToast.showToast(getContext(), e.getMessage(), Toast.LENGTH_SHORT, true);
                }
            });
        }
    }

    private void openPermissionSpecialDialog() {
        if (permissionSpecialUIAlert != null && permissionSpecialUIAlert.isShowing()) {
            return;
        }
        if (permissionSpecialUIAlert == null) {
            permissionSpecialUIAlert = new PermissionSpecialUIAlert.Builder(mContext);
        }
        permissionSpecialUIAlert.create().show();
        isDialogShow = true;
        permissionSpecialUIAlert.dialog.setOnDismissListener(v -> {
            isDialogShow = false;
            openPermissionDialog();
        });
    }

    private void openPrivacyStatement() {
        if (privacyStatementUIAlert != null && privacyStatementUIAlert.isShowing()) {
            return;
        }
        if (privacyStatementUIAlert == null) {
            privacyStatementUIAlert = new PrivacyStatementUIAlert.Builder(mContext);
        }
        privacyStatementUIAlert.create().show();
        isDialogShow = true;
        privacyStatementUIAlert.dialog.setOnDismissListener(v -> {
            isDialogShow = false;
        });
    }

    private void openBasicPrivacyStatement() {
        if (basicPrivacyAgreementUIAlert != null && basicPrivacyAgreementUIAlert.isShowing()) {
            return;
        }
        if (basicPrivacyAgreementUIAlert == null) {
            basicPrivacyAgreementUIAlert = new BasicPrivacyAgreementUIAlert.Builder(mContext);
        }
        basicPrivacyAgreementUIAlert.create().show();
        isDialogShow = true;
        basicPrivacyAgreementUIAlert.dialog.setOnDismissListener(v -> {
            isDialogShow = false;
        });
    }

    private void openServicePrivacyStatement() {
        if (servicePrivacyAgreementUIAlert != null && servicePrivacyAgreementUIAlert.isShowing()) {
            return;
        }
        if (servicePrivacyAgreementUIAlert == null) {
            servicePrivacyAgreementUIAlert = new ServicePrivacyAgreementUIAlert.Builder(mContext);
        }
        servicePrivacyAgreementUIAlert.create().show();
        isDialogShow = true;
        servicePrivacyAgreementUIAlert.dialog.setOnDismissListener(v -> {
            isDialogShow = false;
        });
    }

    private void openRealTimeData() {
        if (realTimeDataUIAlert != null && realTimeDataUIAlert.isShowing()) {
            return;
        }
        if (realTimeDataUIAlert == null) {
            realTimeDataUIAlert = new SystemDataUIAlert.Builder(mContext);
        }
        realTimeDataUIAlert.create(getString(R.string.str_system_data_real_time_title),
                0).show();
        isDialogShow = true;
        realTimeDataUIAlert.dialog.setOnDismissListener(v -> {
            isDialogShow = false;
        });
    }

    private void openDiagnosticData() {
        if (diagnosticDataUIAlert != null && diagnosticDataUIAlert.isShowing()) {
            return;
        }
        if (diagnosticDataUIAlert == null) {
            diagnosticDataUIAlert = new SystemDataUIAlert.Builder(mContext);
        }
        diagnosticDataUIAlert.create(getString(R.string.str_system_data_diagnostic_title),
                1).show();
    }

    private void openInternationalPrivacyStatement() {
        if (internationalPrivacyAgreementUIAlert != null && internationalPrivacyAgreementUIAlert.isShowing()) {
            return;
        }
        if (internationalPrivacyAgreementUIAlert == null) {
            internationalPrivacyAgreementUIAlert = new InternationalPrivacyAgreementUIAlert.Builder(mContext);
        }
        internationalPrivacyAgreementUIAlert.create().show();
    }

    private void openDatetimeSettingDialog() {
        if (datetimeSettingUIAlert != null && datetimeSettingUIAlert.isShowing()) {
            return;
        }
        if (datetimeSettingUIAlert == null) {
            datetimeSettingUIAlert = new DatetimeSettingUIAlert(mContext);
        }
        datetimeSettingUIAlert.show();
    }

    private void openInstrumentFuelUnitDialog() {
        if (instrumentFuelUnitUIAlert != null && instrumentFuelUnitUIAlert.isShowing()) {
            return;
        }
        if (instrumentFuelUnitUIAlert == null) {
            instrumentFuelUnitUIAlert = new InstrumentFuelUnitUIAlert.Builder(mContext);
        }
        instrumentFuelUnitUIAlert.create().show();
    }

    private void openTirePressureUnitDialog() {
        if (tirePressureUnitUIAlert != null && tirePressureUnitUIAlert.isShowing()) {
            return;
        }
        if (tirePressureUnitUIAlert == null) {
            tirePressureUnitUIAlert = new TirePressureUnitUIAlert.Builder(mContext);
        }
        tirePressureUnitUIAlert.create().show();
    }

    private void openPowerConsumptionUnitDialog() {
        if (powerConsumptionUnitUIAlert != null && powerConsumptionUnitUIAlert.isShowing()) {
            return;
        }
        if (powerConsumptionUnitUIAlert == null) {
            powerConsumptionUnitUIAlert = new PowerConsumptionUnitUIAlert.Builder(mContext);
        }
        powerConsumptionUnitUIAlert.create().show();
    }

    private void openUnitSettingDialog() {
        if (unitSettingUIAlert != null && unitSettingUIAlert.isShowing()) {
            return;
        }
        if (unitSettingUIAlert == null) {
            unitSettingUIAlert = new UnitSettingUIAlert.Builder(mContext);
        }
        unitSettingUIAlert.create().show();
    }

    private void openTemperatureUnitDialog() {
        if (temperatureUnitUIAlert != null && temperatureUnitUIAlert.isShowing()) {
            return;
        }
        if (temperatureUnitUIAlert == null) {
            temperatureUnitUIAlert = new TemperatureUnitUIAlert.Builder(mContext);
        }
        temperatureUnitUIAlert.create().show();
    }

    private void openDeviceInfoDialog() {
        if (deviceInfoUIAlert != null && deviceInfoUIAlert.isShowing()) {
            return;
        }
        if (deviceInfoUIAlert == null) {
            String device_name = binding.tvDeviceMsg.getText().toString();
            deviceInfoUIAlert = new DeviceInfoUIAlert.Builder(mContext, device_name);
        }
        deviceInfoUIAlert.create().show();
    }

    private void openVersionMsgDialog() {
        // 系统信息视频
        // systemVideoUIAlert = new SystemVideoUIAlert.Builder(mContext);
        // systemVideoUIAlert.create().show();
    }

    private void openGestureNavigationDialog() {
        if (gestureNavigationUIAlert != null && gestureNavigationUIAlert.isShowing()) {
            return;
        }
        if (gestureNavigationUIAlert == null) {
            gestureNavigationUIAlert = new GestureNavigationUIAlert.Builder(mContext);
        }
        gestureNavigationUIAlert.create().show();
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_version_msg:
                openVersionMsgDialog();
                break;
            case R.id.rl_device_msg:
                openDeviceInfoDialog();
                TextView tvDeviceMsg = binding.tvDeviceMsg;
                Log.i(TAG, "binding= " + binding + "   tvDeviceMsg2= " + tvDeviceMsg);

//                binding.tvDeviceMsg.setText("xxx=" + SystemClock.uptimeMillis());
                CharSequence text = binding.tvDeviceMsg.getText();
                Log.d(TAG, "tvDeviceMsg onClick: " + text);
                break;
            case R.id.rl_storage_msg:

                break;
            case R.id.sw_analysis:
                if (viewModel.getAnalysis().getValue() == 1) {
                    viewModel.setAnalysis(0);
                    presenter.setSwAnalysis(0);
                } else {
                    viewModel.setAnalysis(1);
                    presenter.setSwAnalysis(1);
                }
                systemHandler.sendMessageDelayed(MessageConst.SYSTEM_ANALYSIS);
                break;
            case R.id.iv_analysis_tips:

                break;
            case R.id.rl_permission:
                Log.d(TAG, "点击权限");
                openPermissionDialog();
                break;
            case R.id.rl_privacy_policy:
                openPrivacyStatement();
                break;
            case R.id.rl_basic_privacy:
                openBasicPrivacyStatement();
                break;
            case R.id.rl_service_privacy:
                openServicePrivacyStatement();
                break;
            case R.id.rl_real_time_data:
                if (isServicePrivacyAgreement) {
                    openRealTimeData();
                } else {
                    EToast.showToast(mContext, getString(R.string.str_system_permission_service_privacy_agreement_tips), 0, false);
                }
                break;
            case R.id.rl_diagnostic_data:
                if (isServicePrivacyAgreement) {
                    openDiagnosticData();
                } else {
                    EToast.showToast(mContext, getString(R.string.str_system_permission_service_privacy_agreement_tips), 0, false);
                }
                break;
            case R.id.rl_privacy_statement:
                if (presenter.isPGear()) {
                    openInternationalPrivacyStatement();
                } else {
                    EToast.showToast(mContext, getString(R.string.str_system_international_privacy_statement_not_p_gear), 0, false);
                }
                break;
            case R.id.rl_gesture_navigation:
                if (gestureNavigationUIAlert == null) {
                    gestureNavigationUIAlert = new GestureNavigationUIAlert.Builder(mContext);
                }
                gestureNavigationUIAlert.create().show();
                break;
            case R.id.rl_language_setting:
                if (languageSettingUIAlert == null) {
                    languageSettingUIAlert = new LanguageSettingUIAlert.Builder(mContext);
                }
                languageSettingUIAlert.create().show();
                break;
            case R.id.rl_datetime_setting:
                openDatetimeSettingDialog();
                break;
            case R.id.rl_instrument_fuel_unit:  // 仪表油耗单位
                openInstrumentFuelUnitDialog();
                break;
            case R.id.rl_tire_pressure_unit: // 胎压单位
                openTirePressureUnitDialog();
                break;
            case R.id.rl_power_consumption_unit: // 电耗单位
                openPowerConsumptionUnitDialog();
                break;
            case R.id.rl_unit_setting: // 单位设置
                openUnitSettingDialog();
                break;
            case R.id.rl_temperature_setting:
                openTemperatureUnitDialog();
                break;
            case R.id.rl_user_feedback:
                // 用户反馈
                openUserFeedbackDialog();
                break;
            default:
                break;
        }
    }

    private void openUserFeedbackDialog() {
        if (userFeedBackUIAlert == null) {
            userFeedBackUIAlert = new UserFeedBackUIAlert.Builder(mContext);
        }
        AccountInfoManager.getInstance().initialize(mContext);
        UserFeedBackUIAlert userFeedBackUIAlert1 = userFeedBackUIAlert.create();
        if (!userFeedBackUIAlert1.isShowing()) {
            userFeedBackUIAlert1.show();
        }
    }

    private void openSystemDataResetDialog() {
        String simStatus = null;
        simStatus = Settings.System.getString(mContext.getContentResolver(), "sys.tsp.sim.activation");
        Log.d(TAG, "开卡状态：UserStatus: " + simStatus);
        if (simStatus != null && simStatus.equals("0")) {
            UserInfo userInfo = AccountInfoManager.getInstance().getUserInfo(mContext);
            String carOwner = userInfo.getCarOwner();
            if (carOwner == null) {
                AccountInfoManager.getInstance().startLogin(mContext);
            } else {
                if (systemDataResetUIAlert != null && systemDataResetUIAlert.isShowing()) {
                    return;
                }
                if (systemDataResetUIAlert == null) {
                    systemDataResetUIAlert = new SystemDataResetUIAlert.Builder(mContext);
                }
                systemDataResetUIAlert.create().show();
            }
        } else {
            if (systemDataResetNoUserUIAlert == null) {
                systemDataResetNoUserUIAlert = new SystemDataResetNoUserUIAlert.Builder(mContext);
            }
            systemDataResetNoUserUIAlert.create().show();
        }
    }


    /**
     * 获取内存使用情况
     */
    public void getMemoryInfo() {
        StatFs statFs = new StatFs(mContext.getDataDir().getPath());
        long blockSizeLong = statFs.getBlockSizeLong();
        long blockCountLong = statFs.getBlockCountLong();
        long availableBlocksLong = statFs.getAvailableBlocksLong();

        long totalSize = blockSizeLong * blockCountLong;
        long availableSize = blockSizeLong * availableBlocksLong;

        getActivity().runOnUiThread(() -> {
            binding.tvStorageMsg.setText(formatGB(totalSize - availableSize) + " GB / " + formatGB(totalSize) + " GB");
        });
    }

    private static String formatGB(long bytes) {
        double gb = bytes / (1024.0 * 1024.0 * 1024.0);
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(gb);
    }

    private void processTargetDialogEvent(TargetDialogInfo targetDialog) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= " + targetDialog);
        if (targetDialog == null) {
            return;
        }
        //具体Tab索引
        if (targetDialog.getTargetTab() == MainActivity.MainTabIndex.SYSTEM) {
            switch (targetDialog.getTargetDialog()) {
                //常量-具体窗口
                //常量-操作
                case SystemFragment.POWER_CONSUMPTION_UNIT:
                    if (targetDialog.getOperation() == 1) {
                        openPowerConsumptionUnitDialog();
                    } else {
                        powerConsumptionUnitUIAlert.dialog.dismiss();
                    }
                    break;
                case SystemFragment.GAUGE_FUEL_CONSUMPTION:
                    if (targetDialog.getOperation() == 1) {
                        openInstrumentFuelUnitDialog();
                    } else {
                        instrumentFuelUnitUIAlert.dialog.dismiss();
                    }
                    break;
                case SystemFragment.GESTURE_NAVIGATION:
                    if (targetDialog.getOperation() == 1) {
                        openGestureNavigationDialog();
                    } else {
                        gestureNavigationUIAlert.dialog.dismiss();
                    }
                    break;
                case SystemFragment.RESET_EXIT_SETTING:
                    if (targetDialog.getOperation() == 1) {
                        openSystemDataResetDialog();
                    } else {
                        systemDataResetUIAlert.dialog.dismiss();
                    }
                    break;
                case SystemFragment.DEVICE_INFORMATION:
                    if (targetDialog.getOperation() == 1) {
                        openDeviceInfoDialog();
                    } else {
                        deviceInfoUIAlert.dialog.dismiss();
                    }
                    break;
                case SystemFragment.PRIVACY_SETTING:
                    if (targetDialog.getOperation() == 1) {
                        openPrivacyStatement();
                    } else {
                        privacyStatementUIAlert.dialog.dismiss();
                    }
            }
        }
    }

    @Override
    public void onStop() {
        if (PowerConsumptionUnitUIAlert.isShow) {
            powerConsumptionUnitUIAlert.dialog.dismiss();
        }
        if (InstrumentFuelUnitUIAlert.isShow) {
            instrumentFuelUnitUIAlert.dialog.dismiss();
        }
        if (GestureNavigationUIAlert.isShow) {
            gestureNavigationUIAlert.dialog.dismiss();
        }
        if (SystemDataResetUIAlert.isShow) {
            systemDataResetUIAlert.dialog.dismiss();
        }
        if (DeviceInfoUIAlert.isShow) {
            deviceInfoUIAlert.dialog.dismiss();
        }
        if (PrivacyStatementUIAlert.isShow) {
            privacyStatementUIAlert.dialog.dismiss();
        }
        super.onStop();
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: ");
        // 埋点上报数据
        DataPoint dataPoint = new DataPoint();
        dataPoint.setId(CommonConst.DataPoint.id);
        dataPoint.setEventId(CommonConst.EventId.Vehicle_Set);
        dataPoint.setTimestamp(System.currentTimeMillis());
        dataPoint.setSupplierCode(CommonConst.DataPoint.supplierCode);
        dataPoint.setPlatformCode(CommonConst.DataPoint.platformCode);
        dataPoint.setNodeType(LogDataUtil.NODE_TYPE_ADD_OPERATION);
        // 上报数据驾驶页面
        ArrayList<Content> list = getData();
        if (!list.isEmpty()) {
            dataPoint.setContent(list);
        }

        LiveEventBus
                .get(DataPointReportLifeCycle.KEY_DATA_POINT)
                .post(dataPoint);
    }

    private ArrayList<Content> getData() {
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        int hasReset = 0;
        // 恢复出厂设置
        try {
            // 检查标记值
            hasReset = Settings.Global.getInt(
                    mContext.getContentResolver(),
                    "has_factory_reset"
            );
            // 如果需要，也可以获取时间戳
//            Settings.Global.getLong(
//                    mContext.getContentResolver(),
//                    "last_reset_timestamp",
//                    0L
//            );
        } catch (Settings.SettingNotFoundException e) {
            // 如果设置不存在（首次使用），返回false
            hasReset = 0;
        }
        dataList.add(reportData(CommonConst.CodeId.ZB141211, CommonConst.Att.RestoreFSw, String.valueOf(hasReset)));
        return dataList;
    }

    private Content reportData(String attributeId, String locationId, String attributeValue) {
        Content content = new Content();
        content.setAttributeId(attributeId);
        content.setLocationId(locationId);
        content.setAttributeValue(attributeValue);
        return content;
    }
}
