package com.bitech.vehiclesettings.view.newenergy

import android.content.Context
import android.graphics.Color
import android.graphics.DashPathEffect
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.WindowManager
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.lifecycleScope
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogEnergyCurveBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.IFillFormatter
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * @Description: 能耗曲线
 **/
class EnergyCurveDialog(context: Context) : BaseDialog(context), LifecycleOwner {

    private val lifecycleRegistry = LifecycleRegistry(this)

    override val lifecycle: Lifecycle
        get() = lifecycleRegistry

    private var carNewEnergyManager = NewEnergyViewModel.newEnergyManager

    private lateinit var binding: DialogEnergyCurveBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleRegistry.currentState = Lifecycle.State.CREATED
        LogUtil.d(TAG, "onCreate : ")
        binding = DialogEnergyCurveBinding.inflate(layoutInflater)
        // 绑定自定义dialog视图
        setContentView(binding.root)
        // 初始化页面视图
        initView()
        // 初始化页面监听
        intiListener()
        // 初始化页面数据
        initData()
    }

    override fun onStart() {
        super.onStart()
        lifecycleRegistry.currentState = Lifecycle.State.STARTED
    }

    override fun onStop() {
        lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
        super.onStop()
    }

    /**
     * 初始化页面视图.
     *
     */
    private fun initView() {
        LogUtil.d(TAG, "initView : ")
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG
        attributes?.width = 1992
        attributes?.height = 1128
        window?.attributes = attributes
        isShow = true
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
        isShow = false
    }

    /**
     * 初始化页面监听.
     *
     */
    private fun intiListener() {
        LogUtil.d(TAG, "intiListener : ")
        // 页面相关事件监听设置
        binding.apply {
            tvElectricCurve.setOnClickListener {
                LogUtil.d(TAG, "onClick tvElectricCurve")
                binding.tabSelectedIndex = 0
                setCurveText()
            }
            tvFuelCurve.setOnClickListener {
                LogUtil.d(TAG, "onClick tvFuelCurve")
                binding.tabSelectedIndex = 1
                setCurveText()
            }
            tvFuel10km.setOnClickListener {
                LogUtil.d(TAG, "onClick tvFuel10km")
                if (binding.fuelSelectedIndex == 0) {
                    return@setOnClickListener
                }
                binding.fuelSelectedIndex = 0
                setCurveText()

                initChartView(binding.lineChartFuel, false, 10f)
                setData(binding.lineChartFuel, 50, false)
            }
            tvFuel25km.setOnClickListener {
                LogUtil.d(TAG, "onClick tvFuel25km")
                if (binding.fuelSelectedIndex == 1) {
                    return@setOnClickListener
                }
                binding.fuelSelectedIndex = 1
                setCurveText()

                initChartView(binding.lineChartFuel, false, 25f)
                setData(binding.lineChartFuel, 100, false)
            }
            tvFuel50km.setOnClickListener {
                LogUtil.d(TAG, "onClick tvFuel50km")
                if (binding.fuelSelectedIndex == 2) {
                    return@setOnClickListener
                }
                binding.fuelSelectedIndex = 2
                setCurveText()

                initChartView(binding.lineChartFuel, false, 50f)
                setData(binding.lineChartFuel, 150, false)
            }
        }
    }

    /**
     * 初始化页面数据.
     *
     */
    private fun initData() {
        lifecycleScope.launch(Dispatchers.Default) {
            LogUtil.d(TAG, "initData : ")

            //100KM平均电耗
            val energyConsumptionPer50 =
                carNewEnergyManager.energyConsumptionPer50Km.takeIf { it >= 0 && it != Float.MIN_VALUE }
                    ?: ERROR_VALUE_TEXT
            //50KM平均油耗
            val energyFuelPer50 =
                carNewEnergyManager.energyFuelPer50Km.takeIf { it >= 0 && it != Float.MIN_VALUE }
                    ?: ERROR_VALUE_TEXT
            LogUtil.d(TAG, "energyConsumptionPer50Km : $energyConsumptionPer50 && energyFuelPer50Km : $energyFuelPer50")

            withContext(Dispatchers.Main) {

                binding.apply {
                    binding.tabSelectedIndex = 0
                    binding.fuelSelectedIndex = 0
                    setCurveText()

                    initChartView(binding.lineChartElectric, true, 100f)
                    setData(binding.lineChartElectric, 100, true)

                    initChartView(binding.lineChartFuel, false, 10f)
                    setData(binding.lineChartFuel, 50, false)
                }
            }
        }
    }

    fun initChartView(lineChart: LineChart, electric: Boolean, xAxisValue: Float) {
        // // Chart Style // //
        val chart = lineChart
        // background color
        chart.setBackgroundColor(context.getColor(R.color.color_dialog_border_bg))
        // disable description text
        chart.getDescription().setEnabled(false)
        // enable touch gestures
        chart.setTouchEnabled(true)
        // set listeners
        chart.setDrawGridBackground(false)
        // enable scaling and dragging
        chart.setDragEnabled(false)
        chart.setScaleEnabled(false)
        // chart.setScaleXEnabled(true);
        // chart.setScaleYEnabled(true);
        // force pinch zoom along both axis
        chart.setPinchZoom(false)

        // // X-Axis Style // //
        val xAxis: XAxis = chart.xAxis
        xAxis.textSize = 20f
        xAxis.textColor = context.getColor(R.color.text_color_1)
        xAxis.labelCount = 5
        xAxis.position = XAxis.XAxisPosition.BOTTOM
        xAxis.axisMaximum = xAxisValue
        xAxis.axisMinimum = 0f
        // vertical grid lines
        xAxis.enableGridDashedLine(10f, 10f, 0f)

        // // Y-Axis Style // //
        val yAxis: YAxis = chart.axisLeft
        yAxis.textSize = 20f
        yAxis.textColor = context.getColor(R.color.text_color_1)
        yAxis.labelCount = if (electric) 5 else 4
        // disable dual axis (only use LEFT axis)
        chart.axisRight.isEnabled = false
        yAxis.setDrawAxisLine(false)
        // horizontal grid lines
        yAxis.enableGridDashedLine(10f, 10f, 0f)
        // axis range
        yAxis.axisMaximum = if (electric) 80f else 20f
        yAxis.axisMinimum = if (electric) -20f else 0f

        // draw points over time
        chart.animateX(1500)

        // get the legend (only possible after setting data)
        val l = chart.legend
        // draw legend entries as lines
        l.form = Legend.LegendForm.NONE

    }

    private fun setData(lineChart: LineChart, count: Int, electric: Boolean) {
        val chart = lineChart

        val values = ArrayList<Entry>()

        val range = if (electric) 50f else 20f

        for (i in 0 until count) {
            val value = (Math.random() * range).toFloat()
            values.add(Entry(i.toFloat(), value))
        }

        val set1: LineDataSet

        if (chart.getData() != null &&
            chart.getData().getDataSetCount() > 0
        ) {
            set1 = chart.getData().getDataSetByIndex(0) as LineDataSet
            set1.values = values
            set1.notifyDataSetChanged()
            chart.getData().notifyDataChanged()
            chart.notifyDataSetChanged()
        } else {
            // create a dataset and give it a type
            set1 = LineDataSet(values, "")

            set1.mode = LineDataSet.Mode.CUBIC_BEZIER
            set1.cubicIntensity = 0.2f
            set1.setDrawFilled(true)
            set1.setDrawCircles(false)

            set1.setDrawIcons(false)

            // black lines and points
            set1.color = if (electric) context.getColor(R.color.chart_line_green) else context.getColor(R.color.chart_line_blue)
            set1.fillColor = Color.BLACK

            // line thickness and point size
            set1.lineWidth = 1f
            set1.circleRadius = 3f

            // draw points as solid circles
            set1.setDrawCircleHole(false)

            // customize legend entry
            set1.formLineWidth = 1f
            set1.formLineDashEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f)
            set1.formSize = 15f

            // text size of values
            set1.valueTextSize = 18f

            // draw selection line as dashed
            set1.enableDashedHighlightLine(10f, 5f, 0f)

            // set the filled area
            set1.setDrawFilled(true)
            set1.fillFormatter =
                IFillFormatter { dataSet, dataProvider -> chart.getAxisLeft().getAxisMinimum() }

            // set color of filled area
            // drawables only supported on api level 18 and above
            val drawable = ContextCompat.getDrawable(
                context,
                if (electric) R.drawable.fade_green else R.drawable.fade_blue
            )
            set1.fillDrawable = drawable

            val dataSets = ArrayList<ILineDataSet>()
            dataSets.add(set1) // add the data sets

            // create a data object with the data sets
            val data = LineData(dataSets)
            data.setDrawValues(false)

            // set data
            chart.setData(data)
        }
    }

    fun setCurveText() {
        val valueText = if (binding.tabSelectedIndex == 0) {
            "12.8 kW·h/100km"
        } else {
            "5.8 L/100km"
        }
        val descResId = if (binding.tabSelectedIndex == 0) {
            R.string.ne_average_power_consumption_100km
        } else {
            when (binding.fuelSelectedIndex) {
                0 -> R.string.ne_average_fuel_consumption_10km
                1 -> R.string.ne_average_fuel_consumption_25km
                2 -> R.string.ne_average_fuel_consumption_50km
                else -> R.string.ne_average_fuel_consumption_10km
            }
        }
        binding.tvCurveValueDesc.text = context.getString(descResId)
        setStyledText(binding.tvCurveValue, valueText)
    }

    fun setStyledText(textView: TextView, text: String) {
        val colorSpan = ForegroundColorSpan(context.getColor(R.color.text_color_2))
        val sizeSpan = AbsoluteSizeSpan(32)
        val spannableString = SpannableString(text)

        val index = if (binding.tabSelectedIndex == 0) {
            10
        } else {
            7
        }
        spannableString.setSpan(
            sizeSpan,
            text.length - index,
            text.length,
            Spannable.SPAN_EXCLUSIVE_INCLUSIVE
        )
        spannableString.setSpan(
            colorSpan,
            text.length - index,
            text.length,
            Spannable.SPAN_EXCLUSIVE_INCLUSIVE
        )
        textView.text = spannableString
    }

    /**
     * 弹窗销毁时调用.
     *
     */
    override fun dismiss() {
        LogUtil.d(TAG, "dismiss : ")
        super.dismiss()
        isShow = false
    }

    companion object {
        // 日志标志位
        private const val TAG = "EnergyCurveDialog"
        private const val ERROR_VALUE_TEXT = "---"
        var isShow = false
    }
}