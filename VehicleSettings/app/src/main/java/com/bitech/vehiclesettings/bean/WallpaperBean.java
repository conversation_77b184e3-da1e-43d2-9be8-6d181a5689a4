package com.bitech.vehiclesettings.bean;
import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;

import com.bitech.vehiclesettings.R;

import java.net.URI;

public class WallpaperBean implements Parcelable {
    private Uri uri;
    private int id;
    private int name;
    private boolean isSelected;

    public WallpaperBean(Uri uri, int name) {
        this.uri = uri;
        this.name = name;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public WallpaperBean(Uri uri) {
        this.uri = uri;
        this.name = R.string.str_wrapper_null;
    }

    public WallpaperBean(int id, int name) {
        this.id = id;
        this.name = name;
    }

    protected WallpaperBean(Parcel in) {
        name = in.readInt();
    }

    public static final Creator<WallpaperBean> CREATOR = new Creator<WallpaperBean>() {
        @Override
        public WallpaperBean createFromParcel(Parcel in) {
            return new WallpaperBean(in);
        }

        @Override
        public WallpaperBean[] newArray(int size) {
            return new WallpaperBean[size];
        }
    };

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(name);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public Uri getUri() {
        return uri;
    }

    public void setUri(Uri uri) {
        this.uri = uri;
    }

    public int getName() {
        return name;
    }

    public void setName(int name) {
        this.name = name;
    }
}
