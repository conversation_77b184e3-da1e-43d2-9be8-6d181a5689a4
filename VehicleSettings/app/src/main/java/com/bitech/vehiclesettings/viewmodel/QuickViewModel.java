package com.bitech.vehiclesettings.viewmodel;

import android.util.Log;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenter;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenterListener;
import com.bitech.vehiclesettings.utils.PrefsConst;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class QuickViewModel extends ViewModel {
    // 中控锁
    private final MutableLiveData<Integer> centralLocking = new MutableLiveData<Integer>(PrefsConst.DefaultValue.Q_CENTER_LOCK);
    // 后尾门
    private final MutableLiveData<Integer> rearTailgate = new MutableLiveData<>(PrefsConst.DefaultValue.Q_REAR_TAIL_GATE);
    // 后视镜折叠
    private final MutableLiveData<Integer> rearMirrorFold = new MutableLiveData<>(PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD);
    // 车窗
    private final MutableLiveData<Integer> windowDesc = new MutableLiveData<>(0);
    // 车窗返回
    private final MutableLiveData<Integer> windowReback = new MutableLiveData<>(0);
    // 车窗状态
    private final MutableLiveData<Integer> windowState = new MutableLiveData<>(PrefsConst.DefaultValue.Q_WINDOW);
    // 车窗锁
    private final MutableLiveData<Integer> windowLock = new MutableLiveData<>(PrefsConst.DefaultValue.Q_WINDOW_LOCK);
    // 遮阳帘 Sunshade
    private final MutableLiveData<Integer> sunshade = new MutableLiveData<>(PrefsConst.DefaultValue.Q_SUNSHADE);
    // 电动尾翼
    private final MutableLiveData<Integer> autoTail = new MutableLiveData<>(PrefsConst.DefaultValue.Q_AUTO_TAIL);
    // 天窗
    private final MutableLiveData<Integer> skyWindow = new MutableLiveData<>(PrefsConst.DefaultValue.Q_SKY_WINDOW);
    // 感应靠近解锁
    private final MutableLiveData<Integer> approachingUnlock = new MutableLiveData<>(PrefsConst.DefaultValue.Q_APPROACHING_UNLOCK);
    // 感应离车上锁
    private final MutableLiveData<Integer> departureLocking = new MutableLiveData<>(PrefsConst.DefaultValue.Q_DEPARTURE_LOCKING);
    // 锁车自动升窗
    private final MutableLiveData<Integer> lockAutoRaiseWindow = new MutableLiveData<>(PrefsConst.DefaultValue.Q_LOCK_AUTO_RAISE_WINDOW);
    // 锁车收起遮阳帘
    private final MutableLiveData<Integer> lockCarSunroofShade = new MutableLiveData<>(PrefsConst.DefaultValue.Q_LOCK_CAR_SUNROOF_SHADE);
    // 设防提示
    private final MutableLiveData<Integer> defenseReminder = new MutableLiveData<>(PrefsConst.DefaultValue.Q_DEFENSE_REMINDER);
    // 左儿童锁
    private final MutableLiveData<Integer> leftChildLock = new MutableLiveData<>(PrefsConst.DefaultValue.Q_LEFT_CHILD_LOCK);
    // 右儿童锁
    private final MutableLiveData<Integer> rightChildLock = new MutableLiveData<>(PrefsConst.DefaultValue.Q_RIGHT_CHILD_LOCK);
    // 自动落锁
    private final MutableLiveData<Integer> automaticLocking = new MutableLiveData<>(PrefsConst.DefaultValue.Q_AUTOMATIC_LOCKING);
    // 驻车自动解锁
    private final MutableLiveData<Integer> automaticParkingUnlock = new MutableLiveData<>(PrefsConst.DefaultValue.Q_AUTOMATIC_PARKING_UNLOCK);
    // 后视镜自动折叠
    private final MutableLiveData<Integer> autoRearMirrorFold = new MutableLiveData<>(PrefsConst.DefaultValue.Q_AUTO_REAR_MIRROR_FOLD);
    // 雨天自动加热外后视镜
    private final MutableLiveData<Integer> autoHotRearMirror = new MutableLiveData<>(PrefsConst.DefaultValue.Q_AUTO_HOT_REAR_MIRROR);
    // 倒车时后视镜自动调节
    private final MutableLiveData<Integer> backRearAdjust = new MutableLiveData<>(PrefsConst.DefaultValue.Q_BACK_AUTO_REAR_MIRROR_ADJUST);
    // 座椅便携进入/退出
    private final MutableLiveData<Integer> seatPortable = new MutableLiveData<>(PrefsConst.DefaultValue.Q_SEAT_PORTABLE);
    // 后尾门高度
    private final MutableLiveData<Integer> hudRoate = new MutableLiveData<Integer>(PrefsConst.DefaultValue.Q_REAR_TAILGATE_ROATE);
    // 雨刮器灵敏度
    private final MutableLiveData<Integer> wiperSensitivity = new MutableLiveData<>(PrefsConst.DefaultValue.Q_WIPER_SENS);
    // 副驾安全气囊
    private final MutableLiveData<Integer> driveAirbag = new MutableLiveData<>(PrefsConst.DefaultValue.Q_DRIVE_AIR_BAG);
    // 后排屏
    private final MutableLiveData<Integer> rearScreenControl = new MutableLiveData<>(PrefsConst.DefaultValue.Q_REAR_SCREEN_CONTROL);
    // 加油小门
    private final MutableLiveData<Integer> refuelSmallDoor = new MutableLiveData<>(PrefsConst.DefaultValue.Q_REFUEL_SMALL_DOOR);
    // 后视镜调节
    private final MutableLiveData<Integer> rearMirror = new MutableLiveData<>(PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD_DIALOG);
    // 遮阳帘前排全开
    private final MutableLiveData<Integer> sunshadeFrontOpen = new MutableLiveData<>(PrefsConst.DefaultValue.Q_SUNSHADE);
    // 遮阳帘前排全关
    private final MutableLiveData<Integer> sunshadeFrontClose = new MutableLiveData<>(PrefsConst.DefaultValue.Q_SUNSHADE);
    // 遮阳帘后排全开
    private final MutableLiveData<Integer> sunshadeRearOpen = new MutableLiveData<>(PrefsConst.DefaultValue.Q_SUNSHADE);
    // 遮阳帘后排全关
    private final MutableLiveData<Integer> sunshadeRearClose = new MutableLiveData<>(PrefsConst.DefaultValue.Q_SUNSHADE);

    public QuickPresenterListener quickPresenter;
    private final ExecutorService executor = Executors.newCachedThreadPool();

    public void initData() {
        executor.execute(() -> {
            centralLocking.postValue(quickPresenter.getCentralLocking());
            rearTailgate.postValue(quickPresenter.getRearTailGate());
            rearMirrorFold.postValue(quickPresenter.getRearMirror());
            windowState.postValue(quickPresenter.getWindow());
            windowLock.postValue(quickPresenter.getWindowLock());
            sunshade.postValue(quickPresenter.getSunshade());
            autoTail.postValue(quickPresenter.getAutoTail());
            skyWindow.postValue(quickPresenter.getSkyWindow());
            approachingUnlock.postValue(quickPresenter.getApproachingUnlock());
            departureLocking.postValue(quickPresenter.getDepartureLocking());
            lockAutoRaiseWindow.postValue(quickPresenter.getLockAutoRaiseWindow());
            lockCarSunroofShade.postValue(quickPresenter.getLockCarSunRoofShadeClose());
            defenseReminder.postValue(quickPresenter.getDefenseReminder());
            leftChildLock.postValue(quickPresenter.getLeftChildLock());
            rightChildLock.postValue(quickPresenter.getRightChildLock());
            automaticLocking.postValue(quickPresenter.getAutomaticLocking());
            automaticParkingUnlock.postValue(quickPresenter.getAutomaticParkingUnlock());
            autoRearMirrorFold.postValue(quickPresenter.getAutoRearMirrorFold());
            autoHotRearMirror.postValue(quickPresenter.getAutoHotRearMirror());
            backRearAdjust.postValue(quickPresenter.getBackAutoRearMirrorAdjust());
            seatPortable.postValue(quickPresenter.getSeatPortable());
            hudRoate.postValue(quickPresenter.getHudRoate());
            wiperSensitivity.postValue(quickPresenter.getWiperSens());
            driveAirbag.postValue(quickPresenter.getDriveAirBag());
            rearScreenControl.postValue(quickPresenter.getRearScreenControl());
            refuelSmallDoor.postValue(quickPresenter.getRefuelSmallDoor());
            rearMirror.postValue(quickPresenter.getRearMirror());
        });
    }

    public QuickViewModel() {
        // 中控锁
        centralLocking.setValue(PrefsConst.DefaultValue.Q_CENTER_LOCK);
        // 后尾门
        rearTailgate.setValue(PrefsConst.DefaultValue.Q_REAR_TAIL_GATE);
        // 后视镜折叠
        rearMirrorFold.setValue(PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD);
        // 车窗状态
        windowState.setValue(PrefsConst.DefaultValue.Q_WINDOW);
        // 车窗锁
        windowLock.setValue(PrefsConst.DefaultValue.Q_WINDOW_LOCK);
        // 遮阳帘
        sunshade.setValue(PrefsConst.DefaultValue.Q_SUNSHADE);
        // 电动尾翼
        autoTail.setValue(PrefsConst.DefaultValue.Q_AUTO_TAIL);
        // 天窗
        skyWindow.setValue(PrefsConst.DefaultValue.Q_SKY_WINDOW);
        // 感应靠近解锁
        approachingUnlock.setValue(PrefsConst.DefaultValue.Q_APPROACHING_UNLOCK);
        // 感应离车上锁
        departureLocking.setValue(PrefsConst.DefaultValue.Q_DEPARTURE_LOCKING);
        // 锁车自动升窗
        lockAutoRaiseWindow.setValue(PrefsConst.DefaultValue.Q_LOCK_AUTO_RAISE_WINDOW);
        // 锁车收起遮阳帘
        lockCarSunroofShade.setValue(PrefsConst.DefaultValue.Q_LOCK_CAR_SUNROOF_SHADE);
        // 设防提示
        defenseReminder.setValue(PrefsConst.DefaultValue.Q_DEFENSE_REMINDER);
        // 左儿童锁
        leftChildLock.setValue(PrefsConst.DefaultValue.Q_LEFT_CHILD_LOCK);
        // 右儿童锁
        rightChildLock.setValue(PrefsConst.DefaultValue.Q_RIGHT_CHILD_LOCK);
        // 自动落锁
        automaticLocking.setValue(PrefsConst.DefaultValue.Q_AUTOMATIC_LOCKING);
        // 驻车自动解锁
        automaticParkingUnlock.setValue(PrefsConst.DefaultValue.Q_AUTOMATIC_PARKING_UNLOCK);
        // 后视镜自动折叠
        autoRearMirrorFold.setValue(PrefsConst.DefaultValue.Q_AUTO_REAR_MIRROR_FOLD);
        // 雨天自动加热外后视镜
        autoHotRearMirror.setValue(PrefsConst.DefaultValue.Q_AUTO_HOT_REAR_MIRROR);
        // 倒车时后视镜自动调节
        backRearAdjust.setValue(PrefsConst.DefaultValue.Q_BACK_AUTO_REAR_MIRROR_ADJUST);
        // 座椅便携进入/退出
        seatPortable.setValue(PrefsConst.DefaultValue.Q_SEAT_PORTABLE);
        // 后尾门高度
        hudRoate.setValue(PrefsConst.DefaultValue.Q_REAR_TAILGATE_ROATE);
        // 雨刮器灵敏度
        wiperSensitivity.setValue(PrefsConst.DefaultValue.Q_WIPER_SENS);
        // 副驾安全气囊
        driveAirbag.setValue(PrefsConst.DefaultValue.Q_DRIVE_AIR_BAG);
        // 后排屏
        rearScreenControl.setValue(PrefsConst.DefaultValue.Q_REAR_SCREEN_CONTROL);
        // 加油小门
        refuelSmallDoor.setValue(PrefsConst.DefaultValue.Q_REFUEL_SMALL_DOOR);
        // 后视镜调节
        rearMirror.setValue(PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD_DIALOG);
    }

    public MutableLiveData<Integer> getCentralLocking() {
        return centralLocking;
    }

    public void setCentralLocking(Integer status) {
        centralLocking.postValue(status);
    }

    public MutableLiveData<Integer> getRearTailgate() {
        return rearTailgate;
    }

    public void setRearTailgate(Integer status) {
        rearTailgate.postValue(status);
    }

    public MutableLiveData<Integer> getRearMirrorFold() {
        return rearMirrorFold;
    }

    public void setRearMirrorFold(Integer status) {
        rearMirrorFold.postValue(status);
    }

    public MutableLiveData<Integer> getWindowDesc() {
        return windowDesc;
    }

    public void setWindowDesc(Integer status) {
        windowDesc.postValue(status);
    }

    public MutableLiveData<Integer> getWindowReback() {
        return windowReback;
    }

    public void setWindowReback(Integer status) {
        windowReback.postValue(status);
    }

    public MutableLiveData<Integer> getWindowState() {
        return windowState;
    }

    public void setWindowState(Integer status) {
        windowState.postValue(status);
    }

    public MutableLiveData<Integer> getWindowLock() {
        return windowLock;
    }

    public void setWindowLock(Integer status) {
        windowLock.postValue(status);
    }

    public MutableLiveData<Integer> getSunshade() {
        return sunshade;
    }

    public void setSunshade(Integer status) {
        sunshade.postValue(status);
    }

    public MutableLiveData<Integer> getAutoTail() {
        return autoTail;
    }

    public void setAutoTail(Integer status) {
        autoTail.postValue(status);
    }

    public MutableLiveData<Integer> getSkyWindow() {
        return skyWindow;
    }

    public void setSkyWindow(Integer status) {
        skyWindow.postValue(status);
    }

    public MutableLiveData<Integer> getApproachingUnlock() {
        return approachingUnlock;
    }

    public void setApproachingUnlock(Integer status) {
        approachingUnlock.postValue(status);
    }

    public MutableLiveData<Integer> getDepartureLocking() {
        return departureLocking;
    }

    public void setDepartureLocking(Integer status) {
        departureLocking.postValue(status);
    }

    public MutableLiveData<Integer> getLockAutoRaiseWindow() {
        return lockAutoRaiseWindow;
    }

    public void setLockAutoRaiseWindow(Integer status) {
        lockAutoRaiseWindow.postValue(status);
    }

    public MutableLiveData<Integer> getLockCarSunroofShade() {
        return lockCarSunroofShade;
    }

    public void setLockCarSunroofShade(Integer status) {
        lockCarSunroofShade.postValue(status);
    }

    public MutableLiveData<Integer> getDefenseReminder() {
        return defenseReminder;
    }

    public void setDefenseReminder(Integer status) {
        defenseReminder.postValue(status);
    }

    public MutableLiveData<Integer> getLeftChildLock() {
        return leftChildLock;
    }

    public void setLeftChildLock(Integer status) {
        leftChildLock.postValue(status);
    }

    public MutableLiveData<Integer> getRightChildLock() {
        return rightChildLock;
    }

    public void setRightChildLock(Integer status) {
        rightChildLock.postValue(status);
    }

    public MutableLiveData<Integer> getAutomaticLocking() {
        return automaticLocking;
    }

    public void setAutomaticLocking(Integer status) {
        automaticLocking.postValue(status);
    }

    public MutableLiveData<Integer> getAutomaticParkingUnlock() {
        return automaticParkingUnlock;
    }

    public void setAutomaticParkingUnlock(Integer status) {
        automaticParkingUnlock.postValue(status);
    }

    public MutableLiveData<Integer> getAutoRearMirrorFold() {
        return autoRearMirrorFold;
    }

    public void setAutoRearMirrorFold(Integer status) {
        autoRearMirrorFold.postValue(status);
    }

    public MutableLiveData<Integer> getAutoHotRearMirror() {
        return autoHotRearMirror;
    }

    public void setAutoHotRearMirror(Integer status) {
        autoHotRearMirror.postValue(status);
    }

    public MutableLiveData<Integer> getBackRearAdjust() {
        return backRearAdjust;
    }

    public void setBackRearAdjust(Integer status) {
        backRearAdjust.postValue(status);
    }

    public MutableLiveData<Integer> getSeatPortable() {
        return seatPortable;
    }

    public void setSeatPortable(Integer status) {
        seatPortable.postValue(status);
    }

    public MutableLiveData<Integer> getHudRoate() {
        return hudRoate;
    }

    public void setHudRoate(Integer status) {
        hudRoate.postValue(status);
    }

    public MutableLiveData<Integer> getWiperSensitivity() {
        return wiperSensitivity;
    }

    public void setWiperSensitivity(Integer status) {
        if (Objects.equals(wiperSensitivity.getValue(), status)) return;
        wiperSensitivity.postValue(status);
    }

    public MutableLiveData<Integer> getDriveAirBag() {
        return driveAirbag;
    }

    public void setDriveAirBag(Integer status) {
        driveAirbag.postValue(status);
    }

    public void setRearScreenControl(int status) {
        rearScreenControl.postValue(status);
    }

    public MutableLiveData<Integer> getRearScreenControl() {
        return rearScreenControl;
    }

    public MutableLiveData<Integer> getRefuelSmallDoor() {
        return refuelSmallDoor;
    }

    public void setRefuelSmallDoor(Integer status) {
        refuelSmallDoor.postValue(status);
    }

    public void setRearMirror(Integer status) {
        rearMirror.postValue(status);
    }

    public MutableLiveData<Integer> getRearMirror() {
        return rearMirror;
    }

    public void setSunshadeFrontOpen(Integer status) {
        sunshadeFrontOpen.postValue(status);
    }

    public MutableLiveData<Integer> getSunshadeFrontOpen() {
        return sunshadeFrontOpen;
    }

    public void setSunshadeFrontClose(Integer status) {
        sunshadeFrontClose.postValue(status);
    }

    public MutableLiveData<Integer> getSunshadeFrontClose() {
        return sunshadeFrontClose;
    }

    public void setSunshadeRearOpen(Integer status) {
        sunshadeRearOpen.postValue(status);
    }

    public MutableLiveData<Integer> getSunshadeRearOpen() {
        return sunshadeRearOpen;
    }

    public void setSunshadeRearClose(Integer status) {
        sunshadeRearClose.postValue(status);
    }

    public MutableLiveData<Integer> getSunshadeRearClose() {
        return sunshadeRearClose;
    }

}
