package com.bitech.vehiclesettings.base.kt

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: BaseFragment
 **/
abstract class BaseDialogFragment<V : ViewBinding, VM : ViewModel> : DialogFragment() {

    // 定义fragment viewDataBinding对象，并延迟初始化
    protected lateinit var viewBinding: V

    // 定义fragment viewModel对象，并延迟初始化
    protected lateinit var viewModel: VM

    // 日志标志位
    private val tag = javaClass.simpleName

    // 主页activity对象
    private lateinit var activity: AppCompatActivity
    // 子对话框计数器
    private var childDialogCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(tag, "onCreate : ")
        activity = getActivity() as AppCompatActivity
        // 在fragment创建时初始化ViewModel
        viewModel = ViewModelProvider(this)[getViewModel()]

//        if (useCustomStyle() > 0) {
//            setStyle(STYLE_NO_FRAME, useCustomStyle())
//        } else {
//            setStyle(STYLE_NO_FRAME, R.style.BaseDialogStyle)
//        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        LogUtil.d(tag, "onCreateView : ")
        // 在fragment视图创建时处初始化viewDataBinding
        viewBinding = getLayoutId(container)
        isCancelable = true
        // 返回构建的fragment视图
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        LogUtil.d(tag, "onViewCreated : ")
        // 在视图被创建后，进行视图与ViewModel的绑定，绑定是否实现取决于子类是否重写该方法
        bindViewModel(viewBinding, viewModel)
        // 初始化视图
        initView()
        // 初始化监听
        intiListener()
        // 初始化viewModel订阅
        initObserve()
    }

    override fun onStart() {
        super.onStart()
        // 初始化数据
        initData()
        LogUtil.d(tag, "onStart : ")

    }
    // 检查是否有活跃的子对话框
    private fun hasActiveChildDialogs(): Boolean {
        return childDialogCount > 0
    }

    // 增加子对话框计数
    fun incrementChildDialogCount() {
        childDialogCount++
    }

    // 减少子对话框计数
    fun decrementChildDialogCount() {
        if (childDialogCount > 0) {
            childDialogCount--
        }
    }

    override fun onResume() {
        super.onResume()
        LogUtil.d(tag, "onResume : ")
        if (dialogWidth() > 0 && dialogHeight() > 0) {
            val dialog = dialog
            dialog?.window?.let {
                it.setBackgroundDrawableResource(if (dialogBackgroundRes() > 0) dialogBackgroundRes() else 0)
                val params: WindowManager.LayoutParams = it.attributes
                params.gravity = dialogGravity()
                it.setAttributes(params)
                it.setLayout(dialogWidth(), dialogHeight())
            }
        }
        dialog?.window?.let { window ->
            val decorView = window.decorView
            decorView.viewTreeObserver.addOnWindowFocusChangeListener { hasFocus ->
                if (!hasFocus && !hasActiveChildDialogs()) {
//                    dismiss()
                }
            }
        }
    }

    /**
     * @return Dialog背景DrawableRes
     */
    protected fun dialogBackgroundRes(): Int {
        return R.drawable.border_bg_dialog
    }

    /**
     * @return 窗口位置
     */
    protected fun dialogGravity(): Int {
        return Gravity.CENTER
    }

    /**
     * @return 宽度
     */
    protected fun dialogWidth(): Int {
        return 1176
    }

    /**
     * @return 高度
     */
    protected fun dialogHeight(): Int {
        return 800
    }

    /**
     * @return 使用自定义的style
     */
    protected fun useCustomStyle(): Int {
        return 0
    }

    override fun onPause() {
        LogUtil.d(tag, "onPause : ")
        super.onPause()
    }

    override fun onStop() {
        LogUtil.d(tag, "onStop : ")
        super.onStop()
    }

    override fun onDestroy() {
        LogUtil.d(tag, "onDestroy : ")
        // 取消viewModel订阅
        removeObserve()
        super.onDestroy()
    }

    override fun dismiss() {
        if (fragmentManager == null) return
        super.dismissAllowingStateLoss()
    }

    protected abstract fun getDialogTag(): String

    fun add() {
        fragmentManager?.let {
            val ft = it.beginTransaction()
            val prev = it.findFragmentByTag(getDialogTag())
            if (prev != null) {
                ft.remove(prev)
            }
            ft.add(this, getDialogTag())
            ft.commitAllowingStateLoss()
        }
    }

    /**
     * 实现fragment之间的跳转.
     *
     * @param tabNameId  选项菜单名称ID
     */
    fun toFragment(tabNameId: Int) {
//        activity.fragmentToFragment(tabNameId)
    }

    /**
     * 获取布局资源文件id,抽象方法，后续Activity需实现该方法绑定视图文件.
     *
     * @return 布局资源文件id
     */
    abstract fun getLayoutId(container: ViewGroup?): V

    /**
     * 获取ViewModel类，抽象方法，后续ViewModel需继承实现该方法返回ViewModel本身.
     *
     * @return
     */
    abstract fun getViewModel(): Class<VM>

    /**
     * 初始化fragment视图控件.
     *
     */
    protected abstract fun initView()

    /**
     * 初始化fragment控件监听.
     *
     */
    protected abstract fun intiListener()

    /**
     * 初始化fragment创建时数据.
     *
     */
    protected abstract fun initData()

    /**
     * 初始化viewModel订阅.
     *
     */
    protected abstract fun initObserve()

    /**
     * 取消viewModel订阅.
     *
     */
    protected abstract fun removeObserve()

    /**
     * viewDataBinding与ViewModel的双向绑定.
     */
    open fun bindViewModel(viewBinding: V, viewModel: VM) {
        // TODO: 子类可以重写该方法完成绑定,只有需要绑定的时候实现该方法就行.
    }
}
