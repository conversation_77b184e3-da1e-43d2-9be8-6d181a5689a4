package com.bitech.vehiclesettings.view.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.ContextThemeWrapper
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogBtPairedBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst

/**
 * @ClassName: BtPairedCodeDialog
 *
 * @Date:  2024/6/5 14:41
 * @Description: 蓝牙配对码弹窗.
 **/
class BtPairedCodeDialog(context: Context) : Dialog(context, R.style.dialog), View.OnClickListener {

    // 蓝牙配对码弹窗视图
    private lateinit var binding: DialogBtPairedBinding

    // 页面点击事件监听
    private var confirmDialogClickCallback: OnConfirmDialogClickCallback? = null

    // 蓝牙配对码
    private var btPairedCodeMessage: String = ""

    /**
     * 生命周期-创建.
     *
     * @param savedInstanceState 状态保存对象
     */
    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        val themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue)
        val themedContext = ContextThemeWrapper(context, themeId)
        binding = DialogBtPairedBinding.inflate(
            LayoutInflater.from(themedContext)
        )
//        binding = DialogBtPairedBinding
//                .bind(layoutInflater.inflate(R.layout.dialog_bt_paired, null))
        setContentView(binding.root)
        // 初始化视图
        initView()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        LogUtil.d(TAG, "initView :")
        // 设置对话框窗口属性
        val attributes = window?.attributes
        // 设置弹窗类型-系统弹窗
        attributes?.type = WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG
        attributes?.windowAnimations = 0
        window?.attributes = attributes
        // 外部点击可关闭
        setCanceledOnTouchOutside(true)
        // 对话框按钮监听
        binding.dialogOkBtn.visibility = View.VISIBLE
        binding.dialogOkBtn.setOnClickListener(this)
        binding.dialogCancelBtn.setOnClickListener(this)
        // 设置蓝牙配对码
        if (btPairedCodeMessage.isNotBlank()) {
            // 配对码不为空，则设置显示蓝牙配对码
            showBtPairedCode(btPairedCodeMessage)
        }
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    /**
     * dialog 按钮点击事件.
     *
     * @param view View
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.dialog_ok_btn -> {
                LogUtil.d(TAG, "onClick : ok is click!")
                confirmDialogClickCallback?.onDmsConfirmClick()
                //TODO 常用蓝牙设备
                //TODO 始终播放手机音频
                dismiss()
            }
            R.id.dialog_cancel_btn-> {
                LogUtil.d(TAG, "onClick : cancel is click!")
                confirmDialogClickCallback?.onDmsCancelClick()
                dismiss()
            }
        }
    }

    /**
     * 获取蓝牙配对码弹窗中 checkbox 的选中状态.
     *
     * @return checkbox 是否选中
     */
    fun isPreferencesCheckboxChecked(): Boolean {
        return binding.btPreferencesCheckbox.isChecked
    }
    /**
     * 显示蓝牙配对码.
     *
     * @param btPairedCode 蓝牙配对码
     */
    private fun showBtPairedCode(btPairedCode: String) {
        // 对蓝牙配对码字符串进行分离
        val codeArray = btPairedCode.toCharArray().map { it.toString().toInt() }.toIntArray()
        LogUtil.i(
            TAG, "showBtPairedCode : " +
                    "btPairedCode = $btPairedCode , " +
                    "codeArray = ${codeArray.joinToString()}"
        )
        // 显示蓝牙配对码
        binding.apply {
            settingsBtPairedCodeOneTv.text = codeArray[0].toString()
            settingsBtPairedCodeTwoTv.text = codeArray[1].toString()
            settingsBtPairedCodeThreeTv.text = codeArray[2].toString()
            settingsBtPairedCodeFourTv.text = codeArray[3].toString()
            settingsBtPairedCodeFiveTv.text = codeArray[4].toString()
            settingsBtPairedCodeSixTv.text = codeArray[5].toString()
        }
    }

    /**
     * 设置蓝牙配对码.
     *
     * @param btPairedCode
     */
    fun setBtPairedCode(btPairedCode: String) {
        LogUtil.i(TAG, "setBtPairedCode : btPairedCode = $btPairedCode")
        // 去除蓝牙配对码中的空格
        this.btPairedCodeMessage = btPairedCode
    }

    /**
     * 设置确认按钮点击事件监听.
     *
     * @param callback
     */
    fun setDialogClickCallback(callback: OnConfirmDialogClickCallback) {
        confirmDialogClickCallback = callback
    }

    /**
     * @ClassName: OnConfirmDialogClickCallback
     *
     * @Date:  2024/6/5 14:44
     * @Description: 确认按钮点击事件回调.
     **/
    interface OnConfirmDialogClickCallback {
        /**
         * 确认按钮被点击.
         *
         */
        fun onDmsConfirmClick()
        /**
         * 取消按钮被点击.
         *
         */
        fun onDmsCancelClick()
    }

    companion object {
        // 日志标志位
        private const val TAG = "BtPairedCodeDialog"
    }
}
