package com.bitech.vehiclesettings.service.sound

import android.content.Context
import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.bitech.platformlib.bean.Topics
import com.bitech.platformlib.interfaces.driving.IDrivingManagerListener
import com.bitech.platformlib.interfaces.voice.IVoiceManagerListener
import com.bitech.platformlib.manager.DrivingManager
import com.bitech.platformlib.manager.VoiceManager
import com.bitech.platformlib.utils.MsgUtil
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.carapi.constants.CarVoice
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class VoiceLifecycle(
    private val lifecycleOwner: LifecycleOwner
) : LifecycleEventObserver {
    private val TAG = "VoiceLifecycle"

    private var drivingManager: DrivingManager = DrivingManager.getInstance()
    private var voiceManager: VoiceManager = VoiceManager.getInstance()
    private val voicePresenter = VoicePresenter.getInstance()
    private val context: Context = MyApplication.getContext()

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                Log.i(TAG, "onCreate: $source")
                drivingManager = DrivingManager.getInstance()
                voiceManager = VoiceManager.getInstance()
                if (drivingManager != null) {
                    drivingManager.addCallback(TAG, msgDrivingCallback)
                    drivingManager.registerListener()
                }
                if (voiceManager != null) {
                    voiceManager.addCallback(TAG, msgVoiceCallback)
                    voiceManager.registerListener()
                }
                lifecycleOwner.lifecycleScope.launch(Dispatchers.Default) {
                    initVoiceMemoryFromPrefs()
                }
            }
            Lifecycle.Event.ON_DESTROY -> {
                Log.i(TAG, "onDestroy: $source")
                if (drivingManager != null) {
                    drivingManager.removeCallback(TAG)
                }
                if (voiceManager != null) {
                    voiceManager.removeCallback(TAG)
                }
            }
            else -> {}
        }
    }

    private val msgDrivingCallback = object : IDrivingManagerListener {
        override fun getVehicleSpeed(speed: Int) {
            Log.d(TAG, "车速变化: $speed")
            val compensation = voiceManager.getCompensation()
            if (compensation == CarVoice.Compensation.OFF) {
                return
            } else {
                voicePresenter.setDB(speed, compensation)
            }
        }
    }

    private val msgVoiceCallback = object : IVoiceManagerListener {}

    private fun initVoiceMemoryFromPrefs() {
        Log.d(TAG, "initVoiceMemoryFromPrefs")
        var voiceMedia = Prefs.getGlobalValue(PrefsConst.GlobalValue.VOICE_MEDIA, CarVoice.Media.DEFAULT)
        Log.d(TAG, "Media: $voiceMedia")
        if (voiceMedia > 15) {
            Log.d(TAG, "Media>15")
            voicePresenter.setVoiceMedia(15)
        } else {
            voicePresenter.setVoiceMedia(voiceMedia)
        }
        val voicePhone = Prefs.getGlobalValue(PrefsConst.GlobalValue.VOICE_PHONE, CarVoice.Phone.DEFAULT)
        Log.d(TAG, "Phone: $voicePhone")
        voicePresenter.setVoicePhone(voicePhone)

        val voiceNavi = Prefs.getGlobalValue(PrefsConst.GlobalValue.VOICE_NAVI, CarVoice.Navi.DEFAULT)
        Log.d(TAG, "Navi: $voiceNavi")
        voicePresenter.setVoiceNavi(voiceNavi)

        val voiceVR = Prefs.getGlobalValue(PrefsConst.GlobalValue.VOICE_VR, CarVoice.VR.DEFAULT)
        Log.d(TAG, "VR: $voiceVR")
        voicePresenter.setVoiceVR(voiceVR)

        val voiceAlarm = Prefs.getGlobalValue(PrefsConst.GlobalValue.VOICE_ALARM, CarVoice.VR.DEFAULT)
        Log.d(TAG, "Alarm: $voiceAlarm")
        voicePresenter.setVoiceAlarm(voiceAlarm)

        val buttonSound = Prefs.get(PrefsConst.VOICE_BUTTON_SOUND, CarVoice.ButtonSound.DEFAULT)
        Log.d(TAG, "buttonSound: $buttonSound")
        voicePresenter.setButtonSound(buttonSound)

        // AVAS始终为开
        voicePresenter.setLowSpeedAnalog(CarVoice.AVAS.ON)
        Log.d(TAG, "AVAS: ${CarVoice.AVAS.ON}")

        val voiceExternalMode = voicePresenter.getVoiceExternalMode()
        Log.d(TAG, "ExternalMode: $voiceExternalMode")
        voicePresenter.setVoiceExternalMode(voiceExternalMode)

        val callBroadcast = voicePresenter.getCallBroadcast()
        Log.d(TAG, "callBroadcast: $callBroadcast")
        voicePresenter.setCallBroadcast(callBroadcast)

        val compensation = Prefs.get(PrefsConst.VOICE_COMPENSATION, CarVoice.Compensation.DEFAULT)
        Log.d(TAG, "compensation: $compensation")
        voicePresenter.setCompensation(compensation)

        val voiceLowerMediaTone = voicePresenter.getVoiceLowerMediaTone()
        Log.d(TAG, "LowerMediaTone: $voiceLowerMediaTone")
        voicePresenter.setVoiceLowerMediaTone(voiceLowerMediaTone)

        val headRest = Prefs.get(PrefsConst.VOICE_HEADREST, CarVoice.HeadRest.DEFAULT)
        Log.d(TAG, "headRest: $headRest")
        voicePresenter.setHeadRest(headRest)

        val eq = Prefs.get(PrefsConst.VOICE_EQ, CarVoice.EQ.DEFAULT)
        Log.d(TAG, "eq: $eq")
        voicePresenter.setEQ(eq)

        val surroundSound = Prefs.get(PrefsConst.VOICE_SURROUND_SOUND, CarVoice.SurroundSound.DEFAULT)
        Log.d(TAG, "surroundSound: $surroundSound")
        voicePresenter.setSurroundSound(eq)

        val virtualScene = Prefs.get(PrefsConst.VOICE_VIRTUAL_SCENE, CarVoice.VirtualScene.DEFAULT)
        Log.d(TAG, "virtualScene: $virtualScene")
        voicePresenter.setVirtualScene(virtualScene)

        // 均衡调节
        voicePresenter.setSubBass(Prefs.get(PrefsConst.VOICE_SUB_BASS, CarVoice.Equalization.DEFAULT))
        Log.d(TAG, "subBass: ${voicePresenter.getSubBass()}")
        val bass = Prefs.get(PrefsConst.VOICE_BASS, CarVoice.Equalization.DEFAULT)
        Log.d(TAG, "bass: $bass")
        voicePresenter.setBass(bass)

        val lowMid = Prefs.get(PrefsConst.VOICE_LOW_MID, CarVoice.Equalization.DEFAULT)
        Log.d(TAG, "lowMid: $lowMid")
        voicePresenter.setLowMid(lowMid)

        val mid = Prefs.get(PrefsConst.VOICE_MID, CarVoice.Equalization.DEFAULT)
        Log.d(TAG, "mid: $mid")
        voicePresenter.setMid(mid)

        val highMid = Prefs.get(PrefsConst.VOICE_HIGH_MID, CarVoice.Equalization.DEFAULT)
        Log.d(TAG, "highMid: $highMid")
        voicePresenter.setHighMid(highMid)

        val treble = Prefs.get(PrefsConst.VOICE_TREBLE, CarVoice.Equalization.DEFAULT)
        Log.d(TAG, "treble: $treble")
        voicePresenter.setTreble(treble)

        val superTreble = Prefs.get(PrefsConst.VOICE_SUPER_TREBLE, CarVoice.Equalization.DEFAULT)
        Log.d(TAG, "superTreble: $superTreble")
        voicePresenter.setSuperTreble(superTreble)
    }
}