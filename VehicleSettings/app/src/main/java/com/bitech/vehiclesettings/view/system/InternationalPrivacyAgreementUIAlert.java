package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebSettings;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSBasicPravicyAgreementBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSInternationalPravicyAgreementBinding;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.common.NoToggleSwitch;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class InternationalPrivacyAgreementUIAlert extends BaseDialog {
    private static final String TAG = InternationalPrivacyAgreementUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public InternationalPrivacyAgreementUIAlert(@NonNull Context context) {
        super(context);
    }

    public InternationalPrivacyAgreementUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected InternationalPrivacyAgreementUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        InternationalPrivacyAgreementUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSInternationalPravicyAgreementBinding binding;
        private boolean isBlueOpen = false;
        private InternationalPrivacyAgreementUIAlert dialog = null;
        private View layout;
        WindowManager.LayoutParams layoutParams;

        public Builder(Context context) {
            this.context = context;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public InternationalPrivacyAgreementUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }

        public InternationalPrivacyAgreementUIAlert create() {
            if (dialog == null)
                dialog = new InternationalPrivacyAgreementUIAlert(context, R.style.Dialog);
            dialog.setCancelable(isCan);

            binding = DialogAlertSInternationalPravicyAgreementBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1500;
            layoutParams.height = 900;
            window.setAttributes(layoutParams);

            // 初始化switch数据
            initSwitch();
            // 设置正文内容
            setText();
            // 设置switch效果
            setSwitch();
            // 设置按钮点击效果
            setButton();

            return dialog;
        }

        private void initSwitch() {
            int activityDataStatus = onProgressChangedListener.getDataStatus(0);
            int settingDataStatus = onProgressChangedListener.getDataStatus(1);
            int browseDataStatus = onProgressChangedListener.getDataStatus(2);
            binding.swActivityData.setChecked(CommonUtils.IntToBool(activityDataStatus));
            binding.swSettingData.setChecked(CommonUtils.IntToBool(settingDataStatus));
            binding.swBrowseData.setChecked(CommonUtils.IntToBool(browseDataStatus));
        }

        private void setButton() {
            binding.tvAcknowledge.setOnClickListener(v -> {
                dialog.dismiss();
            });
        }

        private void setSwitch() {
            binding.swActivityData.setOnCheckedChangeListener((compoundButton, b) -> {
                onProgressChangedListener.updateSwitch(binding.swActivityData, 0);
            });
            binding.swSettingData.setOnCheckedChangeListener((compoundButton, b) -> {
                onProgressChangedListener.updateSwitch(binding.swSettingData, 1);
            });
            binding.swBrowseData.setOnCheckedChangeListener((compoundButton, b) -> {
                onProgressChangedListener.updateSwitch(binding.swBrowseData, 2);
            });
        }

        private void setText() {
            String content = context.getString(R.string.str_system_international_privacy_statement_content) + " ";
            String link = context.getString(R.string.str_system_international_privacy_statement_link);

            SpannableString spannable = new SpannableString(content + link);

            if (!link.isEmpty()) {
                int start = content.length();
                int end = start + link.length();

                spannable.setSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View view) {
                        DetailsUIAlert.Builder detailsUIAlert = new DetailsUIAlert.Builder(context);
                        detailsUIAlert.create(
                                context.getString(R.string.str_system_international_privacy_statement_chery_title),
                                context.getString(R.string.str_system_international_privacy_statement_chery_content),
                                1600, 1000, Gravity.LEFT, com.bitech.base.R.dimen.font_24px
                        ).show();
                        detailsUIAlert.setScrollable(true);
                    }
                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(ContextCompat.getColor(context, R.color.blue)); // 链接颜色
//                        ds.setUnderlineText(false); // 可选：去掉下划线
                    }
                }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }

            binding.tvText.setText(spannable);
            binding.tvText.setHighlightColor(Color.TRANSPARENT);
            binding.tvText.setMovementMethod(LinkMovementMethod.getInstance());
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {

        int getDataStatus(int index);

        void updateSwitch(NoToggleSwitch noToggleSwitch, int index);
    }
}
