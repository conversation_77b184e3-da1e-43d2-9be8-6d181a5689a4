package com.bitech.vehiclesettings.utils

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.SearchFirstMenuBean
import com.bitech.vehiclesettings.common.PackageConstants
import com.bitech.vehiclesettings.manager.CarConfigInfoManager

/**
 * @Description: 这是一个公共字符串、数字等使用类.
 **/
object Contacts {

    // 定义一个静态变量，用于作为Activity传递的Key
    const val ACTIVITY_MESSAGE = "activity_message"

    // 车辆中心sharedPref数据存放文件名称
    const val SETTINGS_DATA_NAME = "settings_data"

    // 当前连接的蓝牙对象,用于进行蓝牙回连
    const val SHARED_PRE_BT_DEVICE = "settings_connected_bt_device"

    // 距离过远断开的蓝牙，用于进行距离回归时的蓝牙回连
    const val SHARED_PRE_LONG_DISTANCE_BT_DEVICE = "settings_disconnected_bt_device_long_distance"

    // 是否远距离断开蓝牙
    const val SHARED_PRE_IS_REMOTE_DISCONNECTED = "settings_is_remote_disconnect_bt"

    // 当前连接的蓝牙对象,默认值
    const val BT_DEVICE_DEFAULT = "settings_connected_bt_device_null"

    // 设置数据库名称
    const val SETTINGS_DATABASE_NAME = "settings_database"

    // 网络通知开关系统属性Key
    const val NETWORK_NOTIFICATION_KEY = "network_notification_switch"

    // WIFI热点设备接入广播
    const val WIFI_AP_STA_JOIN_ACTION = "android.net.wifi.WIFI_AP_STA_JOIN"

    // WIFI热点设备断开广播
    const val WIFI_AP_STA_LEAVE_ACTION = "android.net.wifi.WIFI_AP_STA_LEAVE"

    // 是否首次进入息屏设置弹窗KEY
    const val IS_FIRST_ENTER_STANDBY_KEY = "isFirstEnterStandbyKey"

    // 是否用户选择了语言
    const val IS_USER_CHOOSE_LANGUAGE_KEY = "isUserChooseLanguage"

    // 进入休眠模式重启前的电源状态
    const val POWER_STATUS = "SETTINGS_CAR_POWER_STATUS"

    // 主题设置自动显示模式下是否刷新界面KEY
    const val REFRESH_THEME_AUTO = "refresh_theme_auto"

    // 搜索关键词存放的Settings Provider KEY
    const val SETTINGS_SEARCH_KEYWORD_KEY = "com.chery.settings.search.keyword"

    // 搜索关键词默认值，用于重置
    const val SETTINGS_SEARCH_KEYWORD_DEFAULT = "com.chery.settings.search.default"

    // 系统蓝牙和WIFI重置参数KEY
    const val SETTINGS_PROPERTY_SYS_FACTORY_RESET_KEY = "sys.factory.reset"
    const val SETTINGS_PROPERTY_SYS_FACTORY_RESET_VALUE = "sys.factory.reset"

    // T13J零件号-国际无碟导航左舵
    const val CAR_703002910AA = "703002910AA"

    // T13J零件号-国际无碟导航右舵
    const val CAR_703002911AA = "703002911AA"

    // T13J零件号-新增零件号
    const val CAR_703003227AA = "703003227AA"

    // 弹窗类型
    const val BT_PAIRED_FAIL_DIALOG = 1
    const val SC_SMART_HIGH_BEAM_ASSIST_DIALOG = 2
    const val AD_LIMIT_SPEED_ASSIST_DIALOG = 3
    const val AD_INTELLIGENT_NAVI_EXIT_DIALOG = 4

    // Toast类型
    const val TOAST_TYPE_SMALL = 0
    const val TOAST_TYPE_MEDIUM = 1
    const val TOAST_TYPE_LARGE= 2

    // 蓝牙连接类型
    const val BT_CONNECT_HFP = 0
    const val BT_CONNECT_A2DP = 1
    const val BT_CONNECT_ALL = 2
    const val BT_CONNECT_AA = 3
    const val BT_CONNECT_CP = 4
    const val BT_CONNECT_AA_TO_CP = 5
    const val BT_CONNECT_CP_TO_AA = 6

    // 协议栈拒绝连接ID
    const val PROFILE_ID_HFP = 3
    const val PROFILE_ID_A2DP = 4
    const val PROFILE_ID_AVRCP = 5

    // 蓝牙ACL断开CODE码-远距离断开
    const val BT_ACL_DISCONNECTED_8 = 8

    // 是否需要回连蓝牙-用于突然下电或待机模式重启后情况下的蓝牙回连
    var isBtLashBack = false

    fun setIsBBtLashBack(){
        isBtLashBack = true
    }
    // 是否处于待机模式
    var isStandbyMode = false

    // 是否停止扫描
    var isStopScan = false

    // CP协议是否连接,冷启动回连判断
    var isConnectCpProxy = false

    // CP设备列表协议是否连接,冷启动回连判断
    var isConnectDevCpProxy = false

    // 是否重启activity(用于白天黑夜模式切换)
    var isRestartActivity = false

    // 是否需要关闭热点(carPlay或AA已连接，关闭热点需先断开CP和AA，且需要等待关闭完成后才能关闭热点)
    var isNeedCloseHotspot = false

    // 当前菜单栏选中的位置id
    var curTabId = R.string.m_quick_control

    // 蓝牙默认名称
    const val BT_DEFAULT_NAME = "Cloudrive-123456"

    // CP连接类型
    const val CP_CONNECT_WIRELESS = 0
    const val CP_CONNECT_WIRED = 1
    const val CP_CONNECT_INVALID = -1

    // WIFI加密类型
    const val WIFI_CAP_WPA = "WPA"
    const val WIFI_CAP_WPA3 = "RSN-SAE-CCMP"
    const val WIFI_CAP_WPA2_3 = "RSN-PSK+SAE-CCMP"
    const val WIFI_CAP_WEP = "WEP"
    const val WIFI_CAP_EAP = "EAP"

    // WIFI连接方式
    const val WIFI_CONNECTED_FROM_SCAN_LIST = 0
    const val WIFI_CONNECTED_FROM_CONNECTED_LIST = 1

    // WIFI连接错误类型
    const val WIFI_PASSWORD_ERROR = 0
    const val WIFI_CONNECTED_TIMEOUT = 1
    const val WIFI_CONNECTED_FAIL = 2
    const val WIFI_CONNECTED_COMPLETE = 3

    // 自动校准开关状态
    const val SYSTEM_AUTO_CAB_OPEN = 1
    const val SYSTEM_AUTO_CAB_CLOSE = 0

    // ICM时间转换格式
    const val ICM_TIME_FORMAT = "yyyyMMddHHmmss"

    // 存储空间
    const val STORAGE_64 = 68719476736L

    // 钥匙状态LiveData
    var keyStatusLiveData = MutableLiveData<Int>()

    // 中控屏亮度LiveData
    val screenBrightnessLiveData = MutableLiveData<Int>()

    // 仪表屏幕亮度LiveData
    val meterBrightnessLiveData = MutableLiveData<Int>()

    // 车辆中心页面菜单实体-wifi
    var wifiMenuBean = createWifiMenuBean(MyApplication.getContext())

    // 车辆中心页面菜单实体-蓝牙
    var btMenuBean = createBtMenuBean(MyApplication.getContext())
    /**
     * 构建WIFI可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     */
    private fun createWifiMenuBean(context: Context): SearchFirstMenuBean {
        // 构建WIFI一级菜单选项
        val wifiFirstMenu = if (CarConfigInfoManager.instance.isSupportWifi()) {
            SearchFirstMenuBean(context.getString(R.string.tab_wifi))
        } else {
            SearchFirstMenuBean(context.getString(R.string.tab_hot_spot))
        }
        wifiFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_wifi_s
        wifiFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_WIFI_KEY_VALUE
        // 添加WIFI对应的二级菜单项
        if (CarConfigInfoManager.instance.isSupportWifi()) {
            wifiFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.wifi_switcher))
            )
            wifiFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.wifi_connected))
            )
            wifiFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.wifi_can_use))
            )
            wifiFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.wifi_network_notification_switcher))
            )
        }
//        wifiFirstMenu.secondMenuList.add(
//            SearchFirstMenuBean
//                .SearchSecondMenu(context.getString(R.string.wifi_hotspot_switcher))
//        )
//        wifiFirstMenu.secondMenuList.add(
//            SearchFirstMenuBean
//                .SearchSecondMenu(context.getString(R.string.wifi_hot_spot_name))
//        )
//        wifiFirstMenu.secondMenuList.add(
//            SearchFirstMenuBean
//                .SearchSecondMenu(context.getString(R.string.wifi_hot_spot_password))
//        )
        return wifiFirstMenu
    }

    /**
     * 构建蓝牙可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     */
    private fun createBtMenuBean(context: Context): SearchFirstMenuBean {
        // 构建蓝牙一级菜单选项
        val btFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_bluetooth))
        btFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_bt_s
        btFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_BLUETOOTH_KEY_VALUE
        // 添加蓝牙对应的二级菜单项
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_switcher))
        )
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_device_name))
        )
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_can_find))
        )
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_paired_device))
        )
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_can_use_devices))
        )
        return btFirstMenu
    }

    /*// 车辆中心页面菜单实体-快捷控制
    var scMenuBean = createScMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-新能源
    var newEnergyMenuBean = createNewEnergyMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-整车设置
    var vsMenuBean = createVsMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-辅助驾驶
    var adMenuBean = createAdMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-氛围灯
    var ambientMenuBean = createAmbientMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-车辆舒适
    var vcMenuBean = createVcMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-蓝牙
    var btMenuBean = createBtMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-wifi
    var wifiMenuBean = createWifiMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-声音
    var soundMenuBean = createSoundMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-语音
    var vrMenuBean = createVrMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-显示
    var displayMenuBean = createDisplayMenuBean(SettingApplication.context)

    // 车辆中心页面菜单实体-系统
    var systemMenuBean = createSystemMenuBean(SettingApplication.context)

    // 系统应用包名列表
    val packageNames = mutableListOf(
        PackageConstants.Avm.PACKAGE_NAME, PackageConstants.Help.PACKAGE_NAME,
        PackageConstants.Dialer.PACKAGE_NAME, PackageConstants.Upgrade.PACKAGE_NAME,
        PackageConstants.BT.PACKAGE_NAME, PackageConstants.Hvac.PACKAGE_NAME,
        PackageConstants.Eol.PACKAGE_NAME, PackageConstants.EngineeringMode.PACKAGE_NAME,
        PackageConstants.CarPlay.PACKAGE_NAME, PackageConstants.AndroidAuto.PACKAGE_NAME,
        PackageConstants.CpAaAdapter.PACKAGE_NAME, PackageConstants.Phone.PACKAGE_NAME,
        PackageConstants.PetalMap.PACKAGE_NAME, PackageConstants.Adas.PACKAGE_NAME,
        PackageConstants.Iflytek.PACKAGE_NAME
    )

    // 颜色色表RGB值
    val rgbColors = mutableListOf(
        Color.rgb(255, 11, 11), Color.rgb(255, 117, 117),
        Color.rgb(239, 128, 62), Color.rgb(239, 132, 62),
        Color.rgb(215, 145, 20), Color.rgb(210, 150, 10),
        Color.rgb(199, 166, 0), Color.rgb(216, 180, 0),
        Color.rgb(213, 187, 0), Color.rgb(213, 187, 0),
        Color.rgb(213, 187, 0), Color.rgb(200, 198, 0),
        Color.rgb(200, 198, 0), Color.rgb(204, 202, 0),
        Color.rgb(210, 210, 0), Color.rgb(210, 210, 0),
        Color.rgb(200, 197, 0), Color.rgb(200, 197, 0),
        Color.rgb(196, 194, 0), Color.rgb(212, 210, 0),
        Color.rgb(173, 215, 40), Color.rgb(173, 215, 40),
        Color.rgb(173, 215, 40), Color.rgb(237, 237, 237),
        Color.rgb(237, 237, 237), Color.rgb(255, 255, 255),
        Color.rgb(255, 255, 255), Color.rgb(237, 237, 237),
        Color.rgb(237, 237, 237), Color.rgb(237, 237, 237),
        Color.rgb(170, 210, 50), Color.rgb(170, 210, 50),
        Color.rgb(170, 210, 50), Color.rgb(142, 224, 82),
        Color.rgb(40, 208, 44), Color.rgb(44, 210, 47),
        Color.rgb(14, 210, 0), Color.rgb(13, 230, 85),
        Color.rgb(13, 230, 85), Color.rgb(0, 216, 96),
        Color.rgb(10, 255, 90), Color.rgb(10, 255, 90),
        Color.rgb(82, 251, 198), Color.rgb(82, 251, 198),
        Color.rgb(80, 250, 199), Color.rgb(80, 253, 200),
        Color.rgb(79, 251, 203), Color.rgb(78, 250, 200),
        Color.rgb(50, 218, 230), Color.rgb(50, 218, 230),
        Color.rgb(47, 220, 223), Color.rgb(47, 220, 223),
        Color.rgb(45, 220, 221), Color.rgb(88, 241, 242),
        Color.rgb(40, 218, 220), Color.rgb(26, 224, 222),
        Color.rgb(26, 224, 222), Color.rgb(122, 234, 251),
        Color.rgb(122, 234, 251), Color.rgb(97, 216, 255),
        Color.rgb(97, 216, 255), Color.rgb(101, 217, 255),
        Color.rgb(81, 212, 255), Color.rgb(39, 202, 255),
        Color.rgb(39, 202, 255), Color.rgb(53, 172, 255),
        Color.rgb(53, 172, 255), Color.rgb(29, 162, 255),
        Color.rgb(49, 49, 241), Color.rgb(49, 49, 241),
        Color.rgb(49, 49, 241), Color.rgb(112, 66, 206),
        Color.rgb(131, 38, 246), Color.rgb(122, 55, 185),
        Color.rgb(186, 11, 197), Color.rgb(173, 33, 175),
        Color.rgb(189, 64, 179), Color.rgb(189, 64, 179),
        Color.rgb(189, 64, 179), Color.rgb(170, 90, 190),
        Color.rgb(180, 95, 195), Color.rgb(180, 95, 195),
        Color.rgb(190, 100, 200), Color.rgb(190, 100, 200),
        Color.rgb(200, 100, 200), Color.rgb(200, 100, 200),
        Color.rgb(210, 90, 200), Color.rgb(215, 90, 200),
        Color.rgb(215, 90, 200), Color.rgb(220, 90, 190),
        Color.rgb(231, 99, 186), Color.rgb(231, 99, 186),
        Color.rgb(240, 104, 162), Color.rgb(248, 98, 116),
        Color.rgb(248, 98, 116), Color.rgb(255, 73, 73),
        Color.rgb(255, 73, 73), Color.rgb(255, 73, 73),
        Color.rgb(255, 90, 80), Color.rgb(255, 90, 80),
        Color.rgb(255, 123, 109)
    )

    // 颜色色表RGB值对应的车机信号表
    val customColors = mutableListOf(
        IHU_SetTheaterDimmingColor_64.NUMBER3_SPORT, IHU_SetTheaterDimmingColor_64.NUMBER46,
        IHU_SetTheaterDimmingColor_64.NUMBER45, IHU_SetTheaterDimmingColor_64.NUMBER44,
        IHU_SetTheaterDimmingColor_64.NUMBER43, IHU_SetTheaterDimmingColor_64.NUMBER42,
        IHU_SetTheaterDimmingColor_64.NUMBER41, IHU_SetTheaterDimmingColor_64.NUMBER40_NORMAL,
        IHU_SetTheaterDimmingColor_64.NUMBER39, IHU_SetTheaterDimmingColor_64.NUMBER39,
        IHU_SetTheaterDimmingColor_64.NUMBER39, IHU_SetTheaterDimmingColor_64.NUMBER38,
        IHU_SetTheaterDimmingColor_64.NUMBER38, IHU_SetTheaterDimmingColor_64.NUMBER37,
        IHU_SetTheaterDimmingColor_64.NUMBER36, IHU_SetTheaterDimmingColor_64.NUMBER36,
        IHU_SetTheaterDimmingColor_64.NUMBER35, IHU_SetTheaterDimmingColor_64.NUMBER35,
        IHU_SetTheaterDimmingColor_64.NUMBER34, IHU_SetTheaterDimmingColor_64.NUMBER33,
        IHU_SetTheaterDimmingColor_64.NUMBER32, IHU_SetTheaterDimmingColor_64.NUMBER32,
        IHU_SetTheaterDimmingColor_64.NUMBER32, IHU_SetTheaterDimmingColor_64.NUMBER63,
        IHU_SetTheaterDimmingColor_64.NUMBER63, IHU_SetTheaterDimmingColor_64.NUMBER64,
        IHU_SetTheaterDimmingColor_64.NUMBER64, IHU_SetTheaterDimmingColor_64.NUMBER63,
        IHU_SetTheaterDimmingColor_64.NUMBER63, IHU_SetTheaterDimmingColor_64.NUMBER63,
        IHU_SetTheaterDimmingColor_64.NUMBER31, IHU_SetTheaterDimmingColor_64.NUMBER31,
        IHU_SetTheaterDimmingColor_64.NUMBER31, IHU_SetTheaterDimmingColor_64.NUMBER30,
        IHU_SetTheaterDimmingColor_64.NUMBER29, IHU_SetTheaterDimmingColor_64.NUMBER28,
        IHU_SetTheaterDimmingColor_64.NUMBER27, IHU_SetTheaterDimmingColor_64.NUMBER26,
        IHU_SetTheaterDimmingColor_64.NUMBER26, IHU_SetTheaterDimmingColor_64.NUMBER1_ECO,
        IHU_SetTheaterDimmingColor_64.NUMBER25, IHU_SetTheaterDimmingColor_64.NUMBER25,
        IHU_SetTheaterDimmingColor_64.NUMBER24, IHU_SetTheaterDimmingColor_64.NUMBER24,
        IHU_SetTheaterDimmingColor_64.NUMBER23, IHU_SetTheaterDimmingColor_64.NUMBER22,
        IHU_SetTheaterDimmingColor_64.NUMBER21, IHU_SetTheaterDimmingColor_64.NUMBER20,
        IHU_SetTheaterDimmingColor_64.NUMBER18, IHU_SetTheaterDimmingColor_64.NUMBER18,
        IHU_SetTheaterDimmingColor_64.NUMBER17, IHU_SetTheaterDimmingColor_64.NUMBER17,
        IHU_SetTheaterDimmingColor_64.NUMBER16, IHU_SetTheaterDimmingColor_64.NUMBER19,
        IHU_SetTheaterDimmingColor_64.NUMBER15, IHU_SetTheaterDimmingColor_64.NUMBER14,
        IHU_SetTheaterDimmingColor_64.NUMBER14, IHU_SetTheaterDimmingColor_64.NUMBER13,
        IHU_SetTheaterDimmingColor_64.NUMBER13, IHU_SetTheaterDimmingColor_64.NUMBER12,
        IHU_SetTheaterDimmingColor_64.NUMBER12, IHU_SetTheaterDimmingColor_64.NUMBER11,
        IHU_SetTheaterDimmingColor_64.NUMBER10, IHU_SetTheaterDimmingColor_64.NUMBER9,
        IHU_SetTheaterDimmingColor_64.NUMBER9, IHU_SetTheaterDimmingColor_64.NUMBER7,
        IHU_SetTheaterDimmingColor_64.NUMBER7, IHU_SetTheaterDimmingColor_64.NUMBER8,
        IHU_SetTheaterDimmingColor_64.NUMBER2, IHU_SetTheaterDimmingColor_64.NUMBER2,
        IHU_SetTheaterDimmingColor_64.NUMBER2, IHU_SetTheaterDimmingColor_64.NUMBER6,
        IHU_SetTheaterDimmingColor_64.NUMBER5, IHU_SetTheaterDimmingColor_64.NUMBER4,
        IHU_SetTheaterDimmingColor_64.NUMBER62, IHU_SetTheaterDimmingColor_64.NUMBER61,
        IHU_SetTheaterDimmingColor_64.NUMBER60, IHU_SetTheaterDimmingColor_64.NUMBER60,
        IHU_SetTheaterDimmingColor_64.NUMBER60, IHU_SetTheaterDimmingColor_64.NUMBER59,
        IHU_SetTheaterDimmingColor_64.NUMBER58, IHU_SetTheaterDimmingColor_64.NUMBER58,
        IHU_SetTheaterDimmingColor_64.NUMBER57, IHU_SetTheaterDimmingColor_64.NUMBER57,
        IHU_SetTheaterDimmingColor_64.NUMBER56, IHU_SetTheaterDimmingColor_64.NUMBER56,
        IHU_SetTheaterDimmingColor_64.NUMBER55, IHU_SetTheaterDimmingColor_64.NUMBER54,
        IHU_SetTheaterDimmingColor_64.NUMBER54, IHU_SetTheaterDimmingColor_64.NUMBER53,
        IHU_SetTheaterDimmingColor_64.NUMBER52, IHU_SetTheaterDimmingColor_64.NUMBER52,
        IHU_SetTheaterDimmingColor_64.NUMBER51, IHU_SetTheaterDimmingColor_64.NUMBER50,
        IHU_SetTheaterDimmingColor_64.NUMBER50, IHU_SetTheaterDimmingColor_64.NUMBER49,
        IHU_SetTheaterDimmingColor_64.NUMBER49, IHU_SetTheaterDimmingColor_64.NUMBER49,
        IHU_SetTheaterDimmingColor_64.NUMBER48, IHU_SetTheaterDimmingColor_64.NUMBER48,
        IHU_SetTheaterDimmingColor_64.NUMBER47
    )

    *//**
     * 初始化车辆菜单.
     *
     *//*
    fun initSettingsMenu() {
        // 车辆中心页面菜单实体-快捷控制
        scMenuBean = createScMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-新能源
        newEnergyMenuBean = createNewEnergyMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-整车设置
        vsMenuBean = createVsMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-辅助驾驶
        adMenuBean = createAdMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-氛围灯
        ambientMenuBean = createAmbientMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-车辆舒适
        vcMenuBean = createVcMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-蓝牙
        btMenuBean = createBtMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-wifi
        wifiMenuBean = createWifiMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-声音
        soundMenuBean = createSoundMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-语音
        vrMenuBean = createVrMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-显示
        displayMenuBean = createDisplayMenuBean(SettingApplication.context)
        // 车辆中心页面菜单实体-系统
        systemMenuBean = createSystemMenuBean(SettingApplication.context)
    }

    *//**
     * 构建快捷控制可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createScMenuBean(context: Context): SearchFirstMenuBean {
        // 构建快捷控制一级菜单选项
        val scFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_shortcut_control))
        scFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_quick_crol_s
        scFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_SHORTCUT_CONTROL_KEY_VALUE
        // 添加快捷控制对应的二级菜单项
        scFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sc_central_lock_text))
        )
        if (CarConfigInfoManager.instance.hasPlg()) {
            // 后尾门已配置，则添加到二级菜单列表
            scFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_tailgate_text))
            )
        }
        scFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sc_window_lock_text))
        )
        scFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sc_sunshade_adjust_text))
        )
        if (CarConfigInfoManager.instance.hasDriveMode()) {
            // 驾驶模式已配置，则添加到二级菜单列表
            scFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_drive_mode_text))
            )
        }
        if (CarConfigInfoManager.instance.isSupportEnergyFlow()) {
            // 四驱能量流功能已配置，则添加到二级菜单列表
            scFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_drive_energy_flow_text))
            )
        }
        if (CarConfigInfoManager.instance.hasPeps()) {
            // 靠近解锁，远离上锁已配置，则添加到二级菜单列表
            scFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_smart_key_lock_text))
            )
        }
        if (CarConfigInfoManager.instance.hasIntelligentOpenTrunk()) {
            // 智能钥匙感应尾门开启功能已配置，则添加到二级菜单列表
            scFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_smart_key_reaction_text))
            )
        }
        if (CarConfigInfoManager.instance.hasAutoLight()) {
            // 前照灯延迟已配置，则添加到二级菜单列表
            scFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_light_headlamp_text))
            )
        }
        if (CarConfigInfoManager.instance.hasFcm()) {
            // 智能远光灯辅助已配置，则添加到二级菜单列表
            scFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_light_smart_full_beam_text))
            )
        }
        if (CarConfigInfoManager.instance.hasWelcomeFunction()) {
            // 靠近迎宾已配置，则添加到二级菜单列表
            scFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_light_approach_welcome_text))
            )
        }
        scFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sc_light_height_adjust_text))
        )
        return scFirstMenu
    }

    *//**
     * 构建新能源可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createNewEnergyMenuBean(context: Context): SearchFirstMenuBean {
        // 构建新能源一级菜单选项
        val neFirstMenu = SearchFirstMenuBean(context.getString(R.string.ne_new_energy))
        neFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_phev_s
        neFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_NEW_ENERGY_KEY_VALUE
        // 添加新能源对应的二级菜单项
        if(CarConfigInfoManager.instance.isBev()){
            neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_charge_stop_soc))
            )
            neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_dischage))
            )
            neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_dischage_stop_soc))
            )
            neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_electronic_lock2))
            )
           *//* neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_traction_mode))
            )*//*
            neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_battery_insulation_cooling))
            )
           *//* neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_ultimate_energy_saving_mode))
            )*//*
        }else if(CarConfigInfoManager.instance.isHev()){
            neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_energy_consumption_list))
            )
           *//* neFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ne_ev_pilot_lamp))
            )*//*
        }
        neFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.ne_energy_recovery_level))
        )
        return neFirstMenu
    }

    *//**
     * 构建整车设置可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createVsMenuBean(context: Context): SearchFirstMenuBean {
        // 构建整车设置一级菜单选项
        val vsFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_vehicle_setup))
        vsFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_veihcle_s
        vsFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_VEHICLE_SETUP_KEY_VALUE
        // 添加整车设置对应的二级菜单项
        vsFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vs_steering_wheel_custom_text))
        )
        if (CarConfigInfoManager.instance.hasPlg()) {
            // 后尾门已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_tailgate_open_height_text))
            )
        }
        if (CarConfigInfoManager.instance.hasWipeSensitivity()) {
            // 雨刮器灵已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.sc_wipe_sensitivity_text))
            )
        }
        vsFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sc_wipe_repair_mode_text))
        )
        vsFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vs_fortification_text))
        )
        if (CarConfigInfoManager.instance.hasVehicleSpeedAutoLock()) {
            // 自动落锁已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_automatic_lock_text))
            )
        }
        // 主驾单独解锁，T13J-1157 不支持
        *//*vsFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vs_master_car_unlocked_text))
        )*//*
        if (CarConfigInfoManager.instance.hasCwc()) {
            // 无线充电已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_wireless_charge_text))
            )
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_forget_reminder_text))
            )
        }
        if (CarConfigInfoManager.instance.hasDriveModeMemory()) {
            // 驾驶模式记忆已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_drive_mode_memory_text))
            )
        }
        if (CarConfigInfoManager.instance.hasSteeringModeSelectWithDriveMode()) {
            // 转向力模式关联驾驶模式已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_steering_force_with_drive_text))
            )
        }
        if (CarConfigInfoManager.instance.hasSteeringModeSelect()) {
            // 转向力模式已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_steering_force_mode_text))
            )
        }
        if (CarConfigInfoManager.instance.hasIpb()) {
            // 制动俯仰控制、灵敏度、关联驾驶模式、制动感觉模式已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_brake_pitch_control_text))
            )
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_brake_pitch_control_sensitivity_text))
            )
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_brake_sensation_with_drive_mode_text))
            )
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vs_brake_sensation_mode_text))
            )
        }
        vsFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vs_tire_pressure_reset_text))
        )
        // 添加车辆舒适对应的二级菜单项
        if (CarConfigInfoManager.instance.hasMirrorAutomaticFold()) {
            // 外后视镜自动折叠功能已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vc_rear_mirror_auto_fold_text))
            )
        }
        if (CarConfigInfoManager.instance.hasReverseMirrorAutoAdjust()) {
            // 倒车时后视镜自动调节功能已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(
                        context.getString(R.string.vc_reverse_mirror_auto_adjust_text)
                    )
            )
        }
        if (CarConfigInfoManager.instance.hasCir()) {
            // 车内儿童检测已配置，则添加到二级菜单列表
            vsFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vc_car_child_monitoring_text))
            )
        }
        return vsFirstMenu
    }

    *//**
     * 构建辅助驾驶可搜索菜单项.
     *
     * @param context 环境上下文
     * @return
     *//*
    private fun createAdMenuBean(context: Context): SearchFirstMenuBean {
        // 构建辅助驾驶一级菜单选项
        val aDFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_assisted_driving))
        aDFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_driving_s
        aDFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_ASSISTANCE_DRIVER_KEY_VALUE
        // 添加辅助驾驶对应的二级菜单项
        *//*aDFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.ad_intelligent_navi_exit_text))
        )*//*
        if(CarConfigInfoManager.instance.hasTrafficJamAssist()){
            //智能领航功能退出提醒
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_intelligent_navi_exit_text))
            )
        }

        if (CarConfigInfoManager.instance.hasSpeedLimitAssistance()) {
            // 限速辅助功能已配置,则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_limit_speed_assist_text))
            )
        }
        if (CarConfigInfoManager.instance.hasOverSpeedWarning()) {
            // 超速报警功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_over_speed_alarm_text))
            )
        }
        if (CarConfigInfoManager.instance.hasSpeedControlFunction()) {
            // 智能速度控制系统功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_intelligent_speed_control_text))
            )
        }
        if (CarConfigInfoManager.instance.hasBeforeCollisionForewarn()) {
            // 前碰预警系统功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_before_collision_forewarn_text))
            )
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_before_collision_forewarn_sensitivity_text))
            )
        }
        if (CarConfigInfoManager.instance.hasAutomaticEmergencyBraking()) {
            // 自动紧急制动功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_auto_emergency_brake_text))
            )
        }
        if (CarConfigInfoManager.instance.hasRearCrossTrafficBraking()) {
            // 后方交通穿行防碰撞功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_reverse_side_emergency_brake_text))
            )
        }
        if (CarConfigInfoManager.instance.hasLaneAssistAlarm()) {
            // 车道辅助报警系统功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_lane_assist_alarm_text))
            )
        }
        if (CarConfigInfoManager.instance.hasLaneAssistAlarmTypeSensitivity()) {
            // 车道辅助方式及灵敏度功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_lane_assist_alarm_type_text))
            )
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_lane_assist_sensitivity_text))
            )
        }
        if (CarConfigInfoManager.instance.hasLaneDeparturePrevention()) {
            // 车道辅助干预功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_lane_assist_intervene_text))
            )
        }
        if (CarConfigInfoManager.instance.hasEmergencyLaneKeeping()) {
            // 紧急车道保持功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_emergency_lane_keep_text))
            )
        }
        if (CarConfigInfoManager.instance.hasDoorOpenWarn()) {
            // 门开预警功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_door_open_warn_text))
            )
        }
        if (CarConfigInfoManager.instance.hasBlindSpotMonitor()) {
            // 盲区监测功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_blind_spot_monitor_text))
            )
        }
        if (CarConfigInfoManager.instance.hasRearApproachWarn()) {
            // 后方监测预警功能已配置，则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_rear_approach_warn_text))
            )
        }
        if (CarConfigInfoManager.instance.hasTiredDriveWarn()) {
            // 疲劳驾驶提醒功能已配置,则添加到二级菜单列表
            aDFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.ad_tired_drive_warn_text))
            )
        }
        return aDFirstMenu
    }

    *//**
     * 构建氛围灯可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createAmbientMenuBean(context: Context): SearchFirstMenuBean {
        // 构建氛围灯一级菜单选项
        val aMFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_ambient_light))
        aMFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_lamp_s
        aMFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_AMBIENT_LIGHT_KEY_VALUE
        // 添加氛围灯对应的二级菜单项
        aMFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.al_recommended_color_text))
        )
        aMFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.al_with_drive_mode_text))
        )
        return aMFirstMenu
    }

    *//**
     * 构建车辆舒适可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createVcMenuBean(context: Context): SearchFirstMenuBean {
        // 构建车辆舒适一级菜单选项
        val vCFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_vehicle_comfort))
        vCFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_comfort_s
        vCFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_VEHICLE_COMFORT_KEY_VALUE
        // 添加车辆舒适对应的二级菜单项
        if (CarConfigInfoManager.instance.hasMirrorAutomaticFold()) {
            // 外后视镜自动折叠功能已配置，则添加到二级菜单列表
            vCFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vc_rear_mirror_auto_fold_text))
            )
        }
        if (CarConfigInfoManager.instance.hasReverseMirrorAutoAdjust()) {
            // 倒车时后视镜自动调节功能已配置，则添加到二级菜单列表
            vCFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(
                        context.getString(R.string.vc_reverse_mirror_auto_adjust_text)
                    )
            )
        }
        if (CarConfigInfoManager.instance.hasCir()) {
            // 车内儿童检测已配置，则添加到二级菜单列表
            vCFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.vc_car_child_monitoring_text))
            )
        }
        return vCFirstMenu
    }

    *//**
     * 构建蓝牙可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createBtMenuBean(context: Context): SearchFirstMenuBean {
        // 构建蓝牙一级菜单选项
        val btFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_bluetooth))
        btFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_bt_s
        btFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_BLUETOOTH_KEY_VALUE
        // 添加蓝牙对应的二级菜单项
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_switcher))
        )
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_device_name))
        )
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_can_find))
        )
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_paired_device))
        )
        btFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.bt_can_use_devices))
        )
        return btFirstMenu
    }

    *//**
     * 构建WIFI可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createWifiMenuBean(context: Context): SearchFirstMenuBean {
        // 构建WIFI一级菜单选项
        val wifiFirstMenu = if (CarConfigInfoManager.instance.isSupportWifi()) {
            SearchFirstMenuBean(context.getString(R.string.tab_wifi))
        } else {
            SearchFirstMenuBean(context.getString(R.string.tab_hot_spot))
        }
        wifiFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_wifi_s
        wifiFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_WIFI_KEY_VALUE
        // 添加WIFI对应的二级菜单项
        if (CarConfigInfoManager.instance.isSupportWifi()) {
            wifiFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.wifi_switcher))
            )
            wifiFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.wifi_connected))
            )
            wifiFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.wifi_can_use))
            )
            wifiFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.wifi_network_notification_switcher))
            )
        }
//        wifiFirstMenu.secondMenuList.add(
//            SearchFirstMenuBean
//                .SearchSecondMenu(context.getString(R.string.wifi_hotspot_switcher))
//        )
//        wifiFirstMenu.secondMenuList.add(
//            SearchFirstMenuBean
//                .SearchSecondMenu(context.getString(R.string.wifi_hot_spot_name))
//        )
//        wifiFirstMenu.secondMenuList.add(
//            SearchFirstMenuBean
//                .SearchSecondMenu(context.getString(R.string.wifi_hot_spot_password))
//        )
        return wifiFirstMenu
    }

    *//**
     * 构建声音可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createSoundMenuBean(context: Context): SearchFirstMenuBean {
        // 构建声音一级菜单选项
        val soundFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_sound))
        soundFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_vol_s
        soundFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_SOUND_KEY_VALUE
        // 添加声音对应的二级菜单项
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_adjust_navi_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_adjust_vr_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_adjust_media_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_adjust_bt_music_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_adjust_phone_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_adjust_warning_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_adjust_bgm_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_adjust_default_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_speed_volume_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_effect_adjust_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_balance_adjust_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_loudness_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_key_tone_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_call_in_report_title))
        )
        soundFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_reset_value_text))
        )
        return soundFirstMenu
    }

    *//**
     * 构建语音可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createVrMenuBean(context: Context): SearchFirstMenuBean {
        // 构建语音一级菜单选项
        val vrFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_voice))
        vrFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_vr_s
        vrFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_VOICE_KEY_VALUE
        // 添加语音对应的二级菜单项
        vrFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vr_continuous_dialogue_time_text))
        )
        vrFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vr_sound_src_location_text))
        )
        vrFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vr_main_drive_mode_text))
        )
        vrFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vr_auto_mode_text))
        )
        vrFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vr_rouse_switch_text))
        )
        vrFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.vr_voice_broadcast_display))
        )
        return vrFirstMenu
    }

    *//**
     * 构建显示可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createDisplayMenuBean(context: Context): SearchFirstMenuBean {
        // 构建显示一级菜单选项
        val displayFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_display))
        displayFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_display_s
        displayFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_DISPLAY_KEY_VALUE
        // 添加显示对应的二级菜单项
        displayFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.display_wallpaper_text))
        )
        displayFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.display_standby_text))
        )
        displayFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.display_mode_text))
        )
        displayFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.display_center_brightness_text))
        )
        displayFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.display_dash_brightness_text))
        )
        if(!CarConfigInfoManager.instance.hasRegionsOfBrazil()){
            displayFirstMenu.secondMenuList.add(
                SearchFirstMenuBean
                    .SearchSecondMenu(context.getString(R.string.display_video_limit_text))
            )
        }
        displayFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.display_screen_clean_text))
        )
        displayFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.sound_reset_value_text))
        )
        return displayFirstMenu
    }

    *//**
     * 构建系统可搜索菜单项.
     *
     * @param context 环境上下文
     * @return SearchFirstMenuBean
     *//*
    private fun createSystemMenuBean(context: Context): SearchFirstMenuBean {
        // 构建系统一级菜单选项
        val systemFirstMenu = SearchFirstMenuBean(context.getString(R.string.tab_system))
        systemFirstMenu.firstLevelMenuIconId = R.drawable.vehicle_center_tab_icon_system_s
        systemFirstMenu.firstMenuId = PackageConstants.Settings.PAGE_SYSTEM_KEY_VALUE
        // 添加系统对应的二级菜单项
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_24_hour_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_date_show_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_automatic_calibration_datetime_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_time_zone_setting_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_language_setting_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_version_info_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_software_version_info_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_hardware_version_info_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_storage_space_text))
        )
        systemFirstMenu.secondMenuList.add(
            SearchFirstMenuBean
                .SearchSecondMenu(context.getString(R.string.system_factory_data_reset_text))
        )
        return systemFirstMenu
    }*/
}
