package com.bitech.vehiclesettings.carapi.constants;

public class CarNewEnergy {

    /**
     * 能量流状态
     * 0x0:Not Active
     * 0x1:Ready
     * 0x2:2WD_EV
     * 0x3:4WD_EV
     * 0x4:2WD_ExtendRangeDisCharge
     * 0x5:2WD_ExtendRangeCharge
     * 0x6:4WD_ExtendRangeDisCharge
     * 0x7:4WD_ExtendRangeCharge
     * 0x8:2WD_Parallel
     * 0x9:4WD_Parallel
     * 0xA:Engine
     * 0xB:Running_Generate
     * 0xC:Parking_Generate
     * 0xD:2WD_ReGenerateBrake
     * 0xE:4WD_ReGenerateBrake
     * 0xF:Charge
     * 0x10:DecelerateCharge
     * 0x11:4WD_EVByRear
     * 0x12:4WD_ExtendRangeChargeByRear
     * 0x13:4WD_ExtendRangeDisChargeByRear
     * 0x14:4WD_ReGenerateBrakeByRear
     * 0x15~0x1F:Reserved
     */
    public static class HcuEnergyFlowSts {
        public static final int ST01_Ready = 0x1;
        public static final int ST02_2WD_EV = 0x2;
        public static final int ST03_4WD_EV = 0x3;
        public static final int ST04_2WD_ExtendRangeDisCharge = 0x4;
        public static final int ST05_2WD_ExtendRangeCharge = 0x5;
        public static final int ST06_4WD_ExtendRangeDisCharge = 0x6;
        public static final int ST07_4WD_ExtendRangeCharge = 0x7;
        public static final int ST08_2WD_Parallel = 0x8;
        public static final int ST09_4WD_Parallel = 0x9;
        public static final int ST0A_Engine = 0xA;
        public static final int ST0B_Running_Generate = 0xB;
        public static final int ST0C_Parking_Generate = 0xC;
        public static final int ST0D_2WD_ReGenerateBrake = 0xD;
        public static final int ST0E_4WD_ReGenerateBrake = 0xE;
        public static final int ST0F_Charge = 0xF;
        public static final int ST10_DecelerateCharge = 0x10;
        public static final int ST11_4WD_EVByRear = 0x11;
        public static final int ST12_4WD_ExtendRangeChargeByRear = 0x12;
        public static final int ST13_4WD_ExtendRangeDisChargeByRear = 0x13;
        public static final int ST14_4WD_ReGenerateBrakeByRear = 0x14;
    }

    /**
     * 充电状态
     * 0x1:Parking charge                                                                                                                                                                                                                                                                                          0x2:Driving charge
     * 0x3:no charge
     * 0x4:charge complete
     * 0xE:Abnormal
     * 0xF:Invalid
     */
    public static class FastChargingSts {
        public static final int PARKING_CHARGE = 0x1; //充电中
        public static final int NO_CHARGE = 0x3;
        public static final int CHARGE_COMPLETE = 0x4; //充电完成
    }

    /**
     * 慢充/放电枪连接状态
     * 0x0:Not connected
     * 0x1:Charge Connected
     * 0x2:V2V
     * 0x3:V2L
     * 0x4:Half Connection
     * 0x5:Error
     * 0x6~0x7:Reserved
     */
    public static class SlowGunConnectSts {
        public static final int NOT_CONNECTED = 0x0;
        public static final int CHARGE_CONNECTED = 0x1; //慢充枪连接
        public static final int V2V = 0x2;
        public static final int V2L = 0x3; //放电枪连接
    }

    /**
     * 快充枪连接状态
     */
    public static class FastGunConnectSts {
        public static final int NOT_CONNECTED = 0x0;
        public static final int CHARGE_CONNECTED = 0x1; //快充枪连接

    }

    /**
     * 对外放电功能开启状态--上报
     */
    public static class V2LFunctionSts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 对外放电开关状态--下发
     */
    public static class V2LSwitchSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
    }

    /**
     * 能量回收强度反馈状态
     * 0x0:Not Active
     * 0x1:Off
     * 0x2:Low
     * 0x3:Middle
     * 0x4:High
     * 能量回收强度下发状态
     * 0x0:Not Active
     * 0x1:Off
     * 0x2:Low
     * 0x3:Middle
     * 0x4:High
     */
    public static class EnergyRecoverySts {
        public static final int HIGH_LEVEL = 0x4;
        public static final int MIDDLE_LEVEL = 0x3;
        public static final int LOW_LEVEL = 0x2;
    }

    /**
     * 电子锁反馈状态
     * 0x0:Unlock
     * 0x1:Lock
     * 0x2:Lock error
     * 0x3:Unlock error
     * 0x4:Elock error
     */
    public static class ElectricLockSts {
        public static final int UNLOCK = 0x0;
        public static final int LOCK = 0x1;
    }

    /**
     * 电子锁下发状态
     * 0x0:NoUnlockReq
     * 0x1:UnlockReq
     */
    public static class ElectricLockSet {
        public static final int UNLOCK = 0x1;
    }

    /**
     * 拖车模式（牵引模式）
     */
    public static class TomModeSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
    }

    /**
     * 预约充电设置与否状态反馈
     * 0x0:NotBook
     * 0x1:Booked
     * 0x2:Reserved
     */
    public static class BookChargeSts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * TBOX预约充电等待状态
     * 0x0:Default
     * 0x1:WaitingCharge
     * 0x2:Charging
     * 0x3:ChargeFinished
     * 0x4:Reserved
     */
    public static class BookChargeWaitingSts {
        public static final int DEFAULT = 0x0;
        public static final int WAITING_CHARGE = 0x1;
        public static final int CHARGING = 0x2;
        public static final int CHARGE_FINISHED = 0x3;
    }

    /**
     * 预约充电开启/取消设置请求
     * 0x0:default
     * 0x1:BookSet
     * 0x2:CancelBook
     * 0x3:Reserved
     */
    public static class BookChargeReq {
        public static final int BOOK_SET = 0x1;
        public static final int CANCEL_BOOK = 0x2;
    }

    /**
     * 预约出行状态
     * 0x1 ON/0x2 OFF
     */
    public static class BookTravelSts {
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
    }

    /**
     * 强制驻车发电状态
     * 0x0: Not Active
     * 0x1: Active
     */
    public static class ForceChargeSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 强制驻车发电开关状态
     * 0x0:Not Active
     * 0x1:On
     * 0x2:Off
     */
    public static class ForceChargeSwitchSts {
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
    }

    /**
     * 低压电源状态
     * 0x0:Off
     * 0x1:Comfortable
     * 0x2:ON
     * 0x3:Reserved
     */
    public static class PowerMode {
        public static final int OFF = 0x0;
        public static final int COMFORTABLE = 0x1;
        public static final int ON = 0x2;
    }

    /**
     * APC限制等级
     * 0x0:Level 0
     * 0x1:Level 1
     * 0x2:Level 2
     * 0x3:Level 3
     * 0x4:Level 4
     * 0x5:Level 5
     */
    public static class ApcLevelLimit {
        public static final int LEVEL_0 = 0x0;
        public static final int LEVEL_1 = 0x1;
        public static final int LEVEL_2 = 0x2;
        public static final int LEVEL_3 = 0x3;
        public static final int LEVEL_4 = 0x4;
        public static final int LEVEL_5 = 0x5;

    }

    /**
     * 车辆模式
     * 0x0:Normal Mode
     * 0x1:Factory Mode
     * 0x2:Transport Mode
     * 0x3~0x7:Reserved
     */
    public static class CarMode {
        public static final int NORMAL_MODE = 0x0;
        public static final int FACTORY_MODE = 0x1;
        public static final int TRANSPORT_MODE = 0x2;
    }

    /**
     * 负载限制
     * 0x0:No warning
     * 0x1:Warning
     */
    public static class LbatipVehPwrMod {
        public static final int NO_WARNING = 0x0;
        public static final int WARNING = 0x1;
    }

    /**
     * 极致节能模式状态
     * 0x0:Unavailable
     * 0x1:Available
     */
    public static class EnergySavingAvailable {
        public static final int UN_AVAILABLE = 0x0;
        public static final int AVAILABLE = 0x1;
    }

    /**
     * 电池状态
     * 0x0:Init
     * 0x1:standby
     * 0x2:PreCharge
     * 0x3:HVOn
     * 0x4:AC charge Mode
     * 0x5:DC charge Mode
     * 0x6:Boost charging mode
     * 0x7:PreHeat Mode
     * 0x8:keep warm
     * 0x9:powerdown
     * 0xA:emergencypowerdown
     * 0xB:fault
     * 0xC:afterrun
     * 0xD:Keep warm complete Mode
     * 0xE:PreHeat complete Mode
     */
    public static class BatteryStatus {
        public static final int AC_CHARGE_MODE = 0x4;
        public static final int DC_CHARGE_MODE = 0x5;
    }

    /**
     * 档位状态 VCU_PRNDGearAct
     * 0x0:Init
     * 0x1:P
     * 0x2:R
     * 0x3:N
     * 0x4:D
     * 0x5:Reserved
     * 0x6:Reserved
     * 0x7:Invalid
     */
    public static class GearStatus {
        public static final int GEAR_INIT = 0x0;
        public static final int GEAR_P = 0x1;
        public static final int GEAR_R = 0x2;
        public static final int GEAR_N = 0x3;
        public static final int GEAR_D = 0x4;
    }
    /**
     * 重置清零
     */
    public static class Reset {
        public static final int ENABLE = 0x1;
    }

    /**
     * 驻车保电set
     */
    public static class ParkPowerSet{
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int Reversed = 0x3;
    }

    /**
     * 驻车保电get
     */
    public static class ParkPowerGet{
        public static final int ON = 0x1;
        public static final int OFF = 0x0;
    }

    /**
     * 展车模式Req
     * 0x0:No Request
     * 0x1:Request enter
     * 0x2:Request exit
     */
    public static class ShowRoomReq {
        public static final int NO_REQUEST = 0x0;
        public static final int ENTER = 0x1;
        public static final int EXIT = 0x2;
    }

    /**
     * 展车模式Switch
     * 0x0:Not Active
     * 0x1:Close
     * 0x2:Open
     * 0x3:Reserved
     */
    public static class ShowRoomSwitch {
        public static final int NOT_ACTIVE = 0x0;
        public static final int CLOSE = 0x1;
        public static final int OPEN = 0x2;
        public static final int Reserved = 0x3;
    }


}
