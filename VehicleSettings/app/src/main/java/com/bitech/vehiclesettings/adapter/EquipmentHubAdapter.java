package com.bitech.vehiclesettings.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.bitech.vehiclesettings.R;

import java.util.Arrays;
import java.util.List;

public class EquipmentHubAdapter extends RecyclerView.Adapter<EquipmentHubAdapter.EquipmentHubViewHolder> {

    private List<Integer> unselectedHubList = Arrays.asList(
            R.mipmap.ic_new_equipment_hub_1,
            R.mipmap.ic_new_equipment_hub_2,
            R.mipmap.ic_new_equipment_hub_3
    );

    private List<Integer> selectedHubList = Arrays.asList(
            R.mipmap.ic_new_equipment_hub_1_selected,
            R.mipmap.ic_new_equipment_hub_2_selected,
            R.mipmap.ic_new_equipment_hub_3_selected
    );
    private int selectedPosition = RecyclerView.NO_POSITION;

    public EquipmentHubAdapter() {}

    // ViewHolder 定义
    public static class EquipmentHubViewHolder extends RecyclerView.ViewHolder {
        ImageView ivHub;

        public EquipmentHubViewHolder(@NonNull View itemView) {
            super(itemView);
            ivHub = itemView.findViewById(R.id.iv_new_equipment_color);
        }
    }

    @NonNull
    @Override
    public EquipmentHubViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_new_equipment_color, parent, false);
        return new EquipmentHubViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull EquipmentHubViewHolder holder, int position) {
        if (position == selectedPosition) {
            // ✅ 选中状态，设置为选中图片 + 放大为 96dp
            holder.ivHub.setImageResource(selectedHubList.get(position));
            holder.ivHub.getLayoutParams().width = 144;
            holder.ivHub.getLayoutParams().height = 144;
            holder.ivHub.requestLayout();
        } else {
            // ✅ 未选中状态，设置为未选中图片 + 还原大小为 64dp
            holder.ivHub.setImageResource(unselectedHubList.get(position));
            holder.ivHub.getLayoutParams().width = 96;
            holder.ivHub.getLayoutParams().height = 96;
            holder.ivHub.requestLayout();
        }

        // ✅ 点击事件处理
        holder.itemView.setOnClickListener(v -> {
            if (selectedPosition != position) {
                int previousSelected = selectedPosition;
                selectedPosition = position;

                // 刷新之前和当前的状态
                notifyItemChanged(previousSelected);
                notifyItemChanged(selectedPosition);
            }
        });
    }

    @Override
    public int getItemCount() {
        return unselectedHubList.size();
    }
}
