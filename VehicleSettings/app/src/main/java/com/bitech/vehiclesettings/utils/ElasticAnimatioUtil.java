package com.bitech.vehiclesettings.utils;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.graphics.Color;
import android.view.View;
import android.view.animation.AccelerateInterpolator;

import androidx.dynamicanimation.animation.SpringAnimation;
import androidx.dynamicanimation.animation.SpringForce;

public class ElasticAnimatioUtil {

    public static void imageAnimaStart(View view) {
        imageAnimaStart(view, 1);
    }

    private static void imageAnimaStart(View view, int type) {
        if (type == 0) {
            // 初始缩小按钮
            view.setScaleX(0.9f);
            view.setScaleY(0.9f);
            SpringAnimation scaleXAnim = new SpringAnimation(view, SpringAnimation.SCALE_X, 1f);
            SpringAnimation scaleYAnim = new SpringAnimation(view, SpringAnimation.SCALE_Y, 1f);
            // 配置弹簧参数  // 低刚度（更弹性） // 中等回弹
            SpringForce spring = new SpringForce(1f)
                    .setStiffness(SpringForce.STIFFNESS_MEDIUM)
                    .setDampingRatio(SpringForce.DAMPING_RATIO_NO_BOUNCY);

            scaleXAnim.setSpring(spring);
            scaleYAnim.setSpring(spring);
            // 启动动画
            scaleXAnim.start();
            scaleYAnim.start();
        } else {
            startElasticAnimation(view);
        }

    }

    // 启动弹性动画
    private static void startElasticAnimation(View view) {
        // 创建缩放动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0.9f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0.9f, 1f);

        // 组合动画
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX, scaleY);
        animatorSet.setDuration(200).start();
    }

    public static void startMaskAnimation(float low, float high, View view) {
        // 初始化遮罩为完全透明
        ValueAnimator animator = ValueAnimator.ofFloat(low, high); // 透明度从0到1
        animator.setDuration(400); // 动画持续3秒
        animator.setInterpolator(new AccelerateInterpolator());
        animator.addUpdateListener(animation -> {
            float animatedValue = (float) animation.getAnimatedValue();
            int color = calculateColor(animatedValue); // 根据透明度计算颜色
            view.getBackground().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_OVER);
            view.setAlpha(animatedValue); // 设置透明度
        });
        animator.start();
    }

    public static void setMast(float animatedValue, View view) {
        int color = calculateColor(animatedValue); // 根据透明度计算颜色
        view.getBackground().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_OVER);
        view.setAlpha(animatedValue); // 设置透明度
    }

    // 根据透明度计算颜色，这里假设从完全透明到半透明的黑色
    private static int calculateColor(float alpha) {
        int red = 0;
        int green = 0;
        int blue = 0;
        return Color.argb((int) (alpha * 255), red, green, blue);
    }

}
