package com.bitech.vehiclesettings.utils;

import android.view.View;

public class BindingUtil {
    /**
     * 通用点击事件绑定方法
     *
     * @param view     需要绑定事件的视图
     * @param listener 事件监听器
     */
    public static void bindClick(View view, View.OnClickListener listener) {
        if (view != null && listener != null) {
            view.setOnClickListener(listener);
        }
    }

    /**
     * 批量绑定多个视图的点击事件（可变参数）
     *
     * @param listener 事件监听器
     * @param views    多个视图
     */
    public static void bindClicks(View.OnClickListener listener, View... views) {
        if (views == null || listener == null) {
            return;
        }
        for (View view : views) {
            if (view != null) {
                view.setOnClickListener(listener);
            }
        }
    }

    /**
     * 批量绑定多个视图的点击事件（可变参数）
     *
     * @param listener 事件长按监听器
     * @param views    多个视图
     */
    public static void bindOnLongClicks(View.OnLongClickListener listener, View... views) {
        if (views == null || listener == null) {
            return;
        }
        for (View view : views) {
            if (view != null) {
                view.setOnLongClickListener(listener);
            }
        }
    }
}
