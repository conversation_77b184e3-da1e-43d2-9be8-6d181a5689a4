package com.bitech.vehiclesettings.carapi.constants

class CarQuickControl {

    /**
     * 按钮状态
     * 0x0:OFF
     * 0x1:ON
     */
    object ButtonSts {
        const val OFF = 0x0;
        const val ON = 0x1;
    }
    /**
     * 中控锁发送
     * ICC -> FLZCU  信号名：ICC_CenterLockSwt
     * 0x0:Not Active
     * 0x1: UNLOCK
     * 0x2:LOCK
     * 0x3:Reserved
     */
    object SetCentralLockSts {
        const val NOT_ACTIVE: Int = 0x0
        const val UNLOCK: Int = 0x1
        const val LOCK: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取中控锁
     * FLZCU -> ICC 信号名：LHFDoorLockSts
     * 0x0:Locked
     * 0x1:Unlocked
     * 0x2:Superlocked
     * 0x3:Unknown
     */
    object GetCentralLockSts {
        const val LOCKED: Int = 0x0
        const val UNLOCKED: Int = 0x1
        const val SUPERLOCKED: Int = 0x2
        const val UNKNOWN: Int = 0x3
    }

    /**
     * 发送后尾门
     * ICC -> PLG 信号名：ICC_TrunkSW
     * 0x0:Not Active
     * 0x1:ON
     * 0x2:OFF
     * 0x3:Reserved
     */
    object SetRearTailGateSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ON: Int = 0x1
        const val OFF: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取后尾门
     * PLG -> ICC 信号名：PLG_LatchSts
     * 0x0:Open
     * 0x1:Secondary
     * 0x2:Latched
     * 0x3:Initializing
     */
    object GetRearTailGateSts {
        const val OPEN: Int = 0x0
        const val SECONDARY: Int = 0x1
        const val LATCHED: Int = 0x2
        const val INITIALIZING: Int = 0x3
    }

    /**
     * 设置后视镜折叠
     * ICC -> FLZCU 信号名：ICC_RearMirrorFoldCmd
     * 0x0:Not Active
     * 0x1:Fold
     * 0x2:Unfold
     */
    object SetRearMirrorFoldSts {
        const val NOT_ACTIVE: Int = 0x0
        const val FOLD: Int = 0x1
        const val UNFOLD: Int = 0x2
    }

    /**
     * 获取后视镜折叠
     * FLZCU -> ICC 信号名：RearViewFoldSts
     * 0x0:invalid
     * 0x1:Fold
     * 0x2:Unfold
     */
    object GetRearMirrorFoldSts {
        const val INVALID: Int = 0x0
        const val FOLD: Int = 0x1
        const val UNFOLD: Int = 0x2
    }

    /**
     * 四门车窗开启状态
     * 0x0:关闭
     * 0x1:开启
     * 0x2:透气
     * 0x3:无
     */
    object WindowSts {
        const val CLOSE: Int = 0x0
        const val OPEN: Int = 0x1
        const val AIR: Int = 0x2
        const val INVALID: Int = 0x3
    }

    /**
     * 获取四门车窗开启状态
     * 0x0:关闭
     * 0x64:开启
     * 0x14:透气
     */
    object GetWindowSts {
        const val CLOSE: Int = 0x0
        const val OPEN: Int = 0x64
        const val AIR: Int = 0x14
    }

    /**
     * 设置四门车窗开启状态
     * 0xE:关闭
     * 0xD:开启
     * 0xF:透气
     */
    object SetWindowSts {
        const val CLOSE: Int = 0xE
        const val OPEN: Int = 0xD
        const val AIR: Int = 0xF
    }

    /**
     * 设置遮阳帘前后排
     * 0x0:front
     * 0x1:rear
     */
    object SetSunshadeSts {
        const val FRONT: Int = 0x0
        const val REAR: Int = 0x1
    }

    /**
     * 设置前排遮阳帘状态
     * 0x0:Not Active
     * 0x1:Auto Open
     * 0x2:Auto Close
     * 0x3:Reserved
     */
    object SetFrontSunshadeSts {
        const val NOT_ACTIVE: Int = 0x0
        const val AUTO_OPEN: Int = 0x1
        const val AUTO_CLOSE: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取前排遮阳帘状态
     * 0x0:Not Active
     * 0x1:Close
     * 0x2:Open
     * 0x3:Closing
     * 0x4:Opening
     * 0x5:Other Position
     * 0x6:Reserved
     */
    object GetFrontSunshadeSts {
        const val NOT_ACTIVE: Int = 0x0
        const val CLOSE: Int = 0x1
        const val OPEN: Int = 0x2
        const val CLOSING: Int = 0x3
        const val OPENING: Int = 0x4
        const val OTHER_POSITION: Int = 0x5
        const val RESERVED: Int = 0x6
    }

    /**
     * 设置后排遮阳帘状态
     * 0x0:Not Active
     * 0x1:Auto Open
     * 0x2:Auto Close
     * 0x3:Reserved
     */
    object SetRearSunshadeSts {
        const val NOT_ACTIVE: Int = 0x0
        const val AUTO_OPEN: Int = 0x1
        const val AUTO_CLOSE: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取后排遮阳帘状态
     * 0x0:Not Active
     * 0x1:Close
     * 0x2:Open
     * 0x3:Closing
     * 0x4:Opening
     * 0x5:Other Position
     * 0x6:Reserved
     */
    object GetRearSunshadeSts {
        const val NOT_ACTIVE: Int = 0x0
        const val CLOSE: Int = 0x1
        const val OPEN: Int = 0x2
        const val CLOSING: Int = 0x3
        const val OPENING: Int = 0x4
        const val OTHER_POSITION: Int = 0x5
        const val RESERVED: Int = 0x6
    }

    /**
     * 设置车窗锁状态
     * ICC_WinInhbSwt
     * 0x0:Not Active
     * 0x1:Inhibit
     * 0x2:Permit
     * 0x3:Reserved
     */
    object SetWindowLockSts {
        const val NOT_ACTIVE: Int = 0x0
        const val INHIBIT: Int = 0x1
        const val PERMIT: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取车窗锁状态
     * FLZCU_WindowInhibitSts
     * 0x0:Permit
     * 0x1:Inhibit
     */
    object GetWindowLockSts {
        const val PERMIT: Int = 0x0
        const val INHIBIT: Int = 0x1
    }

    /**
     * 设置锁车关闭遮阳帘状态
     * ICC_LockCarSunRoofShadeCloseSw
     * 0x0:Not Active
     * 0x1:ON
     * 0x2:OFF
     * 0x3:Reserved
     */
    object SetLockCarSunRoofShadeCloseSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ON: Int = 0x1
        const val OFF: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取锁车收起遮阳帘状态
     * SRFR_LockCarSunRoofShadeCloseSw
     * 0x0:Not Active
     * 0x1:Enable
     * 0x2:Disable
     * 0x3:Reserved
     */
    object GetLockCarSunRoofShadeCloseSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ENABLE: Int = 0x1
        const val DISABLE: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 设置设防提示状态
     * ICC_lockSetSwitchSts
     * 0x0:Not Active
     * 0x1:OFF
     * 0x2:Only Lighting
     * 0x3:Sound and Lighting
     */
    object SetDefenseReminderSts {
        const val NOT_ACTIVE: Int = 0x0
        const val OFF: Int = 0x1
        const val ONLY_LIGHTING: Int = 0x2
        const val SOUND_AND_LIGHTING: Int = 0x3
    }

    /**
     * 获取设防提示状态
     * FLZCU_AlarmWarnSetSW
     * 0x0:Not Active
     * 0x1:OFF
     * 0x2:Only Lighting
     * 0x3:Sound and Lighting
     */
    object GetDefenseReminderSts {
        const val NOT_ACTIVE: Int = 0x0
        const val OFF: Int = 0x1
        const val ONLY_LIGHTING: Int = 0x2
        const val SOUND_AND_LIGHTING: Int = 0x3
    }

    /**
     * 设置儿童锁状态
     * ICC_ChildLockSW
     * 0x0:Not Active
     * 0x1:Window
     * 0x2:ChildLock_RL
     * 0x3:ChildLock_RR
     */
    object SetChildLockSts {
        const val NOT_ACTIVE: Int = 0x0
        const val WINDOW: Int = 0x1
        const val CHILD_LOCK_RL: Int = 0x2
        const val CHILD_LOCK_RR: Int = 0x3
    }

    /**
     * 获取左儿童锁状态
     * RL_ChildrenProtectSwitch
     * 0x0:Locked
     * 0x1:Unlocked
     * 0x2:Superlocked
     */
    object GetLeftChildLockSts {
        const val LOCKED: Int = 0x0
        const val UNLOCKED: Int = 0x1
        const val SUPERLOCKED: Int = 0x2
    }

    /**
     * 获取右儿童锁状态
     * RR_ChildrenProtectSwitch
     * 0x0:Locked
     * 0x1:Unlocked
     * 0x2:Superlocked
     */
    object GetRightChildLockSts {
        const val LOCKED: Int = 0x0
        const val UNLOCKED: Int = 0x1
        const val SUPERLOCKED: Int = 0x2
    }

    /**
     * 设置自动落锁状态
     * ICC -> FLZCU 信号名：ICC_AutolockSts
     * 0x0:Not Active
     * 0x1:Autolock mode
     * 0x2:Not autolock mode
     * 0x3:Not Used
     */
    object SetAutoLockSts {
        const val NOT_ACTIVE: Int = 0x0
        const val AUTOLOCK_MODE: Int = 0x1
        const val NOT_AUTOLOCK_MODE: Int = 0x2
        const val NOT_USED: Int = 0x3
    }

    /**
     * 获取自动落锁状态
     * FLZCU -> ICC 信号名：FLZCU_AutolockSts
     * 0x0:Not autolock mode
     * 0x1:Autolock mode
     */
    object GetAutoLockSts {
        const val NOT_AUTOLOCK_MODE: Int = 0x0
        const val AUTOLOCK_MODE: Int = 0x1
    }

    /**
     * 设置驻车自动解锁状态
     * ICC -> FLZCU 信号名：ICC_ParkUnlockEnable
     * 0x0:Not Active
     * 0x1:Disable
     * 0x2:Enable
     * 0x3:Reserved
     */
    object SetAutomaticParkUnlockSts {
        const val NOT_ACTIVE: Int = 0x0
        const val DISABLE: Int = 0x1
        const val ENABLE: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取驻车自动解锁状态
     * FLZCU -> ICC 信号名：FLZCU_ParkUnlockEnableFb
     * 0x0:Disable
     * 0x1:Enable
     */
    object GetAutomaticParkUnlockSts {
        const val DISABLE: Int = 0x0
        const val ENABLE: Int = 0x1
    }

    /**
     * 设置前遮阳帘状态
     * ICC_FShadReq
     * 0x0:Not Active
     * 0x1:Autoopen
     * 0x2:Autoclose
     * 0x3:Reserved
     */
    object SetFrontShadeSts {
        const val NOT_ACTIVE: Int = 0x0
        const val AUTO_OPEN: Int = 0x1
        const val AUTO_CLOSE: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取前遮阳帘状态
     * FRZCU_FShadSts
     * 0x0:Not Active
     * 0x1:Close
     * 0x2:Open
     * 0x3:Closing
     * 0x4:Opening
     * 0x5:Other Position
     * 0x6~0xF:Reserved
     */
    object GetFrontShadeSts {
        const val NOT_ACTIVE: Int = 0x0
        const val CLOSE: Int = 0x1
        const val OPEN: Int = 0x2
        const val CLOSING: Int = 0x3
        const val OPENING: Int = 0x4
        const val OTHER_POSITION: Int = 0x5
        const val RESERVED: Int = 0x6
    }

    /**
     * 设置后排阳帘状态
     * ICC_RShadReq
     * 0x0:Not Active
     * 0x1:Autoopen
     * 0x2:Autoclose
     * 0x3:Reserved
     */
    object SetRearShadeSts {
        const val NOT_ACTIVE: Int = 0x0
        const val AUTO_OPEN: Int = 0x1
        const val AUTO_CLOSE: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取后排遮阳帘状态
     * FRZCU_RShadSts
     * 0x0:Not Active
     * 0x1:Close
     * 0x2:Open
     * 0x3:Closing
     * 0x4:Opening
     * 0x5:Other Position
     * 0x6~0xF:Reserved
     */
    object GetRearShadeSts {
        const val NOT_ACTIVE: Int = 0x0
        const val CLOSE: Int = 0x1
        const val OPEN: Int = 0x2
        const val CLOSING: Int = 0x3
        const val OPENING: Int = 0x4
        const val OTHER_POSITION: Int = 0x5
        const val RESERVED: Int = 0x6
    }

    /**
     * 设置电动尾翼状态
     * ICC_SpoilerCtrlCmd
     * 0x0:Not Active
     * 0x1:Auto
     * 0x2:Open Level 1
     * 0x3:Open Level 2
     * 0x4:Close
     * 0x5~0x7:Reserved
     */
    object SetAutoTailSts {
        const val NOT_ACTIVE: Int = 0x0
        const val AUTO: Int = 0x1
        const val OPEN_LEVEL_1: Int = 0x2
        const val OPEN_LEVEL_2: Int = 0x3
        const val CLOSE: Int = 0x4
        const val RESERVED: Int = 0x5
    }

    /**
     * 获取电动尾翼状态
     * FLZCU_SpoilerCtrlFb
     * 0x0:Not Active
     * 0x1:Auto
     * 0x2:Open Level1
     * 0x3:Open Level2
     * 0x4:Close
     * 0x5~0x7:Reserved
     */
    object GetAutoTailSts {
        const val NOT_ACTIVE: Int = 0x0
        const val AUTO: Int = 0x1
        const val OPEN_LEVEL_1: Int = 0x2
        const val OPEN_LEVEL_2: Int = 0x3
        const val CLOSE: Int = 0x4
        const val RESERVED: Int = 0x5
    }

    /**
     * 电动尾翼UI状态
     * 0x0:CLOSE
     * 0x1:OPEN_LEVEL_1
     * 0x2:OPEN_LEVEL_2
     * 0x3:AUTO
     */
    object AutoTailUIState {
        const val CLOSE: Int = 0x0
        const val OPEN_LEVEL_1: Int = 0x1
        const val OPEN_LEVEL_2: Int = 0x2
        const val AUTO: Int = 0x3
    }

    /**
     * 设置感应靠近上锁状态
     * ICC_UIROpenSetCmd
     * 0x0:Not Active
     * 0x1:On
     * 0x2:Off
     * 0x3:Not Used
     */
    object SetApproachingUnlockSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ON: Int = 0x1
        const val OFF: Int = 0x2
        const val NOT_USED: Int = 0x3
    }

    /**
     * 获取感应靠近上锁状态
     * FLZCU_UIROpenStas
     * 0x0:OFF
     * 0x1:ON
     */
    object GetApproachingUnlockSts {
        const val OFF: Int = 0x0
        const val ON: Int = 0x1
    }

    /**
     * 设置感应离车上锁状态
     * ICC_WALOpenSetCmd
     * 0x0:Not Active
     * 0x1:ON
     * 0x2:OFF
     * 0x3:Not Used
     */
    object SetDepartureLockingSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ON: Int = 0x1
        const val OFF: Int = 0x2
        const val NOT_USED: Int = 0x3
    }

    /**
     * 获取感应离车上锁状态
     * FLZCU_WALOpenStas
     * 0x0:OFF
     * 0x1:ON
     */
    object GetDepartureLockingSts {
        const val OFF: Int = 0x0
        const val ON: Int = 0x1
    }

    /**
     * 设置锁车自动升窗状态
     * ICC -> FLZCU 信号名：ICC_LockCarWinCloseSw
     * 0x0:Not Active
     * 0x1:ON
     * 0x2:OFF
     * 0x3:Reserved
     */
    object SetLockAutoRaiseWindowSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ON: Int = 0x1
        const val OFF: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取锁车自动升窗状态
     * FLZCU -> ICC 信号名：FLZCU_LockCarWinCloseFb
     * 0x0:Enable
     * 0x1:Disable
     */
    object GetLockAutoRaiseWindowSts {
        const val ENABLE: Int = 0x0
        const val DISABLE: Int = 0x1
    }

    /**
     * 设置后视镜自动折叠状态
     * ICC -> FLZCU 信号名：IIC_AutoFoldSts
     * 0x0：Not Active
     * 0x1：Autofold mode
     * 0x2：Not autofold mode
     * 0x3：Not used
     */
    object SetAutoRearMirrorFoldSts {
        const val NOT_ACTIVE: Int = 0x0
        const val AUTO_FOLD_MODE: Int = 0x1
        const val NOT_AUTO_FOLD_MODE: Int = 0x2
        const val NOT_USED: Int = 0x3
    }

    /**
     * 获取后视镜自动折叠状态
     * FLZCU -> ICC 信号名：FLZCU_AutoFoldSts
     * 0x0：Not Autofold
     * 0x1：Autofold
     */
    object GetAutoRearMirrorFoldSts {
        const val NOT_AUTO_FOLD: Int = 0x0
        const val AUTO_FOLD: Int = 0x1
    }

    /**
     * 设置雨天自动加热后视镜状态
     * ICC_AutoHeatingset
     * 0x0:Not Active
     * 0x1:ON
     * 0x2:OFF
     * 0x3:Reserved
     */
    object SetAutoHeatingRearMirrorSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ON: Int = 0x1
        const val OFF: Int = 0x2
        const val RESERVED: Int = 0x3
    }

    /**
     * 获取雨天自动加热后视镜状态
     * FLZCU_AutoHeatingFb
     * 0x0:Close
     * 0x1:Open
     */
    object GetAutoHeatingRearMirrorSts {
        const val CLOSE: Int = 0x0
        const val OPEN: Int = 0x1
    }

    /**
     * 设置座椅便携进入状态
     * EasyEntryExitSet
     * 0x0:Not Active
     * 0x1:ON
     * 0x2:OFF
     * 0x3:Not Used
     */
    object SetSeatPortableSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ON: Int = 0x1
        const val OFF: Int = 0x2
        const val NOT_USED: Int = 0x3
    }

    /**
     * 获取座椅便携进入状态
     * EasyEntryExitFb
     * 0x0:Not Active
     * 0x1:ON
     * 0x2:OFF
     * 0x3:Not Used
     */
    object GetSeatPortableSts {
        const val NOT_ACTIVE: Int = 0x0
        const val ON: Int = 0x1
        const val OFF: Int = 0x2
        const val NOT_USED: Int = 0x3
    }

    /**
     * 发送方向盘自定义信号
     * ICC_MultiplexSignalStatusSet
     * 0x0:Not Active
     * 0x1:Mirror
     * 0x2:EPS
     * 0x3:HUD
     * 0x4:OFF
     * 0x5:Reserved
     * 0x6:Reserved
     * 0x7:Invalid
     */
    object SetMultiplexSignalStatusSts {
        const val NOT_ACTIVE: Int = 0x0
        const val MIRROR: Int = 0x1
        const val EPS: Int = 0x2
        const val HUD: Int = 0x3
        const val OFF: Int = 0x4
        const val RESERVED_1: Int = 0x5
        const val RESERVED_2: Int = 0x6
        const val INVALID: Int = 0x7
    }

    /**
     * 获取方向盘自定义信号
     * MFS_3_MultiplexSignalStatusSet
     * 0x0:Not Active
     * 0x1:Mirror
     * 0x2:EPS
     * 0x3:HUD
     * 0x4:OFF
     * 0x5:Reserved
     * 0x6:Reserved
     * 0x7:Invalid
     */
    object GetMultiplexSignalStatusSts {
        const val NOT_ACTIVE: Int = 0x0
        const val MIRROR: Int = 0x1
        const val EPS: Int = 0x2
        const val HUD: Int = 0x3
        const val OFF: Int = 0x4
        const val RESERVED_1: Int = 0x5
        const val RESERVED_2: Int = 0x6
        const val INVALID: Int = 0x7
    }

    /**
     * 设置倒车时后视镜自动调节状态
     * ICC_ReverseExtMirrorSts
     * 0x0:Not  Active
     * 0x1:OFF
     * 0x2:Only right side
     * 0x3:Only left side
     * 0x4:Both sides
     * 0x5:Not Used
     */
    object SetBackAutoRearMirrorAdjustSts {
        const val NOT_ACTIVE: Int = 0x0
        const val OFF: Int = 0x1
        const val ONLY_RIGHT_SIDE: Int = 0x2
        const val ONLY_LEFT_SIDE: Int = 0x3
        const val BOTH_SIDES: Int = 0x4
        const val NOT_USED: Int = 0x5
    }

    /**
     * 获取倒车时后视镜自动调节状态
     * FLZCU_ReverseExtMirrorSts
     * 0x0:OFF
     * 0x1:Only right side
     * 0x2:Only left side
     * 0x3:Both sides
     */
    object GetBackAutoRearMirrorAdjustSts {
        const val OFF: Int = 0x0
        const val ONLY_RIGHT_SIDE: Int = 0x1
        const val ONLY_LEFT_SIDE: Int = 0x2
        const val BOTH_SIDES: Int = 0x3
    }

    /**
     * 倒车时后视镜自动调节UI状态
     * 0x0:OFF
     * 0x1:Both sides
     * 0x2:Only left side
     * 0x3:Only right side
     */
    object BackAutoRearMirrorAdjustUIState {
        const val OFF: Int = 0x0
        const val BOTH_SIDES: Int = 0x1
        const val ONLY_LEFT_SIDE: Int = 0x2
        const val ONLY_RIGHT_SIDE: Int = 0x3
    }

    /**
     * 设置加油小门状态
     * LidOpenReq
     * 0x0:No Request
     * 0x1:Request
     */
    object SetRefuelSmallDoorSts {
        const val NO_REQUEST: Int = 0x0
        const val REQUEST: Int = 0x1
    }

    /**
     * 获取加油小门状态
     * FuelTankLidSts
     * 0x0:Close
     * 0x1:Open
     */
    object GetRefuelSmallDoorSts {
        const val CLOSE: Int = 0x0
        const val OPEN: Int = 0x1
    }

    /**
     * 设置后排屏状态
     * 0x0:关闭
     * 0x1:打开
     */
    object SetRearScreenControlSts {
        const val CLOSE: Int = 0x0
        const val OPEN: Int = 0x1
    }

    /**
     * 获取后排屏状态
     * 0x0:关闭
     * 0x1:打开
     */
    object GetRearScreenControlSts {
        const val CLOSE: Int = 0x0
        const val OPEN: Int = 0x1
    }

    /**
     * 设置雨刮灵敏度
     * ICC -> FLZCU 信号名：ICC_WiprSnvty
     * 0x0:Not Active
     * 0x1:Level 1
     * 0x2:Level 2
     * 0x3:Level 3
     * 0x4:Level 4
     * 0x5:Reserved
     * 0x6:Reserved
     */
    object SetWiperSensitivitySts {
        const val NOT_ACTIVE: Int = 0x0
        const val LEVEL_1: Int = 0x1
        const val LEVEL_2: Int = 0x2
        const val LEVEL_3: Int = 0x3
        const val LEVEL_4: Int = 0x4
        const val RESERVED: Int = 0x5
    }

    /**
     * 获取雨刮灵敏度状态
     * FLZCU -> ICC 信号名：FLZCU_WipeSensitivitySts
     * 0x0:Not Active
     * 0x1:Level 1
     * 0x2:Level 2
     * 0x3:Level 3
     * 0x4:Level 4
     * 0x5~0x7:Reserved
     */
    object GetWipeSensitivitySts {
        const val NOT_ACTIVE: Int = 0x0
        const val LEVEL_1: Int = 0x1
        const val LEVEL_2: Int = 0x2
        const val LEVEL_3: Int = 0x3
        const val LEVEL_4: Int = 0x4
        const val RESERVED: Int = 0x5
    }

    /**
     * 雨刮灵敏度UI状态
     * 0x0:LEVEL_1
     * 0x1:LEVEL_2
     * 0x2:LEVEL_3
     * 0x3:LEVEL_4
     */
    object WipeSensitivityUIState {
        const val LEVEL_1: Int = 0x0
        const val LEVEL_2: Int = 0x1
        const val LEVEL_3: Int = 0x2
        const val LEVEL_4: Int = 0x3
    }

    /**
     * 设置副驾安全气囊状态
     * ICC -> ACU 信号名：ICC_PABSetCmd
     * 0x0:NotActive
     * 0x1:PAB OFF
     * 0x2:PAB ON
     */
    object SetDriveAirbagSts {
        const val NOT_ACTIVE: Int = 0x0
        const val PAB_OFF: Int = 0x1
        const val PAB_ON: Int = 0x2
    }

    /**
     * 获取副驾安全气囊状态
     * ACU -> ICC 信号名：ABM_1_PABSetSts
     * 0x0:PAB OFF
     * 0x1:PAB ON
     */
    object GetDriveAirbagSts {
        const val PAB_OFF: Int = 0x0
        const val PAB_ON: Int = 0x1
    }

    /**
     * 方向盘自定义按键
     * 0x0: 行车记录仪抓拍
     * 0x1: AVM进入/退出
     * 0x2: 方向盘HUD调节
     * 0x3: 后视镜调节
     * 0x4: 音源切换
     * 0x5: 方向盘调节
     * 0x6: 全息影像
     */
    object CustomButtonType {
        const val RECORD_SNAPSHOT: Int = 0x0
        const val AVM_ENTER_EXIT: Int = 0x1
        const val DIRECTION_HUD_ADJUST: Int = 0x2
        const val REAR_MIRROR_ADJUST: Int = 0x3
        const val SOUND_SOURCE_SWITCH: Int = 0x4
        const val DIRECTION_ADJUST: Int = 0x5
        const val HOLOGRAM: Int = 0x6
    }

    /**
     * 遮阳帘初始化状态
     * sunShadeNormalized
     * 0x0:De-initialized
     * 0x1:Initialized
     */
    object SunShadeNormalizedSts {
        const val DE_INITIALIZED: Int = 0x0
        const val INITIALIZED: Int = 0x1
    }

    /**
     * 遮阳帘学习状态
     * sunShadeTeachRunSts
     * 0x0:no teach run
     * 0x1:teach run successful
     */
    object SunShadeTeachRunSts {
        const val NO_TEACH_RUN: Int = 0x0
        const val TEACH_RUN_SUCCESSFUL: Int = 0x1
    }

    /**
     * 遮阳帘传感器状态
     * sunshadeHallSensorSts
     * 0x0:Normal
     * 0x1:Fail
     */
    object SunShadeHallSensorSts {
        const val NORMAL: Int = 0x0
        const val FAIL: Int = 0x1
    }

    /**
     * 遮阳帘电机防夹状态
     * sunshadeAntiPinchSts
     * 0x0:Normal
     * 0x1:Active
     */
    object SunShadeAntiPinchSts {
        const val NORMAL: Int = 0x0
        const val ACTIVE: Int = 0x1
    }

    /**
     * 遮阳帘热防护状态
     * sunshadeThermalProtectionSts
     * 0x0: Normal
     * 0x1: Active
     */
    object SunShadeThermalProtectionSts {
        const val NORMAL: Int = 0x0
        const val ACTIVE: Int = 0x1
    }

    /**
     * 遮阳帘过压状态
     * sunShadeOverVoltageSts
     * 0x0: Normal
     * 0x1: OverVoltage
     */
    object SunShadeOverVoltageSts {
        const val NORMAL: Int = 0x0
        const val OVER_VOLTAGE: Int = 0x1
    }

    /**
     * 遮阳帘欠压状态
     * sunShadeUnderVoltageSts
     * 0x0: Normal
     * 0x1: UnderVoltage
     */
    object SunShadeUnderVoltageSts {
        const val NORMAL: Int = 0x0
        const val UNDER_VOLTAGE: Int = 0x1
    }

    /**
     * 方向盘自定义按键
     * 0x0: not active
     * 0x1: Mirror
     * 0x2: EPS
     * 0x3: HUD
     */
    object MfsSwitchModeSts {
        const val NOT_ACTIVE: Int = 0x0;
        const val MIRROR: Int = 0x1;
        const val EPS: Int = 0x2;
        const val HUD: Int = 0x3;
    }

    /**
     *  车门状态
     *  0x0: 关闭
     *  0x1: 开启
     *  0x2: 开启
     */
    object DoorStatus {
        const val CLOSE: Int = 0x0
        const val OPEN_1: Int = 0x1
        const val OPEN_2: Int = 0x2
    }

    /**
     * 引擎盖状态
     * 0x0: 关闭
     * 0x1: 开启
     * 0x2: 开启
     */
    object VehicleHoodSts {
        const val CLOSE: Int = 0x0
        const val OPEN_1: Int = 0x1
        const val OPEN_2: Int = 0x2
    }

    /**
     * 车辆档位状态获取
     * 0x0: Init
     * 0x1: P
     * 0x2: R
     * 0x3: N
     * 0x4: D
     * 0x5: Reserved
     * 0x6: Reserved
     * 0x7: Invalid
     */
    object VehicleGearSts {
        const val INIT: Int = 0x0
        const val P: Int = 0x1
        const val R: Int = 0x2
        const val N: Int = 0x3
        const val D: Int = 0x4
        const val RESERVED_1: Int = 0x5
        const val RESERVED_2: Int = 0x6
        const val INVALID: Int = 0x7
    }
}