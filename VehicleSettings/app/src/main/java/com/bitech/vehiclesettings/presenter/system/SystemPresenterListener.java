package com.bitech.vehiclesettings.presenter.system;

import android.content.Context;

import com.bitech.vehiclesettings.bean.AppPermissionBean;
import com.bitech.vehiclesettings.bean.PermissionAppBean;
import com.bitech.vehiclesettings.bean.PrivacyStatementBean;
import com.bitech.vehiclesettings.bean.RecordItemBean;
import com.bitech.vehiclesettings.bean.SystemDataBean;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface SystemPresenterListener {


    void gotoEngineMode();

    /**
     * 设置分析与改进
     * @param status 1 开启 0 关闭
     */
    void setSwAnalysis(int status);
    int getSwAnalysis();

    /**
     * 获取访问记录
     * @param position 菜单项
     * @return
     */
    List<RecordItemBean> getRecordsForPermission(int position);

    /**
     * 设置访问次数
     */
    int getRecordTimes(int position);

    /**
     * 获取授权app列表
     * @param position 菜单项
     * @return
     */
    List<AppPermissionBean> getPermissionAppList(int position);

    void setDateDisplay(int dateDisplay, Context context);
    int getDateDisplay();

    void setTimeDisplay(int timeDisplay,Context context);
    boolean getTimeDisplay(Context context);

    void setAutoCalibration(int autoCalibration);
    int getAutoCalibration();

    /**
     * 恢复出厂设置
     * \
     */
    void factoryReset();

    /**
     * 仪表油耗单位
     * @param state
     */
    public void setInstrumentFuelUnit(int state);
    public int getInstrumentFuelUnit();
    public void requestInstrumentFuelUnit();

    /**
     * 胎压单位
     * @param state
     */
    public void setTirePressureUnit(int state);
    public int getTirePressureUnit();
    public void requestTirePressureUnit();

    /**
     * 电耗单位
     * @param state
     */
    public void setPowerConsumptionUnit(int state);
    public int getPowerConsumptionUnit();
    public void requestPowerConsumptionUnit();

    /**
     * 单位设置
     * @param state
     */
    public void setUnitSetting(int state);
    public int getUnitSetting();

    /**
     * 温度单位
     * @param state
     */
    public void setTemperatureUnit(int state);
    public int getTemperatureUnit();

    /**
     * 权限应用开关
     * @param position
     * @param status
     */
    public void setSwitchPermission(int position, int status);
    public int getSwitchPermission(int position);

    /**
     * 权限时间分配
     * @param position
     * @param status
     */
    public void setPermissionDuring(int position, int status);
    public LocalDateTime getPermissionDuring(int position);
    public int getPermissionDuringStatus(int position);

    /**
     * 设置权限应用
     * @param position
     * @param index
     * @param bean
     */
    public int setPermissionApp(int position, int index, AppPermissionBean bean);
    public AppPermissionBean getPermissionApp(int position, int index);

    /**
     * 获取隐私政策详情
     * @return
     */
    public List<PrivacyStatementBean> getPrivacyStatementList();

    /**
     * 服务隐私协议
     * @param status
     */
    public void setServicePrivacyAgreement(int status);
    public int getServicePrivacyAgreement();

    /**
     * 设置系统数据
     * @param type
     * @return
     */
    List<SystemDataBean> getSystemDatatList(int type);
    int setSystemData(int type, int position, boolean isChecked);

    /**
     * 设置系统数据采集开启情况
     * @param index
     * @return
     */
    int getSystemDataAcquisitionStatus(int index);
    void setSystemDataAcquisitionStatus(int index, int status);

    /**
     * 是否位于P挡位
     * @return
     */
    boolean isPGear();

    /**
     * 判断是否为第一次启动
     * @return
     */
    boolean isFirstStart();

    /**
     * 获取、设置设备名称
     * @return
     */
    public String getDeviceInfo();
    public void setDeviceInfo(String info);

    /**
     * 获取存储信息
     * @return
     */
    public String getStorageMsg();

    /**
     * 获取系统信息
     */
    public String getSystemHardwareVersionInfo();
    public String getSystemSoftwareVersionInfo();
    public String getTBoxSoftwareVersionInfo();
    public String getTBoxHardwareVersionInfo();

    /**
     * 功能初始化
     */
    public Map<String, Boolean> systemReset();

    public void destroy();
}
