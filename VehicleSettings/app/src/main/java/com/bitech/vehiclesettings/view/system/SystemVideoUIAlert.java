package com.bitech.vehiclesettings.view.system;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSDeviceInfoBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSInfoVideoBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSInstrumentUnitBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSUnitSettingBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSVerifyInfoBinding;
import com.bitech.vehiclesettings.presenter.system.SystemPresenterListener;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.Locale;

public class SystemVideoUIAlert extends BaseDialog {
    private static final String TAG = SystemVideoUIAlert.class.getSimpleName();
    private static SystemVideoUIAlert.OnProgressChangedListener onProgressChangedListener;

    public SystemVideoUIAlert(@NonNull Context context) {
        super(context);
    }

    public SystemVideoUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected SystemVideoUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static SystemVideoUIAlert.OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(SystemVideoUIAlert.OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = true;
        private DialogAlertSInfoVideoBinding binding;
        private SystemVideoUIAlert dialog;
        private String originalDeviceName;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder(Context context, String originalDeviceName) {
            this.context = context;
            this.originalDeviceName = originalDeviceName;
        }

        public SystemVideoUIAlert create() {
            dialog = new SystemVideoUIAlert(context, R.style.Dialog);
            binding = DialogAlertSInfoVideoBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            // 1. 设置弹窗尺寸
            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1954;
                layoutParams.height = 1074;
                window.setAttributes(layoutParams);
            }

            return dialog;
        }
    }


    @Override
    public void dismiss() {
        unregisterReceiver(getContext());
        super.dismiss();
    }

    private void unregisterReceiver(Context context) {
        // Implementation for unregistering receivers
    }

    public interface OnProgressChangedListener {
        String getDeviceInfo();
        void setDeviceInfo(String info);
    }
}
