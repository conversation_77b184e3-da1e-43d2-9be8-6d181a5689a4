package com.bitech.vehiclesettings.fragment;

import android.annotation.SuppressLint;
import android.car.media.CarAudioManager;
import android.content.ContentResolver;
import android.content.SharedPreferences;
import android.database.ContentObserver;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.voice.IVoiceManagerListener;
import com.bitech.platformlib.manager.VoiceManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.bean.report.Content;
import com.bitech.vehiclesettings.bean.report.DataPoint;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundEffectAdjustmentBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundEqualizerBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundHeadrestSpeakerBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundVoiceCompensationBinding;
import com.bitech.vehiclesettings.databinding.FragmentVoiceBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.service.DataPointReportLifeCycle;
import com.bitech.vehiclesettings.service.VolumeChangeObserver;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.voice.AlarmTypeUIAlert;
import com.bitech.vehiclesettings.view.voice.EffectAdjustmentUIAlert;
import com.bitech.vehiclesettings.view.voice.EqualizerUIAlert;
import com.bitech.vehiclesettings.view.voice.HeadrestSpeakerUIAlert;
import com.bitech.vehiclesettings.view.voice.ResetVoiceUIAlert;
import com.bitech.vehiclesettings.view.voice.VoiceCompensationDescUIAlert;
import com.bitech.vehiclesettings.view.voice.VoiceCompensationUIAlert;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;
import com.bitech.vehiclesettings.viewmodel.VoiceViewModel;
import com.chery.ivi.vdb.client.listener.VDNotifyListener;
import com.chery.ivi.vdb.event.VDEvent;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.lion.datapoint.log.LogDataUtil;

import java.util.ArrayList;
import java.util.Objects;

public class VoiceFragment extends BaseFragment<FragmentVoiceBinding> implements SafeHandlerCallback, SharedPreferences.OnSharedPreferenceChangeListener {
    private static final String TAG = VoiceFragment.class.getSimpleName();
    private static VolumeChangeObserver mVolumeChangeObserver;

    private ArrayList<Content> dataList;
    AlarmTypeUIAlert alarmTypeUIAlert;
    VoiceCompensationUIAlert.Builder voiceCompensationUIAlert;
    VoiceCompensationDescUIAlert.Builder voiceCompensationUIAlertDesc;
    HeadrestSpeakerUIAlert.Builder headrestSpeakerUIAlert;
    EffectAdjustmentUIAlert.Builder effectAdjustmentUIAlert;
    ResetVoiceUIAlert.Builder resetUIAlert;
    private MainActViewModel mainActViewModel;
    public static final int EFFECT_ADJUST = 10;
    public static final int HEAD_REST = 11;
    private Handler debounceHandler = new Handler();
    private Runnable debounceRunnable;
    ContentObserver observer;
    ContentResolver resolver;
    Uri buttonSoundUir = Settings.System.getUriFor(Settings.System.SOUND_EFFECTS_ENABLED);
    Uri lowerMediaToneUir = Settings.System.getUriFor(PrefsConst.GlobalValue.LOWER_MEDIA_TONE);
    Uri voiceCallBroadcastUir = Settings.System.getUriFor(PrefsConst.GlobalValue.VOICE_CALL_BROADCAST);
    VoicePresenter presenter = VoicePresenter.getInstance();
    VoiceViewModel viewModel;
    private volatile boolean isActive;
    private SafeHandler voiceHandler;
    private VoiceManager manager = (VoiceManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_VOICE_MANAGER);
    private int lastUserProgress = 0;
    private void registerVoiceListen() {
        manager.addCallback(TAG, new IVoiceManagerListener() {

            @Override
            public void getAPCCallback(int value) {
                Log.d(TAG, "voice--onApcLevelLimit: " + value);
                if (value == CarNewEnergy.ApcLevelLimit.LEVEL_4 || value == CarNewEnergy.ApcLevelLimit.LEVEL_5) {
                    int voiceMedia = presenter.getVoiceMedia();
                    int mediaBrightnessLimit = presenter.getMediaBrightnessLimit(voiceMedia);
                    presenter.setVoiceMedia(mediaBrightnessLimit);
                    if (Objects.isNull(binding)) {
                        return;
                    }
                    binding.sbMedia.setProgress(mediaBrightnessLimit);
                    binding.sbMedia.setSeekBarStyle(getContext(), binding.sbMedia.getSeekBar());
                    SliceReceiver.notifyChange(ProviderURI.VOICE);
                }
            }

            @Override
            public void getEQCallback(int signal) {
                int status = CarVoice.EQ.signalReverse(signal);
                if (status == CarVoice.EQ.INVALID) return;
                if (Objects.isNull(binding)) {
                    return;
                }
                if (viewModel.getEq().getValue() != null && viewModel.getEq().getValue() != status) {
                    Log.d(TAG, "getEQCallback Callback status: " + status);
                    viewModel.setEq(status);
                    effectAdjustmentUIAlert.updateEQUI(status);
                }
            }

            @Override
            public void getSurroundSoundCallback(int signal) {
                int status = CarVoice.SurroundSound.signalReverse(signal);
                if (status == CarVoice.SurroundSound.INVALID) return;
                if (Objects.isNull(binding)) {
                    return;
                }
                if (viewModel.getSurroundSound().getValue() != null && viewModel.getSurroundSound().getValue() != status) {
                    Log.d(TAG, "getSurroundSoundCallback Callback status: " + status);
                    viewModel.setSurroundSound(status);
                    effectAdjustmentUIAlert.updateSurroundSound(status);
                }
            }

            @Override
            public void getVirtualSceneCallback(int signal) {
                int status = CarVoice.VirtualScene.signalReverse(signal);
                if (status == CarVoice.VirtualScene.INVALID) return;
                if (Objects.isNull(binding)) {
                    return;
                }
                if (presenter.getEQ() != CarVoice.EQ.ALL) {
                    viewModel.setVirtualScene(0);
                    effectAdjustmentUIAlert.updateVirtualScene(0);
                    return;
                }
                Integer uiValue = viewModel.getVirtualScene().getValue();
                uiValue = VoicePresenter.virtualSceneUI2Signal(uiValue);
                if (uiValue != status) {
                    Log.d(TAG, "getVirtualSceneCallback Callback status: " + status);
                    int index = VoicePresenter.virtualSceneSignal2UI(status);
                    viewModel.setVirtualScene(index);
                    if (effectAdjustmentUIAlert.isShowing()) {
                        effectAdjustmentUIAlert.updateVirtualScene(index);
                    }
                }
            }

            @Override
            public void getHeadRestCallback(int signal) {
                if (signal == -1 || signal == Integer.MIN_VALUE) return;
                if (Objects.isNull(binding)) {
                    return;
                }
                if (viewModel.getHeadRest().getValue() != null && viewModel.getHeadRest().getValue() != signal) {
                    Log.d(TAG, "getHeadRestCallback Callback status: " + signal);
                    headrestSpeakerUIAlert.updateHeadRest(signal);
                }
            }

            @Override
            public void getSubBassCallback(int status) {
                if (Objects.isNull(binding)) {
                    return;
                }
                if (status == CarVoice.Equalization.NOT_ACTIVE) {
                    return;
                }
                if (viewModel.getSubBass().getValue() != null && viewModel.getSubBass().getValue() != status) {
                    Log.d(TAG, "getSubBassCallback Callback status: " + status);
                    viewModel.setSubBass(status);
                }
            }

            @Override
            public void getBassCallback(int status) {
                if (Objects.isNull(binding)) {
                    return;
                }
                if (status == CarVoice.Equalization.NOT_ACTIVE) {
                    return;
                }
                if (viewModel.getBass().getValue() != null && viewModel.getBass().getValue() != status) {
                    Log.d(TAG, "getBassCallback Callback status: " + status);
                    viewModel.setBass(status);
                }
            }

            @Override
            public void getLowMidCallback(int status) {
                if (Objects.isNull(binding)) {
                    return;
                }
                if (status == CarVoice.Equalization.NOT_ACTIVE) {
                    return;
                }
                if (viewModel.getLowMid().getValue() != null && viewModel.getLowMid().getValue() != status) {
                    Log.d(TAG, "getLowMidCallback Callback status: " + status);
                    viewModel.setLowMid(status);
                }
            }

            @Override
            public void getMidCallback(int status) {
                if (Objects.isNull(binding)) {
                    return;
                }
                if (status == CarVoice.Equalization.NOT_ACTIVE) {
                    return;
                }
                if (viewModel.getMid().getValue() != null && viewModel.getMid().getValue() != status) {
                    Log.d(TAG, "getMidCallback Callback status: " + status);
                    viewModel.setMid(status);
                }
            }

            @Override
            public void getHighMidCallback(int status) {
                if (Objects.isNull(binding)) {
                    return;
                }
                if (status == CarVoice.Equalization.NOT_ACTIVE) {
                    return;
                }
                if (viewModel.getHighMid().getValue() != null && viewModel.getHighMid().getValue() != status) {
                    Log.d(TAG, "getHighMidCallback Callback status: " + status);
                    viewModel.setHighMid(status);
                }
            }

            @Override
            public void getTrebleCallback(int status) {
                if (Objects.isNull(binding)) {
                    return;
                }
                if (status == CarVoice.Equalization.NOT_ACTIVE) {
                    return;
                }
                if (viewModel.getTreble().getValue() != null && viewModel.getTreble().getValue() != status) {
                    Log.d(TAG, "getTrebleCallback Callback status: " + status);
                    viewModel.setTreble(status);
                }
            }

            @Override
            public void getSuperTrebleCallback(int status) {
                if (Objects.isNull(binding)) {
                    return;
                }
                if (status == CarVoice.Equalization.NOT_ACTIVE) {
                    return;
                }
                if (viewModel.getSuperTreble().getValue() != null && viewModel.getSuperTreble().getValue() != status) {
                    Log.d(TAG, "getSuperTrebleCallback Callback status: " + status);
                    viewModel.setSuperTreble(status);
                }
            }

            @Override
            public void getCompensationCallback(int signal) {
                int status = CarVoice.Compensation.signalReverse(signal);
                if (status == CarVoice.Compensation.INVALID || status == Integer.MIN_VALUE) return;
                if (Objects.isNull(binding)) {
                    return;
                }
                if (viewModel.getCompensation().getValue() != null && viewModel.getCompensation().getValue() != status) {
                    Log.d(TAG, "getCompensationCallback Callback status: " + status);
                    voiceCompensationUIAlert.updateCompensation(status);
                }
            }

            @Override
            public void getVoiceMediaCallback(int status) {

            }

            @Override
            public void getVoiceNaviCallback(int status) {

            }

            @Override
            public void getVoiceVRCallback(int status) {

            }

            @Override
            public void getVoicePhoneCallback(int status) {

            }
        });
        manager.registerListener();

        VDNotifyListener listener = new VDNotifyListener() {
            @Override
            public void onVDNotify(VDEvent vdEvent, int i) {

            }
        };
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isActive = true;
        Log.d(TAG, "onCreate voiceHandler: ");
        voiceHandler = new SafeHandler(this);
        registerVoiceListen();
    }

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null) return;
        loadPageAnim(binding.scrollView, currentPosition, position);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = getLayoutResId(inflater, container).getRoot();
        if (binding != null) {
            initObserver();
        }
        return rootView;
    }

    @Override
    protected FragmentVoiceBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentVoiceBinding.inflate(inflater, container, false);
        return binding;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        resolver = requireContext().getContentResolver();

        observer = new ContentObserver(new Handler(Looper.getMainLooper())) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                super.onChange(selfChange, uri);
                if (binding != null && getContext() != null) {
                    switch (uri.getLastPathSegment()) {
                        case Settings.System.SOUND_EFFECTS_ENABLED:
                            int soundButton = Prefs.getSystemValue(Settings.System.SOUND_EFFECTS_ENABLED, PrefsConst.TRUE);
                            binding.swButtonSound.setChecked(soundButton == 1);
                            Log.d(TAG, "按键音状态变更回调：设置为 " + soundButton);
                            break;
                        case PrefsConst.GlobalValue.LOWER_MEDIA_TONE:
                            int lowerMediaTone = Prefs.getSystemValue(PrefsConst.GlobalValue.LOWER_MEDIA_TONE, PrefsConst.TRUE);
                            binding.swVoiceLowerMediaTone.setChecked(lowerMediaTone == 1);
                            Log.d(TAG, "导航压低媒体音状态变更回调：设置为 " + lowerMediaTone);
                            break;
                        case PrefsConst.GlobalValue.VOICE_CALL_BROADCAST:
                            int callBroadcast = Prefs.getSystemValue(PrefsConst.GlobalValue.VOICE_CALL_BROADCAST, PrefsConst.TRUE);
                            binding.swCallBroadcast.setChecked(callBroadcast == 1);
                            Log.d(TAG, "来电播报状态变更回调：设置为 " + callBroadcast);
                            break;
                    }
                }
            }
        };

        resolver.registerContentObserver(buttonSoundUir, false, observer);
        resolver.registerContentObserver(lowerMediaToneUir, false, observer);
        resolver.registerContentObserver(voiceCallBroadcastUir, false, observer);
    }

    private void processTargetDialogEvent(TargetDialogInfo targetDialog) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= " + targetDialog);
        if (targetDialog == null) {
            return;
        }
        //具体Tab索引
        if (targetDialog.getTargetTab() == MainActivity.MainTabIndex.VOICE) {
            switch (targetDialog.getTargetDialog()) {
                case EFFECT_ADJUST:
                    if (targetDialog.getOperation() == 1) {
                        if (effectAdjustmentUIAlert == null) {
                            effectAdjustmentUIAlert = new EffectAdjustmentUIAlert.Builder(mContext, presenter, viewModel, voiceHandler);
                        }
                        effectAdjustmentUIAlert.create().show();
                    }
                    break;
                case HEAD_REST:
                    if (targetDialog.getOperation() == 1) {
                        if (headrestSpeakerUIAlert == null) {
                            headrestSpeakerUIAlert = new HeadrestSpeakerUIAlert.Builder(mContext, presenter, viewModel, voiceHandler);
                        }
                        headrestSpeakerUIAlert.create().show();
                    }
                    break;
            }
        }
    }

    @Override
    protected void initView() {
        initData();
        binding.getRoot().post(this::onViewClicked);
        mVolumeChangeObserver = new VolumeChangeObserver(mContext);
        mVolumeChangeObserver.registerReceiver();
        int usageMedia = VoicePresenter.getInstance().carAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA);
        int usagePhone = VoicePresenter.getInstance().carAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION);
        int usageNavi = VoicePresenter.getInstance().carAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE);
        int usageVr = VoicePresenter.getInstance().carAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT);

        VoicePresenter.getInstance().carAudioManager.registerCarVolumeCallback(new CarAudioManager.CarVolumeCallback() {
            @Override
            public void onGroupVolumeChanged(int zoneId, int groupId, int flags) {
                if (groupId == usageMedia) {
                    if (binding == null) return;
                    int volume = presenter.getVoiceMedia();
                    if (volume == lastUserProgress) {
                        return;
                    }
                    Log.d(TAG, "媒体音量变化: " + volume);
                    binding.sbMedia.setProgress(volume);
                } else if (groupId == usagePhone) {
                    if (binding == null) return;
                    int volume = presenter.getVoicePhone();
                    Log.d(TAG, "电话音量变化: " + volume);
                    binding.sbPhone.setProgress(volume);
                } else if (groupId == usageNavi) {
                    if (binding == null) return;
                    int volume = presenter.getVoiceNavi();
                    Log.d(TAG, "导航音量变化: " + volume);
                    binding.sbNavi.setProgress(volume);
                } else if (groupId == usageVr) {
                    if (binding == null) return;
                    int volume = presenter.getVoiceVR();
                    Log.d(TAG, "语音音量变化: " + volume);
                    binding.sbVR.setProgress(volume);
                } else {
                    Log.d(TAG, "未知的音量组 ID: " + groupId);
                }
            }

        });
    }

    public void initData() {
        viewModel.setVirtualScene(presenter.getVirtualScene());
        viewModel.setHeadRest(presenter.getHeadRest());
        viewModel.setCallBroadcast(presenter.getCallBroadcast());
        viewModel.setLowSpeedAnalog(presenter.getLowSpeedAnalog());
        viewModel.setMediaProgress(presenter.getVoiceMedia());
        viewModel.setNaviProgress(presenter.getVoiceNavi());
        viewModel.setVRProgress(presenter.getVoiceVR());
        viewModel.setPhoneProgress(presenter.getVoicePhone());
        viewModel.setVoiceExternalMode(presenter.getVoiceExternalMode());
        viewModel.setVoiceLowerMediaTone(presenter.getVoiceLowerMediaTone());
        viewModel.setButtonSound(Prefs.get(PrefsConst.VOICE_BUTTON_SOUND, CarVoice.ButtonSound.DEFAULT));
        viewModel.setAlarmType(presenter.getAlarmType());
        initSeekbar();
    }

    public void initSeekbar() {
        binding.sbNavi.setProgress(presenter.getVoiceNavi());
        binding.sbNavi.setSeekBarStyle(getContext(), binding.sbNavi.getSeekBar());
        binding.sbVR.setProgress(presenter.getVoiceVR());
        binding.sbVR.setSeekBarStyle(getContext(), binding.sbVR.getSeekBar());
        binding.sbMedia.setProgress(presenter.getVoiceMedia());
        binding.sbMedia.setSeekBarStyle(getContext(), binding.sbMedia.getSeekBar());
        binding.sbPhone.setProgress(presenter.getVoicePhone());
        binding.sbPhone.setSeekBarStyle(getContext(), binding.sbPhone.getSeekBar());
        int voiceAlarm = presenter.getVoiceAlarm();
        binding.sbAlarm.setProgress(voiceAlarm <= 0 ? 1 : voiceAlarm);
        binding.sbAlarm.setSeekBarStyle(getContext(), binding.sbAlarm.getSeekBar());
    }

    @SuppressLint("CheckResult")
    public void onViewClicked() {

        if (binding == null) {
            Log.e(TAG, "Binding is null in onViewClicked");
            return;
        }

        // 展示更多
        binding.llSdMore.setOnClickListener(v -> moreChange());
        // 报警音类型
        binding.rlSoundAlarmType.setOnClickListener(v -> {
            if (alarmTypeUIAlert == null) {
                alarmTypeUIAlert = new AlarmTypeUIAlert(mContext);
            }
            alarmTypeUIAlert.show();
        });
        // 随速补偿
        binding.rlVoiceCompensation.setOnClickListener(v -> {
            if (voiceCompensationUIAlert == null) {
                voiceCompensationUIAlert = new VoiceCompensationUIAlert.Builder(mContext, presenter, viewModel, voiceHandler);
            }
            voiceCompensationUIAlert.create().show();
        });

        binding.ivVoiceCompensationDesc.setOnClickListener(v -> {
            if (voiceCompensationUIAlert == null) {
                voiceCompensationUIAlert = new VoiceCompensationUIAlert.Builder(mContext, presenter, viewModel, voiceHandler);
            }
            voiceCompensationUIAlert.create().show();
        });

        // 随速补偿描述
        binding.ivVoiceCompensation.setOnClickListener(v -> {
            if (voiceCompensationUIAlertDesc == null) {
                voiceCompensationUIAlertDesc = new VoiceCompensationDescUIAlert.Builder(mContext);
            }
            voiceCompensationUIAlertDesc.create().show();
        });
        // 头枕扬声器
        binding.flHeadrestSpeaker.setOnClickListener(v -> {
            if (headrestSpeakerUIAlert == null) {
                headrestSpeakerUIAlert = new HeadrestSpeakerUIAlert.Builder(mContext, presenter, viewModel, voiceHandler);
            }
            headrestSpeakerUIAlert.create().show();
        });
        // 音效调节
        binding.flSoundEffect.setOnClickListener(v -> {
            if (effectAdjustmentUIAlert == null) {
                effectAdjustmentUIAlert = new EffectAdjustmentUIAlert.Builder(mContext, presenter, viewModel, voiceHandler);
            }
            effectAdjustmentUIAlert.create().show();
        });
        // 来电播报
        binding.swCallBroadcast.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setCallBroadcast(CommonUtils.BoolToInt(isChecked));
            presenter.setCallBroadcast(CommonUtils.BoolToInt(isChecked));
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_CALL_BROADCAST);
        });
        // 车外低速模拟音
        binding.swLowSpeedAnalog.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                presenter.setLowSpeedAnalog(CarVoice.AVAS.ON);
            } else {
                presenter.setLowSpeedAnalog(CarVoice.AVAS.OFF);
            }
            SliceReceiver.notifyChange(ProviderURI.AVAS);
        });
        // 外放模式
        binding.swVoiceExternalMode.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setVoiceExternalMode(CommonUtils.BoolToInt(isChecked));
            presenter.setVoiceExternalMode(isChecked ? CarVoice.ExternalMode.IN : CarVoice.ExternalMode.OUT);
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_EXTERNAL_MODE);
        });
        // 导航压低媒体音
        binding.swVoiceLowerMediaTone.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setVoiceLowerMediaTone(CommonUtils.BoolToInt(isChecked));
            presenter.setVoiceLowerMediaTone(CommonUtils.BoolToInt(isChecked));
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_LOWER_MEDIA_TONE);
        });
        // 按键音
        binding.swButtonSound.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setButtonSound(CommonUtils.BoolToInt(isChecked));
            presenter.setButtonSound(CommonUtils.BoolToInt(isChecked));
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_BUTTON_SOUND);
        });


        binding.sbMedia.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {


            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    lastUserProgress = progress;
                    int limited = presenter.getMediaBrightnessLimit(lastUserProgress);
                    seekBar.setProgress(limited);
                }
                binding.sbMedia.setSeekBarStyle(mContext, seekBar);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(false);
                if (debounceRunnable != null) {
                    debounceHandler.removeCallbacks(debounceRunnable);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(true);
                debounceRunnable = () -> {
                    presenter.setVoiceMedia(lastUserProgress);
                    presenter.playMediaAudio();
                };
                debounceHandler.postDelayed(debounceRunnable, 300);
                binding.sbMedia.setSeekBarStyle(mContext, seekBar);
            }
        });

        binding.sbPhone.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                binding.sbPhone.setSeekBarStyle(mContext, seekBar);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(false);
                if (debounceRunnable != null) {
                    debounceHandler.removeCallbacks(debounceRunnable);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(true);
                binding.sbPhone.setSeekBarStyle(mContext, seekBar);
                presenter.playPhoneAudio();
                debounceRunnable = () -> {
                    presenter.setVoicePhone(seekBar.getProgress());
                    presenter.playPhoneAudio();
                };
                debounceHandler.postDelayed(debounceRunnable, 300);
            }
        });

        binding.sbVR.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                binding.sbVR.setSeekBarStyle(mContext, seekBar);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(false);
                if (debounceRunnable != null) {
                    debounceHandler.removeCallbacks(debounceRunnable);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(true);
                debounceRunnable = () -> {
                    presenter.setVoiceVR(seekBar.getProgress());
                    presenter.playVRAudio();
                };
                debounceHandler.postDelayed(debounceRunnable, 300);
                binding.sbVR.setSeekBarStyle(mContext, seekBar);
            }
        });

        binding.sbNavi.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                binding.sbNavi.setSeekBarStyle(mContext, seekBar);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(false);
                if (debounceRunnable != null) {
                    debounceHandler.removeCallbacks(debounceRunnable);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(true);
                debounceRunnable = () -> {
                    presenter.setVoiceNavi(seekBar.getProgress());
                    presenter.playNaviAudio();
                };
                debounceHandler.postDelayed(debounceRunnable, 300);
                binding.sbNavi.setSeekBarStyle(mContext, seekBar);
            }
        });
        binding.sbAlarm.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (seekBar.getProgress() < 1) seekBar.setProgress(1);
                binding.sbAlarm.setSeekBarStyle(mContext, seekBar);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(false);
                if (debounceRunnable != null) {
                    debounceHandler.removeCallbacks(debounceRunnable);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(true);
                debounceRunnable = () -> {
                    presenter.setVoiceAlarm(seekBar.getProgress());
                };
                binding.sbAlarm.setSeekBarStyle(mContext, seekBar);
                debounceHandler.postDelayed(debounceRunnable, 300);
            }
        });


        binding.rbReset.setOnClickListener(v -> {
            if (resetUIAlert == null) {
                resetUIAlert = new ResetVoiceUIAlert.Builder(mContext);
                resetUIAlert.setOnDialogClickListener(new ResetVoiceUIAlert.Builder.OnDialogClickListener() {

                    @Override
                    public void onConfirm() {
                        presenter.reset();
                        onResume();
                    }

                    @Override
                    public void onCancel() {
                    }
                });
            }
            resetUIAlert.create().show();
        });

    }

    public void moreChange() {
        if (binding.llMedia.getVisibility() == View.GONE) {
            binding.llMedia.setVisibility(View.VISIBLE);
        } else {
            binding.llMedia.setVisibility(View.GONE);
        }
        if (binding.llPhone.getVisibility() == View.GONE) {
            binding.llPhone.setVisibility(View.VISIBLE);
        } else {
            binding.llPhone.setVisibility(View.GONE);
        }
        if (binding.llAlarm.getVisibility() == View.GONE) {
            binding.llAlarm.setVisibility(View.VISIBLE);
        } else {
            binding.llAlarm.setVisibility(View.GONE);
        }
        binding.tvSdMore.setText(binding.llMedia.getVisibility() == View.GONE ? R.string.str_wrapper_more : R.string.str_wrapper_less);
        binding.ivSdMore.setImageResource(binding.llMedia.getVisibility() == View.GONE ? R.mipmap.ic_expand : R.mipmap.ic_retract);
    }

    @Override
    protected void setListener() {
        EqualizerUIAlert.setOnChangedListener(new EqualizerUIAlert.ChangedListener() {
            @Override
            public void initObserve(DialogAlertSoundEqualizerBinding binding) {
                viewModel.getSubBass().observe(getViewLifecycleOwner(), (signal) -> {
                    binding.sbSubBass.setProgress(CarVoice.Equalization.signalReverse(signal));
                });
                viewModel.getBass().observe(getViewLifecycleOwner(), (signal) -> {
                    binding.sbBass.setProgress(CarVoice.Equalization.signalReverse(signal));
                });
                viewModel.getLowMid().observe(getViewLifecycleOwner(), (signal) -> {
                    binding.sbLowMid.setProgress(CarVoice.Equalization.signalReverse(signal));
                });
                viewModel.getMid().observe(getViewLifecycleOwner(), (signal) -> {
                    binding.sbMid.setProgress(CarVoice.Equalization.signalReverse(signal));
                });
                viewModel.getHighMid().observe(getViewLifecycleOwner(), (signal) -> {
                    binding.sbHighMid.setProgress(CarVoice.Equalization.signalReverse(signal));
                });
                viewModel.getTreble().observe(getViewLifecycleOwner(), (signal) -> {
                    binding.sbTreble.setProgress(CarVoice.Equalization.signalReverse(signal));
                });
                viewModel.getSuperTreble().observe(getViewLifecycleOwner(), (signal) -> {
                    binding.sbSuperTreble.setProgress(CarVoice.Equalization.signalReverse(signal));
                });
            }

            @SuppressLint("NonConstantResourceId")
            @Override
            public void onChanged(SeekBar seekBar, int progress, DialogAlertSoundEqualizerBinding binding) {
                switch (seekBar.getId()) {
                    case R.id.sbSubBass:
                        binding.tvSubBass.setText(String.valueOf(seekBar.getProgress() - 7));
                        viewModel.setSubBass(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        presenter.setSubBass(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        voiceHandler.sendMessageDelayed(MessageConst.VOICE_SUB_BASS);
                        break;
                    case R.id.sbBass:
                        binding.tvBass.setText(String.valueOf(seekBar.getProgress() - 7));
                        viewModel.setBass(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        presenter.setBass(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        voiceHandler.sendMessageDelayed(MessageConst.VOICE_BASS);
                        break;
                    case R.id.sbLowMid:
                        binding.tvLowMid.setText(String.valueOf(seekBar.getProgress() - 7));
                        viewModel.setLowMid(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        presenter.setLowMid(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        voiceHandler.sendMessageDelayed(MessageConst.VOICE_LOW_MID);
                        break;
                    case R.id.sbMid:
                        binding.tvMid.setText(String.valueOf(seekBar.getProgress() - 7));
                        viewModel.setMid(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        presenter.setMid(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        voiceHandler.sendMessageDelayed(MessageConst.VOICE_MID);
                        break;
                    case R.id.sbHighMid:
                        binding.tvHighMid.setText(String.valueOf(seekBar.getProgress() - 7));
                        viewModel.setHighMid(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        presenter.setHighMid(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        voiceHandler.sendMessageDelayed(MessageConst.VOICE_HIGH_MID);
                        break;
                    case R.id.sbTreble:
                        binding.tvTreble.setText(String.valueOf(seekBar.getProgress() - 7));
                        viewModel.setTreble(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        presenter.setTreble(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        voiceHandler.sendMessageDelayed(MessageConst.VOICE_TREBLE);
                        break;
                    case R.id.sbSuperTreble:
                        binding.tvSuperTreble.setText(String.valueOf(seekBar.getProgress() - 7));
                        viewModel.setSuperTreble(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        presenter.setSuperTreble(CarVoice.Equalization.signal2UI(seekBar.getProgress()));
                        voiceHandler.sendMessageDelayed(MessageConst.VOICE_SUPER_TREBLE);
                        break;
                    default:
                        break;
                }
            }
        });
        HeadrestSpeakerUIAlert.setOnChangedListener(new HeadrestSpeakerUIAlert.ChangedListener() {
            @Override
            public void initObserve(DialogAlertSoundHeadrestSpeakerBinding binding) {
                viewModel.getHeadRest().observe(getViewLifecycleOwner(), headRest -> {
                    switch (headRest) {
                        case CarVoice.HeadRest.SHARE:
                            binding.spvHeadrest.setSelectedIndex(0, true);
                            break;
                        case CarVoice.HeadRest.DRIVING:
                            binding.spvHeadrest.setSelectedIndex(1, true);
                            break;
                        case CarVoice.HeadRest.PRIVATE:
                            binding.spvHeadrest.setSelectedIndex(2, true);
                            break;
                    }
                });
            }
        });
        EffectAdjustmentUIAlert.setOnChangedListener(new EffectAdjustmentUIAlert.ChangedListener() {

            @Override
            public void initObserve(DialogAlertSoundEffectAdjustmentBinding binding) {
                viewModel.getEq().observe(getViewLifecycleOwner(), eq -> {
                    eq = CarVoice.EQ.signalReverse(eq);
                    if (eq == CarVoice.EQ.INVALID) {
                        return;
                    }
                    effectAdjustmentUIAlert.checkEQAndHeadrest();
                    effectAdjustmentUIAlert.updateEQUI(eq);
                    binding.swSurroundSound.setChecked(Prefs.get(PrefsConst.VOICE_SURROUND_SOUND, CarVoice.SurroundSound.DEFAULT) == CarVoice.SurroundSound.ON);
//                    binding.tvCoordinates.setText(String.format(Locale.getDefault(), "坐标: (%d,%d)", viewModel.getPositionX().getValue(), viewModel.getPositionY().getValue()));
                });
                viewModel.getVirtualScene().observe(getViewLifecycleOwner(), virtualScene -> {
                    if (virtualScene == CarVoice.VirtualScene.INVALID) {
                        return;
                    }
                    binding.spvVirtual.setSelectedIndex(virtualScene, true);
//                    effectAdjustmentUIAlert.updateVirtualScene(virtualScene);
                });
                viewModel.getSurroundSound().observe(getViewLifecycleOwner(), surroundSound -> {
                    surroundSound = CarVoice.SurroundSound.signalReverse(surroundSound);
                    if (surroundSound == CarVoice.SurroundSound.INVALID) {
                        return;
                    }
                    binding.swSurroundSound.setChecked(surroundSound == CarVoice.SurroundSound.ON);
                    effectAdjustmentUIAlert.checkEQAndHeadrest();
                    effectAdjustmentUIAlert.updateSurroundSound(surroundSound);
                });
            }
        });

        VoiceCompensationUIAlert.setOnChangedListener(new VoiceCompensationUIAlert.ChangedListener() {

            @Override
            public void initObserve(DialogAlertSoundVoiceCompensationBinding binding) {
                viewModel.getCompensation().observe(getViewLifecycleOwner(), compensation -> {
                    switch (compensation) {
                        case CarVoice.Compensation.OFF:
                            binding.spvVC.setSelectedIndex(0, true);
                            break;
                        case CarVoice.Compensation.LOW:
                            binding.spvVC.setSelectedIndex(1, true);
                            break;
                        case CarVoice.Compensation.MIDDLE:
                            binding.spvVC.setSelectedIndex(2, true);
                            break;
                        case CarVoice.Compensation.HIGH:
                            binding.spvVC.setSelectedIndex(3, true);
                    }
                });
            }
        });
    }

    @Override
    protected void initObserve() {

    }

    /**
     * ui更新
     */
    private void initObserver() {
        //MainActivity的ViewModel
        mainActViewModel = new ViewModelProvider(requireActivity()).get(MainActViewModel.class);
        processTargetDialogEvent(mainActViewModel.getTargetDialogLiveEvent().getValue());
        mainActViewModel.getTargetDialogLiveEvent().observe(getViewLifecycleOwner(), this::processTargetDialogEvent);
        viewModel = new ViewModelProvider(this).get(VoiceViewModel.class);
        // 头枕扬声器
        viewModel.getHeadRest().observe(getViewLifecycleOwner(), this::updateHeadRestUI);
        // 虚拟现场
        viewModel.getVirtualScene().observe(getViewLifecycleOwner(), (status) -> {
            updateVirtualScene(viewModel.getVirtualScene().getValue());
        });
        // 来电播报
        viewModel.getCallBroadcast().observe(getViewLifecycleOwner(), this::updateCallBroadcast);
        // 车外低速模拟音
        viewModel.getLowSpeedAnalog().observe(getViewLifecycleOwner(), this::updateLowSpeedAnalog);
        // 外放模式
        viewModel.getVoiceExternalMode().observe(getViewLifecycleOwner(), this::updateVoiceExternalMode);
        // 导航压低媒体音
        viewModel.getVoiceLowerMediaTone().observe(getViewLifecycleOwner(), this::updateVoiceLowerMediaTone);
        // 按键音
        viewModel.getButtonSound().observe(getViewLifecycleOwner(), this::updateButtonSound);
        // 媒体音
        viewModel.getMediaProgress().observe(getViewLifecycleOwner(), this::updateMedia);
        // 导航音
        viewModel.getNaviProgress().observe(getViewLifecycleOwner(), this::updateNavi);
        // 电话音
        viewModel.getPhoneProgress().observe(getViewLifecycleOwner(), this::updatePhone);
        // 语音
        viewModel.getVRProgress().observe(getViewLifecycleOwner(), this::updateVR);
    }

    private void updateHeadRestUI(Integer status) {
        Log.d(TAG, "头枕扬声器: " + status);
        if (status < 0) {
            Log.d(TAG, "信号错误");
            return;
        }
        binding.tvHeadrest.setText(getString(VoicePresenter.VoiceConstant.getHeadRest(status)));
        Log.d(TAG, "头枕扬声器: " + binding.tvHeadrest.getText());
    }

    private void updateVirtualScene(Integer status) {
        Log.d(TAG, "虚拟现场: " + status);
        if (status < 0) {
            Log.d(TAG, "信号错误");
            return;
        }
        binding.tvEffect.setText(getString(CarVoice.VirtualScene.virtualSceneText(status)));
        Log.d(TAG, "虚拟现场: " + binding.tvEffect.getText());
    }

    private void updateCallBroadcast(Integer status) {
        Log.d(TAG, "来电播报: " + status);
        binding.swCallBroadcast.setChecked(CommonUtils.IntToBool(status));
    }

    private void updateLowSpeedAnalog(Integer status) {
        Log.d(TAG, "车外低速模拟音: " + status);
        binding.swLowSpeedAnalog.setChecked(CommonUtils.IntToBool(status));
    }

    private void updateMedia(int status) {
        Log.d(TAG, "媒体音变化: " + status);
        binding.sbMedia.setProgress(status);
    }

    private void updateNavi(int status) {
        Log.d(TAG, "导航音变化: " + status);
        binding.sbNavi.setProgress(status);
    }

    private void updatePhone(int status) {
        Log.d(TAG, "电话音变化: " + status);
        binding.sbPhone.setProgress(status);
    }

    private void updateVR(int status) {
        Log.d(TAG, "语音变化: " + status);
        binding.sbVR.setProgress(status);
    }

    private void updateVoiceExternalMode(Integer status) {
        Log.d(TAG, "外放模式: " + status);
        binding.swVoiceExternalMode.setChecked(CommonUtils.IntToBool(status));
    }

    private void updateVoiceLowerMediaTone(Integer status) {
        Log.d(TAG, "导航时压低媒体音: " + status);
        binding.swVoiceLowerMediaTone.setChecked(CommonUtils.IntToBool(status));
    }

    private void updateButtonSound(Integer status) {
        Log.d(TAG, "按键音: " + status);
        binding.swButtonSound.setChecked(CommonUtils.IntToBool(status));
    }

    @Override
    public void onResume() {
        super.onResume();
        initSeekbar();
        initView();
        initData();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isActive = false;
        if (manager != null) {
            manager.removeCallback(TAG);
            manager.unregisterListener();
            manager = null;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 注销 ContentObserver，防止泄漏或空指针
        if (resolver != null && observer != null) {
            resolver.unregisterContentObserver(observer);
            Log.d(TAG, "已注销 ContentObserver");
            observer = null;
        }

        if (binding != null) {
            binding = null;
        }
        if (mVolumeChangeObserver != null) {
            mVolumeChangeObserver.unregisterReceiver();
        }
    }

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.VOICE_CALL_BROADCAST:
                int callBroadcast = presenter.getCallBroadcast();
                if (callBroadcast == Integer.MIN_VALUE) break;
                if (callBroadcast != viewModel.getCallBroadcast().getValue()) {
                    viewModel.setCallBroadcast(callBroadcast);
                }
                break;
            case MessageConst.VOICE_EXTERNAL_MODE:
                int voiceExternalMode = presenter.getVoiceExternalMode();
                if (voiceExternalMode == Integer.MIN_VALUE) break;
                if (voiceExternalMode != viewModel.getVoiceExternalMode().getValue()) {
                    viewModel.setVoiceExternalMode(voiceExternalMode);
                }
                break;
            case MessageConst.VOICE_LOWER_MEDIA_TONE:
                int voiceLowerMediaTone = presenter.getVoiceLowerMediaTone();
                if (voiceLowerMediaTone == Integer.MIN_VALUE) break;
                if (voiceLowerMediaTone != viewModel.getVoiceLowerMediaTone().getValue()) {
                    viewModel.setVoiceLowerMediaTone(voiceLowerMediaTone);
                }
                break;
            case MessageConst.VOICE_BUTTON_SOUND:
                int buttonSound = presenter.getButtonSound();
                if (buttonSound == Integer.MIN_VALUE) break;
                if (buttonSound != viewModel.getButtonSound().getValue()) {
                    viewModel.setButtonSound(buttonSound);
                }
            case MessageConst.VOICE_HEADREST:
                int headrest = presenter.getHeadRest();
                if (headrest == Integer.MIN_VALUE) break;
                if (headrest != viewModel.getHeadRest().getValue()) {
                    viewModel.setHeadRest(headrest);
                }
                break;
            case MessageConst.VOICE_SUB_BASS:
                int subBass = presenter.getSubBass();
                if (subBass == Integer.MIN_VALUE || subBass == CarVoice.Equalization.NOT_ACTIVE) {
                    break;
                }
                if (subBass != viewModel.getSubBass().getValue()) {
                    viewModel.setSubBass(subBass);
                }
                break;
            case MessageConst.VOICE_BASS:
                int bass = presenter.getBass();
                if (bass == Integer.MIN_VALUE || bass == CarVoice.Equalization.NOT_ACTIVE) {
                    break;
                }
                if (bass != viewModel.getBass().getValue()) {
                    viewModel.setBass(bass);
                }
                break;
            case MessageConst.VOICE_LOW_MID:
                int lowMid = presenter.getLowMid();
                if (lowMid == Integer.MIN_VALUE || lowMid == CarVoice.Equalization.NOT_ACTIVE) {
                    break;
                }
                if (lowMid != viewModel.getLowMid().getValue()) {
                    viewModel.setLowMid(lowMid);
                }
                break;
            case MessageConst.VOICE_MID:
                int mid = presenter.getMid();
                if (mid == Integer.MIN_VALUE || mid == CarVoice.Equalization.NOT_ACTIVE) {
                    break;
                }
                if (mid != viewModel.getMid().getValue()) {
                    viewModel.setMid(mid);
                }
                break;
            case MessageConst.VOICE_HIGH_MID:
                int highMid = presenter.getHighMid();
                if (highMid == Integer.MIN_VALUE || highMid == CarVoice.Equalization.NOT_ACTIVE) {
                    break;
                }
                if (highMid != viewModel.getHighMid().getValue()) {
                    viewModel.setHighMid(highMid);
                }
                break;
            case MessageConst.VOICE_TREBLE:
                int treble = presenter.getTreble();
                if (treble == Integer.MIN_VALUE || treble == CarVoice.Equalization.NOT_ACTIVE) {
                    break;
                }
                if (treble != viewModel.getTreble().getValue()) {
                    viewModel.setTreble(treble);
                }
                break;
            case MessageConst.VOICE_SUPER_TREBLE:
                int superTreble = presenter.getSuperTreble();
                if (superTreble == Integer.MIN_VALUE || superTreble == CarVoice.Equalization.NOT_ACTIVE) {
                    break;
                }
                if (superTreble != viewModel.getSuperTreble().getValue()) {
                    viewModel.setSuperTreble(superTreble);
                }
                break;
            case MessageConst.VOICE_SURROUND_SOUND:
                int surroundSound = presenter.getSurroundSound();
                if (surroundSound != viewModel.getSurroundSound().getValue()) {
                    viewModel.setSurroundSound(surroundSound);
                }
                effectAdjustmentUIAlert.updateSurroundSound(surroundSound);
                break;
            case MessageConst.VOICE_EQ:
                int eq = presenter.getEQ();
                if (eq != viewModel.getEq().getValue()) {
                    viewModel.setEq(eq);
                }
                effectAdjustmentUIAlert.updateEQUI(eq);
                break;
            case MessageConst.VOICE_VIRTUAL_SCENE:
                int virtualScene = presenter.getVirtualScene();
                virtualScene = VoicePresenter.virtualSceneSignal2UI(virtualScene);
                if (virtualScene != viewModel.getVirtualScene().getValue()) {
                    viewModel.setVirtualScene(virtualScene);
                }
                effectAdjustmentUIAlert.updateVirtualScene(virtualScene);
                break;
            case MessageConst.VOICE_COMPENSATION:
                int compensation = presenter.getCompensation();
                if (compensation == Integer.MIN_VALUE) break;
                if (compensation != viewModel.getCompensation().getValue()) {
                    viewModel.setCompensation(compensation);
                }
                break;
            default:
                break;
        }

    }

    @Override
    public boolean isActive() {
        return isActive;
    }

    @Override
    public void onStop() {
        if (EffectAdjustmentUIAlert.isShow) {
            effectAdjustmentUIAlert.dismiss();
        }
        if (HeadrestSpeakerUIAlert.isShow) {
            headrestSpeakerUIAlert.dismiss();
        }
        super.onStop();
    }

    @Override
    public void onPause() {
        super.onPause();
        presenter.releasePlayer();
        Log.d(TAG, "onPause: ");
        // 埋点上报数据
        DataPoint dataPoint = new DataPoint();
        dataPoint.setId(CommonConst.DataPoint.id);
        dataPoint.setEventId(CommonConst.EventId.Drive_Set);
        dataPoint.setTimestamp(System.currentTimeMillis());
        dataPoint.setSupplierCode(CommonConst.DataPoint.supplierCode);
        dataPoint.setPlatformCode(CommonConst.DataPoint.platformCode);
        dataPoint.setNodeType(LogDataUtil.NODE_TYPE_ADD_OPERATION);
        // 上报数据驾驶页面
        ArrayList<Content> list = getData();
        if (!list.isEmpty()) {
            dataPoint.setContent(list);
        }

        LiveEventBus
                .get(DataPointReportLifeCycle.KEY_DATA_POINT)
                .post(dataPoint);
    }

    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, @Nullable String key) {
        if (binding == null || !isAdded()) {
            return;
        }

        switch (key) {
            case PrefsConst.VOICE_EXTERNAL_MODE:
                int status = Prefs.getInt(PrefsConst.VOICE_EXTERNAL_MODE);
                binding.swVoiceExternalMode.setChecked(status == PrefsConst.TRUE);
                break;
        }
    }

    //region 埋点数据上报
    private ArrayList<Content> getData() {
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        return dataList;
    }

    private Content reportData(String attributeId, String locationId, String attributeValue) {
        Content content = new Content();
        content.setAttributeId(attributeId);
        content.setLocationId(locationId);
        content.setAttributeValue(attributeValue);
        return content;
    }
}
