package com.bitech.vehiclesettings.fragment;

import static android.content.Context.WIFI_SERVICE;
import static com.bitech.platformlib.bean.Topics.Connect.CONNECT_FIVE_G_STATUS_MONITOR;
import static com.bitech.platformlib.bean.Topics.Connect.HIGH_TEMPERATURE_REMIND;
import static com.bitech.platformlib.bean.Topics.Connect.PHONE_CHARGE_STATUS;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelProvider;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.manager.ConnectManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.bean.report.Content;
import com.bitech.vehiclesettings.bean.report.DataPoint;
import com.bitech.vehiclesettings.broadcast.BluetoothReceiver;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.broadcast.WifiReceiver;
import com.bitech.vehiclesettings.carapi.constants.CarConnect;
import com.bitech.vehiclesettings.databinding.FragmentConnectBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.connect.ConnectPresenter;
import com.bitech.vehiclesettings.presenter.connect.ConnectPresenterListener;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.service.DataPointReportLifeCycle;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.connect.BtFragment;
import com.bitech.vehiclesettings.view.connect.ChargingRemindUIAlert;
import com.bitech.vehiclesettings.view.connect.HighTemperatureRemindUIAlert;
import com.bitech.vehiclesettings.view.connect.HotpotFragment;
import com.bitech.vehiclesettings.view.connect.WifiFragment;
import com.bitech.vehiclesettings.view.connect.WirelessChargingUIAlert;
import com.bitech.vehiclesettings.viewmodel.BtViewModel;
import com.bitech.vehiclesettings.viewmodel.ConnectViewModel;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;
import com.bitech.vehiclesettings.viewmodel.WifiViewModel;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.base.VDKey;
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.lion.datapoint.log.LogDataUtil;

import java.util.ArrayList;


public class ConnectFragment extends BaseFragment<FragmentConnectBinding> implements SafeHandlerCallback {
    private static final String TAG = ConnectFragment.class.getSimpleName();

    View rlWirelessCharging;
    private final BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
    private boolean mUpdating = false;
    private boolean isBlueOpen = false;
    private boolean isWifiOpen = false;
    private WifiManager wifiManager;
    // WIFI广播接收器对象
    private WifiReceiver wifiReceiver;
    //Bluetooth广播接收器对象
    private BluetoothReceiver bluetoothReceiver;
    private ConnectPresenterListener presenter;
    // 无线充电
    private WirelessChargingUIAlert.Builder wirelessChargingUIAlert;
    // 高温提醒弹窗
    private HighTemperatureRemindUIAlert.Builder highTemperatureRemindUIAlert;
    // 弹窗
    private DetailsUIAlert.Builder detailUIAlert;
    private Boolean isUserTouching = false;

    DetailsUIAlert.Builder forgetDialog;

    private WifiViewModel wifiViewModel;
    private BtViewModel btViewModel;
    private ConnectViewModel viewModel;
    private SafeHandler handler;
    private volatile boolean isActive;
    private ArrayList<Content> dataList;
    ConnectManager manager = (ConnectManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_CONNECT_MANAGER);
    private MainActViewModel mainActViewModel;
    private WifiFragment wifiFragment;
    public static final int WIFI_FRAGMENT = 10;
    private final String[] carPropertyIds = new String[]{
            PHONE_CHARGE_STATUS,
            HIGH_TEMPERATURE_REMIND,
            Topics.Connect.PHONE_LEAVE_ALERT,
            CONNECT_FIVE_G_STATUS_MONITOR,
            Topics.Connect.WIRELESS_CHARGE,
            Topics.Connect.PHONE_FORGOTTEN_STATUS
    };

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null) return;
        loadPageAnim(binding.scrollView, currentPosition, position);
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = getLayoutResId(inflater, container).getRoot();
        initView();
        setListener();
        return rootView;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isActive = true;
        // 获取WifiViewModel实例
        wifiViewModel = new ViewModelProvider(this).get(WifiViewModel.class);
        btViewModel = new ViewModelProvider(this).get(BtViewModel.class);
        handler = new SafeHandler(this);
    }

    @Override
    protected FragmentConnectBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentConnectBinding.inflate(inflater, container, false);
        presenter = new ConnectPresenter(mContext);
        // 高温提醒弹窗
        highTemperatureRemindUIAlert = new HighTemperatureRemindUIAlert.Builder(mContext);
        return binding;
    }

    private void initReceiver() {
        // 初始化WIFI状态广播
        wifiReceiver = new WifiReceiver();
        wifiReceiver.registerWifiStateListener(
                this.getClass().getSimpleName(),
                new WifiReceiver.WifiStateListener() {
                    @Override
                    public void onWifiPasswordError() {
                    }

                    @Override
                    public void onNetworkStateChange(@NonNull WifiInfo wifiInfo, @NonNull NetworkInfo networkInfo) {
                    }

                    @Override
                    public void onWifiScanResult() {
                    }

                    @Override
                    public void onWifiHotspotLeave(@NonNull String hotspotMac) {
                    }

                    @Override
                    public void onWifiHotspotJoin(@NonNull String hotspotMac, @NonNull String hotspotName) {

                    }

                    //TODO 实现热点状态回调
                    @Override
                    public void onWifiHotspotStateChange(int state) {
                        Log.d(TAG, "onWifiStateChange : zhc6whu 热点 wifi state = " + state);
                        switch (state) {
                            case 13:
                                // WIFI热点开关打开
                                binding.swHotpot.setChecked(true);
                                binding.tvHotpotDesc.setText(getString(R.string.str_toggle_open));
                                break;
                            case 11:
                                // WIFI热点开关关闭
                                binding.swHotpot.setChecked(false);
                                binding.tvHotpotDesc.setText(getString(R.string.str_toggle_close));
                                break;
                        }
                        SliceReceiver.notifyChange(ProviderURI.BROADCAST);
                    }


                    @Override
                    public void onWifiStateChange(int state) {
                        Log.d(TAG, "onWifiStateChange : wifi state = " + state);
                        switch (state) {
                            case WifiManager.WIFI_STATE_ENABLED:
                                // WIFI开关打开
                                binding.swWifi.setChecked(true);
                                break;
                            case WifiManager.WIFI_STATE_DISABLED:
                                // WIFI开关关闭
                                binding.swWifi.setChecked(false);
                                break;
                        }
                    }
                }
        );
        //蓝牙监听广播
        bluetoothReceiver = new BluetoothReceiver();
        // 注册蓝牙广播监听
        bluetoothReceiver.registerBtStateListener(this.getClass().getSimpleName(), new BluetoothReceiver.BluetoothStateListener() {

            @Override
            public void onBtACLDisconnected(@NonNull BluetoothDevice device, int reason) {
            }

            @Override
            public void onBtPbapAndMapConnectState(int state, int preState, @NonNull BluetoothDevice device) {
            }

            @Override
            public void onBtA2dpConnectState(int state, int preState, @NonNull BluetoothDevice device) {
            }

            @Override
            public void onBtHfpConnectState(int state, int preState, @NonNull BluetoothDevice device) {
            }

            @Override
            public void onPairedCode(@NonNull BluetoothDevice device, @NonNull String pairingKey) {
            }

            @Override
            public void onPairedState(int pairedState, @NonNull BluetoothDevice device) {

            }

            @Override
            public void onStopScan() {
            }

            @Override
            public void onStartScan() {

            }

            @Override
            public void onBtScanDevice(@NonNull BluetoothDevice device, short deviceRssi) {

            }

            @Override
            public void onBluetoothFoundStateChanged(int foundState) {

            }

            @Override
            public void onBluetoothStateChanged(int state) {
                Log.d(TAG, "onBluetoothStateChanged : bt state = " + state);
                switch (state) {
                    case BluetoothAdapter.STATE_OFF:
                        // 蓝牙关闭
                        if (binding != null) {
                            binding.swBlue.setChecked(false);
                        }
                        break;
                    case BluetoothAdapter.STATE_ON:
                        // 蓝牙开启
                        if (binding != null) {
                            binding.swBlue.setChecked(true);
                        }
                        break;
                }

            }

            @Override
            public void onBtNameChanged(@NonNull String btName) {

            }
        });

    }

    private void unInitReceiver() {
        //注销广播
        if (wifiReceiver != null) {
            wifiReceiver.unregisterWifiStateListener(ConnectFragment.class.getSimpleName());
            wifiReceiver = null;
        }
        if (bluetoothReceiver != null) {
            bluetoothReceiver.unregisterBtStateListener(ConnectFragment.class.getSimpleName());
            bluetoothReceiver = null;
        }
    }

    //初始化当前页面状态
    @Override
    protected void initView() {
        wifiManager = (WifiManager) mContext.getApplicationContext().getSystemService(WIFI_SERVICE);
        onViewClicked();
        initReceiver();
        //判断当前蓝牙状态
        if (mBluetoothAdapter != null) {
            if (mBluetoothAdapter.isEnabled()) {
                isBlueOpen = true;
                binding.swBlue.setChecked(true);
                binding.tvBlueDesc.setText(getString(R.string.str_toggle_open));
            } else {
                isBlueOpen = false;
                binding.swBlue.setChecked(false);
                binding.tvBlueDesc.setText(getString(R.string.str_toggle_close));
            }
        }
        //判断当前Wifi状态
        if (wifiManager != null) {
            if (wifiManager.isWifiEnabled()) {
                isWifiOpen = true;
                if (wifiManager.getWifiState() == WifiManager.WIFI_STATE_ENABLED) {
                    binding.swWifi.setChecked(true);
                }
                binding.tvWifiDesc.setText(getString(R.string.str_toggle_open));
            } else {
                isWifiOpen = false;
                binding.swWifi.setChecked(false);
                binding.tvWifiDesc.setText(getString(R.string.str_toggle_close));
            }
        }
        if (wifiViewModel.getHotspotState()) {
            binding.swHotpot.setChecked(wifiViewModel.getHotspotState());
            binding.tvHotpotDesc.setText(getString(R.string.str_toggle_open));
        } else {
            binding.swHotpot.setChecked(wifiViewModel.getHotspotState());
            binding.tvHotpotDesc.setText(getString(R.string.str_toggle_close));
        }
    }

    protected void initData() {
        viewModel.initData();
    }


    @Override
    protected void setListener() {
        //连接界面的switch监听，switch打开为启用蓝牙，关闭为关闭蓝牙
        binding.swBlue.setOnCheckedChangeListener(
                (buttonView, isChecked) -> {
                    binding.swBlue.setEnabled(false);
                    if (isChecked) {
                        btViewModel.setBluetoothStatus(true);
                        binding.tvBlueDesc.setText(getString(R.string.str_toggle_open));
                    } else {
                        btViewModel.setBluetoothStatus(false);
                        binding.tvBlueDesc.setText(getString(R.string.str_toggle_close));
                        VDEvent event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE); // 失败了会返回null
                        if (event != null) {
                            Bundle bundle = event.getPayload();
                            int status = bundle.getInt(VDKey.STATUS);
                            int type = bundle.getInt(VDKey.TYPE);
                            if (status > 0 && type == 8) {
                                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        binding.swBlue.setChecked(btViewModel.getBluetoothStatus()); // 2 秒后检查热点状态并更新 Switch
                                    }
                                }, 2000); // 延迟 2000 毫秒（2 秒）
                            }
                        }
                    }
                    // 使用 Handler 延迟恢复按钮状态
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (isAdded() && getView() != null) { // 检查Fragment是否存活
                                binding.swBlue.setEnabled(true);
                            }
                        }
                    }, 1000); // 0.5 秒后恢复可点击状态
                }
        );
        //连接界面的switch监听，switch打开为启用wifi，关闭为关闭wifi
        binding.swWifi.setOnCheckedChangeListener(
                (buttonView, isChecked) -> {
                    // 创建实例
                    binding.swWifi.setEnabled(false);
                    if (isChecked) {
                        wifiViewModel.setWifiState(true);
                        binding.tvWifiDesc.setText(getString(R.string.str_toggle_open));
                    } else {
                        wifiViewModel.setWifiState(false);
                        binding.tvWifiDesc.setText(getString(R.string.str_toggle_close));
                    }
                    // 使用 Handler 延迟恢复按钮状态
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (binding == null || !isAdded()) {
                                return;
                            }
                            binding.swWifi.setEnabled(true);
                        }
                    }, 1000); // 0.5 秒后恢复可点击状态
                }
        );
        binding.swHotpot.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    isUserTouching = true;
                }
                return false;
            }
        });
        binding.swHotpot.setOnCheckedChangeListener(
                (buttonView, isChecked) -> {
                    // 禁用按钮，防止重复点击
                    if (isUserTouching) {
                        isUserTouching = false;
                        binding.swHotpot.setEnabled(false);
                        if (isChecked) {
                            wifiViewModel.setHotspotState(true);
                            binding.tvHotpotDesc.setText(getString(R.string.str_toggle_open));
                        } else {
                            wifiViewModel.setHotspotState(false);
                            binding.tvHotpotDesc.setText(getString(R.string.str_toggle_close));
                            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    binding.swHotpot.setChecked(wifiViewModel.getHotspotState()); // 2 秒后检查热点状态并更新 Switch
                                }
                            }, 2000); // 延迟 2000 毫秒（2 秒）
                        }
                        // 使用 Handler 延迟恢复按钮状态
                        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                binding.swHotpot.setEnabled(true);
                            }
                        }, 2000); // 0.5 秒后恢复可点击状态
                    }
                }
        );
        binding.sw5G.setOnCheckedChangeListener((buttonView, isChecked) -> {
            int signalVal = isChecked ? CarConnect.FiveG_set.FIVEG_ON : CarConnect.FiveG_set.FIVEG_OFF;
            viewModel.setMobileNetworkState(signalVal);
            handler.sendMessageDelayed(MessageConst.CONNECT_FIVE_G);
        });

        WirelessChargingUIAlert.setOnProgressChangedListener(new WirelessChargingUIAlert.onProgressChangedListener() {
            @Override
            public void onFrontSwitch(int signalVal) {
                viewModel.setFrontChargingState(signalVal);
                handler.sendMessageDelayed(MessageConst.CONNECT_FRONT_CHARGING);

                if (signalVal == CarConnect.ICC_CWCWorkingStsSet.ON) {
                    // 无线充电开始获取上次遗忘提醒的值
                    int preForgetReminderState = Prefs.get(PrefsConst.CONNECT_FORGET_REMIND_STATE, CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.OFF);
                    // 上一次遗忘提醒时开启，也需要开启遗忘提醒
                    if (preForgetReminderState == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.ON) {
                        viewModel.setForgetReminder(CarConnect.ICC_CWCPhoneforgottenFunStsSet.PHONE_FORGOTTEN_FUN_ON);
                    }
                } else if (signalVal == CarConnect.ICC_CWCWorkingStsSet.OFF) {
                    // 关闭时需要记住上一次遗忘提醒的状态
                    Prefs.put(PrefsConst.CONNECT_FORGET_REMIND_STATE, viewModel.forgetReminderLiveData.getValue());
                    // 遗忘提醒也要关闭
                    if (viewModel.forgetReminderLiveData.getValue() == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.ON) {
                        viewModel.setForgetReminder(CarConnect.ICC_CWCPhoneforgottenFunStsSet.PHONE_FORGOTTEN_FUN_OFF);
                        if (wirelessChargingUIAlert != null) {
                            wirelessChargingUIAlert.updateForgetReminderUI(CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.OFF);
                        }
                    }
                }
            }

            @Override
            public void onBackChargingSwitch(int state) {
                // 后排无需求
            }

            @Override
            public void onForgetReminderSwitch(int state) {
                viewModel.setForgetReminder(state);
                handler.sendMessageDelayed(MessageConst.CONNECT_FORGET_REMINDER);
            }

            @Override
            public int getFrontWirelessChargingState() {
                return presenter.getFrontChargingState();
            }

            @Override
            public int getForgetRemindState() {
                return presenter.getForgetReminder();
            }
        });
        // 无线充电 充电温馨提醒
        ChargingRemindUIAlert.setOnProgressChangedListener(new ChargingRemindUIAlert.onProgressChangedListener() {
            @Override
            public void onSwitch(int state) {
                Log.d(TAG, "onSwitch: " + state);
                presenter.setChargingRemindState(state);
            }
        });
        binding.ivWirelessRemind.setOnClickListener(v -> {
            openTipsDialog(getString(R.string.str_warm_reminder_for_charging), getString(R.string.str_warm_reminder_for_charging_content_1), 1176, 488, Gravity.LEFT);
        });
    }

    public void onViewClicked() {
        binding.llBluetooth.setOnClickListener(v -> {
            //展示WifiDialogFragment
            openBluetoothDialog();
        });
        binding.llWifi.setOnClickListener(v -> {
            //展示WifiDialogFragment
            openWifiDialog();
        });
        binding.llHotpot.setOnClickListener(v -> {
            //展示WifiDialogFragment
            openHotspotDialog();
        });
        binding.llWirelessCharging.setOnClickListener(v -> {
            wirelessChargingUIAlert = new WirelessChargingUIAlert.Builder(mContext);
            wirelessChargingUIAlert.create().show();
        });
    }

    public void openBluetoothDialog() {
        FragmentManager fragmentManager = requireActivity().getSupportFragmentManager();
        Fragment existingBtFragment = fragmentManager.findFragmentByTag("BluetoothDialogFragment");
        if (existingBtFragment == null || !existingBtFragment.isVisible()) {
            BtFragment btFragment = new BtFragment();
            btFragment.show(fragmentManager, "BluetoothDialogFragment");
        }
    }


    public void openWifiDialog() {
        FragmentManager fragmentManager = requireActivity().getSupportFragmentManager();
        Fragment existingWifiFragment = fragmentManager.findFragmentByTag("WifiDialogFragment");
        if (existingWifiFragment == null || !existingWifiFragment.isVisible()) {
            WifiFragment dialogFragment = new WifiFragment();
            dialogFragment.show(fragmentManager, "WifiDialogFragment");
        }
    }

    public void hideWifiDialog() {
        if (wifiFragment != null && wifiFragment.getShowsDialog()) {
            wifiFragment.dismiss();
        }
    }

    public void openHotspotDialog() {
        FragmentManager fragmentManager = requireActivity().getSupportFragmentManager();
        Fragment existingHotspotFragment = fragmentManager.findFragmentByTag("HotspotDialogFragment");
        if (existingHotspotFragment == null || !existingHotspotFragment.isVisible()) {
            HotpotFragment dialogFragment = new HotpotFragment();
            dialogFragment.show(fragmentManager, "HotspotDialogFragment");
        }
    }

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.CONNECT_FRONT_CHARGING:
                // 获得前排充电状态
                viewModel.getFrontChargingState();
                break;
            case MessageConst.CONNECT_FORGET_REMINDER:
                // 获得遗忘提醒状态
                viewModel.getForgetReminder();
                break;
            case MessageConst.CONNECT_BACK_CHARGING:
                // 获得后排充电状态 -- 需求暂无
                break;
            case MessageConst.CONNECT_FIVE_G:
                // 获得5G网络状态
                viewModel.getMobileNetworkState();
                break;
            case MessageConst.CONNECT_WIRELESS_CHARGING_REMINDER:
                // 获得无线充电提醒状态
                break;
            default:
                break;
        }
    }

    @Override
    public boolean isActive() {
        return isActive;
    }

    protected void initObserve() {
        //MainActivity的ViewModel
        mainActViewModel = new ViewModelProvider(requireActivity()).get(MainActViewModel.class);
        processTargetDialogEvent(mainActViewModel.getTargetDialogLiveEvent().getValue());
        mainActViewModel.getTargetDialogLiveEvent().observe(getViewLifecycleOwner(), this::processTargetDialogEvent);
        viewModel = new ViewModelProvider(this).get(ConnectViewModel.class);
        // 前排充电 更新UI
        viewModel.frontChargingLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateFrontChargingUI(signalVal);
        });
        // 遗忘提醒 更新UI
        viewModel.forgetReminderLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateForgetReminderUI(signalVal);
        });
        // 5G开关 更新 UI
        viewModel.fiveGLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updateFiveGUI(signalVal);
        });
        // 手机充电状态
        viewModel.phoneChargingLiveData.observe(getViewLifecycleOwner(), signalVal -> {
            updatePhoneChargingUI(signalVal);
        });
    }

    private void updatePhoneChargingUI(Integer signalVal) {
        Integer frontCharging = viewModel.frontChargingLiveData.getValue();
        if (frontCharging == 0) {
            binding.tvWxcdDesc.setText(getString(R.string.str_wxcd_close));
            return;
        }
        if (signalVal == 0) {
            binding.tvWxcdDesc.setText(getString(R.string.str_wxcd_desc));
        } else if (signalVal == 1) {
            binding.tvWxcdDesc.setText(getString(R.string.str_wxcd_charging));
        } else if (signalVal == 2) {
            binding.tvWxcdDesc.setText(getString(R.string.str_wxcd_completed));
        } else if (signalVal == 3) {
            binding.tvWxcdDesc.setText(getString(R.string.str_wxcd_error));
        }
    }

    private void processTargetDialogEvent(TargetDialogInfo targetDialog) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= " + targetDialog);
        if (targetDialog == null) {
            return;
        }
        //具体Tab索引
        if (targetDialog.getTargetTab() == MainActivity.MainTabIndex.CONNECT) {
            switch (targetDialog.getTargetDialog()) {
                case ConnectFragment.WIFI_FRAGMENT:
                    if (targetDialog.getOperation() == 1) {
                        openWifiDialog();
                    }
                    break;
                default:
                    break;
            }
        }
        mainActViewModel.getTargetDialogLiveEvent().setValue(null);
    }

    private void updateFiveGUI(Integer signalVal) {
        if (signalVal == 1) {
            binding.sw5G.setChecked(true);
            binding.tvFiveGDesc.setText(getString(R.string.str_toggle_open));
        } else {
            binding.sw5G.setChecked(false);
            binding.tvFiveGDesc.setText(getString(R.string.str_toggle_close));
        }
    }

    private void updateForgetReminderUI(Integer signalVal) {
        if (wirelessChargingUIAlert != null) {
            wirelessChargingUIAlert.updateForgetReminderUI(signalVal);
        }
    }

    private void updateFrontChargingUI(Integer signalVal) {
        if (signalVal == CarConnect.CWC_workingSts.CWC_ON) {
            binding.tvWxcdDesc.setText(getString(R.string.str_wxcd_desc));
        }else if (signalVal == CarConnect.CWC_workingSts.CWC_OFF) {
            binding.tvWxcdDesc.setText(getString(R.string.str_wxcd_close));
        }
        // 更新充电状态
        updatePhoneChargingUI(viewModel.phoneChargingLiveData.getValue());
        if (wirelessChargingUIAlert != null) {
            wirelessChargingUIAlert.updateFrontChargingUI(signalVal);
        }
    }

    // 打开自定义提示窗口
    @SuppressLint("RtlHardcoded")
    private void openTipsDialog(String title, String content, int width, int height, int gravity) {
        // 如果对话框已存在且正在显示，则不创建新的
        if (detailUIAlert != null && detailUIAlert.isShowing()) {
            return;
        }

        if (detailUIAlert == null) {
            detailUIAlert = new DetailsUIAlert.Builder(mContext);
        }
        detailUIAlert.create(title, content, width, height, gravity).show();
        detailUIAlert.setPadding(120);
        detailUIAlert.setScrollable(true);
        detailUIAlert.setTextSize(com.bitech.base.R.dimen.font_24px);
    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: ");
        // 埋点上报数据
        DataPoint dataPoint = new DataPoint();
        dataPoint.setId(CommonConst.DataPoint.id);
        dataPoint.setEventId(CommonConst.EventId.Connect_Set);
        dataPoint.setTimestamp(System.currentTimeMillis());
        dataPoint.setSupplierCode(CommonConst.DataPoint.supplierCode);
        dataPoint.setPlatformCode(CommonConst.DataPoint.platformCode);
        dataPoint.setNodeType(LogDataUtil.NODE_TYPE_ADD_OPERATION);
        // 上报数据驾驶页面
        ArrayList<Content> list = getData();
        if (!list.isEmpty()) {
            dataPoint.setContent(list);
        }

        LiveEventBus
                .get(DataPointReportLifeCycle.KEY_DATA_POINT)
                .post(dataPoint);
    }

    //region 埋点数据上报
    private ArrayList<Content> getData() {
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        //蓝牙开关状态
        dataList.add(reportData(CommonConst.CodeId.ZB141701, CommonConst.Att.BTSw, String.valueOf(btViewModel.getBluetoothStatus())));
        //蓝牙可见性
        dataList.add(reportData(CommonConst.CodeId.ZB141701, CommonConst.Att.BTViSw, String.valueOf(btViewModel.getBtFoundSwitch())));
        //当前配对设备名称
        dataList.add(reportData(CommonConst.CodeId.ZB141701, CommonConst.Att.PairedDevices, String.valueOf(btViewModel.getPairedBtDeviceName())));
        //配对成功时间
        dataList.add(reportData(CommonConst.CodeId.ZB141701, CommonConst.Att.ConnectTime, btViewModel.getConnectTime()));
        //wifi开关
        dataList.add(reportData(CommonConst.CodeId.ZB141702, CommonConst.Att.WiFiSw, String.valueOf(wifiViewModel.getWifiState())));
        //网络通知
        dataList.add(reportData(CommonConst.CodeId.ZB141702, CommonConst.Att.WiFiPushSw, String.valueOf(wifiViewModel.getNetWorkNotification())));
        //热点开关
        dataList.add(reportData(CommonConst.CodeId.ZB141702, CommonConst.Att.HotspotSw, String.valueOf(wifiViewModel.getHotspotState())));
        //无线充电
        dataList.add(reportData(CommonConst.CodeId.ZB141703, CommonConst.Att.WirelessPowerSw, String.valueOf(viewModel.frontChargingLiveData.getValue())));
        //遗忘提醒
        dataList.add(reportData(CommonConst.CodeId.ZB141703, CommonConst.Att.ForgetSw, String.valueOf(viewModel.forgetReminderLiveData.getValue())));
        return dataList;
    }

    private Content reportData(String attributeId, String locationId, String attributeValue) {
        Content content = new Content();
        content.setAttributeId(attributeId);
        content.setLocationId(locationId);
        content.setAttributeValue(attributeValue);
        return content;
    }

    @Override
    public void onStop() {
        hideWifiDialog();
        super.onStop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        unInitReceiver();
        // 释放 Presenter 的引用，若有相应的释放方法
        presenter = null;
        isActive = false;
        if (binding != null) {
            binding = null;
        }
    }
}
