package com.bitech.vehiclesettings.view.system;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSLanguageBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSTimeZoneBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.Locale;

public class TimeZoneSettingUIAlert extends BaseDialog {
    private static final String TAG = TimeZoneSettingUIAlert.class.getSimpleName();


    public TimeZoneSettingUIAlert(@NonNull Context context) {
        super(context);
    }

    public TimeZoneSettingUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected TimeZoneSettingUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }


    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private TimeZoneSettingUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSTimeZoneBinding binding;
        private Locale[] languages = Locale.getAvailableLocales();
        Configuration configuration;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private TimeZoneSettingUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
            configuration = context.getResources().getConfiguration();
        }

        public TimeZoneSettingUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public TimeZoneSettingUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new TimeZoneSettingUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSTimeZoneBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            init();
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);
            return dialog;
        }

        public void init() {
            String[] timeZoneItems = new String[]{
                    "Asia/Shanghai",
                    "Asia/Tokyo",
                    "America/New_York",
                    "Europe/London",
                    "Australia/Sydney"
            };

            binding.npLanguage.setMinValue(0);
            binding.npLanguage.setMaxValue(timeZoneItems.length - 1);
            binding.npLanguage.setDisplayedValues(timeZoneItems);
            binding.npLanguage.setWrapSelectorWheel(false);

            // 获取当前时区
            String currentTimeZoneId = java.util.TimeZone.getDefault().getID();

            int currentIndex = 0;
            for (int i = 0; i < timeZoneItems.length; i++) {
                if (timeZoneItems[i].equals(currentTimeZoneId)) {
                    currentIndex = i;
                    break;
                }
            }

            binding.npLanguage.setValue(currentIndex);

            // 确定按钮切换时区
            binding.btnConfirm.setOnClickListener(v -> {
                int position = binding.npLanguage.getValue();
                String targetTimeZoneId = timeZoneItems[position];
                updateTimeZone(targetTimeZoneId);

                dialog.dismiss();
            });

            // 取消
            binding.btnCancel.setOnClickListener(v -> dialog.dismiss());
        }

        private void updateTimeZone(String targetTimeZoneId) {
            // TODO 切换时区
            try {
                AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
                alarmManager.setTimeZone(targetTimeZoneId);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

}
