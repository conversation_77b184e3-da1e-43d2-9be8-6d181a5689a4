package com.bitech.vehiclesettings.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.media.AudioSystem;
import android.media.audiofx.Visualizer;
import android.os.Bundle;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;
import android.widget.Toast;

import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.ColorBean;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.bean.pick.PickFrontBean;
import com.bitech.platformlib.bean.pick.PickRearBean;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.interfaces.light.ILightManagerListener;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.adapter.SuggestColorAdapter;
import com.bitech.vehiclesettings.bean.report.Content;
import com.bitech.vehiclesettings.databinding.FragmentLightInBinding;
import com.bitech.vehiclesettings.fragment.factory.LightInViewModelFactory;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.light.LightInPresenter;
import com.bitech.vehiclesettings.presenter.light.LightInPresenterListener;
import com.bitech.vehiclesettings.presenter.light.LightPresenter;
import com.bitech.vehiclesettings.presenter.light.LightPresenterListener;
import com.bitech.vehiclesettings.utils.AppEnum;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.ColorUtils;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GlideBackgroundUtils;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.OnOffEvent;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.light.ColorPickerView;
import com.bitech.vehiclesettings.view.light.OffsetItemDecoration;
import com.bitech.vehiclesettings.viewmodel.LightInViewModel;
import com.blankj.utilcode.util.StringUtils;
import com.bumptech.glide.Glide;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import de.greenrobot.event.EventBus;

/**
 * 灯光
 *
 * <AUTHOR>
 */
public class LightInFragment extends BaseFragment<FragmentLightInBinding> implements View.OnClickListener, View.OnLongClickListener, SafeHandlerCallback {

    private static final String TAG = LightInFragment.class.getSimpleName();
    private ArrayList<Content> dataList;
    // handler消息处理状态标志
    private volatile boolean isActive;
    private LightPresenterListener lightPresenter;
    private LightInPresenterListener lightInPresenter;
    private SafeHandler lightHandler;
    private LightInViewModel viewModel;
    private SuggestColorAdapter colorAdapter;
    private int brightnessAuto = 0;
    private Visualizer visualizer;
    private LightManager manager = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
    // 灯光效果
    private int swLightEffect;
    private int prevLightArea, curLightArea;
    private int offsetLightArea;
    private final int animLightAreaWidth = 254;
    // 颜色定义
    private String curColor = PrefsConst.DefaultValue.CUR_COLOR;
    private int curPositionX;
    private List<ColorBean> colorBeans = new ArrayList<>();
    private LightInBean inBean;
    private List<ColorBean> colorBeanList;
    private boolean mIsChecked;

    public void setIsChecked(boolean isChecked) {
        this.mIsChecked = isChecked;
    }

    @Override
    protected FragmentLightInBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentLightInBinding.inflate(getLayoutInflater());
        return binding;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isActive = true;
        lightPresenter = new LightPresenter(mContext);
        lightInPresenter = new LightInPresenter(mContext);
        lightHandler = new SafeHandler(this);
        inBean = lightInPresenter.getlInBean();
        LightInViewModelFactory factory = new LightInViewModelFactory(inBean);
        viewModel = new ViewModelProvider(this, factory).get(LightInViewModel.class);
        EventBus.getDefault().register(this);
        regLightListen();
    }

    @Override
    protected void initView() {
        initData();
        initDeviceSignal();
        initObserver();
        colorBeanList = lightInPresenter.getColors();
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false);
        binding.rvRecommendColor.setLayoutManager(layoutManager);
        colorAdapter = new SuggestColorAdapter(mContext, colorBeanList);
        binding.rvRecommendColor.setAdapter(colorAdapter);
        binding.rvRecommendColor.addItemDecoration(new OffsetItemDecoration(0, 8));
        SuggestColorAdapter.setOnItemClickListener(position -> {
            if (swLightEffect == 2) {
                EToast.showToast(mContext, getString(R.string.str_light_color_disable_tip), Toast.LENGTH_SHORT, false);
                return;
            }
            if (swLightEffect == 3) {
                EToast.showToast(mContext, getString(R.string.str_light_color_disable_tip), Toast.LENGTH_SHORT, false);
                return;
            }
            if (mIsChecked) {
                return;
            }
            if (position != viewModel.getColorPos().getValue()) {
                viewModel.setColorPos(position);
            }
            ColorBean colorBean = colorBeanList.get(position);
            // 区分多色和单色
            if (StringUtils.isEmpty(colorBean.colorFrontHex)) {
                lightInPresenter.selectSingleColor(position, viewModel.getColorBrightsPos().getValue(), AppEnum.ambientLightColor.getLinByHex(colorBean.colorHex), inBean.getBrightPos());
                Log.d(TAG, "主题模式 氛围灯颜色定义 position: " + position + ", 单色：" + AppEnum.ambientLightColor.getLinByHex(colorBean.colorHex) + ",亮度:" + inBean.getBrightPos());
            } else {
                lightInPresenter.selectMutiColor(position, viewModel.getColorBrightsPos().getValue(), AppEnum.ambientLightColor.getLinByHex(colorBean.colorFrontHex), AppEnum.ambientLightColor.getLinByHex(colorBean.colorRearHex), inBean.getBrightPos());
                Log.d(TAG, "主题模式 氛围灯颜色定义 position: " + position + ", 多色前排:" + AppEnum.ambientLightColor.getLinByHex(colorBean.colorFrontHex) + ",多色后排：" + AppEnum.ambientLightColor.getLinByHex(colorBean.colorRearHex) + ",亮度:" + inBean.getBrightPos());
            }

        });
        offsetLightArea = curLightArea * (animLightAreaWidth);
        binding.viewLightArea.setTranslationX(offsetLightArea);
        if (curLightArea == 0) {
            lightPresenter.setTextView(false, binding.tvLightAreaFont, binding.tvLightAreaRear);
        } else if (curLightArea == 1) {
            lightPresenter.setTextView(false, binding.tvLightAreaRear, binding.tvLightAreaFont);
        }
        prevLightArea = curLightArea;

        curPositionX = lightInPresenter.getPickColorPosX();
        fillCustomColorAll(inBean.getLightSel());
        Log.d(TAG, "initView curPositionX: " + curPositionX);
        // 前排
        Glide.with(this).load(R.mipmap.ic_light_car_1).into(binding.ivLightCar1);
        Glide.with(this).load(R.mipmap.ic_light_car_2).into(binding.ivLightCar2);
        Glide.with(this).load(R.mipmap.ic_light_car_3).into(binding.ivLightCar3);
        Glide.with(this).load(R.mipmap.ic_light_car_4).into(binding.ivLightCar4);
        // 后排
        Glide.with(this).load(R.mipmap.iv_light_car_rear1).into(binding.ivLightCarRear1);
        Glide.with(this).load(R.mipmap.iv_light_car_rear2).into(binding.ivLightCarRear2);
        Glide.with(this).load(R.mipmap.iv_light_car_rear3).into(binding.ivLightCarRear3);
        Glide.with(this).load(R.mipmap.iv_light_car_rear4).into(binding.ivLightCarRear4);
        Glide.with(this).load(R.mipmap.iv_light_car_rear5).into(binding.ivLightCarRear5);
        Glide.with(this).load(R.mipmap.iv_light_car_rear6).into(binding.ivLightCarRear6);
        Glide.with(this).load(R.mipmap.iv_light_car_rear7).into(binding.ivLightCarRear7);
        Glide.with(this).load(R.mipmap.iv_light_car_rear8).into(binding.ivLightCarRear8);
        Glide.with(this).load(R.mipmap.iv_light_car_rear9).into(binding.ivLightCarRear9);

        if ((inBean.getThemeMode() == CommonConst.TAB_0) || (inBean.getLightSel() == CommonConst.TAB_0)) {
            binding.ivLightCar1.setVisibility(View.VISIBLE);
            binding.ivLightCar2.setVisibility(View.VISIBLE);
            binding.ivLightCar3.setVisibility(View.VISIBLE);
            binding.ivLightCar4.setVisibility(View.VISIBLE);
            binding.ivLightCarRear1.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear2.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear3.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear4.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear5.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear6.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear7.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear8.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear9.setVisibility(View.INVISIBLE);
        } else {
            binding.ivLightCar1.setVisibility(View.INVISIBLE);
            binding.ivLightCar2.setVisibility(View.INVISIBLE);
            binding.ivLightCar3.setVisibility(View.INVISIBLE);
            binding.ivLightCar4.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear1.setVisibility(View.VISIBLE);
            binding.ivLightCarRear2.setVisibility(View.VISIBLE);
            binding.ivLightCarRear3.setVisibility(View.VISIBLE);
            binding.ivLightCarRear4.setVisibility(View.VISIBLE);
            binding.ivLightCarRear5.setVisibility(View.VISIBLE);
            binding.ivLightCarRear6.setVisibility(View.VISIBLE);
            binding.ivLightCarRear7.setVisibility(View.VISIBLE);
            binding.ivLightCarRear8.setVisibility(View.VISIBLE);
            binding.ivLightCarRear9.setVisibility(View.VISIBLE);
        }
        syncSceneOnOff(inBean.getLightSw());
        lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR);

    }

    public void onEvent(OnOffEvent event) {
        if (event != null) {
            syncSceneOnOff(event.sw);
            Log.d(TAG, "【onEvent】开关: " + event.sw);
        }
    }

    private void syncSceneOnOff(int open) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                OnOff(open);
            });
        }
    }

    /**
     * 数据初始化
     */
    @Override
    protected void initData() {
        binding.spvLightAtmosphere.setItems(R.string.str_light_atmosphere_1, R.string.str_light_atmosphere_2);
        binding.spvLightEffect.setItems(R.string.str_light_effect_1, R.string.str_light_effect_2, R.string.str_light_effect_3, R.string.str_light_effect_4);
        viewModel.setThemeSw(inBean.getThemeMode());
        viewModel.setLightSel(inBean.getLightSel());
        // 自动顶灯
        viewModel.setAutomaticCeiling(inBean.getAutomaticCeiling());
        // 色块选中
        viewModel.setColorPos(inBean.getColorPos());
        lightPresenter.setSwitchView(inBean.getThemeMode(), binding.rvRecommendColor, binding.llLightCustom);
        curLightArea = inBean.getLightSel();
        offsetLightArea = inBean.getLightSel();

        Log.d(TAG, "initData 记录前后排位置值: " + inBean.getLightSel() + ",氛围灯开关:" + inBean.getLightSw());
        // 主题模式
        if (inBean.getThemeMode() == CommonConst.TAB_0) {
            viewModel.setColorBrightsPos(inBean.getBrightPos());
            viewModel.setLightSw(inBean.getLightSw());
        } else {
            if (inBean.getLightSel() == CommonConst.TAB_0) {
                viewModel.setLightSw(inBean.getLightFontSw());
                viewModel.setColorBrightsPos(inBean.getFront().getFrontBrightness());
                curColor = inBean.getPickFront().getPickColor();
            } else {
                viewModel.setLightSw(inBean.getLightRearSw());
                viewModel.setColorBrightsPos(inBean.getRear().getRearBrightness());
                curColor = inBean.getPickRear().getPickColor();
            }
        }
        brightnessAuto = Prefs.get(PrefsConst.L_BRIGHTNESS_AUOT, PrefsConst.DefaultValue.L_BRIGHTNESS_AUOT);
        binding.tvBrightnessAuto.setSelected(brightnessAuto == CommonConst.OPEN);
        //车灯效果
        swLightEffect = inBean.getLampEffect();
        binding.spvLightEffect.setSelectedIndex(swLightEffect, false);
        binding.spvLightAtmosphere.setSelectedIndex(inBean.getThemeMode(), false);

    }

    private void initDeviceSignal() {
        lightHandler.sendMessage(MessageConst.LIGHT_IN_INIT_DATA);
    }

    private void fillCustomColorAll(int frontSel) {
        colorBeans.clear();
        for (int i = 0; i < 6; i++) {
            // 正常加载之前缓存的颜色值
            fillCustomColor(frontSel, i);
        }
    }

    private void fillCustomColor(int frontSel, int pos) {
        ColorBean colorBean = lightInPresenter.getPickColor(frontSel, pos);
        colorBeans.add(colorBean);
        if (colorBean != null && !StringUtils.isEmpty(colorBean.colorHex)) {
            String vColor = colorBean.colorHex;
            int posx = colorBean.xPosition;

            if (pos == CommonConst.TAB_0) {
                binding.tvCustomColor0.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                binding.tvCustomColor0.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(vColor)));
                if (posx == curPositionX) {
                    updateColorView(binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask4, binding.viewMask5);
                }

            }
            if (pos == CommonConst.TAB_1) {
                binding.tvCustomColor1.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                binding.tvCustomColor1.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(vColor)));
                if (posx == curPositionX) {
                    updateColorView(binding.viewMask1, binding.viewMask0, binding.viewMask2, binding.viewMask3, binding.viewMask4, binding.viewMask5);
                }
            }
            if (pos == CommonConst.TAB_2) {
                binding.tvCustomColor2.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                binding.tvCustomColor2.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(vColor)));
                if (posx == curPositionX) {
                    updateColorView(binding.viewMask2, binding.viewMask0, binding.viewMask1, binding.viewMask3, binding.viewMask4, binding.viewMask5);
                }
            }
            if (pos == CommonConst.TAB_3) {
                binding.tvCustomColor3.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                binding.tvCustomColor3.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(vColor)));
                if (posx == curPositionX) {
                    updateColorView(binding.viewMask3, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask4, binding.viewMask5);
                }
            }
            if (pos == CommonConst.TAB_4) {
                binding.tvCustomColor4.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                binding.tvCustomColor4.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(vColor)));
                if (posx == curPositionX) {
                    updateColorView(binding.viewMask4, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask5);
                }
            }
            if (pos == CommonConst.TAB_5) {
                binding.tvCustomColor5.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                binding.tvCustomColor5.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(vColor)));
                if (posx == curPositionX) {
                    updateColorView(binding.viewMask5, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask4);
                }
            }
        } else {
            if (pos == CommonConst.TAB_0) {
                binding.tvCustomColor0.setBackground(mContext.getDrawable(R.drawable.add_bg));
                binding.tvCustomColor0.setBackgroundTintList(null);
            }
            if (pos == CommonConst.TAB_1) {
                binding.tvCustomColor1.setBackground(mContext.getDrawable(R.drawable.add_bg));
                binding.tvCustomColor1.setBackgroundTintList(null);
            }
            if (pos == CommonConst.TAB_2) {
                binding.tvCustomColor2.setBackground(mContext.getDrawable(R.drawable.add_bg));
                binding.tvCustomColor2.setBackgroundTintList(null);
            }
            if (pos == CommonConst.TAB_3) {
                binding.tvCustomColor3.setBackground(mContext.getDrawable(R.drawable.add_bg));
                binding.tvCustomColor3.setBackgroundTintList(null);
            }
            if (pos == CommonConst.TAB_4) {
                binding.tvCustomColor4.setBackground(mContext.getDrawable(R.drawable.add_bg));
                binding.tvCustomColor4.setBackgroundTintList(null);
            }
            if (pos == CommonConst.TAB_5) {
                binding.tvCustomColor5.setBackground(mContext.getDrawable(R.drawable.add_bg));
                binding.tvCustomColor5.setBackgroundTintList(null);
            }
        }

    }

    public void setSeekBarStyle(Context context, SeekBar seekBar) {
        if (seekBar.getProgress() == seekBar.getMax()) {
            seekBar.setProgressDrawable(context.getDrawable(R.drawable.progress_blue_white_max));
        } else {
            seekBar.setProgressDrawable(context.getDrawable(R.drawable.progress_blue_white));
        }
    }

    @Override
    protected void setListener() {
        BindingUtil.bindClicks(this, binding.tvLightSw);
        BindingUtil.bindClicks(this, binding.tvLightAreaFont, binding.tvLightAreaRear);
        // 自动
        BindingUtil.bindClicks(this, binding.tvBrightnessAuto, binding.tvLightAutoTop);
        BindingUtil.bindClicks(this, binding.tvCustomColor0, binding.tvCustomColor1, binding.tvCustomColor2, binding.tvCustomColor3, binding.tvCustomColor4, binding.tvCustomColor5);
        BindingUtil.bindClicks(this, binding.tvLightSync);
        BindingUtil.bindOnLongClicks(this, binding.tvCustomColor0, binding.tvCustomColor1, binding.tvCustomColor2, binding.tvCustomColor3, binding.tvCustomColor4, binding.tvCustomColor5);
        binding.sbLightBrightnessAdjust.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean pressed) {
                setSeekBarStyle(mContext, seekBar);
                // 亮度调节
                if (pressed) {
                    // 检查功能是否可用
                    if (checkCurpageOpen()) {
                        if (viewModel.getColorBrightsPos().getValue() != null && (swLightEffect != 0)) {
                            binding.sbLightBrightnessAdjust.setProgress(viewModel.getColorBrightsPos().getValue());
                        }
                        if (swLightEffect == 1) {
                            EToast.showToast(mContext, getString(R.string.str_light_bri_disable_tip), Toast.LENGTH_SHORT, false);
                            return;
                        }
                        if (swLightEffect == 2) {
                            EToast.showToast(mContext, getString(R.string.str_light_bri_disable_tip), Toast.LENGTH_SHORT, false);
                            return;
                        }
                        if (swLightEffect == 3) {
                            EToast.showToast(mContext, getString(R.string.str_light_bri_disable_tip), Toast.LENGTH_SHORT, false);
                            return;
                        }
                    }

                    viewModel.setAutoBright(CommonConst.CLOSE);
                    viewModel.setColorBrightsPos(progress);
                    if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                        viewModel.setLightSync(CommonConst.CLOSE);
                    }
                    lightInPresenter.brightAdj(progress, pressed);

                }

                float bri = progress / 100.0f;
                if (inBean.getThemeMode() == CommonConst.TAB_0) {
                    ColorBean colorBean = colorBeanList.get(inBean.getColorPos());
                    if (colorBean != null && (!StringUtils.isEmpty(colorBean.colorHex))) {
                        binding.ivLightCar1.setColorFilter(ColorUtils.adjustBrightness(ColorUtils.hexColorToInt(colorBean.colorHex), bri), PorterDuff.Mode.SRC_ATOP);
                        binding.ivLightCar2.setColorFilter(ColorUtils.adjustBrightness(ColorUtils.hexColorToInt(colorBean.colorHex), bri), PorterDuff.Mode.SRC_ATOP);
                        binding.ivLightCar3.setColorFilter(ColorUtils.adjustBrightness(ColorUtils.hexColorToInt(colorBean.colorHex), bri), PorterDuff.Mode.SRC_ATOP);
                        binding.ivLightCar4.setColorFilter(ColorUtils.adjustBrightness(ColorUtils.hexColorToInt(colorBean.colorHex), bri), PorterDuff.Mode.SRC_ATOP);
                    }

                } else {
                    if (!StringUtils.isEmpty(curColor)) {
                        lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), bri, inBean.getLightSel()));
                    }
                }
                Log.d(TAG, "【亮度】事件 onProgressChanged: 当前亮度:" + progress + ",press:" + pressed);

            }


            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                //seekBar.setThumb(ContextCompat.getDrawable(mContext, R.mipmap.ic_sound_slider_s));
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                //seekBar.setThumb(ContextCompat.getDrawable(mContext, R.mipmap.ic_sound_slider));
            }
        });
        // 绑定车灯设置
        binding.spvLightEffect.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index, String text) {

            }

            @Override
            public void onItemClicked(int index, String text) {
                swLightEffect = index;
                lightEffectChg(swLightEffect);
                lightInPresenter.setLightEffect(index);
            }
        });

        // 主题切换  主题自定义
        binding.spvLightAtmosphere.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index, String text) {

            }

            @Override
            public void onItemClicked(int index, String text) {
                viewModel.setThemeSw(index);
                int open = 0;
                if (index == CommonConst.POS_0) {
                    viewModel.setLightSw(inBean.getLightSw());
                    open = inBean.getBrightPos();
                    viewModel.setColorBrightsPos(open);
                    viewModel.setAutoBright(inBean.getBrightnessAuto());
                    chgMainFrontRearPic(CommonConst.POS_0);
                    Log.d(TAG, "[切换主题]:主题 亮度:" + inBean.getBrightPos() + ",自动亮度状态:" + inBean.getBrightnessAuto() + ",开关：" + inBean.getLightSw());
                } else {

                    if (viewModel.getLightSel().getValue() != null && (viewModel.getLightSel().getValue() == 0)) {
                        open = inBean.getLightFontSw();
                        viewModel.setLightSw(open);
                        viewModel.setColorBrightsPos(inBean.getFront().getFrontBrightness());
                        viewModel.setAutoBright(inBean.getFront().getFrontBrightnessAuto());
                        Log.d(TAG, "[切换主题]:自定义前排 亮度:" + inBean.getFront().getFrontBrightness() + ",自动亮度状态:" + inBean.getFront().getFrontBrightnessAuto() + ",开关：" + inBean.getLightFontSw());
                        chgMainFrontRearPic(CommonConst.POS_0);
                    }
                    if (viewModel.getLightSel().getValue() != null && (viewModel.getLightSel().getValue() == 1)) {
                        open = inBean.getLightRearSw();
                        viewModel.setLightSw(open);
                        viewModel.setColorBrightsPos(inBean.getRear().getRearBrightness());
                        viewModel.setAutoBright(inBean.getRear().getRearBrightnessAuto());
                        Log.d(TAG, "[切换主题]:自定义后排 亮度:" + inBean.getRear().getRearBrightness() + ",自动亮度状态:" + inBean.getRear().getRearBrightnessAuto() + ",开关：" + inBean.getLightRearSw());
                        chgMainFrontRearPic(CommonConst.POS_1);
                    }
                }
                lightInPresenter.chgThemeMode(index);
            }
        });


        binding.lightColorPicker.setColorChangedListener(new ColorPickerView.onColorChangedListener() {
            @Override
            public void colorChanged(int red, int green, int blue, int positionX) {
                curColor = String.format("#%02X%02X%02X", red, green, blue);
                //判断是否选中
                checkColorPick(positionX);
            }

            @Override
            public void stopColorChanged(int red, int green, int blue, int positionX) {
                curColor = String.format("#%02X%02X%02X", red, green, blue);
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                }
                Prefs.put(PrefsConst.SELECT_CUR_COLOR, curColor);
                float vBri = viewModel.getColorBrightsPos().getValue() / 100.0f;
                curPositionX = positionX;
                checkColorPick(positionX);
                lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), vBri, inBean.getLightSel()));
                //自定义颜色选择
                if (inBean != null) {
                    lightInPresenter.pickColor(red, green, blue, positionX);
                }
            }

            @Override
            public boolean stopPick() {
                if (swLightEffect == 2) {
                    EToast.showToast(mContext, getString(R.string.str_light_color_disable_tip), Toast.LENGTH_SHORT, false);
                    return true;
                }
                if (swLightEffect == 3) {
                    EToast.showToast(mContext, getString(R.string.str_light_color_disable_tip), Toast.LENGTH_SHORT, false);
                    return true;
                }
                return false;
            }
        });

        if (inBean.getThemeMode() == 0) {
            openOrCloseLight(inBean.getLightSw() == CommonConst.OPEN);
        }
    }

    private void checkColorPick(int positionX) {
        boolean checked = true;
        boolean vFlag = false;
        if (inBean.getLightSel() == 0) {
            PickFrontBean frontBean = inBean.getPickFront();
            vFlag = updateColorView(binding.viewMask0, (frontBean.getColor0().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            vFlag = updateColorView(binding.viewMask1, (frontBean.getColor1().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            vFlag = updateColorView(binding.viewMask2, (frontBean.getColor2().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            vFlag = updateColorView(binding.viewMask3, (frontBean.getColor3().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            vFlag = updateColorView(binding.viewMask4, (frontBean.getColor4().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            updateColorView(binding.viewMask5, (frontBean.getColor5().xPosition == positionX) && checked);
        } else {
            PickRearBean rearBean = inBean.getPickRear();
            vFlag = updateColorView(binding.viewMask0, (rearBean.getColor0().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            vFlag = updateColorView(binding.viewMask1, (rearBean.getColor1().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            vFlag = updateColorView(binding.viewMask2, (rearBean.getColor2().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            vFlag = updateColorView(binding.viewMask3, (rearBean.getColor3().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            vFlag = updateColorView(binding.viewMask4, (rearBean.getColor4().xPosition == positionX) && checked);
            if (checked && vFlag) checked = false;
            updateColorView(binding.viewMask5, (rearBean.getColor5().xPosition == positionX) && checked);
        }
    }

    @Override
    protected void initObserve() {

    }

    /**
     * @param view The view that was clicked.
     */
    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_light_sync:
                // 同步
                int sel = viewModel.getLightSel().getValue();
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                } else {
                    if (sel == 0) {
                        lightInPresenter.syncAtmosphere(CommonConst.LIGHT_SW_FRONT, CommonConst.OPEN);

                    } else {
                        lightInPresenter.syncAtmosphere(CommonConst.LIGHT_SW_REAR, CommonConst.OPEN);

                    }
                    if (!StringUtils.isEmpty(curColor)) {
                        setAColorFilter(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, CommonConst.TAB_0);
                        setAColorFilter(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, CommonConst.TAB_1);
                    }
                    viewModel.setLightSync(CommonConst.OPEN);
                }

                break;
            case R.id.tv_light_auto_top:
                if (viewModel.getAutomaticCeiling().getValue() == CommonConst.POS_1) {
                    viewModel.setAutomaticCeiling(CommonConst.CLOSE);
                    lightInPresenter.setAutomaticCeiling(CarLight.DoorControlSW.OFF);
                } else {
                    viewModel.setAutomaticCeiling(CommonConst.OPEN);
                    lightInPresenter.setAutomaticCeiling(CarLight.DoorControlSW.ON);
                }
                //
                lightHandler.sendMessageDelayed(MessageConst.LIGHT_AUTOMATIC_CEILING);
                //binding.lightColorPicker.setImgPickerPosition(curPositionX);
                break;
            case R.id.tv_light_sw:
                // 氛围灯开关
                int open;
                if (viewModel.getLightSw().getValue() == 1) {
                    open = CommonConst.CLOSE;
                } else {
                    open = CommonConst.OPEN;
                }
                int pos = OnOff(open);
                lightInPresenter.setAmbLightSw(open, pos);
                break;
            case R.id.tv_brightness_auto:
                // 自动亮度
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                }
                int bStatus = lightInPresenter.getAutoLamp();
                Log.d(TAG, "onClick getAutoLamp: " + bStatus);
                if (bStatus == CarLight.SAutoLamp.ACTIVATE) {
                    // 明亮
                    viewModel.setColorBrightsPos(CarLight.BrightAuto.HIGHT);
                } else if (bStatus == CarLight.SAutoLamp.DEAC_TIVATE) {
                    // 暗
                    viewModel.setColorBrightsPos(CarLight.BrightAuto.LOW);
                }

                if (viewModel.getAutoBright().getValue() == 1) {
                    viewModel.setAutoBright(0);
                    lightInPresenter.setBrightnessAuto(0);
                } else {
                    viewModel.setAutoBright(1);
                    lightInPresenter.setBrightnessAuto(1);
                }

                break;
            case R.id.tv_custom_color_0:
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                }
                if (!colorBeans.get(0).hasColor) {
                    colorBeans.get(0).hasColor = true;
                    colorBeans.get(0).xPosition = curPositionX;
                    colorBeans.get(0).colorHex = curColor;
                    if (binding.tvCustomColor0 != null) {
                        binding.tvCustomColor0.setBackground(getActivity().getDrawable(R.drawable.custom_button_s));
                        binding.tvCustomColor0.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                    }

                } else {
                    if (!StringUtils.isEmpty(colorBeans.get(0).colorHex)) {
                        curColor = colorBeans.get(0).colorHex;
                        curPositionX = colorBeans.get(0).xPosition;
                        binding.lightColorPicker.setImgPickerPosition(colorBeans.get(0).xPosition);
                    }
                }
                updateColorView(binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask4, binding.viewMask5);
                if (!StringUtils.isEmpty(curColor)) {
                    lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, inBean.getLightSel()));
                }
                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(0, colorBeans.get(0));
                Log.d(TAG, "自定义颜色1: " + curColor + ",curPositionX" + curPositionX);
                break;
            case R.id.tv_custom_color_1:
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                }
                if (!colorBeans.get(1).hasColor) {
                    colorBeans.get(1).hasColor = true;
                    colorBeans.get(1).xPosition = curPositionX;
                    colorBeans.get(1).colorHex = curColor;
                    if (binding.tvCustomColor0 != null) {
                        binding.tvCustomColor1.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                        if (!StringUtils.isEmpty(curColor)) {
                            binding.tvCustomColor1.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                        }
                    }
                } else {
                    curColor = colorBeans.get(1).colorHex;
                    curPositionX = colorBeans.get(1).xPosition;
                    binding.lightColorPicker.setImgPickerPosition(colorBeans.get(1).xPosition);
                }
                updateColorView(binding.viewMask1, binding.viewMask0, binding.viewMask2, binding.viewMask3, binding.viewMask4, binding.viewMask5);
                if (!StringUtils.isEmpty(curColor)) {
                    lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, inBean.getLightSel()));
                }
                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(1, colorBeans.get(1));
                Log.d(TAG, "自定义颜色2: " + curColor + ",curPositionX" + curPositionX);
                break;
            case R.id.tv_custom_color_2:
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                }
                if (!colorBeans.get(2).hasColor) {
                    colorBeans.get(2).hasColor = true;
                    colorBeans.get(2).xPosition = curPositionX;
                    colorBeans.get(2).colorHex = curColor;
                    if (binding.tvCustomColor0 != null) {
                        binding.tvCustomColor2.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                        if (!StringUtils.isEmpty(curColor)) {
                            binding.tvCustomColor2.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                        }
                    }
                } else {
                    curColor = colorBeans.get(2).colorHex;
                    curPositionX = colorBeans.get(2).xPosition;
                    binding.lightColorPicker.setImgPickerPosition(colorBeans.get(2).xPosition);
                }
                updateColorView(binding.viewMask2, binding.viewMask0, binding.viewMask1, binding.viewMask3, binding.viewMask4, binding.viewMask5);
                if (!StringUtils.isEmpty(curColor)) {
                    lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, inBean.getLightSel()));
                }
                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(2, colorBeans.get(2));
                Log.d(TAG, "自定义颜色3: " + curColor + ",curPositionX" + curPositionX);
                break;
            case R.id.tv_custom_color_3:
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                }
                if (!colorBeans.get(3).hasColor) {
                    colorBeans.get(3).hasColor = true;
                    colorBeans.get(3).xPosition = curPositionX;
                    colorBeans.get(3).colorHex = curColor;
                    if (binding.tvCustomColor0 != null) {
                        binding.tvCustomColor3.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                        if (!StringUtils.isEmpty(curColor)) {
                            binding.tvCustomColor3.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                        }
                    }
                } else {
                    curColor = colorBeans.get(3).colorHex;
                    curPositionX = colorBeans.get(3).xPosition;
                    binding.lightColorPicker.setImgPickerPosition(colorBeans.get(3).xPosition);
                }
                updateColorView(binding.viewMask3, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask4, binding.viewMask5);
                if (!StringUtils.isEmpty(curColor)) {
                    lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, inBean.getLightSel()));
                }
                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(3, colorBeans.get(3));
                Log.d(TAG, "自定义颜色4: " + curColor + ",curPositionX" + curPositionX);
                break;
            case R.id.tv_custom_color_4:
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                }
                if (!colorBeans.get(4).hasColor) {
                    colorBeans.get(4).hasColor = true;
                    colorBeans.get(4).xPosition = curPositionX;
                    colorBeans.get(4).colorHex = curColor;
                    if (binding.tvCustomColor0 != null) {
                        binding.tvCustomColor4.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                        if (!StringUtils.isEmpty(curColor)) {
                            binding.tvCustomColor4.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                        }
                    }
                } else {
                    curColor = colorBeans.get(4).colorHex;
                    curPositionX = colorBeans.get(4).xPosition;
                    binding.lightColorPicker.setImgPickerPosition(colorBeans.get(4).xPosition);
                }
                updateColorView(binding.viewMask4, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask5);
                if (!StringUtils.isEmpty(curColor)) {
                    lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, inBean.getLightSel()));
                }
                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(4, colorBeans.get(4));
                Log.d(TAG, "自定义颜色5: " + curColor + ",curPositionX" + curPositionX);
                break;
            case R.id.tv_custom_color_5:
                if (viewModel.getLightSync().getValue() == CommonConst.OPEN) {
                    viewModel.setLightSync(CommonConst.CLOSE);
                }
                if (!colorBeans.get(5).hasColor) {
                    colorBeans.get(5).hasColor = true;
                    colorBeans.get(5).xPosition = curPositionX;
                    colorBeans.get(5).colorHex = curColor;
                    if (binding.tvCustomColor0 != null) {
                        binding.tvCustomColor5.setBackground(mContext.getDrawable(R.drawable.custom_button_s));
                        if (!StringUtils.isEmpty(curColor)) {
                            binding.tvCustomColor5.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                        }
                    }
                } else {
                    curColor = colorBeans.get(5).colorHex;
                    curPositionX = colorBeans.get(5).xPosition;
                    binding.lightColorPicker.setImgPickerPosition(colorBeans.get(5).xPosition);
                }
                updateColorView(binding.viewMask5, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask4);
                if (!StringUtils.isEmpty(curColor)) {
                    lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, inBean.getLightSel()));
                }
                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(5, colorBeans.get(5));
                Log.d(TAG, "自定义颜色6: " + curColor + ",curPositionX" + curPositionX);
                break;
            case R.id.tv_light_area_font:
                // 前排事件
                curLightArea = 0;
                selLightAreatranslate();
                if (curLightArea == CommonConst.TAB_0) {
                    lightPresenter.setTextView(false, binding.tvLightAreaFont, binding.tvLightAreaRear);
                } else if (curLightArea == CommonConst.TAB_1) {
                    lightPresenter.setTextView(false, binding.tvLightAreaRear, binding.tvLightAreaFont);
                }
                viewModel.setLightSel(CommonConst.TAB_0);
                viewModel.setLightSw(viewModel.getLightFront().getValue());
                viewModel.setColorBrightsPos(inBean.getFront().getFrontBrightness());
                viewModel.setAutoBright(inBean.getFront().getFrontBrightnessAuto());
                curColor = inBean.getPickFront().getPickColor();
                curPositionX = inBean.getPickFront().getPositionX();
                binding.lightColorPicker.setImgPickerPosition(curPositionX);
                fillCustomColorAll(CommonConst.TAB_0);
                lightInPresenter.setLightFrontOrRear(CommonConst.TAB_0);
                if (!StringUtils.isEmpty(curColor)) {
                    lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, CommonConst.TAB_0));
                }

                chgMainFrontRearPic(CommonConst.TAB_0);
                Log.d(TAG, "【亮度】切换前排: " + curLightArea + ",亮度：" + inBean.getFront().getFrontBrightness() + ",自动亮度状态" + inBean.getFront().getFrontBrightnessAuto());
                break;
            case R.id.tv_light_area_rear:
                // 后排事件
                curLightArea = 1;
                selLightAreatranslate();
                if (curLightArea == CommonConst.TAB_0) {
                    lightPresenter.setTextView(false, binding.tvLightAreaFont, binding.tvLightAreaRear);
                } else if (curLightArea == CommonConst.TAB_1) {
                    lightPresenter.setTextView(false, binding.tvLightAreaRear, binding.tvLightAreaFont);
                }
                viewModel.setLightSel(CommonConst.TAB_1);
                viewModel.setLightSw(viewModel.getLightRear().getValue());
                viewModel.setColorBrightsPos(inBean.getRear().getRearBrightness());
                viewModel.setAutoBright(inBean.getRear().getRearBrightnessAuto());
                curColor = inBean.getPickRear().getPickColor();
                curPositionX = inBean.getPickRear().getPositionX();
                binding.lightColorPicker.setImgPickerPosition(curPositionX);
                fillCustomColorAll(CommonConst.TAB_1);
                lightInPresenter.setLightFrontOrRear(CommonConst.TAB_1);
                if (!StringUtils.isEmpty(curColor)) {
                    lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, CommonConst.TAB_1));
                }
                chgMainFrontRearPic(CommonConst.TAB_1);
                Log.d(TAG, "【亮度】切换后排: " + curLightArea + ",亮度：" + inBean.getRear().getRearBrightness() + ",自动亮度状态" + inBean.getRear().getRearBrightnessAuto());
                break;
            default:
                break;
        }
    }

    private void chgMainFrontRearPic(int selFront) {

        MainActivity activity = (MainActivity) getActivity();
        if (activity != null) {
            if (activity.getBinding().tvLight.isSelected()) {
                if (selFront == CommonConst.TAB_0) {
                    activity.getBinding().ivModel.setVisibility(View.VISIBLE);
                    GlideBackgroundUtils.setBackgroundSafely(Glide.with(this), activity.getBinding().ivModel, R.mipmap.ic_light_lighting_inside);
                    binding.ivLightCar1.setVisibility(View.VISIBLE);
                    binding.ivLightCar2.setVisibility(View.VISIBLE);
                    binding.ivLightCar3.setVisibility(View.VISIBLE);
                    binding.ivLightCar4.setVisibility(View.VISIBLE);

                    binding.ivLightCarRear1.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear2.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear3.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear4.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear5.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear6.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear7.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear8.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear9.setVisibility(View.INVISIBLE);


                } else {
                    activity.getBinding().ivModel.setVisibility(View.VISIBLE);
                    GlideBackgroundUtils.setBackgroundSafely(Glide.with(this), activity.getBinding().ivModel, R.mipmap.ic_light_car_back);
                    binding.ivLightCar1.setVisibility(View.INVISIBLE);
                    binding.ivLightCar2.setVisibility(View.INVISIBLE);
                    binding.ivLightCar3.setVisibility(View.INVISIBLE);
                    binding.ivLightCar4.setVisibility(View.INVISIBLE);
                    binding.ivLightCarRear1.setVisibility(View.VISIBLE);
                    binding.ivLightCarRear2.setVisibility(View.VISIBLE);
                    binding.ivLightCarRear3.setVisibility(View.VISIBLE);
                    binding.ivLightCarRear4.setVisibility(View.VISIBLE);
                    binding.ivLightCarRear5.setVisibility(View.VISIBLE);
                    binding.ivLightCarRear6.setVisibility(View.VISIBLE);
                    binding.ivLightCarRear7.setVisibility(View.VISIBLE);
                    binding.ivLightCarRear8.setVisibility(View.VISIBLE);
                    binding.ivLightCarRear9.setVisibility(View.VISIBLE);

                }

            }
        }
    }

    private int OnOff(int open) {
        viewModel.setLightSw(open);
        int effect = swLightEffect;
        int pos = 0;
        openOrCloseLight(open == CommonConst.OPEN);
        if (effect != 0 && (open == CommonConst.OPEN)) {
            // 主题模式开启关闭
            lightEffectChg(effect);
        }
        if (inBean.getThemeMode() == 0) {
            pos = CommonConst.LIGHT_SW_ALL;
        } else {
            int lightSeal = inBean.getLightSel();
            if (lightSeal == 0) {
                pos = CommonConst.LIGHT_SW_FRONT;
            } else {
                pos = CommonConst.LIGHT_SW_REAR;
            }
            viewModel.setLightFront(open);
            viewModel.setLightRear(open);
        }
        return pos;

    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public boolean onLongClick(View view) {
        switch (view.getId()) {
            case R.id.tv_custom_color_0:
                if (colorBeans.get(0).hasColor) {
                    if (!StringUtils.isEmpty(curColor)) {
                        binding.tvCustomColor0.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                    }
                    colorBeans.get(0).colorHex = curColor;
                    colorBeans.get(0).xPosition = curPositionX;
                }
                updateColorView(binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask4, binding.viewMask5);

                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(0, colorBeans.get(0));
                break;
            case R.id.tv_custom_color_1:
                if (colorBeans.get(1).hasColor) {
                    if (!StringUtils.isEmpty(curColor)) {
                        binding.tvCustomColor1.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                    }
                    colorBeans.get(1).colorHex = curColor;
                    colorBeans.get(1).xPosition = curPositionX;
                }
                updateColorView(binding.viewMask1, binding.viewMask0, binding.viewMask2, binding.viewMask3, binding.viewMask4, binding.viewMask5);

                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(1, colorBeans.get(1));
                break;
            case R.id.tv_custom_color_2:
                if (colorBeans.get(2).hasColor) {
                    if (!StringUtils.isEmpty(curColor)) {
                        binding.tvCustomColor2.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                    }
                    colorBeans.get(2).colorHex = curColor;
                    colorBeans.get(2).xPosition = curPositionX;
                }
                updateColorView(binding.viewMask2, binding.viewMask0, binding.viewMask1, binding.viewMask3, binding.viewMask4, binding.viewMask5);

                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(2, colorBeans.get(2));
                break;
            case R.id.tv_custom_color_3:
                if (colorBeans.get(3).hasColor) {
                    if (!StringUtils.isEmpty(curColor)) {
                        binding.tvCustomColor3.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                    }
                    colorBeans.get(3).colorHex = curColor;
                    colorBeans.get(3).xPosition = curPositionX;
                }
                updateColorView(binding.viewMask3, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask4, binding.viewMask5);

                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(3, colorBeans.get(3));
                break;
            case R.id.tv_custom_color_4:
                if (colorBeans.get(4).hasColor) {
                    if (!StringUtils.isEmpty(curColor)) {
                        binding.tvCustomColor4.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                    }
                    colorBeans.get(4).colorHex = curColor;
                    colorBeans.get(4).xPosition = curPositionX;
                }
                updateColorView(binding.viewMask4, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask5);

                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(4, colorBeans.get(4));
                break;
            case R.id.tv_custom_color_5:
                if (colorBeans.get(5).hasColor) {
                    if (!StringUtils.isEmpty(curColor)) {
                        binding.tvCustomColor5.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(curColor)));
                    }
                    colorBeans.get(5).colorHex = curColor;
                    colorBeans.get(5).xPosition = curPositionX;
                }
                updateColorView(binding.viewMask5, binding.viewMask0, binding.viewMask1, binding.viewMask2, binding.viewMask3, binding.viewMask4);

                lightInPresenter.pickColor(curColor, curPositionX);
                lightInPresenter.savePickColor(5, colorBeans.get(5));
                break;
        }
        return false;
    }

    private void selLightAreatranslate() {
        if (prevLightArea == curLightArea) {
            return;
        }
        int toDistance = animLightAreaWidth * curLightArea;
        lightPresenter.beginTranslateAndScale(binding.viewLightArea, offsetLightArea, toDistance, binding.llLightArea.getChildAt(prevLightArea).getWidth(), binding.llLightArea.getChildAt(curLightArea).getWidth());
        offsetLightArea = toDistance;
        prevLightArea = curLightArea;
    }

    private void updateColorView(View... views) {
        for (int i = 0; i < views.length; i++) {
            if (i == 0) {
                views[i].setVisibility(View.VISIBLE);
            } else {
                views[i].setVisibility(View.GONE);
            }
        }
    }

    private boolean updateColorView(View view, boolean checked) {
        if (checked) {
            view.setVisibility(View.VISIBLE);
            return true;
        } else {
            view.setVisibility(View.GONE);
        }
        return false;

    }

    private void audioPlay() {
        visualizer = new Visualizer(AudioSystem.AUDIO_SESSION_ALLOCATE);
        visualizer.setEnabled(false);
        visualizer.setCaptureSize(Visualizer.getCaptureSizeRange()[1]);
        visualizer.setDataCaptureListener(new Visualizer.OnDataCaptureListener() {
            @Override
            public void onWaveFormDataCapture(Visualizer visualizer, byte[] bytes, int samplingRate) {
            }

            @Override
            public void onFftDataCapture(Visualizer visualizer, byte[] fft, int samplingRate) {
                int mCount = fft.length / 2;
                float[] model = new float[fft.length / 2 + 1];
                model[0] = (byte) Math.abs(fft[1]);
                int j = 1;

                for (int i = 2; i < mCount * 2; ) {
                    model[j] = (float) Math.hypot(fft[i], fft[i + 1]);
                    i += 2;
                    j++;
                    model[j] = (float) Math.abs(fft[j]);
                }
                //model即为最终用于绘制的数据
                Log.d(TAG, "onFftDataCapture: " + Arrays.toString(model));
            }
        }, Visualizer.getMaxCaptureRate() / 2, false, true);
        visualizer.setEnabled(true);
    }

    /**
     * 打开关闭氛围灯
     *
     * @param openFlag
     */
    private void openOrCloseLight(boolean openFlag) {

        if (openFlag) {
            if (inBean.getThemeMode() == CommonConst.TAB_0 || (inBean.getLightSel() == CommonConst.TAB_0)) {
                binding.ivLightCar1.setVisibility(View.VISIBLE);
                binding.ivLightCar2.setVisibility(View.VISIBLE);
                binding.ivLightCar3.setVisibility(View.VISIBLE);
                binding.ivLightCar4.setVisibility(View.VISIBLE);
            } else {
                binding.ivLightCarRear1.setVisibility(View.VISIBLE);
                binding.ivLightCarRear2.setVisibility(View.VISIBLE);
                binding.ivLightCarRear3.setVisibility(View.VISIBLE);
                binding.ivLightCarRear4.setVisibility(View.VISIBLE);
                binding.ivLightCarRear5.setVisibility(View.VISIBLE);
                binding.ivLightCarRear6.setVisibility(View.VISIBLE);
                binding.ivLightCarRear7.setVisibility(View.VISIBLE);
                binding.ivLightCarRear8.setVisibility(View.VISIBLE);
                binding.ivLightCarRear9.setVisibility(View.VISIBLE);
            }

            GrayEffectUtils.removeGrayEffect(binding.spvLightAtmosphere);
            GrayEffectUtils.removeGrayEffect(binding.rvRecommendColor);
            binding.rvRecommendColor.setLayoutFrozen(false); // 解冻布局
            binding.rvRecommendColor.setNestedScrollingEnabled(true); // 启用嵌套滚动
            binding.rvRecommendColor.setClickable(true);  // 恢复点击事件
            binding.rvRecommendColor.setLongClickable(true);  // 恢复长按事件
            GrayEffectUtils.removeGrayEffect(binding.flLightCustom);
            GrayEffectUtils.removeGrayEffect(binding.tvLightSync);
            GrayEffectUtils.removeGrayEffect(binding.lightColorPicker);
            GrayEffectUtils.removeGrayEffect(binding.llCustomColor);
            GrayEffectUtils.removeGrayEffect(binding.sbLightBrightnessAdjust);
            GrayEffectUtils.removeGrayEffect(binding.tvBrightnessAuto);
            GrayEffectUtils.removeGrayEffect(binding.spvLightEffect);
        } else {
            GrayEffectUtils.applyGrayEffect(binding.spvLightAtmosphere);
            GrayEffectUtils.applyGrayEffect(binding.rvRecommendColor, false);
            binding.rvRecommendColor.setLayoutFrozen(true); // 冻结布局
            binding.rvRecommendColor.setNestedScrollingEnabled(false); // 禁用嵌套滚动
//            binding.rvRecommendColor.setClickable(false);  // 禁用点击事件
//            binding.rvRecommendColor.setLongClickable(false);  // 禁用长按事件
            GrayEffectUtils.applyGrayEffect(binding.flLightCustom);
            GrayEffectUtils.applyGrayEffect(binding.tvLightSync);
            GrayEffectUtils.applyGrayEffect(binding.lightColorPicker);
            GrayEffectUtils.applyGrayEffect(binding.llCustomColor);
            GrayEffectUtils.applyGrayEffect(binding.sbLightBrightnessAdjust);
            GrayEffectUtils.applyGrayEffect(binding.tvBrightnessAuto);
            GrayEffectUtils.applyGrayEffect(binding.spvLightEffect);
            binding.ivLightCar1.setVisibility(View.INVISIBLE);
            binding.ivLightCar2.setVisibility(View.INVISIBLE);
            binding.ivLightCar3.setVisibility(View.INVISIBLE);
            binding.ivLightCar4.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear1.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear2.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear3.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear4.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear5.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear6.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear7.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear8.setVisibility(View.INVISIBLE);
            binding.ivLightCarRear9.setVisibility(View.INVISIBLE);
        }


    }


    /**
     * 车灯效果切换操作
     *
     * @param sel
     */
    private void lightEffectChg(int sel) {
        if (sel == 0) {
            // 静态
            binding.rvRecommendColor.setLayoutFrozen(false);
            binding.rvRecommendColor.setNestedScrollingEnabled(true);
            binding.rvRecommendColor.setClickable(true);
            binding.rvRecommendColor.setLongClickable(true);
            GrayEffectUtils.removeGrayEffect(binding.rvRecommendColor);

            GrayEffectUtils.removeGrayEffect(binding.flLightCustom);
            GrayEffectUtils.removeGrayEffect(binding.tvLightSync);
            GrayEffectUtils.removeGrayEffect(binding.lightColorPicker);
            GrayEffectUtils.removeGrayEffect(binding.llCustomColor);
            GrayEffectUtils.removeGrayEffect(binding.sbLightBrightnessAdjust);
            GrayEffectUtils.removeGrayEffect(binding.tvBrightnessAuto);

        } else if (sel == 1) {
            // 呼吸
            binding.rvRecommendColor.setLayoutFrozen(false);
            binding.rvRecommendColor.setNestedScrollingEnabled(true);
            binding.rvRecommendColor.setClickable(true);
            binding.rvRecommendColor.setLongClickable(true);
            GrayEffectUtils.removeGrayEffect(binding.rvRecommendColor);

            GrayEffectUtils.removeGrayEffect(binding.tvLightSync);
            GrayEffectUtils.removeGrayEffect(binding.lightColorPicker);
            GrayEffectUtils.removeGrayEffect(binding.llCustomColor);
            GrayEffectUtils.applyGrayEffect(binding.sbLightBrightnessAdjust, false);
            GrayEffectUtils.applyGrayEffect(binding.tvBrightnessAuto, true);

        } else {
            GrayEffectUtils.applyGrayEffect(binding.rvRecommendColor, false);
            binding.rvRecommendColor.setLayoutFrozen(true); // 冻结布局
            binding.rvRecommendColor.setNestedScrollingEnabled(false); // 禁用嵌套滚动
//            binding.rvRecommendColor.setClickable(false);  // 禁用点击事件
//            binding.rvRecommendColor.setLongClickable(false);  // 禁用长按事件
            GrayEffectUtils.applyGrayEffect(binding.tvLightSync, true);
            GrayEffectUtils.applyGrayEffect(binding.lightColorPicker, false);
            GrayEffectUtils.applyGrayEffect(binding.llCustomColor, true);
            GrayEffectUtils.applyGrayEffect(binding.sbLightBrightnessAdjust, false);
            GrayEffectUtils.applyGrayEffect(binding.tvBrightnessAuto, true);
        }
    }

    /**
     * DoorControlS，AutoReadLightSts
     *
     * @param msg
     */
    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.LIGHT_IN_INIT_DATA, MessageConst.LIGHT_AUTOMATIC_CEILING:
                automaticCeiling();
                break;
            case MessageConst.LIGHT_PICK_COLOR:
                binding.lightColorPicker.setImgPickerPosition(curPositionX);
                break;

            case MessageConst.LIGHT_PICK_COLOR_CUSTOM:
                Bundle dataBundle = msg.getData();
                int color = dataBundle.getInt(MessageConst.DATA_BUNDLE_COLOR);
                float bri = dataBundle.getFloat(MessageConst.DATA_BUNDLE_BRI);
                int pos = dataBundle.getInt(MessageConst.DATA_BUNDLE_POS);
                setAColorFilter(color, bri, pos);
                break;

        }
    }

    private Bundle setMsgData(int color, float bri, int pos) {
        Bundle dataBundle = new Bundle();
        dataBundle.putInt(MessageConst.DATA_BUNDLE_COLOR, color);
        dataBundle.putFloat(MessageConst.DATA_BUNDLE_BRI, bri);
        dataBundle.putInt(MessageConst.DATA_BUNDLE_POS, pos);
        return dataBundle;
    }

    private void automaticCeiling() {
        int ceilingStatus = lightInPresenter.getAutomaticCeiling();
        if (ceilingStatus != viewModel.getAutomaticCeiling().getValue()) {
            viewModel.setAutomaticCeiling(ceilingStatus);
        }
    }


    private void regLightListen() {
        manager.addCallback(TAG, new ILightManagerListener() {
            /**
             * 自动吸顶
             */
            @Override
            public void autoReadLightStsCallback(int status) {
                Log.d(TAG, "autoReadLightStsCallback status: " + status);
                if (viewModel.getAutomaticCeiling().getValue() != null && viewModel.getAutomaticCeiling().getValue() != status) {
                    viewModel.setAutomaticCeiling(status);
                }
            }

            @Override
            public void getLHFdoorStsCallback(int status) {
            }

            @Override
            public void getRHFDoorStsCallback(int status) {
            }

            @Override
            public void getLHRdoorStsCallback(int status) {
            }

            @Override
            public void getRHRDoorStsCallback(int status) {
            }

            @Override
            public void getAuotLampCallback(int status) {
                Log.d(TAG, "getAuotLampCallback: " + status);
                if (viewModel.getAutoBright().getValue() == 1) {
                    if (status == CarLight.SAutoLamp.ACTIVATE) {
                        // 明亮
                        viewModel.setColorBrightsPos(CarLight.BrightAuto.HIGHT);

                    } else if (status == CarLight.SAutoLamp.DEAC_TIVATE) {
                        // 暗
                        viewModel.setColorBrightsPos(CarLight.BrightAuto.LOW);
                    }
                    lightInPresenter.setBrightnessAuto(1);
                }
            }

            @Override
            public void powerDownLightOff(int status) {
                Log.d(TAG, "powerDownLightOff status:   " + status);
            }


        });
        manager.registerListener();
    }


    private void initObserver() {
        viewModel.getLightSw().observe(getViewLifecycleOwner(), status -> {
            // 氛围灯开启关闭UI
            updateLightSwUI(status);
        });

        viewModel.getThemeSw().observe(getViewLifecycleOwner(), status -> {
            // 主题自定义切换
            updateThemeSwitchingCustomUI(status);
        });
        viewModel.getLightSel().observe(getViewLifecycleOwner(), status -> {
            // 前后排选择
            updateLightSelUI(status);
        });
        viewModel.getLightFront().observe(getViewLifecycleOwner(), status -> {
            // 前排点亮
            updateLightFront(status);
        });
        viewModel.getLightRear().observe(getViewLifecycleOwner(), status -> {
            // 后排点亮
            updateLightRear(status);
        });
        viewModel.getLightSync().observe(getViewLifecycleOwner(), status -> {
            // 同步
            updateLightSyncUI(status);
        });
        viewModel.getColorPos().observe(getViewLifecycleOwner(), status -> {
            // 色块选中
            UpdateColorPosUI(status);
        });

        viewModel.getColorBrightsPos().observe(getViewLifecycleOwner(), progress -> {
            // 亮度
            updateColorBrightsUI(progress);
        });

        viewModel.getAutomaticCeiling().observe(getViewLifecycleOwner(), status -> {
            // 自动顶灯
            updateAutomaticCeilingUI(status);
        });

        viewModel.getAutoBright().observe(getViewLifecycleOwner(), status -> {
            //
            updateAutoBrightsUI(status);
        });

        viewModel.getXpos().observe(getViewLifecycleOwner(), status -> {
            //
            updatePickColorUI(status);
        });

    }

    private void updatePickColorUI(int positionX) {
        // 同步
//        binding.lightColorPicker.setImgPickerPosition(positionX);
    }

    private void updateLightSyncUI(int open) {
        // 同步
        binding.tvLightSync.setSelected(open == 1);
    }


    private void updateLightSwUI(int open) {
        // 氛围灯开关
        binding.tvLightSw.setSelected(open == 1);


    }

    private void updateLightSelUI(int status) {
        curLightArea = status;
        selLightAreatranslate();
    }

    private void updateLightFront(int status) {
        if (status == 1) {
            binding.viewLightAreaFont.setVisibility(View.VISIBLE);
        } else {
            binding.viewLightAreaFont.setVisibility(View.INVISIBLE);
        }
    }

    private void updateLightRear(int status) {
        if (status == 1) {
            binding.viewLightAreaRear.setVisibility(View.VISIBLE);
        } else {
            binding.viewLightAreaRear.setVisibility(View.GONE);
        }
    }

    // 亮度
    private void updateColorBrightsUI(int progress) {
        if (binding.sbLightBrightnessAdjust.getProgress() != progress) {
            binding.sbLightBrightnessAdjust.setProgress(progress);
        }
    }

    private void updateAutoBrightsUI(int status) {
        binding.tvBrightnessAuto.setSelected(status == 1);
    }

    // 自动顶灯
    private void updateAutomaticCeilingUI(int lightSwSate) {
        binding.tvLightAutoTop.setSelected(lightSwSate == 1);
    }


    // 主题 自定义
    private void updateThemeSwitchingCustomUI(int swLightAtmosphere) {
        binding.spvLightAtmosphere.setSelectedIndex(swLightAtmosphere, false);
        lightPresenter.setSwitchView(swLightAtmosphere, binding.rvRecommendColor, binding.llLightCustom);
    }

    // 更新颜色
    @SuppressLint("NotifyDataSetChanged")
    private void UpdateColorPosUI(int pos) {
        for (ColorBean colorBean : colorBeanList) {
            colorBean.isSelected = false;
        }
        ColorBean colorBean = colorBeanList.get(pos);
        if (inBean.getThemeMode() == CommonConst.TAB_0) {
            // 根据选中的颜色设置氛围灯图片的显示灯带效果
            if (!StringUtils.isEmpty(colorBean.colorHex)) {
                binding.ivLightCar1.setColorFilter(ColorUtils.adjustBrightness(ColorUtils.hexColorToInt(colorBean.colorHex), viewModel.getColorBrightsPos().getValue() / 100.0f), PorterDuff.Mode.SRC_ATOP);
                binding.ivLightCar2.setColorFilter(ColorUtils.adjustBrightness(ColorUtils.hexColorToInt(colorBean.colorHex), viewModel.getColorBrightsPos().getValue() / 100.0f), PorterDuff.Mode.SRC_ATOP);
                binding.ivLightCar3.setColorFilter(ColorUtils.adjustBrightness(ColorUtils.hexColorToInt(colorBean.colorHex), viewModel.getColorBrightsPos().getValue() / 100.0f), PorterDuff.Mode.SRC_ATOP);
                binding.ivLightCar4.setColorFilter(ColorUtils.adjustBrightness(ColorUtils.hexColorToInt(colorBean.colorHex), viewModel.getColorBrightsPos().getValue() / 100.0f), PorterDuff.Mode.SRC_ATOP);
            }
        } else {
            if (!StringUtils.isEmpty(curColor)) {
                lightHandler.sendMessage(MessageConst.LIGHT_PICK_COLOR_CUSTOM, setMsgData(ColorUtils.hexColorToInt(curColor), viewModel.getColorBrightsPos().getValue() / 100.0f, inBean.getLightSel()));
            }
        }
        Log.d(TAG, "applyBrightness: " + ColorUtils.hexColorToInt(colorBean.colorHex));
        colorBean.isSelected = true;
        binding.rvRecommendColor.scrollBy(binding.rvRecommendColor.getScrollX() + 1, binding.rvRecommendColor.getScrollY());
        colorAdapter.notifyDataSetChanged();
        binding.rvRecommendColor.scrollBy(binding.rvRecommendColor.getScrollX() - 1, binding.rvRecommendColor.getScrollY());
    }

    /**
     * @return
     */
    @Override
    public boolean isActive() {
        return isActive;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (manager != null) {
            manager.removeCallback(TAG);
            manager.unregisterListener();
            manager = null;
        }

        if (binding != null) {
            binding = null;
        }
        EventBus.getDefault().unregister(this);
        SuggestColorAdapter.clearListener();

    }

    private boolean checkCurpageOpen() {
        MainActivity activity = (MainActivity) getActivity();
        if (activity != null) {
            if (activity.getBinding().tvLight.isSelected()) {
                return true;
            }
        }
        return false;
    }

    public ArrayList<Content> getData() {
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        if (inBean != null) {
            //        1氛围灯开关	AtmosphereLampSw
            dataList.add(reportData(CommonConst.CodeId.ZB141801, CommonConst.Att.AtmosphereLampSw, String.valueOf(inBean.getLightSw())));
            //        2亮度调节	LightSet
            dataList.add(reportData(CommonConst.CodeId.ZB141803, CommonConst.Att.LightSet, String.valueOf(inBean.getBrightPos()) + "%"));
            //        3前排	FrontRowSw
            dataList.add(reportData(CommonConst.CodeId.ZB141804, CommonConst.Att.FrontRowSw, String.valueOf(inBean.getLightSw())));
            //        4后排	BackRowSw
            dataList.add(reportData(CommonConst.CodeId.ZB141804, CommonConst.Att.BackRowSw, String.valueOf(inBean.getLightSw())));
            //        5远近光自动切换	ASHLBSw
            //        6智能迎宾	AutowelampSw
            //        7靠近迎宾	CourtesyLightSw
            //        8动态氛围灯	TrendSw
            int trendSw = 0;
            if (viewModel.getLightSw().getValue() == 0) {
                trendSw = 0;
            } else {
                if (swLightEffect == 0) {
                    trendSw = 1;
                } else if (swLightEffect == 1) {
                    trendSw = 2;
                } else if (swLightEffect == 2) {
                    trendSw = 3;
                } else if (swLightEffect == 3) {
                    trendSw = 4;
                }
            }
            dataList.add(reportData(CommonConst.CodeId.ZB141815, CommonConst.Att.TrendSw, String.valueOf(trendSw)));
            //        灯光效果	LightingEffect
            dataList.add(reportData(CommonConst.CodeId.ZB141816, CommonConst.Att.LightingEffect, String.valueOf(trendSw)));
            //        大灯延时关闭	HLdelaySet
            //        自动顶灯	AutoinLampSw
            dataList.add(reportData(CommonConst.CodeId.ZB141818, CommonConst.Att.AutoinLampSw, String.valueOf(inBean.getAutomaticCeiling())));

            //        大灯高度调节	HeightSet
            //        亮度设置	ABALSet
            dataList.add(reportData(CommonConst.CodeId.ZB141820, CommonConst.Att.ABALSet, String.valueOf(inBean.getBrightnessAuto())));
            //        颜色调节	LampTypeSet
            //        1：单色氛围；
            //        2：无级调色；
            //        3：驾驶模式；
            //        4：车速模式；
            //        5：音乐律动；
            int lampTypeSet = 0;
            if (swLightEffect == 0) {
                if (viewModel.getLightSel().getValue() == 0) {
                    lampTypeSet = 1;
                } else {
                    lampTypeSet = 2;
                }
            } else if (swLightEffect == 3) {
                lampTypeSet = 5;
            }
            dataList.add(reportData(CommonConst.CodeId.ZB141802, CommonConst.Att.LampTypeSet, String.valueOf(lampTypeSet)));
            //        分区同步	FrontwallSw
            dataList.add(reportData(CommonConst.CodeId.ZB141804, CommonConst.Att.FrontwallSw, String.valueOf(inBean.getLightSync())));
        }

        return dataList;
    }

    private Content reportData(String attributeId, String locationId, String attributeValue) {
        Content content = new Content();
        content.setAttributeId(attributeId);
        content.setLocationId(locationId);
        content.setAttributeValue(attributeValue);
        return content;
    }

    private void setAColorFilter(int color, float bri, int pos) {
        if (inBean == null) {
            return;
        }
        if (binding == null) {
            return;
        }
        if (pos == CommonConst.TAB_0) {
            if (binding.ivLightCar1 != null) {
                binding.ivLightCar1.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCar2 != null) {
                binding.ivLightCar2.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCar3 != null) {
                binding.ivLightCar3.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCar4 != null) {
                binding.ivLightCar4.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
        } else {
            float frontBri = inBean.getFront().getFrontBrightness() / 100.0f;
            int frontColor = inBean.getFront().getFrontColor();
            if (binding.ivLightCarRear1 != null) {
                binding.ivLightCarRear1.setColorFilter(ColorUtils.adjustBrightness(frontColor, frontBri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCarRear2 != null) {
                binding.ivLightCarRear2.setColorFilter(ColorUtils.adjustBrightness(frontColor, frontBri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCarRear3 != null) {
                binding.ivLightCarRear3.setColorFilter(ColorUtils.adjustBrightness(frontColor, frontBri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCarRear4 != null) {
                binding.ivLightCarRear4.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCarRear5 != null) {
                binding.ivLightCarRear5.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCarRear6 != null) {
                binding.ivLightCarRear6.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCarRear7 != null) {
                binding.ivLightCarRear7.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCarRear8 != null) {
                binding.ivLightCarRear8.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
            if (binding.ivLightCarRear9 != null) {
                binding.ivLightCarRear9.setColorFilter(ColorUtils.adjustBrightness(color, bri), PorterDuff.Mode.SRC_ATOP);
            }
        }
    }
}
