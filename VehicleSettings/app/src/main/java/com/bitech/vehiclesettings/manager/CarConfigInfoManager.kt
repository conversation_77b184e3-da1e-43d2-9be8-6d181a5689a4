package com.bitech.vehiclesettings.manager

import android.car.CarInfoManager
import android.car.hardware.CarPropertyValue
import android.car.hardware.property.CarPropertyManager
import com.bitech.vehiclesettings.carapi.constants.CarArea
import com.bitech.vehiclesettings.carapi.constants.CarAvm
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.view.widget.SettingsToast

/**
 * @Description: 车辆信息管理类.
 **/
class CarConfigInfoManager {

    // 车辆信息管理对象
    private var carInfoManager: CarInfoManager? = null

    // 车辆属性管理对象
    private var carPropertyManager: CarPropertyManager? = null

    // 车辆信息
    private var uuid: String? = null
    private var ecuHwNumber: String? = null
    private var ecuSwNumber: String? = null
    private var ecuSerialNumber: String? = null
    private var vehiclePartNumber: String? = null
    // 车辆配置信息
//    private var carConfigInfo = CarConfigInfo()

    // 配置字对象
    private var did10 = ByteArray(4)

    // 自定义Toast
    private var toast: SettingsToast? = null

    // 系统属性设置监听回调
    private val carPropertyCallback: CarPropertyManager.CarPropertyEventCallback by lazy {
        object : CarPropertyManager.CarPropertyEventCallback {
            override fun onChangeEvent(carValue: CarPropertyValue<*>) {
                // 更新相关信息订阅
                updateVehicleInfo(carValue)
            }

            override fun onErrorEvent(p0: Int, p1: Int) {
                // TODO:
            }
        }
    }

    /**
     * 获取车辆配置节信息.
     *
     */
    fun initCarConfigInfo() {
        carInfoManager = CarBaseManager.getCarInfoManager()
        carPropertyManager = CarBaseManager.getCarPropertyManager()
        // 注册车辆信息监听
        carPropertyManager?.registerCallback(
            carPropertyCallback, 0x00000001,
            RATE
        )
        LogUtil.d(TAG, "initCarConfigInfo : carInfoManager = $carInfoManager")
//        did10 = carInfoManager!!.getByteProperty(CarInfoManager.ID_DIAGNOSTIC_CONFIG_7010)
//        uuid = carInfoManager!!.getStringProperty(CarInfoManager.ID_UUID)
//        ecuHwNumber = carInfoManager!!.getStringProperty(CarInfoManager.ID_ECU_HW_NUMBER)
//        ecuSwNumber = carInfoManager!!.getStringProperty(CarInfoManager.ID_ECU_SW_NUMBER)
//        ecuSerialNumber = carInfoManager!!.getStringProperty(CarInfoManager.ID_ECU_SERIAL_NUMBER)
//        vehiclePartNumber = carInfoManager!!.getStringProperty(CarInfoManager.ID_VEHICLE_PART_NUMBER)
        // 解析配置字
        parseConfig(did10)
    }

    /**
     * 更新车辆相关相关信息.
     *
     * @param carValue CarPropertyValue
     */
    private fun updateVehicleInfo(carValue: CarPropertyValue<*>) {
        val id = carValue.propertyId
        val value = carValue.value
        LogUtil.i(TAG, "updateVehicleInfo : id = $id , value = $value")
        //TODO 根据具体ID做逻辑处理
    }

    /**
     * 解析配置字.
     *
     */
    private fun parseConfig(data: ByteArray?) {
        if (data != null && data.size >= 4) {
            //TODO 解析具体配置
        }
    }

    /**
     * 是否是bev车型.
     *
     * @return
     */
    fun isBev(): Boolean {
        return true
    }

    /**
     * 是否是hev车型.
     *
     * @return
     */
    fun isHev(): Boolean {
        return false
    }

    /**
     * 获取档位状态.
     *
     * @return 档位状态
     */
    fun getGearPosition(): Int {
        val status =
            carPropertyManager?.getIntProperty(0x00001, CarArea.GLOBAL)
                ?: CarAvm.GBPositoionDisplay.NOT_DISPLAY
        LogUtil.i(TAG, "getGearPosition : status = $status")
        return status
    }

    /**
     * 释放车辆配置信息.
     *
     */
    fun unInitCarConfigInfo() {
        carInfoManager = null
        // 注销车辆信息监听
        carPropertyManager?.unregisterCallback(carPropertyCallback)
        carPropertyManager = null
    }

    /**
     * 该车辆是否支持WIFI.
     *
     * @return 支持与否
     */
    fun isSupportWifi(): Boolean {
        return true
    }


    companion object {
        // 日志标志位
        private const val TAG = "CarConfigInfoManager"

        const val NO = 0
        const val YES = 1
        const val RATE = 0F
        // 单例对象
        val instance: CarConfigInfoManager by lazy(LazyThreadSafetyMode.PUBLICATION) {
            CarConfigInfoManager()
        }
    }
}
