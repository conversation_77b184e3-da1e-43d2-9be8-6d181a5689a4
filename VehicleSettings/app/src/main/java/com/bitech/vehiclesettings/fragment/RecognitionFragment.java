package com.bitech.vehiclesettings.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.lifecycle.ViewModelProvider;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.FragmentRecognitionBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.recognition.RecognitionPresenter;
import com.bitech.vehiclesettings.presenter.recognition.RecognitionPresenterListener;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.recognition.CameraSwitchOffUIAlert;
import com.bitech.vehiclesettings.view.recognition.CameraSwitchOnUIAlert;
import com.bitech.vehiclesettings.viewmodel.RecognitionModel;

public class RecognitionFragment extends BaseFragment<FragmentRecognitionBinding> implements View.OnClickListener, SafeHandlerCallback {
    private static final String TAG = RecognitionFragment.class.getSimpleName();
    RecognitionPresenterListener presenter;
    private SafeHandler recognitionHandler;
    private CameraSwitchOnUIAlert.Builder cameraSwitchOnUIAlert;
    private CameraSwitchOffUIAlert.Builder cameraSwitchOffUIAlert;
    private DetailsUIAlert.Builder detailUIAlert;
    private RecognitionModel viewModel;
    private volatile boolean isActive;

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null) return;
        loadPageAnim(binding.scrollView, currentPosition, position);
    }

    public void setPresenter(RecognitionPresenterListener presenter) {
        this.presenter = presenter;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isActive = true;
        setPresenter(new RecognitionPresenter<>(getContext()));
        recognitionHandler = new SafeHandler(this);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View rootView = getLayoutResId(inflater, container).getRoot();
        initObserver();
        initView();
        setListener();
        return rootView;
    }

    protected void initData() {
        // 获取数据绑定
        viewModel.setCamera(Prefs.get(PrefsConst.R_DMS_CAMERA_STATUS, 0));
        viewModel.setFatigue(Prefs.get(PrefsConst.R_FATIGUE_DETECTION_STATUS, 0));
        viewModel.setDistraction(Prefs.get(PrefsConst.R_DISTRACTION_STATUS, 0));
        viewModel.setCall(Prefs.get(PrefsConst.R_CALL_STATUS, 0));
        viewModel.setDrink(Prefs.get(PrefsConst.R_DRINK_STATUS, 0));
        viewModel.setSeatHeat(Prefs.get(PrefsConst.R_SEAT_HEAT_STATUS, 0));
        viewModel.setSeatVentilation(Prefs.get(PrefsConst.R_SEAT_VENTILATION_STATUS, 0));
        viewModel.setSightUnlock(Prefs.get(PrefsConst.R_SIGHT_UNLOCK_STATUS, 0));
        viewModel.setGreet(Prefs.get(PrefsConst.R_GREET_STATUS, 0));
        viewModel.setSmoke(Prefs.get(PrefsConst.R_SMOKE_STATUS, 0));
    }

    @Override
    protected FragmentRecognitionBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentRecognitionBinding.inflate(getLayoutInflater());
        return binding;
    }

    @Override
    protected void initView() {
        initData();
        setDetailVisible();
        // 视觉服务: 根据不同的车型显示不同的模块
        presenter.setVisualService(1);
        int visualService = presenter.getVisualService();
        if (visualService == 0) {
            binding.llCallAndDrink.setVisibility(View.GONE);
            binding.llFatigueDetectionAndDistraction.setVisibility(View.GONE);
            binding.tvSafeCheck.setVisibility(View.GONE);
            binding.llSmoke.setVisibility(View.GONE);
            binding.llCamera.setVisibility(View.GONE);
            binding.llSightUnlock.setVisibility(View.GONE);
            binding.tvRecognitionVehicleControl.setVisibility(View.VISIBLE);
            binding.llSeatHeatAndSeatVentilation.setVisibility(View.VISIBLE);
            binding.tvRecognitionCare.setVisibility(View.VISIBLE);
            binding.llGreetAndSmoke.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 检测ViewModel中数据的变更并实时更新UI
     */
    private void initObserver() {
        viewModel = new ViewModelProvider(this).get(RecognitionModel.class);
        // 更新UI摄像头
        viewModel.getCamera().observe(getViewLifecycleOwner(), this::switchCamera);
        // 更新UI疲劳驾驶
        viewModel.getFatigue().observe(getViewLifecycleOwner(), this::switchFatigue);
        // 更新UI视线分心
        viewModel.getDistraction().observe(getViewLifecycleOwner(), this::switchDistraction);
        // 更新UI打电话提醒
        viewModel.getCall().observe(getViewLifecycleOwner(), this::switchCall);
        // 更新UI喝水提醒
        viewModel.getDrink().observe(getViewLifecycleOwner(), this::switchDrink);
        // 更新UI座椅加热
        viewModel.getSeatHeat().observe(getViewLifecycleOwner(), this::switchSeatHeat);
        // 更新UI座椅通风
        viewModel.getSeatVentilation().observe(getViewLifecycleOwner(), this::switchSeatVentilation);
        // 更新UI视线解锁
        viewModel.getSightUnlock().observe(getViewLifecycleOwner(), this::switchSightUnlock);
        // 更新UI个性化问候
        viewModel.getGreet().observe(getViewLifecycleOwner(), this::switchGreet);
        // 更新UI抽烟关怀
        viewModel.getSmoke().observe(getViewLifecycleOwner(), this::switchSmoke);
    }

    @Override
    protected void setListener() {
        BindingUtil.bindClicks(this, binding.swCamera, binding.swSmoke, binding.swCall, binding.swDistraction, binding.swGreet, binding.swFatigueDetection, binding.swSeatHeat, binding.swSeatVentilation, binding.swSightUnlock, binding.swDrink);
        BindingUtil.bindClicks(this, binding.ivCallTips, binding.ivDistractionTips, binding.ivGreetTips, binding.ivSmokeTips, binding.ivSeatHeatTips, binding.ivFatigueDetectionTips, binding.ivSeatVentilationTips, binding.ivSightUnlockTips, binding.ivDrinkTips);
        CameraSwitchOnUIAlert.setOnProgressChangedListener(str -> {
            int status = CommonUtils.BoolToInt("open".equals(str));
/*            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                viewModel.setCamera(status);
            }, 2000);*/
            presenter.setSwCamera(status);
            recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_DMS_CAMERA);
        });
        CameraSwitchOffUIAlert.setOnProgressChangedListener(str -> {
            int status = CommonUtils.BoolToInt("open".equals(str));
/*            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                viewModel.setCamera(status);
            }, 2000);*/
            presenter.setSwCamera(status);
            recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_DMS_CAMERA);
        });
    }

    @Override
    protected void initObserve() {

    }

    // DMS摄像头开关点击UI
    private void switchCamera(int status) {
        binding.swCamera.setChecked(CommonUtils.IntToBool(status));
    }

    // 疲劳检测开关点击UI
    private void switchFatigue(int status) {
        binding.swFatigueDetection.setChecked(CommonUtils.IntToBool(status));
    }

    // 视线分心提醒开关点击UI
    private void switchDistraction(int status) {
        binding.swDistraction.setChecked(CommonUtils.IntToBool(status));
    }

    // 打电话提醒开关点击UI
    private void switchCall(int status) {
        binding.swCall.setChecked(CommonUtils.IntToBool(status));
    }

    // 喝水提醒开关点击UI
    private void switchDrink(int status) {
        binding.swDrink.setChecked(CommonUtils.IntToBool(status));
    }

    // 座椅加热开关点击UI
    private void switchSeatHeat(int status) {
        binding.swSeatHeat.setChecked(CommonUtils.IntToBool(status));
    }

    // 座椅通风开关点击UI
    private void switchSeatVentilation(int status) {
        binding.swSeatVentilation.setChecked(CommonUtils.IntToBool(status));
    }

    // 视线解锁屏保开关点击UI
    private void switchSightUnlock(int status) {
        binding.swSightUnlock.setChecked(CommonUtils.IntToBool(status));
    }

    // 个性化问候开关点击UI
    private void switchGreet(int status) {
        binding.swGreet.setChecked(CommonUtils.IntToBool(status));
    }

    // 抽烟关怀开关点击UI
    private void switchSmoke(int status) {
        binding.swSmoke.setChecked(CommonUtils.IntToBool(status));
    }

    // 设置具体设置的显示隐藏效果绑定
    @SuppressLint("ClickableViewAccessibility")
    private void setDetailVisible() {
        binding.swCamera.setOnCheckedChangeListener((c, b) -> {
            Log.d("摄像头测试", b + "");
            binding.llRecognition.setVisibility(b ? View.VISIBLE : View.GONE);
        });
        binding.llRecognition.setVisibility(binding.swCamera.isChecked() ? View.VISIBLE : View.GONE);
    }

    private void openCameraOpenDialog() {
        if (cameraSwitchOnUIAlert != null && cameraSwitchOnUIAlert.isShowing()) {
            return;
        }
        if (cameraSwitchOnUIAlert == null) {
            cameraSwitchOnUIAlert = new CameraSwitchOnUIAlert.Builder(mContext);
        }
        cameraSwitchOnUIAlert.create().show();
    }

    private void openCameraCloseDialog() {
        if (cameraSwitchOffUIAlert != null && cameraSwitchOffUIAlert.isShowing()) {
            return;
        }
        if (cameraSwitchOffUIAlert == null) {
            cameraSwitchOffUIAlert = new CameraSwitchOffUIAlert.Builder(mContext);
        }
        cameraSwitchOffUIAlert.create().show();
    }

    // 打开自定义提示窗口
    private void openTipsDialog(String title, String content, int width, int height) {
        if (detailUIAlert != null && detailUIAlert.isShowing()) {
            return;
        }
        if (detailUIAlert == null) {
            detailUIAlert = new DetailsUIAlert.Builder(mContext);
        }
        detailUIAlert.create(title, content, width, height).show();
    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.sw_camera:
                if (viewModel.getCamera().getValue() == 1) {
                    openCameraCloseDialog();
                } else {
                    openCameraOpenDialog();
                }
                break;
            case R.id.sw_fatigue_detection:
                if (viewModel.getFatigue().getValue() == 1) {
                    viewModel.setFatigue(0);
                    presenter.setSwFatigue(0);
                } else {
                    viewModel.setFatigue(1);
                    presenter.setSwFatigue(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_FATIGUE);
                break;
            case R.id.sw_distraction:
                if (viewModel.getDistraction().getValue() == 1) {
                    viewModel.setDistraction(0);
                    presenter.setSwDistraction(0);
                } else {
                    viewModel.setDistraction(1);
                    presenter.setSwDistraction(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_DISTRACTION);
                break;
            case R.id.sw_call:
                if (viewModel.getCall().getValue() == 1) {
                    viewModel.setCall(0);
                    presenter.setSwCall(0);
                } else {
                    viewModel.setCall(1);
                    presenter.setSwCall(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_CALL);
                break;
            case R.id.sw_drink:
                if (viewModel.getDrink().getValue() == 1) {
                    viewModel.setDrink(0);
                    presenter.setSwDrink(0);
                } else {
                    viewModel.setDrink(1);
                    presenter.setSwDrink(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_DRINK);
                break;
            case R.id.sw_seat_heat:
                if (viewModel.getSeatHeat().getValue() == 1) {
                    viewModel.setSeatHeat(0);
                    presenter.setSwSeatHeat(0);
                } else {
                    viewModel.setSeatHeat(1);
                    presenter.setSwSeatHeat(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_SEAT_HEAT);
                break;
            case R.id.sw_seat_ventilation:
                if (viewModel.getSeatVentilation().getValue() == 1) {
                    viewModel.setSeatVentilation(0);
                    presenter.setSwSeatVentilation(0);
                } else {
                    viewModel.setSeatVentilation(1);
                    presenter.setSwSeatVentilation(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_SEAT_VENTILATION);
                break;
            case R.id.sw_sight_unlock:
                if (viewModel.getSightUnlock().getValue() == 1) {
                    viewModel.setSightUnlock(0);
                    presenter.setSwSightUnlock(0);
                } else {
                    viewModel.setSightUnlock(1);
                    presenter.setSwSightUnlock(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_SIGHT_UNLOCK);
                break;
            case R.id.sw_greet:
                if (viewModel.getGreet().getValue() == 1) {
                    viewModel.setGreet(0);
                    presenter.setSwGreet(0);
                } else {
                    viewModel.setGreet(1);
                    presenter.setSwGreet(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_GREET);
                break;
            case R.id.sw_smoke:
                if (viewModel.getSmoke().getValue() == 1) {
                    viewModel.setSmoke(0);
                    presenter.setSwSmoke(0);
                } else {
                    viewModel.setSmoke(1);
                    presenter.setSwSmoke(1);
                }
                recognitionHandler.sendMessageDelayed(MessageConst.RECOGNITION_SMOKE);
                break;
            case R.id.iv_fatigue_detection_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title), getString(R.string.str_recognition_tips_content), 1128, 378);
                break;
            case R.id.iv_distraction_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title_1), getString(R.string.str_recognition_tips_content_1), 1128, 278);
                break;
            case R.id.iv_call_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title_2), getString(R.string.str_recognition_tips_content_2), 1128, 278);
                break;
            case R.id.iv_drink_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title_3), getString(R.string.str_recognition_tips_content_3), 1128, 278);
                break;
            case R.id.iv_seat_heat_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title_4), getString(R.string.str_recognition_tips_content_4), 1128, 278);
                break;
            case R.id.iv_seat_ventilation_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title_5), getString(R.string.str_recognition_tips_content_5), 1128, 278);
                break;
            case R.id.iv_sight_unlock_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title_6), getString(R.string.str_recognition_tips_content_6), 1128, 328);
                break;
            case R.id.iv_greet_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title_7), getString(R.string.str_recognition_tips_content_7), 1128, 278);
                break;
            case R.id.iv_smoke_tips:
                openTipsDialog(getString(R.string.str_recognition_tips_title_8), getString(R.string.str_recognition_tips_content_8), 1128, 328);
                break;
            default:
                break;

        }
    }

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.RECOGNITION_DMS_CAMERA:
                int cameraStatus = presenter.getSwCamera();
                Log.d("RecognitionFragment", "摄像头状态:" + cameraStatus);
                if (cameraStatus == viewModel.getCamera().getValue()) {
                    Log.d(TAG, "摄像头设置失败");
                    viewModel.setCamera(cameraStatus);
                    if (cameraStatus == 1) {
                        if (cameraSwitchOffUIAlert != null) {
                            cameraSwitchOffUIAlert.openFail();
                        }
                    } else if (cameraStatus == 0) {
                        if (cameraSwitchOnUIAlert != null) {
                            cameraSwitchOnUIAlert.openFail();
                        }
                    }
                } else {
                    Log.d(TAG, "摄像头设置成功");
                    viewModel.setCamera(cameraStatus);
                    if (cameraStatus == 1) {
                        if (cameraSwitchOnUIAlert != null) {
                            cameraSwitchOnUIAlert.openSuccess();
                        }
                    } else if (cameraStatus == 0) {
                        if (cameraSwitchOffUIAlert != null) {
                            cameraSwitchOffUIAlert.openSuccess();
                        }
                    }
                }
                break;
            case MessageConst.RECOGNITION_FATIGUE:
                int fatigueStatus = presenter.getSwFatigue();
                if (fatigueStatus != viewModel.getFatigue().getValue()) {
                    viewModel.setFatigue(fatigueStatus);
                }
                break;
            case MessageConst.RECOGNITION_DISTRACTION:
                int distractionStatus = presenter.getSwDistraction();
                if (distractionStatus != viewModel.getDistraction().getValue()) {
                    viewModel.setDistraction(distractionStatus);
                }
            case MessageConst.RECOGNITION_CALL:
                int callStatus = presenter.getSwCall();
                if (callStatus != viewModel.getCall().getValue()) {
                    viewModel.setCall(callStatus);
                }
                break;
            case MessageConst.RECOGNITION_DRINK:
                int drinkStatus = presenter.getSwDrink();
                if (drinkStatus != viewModel.getDrink().getValue()) {
                    viewModel.setDrink(drinkStatus);
                }
                break;
            case MessageConst.RECOGNITION_SEAT_HEAT:
                int seatHeatStatus = presenter.getSwSeatHeat();
                if (seatHeatStatus != viewModel.getSeatHeat().getValue()) {
                    viewModel.setSeatHeat(seatHeatStatus);
                }
                break;
            case MessageConst.RECOGNITION_SEAT_VENTILATION:
                int seatVentilationStatus = presenter.getSwSeatVentilation();
                if (seatVentilationStatus != viewModel.getSeatVentilation().getValue()) {
                    viewModel.setSeatVentilation(seatVentilationStatus);
                }
                break;
            case MessageConst.RECOGNITION_SIGHT_UNLOCK:
                int sightUnlockStatus = presenter.getSwSightUnlock();
                if (sightUnlockStatus != viewModel.getSightUnlock().getValue()) {
                    viewModel.setSightUnlock(sightUnlockStatus);
                }
                break;
            case MessageConst.RECOGNITION_GREET:
                int greetStatus = presenter.getSwGreet();
                if (greetStatus != viewModel.getGreet().getValue()) {
                    viewModel.setGreet(greetStatus);
                }
                break;
            case MessageConst.RECOGNITION_SMOKE:
                int smokeStatus = presenter.getSwSmoke();
                if (smokeStatus != viewModel.getSmoke().getValue()) {
                    viewModel.setSmoke(smokeStatus);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public boolean isActive() {
        return isActive;
    }

    /**
     * Fragment 销毁时，做进一步清理，比如关闭对话框
     */
    @Override
    public void onDestroy() {
        super.onDestroy();
        // 如果对话框仍然显示，关闭它
        if (cameraSwitchOnUIAlert != null && cameraSwitchOnUIAlert.create().isShowing()) {
            cameraSwitchOnUIAlert.create().dismiss();
        }
        if (cameraSwitchOffUIAlert != null && cameraSwitchOffUIAlert.create().isShowing()) {
            cameraSwitchOffUIAlert.create().dismiss();
        }
        // 释放 Presenter 的引用，若有相应的释放方法
        presenter = null;
        isActive = false;
        if (binding != null) {
            binding = null;
        }
    }
}