package com.bitech.vehiclesettings.view.system;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class PermissionUIAlert extends BaseDialog {
    private static final String TAG = PermissionUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public PermissionUIAlert(@NonNull Context context) {
        super(context);
    }

    public PermissionUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PermissionUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static PermissionUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        PermissionUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSPermissionBinding binding;

        private AccessRecordUIAlert.Builder accessRecordUIAlert;
        private PermissionAppUIAlert.Builder permissionCameraUIAlert, permissionMicrophoneUIAlert, permissionPositionUIAlert;

        private PermissionUIAlert dialog = null;
        private boolean isInitialized = false; // 添加初始化标志

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;


        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public PermissionUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new PermissionUIAlert(context, R.style.Dialog);
            }
            dialog.setCancelable(isCan);

            if (!isInitialized) { // 确保只初始化一次
                binding = DialogAlertSPermissionBinding.inflate(LayoutInflater.from(context));
                dialog.setContentView(binding.getRoot());

                // 初始化窗口参数
                Window window = dialog.getWindow();
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1900; // 或者使用具体的像素值
                layoutParams.height = 800;
                window.setAttributes(layoutParams);

                // 初始化子Alert
                clickAccessRecord();
                clickPermissionApp();

                isInitialized = true;
            }

            return dialog;
        }

        // 设置权限应用
        private void clickPermissionApp() {
            permissionCameraUIAlert = new PermissionAppUIAlert.Builder(context, 0,
                    context.getString(R.string.str_system_permission_camera), context.getString(R.string.str_system_permission_camera_switch),
                    context.getString(R.string.str_system_permission_camera_link), context.getString(R.string.str_system_permission_camera_text_close), context.getString(R.string.str_system_permission_camera_text_open),
                    context.getString(R.string.str_system_permission_camera_app_close), context.getString(R.string.str_system_permission_camera_app_open));
            binding.rlCamera.setOnClickListener(v -> {
//                permissionCameraUIAlert.create().show();
                onProgressChangedListener.openApp(0);
            });

            permissionMicrophoneUIAlert = new PermissionAppUIAlert.Builder(context, 1,
                    context.getString(R.string.str_system_permission_microphone), context.getString(R.string.str_system_permission_microphone_switch),
                    context.getString(R.string.str_system_permission_microphone_link), context.getString(R.string.str_system_permission_microphone_text_close), context.getString(R.string.str_system_permission_microphone_text_open),
                    context.getString(R.string.str_system_permission_microphone_app_close), context.getString(R.string.str_system_permission_microphone_app_open));
            binding.rlMicrophone.setOnClickListener(v -> {
//                permissionMicrophoneUIAlert.create().show();
                onProgressChangedListener.openApp(1);
            });

            permissionPositionUIAlert = new PermissionAppUIAlert.Builder(context, 2,
                    context.getString(R.string.str_system_permission_location), context.getString(R.string.str_system_permission_location_switch),
                    context.getString(R.string.str_system_permission_location_link), context.getString(R.string.str_system_permission_location_text_close), context.getString(R.string.str_system_permission_location_text_open),
                    context.getString(R.string.str_system_permission_location_app_close), context.getString(R.string.str_system_permission_location_app_open));
            binding.rlPosition.setOnClickListener(v -> {
//                    permissionPositionUIAlert.create().show();
                onProgressChangedListener.openApp(2);
            });

            binding.rlSpecial.setOnClickListener(v -> {
                onProgressChangedListener.openPermissionSpecial();
            });
        }

        // 设置访问记录
        private void clickAccessRecord() {
            BindingUtil.bindClick(binding.ivAccessRecord, v -> {
                accessRecordUIAlert = new AccessRecordUIAlert.Builder(context);
                accessRecordUIAlert.create().show();
            });
        }

        public AccessRecordUIAlert.Builder getAccessRecordUIAlert() {
            return accessRecordUIAlert;
        }

        public PermissionAppUIAlert.Builder getPermissionCameraUIAlert() {
            return permissionCameraUIAlert;
        }

        public PermissionAppUIAlert.Builder getPermissionMicrophoneUIAlert() {
            return permissionMicrophoneUIAlert;
        }

        /**
         * 获取bind
         *
         * @return
         */
        public DialogAlertSPermissionBinding getBinding() {
            return binding;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface onProgressChangedListener {
        void openApp(int position);

        void openPermissionSpecial();
    }
}
