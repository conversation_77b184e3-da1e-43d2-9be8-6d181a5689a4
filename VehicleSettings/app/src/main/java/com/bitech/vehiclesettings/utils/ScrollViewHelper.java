package com.bitech.vehiclesettings.utils;


import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.view.View;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.R;

public class ScrollViewHelper {

    public static void updateScrollView(Context context, View scrollView){
        if (scrollView == null){
            return;
        }
        Drawable thumbDrawable = null;
        thumbDrawable = context.getDrawable(R.drawable.common_scrollbar_thumb);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            scrollView.setVerticalScrollbarThumbDrawable(thumbDrawable);
        }
    }

}
