package com.bitech.vehiclesettings.activity;

import static com.bitech.vehiclesettings.MyApplication.getContext;
import static com.bitech.vehiclesettings.service.DataPointReportLifeCycle.KEY_DATA_POINT;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.content.Intent;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.DecelerateInterpolator;
import android.widget.Switch;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;

import com.bitech.base.activity.BaseActivity;
import com.bitech.vehicle3D.IFrom3DWPHandler;
import com.bitech.vehicle3D.VehicleServiceManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.bean.report.Content;
import com.bitech.vehiclesettings.bean.report.DataPoint;
import com.bitech.vehiclesettings.carapi.constants.Car3DModel;
import com.bitech.vehiclesettings.carapi.constants.CarWallpaper;
import com.bitech.vehiclesettings.databinding.ActivityMainBinding;
import com.bitech.vehiclesettings.fragment.CarSettingFragment;
import com.bitech.vehiclesettings.fragment.ConditionFragment;
import com.bitech.vehiclesettings.fragment.ConnectFragment;
import com.bitech.vehiclesettings.fragment.DisplayFragment;
import com.bitech.vehiclesettings.fragment.DrivingFragment;
import com.bitech.vehiclesettings.fragment.LightBaseFragment;
import com.bitech.vehiclesettings.fragment.NewEnergyFragment;
import com.bitech.vehiclesettings.fragment.NewEquipmentFragment;
import com.bitech.vehiclesettings.fragment.QuickControlFragment;
import com.bitech.vehiclesettings.fragment.RecognitionFragment;
import com.bitech.vehiclesettings.fragment.SystemFragment;
import com.bitech.vehiclesettings.fragment.VoiceFragment;
import com.bitech.vehiclesettings.presenter.display.WallpaperPresenter;
import com.bitech.vehiclesettings.presenter.main.MainPresenter;
import com.bitech.vehiclesettings.presenter.main.MainPresenterListener;
import com.bitech.vehiclesettings.service.DataPointReportLifeCycle;
import com.bitech.vehiclesettings.service.GlobalDataObserver;
import com.bitech.vehiclesettings.service.voice.ConnectControl;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.PagUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.SystemColorUtil;
import com.bitech.vehiclesettings.utils.ThreeDModelUtil;
import com.bitech.vehiclesettings.utils.TtsHelper;
import com.bitech.vehiclesettings.view.connect.BtFragment;
import com.bitech.vehiclesettings.view.connect.HotpotFragment;
import com.bitech.vehiclesettings.view.connect.WifiFragment;
import com.bitech.vehiclesettings.view.display.CleanUIAlert;
import com.bitech.vehiclesettings.view.system.DeviceInfoUIAlert;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;
import com.bitech.wallpaper3D.aidl.IRequestCallback;
import com.google.gson.Gson;
import com.iflytek.autofly.icvp.sdk.ICVPCallBack;
import com.iflytek.autofly.icvp.sdk.ICVPManager;
import com.iflytek.autofly.icvp.sdk.common.ICVPCameraConstants;
import com.jeremyliao.liveeventbus.LiveEventBus;

import org.libpag.PAGView;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

/**
 * 车辆设置、本地设置.
 */
public class MainActivity extends BaseActivity implements View.OnClickListener, CleanUIAlert.OnBackGestureListener, IFrom3DWPHandler {
    private static final String TAG = MainActivity.class.getSimpleName();
    private ActivityMainBinding binding;
    private QuickControlFragment quickControlFragment;
    private LightBaseFragment lightFragment;
    private NewEnergyFragment newEnergyFragment;
    private DrivingFragment drivingFragment;
    private NewEquipmentFragment newEquipmentFragment;
    private DisplayFragment displayFragment;
    private ConnectFragment connectFragment;
    private VoiceFragment voiceFragment;
    private SystemFragment systemFragment;
    private ConditionFragment conditionFragment;
    private DeviceInfoUIAlert.Builder deviceInfoUIAlert;

    private Bundle mBundle;

    private static int currentMainTabIndex = -1;
    private static boolean isActivityStarted;
    private PAGView lastPagView;
    private int lastPosition;
    //重写返回键
    private boolean interceptBackGesture = false;
    private ColorDrawable mainBgColor;

    private MainPresenterListener presenter;

    private MainActViewModel mainActViewModel;

    private static GlobalDataObserver globalDataObserver;

    public ActivityMainBinding getBinding() {
        return binding;
    }

    @Override
    protected View getLayoutResId() {
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt("selPosition", lastPosition);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        mainBgColor = new ColorDrawable(ContextCompat.getColor(this, R.color.main_bg_color));
        if (savedInstanceState != null) {
            lastPosition = savedInstanceState.getInt("selPosition");
        }
        mainActViewModel = new ViewModelProvider(this).get(MainActViewModel.class);
        presenter = new MainPresenter(MainActivity.this);
        // 初始化3D车模
        initWallpaper3DServices();

        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        );

        super.onCreate(savedInstanceState);
//        }


        handleIntent(getIntent());
        onOpenDataReport();
        // 设置默认主题色
        String systemColorValue = MyApplication.getInstance().getSystemColorValue();
        setTheme(SystemColorUtil.SystemColorValueToStyle(systemColorValue));
        // 初始化dms摄像头
        initDMSCamera();
//        // 测试权限
//        permissionTest();
        // 修复switch文字存在null的情况
        fixSwitchDefaultText(findViewById(android.R.id.content));
    }

    public void fixSwitchDefaultText(View root) {
        if (root instanceof Switch) {
            Switch sw = (Switch) root;
            if (sw.getTextOn() == null) sw.setTextOn("");
            if (sw.getTextOff() == null) sw.setTextOff("");
        } else if (root instanceof ViewGroup) {
            ViewGroup vg = (ViewGroup) root;
            for (int i = 0; i < vg.getChildCount(); i++) {
                fixSwitchDefaultText(vg.getChildAt(i));
            }
        }
    }


//    private void permissionTest() {
//        PermissionUtil permissionUtil = new PermissionUtil();
//        permissionUtil.test();
//    }

    private void initDMSCamera() {
        // 设置监听
        ICVPManager.initICVP(getContext(), new ICVPCallBack() {
            @Override
            public void success() {
                Log.d(TAG, "DMS摄像头初始化成功");
                // 保存初始化状态
                Prefs.put(PrefsConst.D_DMS_INIT_STATUS, 1);
                // 设置开启状态
                int status = Prefs.getInt(PrefsConst.D_DMS_OPEN_STATUS);
                ICVPManager.enableDmsCameraSw(status == 1);
                ICVPManager.addCameraStateListener((i, i1) -> {
                    if (i == ICVPCameraConstants.DMS_CAMERA) {
                        Log.d(TAG, "DMS摄像头状态" + i1);
                    }
                });
                Log.d(TAG, "DMS摄像头状态：" + status);
            }

            @Override
            public void fail() {
                Log.d(TAG, "DMS摄像头初始化失败");
                // 保存初始化状态
                Prefs.put(PrefsConst.D_DMS_INIT_STATUS, 0);
            }
        });
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    @Override
    public void initView() {

        binding.tvQuickControl.setSelected(true);
        ViewPagerFragmentAdapter adapter = new ViewPagerFragmentAdapter(getSupportFragmentManager());
        binding.viewPager.setAdapter(adapter);
//        binding.viewPager.setPageTransformer(true, new FadeTransformer());
        binding.viewPager.setOffscreenPageLimit(0);

        binding.viewPager.setCurrentItem(0, false);
        quickBackPic();
        initPagViewOff();
        BindingUtil.bindClicks(this, binding.tvQuickControl, binding.tvLight, binding.tvNewEnergy, binding.tvDrive,
                binding.tvDisplay, binding.tvConnect, binding.tvVoice, binding.tvSystem, binding.tvCondition);
        if (lastPosition == MainTabIndex.DISPLAY) {
            lastPagView = binding.pvDisplay;
            binding.setTabSelectedIndex(lastPosition);
        } else if (lastPosition == MainTabIndex.SYSTEM) {
            lastPagView = binding.pvSystem;
            binding.setTabSelectedIndex(lastPosition);
        } else if (lastPosition == MainTabIndex.VOICE) {
            lastPagView = binding.pvVoice;
            binding.setTabSelectedIndex(lastPosition);
        } else {
            lastPagView = binding.pvQuickControl;
            lastPosition = MainTabIndex.QUICK_CONTROL;
            onClick(binding.tvQuickControl);
        }
        PagUtils.playPagView(this, presenter.getPagsOn()[lastPosition], lastPagView);

        binding.maskView.setAlpha(0f);


    }

    public QuickControlFragment getQuickControlFragment() {
        return quickControlFragment;
    }

    public LightBaseFragment getLightFragment() {
        return lightFragment;
    }

    public NewEnergyFragment getNewEnergyFragment() {
        return newEnergyFragment;
    }

    public DrivingFragment getDrivingFragment() {
        return drivingFragment;
    }

    public NewEquipmentFragment getNewEquipmentFragment() {
        return newEquipmentFragment;
    }

    public DisplayFragment getDisplayFragment() {
        return displayFragment;
    }

    public ConnectFragment getConnectFragment() {
        return connectFragment;
    }

    public VoiceFragment getVoiceFragment() {
        return voiceFragment;
    }

    public SystemFragment getSystemFragment() {
        return systemFragment;
    }

    public ConditionFragment getConditionFragment() {
        return conditionFragment;
    }

    @Override
    protected void onResume() {
        Log.d(TAG, "onResume");
        super.onResume();
        WallpaperPresenter.setCurrentSceneWallpaper(CarWallpaper.Type3D.CAR_3D, false);
        VehicleServiceManager.startCameraStatusPollingTask(Car3DModel.ControlModelState.CONTROL_MODEL_STATE_ID, Car3DModel.ControlModelState.CONTROL);
//        //接收弹窗的传值：页面类型
        int targetPageType = getIntent().getIntExtra("target_page_type", 0);
        switch (targetPageType) {
            default:
                break;
            case MainTabIndex.CONNECT:
                setCurrentItem(targetPageType, binding.tvConnect);
                //MainViewModel 定义LiveData，Fragment监听
                //设置值
//                mainViewModel.setLiveData
                break;

        }
    }

    private void onCloseDataReport() {
        int resorce = 0;
        if (getIntent().getExtras() != null) {
            int targetTab = getIntent().getIntExtra(CommonConst.TARGET_TAB, -1);
            int operation = getIntent().getIntExtra(CommonConst.OPERATION, -1);
            resorce = 1;
            // 语音
            if (targetTab != -1) {
                if (operation == 0) {
                    Log.d(TAG, "handleIntent : finish");
                    return;
                }
            }
        } else {
            // 点击
            resorce = 2;
        }
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        String formattedTime = now.format(formatter);
        // 日志埋点
        Gson gson = new Gson();
        DataPoint dataPoint = new DataPoint();
        dataPoint.setId(CommonConst.DataPoint.id);
        dataPoint.setEventId(CommonConst.EventId.App_Close);
        dataPoint.setTimestamp(System.currentTimeMillis());
        dataPoint.setSupplierCode(CommonConst.DataPoint.supplierCode);
        dataPoint.setPlatformCode(CommonConst.DataPoint.platformCode);
        // 上报数据氛围灯/车外灯光
        ArrayList<Content> list = new ArrayList<>();
        list.add(reportData(CommonConst.CodeId.ZB140F0F, CommonConst.Att.ClsMode, String.valueOf(resorce)));
        list.add(reportData(CommonConst.CodeId.ZB140F0F, CommonConst.Att.ClsTime, String.valueOf(formattedTime)));
        dataPoint.setContent(list);
        LiveEventBus.get(KEY_DATA_POINT).post(dataPoint);
    }

    private void onOpenDataReport() {
        int resorce = 0;
        String eventId = CommonConst.EventId.App_Open;
        String attMode = CommonConst.Att.OpsMode;
        String time = CommonConst.Att.OpsTime;
        if (getIntent().getExtras() != null) {
            int targetTab = getIntent().getIntExtra(CommonConst.TARGET_TAB, -1);
            int operation = getIntent().getIntExtra(CommonConst.OPERATION, -1);
            resorce = 1;
            // 语音
            if (targetTab != -1) {
                if (operation == 0) {
                    Log.d(TAG, "handleIntent : finish");
                    return;
                }
            }
        } else {
            // 点击
            resorce = 2;
        }
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        String formattedTime = now.format(formatter);
        // 日志埋点
        Gson gson = new Gson();
        DataPoint dataPoint = new DataPoint();
        dataPoint.setId(CommonConst.DataPoint.id);
        dataPoint.setEventId(eventId);
        dataPoint.setTimestamp(System.currentTimeMillis());
        dataPoint.setSupplierCode(CommonConst.DataPoint.supplierCode);
        dataPoint.setPlatformCode(CommonConst.DataPoint.platformCode);
        // 上报数据氛围灯/车外灯光
        ArrayList<Content> list = new ArrayList<>();
        list.add(reportData(CommonConst.CodeId.ZB140F0F, attMode, String.valueOf(resorce)));
        list.add(reportData(CommonConst.CodeId.ZB140F0F, time, String.valueOf(formattedTime)));
        dataPoint.setContent(list);
        LiveEventBus
                .get(DataPointReportLifeCycle.KEY_DATA_POINT)
                .post(dataPoint);
    }

    private Content reportData(String attributeId, String locationId, String attributeValue) {
        Content content = new Content();
        content.setAttributeId(attributeId);
        content.setLocationId(locationId);
        content.setAttributeValue(attributeValue);
        return content;
    }


    @Override
    public void onInterceptBackGesture(boolean intercept) {
        this.interceptBackGesture = intercept;
    }

    @Override
    public void onBackPressed() {
        if (interceptBackGesture) {
            // 屏幕清洁的返回被拦截，不执行默认返回操作
            return;
        }
        super.onBackPressed();
    }

    //初始化连接壁纸服务
    public void initWallpaper3DServices() {
        VehicleServiceManager.initializeComplete(this, this, R.id.cl_bg, "settings");
        Log.d(TAG, "AAR 服务初始化完成 - 使用VehicleServiceManager");
    }

    @Override
    protected void onRestart() {
        super.onRestart();
    }

    @Override
    protected void onPause() {
        int preSet = Prefs.getGlobalValue(PrefsConst.GlobalValue.WALLPAPER_STATE_3D, CarWallpaper.Type3D.CAR_3D);// 上一次设置的3D壁纸类型
        if (preSet != 0 && WallpaperPresenter.getWallpaperState() == WallpaperPresenter.WALLPAPER_STATE_3D) {
            WallpaperPresenter.setCurrentSceneWallpaper(preSet);
        }
        if (preSet == CarWallpaper.Type3D.CAR_3D) {
            VehicleServiceManager.startCameraStatusPollingTask(Car3DModel.ControlModelState.CONTROL_MODEL_STATE_ID, Car3DModel.ControlModelState.LAUNCH);
        }

        releaseResources();
        super.onPause();
    }

    private void releaseResources() {
        if (lastPagView != null) {
            lastPagView.stop();
            lastPagView.freeCache();
            lastPagView = null;
        }
    }

    @Override
    protected void onStop() {
        isActivityStarted = false;
        super.onStop();
    }

    @Override
    protected void onStart() {
        super.onStart();
        isActivityStarted = true;
    }

    public static int getCurrentMainTabIndex() {
        return !isActivityStarted ? -1 : currentMainTabIndex;
    }

    @Override
    public void from3DWPSetRequest(int id, Bundle data, IRequestCallback callback) {
        switch (id) {
            case 100: // 车辆设置初始化状态
                from3DWP_initVehicleSettingsRequest(data, callback);
                break;
//            case 901: // 空调开关
//                from3DWP_setACStatusRequest(data, callback);
//                break;
//            case 902: // 空调风量
//                from3DWP_setACFanSpeedRequest(data, callback);
//                break;
//            case 903: // 空调主驾温度
//                from3DWP_setACMainDriverTempRequest(data, callback);
//                break;
//            case 904: // 空调副驾温度
//                from3DWP_setACAssistantDriverTempRequest(data, callback);
//                break;
//            case 905: // 空调主驾风向
//                from3DWP_setACMainDriverDirectionRequest(data, callback);
//                break;
//            case 906: // 空调副驾风向
//                from3DWP_setACAssistantDriverDirectionRequest(data, callback);
//                break;
//            case 907: // 空调香氛开关
//                from3DWP_setACFragranceStatusRequest(data, callback);
//                break;
//            case 908: // 空调上风向开关
//                from3DWP_setACUpWindStatusRequest(data, callback);
//                break;
//            case 909: // 空调下风向开关
//                from3DWP_setACDownWindStatusRequest(data, callback);
//                break;
//            case 910: // 空调玻璃风向开关
//                from3DWP_setACGlassWindStatusRequest(data, callback);
//                break;
//            case 912: // 空调风量类型
//                from3DWP_setACWindTypeRequest(data, callback);
//                break;
            default:
                if (callback != null) {
                    Bundle error = new Bundle();
                    error.putString("error", "不支持的ID: " + id);
                    try {
                        callback.onError(error);
                    } catch (Exception e) {
                        Log.e(TAG, "错误回调调用失败", e);
                    }
                }
                break;
        }
    }

    @Override
    public void from3DWPGetRequest(int id, Bundle data, IRequestCallback callback) {
        switch (id) {
            case 1: // 车辆设置初始化状态
                from3DWP_initVehicleSettingsRequest(data, callback);
                break;
            default:
                if (callback != null) {
                    Bundle error = new Bundle();
                    error.putString("error", "不支持的ID: " + id);
                    try {
                        callback.onError(error);
                    } catch (Exception e) {
                        Log.e(TAG, "错误回调调用失败", e);
                    }
                }
                break;
        }
    }

    private void from3DWP_initVehicleSettingsRequest(Bundle data, IRequestCallback callback) {
        runOnUiThread(() -> {
            try {
                // 调用成功回调
                if (callback != null) {
                    Bundle result = new Bundle();
                    result.putString("status", "success");
                    result.putString("message", "车辆设置初始化完成");
                    try {
                        callback.onSuccess(result);
                    } catch (Exception e) {
                        Log.e(TAG, "回调调用失败", e);
                    }
                }

            } catch (Exception e) {
                Log.e(TAG, "处理车辆设置初始化请求失败", e);
                if (callback != null) {
                    Bundle error = new Bundle();
                    error.putString("error", "处理车辆设置初始化请求失败: " + e.getMessage());
                    try {
                        callback.onError(error);
                    } catch (Exception ex) {
                        Log.e(TAG, "错误回调调用失败", ex);
                    }
                }
            }
        });
    }

    private class ViewPagerFragmentAdapter extends FragmentPagerAdapter {

        public ViewPagerFragmentAdapter(FragmentManager childFragmentManager) {
            super(childFragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        }

        @SuppressLint("UseCompatLoadingForDrawables")
        @NonNull
        @Override
        public Fragment getItem(int position) {
            return getVpFragment(position);
        }

        @Override
        public int getCount() {
            return MainTabIndex.TAB_COUNT;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return null;
        }
    }


    private Fragment getVpFragment(int position) {
        Log.d(TAG, "getVpFragment: " + position);
        return switch (position) {
            case MainTabIndex.QUICK_CONTROL -> {
                if (quickControlFragment == null) {
                    quickControlFragment = new QuickControlFragment();
                }
                yield quickControlFragment;
            }
            case MainTabIndex.LIGHT -> {
                if (lightFragment == null) {
                    lightFragment = new LightBaseFragment();
                }
                yield lightFragment;
            }
            case MainTabIndex.NEW_ENERGY -> {
                if (newEnergyFragment == null) {
                    newEnergyFragment = new NewEnergyFragment();
                }
                yield newEnergyFragment;
            }
            case MainTabIndex.DRIVE -> {
                if (drivingFragment == null) {
                    drivingFragment = new DrivingFragment();
                }
                yield drivingFragment;
            }

            case MainTabIndex.CONDITION -> {
                if (conditionFragment == null) {
                    conditionFragment = new ConditionFragment();
                }
                yield conditionFragment;
            }

            case MainTabIndex.DISPLAY -> {
                if (displayFragment == null) {
                    displayFragment = new DisplayFragment();
                }
                yield displayFragment;
            }
            case MainTabIndex.CONNECT -> {
                if (connectFragment == null) {
                    connectFragment = new ConnectFragment();
                }
                yield connectFragment;
            }
            case MainTabIndex.VOICE -> {
                if (voiceFragment == null) {
                    voiceFragment = new VoiceFragment();
                }
                yield voiceFragment;
            }

//            case MainTabIndex.SMART_RECOGNITION -> {
//                if (recognitionFragment == null) {
//                    recognitionFragment = new RecognitionFragment();
//                }
//                yield recognitionFragment;
//            }
            case MainTabIndex.SYSTEM -> {
                if (systemFragment == null) {
                    systemFragment = new SystemFragment();
                }
                yield systemFragment;
            }
            default -> null;
        };
    }

    private void quickBackPic() {
        binding.ivModel.setVisibility(View.INVISIBLE);
    }

    private void driveBackPic() {
        binding.ivModel.setVisibility(View.INVISIBLE);
    }

    public void lightBackPic() {
        int tab = Prefs.get(PrefsConst.SELECT_TAB, CommonConst.TAB_0);
        int theme = Prefs.get(PrefsConst.SELECT_THEME, CommonConst.TAB_0);
        int front = Prefs.get(PrefsConst.SELECT_FRONT_REAR, CommonConst.TAB_0);
        if (tab == CommonConst.TAB_0) {
            binding.ivModel.setVisibility(View.VISIBLE);
            if (theme == CommonConst.TAB_0) {
                binding.ivModel.setBackgroundResource(R.mipmap.ic_light_lighting_inside);
            } else {
                if (front == CommonConst.TAB_0) {
                    binding.ivModel.setBackgroundResource(R.mipmap.ic_light_lighting_inside);
                } else {
                    binding.ivModel.setBackgroundResource(R.mipmap.ic_light_car_back);
                }
            }
        } else {
            binding.ivModel.setBackgroundResource(R.mipmap.ic_light_lighting_outside);
            binding.ivModel.setVisibility(View.INVISIBLE);
            binding.ivModel.setFadeThreshold((int) getResources().getDimension(com.bitech.base.R.dimen.quick_control_model_height));
            binding.ivModel.handleScroll(Prefs.get(PrefsConst.LIGHT_SCROLL_Y, 0));
        }
    }

    private void commonBackPic() {
        binding.ivModel.setVisibility(View.INVISIBLE);
        binding.ivModel.setBackgroundResource(R.color.main_bg_color);
    }

    @SuppressLint({"NonConstantResourceId", "UseCompatLoadingForDrawables"})
    public void onClick(View view) {
        Log.d(TAG, "onClick: " + view.getId());
        ThreeDModelUtil.set3DWallpaper(Car3DModel.WallpaperState.MODEL, null);
        switch (view.getId()) {
            case R.id.tv_quick_control:
                setCurrentItem(MainTabIndex.QUICK_CONTROL, view);
                if (lastPagView != binding.pvQuickControl) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.QUICK_CONTROL], binding.pvQuickControl);
                VehicleServiceManager.startCameraStatusPollingTask(Car3DModel.CameraStatus.CAMERA_STATUS_ID, Car3DModel.CameraStatus.CONTROL);
                lastPagView = binding.pvQuickControl;
                lastPosition = MainTabIndex.QUICK_CONTROL;
                break;
            case R.id.tv_light:
                setCurrentItem(MainTabIndex.LIGHT, view);
                if (lastPagView != binding.pvLight) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.LIGHT], binding.pvLight);
                VehicleServiceManager.startCameraStatusPollingTask(Car3DModel.CameraStatus.CAMERA_STATUS_ID, Car3DModel.CameraStatus.LIGHT_1);
                lastPagView = binding.pvLight;
                lastPosition = MainTabIndex.LIGHT;
                break;
            case R.id.tv_new_energy:
                setCurrentItem(MainTabIndex.NEW_ENERGY, binding.tvNewEnergy);
                if (lastPagView != binding.pvNewEnergy) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.NEW_ENERGY], binding.pvNewEnergy);
                lastPagView = binding.pvNewEnergy;
                lastPosition = MainTabIndex.NEW_ENERGY;
                break;
            case R.id.tv_drive:
                setCurrentItem(MainTabIndex.DRIVE, binding.tvDrive);
                if (lastPagView != binding.pvDrive) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.DRIVE], binding.pvDrive);
                VehicleServiceManager.startCameraStatusPollingTask(Car3DModel.CameraStatus.CAMERA_STATUS_ID, Car3DModel.CameraStatus.DRIVE);
                lastPagView = binding.pvDrive;
                lastPosition = MainTabIndex.DRIVE;
                break;
            case R.id.tv_condition:
                setCurrentItem(MainTabIndex.CONDITION, binding.tvCondition);
                if (lastPagView != binding.pvCondition) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.CONDITION], binding.pvCondition);
                lastPagView = binding.pvCondition;
                lastPosition = MainTabIndex.CONDITION;
                break;
            case R.id.tv_display:
                setCurrentItem(MainTabIndex.DISPLAY, binding.tvDisplay);
                if (lastPagView != binding.pvDisplay) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.DISPLAY], binding.pvDisplay);
                lastPagView = binding.pvDisplay;
                lastPosition = MainTabIndex.DISPLAY;
//                binding.clBg.setBackground(mainBgColor);
                break;
            case R.id.tv_connect:
                setCurrentItem(MainTabIndex.CONNECT, binding.tvConnect);
                if (lastPagView != binding.pvConnect) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.CONNECT], binding.pvConnect);
                lastPagView = binding.pvConnect;
                lastPosition = MainTabIndex.CONNECT;
//                binding.clBg.setBackground(mainBgColor);
                break;
            case R.id.tv_voice:
                setCurrentItem(MainTabIndex.VOICE, binding.tvVoice);
                if (lastPagView != binding.pvVoice) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.VOICE], binding.pvVoice);
                lastPagView = binding.pvVoice;
                lastPosition = MainTabIndex.VOICE;
//                binding.clBg.setBackground(mainBgColor);
                break;
//            case R.id.tv_smart_recognition:
//                setCurrentItem(MainTabIndex.SMART_RECOGNITION, binding.tvSmartRecognition);
//                if (lastPagView != binding.pvSmartRecognition) {
//                    PagUtils.playPagView(presenter.getPagsOff()[lastPosition], lastPagView);
//                }
//                PagUtils.playPagView(presenter.getPagsOn()[MainTabIndex.SMART_RECOGNITION], binding.pvSmartRecognition);
//                lastPagView = binding.pvSmartRecognition;
//                lastPosition = MainTabIndex.SMART_RECOGNITION;
////                binding.clBg.setBackground(mainBgColor);
//                break;
            case R.id.tv_system:
                setCurrentItem(MainTabIndex.SYSTEM, binding.tvSystem);
                if (lastPagView != binding.pvSystem) {
                    PagUtils.playPagView(mContext, presenter.getPagsOff()[lastPosition], lastPagView);
                }
                PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.SYSTEM], binding.pvSystem);
                lastPagView = binding.pvSystem;
                lastPosition = MainTabIndex.SYSTEM;
//                binding.clBg.setBackground(mainBgColor);
                break;
        }
    }

    private void initPagViewOff() {
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.QUICK_CONTROL], binding.pvQuickControl);
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.LIGHT], binding.pvLight);
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.NEW_ENERGY], binding.pvNewEnergy);
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.DRIVE], binding.pvDrive);
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.CONDITION], binding.pvCondition);
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.DISPLAY], binding.pvDisplay);
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.CONNECT], binding.pvConnect);
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.VOICE], binding.pvVoice);
//        PagUtils.playPagView(presenter.getPagsOff()[MainTabIndex.SMART_RECOGNITION], binding.pvSmartRecognition);
        PagUtils.playPagView(this, presenter.getPagsOff()[MainTabIndex.SYSTEM], binding.pvSystem);
    }

    private void setCurrentItem(int tabIndex, View view) {
        binding.setTabSelectedIndex(tabIndex);
        binding.viewPager.setCurrentItem(tabIndex, false);
        applyViewPropertyAnimator(view);
    }

    private void applyViewPropertyAnimator(View v) {
        AnimatorSet animatorSet = new AnimatorSet();
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(v, "scaleX", 0.85f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(v, "scaleY", 0.85f, 1f);
        scaleX.setRepeatMode(ValueAnimator.REVERSE);
        scaleY.setRepeatMode(ValueAnimator.REVERSE);
        animatorSet.setDuration(500);
        animatorSet.setInterpolator(new DecelerateInterpolator());
        animatorSet.play(scaleX).with(scaleY);
        animatorSet.start();
    }

    @Override
    public void setListener() {
        binding.viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                Fragment vpFragment = getVpFragment(position);
                Log.d(TAG, "onPageSelected: " + position);
                if (vpFragment == null) {
                    return;
                }
                if (vpFragment instanceof QuickControlFragment) {
                    quickBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.transparent));
                    ((QuickControlFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof LightBaseFragment) {
                    lightBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.transparent));
                    ((LightBaseFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof DrivingFragment) {
                    driveBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.transparent));
                    ((DrivingFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof CarSettingFragment) {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                    ((CarSettingFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof ConditionFragment) {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                    ((ConditionFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof NewEquipmentFragment) {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                    ((NewEquipmentFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof DisplayFragment) {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                    ((DisplayFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof ConnectFragment) {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                    ((ConnectFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof VoiceFragment) {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                    ((VoiceFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof RecognitionFragment) {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                    ((RecognitionFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else if (vpFragment instanceof SystemFragment) {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                    ((SystemFragment) vpFragment).loadPageAnim(currentMainTabIndex, position);
                } else {
                    commonBackPic();
                    binding.clBg.setBackground(getDrawable(R.color.main_bg_color));
                }
                currentMainTabIndex = position;
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    public Bundle getBundle() {
        return mBundle;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        onOpenDataReport();
        handleIntent(intent);
    }

    public void handleIntent(Intent intent) {
        int targetTab = intent.getIntExtra(CommonConst.TARGET_TAB, -1);
        int targetDialog = intent.getIntExtra(CommonConst.TARGET_DIALOG, -1);
        int operation = intent.getIntExtra(CommonConst.OPERATION, -1);
        Log.d(TAG, "targetDialog: " + targetDialog + " targetTab: " + targetTab + " operation: " + operation);

        if (targetTab != -1) {
            presenter.menuHandleIntent(binding, targetTab, targetDialog, operation);
            mainActViewModel.getTargetDialogLiveEvent().setValue(new TargetDialogInfo(targetTab, targetDialog, operation));
            if (operation == 0) {
                Log.d(TAG, "handleIntent : finish");
                finish();
            }
        }
        if (targetDialog >= 0 && targetDialog < 10) {
            FragmentManager fragmentManager = getSupportFragmentManager();
            ConnectControl connectControl = new ConnectControl();
            switch (targetDialog) {
                case 0:
                    // 检查是否已有BtDialogFragment实例正在显示
                    Fragment existingBtFragment = fragmentManager.findFragmentByTag("BluetoothDialogFragment");
                    if (existingBtFragment == null || !existingBtFragment.isVisible()) {
                        BtFragment btDialogFragment = new BtFragment();
                        btDialogFragment.show(fragmentManager, "BluetoothDialogFragment");
                        initPagViewOff();
                        PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.CONNECT], binding.pvConnect);
                        binding.setTabSelectedIndex(MainTabIndex.CONNECT);
                        binding.viewPager.setCurrentItem(MainTabIndex.CONNECT, false);
                        commonBackPic();
                        if (operation == 0) {
                            connectControl.sendResultCode("delete_matched_bluetooth_device_1");
                        } else if (operation == 1) {
                            connectControl.sendResultCode("connect_bluetooth_1");
                        }
                    } else {
                        if (operation == 0) {
                            connectControl.sendResultCode("delete_matched_bluetooth_device_2");
                        } else if (operation == 1) {
                            connectControl.sendResultCode("connect_bluetooth_2");
                        }
                    }
                    break;
                case 1:
                    Fragment existingWifiFragment = fragmentManager.findFragmentByTag("WifiDialogFragment");
                    if (existingWifiFragment == null || !existingWifiFragment.isVisible()) {
                        WifiFragment dialogFragment = new WifiFragment();
                        dialogFragment.show(fragmentManager, "WifiDialogFragment");
                        initPagViewOff();
                        PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.CONNECT], binding.pvConnect);
                        binding.setTabSelectedIndex(MainTabIndex.CONNECT);
                        binding.viewPager.setCurrentItem(MainTabIndex.CONNECT, false);
                        commonBackPic();
                    }
                    break;
                case 2:
                    //检查是否已有HotspotDialogFragment实例正在显示
                    Fragment existingHotspotFragment = fragmentManager.findFragmentByTag("HotspotDialogFragment");
                    if (existingHotspotFragment == null || !existingHotspotFragment.isVisible()) {
                        HotpotFragment dialogFragment = new HotpotFragment();
                        dialogFragment.show(fragmentManager, "HotspotDialogFragment");
                        initPagViewOff();
                        PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.CONNECT], binding.pvConnect);
                        binding.setTabSelectedIndex(MainTabIndex.CONNECT);
                        binding.viewPager.setCurrentItem(MainTabIndex.CONNECT, false);
                        commonBackPic();
                        if (operation == 0) {
                            connectControl.sendResultCode("Open_the_hotspot_list_1");
                        } else if (operation == 1) {
                            connectControl.sendResultCode("set_hotspot_password_1");
                        }
                    } else {
                        if (operation == 0) {
                            connectControl.sendResultCode("Open_the_hotspot_list_2");
                        } else if (operation == 1) {
                            //界面已打开
                            connectControl.sendResultCode("set_hotspot_password_2");
                        }
                    }
                    break;
                case 3:
                    //检查是否已有设备名称弹窗正在显示
                    if (deviceInfoUIAlert != null && deviceInfoUIAlert.isShowing()) {
                        return;
                    }
                    // 如果对话框不存在，则创建并显示
                    if (deviceInfoUIAlert == null) {
                        // 从偏好设置获取设备名称（Java 中需处理默认值逻辑）
                        String deviceName = Prefs.get(
                                PrefsConst.SYSTEM_DEVICE_INFO_NAME,
                                PrefsConst.DefaultValue.SYSTEM_DEVICE_INFO_NAME
                        );                    // 获取蓝牙适配器并更新设备名称
                        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                        Log.d("getDeviceName", "getDeviceInfo: " + deviceName);
                        if (bluetoothAdapter != null) {
                            deviceName = bluetoothAdapter.getName();
                        }
                        // 构建设备信息对话框
                        deviceInfoUIAlert = new DeviceInfoUIAlert.Builder(mContext, deviceName);
                        // 创建并显示对话框
                        deviceInfoUIAlert.create().show();
                        initPagViewOff();
                        PagUtils.playPagView(mContext, presenter.getPagsOn()[MainTabIndex.SYSTEM], binding.pvSystem);
                        binding.setTabSelectedIndex(MainTabIndex.SYSTEM);
                        binding.viewPager.setCurrentItem(MainTabIndex.SYSTEM, false);
                        commonBackPic();
                    }
                    break;
            }
        }

        // 在Activity中处理完数据后调用
        intent.removeExtra("target_tab");
        intent.removeExtra("target_dialog");
        intent.removeExtra("operation");
    }

    @Override
    protected void onDestroy() {
        // 外部类Activity生命周期结束时，同时清空消息队列 & 结束Handler生命周期
        // 停止轮询任务
        VehicleServiceManager.stopCameraStatusPollingTask();
        // 使用VehicleServiceManager进行清理
        VehicleServiceManager.cleanup();
        onCloseDataReport();
        super.onDestroy();
        releaseResources();
        // TTS播报销毁
        TtsHelper.getInstance().release();
        quickControlFragment = null;

        lightFragment = null;
        newEnergyFragment = null;
        drivingFragment = null;
        newEquipmentFragment = null;
        displayFragment = null;
        connectFragment = null;
        voiceFragment = null;
        systemFragment = null;
        conditionFragment = null;
    }

    public static class MainTabIndex {
        public static final int QUICK_CONTROL = 0;
        public static final int LIGHT = 1;
        public static final int NEW_ENERGY = 2;
        public static final int DRIVE = 3;
        public static final int CONDITION = 4;
        public static final int DISPLAY = 5;
        public static final int CONNECT = 6;
        public static final int VOICE = 7;
        //        public static final int SMART_RECOGNITION = 8;
        public static final int SYSTEM = 8;
        // tab总数量
        public static final int TAB_COUNT = 9;
    }

}