package com.bitech.vehiclesettings.widget;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.util.Log;
import android.widget.RemoteViews;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.carapi.constants.CarCondition;
import com.bitech.vehiclesettings.service.ConditionLifeCycle;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;

public class TirePressureWidget extends AppWidgetProvider {
    public static final String TAG = "TirePressureWidget";
    public static final String ACTION_TIRE_PRESSURE_UPDATE = "com.bitech.vehiclesettings.widget.TIRE_PRESSURE_UPDATE";

    private static SharedPreferences.OnSharedPreferenceChangeListener prefListener;

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        Log.d(TAG, "onUpdate");
        for (int appWidgetId : appWidgetIds) {
            updateWidget(context, appWidgetManager, appWidgetId);
        }
        registerPrefListener(context);
    }

    @Override
    public void onEnabled(Context context) {
        super.onEnabled(context);
        Log.d(TAG, "onEnabled");
        registerPrefListener(context);
    }

    @Override
    public void onDisabled(Context context) {
        super.onDisabled(context);
        Log.d(TAG, "onDisabled");
        unregisterPrefListener(context);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        super.onReceive(context, intent);
        if (ACTION_TIRE_PRESSURE_UPDATE.equals(intent.getAction())) {
            Log.d(TAG, "Received update request");
            refreshAllWidgets(context);
        }
    }

    private void registerPrefListener(Context context) {
        if (prefListener == null) {
            prefListener = (sharedPreferences, key) -> {
                if (PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT.equals(key)) {
                    Log.d(TAG, "Pressure unit changed");
                    refreshAllWidgets(context);
                }
            };
            Prefs.registerListener(prefListener);
        }
    }

    private void unregisterPrefListener(Context context) {
        if (prefListener != null) {
            Prefs.unregisterListener(prefListener);
            prefListener = null;
        }
    }

    private void refreshAllWidgets(Context context) {
        AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
        int[] appWidgetIds = appWidgetManager.getAppWidgetIds(
                new ComponentName(context, TirePressureWidget.class));

        for (int appWidgetId : appWidgetIds) {
            updateWidget(context, appWidgetManager, appWidgetId);
        }
    }

    private void updateWidget(Context context, AppWidgetManager appWidgetManager, int appWidgetId) {
        RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.widget_tire_pressure);

        Log.d(TAG, "updateWidget: 更新Widget TirePressure");

        // 更新四个轮胎的压力显示
        if (ConditionLifeCycle.leftFrontTyrePressureStatus.get() != CarCondition.TirePositionWarning_LHFTire.ERROR_MODE) {
            updateTirePressureView(views, R.id.tireLeftFront, ConditionLifeCycle.leftFrontTyrePressure.get());
        } else {
            views.setTextViewText(R.id.tireLeftFront, "---");
        }
        if (ConditionLifeCycle.rightFrontTyrePressureStatus.get() != CarCondition.TirePositionWarning_RHFTire.ERROR_MODE) {
            updateTirePressureView(views, R.id.tireRightFront, ConditionLifeCycle.rightFrontTyrePressure.get());
        } else {
            views.setTextViewText(R.id.tireRightFront, "---");
        }
        if (ConditionLifeCycle.leftRearTyrePressureStatus.get() != CarCondition.TirePositionWarning_LHRTire.ERROR_MODE) {
            updateTirePressureView(views, R.id.tireLeftRear, ConditionLifeCycle.leftRearTyrePressure.get());
        } else {
            views.setTextViewText(R.id.tireLeftRear, "---");
        }
        if (ConditionLifeCycle.rightRearTyrePressureStatus.get() != CarCondition.TirePositionWarning_RHRTire.ERROR_MODE) {
            updateTirePressureView(views, R.id.tireRightRear, ConditionLifeCycle.rightRearTyrePressure.get());
        } else {
            views.setTextViewText(R.id.tireRightRear, "---");
        }

        // 更新单位显示
        updateUnitDisplay(views);

        // 更新轮胎状态
        updateTireStatus(views);

        // 点击跳转到车辆控制页面
        Intent clickIntent = new Intent(context, MainActivity.class);
        clickIntent.putExtra(CommonConst.TARGET_TAB, MainActivity.MainTabIndex.CONDITION);
        clickIntent.putExtra(CommonConst.TARGET_DIALOG, CommonConst.INVALID_DIALOG);
        clickIntent.putExtra(CommonConst.OPERATION, CommonConst.DIALOG_OPEN);
        clickIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                context,
                appWidgetId,
                clickIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        views.setOnClickPendingIntent(R.id.widget_tire_pressure, pendingIntent);

        appWidgetManager.updateAppWidget(appWidgetId, views);
    }

    private void updateTireStatus(RemoteViews views) {
        int leftFrontStatus = ConditionLifeCycle.leftFrontTyrePressureStatus.get();
        int rightFrontStatus = ConditionLifeCycle.rightFrontTyrePressureStatus.get();
        int leftRearStatus = ConditionLifeCycle.leftRearTyrePressureStatus.get();
        int rightRearStatus = ConditionLifeCycle.rightRearTyrePressureStatus.get();
        if (leftFrontStatus == CarCondition.TirePositionWarning_LHFTire.NORMAL) {
            views.setImageViewResource(R.id.tireLeftFrontImg, R.drawable.img_widget_tirepressure_car_tire_green_2);
        } else {
            views.setImageViewResource(R.id.tireLeftFrontImg, R.drawable.img_widget_tirepressure_car_tire_red_2);
        }

        if (rightFrontStatus == CarCondition.TirePositionWarning_RHFTire.NORMAL) {
            views.setImageViewResource(R.id.tireRightFrontImg, R.drawable.img_widget_tirepressure_car_tire_green);
        } else {
            views.setImageViewResource(R.id.tireRightFrontImg, R.drawable.img_widget_tirepressure_car_tire_red);
        }

        if (leftRearStatus == CarCondition.TirePositionWarning_LHRTire.NORMAL) {
            views.setImageViewResource(R.id.tireLeftRearImg, R.drawable.img_widget_tirepressure_car_tire_green_2);
        } else {
            views.setImageViewResource(R.id.tireLeftRearImg, R.drawable.img_widget_tirepressure_car_tire_red_2);
        }
        if (rightRearStatus == CarCondition.TirePositionWarning_RHRTire.NORMAL) {
            views.setImageViewResource(R.id.tireRightRearImg, R.drawable.img_widget_tirepressure_car_tire_green);
        } else {
            views.setImageViewResource(R.id.tireRightRearImg, R.drawable.img_widget_tirepressure_car_tire_red);
        }
    }

    private void updateTirePressureView(RemoteViews views, int viewId, int pressure) {
        String formattedPressure = formatPressure(pressure);
        views.setTextViewText(viewId, formattedPressure);
    }

    private String formatPressure(int pressure) {
        if (pressure == Integer.MIN_VALUE) {
            return "---";
        }

        int unit = Prefs.get(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, 1);
        float convertedValue;

        switch (unit) {
            case 0: // kPa
                convertedValue = pressure * 1.373f;
                return String.format("%.0f", convertedValue);
            case 1: // psi
                convertedValue = pressure * 1.373f * 0.1450337f;
                return String.format("%.0f", convertedValue);
            case 2: // bar
                convertedValue = pressure * 1.373f / 100f;
                return String.format("%.1f", convertedValue);
            default:
                convertedValue = pressure * 1.373f;
                return String.format("%.0f", convertedValue);
        }
    }

    private void updateUnitDisplay(RemoteViews views) {
        int unit = Prefs.get(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, 1);
        String unitText;
        switch (unit) {
            case 1:
                unitText = "psi";
                break;
            case 2:
                unitText = "bar";
                break;
            case 0:
            default:
                unitText = "kPa";
        }

        // 更新所有单位显示视图
        views.setTextViewText(R.id.tireLeftFrontUnit, unitText);
        views.setTextViewText(R.id.tireRightFrontUnit, unitText);
        views.setTextViewText(R.id.tireLeftRearUnit, unitText);
        views.setTextViewText(R.id.tireRightRearUnit, unitText);
    }
}