package com.bitech.vehiclesettings.utils;

import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;


import java.text.SimpleDateFormat;
import java.util.Calendar;

public class PFLog {

    private static long mLogTime = 0L;
    protected static boolean mLoggingEnabled = true;

    public static boolean isDebugLogging() {
        return mLoggingEnabled;
    }

    public static void resetTime() {
        mLogTime = SystemClock.elapsedRealtime();
    }

    public static void setDebugLogging(boolean paramBoolean) {
        mLoggingEnabled = paramBoolean;
    }

    public static int v(String paramString1, String paramString2) {
        return mLoggingEnabled ? Log.v(paramString1, paramString2 + getFileLocation()) : 0;
    }

    public static int v(String paramString1, String paramString2, Throwable paramThrowable) {
        return mLoggingEnabled ? Log.v(paramString1, paramString2 + getFileLocation(),
                paramThrowable) : 0;
    }

    public static int d(String paramString1, String paramString2) {
        return mLoggingEnabled ? Log.d(paramString1, paramString2 + getFileLocation()) : 0;
    }

    public static int d(String paramString1, String paramString2, Throwable paramThrowable) {
        return mLoggingEnabled ? Log.d(paramString1, paramString2 + getFileLocation(),
                paramThrowable) : 0;
    }

    public static int i(String paramString1, String paramString2) {
        return mLoggingEnabled ? Log.i(paramString1, paramString2 + getFileLocation()) : 0;
    }

    public static int i(String paramString1, String paramString2, Throwable paramThrowable) {
        return mLoggingEnabled ? Log.i(paramString1, paramString2 + getFileLocation(),
                paramThrowable) : 0;
    }

    public static int w(String paramString1, String paramString2) {
        return mLoggingEnabled ? Log.w(paramString1, paramString2 + getFileLocation()) : 0;
    }

    public static int w(String paramString1, String paramString2, Throwable paramThrowable) {
        return mLoggingEnabled ? Log.w(paramString1, paramString2 + getFileLocation(),
                paramThrowable) : 0;
    }

    public static int w(String paramString, Throwable paramThrowable) {
        return mLoggingEnabled ? Log.w(paramString, paramThrowable) : 0;
    }

    public static int e(String paramString1, String paramString2) {
        return mLoggingEnabled ? Log.e(paramString1, paramString2 + getFileLocation()) : 0;
    }

    public static int e(String paramString1, String paramString2, Throwable paramThrowable) {
        return mLoggingEnabled ? Log.e(paramString1, paramString2 + getFileLocation(),
                paramThrowable) : 0;
    }

    private static String getDate() {
        Calendar localCalendar = Calendar.getInstance();
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(localCalendar
                .getTime());
    }

    private static String addTimeMsg(String paramString) {
        return paramString + " T:" + (SystemClock.elapsedRealtime() - mLogTime);
    }

    private static String getFileLocation() {

        int index = 4;
        StringBuffer stringBuffer = new StringBuffer();

        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();

            String className = stackTrace[index].getFileName();
            String methodName = stackTrace[index].getMethodName();
            int lineNumber = stackTrace[index].getLineNumber();
            methodName = methodName.substring(0, 1).toUpperCase() + methodName.substring(1);
            stringBuffer.append(" [(").append(className).append(":").append(lineNumber).append(")" +
                    "#").append(methodName).append("] ");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return stringBuffer.toString();
    }
}
