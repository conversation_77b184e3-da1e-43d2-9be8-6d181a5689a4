package com.bitech.vehiclesettings.service;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.intelligentdriving.IIntelligentDrivingListener;
import com.bitech.platformlib.interfaces.warning.IWarnManagerListener;
import com.bitech.platformlib.manager.IntelligentDrivingManager;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;


public class LightInLifeCycle implements LifecycleEventObserver {
    private static final String TAG = LightInLifeCycle.class.getName();
    private final VehicleServiceHandler handler;
    private final LifecycleOwner lifecycleOwner;
    private IntelligentDrivingManager intelligentDrivingManager;
    private LightManager lightManager;

    LightInLifeCycle(VehicleServiceHandler handler, LifecycleOwner lifecycleOwner) {
        this.handler = handler;
        this.lifecycleOwner = lifecycleOwner;
    }

    private IIntelligentDrivingListener msgCallback = new IIntelligentDrivingListener() {
        @Override
        public void onNtfLSSInfoDrvg2(int lksModValue, int lksStsValue, int elkStsValue, int lksLeftTrackingStValue, int lksRightTrackingStValue) {
            // ADAS_enum_LKSRightTrackingSt 0x3： 预警
            // ADAS_enum_LKSLeftTrackingSt 0x3： 预警

        }

        @Override
        public void onNtfActiveSafetyFcnInfoAct2(int fcwStsValue, int fcwWarnDistValue, int fcwAcitveStValue, int aebStsValue, int fctaWarnTypeSts) {
            // （ADAS_enum_FcwAcitveSt=0x2/0x3 触发）

        }
    };

    private IWarnManagerListener warningMsgCallBack = new IWarnManagerListener() {
        /**
         * @param success
         */
        @Override
        public void onConfigChanged(boolean success) {
            IWarnManagerListener.super.onConfigChanged(success);
        }

        /**
         * @param status
         */
        @Override
        public void getLHFdoorStsCallback(int status) {
            IWarnManagerListener.super.getLHFdoorStsCallback(status);
        }

        /**
         * @param status
         */
        @Override
        public void getRHFDoorStsCallback(int status) {
            IWarnManagerListener.super.getRHFDoorStsCallback(status);
        }

        /**
         * @param status
         */
        @Override
        public void getLHRdoorStsCallback(int status) {
            IWarnManagerListener.super.getLHRdoorStsCallback(status);
        }

        /**
         * @param status
         */
        @Override
        public void getRHRDoorStsCallback(int status) {
            IWarnManagerListener.super.getRHRDoorStsCallback(status);
        }

        /**
         * @param status
         */
        @Override
        public void getAuotLampCallback(int status) {
            IWarnManagerListener.super.getAuotLampCallback(status);
        }

        /**
         * @param status
         */
        @Override
        public void getCsa2TurnSiglvrCmd(int status) {
            IWarnManagerListener.super.getCsa2TurnSiglvrCmd(status);
        }

        /**
         * @param status
         */
        @Override
        public void getPdcFrontWarning(int status) {
            IWarnManagerListener.super.getPdcFrontWarning(status);
        }

        /**
         * @param status
         */
        @Override
        public void getPdcRearWarning(int status) {
            IWarnManagerListener.super.getPdcRearWarning(status);
        }

        /**
         * @param status
         */
        @Override
        public void getPdcLeftWarning(int status) {
            IWarnManagerListener.super.getPdcLeftWarning(status);
        }

        /**
         * @param status
         */
        @Override
        public void getPdcRightWarning(int status) {
            IWarnManagerListener.super.getPdcRightWarning(status);
        }
    };

    @Override
    public void onStateChanged(@NonNull LifecycleOwner lifecycleOwner, @NonNull Lifecycle.Event event) {
        switch (event) {
            case ON_CREATE:
                Log.d(TAG, "ON_CREATE event triggered" + lifecycleOwner);
                // 在组件创建时执行的逻辑
                intelligentDrivingManager = (IntelligentDrivingManager) BitechCar.getInstance().getServiceManager(BitechCar.INTELLIGENT_DRIVING_MANAGER);
                lightManager = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
                if (intelligentDrivingManager != null) {
                    intelligentDrivingManager.addCallback(TAG, msgCallback);
                }
                break;
            case ON_DESTROY:

                lifecycleOwner.getLifecycle().removeObserver(this);
                // 在组件销毁时执行的逻辑
                Log.d(TAG, "onDestroy: " + lifecycleOwner);
                if (intelligentDrivingManager != null) {
                    intelligentDrivingManager.removeCallback(TAG);
                }
                break;

            default:
                break;
        }
    }


}
