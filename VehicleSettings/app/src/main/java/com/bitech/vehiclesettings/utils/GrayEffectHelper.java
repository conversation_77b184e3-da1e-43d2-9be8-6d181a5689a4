package com.bitech.vehiclesettings.utils;

import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.view.View;
import android.view.ViewGroup;

import com.bitech.vehiclesettings.R;

import java.util.List;

/**
 * 灰度和交互控制工具类：
 * 可分别设置 View 的视觉灰度效果（颜色饱和度/透明度）和是否可交互（启用/禁用）。
 * 支持通过排除ID列表或排除View列表两种方式进行批量处理。
 */
public class GrayEffectHelper {

    private static final float DEFAULT_ALPHA = 0.75f;

    /** ------------------- 1. 灰度处理（视觉效果） ------------------- */

    public static void setGrayScale(View view) {
        setGrayScale(view, DEFAULT_ALPHA);
    }

    public static void setGrayScale(View view, float alphaScale) {
        if (view.getTag(R.id.original_paint) == null) {
            view.setTag(R.id.original_paint, view.getLayerType());
        }

        ColorMatrix matrix = new ColorMatrix();
        matrix.setSaturation(0); // 灰度
        matrix.setScale(1.0f, 1.0f, 1.0f, alphaScale); // 降低透明度

        Paint paint = new Paint();
        paint.setColorFilter(new ColorMatrixColorFilter(matrix));
        view.setLayerType(View.LAYER_TYPE_HARDWARE, paint);
    }

    public static void clearGrayScale(View view) {
        Object original = view.getTag(R.id.original_paint);
        if (original instanceof Integer) {
            view.setLayerType((Integer) original, null);
        }
        view.setTag(R.id.original_paint, null);
    }

    /** ------------------- 2. 可点击状态控制（逻辑交互） ------------------- */

    public static void setEnable(View view, boolean enabled) {
        view.setEnabled(enabled);
    }

    /** ------------------- 3. 批量处理（支持排除部分 ID） ------------------- */

    public static void setGrayScaleExcludeIds(ViewGroup group, float alphaScale, List<Integer> excludeIds) {
        for (int i = 0; i < group.getChildCount(); i++) {
            View child = group.getChildAt(i);
            if (excludeIds != null && excludeIds.contains(child.getId())) continue;

            if (child instanceof ViewGroup) {
                setGrayScaleExcludeIds((ViewGroup) child, alphaScale, excludeIds);
            } else {
                setGrayScale(child, alphaScale);
            }
        }
        if (excludeIds == null || !excludeIds.contains(group.getId())) {
            setGrayScale(group, alphaScale);
        }
    }

    public static void clearGrayScaleExcludeIds(ViewGroup group, List<Integer> excludeIds) {
        for (int i = 0; i < group.getChildCount(); i++) {
            View child = group.getChildAt(i);
            if (excludeIds != null && excludeIds.contains(child.getId())) continue;

            if (child instanceof ViewGroup) {
                clearGrayScaleExcludeIds((ViewGroup) child, excludeIds);
            } else {
                clearGrayScale(child);
            }
        }
        if (excludeIds == null || !excludeIds.contains(group.getId())) {
            clearGrayScale(group);
        }
    }

    public static void setEnableExcludeIds(ViewGroup group, boolean enabled, List<Integer> excludeIds) {
        for (int i = 0; i < group.getChildCount(); i++) {
            View child = group.getChildAt(i);
            if (excludeIds != null && excludeIds.contains(child.getId())) continue;

            if (child instanceof ViewGroup) {
                setEnableExcludeIds((ViewGroup) child, enabled, excludeIds);
            } else {
                setEnable(child, enabled);
            }
        }
        if (excludeIds == null || !excludeIds.contains(group.getId())) {
            setEnable(group, enabled);
        }
    }

    /** ------------------- 4. 批量处理（支持排除部分 View 对象） ------------------- */

    public static void setGrayScaleExcludeViews(ViewGroup group, float alphaScale, List<View> excludeViews) {
        for (int i = 0; i < group.getChildCount(); i++) {
            View child = group.getChildAt(i);
            if (excludeViews != null && excludeViews.contains(child)) continue;

            if (child instanceof ViewGroup) {
                setGrayScaleExcludeViews((ViewGroup) child, alphaScale, excludeViews);
            } else {
                setGrayScale(child, alphaScale);
            }
        }
        if (excludeViews == null || !excludeViews.contains(group)) {
            setGrayScale(group, alphaScale);
        }
    }

    public static void clearGrayScaleExcludeViews(ViewGroup group, List<View> excludeViews) {
        for (int i = 0; i < group.getChildCount(); i++) {
            View child = group.getChildAt(i);
            if (excludeViews != null && excludeViews.contains(child)) continue;

            if (child instanceof ViewGroup) {
                clearGrayScaleExcludeViews((ViewGroup) child, excludeViews);
            } else {
                clearGrayScale(child);
            }
        }
        if (excludeViews == null || !excludeViews.contains(group)) {
            clearGrayScale(group);
        }
    }

    public static void setEnableExcludeViews(ViewGroup group, boolean enabled, List<View> excludeViews) {
        for (int i = 0; i < group.getChildCount(); i++) {
            View child = group.getChildAt(i);
            if (excludeViews != null && excludeViews.contains(child)) continue;

            if (child instanceof ViewGroup) {
                setEnableExcludeViews((ViewGroup) child, enabled, excludeViews);
            } else {
                setEnable(child, enabled);
            }
        }
        if (excludeViews == null || !excludeViews.contains(group)) {
            setEnable(group, enabled);
        }
    }
}
