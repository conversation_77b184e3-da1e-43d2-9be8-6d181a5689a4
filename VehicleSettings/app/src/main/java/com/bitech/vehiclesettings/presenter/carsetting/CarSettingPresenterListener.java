package com.bitech.vehiclesettings.presenter.carsetting;

import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.TextView;

public interface CarSettingPresenterListener {
    void setWiperSens(int status);

    int getWiperSens();

    void setHudRoate(int status);

    int getHudRoate();

    void updateHudRoateUI(Integer vstate, SeekBar seekBar, TextView textView);

    void setLockAutoRaiseWindow(int status);
    int getLockAutoRaiseWindow();

    void setDefenseReminder(int status);
    int getDefenseReminder();

    void setLeftChildLock(int status);
    int getLeftChildLock();

    void setRightChildLock(int status);
    int getRightChildLock();

    void setAutomaticLocking(int status);
    int getAutomaticLocking();

    void setAutomaticParkingUnlock(int status);
    int getAutomaticParkingUnlock();

    void setWiperRepairMode(int status);
    int getWiperRepairMode();

    void setOverSpeed(int status);
    int getOverSpeed();

    void setFatigueDrivingReminder(int status);
    int getFatigueDrivingReminder();

    void setMaintainTips(int status);
    int getMaintainTips();

    void maintainReset();
    int getMaintainKm();
    void setMaintainKm(int km);
    int getMaintainDays();
    void setMaintainDays(int day);

    void setDriveAirBag(int status);
    int getDriveAirBag();

    int getCarSpeed();
    void setCarSpeed(int v);

    void setOverSpeedSeekbar(int status);
    int getOverSpeedSeekbar();

    void setFatigueDriveSeekbar(int status);
    int getFatigueDriveSeekbar();
}
