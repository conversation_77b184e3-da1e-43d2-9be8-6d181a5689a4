package com.bitech.vehiclesettings.view.carsetting;

import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertCChildLockBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertCMaintainResetBinding;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class MaintainResetUIAlert extends BaseDialog {
    private static final String TAG = MaintainResetUIAlert.class.getSimpleName();
    private static MaintainResetUIAlert.onProgressChangedListener onProgressChangedListener;

    public MaintainResetUIAlert(@NonNull Context context) {
        super(context);
    }

    public MaintainResetUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected MaintainResetUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static MaintainResetUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(MaintainResetUIAlert.onProgressChangedListener onProgressChangedListener) {
        MaintainResetUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertCMaintainResetBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private MaintainResetUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public MaintainResetUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public MaintainResetUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new MaintainResetUIAlert(context,
                        R.style.Dialog);
            binding = DialogAlertCMaintainResetBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            layoutParams.height = 508;
            window.setAttributes(layoutParams);

            // 设置确定按钮效果
            clickConfirmMaintainReset();
            // 设置取消按钮效果
            clickCancelMaintainReset();

            return dialog;
        }

        private void clickConfirmMaintainReset() {
            binding.tvMaintainConfirm.setOnClickListener(v -> {
                if (onProgressChangedListener != null) {
                    onProgressChangedListener.onConfirmMaintainReset();
                }
                dialog.dismiss();
            });
        }

        private void clickCancelMaintainReset() {
            binding.tvMaintainCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onConfirmMaintainReset();
    }
}