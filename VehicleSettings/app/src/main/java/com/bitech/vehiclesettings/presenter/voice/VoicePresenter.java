package com.bitech.vehiclesettings.presenter.voice;

import android.car.Car;
import android.car.media.CarAudioManager;
import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;

import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.platformlib.manager.VoiceManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.GlobalVar;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.service.DrivingLifeCycle;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.base.VDKey;
import com.chery.ivi.vdb.event.id.dsp.VDEventDsp;
import com.chery.ivi.vdb.event.id.dsp.VDValueDsp;

import java.io.IOException;


public class VoicePresenter {
    private static final String TAG = VoicePresenter.class.getSimpleName() + "wzh2whu";
    AudioManager audioManager;
    private Context mContext;
    NewEnergyManager newEnergyManager = NewEnergyManager.getInstance();
    VoiceManager voiceManager = VoiceManager.getInstance();
    public static final int MEDIA_MAX_VAL = 31;
    public CarAudioManager carAudioManager;
    public int groupMedia;
    public int groupPhone;
    public int groupNavi;
    public int groupVR;
    private MediaPlayer mediaPlayer;
    private static volatile VoicePresenter instance;

    public static VoicePresenter getInstance() {
        if (instance == null) {
            synchronized (VoicePresenter.class) {
                if (instance == null) {
                    instance = new VoicePresenter();
                }
            }
        }
        return instance;
    }

    public static class VoiceConstant {

        public static final int POSITION_X = 0;
        public static final int POSITION_Y = 0;

        // 头枕扬声器
        public static final int HEADREST_GX = 0;
        public static final int HEADREST_JX = 1;
        public static final int HEADREST_SX = 2;

        public static int getHeadRest(int headRest) {
            switch (headRest) {
                case CarVoice.HeadRest.SHARE:
                    return R.string.str_headrest_gx;
                case CarVoice.HeadRest.DRIVING:
                    return R.string.str_headrest_jx;
                case CarVoice.HeadRest.PRIVATE:
                    return R.string.str_headrest_sx;
            }
            return R.string.str_headrest_gx;
        }
    }

    public VoicePresenter() {
        this.mContext = MyApplication.getContext();
        this.audioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
        Car.createCar(mContext,null,10*1000L, (car, ready) -> {
            if (ready) {
                // CarService 连接成功，重新初始化 manager
                Log.d(TAG, "CarService connected. Initializing managers...");
                carAudioManager = (CarAudioManager) car.getCarManager(Car.AUDIO_SERVICE);
                groupMedia = carAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA);
                groupPhone = carAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION);
                groupNavi = carAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE);
                groupVR = carAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT);
            } else {
                // CarService 断开连接，置空 manager
                Log.d(TAG, "CarService disconnected. Clearing managers...");
                carAudioManager = null;
                groupMedia = -1;
                groupPhone = -1;
                groupNavi = -1;
                groupVR = -1;
            }
        });
    }

    public void setEQ(int index) {
//        setEQ2SOA(index);
        setEQSignal(index);
        Prefs.put(PrefsConst.VOICE_EQ, index);
    }

    public void setEQSignal(int index) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setEQ(index);
            Log.d(TAG, "setEQ: 声场设置:" + index);
        } else {
            Log.d(TAG, "setEQ: 非支持的电源模式：" + index);
        }
    }

    public static void setEQ2SOA(int index) {
        Bundle payload = new Bundle();
        payload.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_ADVANCED_SOUND);
        payload.putInt(VDKey.VALUE, index);
        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, payload);
        VDBus.getDefault().set(event);
        Log.d(TAG, "setEQ2SOA: 声场设置:" + index);
    }

    public int getEQ() {
//        int eq = getEQ2SOA();
        int eq = getEQSignal();
        Prefs.put(PrefsConst.VOICE_EQ, eq);
        Log.d(TAG, "getEQ: 声场设置:" + eq);
        return eq;
    }

    public int getEQSignal() {
        int eq = voiceManager.getEQ();
        eq = CarVoice.EQ.signalReverse(eq);
        if (eq == CarVoice.EQ.INVALID) {
            Log.d(TAG, "getEQ: 声场设置：信号未发送，返回默认值" + CarVoice.EQ.DEFAULT);
            return CarVoice.EQ.DEFAULT;
        }
        Log.d(TAG, "getEQ: 声场设置:" + eq);
        return eq;
    }

    public static int getEQ2SOA() {
        Bundle bundle = new Bundle();
        bundle.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_ADVANCED_SOUND);
        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, bundle);
        VDEvent Gevent = VDBus.getDefault().getOnce(event);
        if (event != null && Gevent != null) {
            int eqType = Gevent.getPayload().getInt(VDKey.VALUE);
            Log.d(TAG, "getEQ2SOA: 声场设置:" + eqType);
            return eqType;
        }
        Log.d(TAG, "getEQ2SOA: 声场设置: 无VDBUS服务");
        return CarVoice.EQ.DEFAULT;
    }

    public void setSurroundSound(int status) {
//        setSurroundSound2SOA(status);
        setSurroundSoundSignal(status);
        Prefs.put(PrefsConst.VOICE_SURROUND_SOUND, status);
    }


    public void setSurroundSoundSignal(int status) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setSurroundSound(status);
            Log.d(TAG, "setSurroundSound: 环绕音:" + status);
        } else {
            Log.d(TAG, "setSurroundSound: 非支持的电源模式：" + status);
        }
    }


    public static void setSurroundSound2SOA(int status) {
        Bundle payload = new Bundle();
        payload.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_SURROUND);
        payload.putInt(VDKey.VALUE, status);
        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, payload);
        VDBus.getDefault().set(event);
        Log.d(TAG, "setSurroundSound2SOA: 环绕音:" + status);
    }


    public int getSurroundSound() {
//        int surroundSound = getSurroundSound2SOA();
        int surroundSound = getSurroundSoundSignal();
        Prefs.put(PrefsConst.VOICE_SURROUND_SOUND, surroundSound);
        return surroundSound;
    }

    public int getSurroundSoundSignal() {
        int surroundSound = voiceManager.getSurroundSound();
        surroundSound = CarVoice.SurroundSound.signalReverse(surroundSound);
        if (surroundSound == CarVoice.SurroundSound.INVALID) {
            Log.d(TAG, "getEQ: 声场设置：信号未发送，返回默认值" + CarVoice.SurroundSound.DEFAULT);
            return CarVoice.SurroundSound.DEFAULT;
        }
        Log.d(TAG, "getSurroundSound: 环绕音:" + surroundSound);
        return surroundSound;
    }

    public static int getSurroundSound2SOA() {
        Bundle bundle = new Bundle();
        bundle.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_SURROUND);
        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, bundle);
        VDEvent Gevent = VDBus.getDefault().getOnce(event);
        if (event != null && Gevent != null) {
            return Gevent.getPayload().getInt(VDKey.VALUE);
        }
        Log.d(TAG, "getSurroundSound2SOA: 环绕音: 无VDBUS服务");
        return CarVoice.SurroundSound.DEFAULT;
    }

    public static int virtualSceneSignal2UI(int signal) {
        int index = 0;
        switch (signal) {
            case CarVoice.VirtualScene.HIFI:
                index = 0;
                break;
            case CarVoice.VirtualScene.STADIUM:
                index = 1;
                break;
            case CarVoice.VirtualScene.VOCAL:
                index = 2;
                break;
            case CarVoice.VirtualScene.HALL:
                index = 3;
                break;
        }
        return index;
    }

    public static int virtualSceneUI2Signal(int index) {
        int signal = 0;
        switch (index) {
            case CarVoice.VirtualScene.HIFI:
                signal = 0x3;
                break;
            case CarVoice.VirtualScene.STADIUM:
                signal = 0x4;
                break;
            case CarVoice.VirtualScene.VOCAL:
                signal = 0x1;
                break;
            case CarVoice.VirtualScene.HALL:
                signal = 0x2;
                break;
        }
        return signal;
    }


    public void setVirtualScene(int index) {
//        setVirtualScene2SOA(index);
        setVirtualSceneSignal(index);
        Prefs.put(PrefsConst.VOICE_VIRTUAL_SCENE, index);
    }

    public void setVirtualSceneSignal(int index) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setVirtualScene(index);
            Log.d(TAG, "setVirtualScene: 虚拟现场设置:" + index);
        } else {
            Log.d(TAG, "setVirtualScene: 非支持的电源模式：" + index);
        }
    }

    public void setVirtualScene2SOA(int index) {
        Bundle payload = new Bundle();
        payload.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_VIRTUAL_SCENE);
        payload.putInt(VDKey.VALUE, index); // TODO 信号对应
        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, payload);
        VDBus.getDefault().set(event);
        Log.d(TAG, "setVirtualScene2SOA: 虚拟现场设置:" + index);
    }


    public int getVirtualScene() {
//        int virtualScene = getVirtualScene2SOA();
        int virtualScene = getVirtualSceneSignal();
        Log.d(TAG, "getVirtualScene: 虚拟现场设置:" + virtualScene);
        return virtualScene;
    }

    public int getVirtualSceneSignal() {
        int virtualScene = voiceManager.getVirtualScene();
        if (virtualScene == Integer.MIN_VALUE) {
            Log.d(TAG, "getVirtualScene: 虚拟现场设置：信号未发送，返回默认值" + CarVoice.VirtualScene.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_VIRTUAL_SCENE, CarVoice.VirtualScene.DEFAULT);
        }
        Log.d(TAG, "getVirtualScene: 虚拟现场设置:" + virtualScene);
        return virtualScene;
    }

    public int getVirtualScene2SOA() {
        Bundle bundle = new Bundle();
        bundle.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_VIRTUAL_SCENE);
        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, bundle);
        VDEvent Gevent = VDBus.getDefault().getOnce(event);
        if (event != null && Gevent != null) {
            return Gevent.getPayload().getInt(VDKey.VALUE);
        }
        Log.d(TAG, "getVirtualScene2SOA: 虚拟现场设置: 无VDBUS服务");
        return CarVoice.VirtualScene.DEFAULT;
    }


    public void setPosition(int x, int y) {
        Log.d(TAG, "setPosition: 坐标: " + x + ", " + y);
        setPositionSOA(x, y);
        setPositionSignal(x, y);
        Prefs.put(PrefsConst.VOICE_CUSTOM_X, x);
        Prefs.put(PrefsConst.VOICE_CUSTOM_Y, y);
    }

    public void setPositionSignal(int x, int y) {
        x += 8;
        y += 8;
        Log.d(TAG, "转换坐标: " + x + ", " + y);
        voiceManager.setBalance(x);
        voiceManager.setFader(y);
    }

    public void setPositionSOA(int x, int y) {
        // 设置声场（set）
        Bundle payload = new Bundle();
        payload.putInt(VDKey.TYPE, x); // balance value
        payload.putInt(VDKey.VALUE, y); // fader value
        VDEvent event = new VDEvent(VDEventDsp.SOUND_FIELD, payload);
        if (event != null) {
            VDBus.getDefault().set(event);
        } else Log.d(TAG, "setPosition VDBus: 无服务");
    }


    public int[] getPosition() {
//        int[] position = getPositionSOA();
        int[] position = getPositionSignal();
        Log.d(TAG, "getPosition: 坐标: " + position[0] + ", " + position[1]);
        return position;
    }

    public int[] getPositionSignal() {
        int balance = voiceManager.getBalance();
        int fader = voiceManager.getFader();
        balance -= 8;
        fader -= 8;
        return new int[]{balance, fader};
    }

    public int[] getPositionSOA() {
        // 获取声场（get/callback）
        VDEvent event = VDBus.getDefault().getOnce(VDEventDsp.SOUND_FIELD);
        int balance_value = event.getPayload().getInt(VDKey.TYPE);
        int fader_value = event.getPayload().getInt(VDKey.VALUE);
        int x = Prefs.get(PrefsConst.VOICE_CUSTOM_X, VoicePresenter.VoiceConstant.POSITION_X);
        int y = Prefs.get(PrefsConst.VOICE_CUSTOM_Y, VoicePresenter.VoiceConstant.POSITION_Y);
        return new int[]{balance_value, fader_value};
    }


    public void setHeadRest(int index) {
//        setHeadRest2SOA(index);
        setHeadRestSignal(index);
        Prefs.put(PrefsConst.VOICE_HEADREST, index);
    }

    public void setHeadRestSignal(int index) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setHeadRest(index);
            Log.d(TAG, "setHeadRest: 头枕扬声器:" + index);
        }
    }

    public void setHeadRest2SOA(int index) {
        Bundle payload = new Bundle();
        payload.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_HEADREST_SPEAKER_MODE);
        payload.putInt(VDKey.VALUE, index);
        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, payload);
        VDBus.getDefault().set(event);
        Log.d(TAG, "setHeadRest2SOA: 头枕扬声器:" + index);
    }


    public int getHeadRest() {
//        int headRest = getHeadRest2SOA();
        int headRest = getHeadRestSignal();
        Prefs.put(PrefsConst.VOICE_HEADREST, headRest);
        Log.d(TAG, "getHeadRest: 头枕扬声器:" + headRest);
        return headRest;
    }

    public int getHeadRestSignal() {
        int headRest = voiceManager.getHeadRest();
        if (headRest == Integer.MIN_VALUE) {
            Log.d(TAG, "getHeadRest: 头枕扬声器：信号未发送，返回默认值" + CarVoice.HeadRest.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_HEADREST, CarVoice.HeadRest.DEFAULT);
        }
        Prefs.put(PrefsConst.VOICE_HEADREST, headRest);
        Log.d(TAG, "getHeadRest: 头枕扬声器:" + headRest);
        return headRest;
    }

    public int getHeadRest2SOA() {
        Bundle bundle = new Bundle();
        bundle.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_HEADREST_SPEAKER_MODE);
        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, bundle);
        VDEvent Gevent = VDBus.getDefault().getOnce(event);
        if (event != null && Gevent != null) {
            int anInt = Gevent.getPayload().getInt(VDKey.VALUE);
            Log.d(TAG, "getHeadRest2SOA: 声场设置:" + anInt);
            return anInt;
        }
        Log.d(TAG, "getHeadRest2SOA: 头枕扬声器: 无VDBUS服务");
        return CarVoice.HeadRest.DEFAULT;
    }


    public void setSubBass(int equalization) {
        // 内置功放 随速补偿和EQ的参数需要转换
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setSubBass(equalization);
            Log.d(TAG, "setSubBass: 重低音设置:" + (equalization - 7));
            Prefs.put(PrefsConst.VOICE_SUB_BASS, equalization);
        }

//        Bundle bundle = new Bundle();
//        bundle.putInt(VDKey.TYPE, type);
//        bundle.putInt(VDKey.VALUE, value);
//        VDEvent vdEvent = new VDEvent(VDEventDsp.EQ_BMT, bundle);
//        VDBus.getDefault().set(vdEvent);
    }


    public int getSubBass() {
        int subBass = voiceManager.getSubBass();
        if (subBass == Integer.MIN_VALUE) {
            Log.d(TAG, "getSubBass: 极低音设置:信号未发送，返回默认值" + CarVoice.Equalization.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_SUB_BASS, CarVoice.Equalization.DEFAULT);
        }
        Prefs.put(PrefsConst.VOICE_SUB_BASS, subBass);
        Log.d(TAG, "getSubBass: 极低音设置:" + subBass);
        return subBass;
    }


    public void setBass(int equalization) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setBass(equalization);
            Log.d(TAG, "setBass: 低音设置:" + (equalization - 7));
        }
    }


    public int getBass() {
        int bass = voiceManager.getBass();
        if (bass == Integer.MIN_VALUE) {
            Log.d(TAG, "getBass: 低音设置:信号未发送，返回默认值" + CarVoice.Equalization.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_BASS, CarVoice.Equalization.DEFAULT);
        }
        Prefs.put(PrefsConst.VOICE_BASS, bass);
        Log.d(TAG, "getBass: 低音设置:" + bass);
        return bass;
    }


    public void setLowMid(int equalization) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setLowMid(equalization);
            Log.d(TAG, "setLowMid: 低音设置:" + (equalization - 7));
        }
    }


    public int getLowMid() {
        int lowMid = voiceManager.getLowMid();
        if (lowMid == Integer.MIN_VALUE) {
            Log.d(TAG, "getLowMid: 中低音设置:信号未发送，返回默认值" + CarVoice.Equalization.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_LOW_MID, CarVoice.Equalization.DEFAULT);
        }
        Prefs.put(PrefsConst.VOICE_LOW_MID, lowMid);
        Log.d(TAG, "getLowMid: 中低音设置:" + lowMid);
        return lowMid;
    }


    public void setMid(int equalization) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setMid(equalization);
            Log.d(TAG, "setMid: 中音设置:" + (equalization - 7));
        }
    }


    public int getMid() {
        int mid = voiceManager.getMid();
        if (mid == Integer.MIN_VALUE) {
            Log.d(TAG, "getMid: 中音设置:信号未发送，返回默认值" + CarVoice.Equalization.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_MID, CarVoice.Equalization.DEFAULT);
        }
        Prefs.put(PrefsConst.VOICE_MID, mid);
        Log.d(TAG, "getMid: 中音设置:" + mid);
        return mid;
    }


    public void setHighMid(int equalization) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setHighMid(equalization);
            Log.d(TAG, "setHighMid: 中高音设置:" + (equalization - 7));
        }
    }


    public int getHighMid() {
        int highMid = voiceManager.getHighMid();
        if (highMid == Integer.MIN_VALUE) {
            Log.d(TAG, "getHighMid: 中高音设置:信号未发送，返回默认值" + CarVoice.Equalization.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_HIGH_MID, CarVoice.Equalization.DEFAULT);
        }
        Prefs.put(PrefsConst.VOICE_HIGH_MID, highMid);
        Log.d(TAG, "getHighMid: 中高音设置:" + highMid);
        return highMid;
    }


    public void setTreble(int equalization) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setTreble(equalization);
            Log.d(TAG, "setTreble:  高音设置:" + (equalization - 7));
        }
    }


    public int getTreble() {
        int treble = voiceManager.getTreble();
        if (treble == Integer.MIN_VALUE) {
            Log.d(TAG, "getTreble: 高音设置:信号未发送，返回默认值" + CarVoice.Equalization.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_TREBLE, CarVoice.Equalization.DEFAULT);
        }
        Prefs.put(PrefsConst.VOICE_TREBLE, treble);
        Log.d(TAG, "getTreble: 高音设置:" + treble);
        return treble;
    }


    public void setSuperTreble(int equalization) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setSuperTreble(equalization);
            Log.d(TAG, "setSuperTreble: 超高音设置:" + (equalization - 7));
        }
    }


    public int getSuperTreble() {
        int superTreble = voiceManager.getSuperTreble();
        if (superTreble == Integer.MIN_VALUE) {
            Log.d(TAG, "getSuperTreble: 超高音设置:信号未发送，返回默认值" + CarVoice.Equalization.DEFAULT);
            return Prefs.get(PrefsConst.VOICE_SUPER_TREBLE, CarVoice.Equalization.DEFAULT);
        }
        Prefs.put(PrefsConst.VOICE_SUPER_TREBLE, superTreble);
        Log.d(TAG, "getSuperTreble: 超高音设置:" + superTreble);
        return superTreble;
    }


    public void setCompensation(int status) {
        // 外置功放 eamp = 1 SOA
        // 内置功放 eamp = 0 setdb
//        setCompensation2SOA(status);
        setCompensationSignal(status);

        Prefs.put(PrefsConst.VOICE_COMPENSATION, status);
    }

    /**
     * 外置功放专用（高配）
     * @param status
     */
    public void setCompensationSignal(int status) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            voiceManager.setCompensation(status);
            Log.d(TAG, "setCompensation: 随速补偿设置:" + status);
        } else {
            Log.d(TAG, "setCompensation：非支持的电源模式：" + status);
        }
        Prefs.put(PrefsConst.VOICE_COMPENSATION, status);
    }

    public void setCompensation2SOA(int status) {
        Bundle payload = new Bundle();
        payload.putInt(VDKey.TYPE, VDValueDsp.SuppressionMediaMode.NAVI_MODE);
        payload.putInt(VDKey.VALUE, status);
        VDEvent event = new VDEvent(VDEventDsp.EXT_AMP_SDVC_LEVEL, payload);
        VDBus.getDefault().set(event);
        Prefs.put(PrefsConst.VOICE_COMPENSATION, status);
        Log.d(TAG, "setCompensation2SOA: 随速补偿设置:" + status);
    }


    public int getCompensation() {
//        int compensation = getCompensation2SOA();
        int compensation = getCompensationSignal();
        Log.d(TAG, "getCompensation: 随速补偿设置:" + compensation);
        Prefs.put(PrefsConst.VOICE_COMPENSATION, compensation);
        return compensation;
    }

    public int getCompensationSignal() {
        int compensation = voiceManager.getCompensation();
        compensation = CarVoice.Compensation.signalReverse(compensation);
        if (compensation == Integer.MIN_VALUE || compensation == CarVoice.Compensation.INVALID) {
            Log.d(TAG, "getCompensation: 随速补偿设置:信号未发送，返回默认值" + CarVoice.Compensation.DEFAULT);
            return CarVoice.Compensation.DEFAULT;
        }
        Log.d(TAG, "getCompensation: 随速补偿设置:" + compensation);
        Prefs.put(PrefsConst.VOICE_COMPENSATION, compensation);
        return compensation;
    }

    public int getCompensation2SOA() {
        // 获取抑制水平（get）
        Bundle bundle = new Bundle();
        bundle.putInt(VDKey.TYPE, VDValueDsp.SuppressionMediaMode.NAVI_MODE);
        VDEvent event = new VDEvent(VDEventDsp.EXT_AMP_SDVC_LEVEL, bundle);
        VDEvent gEvent = VDBus.getDefault().getOnce(event);
        if (gEvent != null) {
            int level = gEvent.getPayload().getInt(VDKey.VALUE);
            Prefs.put(PrefsConst.VOICE_COMPENSATION, level);
        }
        return Prefs.get(PrefsConst.VOICE_COMPENSATION, CarVoice.Compensation.DEFAULT);
    }


    /**
     * 设置报警音类型
     *
     * @param type
     */
    public static void setAlarmType(int type) {
        Log.d(TAG, "setAlarmType: " + type);
        Prefs.setSystemValue(PrefsConst.VOICE_ALARM_TYPE, type);
    }


    /**
     * 获取报警音类型
     *
     * @return
     */
    public static int getAlarmType() {
        int alarmType = Prefs.getSystemValue(PrefsConst.VOICE_ALARM_TYPE, CarVoice.AlarmType.DEFAULT);
        Log.d(TAG, "getAlarmType: " + alarmType);
        return alarmType;
    }


    public void setCallBroadcast(int status) {
        Log.d(TAG, "setCallBroadcast: " + status);
        Prefs.put(PrefsConst.VOICE_CALL_BROADCAST, status);
        Prefs.setSystemValue(PrefsConst.GlobalValue.VOICE_CALL_BROADCAST, status);
    }


    public int getCallBroadcast() {
        int status = Prefs.getSystemValue(PrefsConst.GlobalValue.VOICE_CALL_BROADCAST, CarVoice.CallBroadcast.DEFAULT);
        Log.d(TAG, "getCallBroadcast: " + status);
        return status;
    }


    public void setLowSpeedAnalog(int status) {
        Log.d(TAG, "setLowSpeedAnalog: " + status);
        Prefs.put(PrefsConst.VOICE_LOW_SPEED_ANALOG, status);
        audioManager.setParameters("AVAS_MODE={\"MODE\":" + status + "}");
        MsgUtil.getInstance().setSignalVal(Topics.Voice.VOLUME_AVAS_SET, status == CarVoice.AVAS.ON ? CarVoice.AVAS_ICU.ON : CarVoice.AVAS_ICU.OFF);
    }


    public int getLowSpeedAnalog() {
        int status = Prefs.get(PrefsConst.VOICE_LOW_SPEED_ANALOG, CarVoice.AVAS.DEFAULT);
        Log.d(TAG, "getLowSpeedAnalog: " + status);
        return status;
    }


    public void setVoiceExternalMode(int status) {
        Log.d(TAG, "setVoiceExternalMode: " + status);
        Prefs.put(PrefsConst.VOICE_EXTERNAL_MODE, status);

        if (status == CarVoice.ExternalMode.OUT) {
            status = CarVoice.ExternalMode.IN_OUT;
        }
        audioManager.setParameters("outside_mode=" + status);
//        Bundle payload = new Bundle();
//        payload.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_MEDIA_OUTSIDE_THE_CAR);
//        payload.putInt(VDKey.VALUE, status);
//        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, payload);
//        VDBus.getDefault().set(event);
    }


    public int getVoiceExternalMode() {
//        Bundle bundle = new Bundle();
//        bundle.putInt(VDKey.TYPE, VDValueDsp.DspEffectType.DSP_EFFECT_MEDIA_OUTSIDE_THE_CAR);
//        VDEvent event = new VDEvent(VDEventDsp.DSP_CUSTOM_EFFECT, bundle);
//        VDEvent Gevent = VDBus.getDefault().getOnce(event);
//        if (event != null && Gevent != null) {
//            int status = Gevent.getPayload().getInt(VDKey.VALUE);
//            Log.d(TAG, "getVoiceExternalMode: " + status);
//            return status;
//        }
        return Prefs.get(PrefsConst.VOICE_EXTERNAL_MODE, CarVoice.ExternalMode.DEFAULT);
    }


    public void setVoiceLowerMediaTone(int status) {
        Log.d(TAG, "导航时压低媒体音: " + status);
        Prefs.setSystemValue(PrefsConst.GlobalValue.LOWER_MEDIA_TONE, status);
    }


    public int getVoiceLowerMediaTone() {
        int globalValue = Prefs.getSystemValue(PrefsConst.GlobalValue.LOWER_MEDIA_TONE, PrefsConst.DefaultValue.LOWER_MEDIA_TONE);
        Log.d(TAG, "导航时压低媒体音: " + globalValue);
        return globalValue;
    }


    public void setButtonSound(int status) {
        Log.d(TAG, "setButtonSound: " + status);
        Prefs.put(PrefsConst.VOICE_BUTTON_SOUND, status);
        Settings.System.putInt(mContext.getContentResolver(),
                Settings.System.SOUND_EFFECTS_ENABLED, status);

    }


    public int getButtonSound() {
        int status = Prefs.get(PrefsConst.VOICE_BUTTON_SOUND, CarVoice.ButtonSound.DEFAULT);
        Log.d(TAG, "getButtonSound: " + status);
        return status;
    }


    public void setVoiceMedia(int progress) {
        // 范围：0~31 默认值：12
        int mediaBrightnessLimit = getMediaBrightnessLimit(progress);
//        setVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_MEDIA_MUSIC, progress);
        setVoice(AudioAttributes.USAGE_MEDIA, mediaBrightnessLimit);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.VOICE_MEDIA, mediaBrightnessLimit);
        Log.d(TAG, "设置记忆媒体音值: " + mediaBrightnessLimit);
    }

    public int getMediaBrightnessLimit(int progress) {
        int apcLevelLimit = newEnergyManager.getAPCLevelLimit();
//        int powerStatus = newEnergyManager.getPowerStatus();
//        int carMode = newEnergyManager.getCarMode();
//        int lbatipVehPwrMod = newEnergyManager.getLbatipVehPwrMod();
//        if ((powerStatus == CarNewEnergy.PowerMode.COMFORTABLE || powerStatus == CarNewEnergy.PowerMode.ON)
//                && (carMode == CarNewEnergy.CarMode.NORMAL_MODE || carMode == CarNewEnergy.CarMode.TRANSPORT_MODE)
//                && lbatipVehPwrMod == CarNewEnergy.LbatipVehPwrMod.WARNING) {
        int max = progress;
        if (apcLevelLimit == CarNewEnergy.ApcLevelLimit.LEVEL_5) {
            max = (int) (MEDIA_MAX_VAL * 0.5f);
        }
        if (apcLevelLimit == CarNewEnergy.ApcLevelLimit.LEVEL_4) {
            max = (int) (MEDIA_MAX_VAL * 0.75f);
        }
        progress = Math.min(progress, max);
//        }
        return progress;
    }


    public int getVoiceMedia() {
//        int progress = getVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_MEDIA_MUSIC);
        int progress = getVoice(AudioAttributes.USAGE_MEDIA);
        int mediaBrightnessLimit = getMediaBrightnessLimit(progress);
        Log.d(TAG, "获取媒体音: " + mediaBrightnessLimit);
        return mediaBrightnessLimit;
    }


    public void setVoiceNavi(int progress) {
        // 范围：0~10 默认值：5
//        setVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_NAVI, progress);
        setVoice(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE, progress);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.VOICE_NAVI, progress);
    }


    public int getVoiceNavi() {
//        int progress = getVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_NAVI);
        int progress = getVoice(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE);
        Log.d(TAG, "获取导航音: " + progress);
        return progress;
    }


    public void setVoiceVR(int progress) {
        // 范围：0~10 默认值：5
//        setVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_VR, progress);
        setVoice(AudioAttributes.USAGE_ASSISTANT, progress);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.VOICE_VR, progress);
    }


    public int getVoiceVR() {
//        int progress = getVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_VR);
        int progress = getVoice(AudioAttributes.USAGE_ASSISTANT);
        Log.d(TAG, "获取语音: " + progress);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.VOICE_VR, progress);
        return progress;
    }


    public void setVoicePhone(int progress) {
        // 范围：0~25 默认值：12
//        setVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_PHONE, progress);
        setVoice(AudioAttributes.USAGE_VOICE_COMMUNICATION, progress);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.VOICE_PHONE, progress);
    }


    public int getVoicePhone() {
//        int progress = getVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_PHONE);
        int progress = getVoice(AudioAttributes.USAGE_VOICE_COMMUNICATION);
        Log.d(TAG, "获取电话音: " + progress);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.VOICE_PHONE, progress);
        return progress;
    }


    public void setVoiceAlarm(int progress) {
        // 范围：1~10 默认值：5
        Log.d(TAG, "设置报警音: " + progress);
        Bundle payload = new Bundle();
        payload.putInt(VDKey.TYPE, VDValueDsp.DspChannelType.CHANNEL_ALARM);
        payload.putInt(VDKey.VALUE, progress);
        VDEvent event = new VDEvent(VDEventDsp.CHANNEL_VOLUME, payload);
        VDBus.getDefault().set(event);
//        preAlarmVolume = progress;
        Prefs.setGlobalValue(PrefsConst.GlobalValue.VOICE_ALARM, progress);
    }


    public int getVoiceAlarm() {
        int progress = getVoiceSOA(VDValueDsp.DspChannelType.CHANNEL_ALARM);
        Log.d(TAG, "获取报警音: " + progress);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.VOICE_ALARM, progress);
        return progress;
    }


    public void reset() {
        // 虚拟现场
        setVirtualScene(CarVoice.VirtualScene.DEFAULT);
        // 环绕音
        setSurroundSound(CarVoice.SurroundSound.DEFAULT);
        // 头枕
        setHeadRest(CarVoice.HeadRest.DEFAULT);
        // 自定义声场
        setPosition(VoiceConstant.POSITION_X, VoiceConstant.POSITION_Y);
        // 声场EQ
        setEQ(CarVoice.EQ.DEFAULT);
        // 均衡调节
        setSubBass(CarVoice.Equalization.DEFAULT);
        setBass(CarVoice.Equalization.DEFAULT);
        setLowMid(CarVoice.Equalization.DEFAULT);
        setMid(CarVoice.Equalization.DEFAULT);
        setHighMid(CarVoice.Equalization.DEFAULT);
        setTreble(CarVoice.Equalization.DEFAULT);
        setSuperTreble(CarVoice.Equalization.DEFAULT);
        setCompensation(CarVoice.Compensation.DEFAULT);
        // 报警音类型
        setAlarmType(CarVoice.AlarmType.NATIONAL);
        // 来电播报
        setCallBroadcast(CarVoice.CallBroadcast.DEFAULT);
        // 车外低速模拟音
        setLowSpeedAnalog(CarVoice.AVAS.DEFAULT);
        // 外放模式
        setVoiceExternalMode(CarVoice.ExternalMode.DEFAULT);
        // 导航压低媒体音
        setVoiceLowerMediaTone(CarVoice.LowerMediaTone.DEFAULT);
        // 按键音
        setButtonSound(CarVoice.ButtonSound.DEFAULT);
        // 导航音
        setVoiceNavi(PrefsConst.DefaultValue.V_NAVI);
        // 语音
        setVoiceVR(PrefsConst.DefaultValue.V_VR);
        // 电话音
        setVoicePhone(PrefsConst.DefaultValue.V_PHONE);
        // 媒体音
        setVoiceMedia(PrefsConst.DefaultValue.V_MEDIA);
        // 报警音
        setVoiceAlarm(PrefsConst.DefaultValue.V_ALARM);

        GlobalVar.setIsSetVoice(true);

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }


    private void setVoice(int usage, int progress) {
        if (!isCarAudioManagerValid()) {
            return ;
        }
        int volumeGroupIdForUsage = carAudioManager.getVolumeGroupIdForUsage(usage);
        int groupMaxVolume = carAudioManager.getGroupMaxVolume(volumeGroupIdForUsage);
        Log.d(TAG, "设置音量 --- usage:" + usage + "  音量值:" + progress + "  限制最大音量:" + groupMaxVolume);
        progress = Math.min(progress, groupMaxVolume);
        carAudioManager.setGroupVolume(volumeGroupIdForUsage, progress, 1);
        SliceReceiver.notifyChange(ProviderURI.VOICE);
    }

    private void setVoiceSOA(int type, int progress) {
        Bundle payload = new Bundle();
        payload.putInt(VDKey.TYPE, type);
        payload.putInt(VDKey.VALUE, progress);
        Log.d(TAG, "设置音量 --- type:" + type + "  音量值:" + progress);
        VDEvent event = new VDEvent(VDEventDsp.CHANNEL_VOLUME, payload);
        VDBus.getDefault().set(event);
    }

    public int getVoiceSOA(int type) {
        Bundle bundle = new Bundle();
        bundle.putInt(VDKey.TYPE, type);
        VDEvent event = new VDEvent(VDEventDsp.CHANNEL_VOLUME, bundle);
        VDEvent Gevent = VDBus.getDefault().getOnce(event);
        if (event != null && Gevent != null) {
            int anInt = Gevent.getPayload().getInt(VDKey.VALUE);
            Log.d(TAG, "音量值: " + anInt);
            return anInt;
        }
        Log.d(TAG, "获取不到" + type + "音");
        return 0;
    }

    private int getVoice(int usage) {
        if (!isCarAudioManagerValid()) {
            return -1;
        }
        int volumeGroupIdForUsage = carAudioManager.getVolumeGroupIdForUsage(usage);
        return carAudioManager.getGroupVolume(volumeGroupIdForUsage);
    }

    void playAudio(int resource, int usage) {
        // 停止并释放已有 MediaPlayer
        if (mediaPlayer != null) {
            try {
                if (mediaPlayer.isPlaying()) {
                    mediaPlayer.stop();
                }
            } catch (Exception e) {
                Log.w(TAG, "停止播放异常: " + e.getMessage());
            }
            mediaPlayer.release();
            mediaPlayer = null;
        }

        if (!isCarAudioManagerValid()) {
            return ;
        }
        // 仅当媒体音未被占用时，允许播放调节音
        int[] currentUsages = carAudioManager.getCurrentFocusUsages(0);
        if(isAudioFocusOccupied(currentUsages)){
            return;
        }

        // 获取焦点  MediaPlayer 播放
        mediaPlayer = new MediaPlayer();

        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setUsage(usage)
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .build();

        mediaPlayer.setAudioAttributes(audioAttributes);
        try {
            AssetFileDescriptor afd = mContext.getResources().openRawResourceFd(resource);
            mediaPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
            afd.close();
            mediaPlayer.prepare();
            mediaPlayer.start();
            Log.d(TAG, "播放成功");
        } catch (IOException e) {
            Log.e(TAG, "播放失败：" + e.getMessage());
            mediaPlayer.release();
            mediaPlayer = null;
        }
    }


    public void playMediaAudio() {
        playAudio(R.raw.sample_media, AudioAttributes.USAGE_MEDIA);
    }

    public void playNaviAudio() {
        playAudio(R.raw.sample_navi, AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE);
    }


    public void playPhoneAudio() {
        playAudio(R.raw.sample_phone, AudioAttributes.USAGE_VOICE_COMMUNICATION);
    }


    public void playVRAudio() {
        playAudio(R.raw.sample_vr, AudioAttributes.USAGE_ASSISTANT);
    }


    public void releasePlayer() {
        if (mediaPlayer == null) {
            return;
        }
        if (mediaPlayer.isPlaying()) {
            mediaPlayer.stop();
        }
        mediaPlayer.release();
        mediaPlayer = null;
    }

    /**
     * 低配：随速补偿下发db
     *
     * @param speed        车速
     * @param compensation 随速补偿等级
     */
    public void setDB(int speed, int compensation) {
        int db = 0;

        switch (compensation) {
            case CarVoice.Compensation.OFF:
                return;
            case CarVoice.Compensation.LOW:
                Log.d(TAG, "随速补偿下发db_低");
                db = CarVoice.Compensation.MediaLow.INSTANCE.conversion(speed);
                break;
            case CarVoice.Compensation.MIDDLE:
                Log.d(TAG, "随速补偿下发db_中");
                db = CarVoice.Compensation.MediaMiddle.INSTANCE.conversion(speed);
                break;
            case CarVoice.Compensation.HIGH:
                Log.d(TAG, "随速补偿下发db_高");
                db = CarVoice.Compensation.MediaHigh.INSTANCE.conversion(speed);
                break;
        }

        gain(db);
    }

    private void gain(int db) {
        Log.d(TAG, "音量db: " + db);
        int gain = db * CarVoice.Compensation.INCREASE + CarVoice.Compensation.GAIN;
        Log.d(TAG, "音量gain: " + gain);
        audioManager.setParameters("MEDIA_SVC_GAIN={\"GAIN\": " + gain + "}");
        audioManager.setParameters("CHIME_SVC_GAIN={\"GAIN\": " + gain + "}");
    }

    /**
     * 判断媒体音是否被占用
     * @param currentUsages
     * @return
     */
    private boolean isAudioFocusOccupied(int[] currentUsages) {
        // 检查是否处于倒档
        if(DrivingLifeCycle.gear.get() == CarDriving.VCU_PRNDGearAct.R) {
            return true;
        }
        for (int usageInUse : currentUsages) {
            // 检查是否被以下使用场景占用
            if (usageInUse == CarVoice.AudioFocus.USAGE_VOICE_COMMUNICATION ||
                    usageInUse == CarVoice.AudioFocus.USAGE_CARPLAY_PHONE ||
                    usageInUse == CarVoice.AudioFocus.USAGE_GEAR_R ||
                    usageInUse == CarVoice.AudioFocus.USAGE_HICAR_PHONE) {
                Log.w(TAG, "媒体音正在播放，取消调节音播放");
                return true; // 焦点已被占用
            }
        }
        return false; // 焦点未被占用
    }

    /**
     * 检查 carAudioManager 是否为空，若为空则记录日志并返回 false。
     *
     * @return true 如果 carAudioManager 不为空，否则返回 false。
     */
    private boolean isCarAudioManagerValid() {
        if (carAudioManager == null) {
            Log.e(TAG, "carAudioManager is null. CarService may not be connected.");
            return false;
        }
        return true;
    }
}
