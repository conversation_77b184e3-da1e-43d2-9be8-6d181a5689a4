package com.bitech.vehiclesettings.utils;

import android.annotation.ColorInt;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.view.MotionEvent;
import android.view.View;

import java.util.Map;
import java.util.WeakHashMap;

public class ButtonEffectUtil {
    private static final Map<View, BackgroundState> viewStates = new WeakHashMap<>();

    private static class BackgroundState {
        Drawable normalBg;
        Drawable pressedBg;
        boolean isPressed;
    }

    /**
     * 设置按钮效果（Drawable版本）
     */
    public static void setupButtonEffect(View view, Drawable pressedBg) {
        if (view == null) return;

        BackgroundState state = new BackgroundState();
        state.normalBg = view.getBackground();
        state.pressedBg = pressedBg;
        viewStates.put(view, state);

        view.setOnTouchListener(new PressTouchListener());
    }

    /**
     * 设置按钮效果（颜色版本）
     */
    public static void setupButtonEffect(View view, @ColorInt int pressedColor) {
        setupButtonEffect(view, new ColorDrawable(pressedColor));
    }

    private static class PressTouchListener implements View.OnTouchListener {
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            BackgroundState state = viewStates.get(v);
            if (state == null) return false;

            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    state.isPressed = true;
                    v.setBackground(state.pressedBg);
                    break;

                case MotionEvent.ACTION_MOVE:
                    // 处理移出View区域的情况
                    boolean isInside = event.getX() >= 0 && event.getX() <= v.getWidth() &&
                                     event.getY() >= 0 && event.getY() <= v.getHeight();
                    
                    if (state.isPressed != isInside) {
                        state.isPressed = isInside;
                        v.setBackground(isInside ? state.pressedBg : state.normalBg);
                    }
                    break;

                case MotionEvent.ACTION_UP:
                    if (state.isPressed) {
                        v.setBackground(state.normalBg);
                    }
                    state.isPressed = false;
                    break;

                case MotionEvent.ACTION_CANCEL:
                    v.setBackground(state.normalBg);
                    state.isPressed = false;
                    break;
            }
            return false;
        }
    }

    /**
     * 移除按钮效果
     */
    public static void removeButtonEffect(View view) {
        if (view == null) return;
        
        BackgroundState state = viewStates.get(view);
        if (state != null) {
            view.setBackground(state.normalBg);
        }
        view.setOnTouchListener(null);
        viewStates.remove(view);
    }
}