package com.bitech.vehiclesettings.utils

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.location.GnssStatus
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.location.OnNmeaMessageListener
import android.os.Bundle
import androidx.core.app.ActivityCompat
import com.bitech.vehiclesettings.MyApplication
import java.util.Calendar

@SuppressLint("StaticFieldLeak")
object GpsUtils {
    private val TAG = "GpsUtils"

    private var calendar: Calendar = Calendar.getInstance()
    private val context = MyApplication.getContext()
    private var locationManager: LocationManager =
        context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

    private var callBack: CallBack? = null
    private var currentTimeMillis = 0L

    // 缓存数据
    private var locationData: Location? = null
    private var statusData: GnssStatus? = null
    private var ttffMillisData: Int? = null
    private var messageData: String? = null
    private var timestampData: Long? = null


    interface CallBack {
        fun onGetGpsLocationChanged(calendar: Calendar, location: Location)
        fun onGetNmeaReceived(nmea: String, timestamp: Long)
        fun onGetGpsStatus(status: GnssStatus)
        fun onFirstFix(ttffMillis: Int)
    }

    private var locationListener = object : LocationListener {
        override fun onLocationChanged(location: Location) {
            LogUtil.d(TAG, "onLocationChanged $location")
            locationData = location
            calendar.timeInMillis = location.time
            callBack?.onGetGpsLocationChanged(calendar, location)
        }

        override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {}

        override fun onProviderEnabled(provider: String) {}

        override fun onProviderDisabled(provider: String) {}
    }

    private val statusCallback = object : GnssStatus.Callback() {
        override fun onSatelliteStatusChanged(status: GnssStatus) {
            //LogUtil.d(TAG, "onSatelliteStatusChanged $status")
            statusData = status
            callBack?.onGetGpsStatus(status)
        }

        override fun onFirstFix(ttffMillis: Int) {
            LogUtil.d(TAG, "onFirstFix $ttffMillis")
            ttffMillisData = ttffMillis
            callBack?.onFirstFix(ttffMillis)
        }
    }

    private val nmeaMessageListener = OnNmeaMessageListener { message, timestamp ->
        //LogUtil.d(TAG, "OnNmeaMessageListener $message  $timestamp")
        if (System.currentTimeMillis() - currentTimeMillis > 500) {
            currentTimeMillis = System.currentTimeMillis()
            if (message.contains("GPGGA")) {
                messageData = message
                timestampData = timestamp
                callBack?.onGetNmeaReceived(message, timestamp)
            }
        }
    }

    fun registerCallBack(callBack: CallBack) {
        this.callBack = callBack
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return
        }

        locationManager.addNmeaListener(nmeaMessageListener)
        locationData?.let { locationListener.onLocationChanged(it) }
        statusData?.let { statusCallback.onSatelliteStatusChanged(it) }
        ttffMillisData?.let { statusCallback.onFirstFix(it) }
        if (timestampData != null && messageData != null) {
            nmeaMessageListener.onNmeaMessage(messageData, timestampData!!)
        }
    }

    fun unRegisterCallBack() {
        this.callBack = null
        locationManager.removeNmeaListener(nmeaMessageListener)
    }

    fun init() {
        LogUtil.d(TAG, "init")
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return
        }
        locationManager.requestLocationUpdates(
            LocationManager.GPS_PROVIDER, 1000, 0f,
            locationListener
        )
        locationManager.registerGnssStatusCallback(statusCallback)
    }

    fun unInit() {
        LogUtil.d(TAG, "unInit")
        locationManager.removeUpdates(locationListener)
        locationManager.unregisterGnssStatusCallback(statusCallback)
    }
    fun getLocation(): Location? {
        return locationData
    }

}