package com.bitech.vehiclesettings.view.voice;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.SeekBar;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundEqualizerBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.viewmodel.VoiceViewModel;

public class EqualizerUIAlert extends BaseDialog {
    private static final String TAG = EqualizerUIAlert.class.getSimpleName();

    private static ChangedListener changedListener;

    public EqualizerUIAlert(Context context, int theme) {
        super(context, theme);
    }

    public static void setOnChangedListener(ChangedListener onProgressChangedListener) {
        EqualizerUIAlert.changedListener = onProgressChangedListener;
    }

    public static class Builder implements VerticalSeekBar.OnSeekBarChangeListener {

        private final Context context;
        private boolean isCan = true;

        private DialogAlertSoundEqualizerBinding binding;
        private VoicePresenter voicePresenter;
        private VoiceViewModel viewModel;
        private SafeHandler voiceHandler;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private EqualizerUIAlert dialog = null;

        public Builder(Context context, VoicePresenter presenter, VoiceViewModel viewModel, SafeHandler voiceHandler) {
            this.context = context;
            this.voicePresenter = presenter;
            this.viewModel = viewModel;
            this.voiceHandler = voiceHandler;
        }


        public EqualizerUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public EqualizerUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new EqualizerUIAlert(context, R.style.Dialog);
            binding = DialogAlertSoundEqualizerBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1912; // 或者使用具体的像素值
            window.setAttributes(layoutParams);
            setListener();

            initData();
            changedListener.initObserve(binding);

            return dialog;
        }

        public void dismiss() {
            if (dialog != null) {
                dialog.dismiss();
            }
        }

        private void initData() {
            viewModel.setSubBass(Prefs.get(PrefsConst.VOICE_SUB_BASS, CarVoice.Equalization.DEFAULT));
            viewModel.setBass(Prefs.get(PrefsConst.VOICE_BASS, CarVoice.Equalization.DEFAULT));
            viewModel.setLowMid(Prefs.get(PrefsConst.VOICE_LOW_MID, CarVoice.Equalization.DEFAULT));
            viewModel.setMid(Prefs.get(PrefsConst.VOICE_MID, CarVoice.Equalization.DEFAULT));
            viewModel.setHighMid(Prefs.get(PrefsConst.VOICE_HIGH_MID, CarVoice.Equalization.DEFAULT));
            viewModel.setTreble(Prefs.get(PrefsConst.VOICE_TREBLE, CarVoice.Equalization.DEFAULT));
            viewModel.setSuperTreble(Prefs.get(PrefsConst.VOICE_SUPER_TREBLE, CarVoice.Equalization.DEFAULT));

            // 信号值转换为进度条值 需-7
            binding.tvSubBass.setText(String.valueOf(viewModel.getSubBass().getValue() == null ? CarVoice.Equalization.DEFAULT : viewModel.getSubBass().getValue() - 7));
            binding.tvBass.setText(String.valueOf(viewModel.getBass().getValue() == null ? CarVoice.Equalization.DEFAULT : viewModel.getBass().getValue() - 7));
            binding.tvLowMid.setText(String.valueOf(viewModel.getLowMid().getValue() == null ? CarVoice.Equalization.DEFAULT : viewModel.getLowMid().getValue() - 7));
            binding.tvMid.setText(String.valueOf(viewModel.getMid().getValue() == null ? CarVoice.Equalization.DEFAULT : viewModel.getMid().getValue() - 7));
            binding.tvHighMid.setText(String.valueOf(viewModel.getHighMid().getValue() == null ? CarVoice.Equalization.DEFAULT : viewModel.getHighMid().getValue() - 7));
            binding.tvTreble.setText(String.valueOf(viewModel.getTreble().getValue() == null ? CarVoice.Equalization.DEFAULT : viewModel.getTreble().getValue() - 7));
            binding.tvSuperTreble.setText(String.valueOf(viewModel.getSuperTreble().getValue() == null ? CarVoice.Equalization.DEFAULT : viewModel.getSuperTreble().getValue() - 7));
        }

        private void setListener() {
            binding.sbSubBass.setOnSeekBarChangeListener(this);
            binding.sbBass.setOnSeekBarChangeListener(this);
            binding.sbLowMid.setOnSeekBarChangeListener(this);
            binding.sbMid.setOnSeekBarChangeListener(this);
            binding.sbHighMid.setOnSeekBarChangeListener(this);
            binding.sbTreble.setOnSeekBarChangeListener(this);
            binding.sbSuperTreble.setOnSeekBarChangeListener(this);
        }

        @Override
        public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
//            int progress = seekBar.getProgress();
            Log.d(TAG, "onProgressChanged: " + progress);
            Log.d(TAG, "onProgressChanged: " + seekBar.getId());
            changedListener.onChanged(seekBar, progress, binding);
        }

        @Override
        public void onStartTrackingTouch(SeekBar seekBar) {

        }

        @Override
        public void onStopTrackingTouch(SeekBar seekBar) {

        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface ChangedListener {
        void initObserve(DialogAlertSoundEqualizerBinding binding);

        void onChanged(SeekBar seekBar, int progress, DialogAlertSoundEqualizerBinding binding);
    }

}
