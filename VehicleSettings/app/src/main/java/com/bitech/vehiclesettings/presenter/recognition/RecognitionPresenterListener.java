package com.bitech.vehiclesettings.presenter.recognition;

public interface RecognitionPresenterListener {
    /**
     * 设置摄像头状态
     * @param status 1 开启 0 关闭
     */
    void setSwCamera(int status);
    int getSwCamera();

    /**
     * 设置疲劳检测状态
     * @param status 1 开启 0 关闭
     */
    void setSwFatigue(int status);
    int getSwFatigue();

    /**
     * 设置视线分心状态
     * @param status 1 开启 0 关闭
     */
    void setSwDistraction(int status);
    int getSwDistraction();

    /**
     * 设置打电话提醒状态
     * @param status 1 开启 0 关闭
     */
    void setSwCall(int status);
    int getSwCall();

    /**
     * 设置喝水提醒状态
     * @param status 1 开启 0 关闭
     */
    void setSwDrink(int status);
    int getSwDrink();

    /**
     * 设置座椅加热状态
     * @param status 1 开启 0 关闭
     */
    void setSwSeatHeat(int status);
    int getSwSeatHeat();

    /**
     * 设置座椅通风状态
     * @param status 1 开启 0 关闭
     */
    void setSwSeatVentilation(int status);
    int getSwSeatVentilation();

    /**
     * 设置视线解锁屏保状态
     * @param status 1 开启 0 关闭
     */
    void setSwSightUnlock(int status);
    int getSwSightUnlock();

    /**
     * 设置个性化问候状态
     * @param status 1 开启 0 关闭
     */
    void setSwGreet(int status);
    int getSwGreet();

    /**
     * 设置抽烟关怀状态
     * @param status 1 开启 0 关闭
     */
    void setSwSmoke(int status);
    int getSwSmoke();

    int getVisualService();
    void setVisualService(int state);
}
