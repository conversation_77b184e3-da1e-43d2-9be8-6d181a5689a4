package com.bitech.vehiclesettings.view.newenergy

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.CountDownTimer
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy
import com.bitech.vehiclesettings.databinding.DialogCommonConfirmBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel

/**
 * 立即充电/无法外放电
 */
class ComConfirmDialog(
    context: Context,
    private val hasCancel: Boolean = false,
    private val contentText: String,
    private val dialogType: DialogType,
    private val toastResId: Int = 0
) : BaseDialog(context) {
    companion object {
        // 日志标志位
        private const val TAG = "ComConfirmDialog"

        // 倒计时总时长（毫秒）
        private const val COUNT_DOWN_TIME = 15000L

        // 倒计时间隔（毫秒）
        private const val COUNT_DOWN_INTERVAL = 1000L
    }

    private lateinit var binding: DialogCommonConfirmBinding
    private var carNewEnergyManager = NewEnergyViewModel.newEnergyManager
    private var countDownTimer: CountDownTimer? = null

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate")
        binding = DialogCommonConfirmBinding.bind(
            layoutInflater.inflate(R.layout.dialog_common_confirm, null)
        )
        // 绑定自定义dialog视图
        setContentView(binding.root)
        // 初始化页面视图
        initView()
        // 初始化页面监听
        intiListener()
        // 初始化页面数据
        initData()
        // 预约充电-启动倒计时
        if (dialogType == DialogType.CANCEL_BOOK_CHARGING) {
            startCountDown()
        }
    }

    /**
     * 启动倒计时
     */
    private fun startCountDown() {
        countDownTimer = object : CountDownTimer(COUNT_DOWN_TIME, COUNT_DOWN_INTERVAL) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds = millisUntilFinished / 1000
                LogUtil.d(TAG, "Countdown remaining: $seconds seconds")
            }

            override fun onFinish() {
                LogUtil.d(TAG, "Countdown finished, dismissing dialog")
                dismiss()
            }
        }.start()
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        stopCountDown()
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        stopCountDown()
        carNewEnergyManager.removeCallback(TAG)
        super.dismiss()
    }

    /**
     * 停止倒计时
     */
    private fun stopCountDown() {
        countDownTimer?.cancel()
        countDownTimer = null
    }

    /**
     * 初始化页面视图.
     *
     */
    private fun initView() {
        LogUtil.d(TAG, "initView : ")
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        attributes?.width = 1176
        attributes?.height = 502
        window?.attributes = attributes
    }

    /**
     * 初始化页面监听.
     *
     */
    private fun intiListener() {
        LogUtil.d(TAG, "intiListener : ")
        binding.apply {
            btnCancel.setOnClickListener { dismiss() }
            btnConfirm.setOnClickListener { view ->
                LogUtil.d(TAG, "confirm click")
                when (dialogType) {
                    DialogType.CANCEL_BOOK_CHARGING -> {
                        // 是否取消预约充电弹窗
                        if (toastResId > 0) {
                            SettingsToast.showToast(toastResId)
                            dismiss()
                            return@setOnClickListener
                        }
                        //点击确定 取消预约充电 立即充电
                        carNewEnergyManager.bookChargeSwitch =
                            CarNewEnergy.BookChargeReq.CANCEL_BOOK
                    }

                    DialogType.ENTER_SHOWROOM -> {
                        // 展车模式开启
                        carNewEnergyManager.setShowRoomSwt(CarNewEnergy.ShowRoomSwitch.OPEN)
                    }

                    DialogType.EXIT_SHOWROOM -> {
                        // 展车模式关闭
                        carNewEnergyManager.setShowRoomSwt(CarNewEnergy.ShowRoomSwitch.CLOSE)
                    }

                    DialogType.LOW_BATTERY_DISCHARGE_ERR -> {
                        // 放电电量不足弹窗提醒 无需发送信号
                    }
                }
                dismiss()
            }
        }
    }

    /**
     * 初始化页面数据.
     *
     */
    private fun initData() {
        LogUtil.d(TAG, "initData : ")
        binding.contentText = contentText
        binding.hasCancel = hasCancel

    }

    enum class DialogType {
        LOW_BATTERY_DISCHARGE_ERR,        // 低电量放电弹窗
        CANCEL_BOOK_CHARGING,         // 取消预约充电弹窗
        ENTER_SHOWROOM,     // 展车模式弹窗
        EXIT_SHOWROOM,     // 展车模式弹窗
    }

}