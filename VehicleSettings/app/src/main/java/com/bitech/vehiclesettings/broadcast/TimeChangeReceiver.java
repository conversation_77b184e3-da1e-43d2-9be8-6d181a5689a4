package com.bitech.vehiclesettings.broadcast;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.icu.util.TimeZone;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.bean.GlobalVar;
import com.bitech.vehiclesettings.carapi.constants.CarDisplay;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.luckycatlabs.sunrisesunset.SunriseSunsetCalculator;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class TimeChangeReceiver extends BroadcastReceiver {
    private static final String TAG = TimeChangeReceiver.class.getSimpleName();

    private static Calendar officialSunrise;
    private static Calendar officialSunset;
    private static double longitude = 116.40536;
    private static double latitude = 39.89586;

    private LocationManager locationManager;
    private static LocationDataUpdateListener listener;
    private final LocationListener locationListener;
    public static final String SYSTEM_UI_FORMAT_CHANGE_URI = "com.chery.systemui.action.TIME_FORMAT";
    public static final String BITECH_FORMAT_CHANGE_URI = "com.bitech.vehiclesettings.ACTION_TIME_FORMAT_CHANGED";

    static SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
    static SimpleDateFormat outputFormat = new SimpleDateFormat("HH时mm分ss秒", Locale.CHINA);

    public interface LocationDataUpdateListener {
        void onLocationDataUpdated(double latitude, double longitude);
    }

    public static void setLocationDataUpdateListener(LocationDataUpdateListener l) {
        listener = l;
    }

    public TimeChangeReceiver() {
        locationManager = (LocationManager) MyApplication.getContext().getSystemService(Context.LOCATION_SERVICE);

        locationListener = new LocationListener() {
            @Override
            public void onLocationChanged(@NonNull android.location.Location location) {
                longitude = location.getLongitude();
                latitude = location.getLatitude();

                Log.d(TAG, "位置更新：经度=" + longitude + " 纬度=" + latitude);
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onLocationDataUpdated(latitude, longitude);
                    }
                });
            }
        };
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (Intent.ACTION_TIME_CHANGED.equals(action) ||
                Intent.ACTION_TIME_TICK.equals(action) ||
                Intent.ACTION_TIMEZONE_CHANGED.equals(action)) {

            updateSunriseSunsetTime();

            Log.d(TAG, "经度=" + longitude + " 纬度=" + latitude);
            Log.d(TAG, "日出时间：" + format(officialSunrise.getTime()) +
                    "，日落时间：" + format(officialSunset.getTime()));

            if (DisplayPresenter.getAutoMode()) {
                DisplayPresenter.getInstance().setDisplayMode(CarDisplay.AUTO, false);
                GlobalVar.setIsSetDisplay(false);
                GlobalVar.setIsSetSystem(true);
            }

            registerLocationUpdates();
        }
        if (SYSTEM_UI_FORMAT_CHANGE_URI.equals(intent.getAction())) {
            Log.d(TAG, "导航栏时制变更");
            // 导航栏时制
            SystemPresenter.getInstance().setTimeDisplay(SystemPresenter.getInstance().getTimeDisplay(context) ? 0 : 1, context);
        }
    }

    public void updateSunriseSunsetTime() {
        com.luckycatlabs.sunrisesunset.dto.Location location =
                new com.luckycatlabs.sunrisesunset.dto.Location(latitude, longitude);

        SunriseSunsetCalculator calculator = new SunriseSunsetCalculator(
                location, TimeZone.getDefault().getDisplayName(false, TimeZone.SHORT));

        officialSunrise = calculator.getOfficialSunriseCalendarForDate(Calendar.getInstance());
        officialSunset = calculator.getOfficialSunsetCalendarForDate(Calendar.getInstance());
    }

    /**
     * 当前经纬度的时间是否是白天
     *
     * @return
     */
    public static boolean getIsDay() {
        Calendar now = Calendar.getInstance();
        if (officialSunrise == null || officialSunset == null) return false;
        return now.after(officialSunrise) && now.before(officialSunset);
    }

    public static String format(Date date) {
        try {
            return outputFormat.format(inputFormat.parse(String.valueOf(date)));
        } catch (ParseException e) {
            return "--";
        }
    }

    private void registerLocationUpdates() {
        if (locationManager == null) return;

        if (ContextCompat.checkSelfPermission(MyApplication.getContext(),
                Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "定位权限未授权");
            return;
        }

        try {
            locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    10 * 60 * 1000L, // 每10分钟
                    100f, // 每100米
                    locationListener);
        } catch (Exception e) {
            Log.e(TAG, "定位注册失败：" + e.getMessage());
        }
    }

    public void unregisterLocationUpdates() {
        if (locationManager != null && locationListener != null) {
            locationManager.removeUpdates(locationListener);
        }
    }
}
