package com.bitech.vehiclesettings.view.connect;

import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarConnect;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectWirelessChargingBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertDComfortBrakingBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertDPersonalizedSetingBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.driving.DrivingAnime;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.view.system.AccessRecordUIAlert;
import com.bitech.vehiclesettings.view.system.PermissionAppUIAlert;

public class WirelessChargingUIAlert extends BaseDialog {
    private static final String TAG = WirelessChargingUIAlert.class.getSimpleName();
    private static WirelessChargingUIAlert.onProgressChangedListener onProgressChangedListener;


    public WirelessChargingUIAlert(@NonNull Context context) {
        super(context);
    }

    public WirelessChargingUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected WirelessChargingUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static WirelessChargingUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(WirelessChargingUIAlert.onProgressChangedListener onProgressChangedListener) {
        WirelessChargingUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private WirelessChargingUIAlert.OnDialogResultListener listener;

    public interface onProgressChangedListener {
        void onFrontSwitch(int state);
        void onBackChargingSwitch(int state);
        void onForgetReminderSwitch(int state);
        int getFrontWirelessChargingState();
        int getForgetRemindState();
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConnectWirelessChargingBinding binding;
        DetailsUIAlert.Builder detailUIAlert;
        int frontWirelessChargingState = CarConnect.CWC_workingSts.CWC_OFF;
        int forgetRemindState = CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.OFF;
        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private WirelessChargingUIAlert dialog = null;
        private View layout;
        public Builder(Context context) {
            this.context = context;
        }


        public WirelessChargingUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public WirelessChargingUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new WirelessChargingUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertConnectWirelessChargingBinding.inflate(LayoutInflater.from(context));
            detailUIAlert = new DetailsUIAlert.Builder(context);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 548;
            window.setAttributes(layoutParams);
            initData();
            // 前排开启
            binding.swFront.setOnCheckedChangeListener((buttonView, isChecked) -> {
                onProgressChangedListener.onFrontSwitch(isChecked ? CarConnect.ICC_CWCWorkingStsSet.ON : CarConnect.ICC_CWCWorkingStsSet.OFF);
                if (forgetRemindState == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.ON) {
                    EToast.showToast(context,context.getString(R.string.str_forget_reminder_close_info), 0, false);
                }
            });
            // 遗忘提醒
            openTipDialog();
            binding.swForgetReminder.setOnCheckedChangeListener(((buttonView, isChecked) -> {
                if (isChecked && forgetRemindState == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.ON) return;
                if (!isChecked && forgetRemindState == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.OFF) return;
                onProgressChangedListener.onForgetReminderSwitch(isChecked ? CarConnect.ICC_CWCPhoneforgottenFunStsSet.PHONE_FORGOTTEN_FUN_ON : CarConnect.ICC_CWCPhoneforgottenFunStsSet.PHONE_FORGOTTEN_FUN_OFF);
            }));
            return dialog;
        }

        private void initData() {
            // 获取无线充电开关的状态
            frontWirelessChargingState = onProgressChangedListener.getFrontWirelessChargingState();
            updateFrontChargingUI(frontWirelessChargingState);
            // 获取遗忘提醒的状态
            forgetRemindState = onProgressChangedListener.getForgetRemindState();
            updateForgetReminderUI(forgetRemindState);
        }

        public void openTipDialog() {
            binding.ivForgetReminderTips.setOnClickListener(v -> {
                DetailsUIAlert.Builder detailDialog = null;
                if (detailDialog != null && detailDialog.isShowing()) {
                    return;
                }
                if (detailDialog == null) {
                    detailDialog = new DetailsUIAlert.Builder(context);
                }
                detailDialog.create(context.getString(R.string.str_forget_reminder), context.getString(R.string.str_forget_reminder_info), 1176, 350, Gravity.LEFT).show();
                detailDialog.setTextSize(com.bitech.base.R.dimen.font_24px);
                detailDialog.setPadding(120);
            });
        }

        public void showForgetTips() {
            DetailsUIAlert detailDialog = new DetailsUIAlert.Builder(context)
                    .create(context.getString(R.string.str_forget_reminder), context.getString(R.string.str_forget_reminder_info), 1128, 328, Gravity.CENTER);

            detailDialog.setOnDismissListener(dialogInterface -> {
                if (dialog != null && !dialog.isShowing()) {
                    dialog.show();
                }
            });
            if (dialog != null && dialog.isShowing()) {
                dialog.hide();
            }
            detailDialog.show();
        }

        // 前排充电UI
        public void updateFrontChargingUI(Integer signalVal) {
            if (signalVal == CarConnect.CWC_workingSts.CWC_ON) {
                binding.swFront.setChecked(true);
                binding.tvFrontTip.setText(context.getString(R.string.str_str_wireless_charging_open));
                GrayEffectUtils.removeGrayEffect(binding.llForgetReminder);
            } else {
                binding.swFront.setChecked(false);
                binding.tvFrontTip.setText(context.getString(R.string.str_str_wireless_charging_close));
                GrayEffectUtils.applyGrayEffect(binding.llForgetReminder);
            }
        }

        // 遗忘提醒
        public void updateForgetReminderUI(Integer signalVal) {
            if (signalVal == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.ON) {
                binding.swForgetReminder.setChecked(true);
            }else if (signalVal == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.OFF) {
                binding.swForgetReminder.setChecked(false);
            }
        }
    }
}
