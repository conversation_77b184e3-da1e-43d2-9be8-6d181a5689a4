package com.bitech.vehiclesettings.bean.atmosphere;

public class AmbLigBeanConst {
    public static final int ambLig01Authn = 1 - 1;
    public static final int ambLig02Authn = 2 - 1;
    public static final int ambLig03Authn = 3 - 1;
    public static final int ambLig04Authn = 4 - 1;
    public static final int ambLig05Authn = 5 - 1;
    public static final int ambLig06Authn = 6 - 1;
    public static final int ambLig07Authn = 7 - 1;
    public static final int ambLig08Authn = 8 - 1;
    public static final int ambLig09Authn = 9 - 1;
    public static final int ambLig10Authn = 10 - 1;
    public static final int ambLig11Authn = 11 - 1;
    public static final int ambLig12Authn = 12 - 1;
    public static final int ambLig13Authn = 13 - 1;
    public static final int ambLig14Authn = 14 - 1;
    public static final int ambLig15Authn = 15 - 1;
    public static final int ambLig16Authn = 16 - 1;
    // 7
    public static final int ambLigBriAdj = 17 - 1;
    // 8
    public static final int ambLigColorAdj = 24 - 1;
    // 7
    public static final int ambLigFadeINorOUTStepTi = 32 - 1;
    // 6
    public static final int ambLigFlngModSel = 39 - 1;
    // 7
    public static final int dymAmbLigLevelSig = 45 - 1;
    // 3
    public static final int dymAmbLigDirSig = 52 - 1;
    public static final int ambModLocSig = 55 - 1;
    // 3
    public static final int ambLigModRepOrdIdeSig = 58 - 1;
    // 1
    public static final int ambLigModTimCabSig = 59 - 1;
}
