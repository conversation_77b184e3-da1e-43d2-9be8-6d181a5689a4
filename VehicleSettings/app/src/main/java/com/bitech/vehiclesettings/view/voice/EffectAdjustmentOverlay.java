package com.bitech.vehiclesettings.view.voice;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Message;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.Toast;

import com.bitech.platformlib.interfaces.voice.IVoiceManagerListener;
import com.bitech.platformlib.manager.VoiceManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundEffectAdjustmentBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;

import java.util.Objects;

public class EffectAdjustmentOverlay implements SafeHandlerCallback {

    public static final String TAG = "EffectAdjustmentOverlay";
    private final Context context;
    private final WindowManager windowManager;
    private final View overlayView;
    private final WindowManager.LayoutParams layoutParams;

    final int gridCount = 17; // -7 ~ +7
    final int halfGrid = gridCount / 2; // 7
    final float[] gridSizeX = new float[1];
    final float[] gridSizeY = new float[1];
    private SafeHandler voiceHandler;
    VoicePresenter voicePresenter;
    VoiceManager voiceManager = VoiceManager.getInstance();
    DialogAlertSoundEffectAdjustmentBinding binding;
    int x;
    int y;
    int eq;
    int surroundSound;
    int virtualScene;
    private boolean init = true;

    public EffectAdjustmentOverlay(Context appContext) {
        this.context = appContext;
        int themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue);
        Context themedContext = new ContextThemeWrapper(context, themeId);
        voicePresenter = VoicePresenter.getInstance();
        voiceHandler = new SafeHandler(this);
        this.windowManager = (WindowManager) themedContext.getSystemService(Context.WINDOW_SERVICE);

        binding =
                DialogAlertSoundEffectAdjustmentBinding.inflate(LayoutInflater.from(themedContext));
        this.overlayView = binding.getRoot();

        layoutParams = new WindowManager.LayoutParams();

        layoutParams.width = 1912;
        layoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        layoutParams.format = PixelFormat.TRANSLUCENT;
        init();
        init = false;
    }

    private boolean isPointInsideView(float x, float y, View view) {
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        int viewX = location[0];
        int viewY = location[1];

        return (x > viewX && x < (viewX + view.getWidth())) &&
                (y > viewY && y < (viewY + view.getHeight()));
    }

    void init() {
        initData();
        initUI();
        initListener();
    }

    private void initData() {
        int[] position = voicePresenter.getPosition();
        x = position[0];
        y = position[1];
        eq = voicePresenter.getEQ();
        surroundSound = voicePresenter.getSurroundSound();
        virtualScene = voicePresenter.getVirtualScene();
    }

    private void initUI() {
        // 虚拟现场
        binding.spvVirtual.setItems(R.string.str_sound_effect_item1, R.string.str_sound_effect_item2, R.string.str_sound_effect_item3, R.string.str_sound_effect_item4);
        // 环绕音
        binding.swSurroundSound.setChecked(surroundSound == CarVoice.SurroundSound.ON);
        // eq
        updateEQUI(eq);

        binding.hotArea.post(() -> {
            paintHotArea(binding);
        });
        binding.sv.setOverScrollMode(View.OVER_SCROLL_NEVER);
    }

    public void updateEQUI(int eq) {
        if (binding == null) {
            return;
        }
        switch (eq) {
            case CarVoice.EQ.ALL:
                binding.rbAllCar.setChecked(true);
                binding.rbDriver.setChecked(false);
                binding.rbFront.setChecked(false);
                binding.rbRear.setChecked(false);
                binding.rbCustom.setChecked(false);
                binding.ivAudio.setVisibility(View.VISIBLE);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qcjh);
                binding.ivCustom.setVisibility(View.GONE);
                binding.btnBFReset.setAlpha(0.3f);
                setSurroundClickable(true);
                setVirtualClickable(surroundSound != CarVoice.SurroundSound.ON);
                Prefs.put(PrefsConst.VOICE_EQ, eq);
                break;
            case CarVoice.EQ.DRIVER:
                binding.rbDriver.setChecked(true);
                binding.rbAllCar.setChecked(false);
                binding.rbFront.setChecked(false);
                binding.rbRear.setChecked(false);
                binding.rbCustom.setChecked(false);
                binding.ivAudio.setVisibility(View.VISIBLE);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_zjjh);
                binding.ivCustom.setVisibility(View.GONE);
                binding.btnBFReset.setAlpha(0.3f);
                setVirtualAndSurroundClickable(false);
                Prefs.put(PrefsConst.VOICE_EQ, eq);
                break;
            case CarVoice.EQ.SLEEP:
                binding.rbFront.setChecked(true);
                binding.rbAllCar.setChecked(false);
                binding.rbDriver.setChecked(false);
                binding.rbRear.setChecked(false);
                binding.rbCustom.setChecked(false);
                binding.ivAudio.setVisibility(View.VISIBLE);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qpjh);
                binding.ivCustom.setVisibility(View.GONE);
                binding.btnBFReset.setAlpha(0.3f);
                setVirtualAndSurroundClickable(false);
                Prefs.put(PrefsConst.VOICE_EQ, eq);
                break;
            case CarVoice.EQ.VIP:
                binding.rbRear.setChecked(true);
                binding.rbAllCar.setChecked(false);
                binding.rbDriver.setChecked(false);
                binding.rbFront.setChecked(false);
                binding.rbCustom.setChecked(false);
                binding.ivAudio.setVisibility(View.VISIBLE);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_hpjh);
                binding.ivCustom.setVisibility(View.GONE);
                binding.btnBFReset.setAlpha(0.3f);
                setVirtualAndSurroundClickable(false);
                Prefs.put(PrefsConst.VOICE_EQ, eq);
                break;
            case CarVoice.EQ.CUSTOM:
                binding.rbCustom.setChecked(true);
                binding.rbAllCar.setChecked(false);
                binding.rbDriver.setChecked(false);
                binding.rbFront.setChecked(false);
                binding.rbRear.setChecked(false);
                binding.ivAudio.setVisibility(View.GONE);
                binding.ivCustom.setVisibility(View.VISIBLE);
                binding.btnBFReset.setAlpha(1f);
                setVirtualAndSurroundClickable(false);
                Prefs.put(PrefsConst.VOICE_EQ, eq);
                break;
            default:
                binding.rbAllCar.setChecked(true);
                binding.rbDriver.setChecked(false);
                binding.rbFront.setChecked(false);
                binding.rbRear.setChecked(false);
                binding.rbCustom.setChecked(false);
                binding.ivAudio.setVisibility(View.VISIBLE);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qcjh);
                binding.ivCustom.setVisibility(View.GONE);
                binding.btnBFReset.setAlpha(0.3f);
                setSurroundClickable(true);
                setVirtualClickable(false);
                Prefs.put(PrefsConst.VOICE_EQ, CarVoice.EQ.ALL);
                break;
        }
        updateSurroundSound(surroundSound);
        updateVirtualScene(virtualScene);
    }

    public void updateSurroundSound(int status) {
        if (binding == null) {
            return;
        }
        Log.d(TAG, "updateSurroundSound status: " + status);
        binding.getRoot().post(() -> {
            binding.swSurroundSound.setChecked(status == CarVoice.SurroundSound.ON);
        });
        checkEQAndHeadrest();
    }

    public void updateVirtualScene(int status) {
        if (binding == null) {
            return;
        }
        binding.getRoot().post(() -> {
            binding.spvVirtual.setSelectedIndex(status, true);
        });
    }

    public void checkEQAndHeadrest() {
        if (binding == null) {
            return;
        }
        int headRest = Prefs.get(PrefsConst.VOICE_HEADREST, CarVoice.HeadRest.DEFAULT);
        int eq = Prefs.get(PrefsConst.VOICE_EQ, CarVoice.EQ.DEFAULT);
        int surroundSound = Prefs.get(PrefsConst.VOICE_SURROUND_SOUND, CarVoice.SurroundSound.DEFAULT);
        if (headRest == CarVoice.HeadRest.PRIVATE) {
            setAllClickable(false);
        } else {
            if (eq != CarVoice.EQ.ALL) {
                setVirtualAndSurroundClickableInit(false);
            } else {
                setVirtualClickable(surroundSound == CarVoice.SurroundSound.OFF);
            }
        }
    }

    public void setAllClickable(boolean b) {
        if (binding == null) {
            return;
        }
        setVirtualAndSurroundClickableInit(b);
        binding.mg.setAlpha(b ? 1f : 0.5f);
        for (int i = 0; i < binding.mg.getChildCount(); i++) {
            LinearLayout child = (LinearLayout) binding.mg.getChildAt(i);
            for (int j = 0; j < child.getChildCount(); j++) {
                child.getChildAt(j).setClickable(b);
                child.getChildAt(j).setFocusable(b);
                child.getChildAt(j).setEnabled(b);
            }
        }
    }

    public void setVirtualAndSurroundClickableInit(boolean b) {
        if (binding == null) {
            return;
        }
        binding.swSurroundSound.setEnabled(b);
        if (b) {
            binding.spvVirtual.setAlpha(1f);
            binding.swSurroundSound.setAlpha(1f);
        } else {
            binding.spvVirtual.setAlpha(0.5f);
            binding.swSurroundSound.setAlpha(0.5f);
        }
        binding.spvVirtual.post(() -> {
            binding.spvVirtual.setEnabled(b);
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    void initListener() {
        overlayView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    // 检查点击位置是否在弹窗内容区域外
                    if (!isPointInsideView(event.getRawX(), event.getRawY(), binding.getRoot())) {
                        dismiss();
                        return true;
                    }
                }
                return false;
            }
        });
        binding.mg.setOnClickListener(v -> {
            if (voicePresenter.getHeadRest() == CarVoice.HeadRest.PRIVATE) {
                EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
            }
        });
        // 全车均衡
        binding.rbAllCar.setOnClickListener(v -> {
            if (voicePresenter.getHeadRest() == CarVoice.HeadRest.PRIVATE) {
                EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                return;
            }
            voicePresenter.setEQ(CarVoice.EQ.ALL);
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
            binding.ivAudio.setVisibility(View.VISIBLE);
            binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qcjh);
            binding.ivCustom.setVisibility(View.GONE);
            setSurroundClickable(true);
            setVirtualClickable(surroundSound != CarVoice.SurroundSound.ON);
        });
        // 主驾均衡
        binding.rbDriver.setOnClickListener(v -> {
            if (voicePresenter.getHeadRest() == CarVoice.HeadRest.PRIVATE) {
                EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                return;
            }
            voicePresenter.setEQ(CarVoice.EQ.DRIVER);
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
            binding.ivAudio.setVisibility(View.VISIBLE);
            binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_zjjh);
            binding.ivCustom.setVisibility(View.GONE);
            setVirtualAndSurroundClickable(false);
        });
        // 前排均衡
        binding.rbFront.setOnClickListener(v -> {
            if (voicePresenter.getHeadRest() == CarVoice.HeadRest.PRIVATE) {
                EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                return;
            }
            voicePresenter.setEQ(CarVoice.EQ.SLEEP);
            binding.ivAudio.setVisibility(View.VISIBLE);
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
            binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qpjh);
            binding.ivCustom.setVisibility(View.GONE);
            setVirtualAndSurroundClickable(false);
        });
        // 后排均衡
        binding.rbRear.setOnClickListener(v -> {
            if (voicePresenter.getHeadRest() == CarVoice.HeadRest.PRIVATE) {
                EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                return;
            }
            voicePresenter.setEQ(CarVoice.EQ.VIP);
            binding.ivAudio.setVisibility(View.VISIBLE);
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
            binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_hpjh);
            binding.ivCustom.setVisibility(View.GONE);
            setVirtualAndSurroundClickable(false);
        });
        // 自定义
        binding.rbCustom.setOnClickListener(v -> {
            if (voicePresenter.getHeadRest() == CarVoice.HeadRest.PRIVATE) {
                EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                return;
            }
            voicePresenter.setEQ(CarVoice.EQ.CUSTOM);
            binding.ivAudio.setVisibility(View.GONE);
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
            binding.ivCustom.setVisibility(View.VISIBLE);
            paintHotArea(binding);
            setVirtualAndSurroundClickable(false);
        });
        binding.swSurroundSound.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (init) {
                return;
            }
            if (isChecked) {
                voicePresenter.setSurroundSound(CarVoice.SurroundSound.ON);
                binding.spvVirtual.setSelectedIndex(0, true);
                setVirtualClickable(false);

            } else {
                voicePresenter.setSurroundSound(CarVoice.SurroundSound.OFF);
                setVirtualClickable(true);
            }
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_SURROUND_SOUND);
        });

        binding.touchBlocker.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                if (voicePresenter.getEQ() != CarVoice.EQ.ALL) {
                    EToast.showToast(context, context.getText(R.string.str_headrest_eq_false), Toast.LENGTH_SHORT, false);
                }
                if (voicePresenter.getSurroundSound() == PrefsConst.TRUE
                        && voicePresenter.getEQ() == CarVoice.EQ.ALL) {
                    EToast.showToast(context, context.getText(R.string.str_headrest_surround_false), Toast.LENGTH_SHORT, false);
                }
            }
            return false;
        });

        binding.spvVirtual.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index, String text) {
                voicePresenter.setVirtualScene(index);
                voiceHandler.sendMessageDelayed(MessageConst.VOICE_VIRTUAL_SCENE);
            }

            @Override
            public void onItemClicked(int index, String text) {

            }
        });

        binding.ivCustom.setOnTouchListener(new View.OnTouchListener() {
            private float touchOffsetX, touchOffsetY;

            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        // 手指在 ivCustom 内部的位置
                        touchOffsetX = event.getX();
                        touchOffsetY = event.getY();
                        break;

                    case MotionEvent.ACTION_MOVE:
                        float rawX = event.getRawX();
                        float rawY = event.getRawY();

                        // ivCustom 父布局的屏幕位置
                        int[] parentLoc = new int[2];
                        ((View) v.getParent()).getLocationOnScreen(parentLoc);
                        float parentX = parentLoc[0];
                        float parentY = parentLoc[1];

                        // hotArea 的屏幕位置
                        int[] hotAreaLoc = new int[2];
                        binding.hotArea.getLocationOnScreen(hotAreaLoc);
                        float hotLeft = hotAreaLoc[0];
                        float hotTop = hotAreaLoc[1];
                        float hotRight = hotLeft + binding.hotArea.getWidth();
                        float hotBottom = hotTop + binding.hotArea.getHeight();

                        // 限制不超出 hotArea
                        float clampedRawX = Math.max(hotLeft, Math.min(rawX - touchOffsetX, hotRight - v.getWidth()));
                        float clampedRawY = Math.max(hotTop, Math.min(rawY - touchOffsetY, hotBottom - v.getHeight()));
                        float finalX = clampedRawX - parentX;
                        float finalY = clampedRawY - parentY;

                        v.setX(finalX);
                        v.setY(finalY);

                        // 计算逻辑坐标（中心为 0,0）
                        float hotCenterX = hotLeft + binding.hotArea.getWidth() / 2f;
                        float hotCenterY = hotTop + binding.hotArea.getHeight() / 2f;

                        float centerXInParent = hotCenterX - parentX;
                        float centerYInParent = hotCenterY - parentY;

                        float offsetX = (finalX + v.getWidth() / 2f) - centerXInParent;
                        float offsetY = (finalY + v.getHeight() / 2f) - centerYInParent;

                        int logicX = Math.round(offsetX / gridSizeX[0]);
                        int logicY = Math.round(-offsetY / gridSizeY[0]);

                        logicX = Math.max(-halfGrid, Math.min(halfGrid, logicX));
                        logicY = Math.max(-halfGrid, Math.min(halfGrid, logicY));

                        VoicePresenter.getInstance().setPosition(logicX, logicY);
                        break;
                }
                return true;
            }
        });
        binding.btnBFReset.setOnClickListener(v -> {
            resetPosition(binding);
        });

        voiceManager.addCallback(TAG, new IVoiceManagerListener() {

            @Override
            public void getEQCallback(int signal) {
                 int status = CarVoice.EQ.signalReverse(signal);
                if (status == CarVoice.EQ.INVALID) return;
                if (Objects.isNull(binding)) {
                    return;
                }
                if (eq != status) {
                    Log.d(TAG, "getEQCallback Callback status: " + status);
                    eq = status;
                    updateEQUI(status);
                }
            }

            @Override
            public void getSurroundSoundCallback(int signal) {
                int status = CarVoice.SurroundSound.signalReverse(signal);
                if (status == CarVoice.SurroundSound.INVALID) return;
                if (Objects.isNull(binding)) {
                    return;
                }
                if (surroundSound != status) {
                    Log.d(TAG, "getSurroundSoundCallback Callback status: " + status);
                    surroundSound = status;
                    updateSurroundSound(status);
                }
            }

            @Override
            public void getVirtualSceneCallback(int signal) {
                int status = CarVoice.VirtualScene.signalReverse(signal);
                if (status == CarVoice.VirtualScene.INVALID) return;
                if (Objects.isNull(binding)) {
                    return;
                }
                if (voicePresenter.getEQ() != CarVoice.EQ.ALL) {
                    voicePresenter.setVirtualScene(CarVoice.VirtualScene.HIFI);
                    updateVirtualScene(0);
                    return;
                }
                if (virtualScene != status) {
                    Log.d(TAG, "getVirtualSceneCallback Callback status: " + status);
                    int index = VoicePresenter.virtualSceneSignal2UI(status);
                    virtualScene = status;
                    if (isShowing()) {
                        updateVirtualScene(index);
                    }
                }
            }

            @Override
            public void getHeadRestCallback(int signal) {
            }
        });
        voiceManager.registerListener();
    }

    private void resetPosition(DialogAlertSoundEffectAdjustmentBinding binding) {

        // 计算 hotArea 的中心点（相对于 parent）
        int[] hotAreaLoc = new int[2];
        binding.hotArea.getLocationOnScreen(hotAreaLoc);

        int[] parentLoc = new int[2];
        ((View) binding.ivCustom.getParent()).getLocationOnScreen(parentLoc);

        float centerX = hotAreaLoc[0] + binding.hotArea.getWidth() / 2f;
        float centerY = hotAreaLoc[1] + binding.hotArea.getHeight() / 2f;

        float centerXInParent = centerX - parentLoc[0];
        float centerYInParent = centerY - parentLoc[1];

        // 将 ivCustom 移动到中心位置（减去自身宽高的一半）
        float pixelX = centerXInParent - binding.ivCustom.getWidth() / 2f;
        float pixelY = centerYInParent - binding.ivCustom.getHeight() / 2f;

        binding.ivCustom.setX(pixelX);
        binding.ivCustom.setY(pixelY);
//
//            // 更新坐标显示
//            binding.tvCoordinates.setText("坐标: (0, 0)");

        // 通知 presenter 重置为 0,0
        voicePresenter.setPosition(CarVoice.EQ.DEFAULT_POSITION_X, CarVoice.EQ.DEFAULT_POSITION_Y);
    }

    void paintHotArea(DialogAlertSoundEffectAdjustmentBinding binding) {
        gridSizeX[0] = binding.hotArea.getWidth() / (float) gridCount;
        gridSizeY[0] = binding.hotArea.getHeight() / (float) gridCount;

        float logicX = x;
        float logicY = y;

        float centerX = binding.hotArea.getX() + binding.hotArea.getWidth() / 2f;
        float centerY = binding.hotArea.getY() + binding.hotArea.getHeight() / 2f;

        float pixelX = centerX + logicX * gridSizeX[0] - binding.ivCustom.getWidth() / 2f;
        float pixelY = centerY - logicY * gridSizeY[0] - binding.ivCustom.getHeight() / 2f;

        binding.ivCustom.setX(pixelX);
        binding.ivCustom.setY(pixelY);
    }

    private void setSurroundClickable(boolean b) {
        if (b) {
            binding.swSurroundSound.setAlpha(1f);
        } else {
            binding.swSurroundSound.setAlpha(0.5f);
        }
        binding.swSurroundSound.post(() -> {
            binding.swSurroundSound.setEnabled(b);
        });

    }

    public void setVirtualClickable(boolean b) {
        if (binding == null) {
            return;
        }
        if (b) {
            binding.spvVirtual.setAlpha(1f);
        } else {
            binding.spvVirtual.setAlpha(0.5f);
        }

        binding.getRoot().post(() -> {
            binding.spvVirtual.setEnabled(b);
        });
    }

    public void setVirtualAndSurroundClickable(boolean b) {
        if (binding == null) {
            return;
        }
        binding.swSurroundSound.setEnabled(b);
        if (b) {
            binding.spvVirtual.setAlpha(1f);
            binding.swSurroundSound.setAlpha(1f);
        } else {
            binding.spvVirtual.setAlpha(0.5f);
            binding.swSurroundSound.setAlpha(0.5f);
            // 关闭环绕音
//            binding.swSurroundSound.setChecked(CommonUtils.IntToBool(CarVoice.SurroundSound.OFF));
            //关闭虚拟声场
            binding.spvVirtual.setSelectedIndex(0, true);

        }
        binding.spvVirtual.post(() -> {
            binding.spvVirtual.setEnabled(b);
        });
    }

    public void show() {
        try {
            windowManager.addView(overlayView, layoutParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void dismiss() {
        try {
            if (overlayView.getWindowToken() != null) {
                windowManager.removeView(overlayView);
            }
            voiceManager.registerListener();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isShowing() {
        return overlayView.getWindowToken() != null;
    }

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.VOICE_SURROUND_SOUND:
                updateSurroundSound(surroundSound);
                break;
            case MessageConst.VOICE_EQ:
                updateEQUI(eq);
                break;
            case MessageConst.VOICE_VIRTUAL_SCENE:
                updateVirtualScene(virtualScene);
                break;
        }
    }

    @Override
    public boolean isActive() {
        return isShowing();
    }
}

