package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.ActivityManagerNative;
import android.content.Context;
import android.content.res.Configuration;
import android.os.RemoteException;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.android.internal.app.LocalePicker;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.GlobalVar;
import com.bitech.vehiclesettings.databinding.DialogAlertSLanguageBinding;
import com.bitech.vehiclesettings.utils.SendICUTopicsUtil;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.Locale;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.subjects.PublishSubject;

public class LanguageSettingUIAlert extends BaseDialog {
    private static final String TAG = LanguageSettingUIAlert.class.getSimpleName();

    public LanguageSettingUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private LanguageSettingUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSLanguageBinding binding;
        private Locale[] languages = Locale.getAvailableLocales();
        Configuration configuration;
        private final PublishSubject<Integer> languageSubject = PublishSubject.create();

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private LanguageSettingUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
            configuration = context.getResources().getConfiguration();
        }

        public LanguageSettingUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public LanguageSettingUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new LanguageSettingUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSLanguageBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            init();
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);
            return dialog;
        }

        @SuppressLint("CheckResult")
        public void init() {

            binding.svpLanguage.setItems(R.string.str_system_language_zh, R.string.str_system_language_en);

            // 获取当前语言
            Locale currentLocale = context.getResources().getConfiguration().getLocales().get(0);

            int currentIndex = 0;
            if (Locale.SIMPLIFIED_CHINESE.getLanguage().equals(currentLocale.getLanguage())) {
                currentIndex = 0; // 简体中文
            } else if (Locale.ENGLISH.getLanguage().equals(currentLocale.getLanguage())) {
                currentIndex = 1; // English
            }
            binding.svpLanguage.setSelectedIndex(currentIndex, false);
            binding.svpLanguage.setSelIndicator(0);
            binding.svpLanguage.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    dialog.dismiss();
                    languageSubject.onNext(index);
                    // 发送Topic至ICU
                    SendICUTopicsUtil.sendTopics(SendICUTopicsUtil.Topics.Vehiclesettings_Language_SET, index == 0 ? 0 : 1);
                }
            });
            languageSubject
                    .debounce(500, TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(index -> {
                        Locale targetLocale;
                        switch (index) {
                            case 0:
                                targetLocale = Locale.SIMPLIFIED_CHINESE;
                                break;
                            case 1:
                                targetLocale = Locale.ENGLISH;
                                break;
                            default:
                                targetLocale = Locale.getDefault();
                                break;
                        }
                        try {
                            Configuration config = ActivityManagerNative.getDefault().getConfiguration();
                            config.locale = targetLocale;
                            LocalePicker.updateLocale(config.locale);
                            GlobalVar.setIsSetSystem(true);
                        } catch (RemoteException exception) {
                            Log.e(TAG, "setSystemLanguage : exception = " + exception.getMessage());
                            exception.printStackTrace();
                        }
                    });


        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

}
