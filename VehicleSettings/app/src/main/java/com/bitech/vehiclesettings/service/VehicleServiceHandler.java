package com.bitech.vehiclesettings.service;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;

import java.lang.ref.WeakReference;

public class VehicleServiceHandler extends Handler {
    private static final String TAG = "VehicleServiceHandler";
    public static final int MSG_SHOW_THERMAL_RUNAWAY_DIS = 1;
    private final WeakReference<VehicleService> mServiceWeakReference;

    public VehicleServiceHandler(VehicleService service, Looper looper) {
        super(looper);
        mServiceWeakReference = new WeakReference<>(service);
    }

    @Override
    public void handleMessage(@NonNull android.os.Message msg) {
        super.handleMessage(msg);
        VehicleService service = mServiceWeakReference.get();
        if (service != null) {
            Log.d(TAG, "handleMessage: " + msg.what);
            if (msg.what == MSG_SHOW_THERMAL_RUNAWAY_DIS) {
                NewEnergyLifeCycle.Companion.showThermalRunawayDis((int) msg.obj);
            }
        }
    }

}