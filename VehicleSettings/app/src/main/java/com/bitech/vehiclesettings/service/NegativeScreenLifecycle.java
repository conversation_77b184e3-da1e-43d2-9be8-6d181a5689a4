package com.bitech.vehiclesettings.service;

import static com.bitech.vehiclesettings.MyApplication.getContext;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.driving.IDrivingManagerListener;
import com.bitech.platformlib.interfaces.light.ILightManagerListener;
import com.bitech.platformlib.interfaces.newenergy.INewEnergyListener;
import com.bitech.platformlib.interfaces.quick.IQuickManagerListener;
import com.bitech.platformlib.manager.DisplayManager;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.provider.DrivingModeProvider;
import com.bitech.vehiclesettings.provider.ProviderURI;

import java.util.concurrent.atomic.AtomicInteger;

public class NegativeScreenLifecycle implements LifecycleEventObserver {
    private static final String TAG = NegativeScreenLifecycle.class.getName() + "wzh2whu";
    private final VehicleServiceHandler handler;
    private final LifecycleOwner lifecycleOwner;
    private final Context context;

    public static AtomicInteger parkPower = new AtomicInteger(); // 驻车保电

    private QuickManager quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);
    private DrivingManager drivingManager = (DrivingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_DRIVING_MANAGER);
    private NewEnergyManager newEnergyManager = (NewEnergyManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_ENERGY_MANAGER);
    private DisplayManager displayManager = (DisplayManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_DISPLAY_MANAGER);
    private LightManager lightManager = (LightManager) BitechCar.getInstance().getServiceManager(getContext(), BitechCar.CAR_LIGHT_MANAGER);



    public NegativeScreenLifecycle(Context context, VehicleServiceHandler handler, LifecycleOwner lifecycleOwner) {
        this.context = context.getApplicationContext();
        this.handler = handler;
        this.lifecycleOwner = lifecycleOwner;
        registerListener();
    }

    @Override
    public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
        Log.i(TAG, "onStateChanged: source= " + source + " , event= " + event);
        switch (event) {
            case ON_CREATE:
                Log.i(TAG, "onCreate: " + source);
                if (quickManager != null) quickManager.registerListener();
                if (drivingManager != null) drivingManager.registerListener();
                if (newEnergyManager != null) newEnergyManager.registerListener();
                if (lightManager != null) lightManager.registerListener();

                int parkPowerStatus = newEnergyManager.getParkPowerStatus();
                parkPower.set(parkPowerStatus == Integer.MIN_VALUE ? 0 : parkPowerStatus);
                break;
            case ON_DESTROY:
                Log.i(TAG, "onDestroy: " + source);
                displayManager.removeCallback(TAG);
                quickManager.removeCallback(TAG);
                drivingManager.removeCallback(TAG);
                newEnergyManager.removeCallback(TAG);
                lightManager.removeCallback(TAG);
                break;
            default:
                break;
        }
    }

    private void registerListener() {

        quickManager.addCallback(TAG, new IQuickManagerListener() {
            @Override
            public void refuelSmallDoorCallback(int status) {

                IQuickManagerListener.super.refuelSmallDoorCallback(status);
                // 加油小门
                SliceReceiver.notifyChange(ProviderURI.UNLOCK_FUEL_PORT);
            }

            @Override
            public void wiperSensitivityCallback(int status) {
                IQuickManagerListener.super.wiperSensitivityCallback(status);
                // 雨刮灵敏度
                SliceReceiver.notifyChange(ProviderURI.WIPER_LEVEL);
            }

            @Override
            public void centerLockCallback(int status) {
                IQuickManagerListener.super.centerLockCallback(status);
                // 中控锁
                SliceReceiver.notifyChange(ProviderURI.CENTER_LOCK);
            }

            @Override
            public void rearMirrorCallback(int status) {
                IQuickManagerListener.super.rearMirrorCallback(status);
                // 后视镜折叠
                SliceReceiver.notifyChange(ProviderURI.MIRROR_FOLD);
            }

            @Override
            public void rearTailGateCallback(int status) {
                // 后尾门
                IQuickManagerListener.super.rearTailGateCallback(status);
                SliceReceiver.notifyChange(ProviderURI.TAILGATE);
            }

            @Override
            public void windowLockCallback(int status) {
                // 车窗锁
                IQuickManagerListener.super.windowLockCallback(status);
                SliceReceiver.notifyChange(ProviderURI.WINDOW_LOCK);
            }

            @Override
            public void rearScreenControlCallback(int status) {
                // 后排屏
                IQuickManagerListener.super.rearScreenControlCallback(status);
//                SliceReceiver.notifyChange(ProviderURI.);
            }
        });
        drivingManager.addCallback(TAG, new IDrivingManagerListener() {
            @Override
            public void getEPBStatusCallback(int status) {
                IDrivingManagerListener.super.getEPBStatusCallback(status);
                // 驻车制动
                SliceReceiver.notifyChange(ProviderURI.EPB);
            }

            @Override
            public void bodyInfoESCCallback(int status) {
                IDrivingManagerListener.super.bodyInfoESCCallback(status);
                // ESP
                SliceReceiver.notifyChange(ProviderURI.ESP);
            }

            @Override
            public void hillDescentControlCallback(int status) {
                IDrivingManagerListener.super.hillDescentControlCallback(status);
                // 陡坡缓降
                SliceReceiver.notifyChange(ProviderURI.HDC);
            }

            @Override
            public void autoHoldCallback(int status) {
                IDrivingManagerListener.super.autoHoldCallback(status);
                // 自动驻车
                SliceReceiver.notifyChange(ProviderURI.AUTO_HOLD);
            }

            @Override
            public void driveModeCallback(int driveMode) {
                IDrivingManagerListener.super.driveModeCallback(driveMode);
                // 档位
                context.getContentResolver().notifyChange(DrivingModeProvider.DRIVING_MODE_URI, null);
            }

            @Override
            public void onGearPositionChanged(int gearPosition) {
                IDrivingManagerListener.super.onGearPositionChanged(gearPosition);
                SliceReceiver.notifyChange(ProviderURI.POWER_OFF);
            }
        });
        newEnergyManager.addCallback(TAG, new INewEnergyListener() {
            @Override
            public void onBookChargeSwitch(int value) {
                INewEnergyListener.super.onBookChargeSwitch(value);
                // 预约充电
                SliceReceiver.notifyChange(ProviderURI.BOOK_CHARGE);
            }

            @Override
            public void onParkPowerStatus(int value) {
                INewEnergyListener.super.onParkPowerStatus(value);
                parkPower.set(value);
                SliceReceiver.notifyChange(ProviderURI.BATTERY_LIFE);
            }
        });
        lightManager.addCallback(TAG, new ILightManagerListener() {
            @Override
            public void lightMainSwitchStsCallback(int status) {
                // 大灯调节
                ILightManagerListener.super.lightMainSwitchStsCallback(status);
                SliceReceiver.notifyChange(ProviderURI.HEAD_LAMP);
            }
        });
    }
}
