package com.bitech.vehiclesettings.view.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogDmConfirmBtnBinding
import com.bitech.vehiclesettings.manager.CarConfigInfoManager
import com.bitech.vehiclesettings.utils.LogUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.lang.Runnable

/**
 * @ClassName: DMConfirmDialog
 *
 * @Date:  2024/3/22 16:37
 * @Description: 自定义按钮确认弹窗.
 **/
class DMConfirmDialog(context: Context) :
    Dialog(context, R.style.dialog), View.OnClickListener {

    // 弹窗视图对象
    private lateinit var binding: DialogDmConfirmBtnBinding

    // 弹窗确认按钮显示文言
    private var confirmText: String = context.getString(R.string.dialog_confirm_text)

    // 弹窗提示内容
    private lateinit var message: String

    // handler对象
    private val handler = Handler(Looper.getMainLooper())

    // 点击外部是否可关闭
    private var canceledOnTouchOutside = false

    // 弹窗是否自动经过时间后消失
    private var isAutoDismiss = false

    // 自动消失时间,默认10s
    private var autoDismissTime = 10

    // 是否显示关闭按钮
    private var isCancelBtnShow = true

    // 弹窗确认和取消按钮监听
    private var confirmDialogClickCallback: OnConfirmDialogClickCallback? = null

    private var countDownJob : Job? = null

    // 自动消失时执行runnable
    private val dismissRunnable = Runnable {
        LogUtil.d(TAG, "autoDismiss : ")
        confirmDialogClickCallback?.onCancelClick()
        dismiss()
    }

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        binding = DialogDmConfirmBtnBinding
                .bind(layoutInflater.inflate(R.layout.dialog_dm_confirm_btn, null))
        setContentView(binding.root)
        // 初始化视图
        initView()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        // 设置对话框窗口属性
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_DISPLAY_OVERLAY
        attributes?.windowAnimations = 0
        window?.attributes = attributes
        // 外部点击可关闭设置
        setCanceledOnTouchOutside(canceledOnTouchOutside)
        // 弹窗外部点击关闭事件监听
        setOnCancelListener {
            LogUtil.d(TAG, "setOnCancelListener : ")
            if (handler.hasCallbacks(dismissRunnable)) {
                handler.removeCallbacks(dismissRunnable)
            }
            countDownJob?.cancel()
            confirmDialogClickCallback?.onCancelClick()
        }
        if (isCancelBtnShow) {
            binding.dialogCancelBtn.visibility = View.VISIBLE
        } else {
            binding.dialogCancelBtn.visibility = View.GONE
        }
        // 对话框按钮监听
        binding.dialogConfirmBtn.setOnClickListener(this)
        binding.dialogCancelBtn.setOnClickListener(this)
        binding.dialogTipsTv.text = message
        binding.dialogConfirmBtn.text = confirmText
    }

    override fun onStart() {
        super.onStart()
        LogUtil.i(TAG, "onStart : isAutoDismiss = $isAutoDismiss")
        if (isAutoDismiss) {
            // 自动消失，在规定时间后自动消失
           // handler.postDelayed(dismissRunnable, autoDismissTime*100L)
            countDownJob = CoroutineScope(Job()+Dispatchers.Default).launch {
                flow{
                    for (i in autoDismissTime downTo 0){
                        emit(i)
                        delay(1000)
                    }
                }.flowOn(Dispatchers.Default)
                    .onEach {
                        LogUtil.d(TAG, "onEach : $it")
                        if(it == 0){
                            LogUtil.d(TAG, "onEach dismiss")
                            confirmDialogClickCallback?.onCancelClick()
                            dismiss()
                        }else {
                            binding.dialogCancelBtn.text = context.getString(R.string.dialog_cancel_text)+"(${it}s)"
                        }
                    }
                    .flowOn(Dispatchers.Main)
                    .collect{}
            }
        }
    }

    /**
     * 设置标题警告提示语.
     *
     * @param tips 提示信息
     */
    fun setTips(tips: String) {
        message = tips
    }

    /**
     * 设置确认按钮显示文言.
     * @param tips
     */
    fun setConfirmBtnText(tips: String) {
        confirmText = tips
    }

    /**
     * 设置取消按钮显示
     *
     * @param show
     */
    fun setCloseBtnShow(show: Boolean) {
        LogUtil.i(TAG, "setCloseBtnShow : show = $show")
        isCancelBtnShow = show
    }

    /**
     * 设置自动消失时间.
     *
     * @param time 时间
     */
    fun setAutoDismissTime(time: Int) {
        LogUtil.i(TAG, "setAutoDismissTime : time = $time")
        this.autoDismissTime = time
    }

    /**
     * 显示.
     *
     * @param canceledOnTouchOutside 点击外部是否可关闭,默认不可关闭
     * @param isAutoDismiss 是否经过特定的时间自动消失
     */
    fun onShow(canceledOnTouchOutside: Boolean = true, isAutoDismiss: Boolean = false) {
        LogUtil.i(
            TAG, "onShow : " +
                    "canceledOnTouchOutside = $canceledOnTouchOutside , " +
                    "isAutoDismiss = $isAutoDismiss"
        )
        this.canceledOnTouchOutside = canceledOnTouchOutside
        this.isAutoDismiss = isAutoDismiss
        handler.post { show() }
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        if (handler.hasCallbacks(dismissRunnable)) {
            handler.removeCallbacks(dismissRunnable)
        }
        countDownJob?.cancel()
        super.dismiss()
    }

    /**
     * dialog 按钮点击事件.
     *
     * @param view View
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.dialog_confirm_btn -> {
                LogUtil.d(TAG, "onClick : confirm!")
                confirmDialogClickCallback?.onConfirmClick()
                dismiss()
            }

            R.id.dialog_cancel_btn -> {
                LogUtil.d(TAG, "onClick : cancel!")
                confirmDialogClickCallback?.onCancelClick()
                dismiss()
            }
        }
    }

    /**
     * 设置确认按钮点击事件监听.
     *
     * @param callback 确认取消按钮监听
     */
    fun setDialogClickCallback(callback: OnConfirmDialogClickCallback) {
        LogUtil.i(TAG, "setDialogClickCallback : callback = $callback")
        confirmDialogClickCallback = callback
    }

    interface OnConfirmDialogClickCallback {
        /**
         * 确认按钮被点击.
         *
         */
        fun onConfirmClick()

        /**
         * 取消按钮被点击.
         *
         */
        fun onCancelClick()
    }

    companion object {
        // 日志标志位
        private const val TAG = "DMConfirmDialog"
    }
}
