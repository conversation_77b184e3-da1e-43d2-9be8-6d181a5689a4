package com.bitech.vehiclesettings.viewmodel

import android.annotation.SuppressLint
import android.content.Context
import android.provider.Settings
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.interfaces.quick.IQuickManagerListener
import com.bitech.platformlib.manager.QuickManager
import com.bitech.platformlib.utils.MsgUtil
import com.bitech.vehiclesettings.base.kt.BaseViewModel
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl
import com.bitech.vehiclesettings.fragment.QuickControlFragment
import com.bitech.vehiclesettings.presenter.quick.QuickPresenter
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vehicle.VDEventVehicle
import com.chery.ivi.vdb.event.id.vehicle.VDKeyVehicle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.math.min

class QuickControlViewModel(context: Context) : BaseViewModel() {
    private val TAG: String = QuickPresenter::class.java.simpleName
    private val quickManager =
        BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER) as QuickManager

    @SuppressLint("StaticFieldLeak")
    private var mContext: Context? = null

    // 中控锁
    private val _centralLocking = MutableLiveData<Int>()
    val centralLocking: LiveData<Int>
        get() {
            _centralLocking.postValue(quickManager.centerLock)
            Log.d(TAG, "getCentralLocking: 获取中控锁:${_centralLocking.value}")
            return _centralLocking
        }
    private var centralLockingLastUiState = -1

    // 后尾门
    private val _rearTailgate = MutableLiveData<Int>()
    val rearTailgate: LiveData<Int>
        get() {
            _rearTailgate.postValue(quickManager.rearTailGate)
            Log.d(TAG, "getRearTailgate: 获取后尾门:${_rearTailgate.value}")
            return _rearTailgate
        }
    private var rearTailgateLastUiState = -1

    // 后视镜折叠
    private val _rearMirrorFold = MutableLiveData<Int>()
    val rearMirrorFold: LiveData<Int>
        get() {
            _rearMirrorFold.postValue(quickManager.rearMirror)
            Log.d(TAG, "getRearMirrorFold: 获取后视镜折叠:${_rearMirrorFold.value}")
            return _rearMirrorFold
        }
    private var rearMirrorFoldLastUiState = -1

    // 车窗状态
    private val _windowStateFL = MutableLiveData<Int>()
    val windowStateFL: LiveData<Int>
        get() {
            _windowStateFL.postValue(quickManager.flWindow)
            Log.d(TAG, "getWindowStateFL: 获取车窗状态:${_windowStateFL.value}")
            return _windowStateFL
        }
    private val _windowStateFR = MutableLiveData<Int>()
    val windowStateFR: LiveData<Int>
        get() {
            _windowStateFR.postValue(quickManager.frWindow)
            Log.d(TAG, "getWindowStateFR: 获取车窗状态:${_windowStateFR.value}")
            return _windowStateFR
        }
    private val _windowStateRL = MutableLiveData<Int>()
    val windowStateRL: LiveData<Int>
        get() {
            _windowStateRL.postValue(quickManager.rlWindow)
            Log.d(TAG, "getWindowStateRL: 获取车窗状态:${_windowStateRL.value}")
            return _windowStateRL
        }
    private val _windowStateRR = MutableLiveData<Int>()
    val windowStateRR: LiveData<Int>
        get() {
            _windowStateRR.postValue(quickManager.rrWindow)
            Log.d(TAG, "getWindowStateRR: 获取车窗状态:${_windowStateRR.value}")
            return _windowStateRR
        }
    private val _windowState = MutableLiveData<Int>()
    val windowState: LiveData<Int>
        get() {
            val status: Int
            val statusFL = quickManager.flWindow
            val statusFR = quickManager.frWindow
            val statusRR = quickManager.rlWindow
            val statusRL = quickManager.rrWindow
            status =
                if (statusFL == CarQuickControl.GetWindowSts.AIR && statusFR == CarQuickControl.GetWindowSts.AIR &&
                    statusRR == CarQuickControl.GetWindowSts.AIR && statusRL == CarQuickControl.GetWindowSts.AIR
                ) {
                    CarQuickControl.WindowSts.AIR
                } else if (statusFL == CarQuickControl.GetWindowSts.CLOSE && statusFR == CarQuickControl.GetWindowSts.CLOSE &&
                    statusRR == CarQuickControl.GetWindowSts.CLOSE && statusRL == CarQuickControl.GetWindowSts.CLOSE
                ) {
                    CarQuickControl.WindowSts.CLOSE
                } else if (statusFL == CarQuickControl.GetWindowSts.OPEN && statusFR == CarQuickControl.GetWindowSts.OPEN &&
                    statusRR == CarQuickControl.GetWindowSts.OPEN && statusRL == CarQuickControl.GetWindowSts.OPEN
                ) {
                    CarQuickControl.WindowSts.OPEN
                } else {
                    CarQuickControl.WindowSts.INVALID
                }
            Log.d(TAG, "getWindow: 获取车窗状态:$status")
            _windowState.postValue(status)
            return _windowState
        }
    private var windowStateLastUIState = -1

    // 车窗锁
    private val _windowLock = MutableLiveData<Int>()
    val windowLock: LiveData<Int>
        get() {
            _windowLock.postValue(quickManager.windowLock)
            Log.d(TAG, "getWindowLock: 获取车窗锁:${_windowLock.value}")
            return _windowLock
        }
    private var windowLockLastUIState = -1

    // 遮阳帘
    private val _sunShade = MutableLiveData<Int>()
    val sunShade: LiveData<Int>
        get() {
            _sunShade.postValue(quickManager.sunshadeFront)
            Log.d(TAG, "getSunshade: 获取遮阳帘:${_sunShade.value}")
            return _sunShade
        }
    private var sunShadeLastUIState = -1

    // 电动尾翼
    private val _autoTail = MutableLiveData<Int>()
    val autoTail: LiveData<Int>
        get() {
            _autoTail.postValue(quickManager.spoilerSwitch)
            Log.d(TAG, "getAutoTail: 获取电动尾翼:${_autoTail.value}")
            return _autoTail
        }
    private var autoTailLastUIState = -1

    // 天窗
    val skyWindow = MutableLiveData<Int>()

    // 感应靠近解锁
    private val _approachingUnlock = MutableLiveData<Int>()
    val approachingUnlock: LiveData<Int>
        get() {
            _approachingUnlock.postValue(quickManager.approachingUnlock)
            Log.d(TAG, "getApproachingUnlock: 获取感应靠近解锁:${_approachingUnlock.value}")
            return _approachingUnlock
        }
    private var approachingUnlockLastUiState = -1

    // 感应离车上锁
    private val _departureLocking = MutableLiveData<Int>()
    val departureLocking: LiveData<Int>
        get() {
            _departureLocking.postValue(quickManager.departureLocking)
            Log.d(TAG, "getDepartureLocking: 获取感应离车上锁:${_departureLocking.value}")
            return _departureLocking
        }
    private var departureLockingLastUiState = -1

    // 锁车自动升窗
    private val _lockAutoRaiseWindow = MutableLiveData<Int>()
    val lockAutoRaiseWindow: LiveData<Int>
        get() {
            _lockAutoRaiseWindow.postValue(quickManager.lockAutoRaiseWindow)
            Log.d(TAG, "getLockAutoRaiseWindow: 获取锁车自动升窗:${_lockAutoRaiseWindow.value}")
            return _lockAutoRaiseWindow
        }
    private var lockAutoRaiseWindowLastUIState = -1

    // 锁车收起遮阳帘
    private val _lockCarSunRoofShade = MutableLiveData<Int>()
    val lockCarSunRoofShade: LiveData<Int>
        get() {
            _lockCarSunRoofShade.postValue(quickManager.lockCarSunRoofShadeClose)
            Log.d(TAG, "getSunshade: 获取天窗:${_lockCarSunRoofShade.value}")
            return _lockCarSunRoofShade
        }
    private var lockCarSunRoofShadeLastUIState = -1

    // 设防提示
    private val _defenseReminder = MutableLiveData<Int>()
    val defenseReminder: LiveData<Int>
        get() {
            _defenseReminder.postValue(quickManager.defenseReminder)
            Log.d(TAG, "defenseReminder: 设防提示:${quickManager.defenseReminder}")
            return _defenseReminder
        }
    private var defenseReminderLastUIState = -1

    // 左儿童锁
    private val _leftChildLock = MutableLiveData<Int>()
    val leftChildLock: LiveData<Int>
        get() {
            _leftChildLock.postValue(quickManager.leftChildLock)
            Log.d(TAG, "leftChildLock: 左儿童锁:${quickManager.leftChildLock}")
            return _leftChildLock
        }
    private var leftChildLockLastUiState = -1

    // 右儿童锁
    private val _rightChildLock = MutableLiveData<Int>()
    val rightChildLock: LiveData<Int>
        get() {
            _rightChildLock.postValue(quickManager.rightChildLock)
            Log.d(TAG, "rightChildLock: 右儿童锁:${quickManager.rightChildLock}")
            return _rightChildLock
        }
    private var rightChildLockLastUiState = -1

    // 自动落锁
    private val _automaticLocking = MutableLiveData<Int>()
    val automaticLocking: LiveData<Int>
        get() {
            _automaticLocking.postValue(quickManager.automaticLocking)
            Log.d(TAG, "automaticLocking: 自动落锁:${quickManager.automaticLocking}")
            return _automaticLocking
        }
    private var automaticLockingLastUIState = -1

    // 驻车自动解锁
    private val _automaticParkingUnlock = MutableLiveData<Int>()
    val automaticParkingUnlock: LiveData<Int>
        get() {
            _automaticParkingUnlock.postValue(quickManager.automaticParkingUnlock)
            Log.d(
                TAG,
                "automaticParkingUnlock: 后视镜自动折叠:${quickManager.automaticParkingUnlock}"
            )
            return _automaticParkingUnlock
        }
    private var automaticParkingUnlockLastUIState = -1

    // 后视镜自动折叠
    private val _autoRearMirrorFold = MutableLiveData<Int>()
    val autoRearMirrorFold: LiveData<Int>
        get() {
            _autoRearMirrorFold.postValue(quickManager.autoRearMirrorFold)
            Log.d(TAG, "autoRearMirrorFold: 后视镜自动折叠:${quickManager.autoRearMirrorFold}")
            return _autoRearMirrorFold
        }
    private var autoRearMirrorFoldLastUIState = -1

    // 雨天自动加热外后视镜
    private val _autoHotRearMirror = MutableLiveData<Int>()
    val autoHotRearMirror: LiveData<Int>
        get() {
            _autoHotRearMirror.postValue(quickManager.autoHotRearMirror)
            Log.d(TAG, "autoHotRearMirror: 雨天自动加热外后视镜:${quickManager.autoHotRearMirror}")
            return _autoHotRearMirror
        }
    private var autoHotRearMirrorLastUIState = -1

    // 倒车时后视镜自动调节
    private val _backRearAdjust = MutableLiveData<Int>()
    val backRearAdjust: LiveData<Int>
        get() {
            _backRearAdjust.postValue(quickManager.backRearAdjust)
            Log.d(TAG, "backRearAdjust: 倒车时后视镜自动调节:${quickManager.backRearAdjust}")
            return _backRearAdjust
        }
    private var backRearAdjustLastUIState = -1

    // 座椅便携进入/退出
    private val _seatPortable = MutableLiveData<Int>()
    val seatPortable: LiveData<Int>
        get() {
            _seatPortable.postValue(quickManager.seatPortable)
            Log.d(TAG, "seatPortable: 座椅便携进入/退出:${quickManager.seatPortable}")
            return _seatPortable
        }
    private var seatPortableLastUIState = -1

    // 后尾门高度
    private val _hudRoate = MutableLiveData<Int>()
    val hudRoate: LiveData<Int>
        get() {
            _hudRoate.postValue(quickManager.hudRoate)
            Log.d(TAG, "hudRoate: 后尾门高度:${quickManager.hudRoate}")
            return _hudRoate
        }
    private var hudRoateLastUIState = -1

    // 雨刮器灵敏度
    private val _wiperSensitivity = MutableLiveData<Int>()
    val wiperSensitivity: LiveData<Int>
        get() {
            _wiperSensitivity.postValue(quickManager.wiperSensitivity)
            Log.d(TAG, "wiperSensitivity:雨刮器灵敏度:${quickManager.wiperSensitivity}")
            return _wiperSensitivity
        }
    private var wiperSensitivityLastUIState = -1

    // 副驾安全气囊
    private val _driveAirbag = MutableLiveData<Int>()
    val driveAirbag: LiveData<Int>
        get() {
            _driveAirbag.postValue(quickManager.driveAirbag)
            Log.d(TAG, "driveAirbag:副驾安全气囊:${quickManager.driveAirbag}")
            return _driveAirbag
        }
    private var driveAirbagLastUIState = -1

    // 后排屏
    private val _rearScreenControl = MutableLiveData<Int>()
    val rearScreenControl: LiveData<Int>
        get() {
            _rearScreenControl.postValue(quickManager.rearScreenControl)
            Log.d(TAG, "rearScreenControl: 后排屏:${quickManager.rearScreenControl}")
            return _rearScreenControl
        }
    private var rearScreenControlLastUIState = -1

    // 加油小门
    private val _refuelSmallDoor = MutableLiveData<Int>()
    val refuelSmallDoor: LiveData<Int>
        get() {
            _refuelSmallDoor.postValue(quickManager.oilLidSwitch)
            Log.d(TAG, "refuelSmallDoor: 加油小门:${quickManager.oilLidSwitch}")
            return _refuelSmallDoor
        }
    private var refuelSmallDoorLastUIState = -1

    // 后视镜调节
    private val _rearMirrorAdjust = MutableLiveData<Int>()
    val rearMirrorAdjust: LiveData<Int>
        get() {
            _rearMirrorAdjust.postValue(quickManager.rearMirrorAdjust)
            Log.d(TAG, "rearMirror: 后视镜调节:${quickManager.rearMirrorAdjust}")
            return _rearMirrorAdjust
        }
    private var rearMirrorAdjustLastUIState = -1

    private val executor: ExecutorService = Executors.newCachedThreadPool()

    override fun onCleared() {
        super.onCleared()
        executor.shutdown()
    }

    init {
        mContext = context
        // 数据初始化
//        initData()
        // callback初始化
        quickControllerRegLightListen()
    }

//    private fun initData() {
//        // 中控锁
//        _centralLocking.postValue(PrefsConst.DefaultValue.Q_CENTER_LOCK)
//        // 后尾门
//        _rearTailgate.postValue(PrefsConst.DefaultValue.Q_REAR_TAIL_GATE)
//        // 后视镜折叠
//        _rearMirrorFold.postValue(PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD)
//        // 车窗状态
//        _windowState.postValue(PrefsConst.DefaultValue.Q_WINDOW)
//        // 车窗锁
//        _windowLock.postValue(PrefsConst.DefaultValue.Q_WINDOW_LOCK)
//        // 遮阳帘
//        _sunShade.postValue(PrefsConst.DefaultValue.Q_SUNSHADE)
//        // 电动尾翼
//        _autoTail.postValue(PrefsConst.DefaultValue.Q_AUTO_TAIL)
//        // 天窗
////        skyWindow.postValue(PrefsConst.DefaultValue.Q_SKY_WINDOW)
//        // 感应靠近解锁
//        _approachingUnlock.postValue(PrefsConst.DefaultValue.Q_APPROACHING_UNLOCK)
//        // 感应离车上锁
//        _departureLocking.postValue(PrefsConst.DefaultValue.Q_DEPARTURE_LOCKING)
//        // 锁车自动升窗
//        _lockAutoRaiseWindow.postValue(PrefsConst.DefaultValue.Q_LOCK_AUTO_RAISE_WINDOW)
//        // 锁车收起遮阳帘
//        _lockCarSunRoofShade.postValue(PrefsConst.DefaultValue.Q_LOCK_CAR_SUNROOF_SHADE)
//        // 设防提示
//        _defenseReminder.postValue(PrefsConst.DefaultValue.Q_DEFENSE_REMINDER)
//        // 左儿童锁
//        _leftChildLock.postValue(PrefsConst.DefaultValue.Q_LEFT_CHILD_LOCK)
//        // 右儿童锁
//        _rightChildLock.postValue(PrefsConst.DefaultValue.Q_RIGHT_CHILD_LOCK)
//        // 自动落锁
//        _automaticLocking.postValue(PrefsConst.DefaultValue.Q_AUTOMATIC_LOCKING)
//        // 驻车自动解锁
//        _automaticParkingUnlock.postValue(PrefsConst.DefaultValue.Q_AUTOMATIC_PARKING_UNLOCK)
//        // 后视镜自动折叠
//        _autoRearMirrorFold.postValue(PrefsConst.DefaultValue.Q_AUTO_REAR_MIRROR_FOLD)
//        // 雨天自动加热外后视镜
//        _autoHotRearMirror.postValue(PrefsConst.DefaultValue.Q_AUTO_HOT_REAR_MIRROR)
//        // 倒车时后视镜自动调节
//        _backRearAdjust.postValue(PrefsConst.DefaultValue.Q_BACK_AUTO_REAR_MIRROR_ADJUST)
//        // 座椅便携进入/退出
//        _seatPortable.postValue(PrefsConst.DefaultValue.Q_SEAT_PORTABLE)
//        // 后尾门高度
//        _hudRoate.postValue(PrefsConst.DefaultValue.Q_REAR_TAILGATE_ROATE)
//        // 雨刮器灵敏度
//        _wiperSensitivity.postValue(PrefsConst.DefaultValue.Q_WIPER_SENS)
//        // 副驾安全气囊
//        _driveAirbag.postValue(PrefsConst.DefaultValue.Q_DRIVE_AIR_BAG)
//        // 后排屏
//        _rearScreenControl.postValue(PrefsConst.DefaultValue.Q_REAR_SCREEN_CONTROL)
//        // 加油小门
//        _refuelSmallDoor.postValue(PrefsConst.DefaultValue.Q_REFUEL_SMALL_DOOR)
//        // 后视镜调节
//        _rearMirrorAdjust.postValue(PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD_DIALOG)
//    }

    private fun quickControllerRegLightListen() {
        quickManager.addCallback(
            TAG,
            object : IQuickManagerListener {
                override fun centerLockCallback(status: Int) {
                    Log.d(TAG, "centerLockCallback: 中控锁$status")
                    _centralLocking.postValue(status)
                }

                override fun windowLockCallback(status: Int) {
                    Log.d(TAG, "windowLockCallback: 车窗锁$status")
                    _windowLock.postValue(status)
                }

                override fun approachingUnlockCallback(status: Int) {
                    Log.d(TAG, "approachingUnlockCallback: 感应靠近解锁$status")
                    _approachingUnlock.postValue(status)
                }

                override fun departureLockingCallback(status: Int) {
                    Log.d(TAG, "departureLockingCallback: 感应离车上锁$status")
                    _departureLocking.postValue(status)
                }

                override fun rearTailGateCallback(status: Int) {
                    Log.d(TAG, "rearTailGateCallback: 后尾门:$status")
                    _rearTailgate.postValue(status)
                }

                override fun rearMirrorCallback(status: Int) {
                    Log.d(TAG, "rearMirrorCallback: 后视镜折叠:$status")
                    _rearMirrorFold.postValue(status)
                }

                override fun lockAutoRaiseWindowCallback(status: Int) {
                    Log.d(TAG, "lockAutoRaiseWindowCallback 锁车自动升窗: $status")
                    _lockAutoRaiseWindow.postValue(status)
                }

                override fun automaticLockingCallback(status: Int) {
                    Log.d(TAG, "automaticLockingCallback: 自动落锁: $status")
                    _automaticLocking.postValue(status)
                }

                override fun automaticParkingUnlockCallback(status: Int) {
                    Log.d(
                        TAG, "automaticParkingUnlockCallback: 驻车自动解锁: $status"
                    )
                    _automaticParkingUnlock.postValue(status)
                }

                override fun seatPortableCallback(status: Int) {
                    Log.d(TAG, "seatPortableCallback: 座椅便携进入: $status")
                    _seatPortable.postValue(status)
                }

                override fun refuelSmallDoorCallback(status: Int) {
                    Log.d(TAG, "refuelSmallDoorCallback: 加油小门: $status")
                    _refuelSmallDoor.postValue(status)
                }

                override fun leftChildLockCallback(status: Int) {
                    Log.d(TAG, "leftChildLockCallback: 左前锁: $status")
                    _leftChildLock.postValue(status)
                }

                override fun rightChildLockCallback(status: Int) {
                    Log.d(TAG, "rightChildLockCallback: 右前锁: $status")
                    _rightChildLock.postValue(status)
                }

                override fun defenseReminderCallback(status: Int) {
                    Log.d(TAG, "defenseReminderCallback: 设防提醒: $status")
                    _defenseReminder.postValue(status)
                }

                override fun lockCarSunRoofShadeCloseCallback(status: Int) {
                    Log.d(TAG, "lockCarSunRoofShadeCloseCallback: 锁车收起遮阳帘: $status")
                    _lockCarSunRoofShade.postValue(status)
                }

                override fun hudRoateCallback(status: Int) {
                    Log.d(TAG, "hudRoateCallback: 后尾门开启高度: $status")
                    _hudRoate.postValue(status)
                }

                override fun spoilerCtrlFbCallback(status: Int) {
                    Log.d(TAG, "spoilerCtrlFbCallback: 电动尾翼: $status")
                    _autoTail.postValue(status)
                }

                override fun autoStsCallback(status: Int) {
                    Log.d(TAG, "autoStsCallback: 后视镜自动折叠: $status")
                    _autoRearMirrorFold.postValue(status)
                }

                override fun autoHeatingFbCallback(status: Int) {
                    Log.d(TAG, "autoHeatingFbCallback: 雨天自动加热外后视镜: $status")
                    _autoHotRearMirror.postValue(status)
                }

                override fun backAutoRearMirrorAdjustCallback(status: Int) {
                    Log.d(TAG, "backAutoRearMirrorAdjustCallback: 倒车时后视镜自动调节: $status")
                    _backRearAdjust.postValue(status)
                }

                override fun windowFrontLeftCallback(status: Int) {
                    Log.d(TAG, "windowFrontLeftCallback: 左前窗: $status")
                    _windowStateFL.postValue(status)
                }

                override fun windowFrontRightCallback(status: Int) {
                    Log.d(TAG, "windowFrontRightCallback: 右前窗: $status")
                    _windowStateFR.postValue(status)
                }

                override fun windowRearLeftCallback(status: Int) {
                    Log.d(TAG, "windowRearLeftCallback: 左后窗: $status")
                    _windowStateRL.postValue(status)
                }

                override fun windowRearRightCallback(status: Int) {
                    Log.d(TAG, "windowRearRightCallback: 右后窗: $status")
                    _windowStateRR.postValue(status)
                }

                override fun rearScreenControlCallback(status: Int) {
                    Log.d(TAG, "rearScreenControlCallback: 后屏控制: $status")
                    _rearScreenControl.postValue(status)
                }

                override fun frontShadStsCallback(status: Int) {
                    Log.d(TAG, "frontShadStsCallback: 前排遮阳帘$status")
                }

                override fun rearShadStsCallback(status: Int) {
                    Log.d(TAG, "rearShadStsCallback: 后排遮阳帘$status")
                }

                override fun mfsSwitchModeCallback(status: Int) {
                    Log.d(TAG, "mfsSwitchModeCallback: 方向盘按键自定义开启后视镜调节: $status")
                }

                override fun wiperSensitivityCallback(status: Int) {
                    Log.d(TAG, "wiperSensitivityCallback: 雨刷灵敏度: $status")
                    _wiperSensitivity.postValue(status)
                }

                override fun passengerAirbagCallback(status: Int) {
                    Log.d(TAG, "passengerAirbagCallback: 副驾安全气囊: $status")
                    _driveAirbag.postValue(status)
                }
            })
        quickManager.registerListener()
    }

    fun setCentralLocking(status: Int) {
        //显⽰名称：中控锁 开关设置：解锁/闭锁 开关默认值：解锁
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击中控锁软开关解锁
        //2. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击中控锁软开关闭锁
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_CenterLockSwt = 0x1: UNLOCK，然后发送 0x0:Not
        //Active 给 FLZCU，⼤屏中控锁设置显⽰为解锁，计时 2s 若检测到状态反馈信号 LHFDoorLockSts
        //= 0x1:Unlocked，则⼤屏中控锁设置显⽰保持为解锁状态， 否则弹回闭锁状态；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_CenterLockSwt = 0x2:LOCK，然后发送 0x0:Not Active
        //给 FLZCU，⼤屏中控锁设置显⽰为闭锁，计时 2s 若检测到状态反馈信号 LHFDoorLockSts =
        //0x0:Locked || 0x2:Superlocked，则⼤屏中控锁设置显⽰保持为闭锁状态，否则弹回解锁状态；
        //3. ICC 收到 LHFDoorLockSts =0x3:Unknown，⼤屏显⽰中控锁设置显⽰根据上次状态显⽰不变

        // ICC -> FLZCU  信号名：ICC_CenterLockSwt
        // 0x0:Not Active
        // 0x1: UNLOCK
        // 0x2:LOCK
        // 0x3:Reserved

        // FLZCU -> ICC 信号名：LHFDoorLockSts
        // 0x0:Not Locked
        // 0x1:Unlocked
        // 0x2:Superlocked
        // 0x3:Unknown

        Log.d(TAG, "setCentralLocking: 发送中控锁:$status")
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.ButtonSts.OFF && centralLocking.value == CarQuickControl.GetCentralLockSts.UNLOCKED) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && centralLocking.value == CarQuickControl.GetCentralLockSts.LOCKED) {
                return
            }
            quickManager.centerLock =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetCentralLockSts.UNLOCK else CarQuickControl.SetCentralLockSts.LOCK
//            delayAndExecute { centralLocking }

        }
    }

    fun centerLockingUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_CENTER_LOCK
        if (topic == 0x1) {
            status = 0
            centralLockingLastUiState = 0
        } else if (topic == 0x0 || topic == 0x2) {
            status = 1
            centralLockingLastUiState = 1
        } else if (centralLockingLastUiState != -1) {
            status = centralLockingLastUiState
        }
        return status
    }

    fun setRearTailgate(status: Int) {
        //显⽰名称：后尾⻔（软开关） 开关设置：开启/关闭 开关默认值：关闭
        //配置：PLG 配置
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 后备箱关闭，在⼤屏⻓按后备箱开关 500ms；
        //2. 后备箱打开，在⼤屏⻓按后备箱开关 500ms；
        //执⾏输出（1||2）
        //1. 若触发条件为 1， 开关显⽰为⾼亮（开启状态），ICC 持续发送六帧 ICC_TrunkSW=0x1:ON，之
        //后发送 0x0:Not Active 给 PLG，计时 6.5s 若接收到状态反馈信号 PLG_LatchSts =0x0 :Open ||
        //0x1:Secondary，则后备箱开关保持⾼亮，否则开关弹回⾮⾼亮；
        //2. 若触发条件为 2， 开关显⽰为⾮⾼亮（关闭状态），ICC 持续发送六帧 ICC_TrunkSW=0x2:OFF，
        //之后发送 0x0:Not Active 给 PLG，计时 6.5s 若接收到状态反馈信号 PLG_LatchSts
        //=0x2:Latched，则后备箱开关保持⾮⾼亮，否则开关弹回⾼亮；

        // ICC -> PLG 信号名：ICC_TrunkSW
        // 0x0:Not Active
        // 0x1:ON
        // 0x2:OFF
        // 0x3:Reserved

        // PLG -> ICC 信号名：PLG_LatchSts
        // 0x0:Open
        // 0x1:Secondary
        // 0x2:Latched
        // 0x3:Initializing

        Log.d(TAG, "后尾门: $status")
        // 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.ButtonSts.OFF && rearTailgate.value == CarQuickControl.GetRearTailGateSts.LATCHED) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && (rearTailgate.value == CarQuickControl.GetRearTailGateSts.OPEN || rearTailgate.value == CarQuickControl.GetRearTailGateSts.SECONDARY)) {
                return
            }
            quickManager.rearTailGate =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetRearTailGateSts.OFF else CarQuickControl.SetRearTailGateSts.ON
            delayAndExecute { rearTailgate }
        }
    }

    fun rearTailGateUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_REAR_TAIL_GATE
        if (topic == 0 || topic == 1) {
            status = 1
            rearTailgateLastUiState = 1
        } else if (topic == 2) {
            status = 0
            rearTailgateLastUiState = 0
        } else if (rearTailgateLastUiState != -1) {
            status = rearTailgateLastUiState
        }
        return status
    }

    fun setRearMirror(status: Int) {
        //显⽰名称：后视镜折叠 开关设置：折叠/展开 开关默认值：展开
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 点击⼤屏上的软开关使其展开；
        //2. 点击⼤屏上的软开关使其折叠；
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_RearMirrorFoldCmd =0x2: Unfold ，然后发送 0x0:Not
        //Active 给 FLZCU， FLZCU 控制开启外后视镜展开，计时 2s 若接收到 状态反馈信号
        //RearViewFoldSts=0x2: Unfold，则外后视镜折叠开关保持展开，否则开关弹回折叠；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_RearMirrorFoldCmd =0x1: Fold ，然后发送 0x0:Not
        //Active 给 FLZCU，FLZCU 控制外后视镜折叠，计时 2s 若接收到状态反馈信号 RearViewFoldSts
        //=0x1: Fold，则外后视镜折叠开关保持折叠，否则开关弹回展开；

        // ICC -> FLZCU 信号名：ICC_RearMirrorFoldCmd
        // 0x0:Not Active
        // 0x1:Fold
        // 0x2:Unfold

        // FLZCU -> ICC 信号名：RearViewFoldSts
        // 0x0:invalid
        // 0x1:Fold
        // 0x2:Unfold

        // 后视镜折叠 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)； 开关默认值：展开

        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "后视镜折叠: $status")
            if (status == CarQuickControl.ButtonSts.OFF && rearMirrorFold.value == CarQuickControl.GetRearMirrorFoldSts.UNFOLD) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && rearMirrorFold.value == CarQuickControl.GetRearMirrorFoldSts.FOLD) {
                return
            }
            quickManager.rearMirror =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetRearMirrorFoldSts.UNFOLD else CarQuickControl.SetRearMirrorFoldSts.FOLD
            delayAndExecute { rearMirrorFold }
        }
    }

    fun rearMirrorUIState(topic: Int?): Int {
        var status = PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD
        if (topic == 1) {
            status = 1
            rearMirrorFoldLastUiState = 1
        } else if (topic == 2) {
            status = 0
            rearMirrorFoldLastUiState = 0
        } else if (rearMirrorFoldLastUiState != -1) {
            status = rearMirrorFoldLastUiState
        }
        return status
    }

    fun setWindowState(status: Int) {
        //   前置条件：（1&2&3）
        //   1.	电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //   2.	当一键四门车窗关闭按键激活可用 （激活可用：前置条件3）；
        //   3.	四车窗开启比例不全为 0
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.WindowSts.CLOSE &&
                (quickManager.flWindow == CarQuickControl.GetWindowSts.CLOSE && quickManager.frWindow == CarQuickControl.GetWindowSts.CLOSE &&
                        quickManager.rlWindow == CarQuickControl.GetWindowSts.CLOSE && quickManager.rrWindow == CarQuickControl.GetWindowSts.CLOSE)
            ) {
                return
            }
            if (status == CarQuickControl.WindowSts.OPEN &&
                (quickManager.flWindow == CarQuickControl.GetWindowSts.OPEN && quickManager.frWindow == CarQuickControl.GetWindowSts.OPEN &&
                        quickManager.rlWindow == CarQuickControl.GetWindowSts.OPEN && quickManager.rrWindow == CarQuickControl.GetWindowSts.OPEN)
            ) {
                return
            }
            if (status == CarQuickControl.WindowSts.AIR &&
                (quickManager.flWindow == CarQuickControl.GetWindowSts.AIR && quickManager.frWindow == CarQuickControl.GetWindowSts.AIR &&
                        quickManager.rlWindow == CarQuickControl.GetWindowSts.AIR && quickManager.rrWindow == CarQuickControl.GetWindowSts.AIR)
            ) {
                return
            }
            quickManager.setWindowVentilate(
                if (status == CarQuickControl.WindowSts.CLOSE) CarQuickControl.SetWindowSts.CLOSE
                else if (status == CarQuickControl.WindowSts.OPEN) CarQuickControl.SetWindowSts.OPEN else CarQuickControl.SetWindowSts.AIR
            )
            delayAndExecute { windowState }
        }
    }

    fun windowUIState(topicFL: Int, topicFR: Int, topicRL: Int, topicRR: Int): Int {
        var status = PrefsConst.DefaultValue.Q_WINDOW
        if (topicFL == 0x14 && topicFR == 0x14 && topicRR == 0x14 && topicRL == 0x14) {
            status = 2
            windowStateLastUIState = 2
        } else if (topicFL == 0x0 && topicFR == 0x0 && topicRR == 0x0 && topicRL == 0x0) {
            status = 0
            windowStateLastUIState = 0
        } else if (topicFL == 0x64 && topicFR == 0x64 && topicRR == 0x64 && topicRL == 0x64) {
            status = 1
            windowStateLastUIState = 1
        } else if (windowStateLastUIState != -1) {
            status = windowStateLastUIState
        }
        return status
    }

    fun setWindow(state: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.setWindowVentilate(state)
        }
    }

    fun setWindowStateFL(state: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.flWindow = state
        }
    }

    fun setWindowStateFR(state: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.frWindow = state
        }
    }

    fun setWindowStateRL(state: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.rlWindow = state
        }
    }

    fun setWindowStateRR(state: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.rrWindow = state
        }
    }

    fun setWindowLock(state: Int) {
        // 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setWindowLock: 设置车窗锁状态: $state")
            if (state == CarQuickControl.ButtonSts.OFF && windowLock.value == CarQuickControl.GetWindowLockSts.PERMIT) {
                return
            }
            if (state == CarQuickControl.ButtonSts.ON && windowLock.value == CarQuickControl.GetWindowLockSts.INHIBIT) {
                return
            }
            quickManager.windowLock =
                if (state == CarQuickControl.ButtonSts.OFF) CarQuickControl.GetWindowLockSts.PERMIT else CarQuickControl.SetWindowLockSts.INHIBIT
            delayAndExecute { windowLock }
        }
    }

    fun windowLockUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_WINDOW_LOCK
        if (topic == 0) {
            status = 0
            windowLockLastUIState = 0
        } else if (topic == 1) {
            status = 1
            windowLockLastUIState = 1
        } else if (windowLockLastUIState != -1) {
            status = windowLockLastUIState
        }
        return status
    }

    fun setLockCarSunRoofShade(state: Int) {
//        前置条件（a）
//        1. 电源模式(0x49D:FLZCU_9_PowerMode=0x1:Comfortable/0x2:ON) ；
//        触发条件（a）
//        a. 点击关闭锁车遮阳帘设置项；
//        b. 点击开启锁车遮阳帘设置项
//        执行输出（a&b）
//        a. 若触发条件 a，ICC 发送 ICC_LockCarSunRoofShadeCloseSw= 0x2:OFF；
//        b. 若触发条件 b，ICC 发送 ICC_LockCarSunRoofShadeCloseSw= 0x1:ON；
//        备注：
//        1. 出厂默认状态要求：出厂默认锁车关闭前后遮阳帘功能为disable状态；
//        2. 记忆要求：对于设置结果要能下电/软件重启情况下存储记忆（存EE）
//        3. FRZCU同时接收0x4D4，并记忆设置状态。
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setLockCarSunRoofShadeClose: 设置锁车遮阳帘状态: $state")
            if (state == CarQuickControl.ButtonSts.OFF && lockCarSunRoofShade.value == CarQuickControl.GetLockCarSunRoofShadeCloseSts.DISABLE) {
                return
            }
            if (state == CarQuickControl.ButtonSts.ON && lockCarSunRoofShade.value == CarQuickControl.GetLockCarSunRoofShadeCloseSts.ENABLE) {
                return
            }
            quickManager.lockCarSunRoofShadeClose =
                if (state == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetLockCarSunRoofShadeCloseSts.OFF else CarQuickControl.SetLockCarSunRoofShadeCloseSts.ON
            delayAndExecute { lockCarSunRoofShade }
        }
    }

    fun lockCarSunRoofShadeUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_LOCK_CAR_SUNROOF_SHADE
        if (topic == 0x2) {
            status = 0
            lockCarSunRoofShadeLastUIState = 0
        } else if (topic == 0x1) {
            status = 1
            lockCarSunRoofShadeLastUIState = 1
        } else if (lockCarSunRoofShadeLastUIState != -1) {
            status = lockCarSunRoofShadeLastUIState
        }
        return status
    }

    fun setDefenseReminder(status: Int) {
        //显⽰名称：设防提⽰ 开关设置：灯光、灯光和喇叭 开关默认值：灯光和喇叭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. ⽤⼾在⼤屏上设置设防提⽰为灯光提⽰；
        //2. ⽤⼾在⼤屏上设置设防提⽰为灯光和喇叭；
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧，ICC_lockSetSwitchSts = 0x2:Only Lighting，然后发送
        //0x0:Not Active 给 FLZCU，⼤屏设防提⽰显⽰为灯光提⽰，计时 2s 若检测到状态反馈信号
        //FLZCU_AlarmWarnSetSW = 0x2:Only Lighting，则⼤屏设防提⽰显⽰保持为灯光提⽰状态， 否则
        //回弹；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_lockSetSwitchSts = 0x3：Sound and Lighting，然后发
        //送 0x0:Not Active 给 FLZCU，⼤屏设防提⽰显⽰为灯光和喇叭，计时 2s 若检测到状态反馈信号
        //FLZCU_AlarmWarnSetSW = 0x3：Sound and Lighting，则⼤屏设防提⽰显⽰保持为灯光和喇叭
        //状态，否则回弹；

        // ICC -> FLZCU 信号名：ICC_lockSetSwitchSts
        // 0x0:Not Active
        // 0x1:OFF
        // 0x2:Only Lighting
        // 0x3:Sound and Lighting

        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.defenseReminder == CarQuickControl.GetDefenseReminderSts.ONLY_LIGHTING) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.defenseReminder == CarQuickControl.GetDefenseReminderSts.SOUND_AND_LIGHTING) {
                return
            }
            quickManager.defenseReminder =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetDefenseReminderSts.ONLY_LIGHTING else CarQuickControl.SetDefenseReminderSts.SOUND_AND_LIGHTING
            delayAndExecute { defenseReminder }
        }
    }

    fun defenseReminderUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_DEFENSE_REMINDER
        if (topic == 0x2) {
            status = 0
            defenseReminderLastUIState = 0
        } else if (topic == 0x3) {
            status = 1
            defenseReminderLastUIState = 1
        } else if (defenseReminderLastUIState != -1) {
            status = defenseReminderLastUIState
        }
        return status
    }

    fun setLeftChildLock(status: Int) {
        //显⽰名称：左⼉童锁 开关设置：解锁/闭锁 开关默认值：解锁
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-⻋辆控制界⾯提供左⼉童锁选项：⽤⼾点击左⼉童锁开关按键闭锁；
        //2. 在中控屏⻋辆设置-⻋辆控制界⾯提供左⼉童锁选项：⽤⼾点击左⼉童锁开关按键解锁；
        //执⾏输出（1||2）
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_ChildLockSW = 0x2:ChildLock_RL 给FLZCU，接着发送
        //0x0:Not Active，⼤屏左⼉童锁显⽰为闭锁，FLZCU 控制左后⼉童锁闭锁，计时 2s 若接收到状态反
        //馈信号 RL_ChildrenProtectSwitch =0x0:Locked，则左⼉童锁开关保持闭锁（开关⾼亮），否则
        //开关弹回⾮⾼亮；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ChildLockSW = 0x2:ChildLock_RL 给 FLZCU，接着发送
        //0x0:Not Active，⼤屏左⼉童锁显⽰为解锁，FZCU控制左后⼉童锁解锁，计时2s 若接收到状态反馈
        //信号 RL_ChildrenProtectSwitch=0x1:Unlocked，则左后⼉童锁开关保持解锁（开关⾮⾼亮），否
        //则开关弹回⾼亮；

        // ICC -> FLZCU/FRZCU 信号名：ICC_ChildLockSW
        // 0x0:Not Active
        // 0x1:Window
        // 0x2:ChildLock_RL
        // 0x3:ChildLock_RR

        // FLZCU -> ICC 信号名：RL_ChildrenProtectSwitch
        // 0x0:Locked
        // 0x1:Unlocked
        // 0x2:Superlocked

        // FRZCU -> ICC 信号名：RR_ChildrenProtectSwitch
        // 0x0:Locked
        // 0x1:Unlocked
        // 0x2:Superlocked

        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setLeftChildLock: 设置左⼉童锁:$status")
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.leftChildLock == CarQuickControl.GetLeftChildLockSts.UNLOCKED) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.leftChildLock == CarQuickControl.GetLeftChildLockSts.LOCKED) {
                return
            }
            quickManager.leftChildLock = CarQuickControl.SetChildLockSts.CHILD_LOCK_RL
            delayAndExecute { leftChildLock }
        }
    }

    fun leftChildLockUIState(topic: Int?): Int {
        var status = PrefsConst.DefaultValue.Q_LEFT_CHILD_LOCK
        if (topic == 0x0) {
            status = 1
            leftChildLockLastUiState = 1
        } else if (topic == 0x1) {
            status = 0
            leftChildLockLastUiState = 0
        } else if (leftChildLockLastUiState != -1) {
            status = leftChildLockLastUiState
        }
        return status
    }

    fun setRightChildLock(status: Int) {
        //显⽰名称：右⼉童锁 开关设置：解锁/闭锁 开关默认值：解锁
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击右⼉童锁开关按键闭锁；
        //2. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击右⼉童锁开关按键解锁；
        //执⾏输出（1||2）
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_ChildLockSW = 0x3:ChildLock_RL 给 FRZCU，接着发送
        //0x0:Not Active，⼤屏右⼉童锁显⽰为闭锁，FRZCU 控制右后⼉童锁闭锁，计时 2s 若接收到状态
        //反馈信号 RR_ChildrenProtectSwitch =0x0:Locked，则右⼉童锁开关保持闭锁（开关⾼亮），否
        //则开关弹回⾮⾼亮；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ChildLockSW = 0x3:ChildLock_RL 给 FRZCU，接着发送
        //0x0:Not Active，⼤屏右⼉童锁显⽰为解锁，FRZCU控制右后⼉童锁解锁，计时2s 若接收到状态反
        //馈信号 RR_ChildrenProtectSwitch=0x1:Unlocked，则右⼉童锁开关保持解锁（开关⾮⾼亮），
        //否则开关弹回⾼亮；


        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setRightChildLock: 设置右⼉童锁:$status")
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.rightChildLock == CarQuickControl.GetRightChildLockSts.UNLOCKED) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.rightChildLock == CarQuickControl.GetRightChildLockSts.LOCKED) {
                return
            }
            quickManager.rightChildLock = CarQuickControl.SetChildLockSts.CHILD_LOCK_RR
            delayAndExecute { rightChildLock }
        }
    }

    fun rightChildLockUIState(topic: Int?): Int {
        var status = PrefsConst.DefaultValue.Q_RIGHT_CHILD_LOCK
        if (topic == 0x0) {
            status = 1
            rightChildLockLastUiState = 1
        } else if (topic == 0x1) {
            status = 0
            rightChildLockLastUiState = 0
        } else if (rightChildLockLastUiState != -1) {
            status = rightChildLockLastUiState
        }
        return status
    }

    fun setAutomaticLocking(status: Int) {
        //显⽰名称：⾃动落锁 开关设置：开启/关闭 开关默认值：开启
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在⼤屏上打开⾃动落锁开关
        //2. 在⼤屏上关闭⾃动落锁开关
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_AutolockSts =0x1:Autolock mode 给 FLZCU，接着发 送
        //0x0:Not active ，FLZCU 控制⾃动落锁功能开启，计时 2s 若接收到状态反馈 信号
        //FLZCU_AutolockSts=0x1:Autolock mode，则⾃动落锁开关保持开启， 否则开关弹回关闭；
        //2. 若触发条件为 2，ICC 发送 ICC_AutolockSts=0x2:Not autolock mode 给 FLZCU，FLZCU 控制⾃
        //动落锁功能关闭, 计时 2s 若接收到状态反馈信号 FLZCU_AutolockSts=0x0:Not autolock mode，
        //则⾃动落锁开关保持关闭，否则开关弹回开启；
        //3. 若未进⾏开关设置，⾃动落锁开关接收FLZCU_AutolockSts对应信号显⽰对应开关状态，。

        // ICC -> FLZCU 信号名：ICC_AutolockSts
        // 0x0:Not Active
        // 0x1:Autolock mode
        // 0x2:Not autolock mode
        // 0x3:Not Used

        // FLZCU -> ICC 信号名：FLZCU_AutolockSts
        // 0x0:Not autolock mode
        // 0x1:Autolock mode

        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setAutomaticLocking: 设置⾃动落锁:$status")
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.automaticLocking == CarQuickControl.GetAutoLockSts.NOT_AUTOLOCK_MODE) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.automaticLocking == CarQuickControl.GetAutoLockSts.AUTOLOCK_MODE) {
                return
            }
            quickManager.automaticLocking =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetAutoLockSts.NOT_AUTOLOCK_MODE else CarQuickControl.SetAutoLockSts.AUTOLOCK_MODE
            delayAndExecute { automaticLocking }
        }
    }

    fun automaticLockingUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_AUTOMATIC_LOCKING
        if (topic == 0x0) {
            status = 0
            automaticLockingLastUIState = 0
        } else if (topic == 0x1) {
            status = 1
            automaticLockingLastUIState = 1
        } else if (automaticLockingLastUIState != -1) {
            status = automaticLockingLastUIState
        }
        return status
    }

    fun setAutomaticParkingUnlock(status: Int) {
        //显⽰名称：驻⻋⾃动解锁 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件： （1||2）
        //1. ⽤⼾在⼤屏上设置驻⻋⾃动解锁功能开关开启；
        //2. ⽤⼾在⼤屏上设置驻⻋⾃动解锁功能开关关闭；
        //执⾏输出：
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_ParkUnlockEnable=0x2:Enable 给 FLZCU，之后发送
        //0x0:Not Active，计时 2s 若检测到状态反馈信号 FLZCU_ParkUnlockEnableFb=0x1:Enable，则开
        //关保持开启，否则开关弹回关闭；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ParkUnlockEnable=0x1:Disable 给 FLZCU，之后发送
        //0x0:Not Active，计时 2s 若检测到状态反馈信号 FLZCU_ParkUnlockEnableFb= 0x0:Disable，则
        //开关保持关闭，否则开关弹回开启；

        // ICC -> FLZCU 信号名：ICC_ParkUnlockEnable
        // 0x0:Not Active
        // 0x1:Disable
        // 0x2:Enable
        // 0x3:Reserved

        // FLZCU -> ICC 信号名：FLZCU_ParkUnlockEnableFb
        // 0x0:Disable
        // 0x1:Enable

        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.automaticLocking == CarQuickControl.GetAutomaticParkUnlockSts.DISABLE) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.automaticLocking == CarQuickControl.GetAutomaticParkUnlockSts.ENABLE) {
                return
            }
            quickManager.automaticLocking =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetAutomaticParkUnlockSts.DISABLE else CarQuickControl.SetAutomaticParkUnlockSts.ENABLE
            delayAndExecute { automaticLocking }
        }
    }

    fun automaticParkingUnlockUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_AUTOMATIC_PARKING_UNLOCK
        if (topic == 0x0) {
            status = 0
            automaticParkingUnlockLastUIState = 0
        } else if (topic == 0x1) {
            status = 1
            automaticParkingUnlockLastUIState = 1
        } else if (automaticParkingUnlockLastUIState != -1) {
            status = automaticParkingUnlockLastUIState
        }
        return status
    }

    fun setSunshade(state: Int) {
        // 前置条件（a|(a&b)）
        //a. 电源模式(0x49D:FLZCU_9_PowerMode=0x1:Comfortable/0x2:ON)
        //b. 前遮阳帘处于关闭或其他状态时，(0x4D2: FRZCU_FShadSts=0x1:close或0x5:otherposition)。
        if (MsgUtil.getInstance().supportPowerMode()) {
            // 获取遮阳帘的状态 "0x0:Not Active
            //0x1:Autoopen
            //0x2:AutoClose
            //0x3:Stop"

            if (state == CarQuickControl.SetSunshadeSts.FRONT) {
                // 前排
                //0x0:Not Active
                //0x1:Close
                //0x2:Open
                //0x3:Closing
                //0x4:Opening
                //0x5:Other Position
                //0x6~0xF:Reserved
                val fstatus = quickManager.sunshadeFront
                Log.d(TAG, "setSunshade: 设置前遮阳帘的状态:$fstatus")
                if (fstatus == CarQuickControl.GetFrontSunshadeSts.CLOSE || fstatus == CarQuickControl.GetFrontSunshadeSts.OTHER_POSITION) {
                    // 打开 Autoopen
                    quickManager.sunshadeFront = CarQuickControl.SetFrontSunshadeSts.AUTO_OPEN
                } else if (fstatus == CarQuickControl.GetFrontSunshadeSts.CLOSING || fstatus == CarQuickControl.GetFrontSunshadeSts.OPENING) {
                    // stop
                    quickManager.sunshadeFront = CarQuickControl.SetFrontSunshadeSts.RESERVED
                } else if (fstatus == CarQuickControl.GetFrontSunshadeSts.OPEN) {
                    // ICC_FShadReq=0x2:Autoclose至FRZCU;
                    quickManager.sunshadeFront = CarQuickControl.SetFrontSunshadeSts.AUTO_CLOSE
                } else if (fstatus == CarQuickControl.GetFrontSunshadeSts.NOT_ACTIVE) {
                    // ICC_FShadReq=0x0:Not Active
                    quickManager.sunshadeFront = CarQuickControl.SetFrontSunshadeSts.NOT_ACTIVE
                }
            } else if (state == CarQuickControl.SetSunshadeSts.REAR) {
                // 后排
                val rstatus = quickManager.sunshadeRear
                Log.d(TAG, "setSunshade: 设置后遮阳帘的状态:$rstatus")
                if (rstatus == CarQuickControl.GetRearSunshadeSts.CLOSE || rstatus == CarQuickControl.GetRearSunshadeSts.OTHER_POSITION) {
                    // 打开 Autoopen
                    quickManager.sunshadeRear = CarQuickControl.SetRearSunshadeSts.AUTO_OPEN
                } else if (rstatus == CarQuickControl.GetRearSunshadeSts.CLOSING || rstatus == CarQuickControl.GetRearSunshadeSts.OPENING) {
                    // stop
                    quickManager.sunshadeRear = CarQuickControl.SetRearSunshadeSts.RESERVED
                } else if (rstatus == CarQuickControl.GetRearSunshadeSts.OPEN) {
                    // ICC_FShadReq=0x2:Autoclose至FRZCU;
                    quickManager.sunshadeRear = CarQuickControl.SetRearSunshadeSts.AUTO_CLOSE
                } else if (rstatus == CarQuickControl.GetRearSunshadeSts.NOT_ACTIVE) {
                    // ICC_FShadReq=0x0:Not Active
                    quickManager.sunshadeRear = CarQuickControl.SetRearSunshadeSts.NOT_ACTIVE
                }
            }
        }
    }

    fun sunshadeUIState(topic: Int): Int {
        // todo 待处理
        return -1
    }

    fun setAutoTail(state: Int) {
        // ICC_SpoilerCtrlCmd 设置电动尾翼 BODYINFO_SPOILERSWITCH_SET
        // "0x0:Not Active
        //0x1:Auto
        //0x2:Open Level1
        //0x3:Open Level2
        //0x4:Close
        //0x5~0x7:Reserved"
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (state == CarQuickControl.AutoTailUIState.CLOSE && quickManager.spoilerSwitch == CarQuickControl.GetAutoTailSts.CLOSE) {
                return
            }
            if (state == CarQuickControl.AutoTailUIState.OPEN_LEVEL_1 && quickManager.spoilerSwitch == CarQuickControl.GetAutoTailSts.OPEN_LEVEL_1) {
                return
            }
            if (state == CarQuickControl.AutoTailUIState.OPEN_LEVEL_2 && quickManager.spoilerSwitch == CarQuickControl.GetAutoTailSts.OPEN_LEVEL_2) {
                return
            }
            if (state == CarQuickControl.AutoTailUIState.AUTO && quickManager.spoilerSwitch == CarQuickControl.GetAutoTailSts.AUTO) {
                return
            }
            quickManager.oilLidSwitch =
                if (state == CarQuickControl.AutoTailUIState.CLOSE) CarQuickControl.SetAutoTailSts.CLOSE
                else if (state == CarQuickControl.AutoTailUIState.OPEN_LEVEL_1) CarQuickControl.SetAutoTailSts.OPEN_LEVEL_1
                else if (state == CarQuickControl.AutoTailUIState.OPEN_LEVEL_2) CarQuickControl.SetAutoTailSts.OPEN_LEVEL_2
                else CarQuickControl.SetAutoTailSts.AUTO
            delayAndExecute { autoTail }
        }
    }

    fun autoTailUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_AUTO_TAIL
        if (topic == 0x1) {
            status = 0
            autoTailLastUIState = 0
        } else if (topic == 0x2) {
            status = 1
            autoTailLastUIState = 1
        } else if (topic == 0x3) {
            status = 2
            autoTailLastUIState = 2
        } else if (topic == 0x4) {
            status = 3
            autoTailLastUIState = 3
        } else if (autoTailLastUIState != -1) {
            status = autoTailLastUIState
        }
        return status
    }

    fun setApproachingUnlock(state: Int) {
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (state == CarQuickControl.ButtonSts.OFF && quickManager.approachingUnlock == CarQuickControl.GetApproachingUnlockSts.OFF) {
                return
            }
            if (state == CarQuickControl.ButtonSts.ON && quickManager.approachingUnlock == CarQuickControl.GetApproachingUnlockSts.ON) {
                return
            }
            quickManager.approachingUnlock =
                if (state == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetApproachingUnlockSts.OFF else CarQuickControl.SetApproachingUnlockSts.ON
            delayAndExecute { approachingUnlock }
        }
    }

    fun approachingUnlockUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_APPROACHING_UNLOCK
        if (topic == 0x0) {
            status = 0
            approachingUnlockLastUiState = 0
        }
        if (topic == 0x1) {
            status = 1
            approachingUnlockLastUiState = 1
        } else if (approachingUnlockLastUiState != -1) {
            status = approachingUnlockLastUiState
        }
        return status
    }

    fun setDepartureLocking(state: Int) {
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；

        // 若触发条件为 1，ICC 连续发送三帧 ICC_WALOpenSetCmd=0x1: On 给 FLZCU，之后发送0x0:Not Active，计时 2s
        // 若检测到状态反馈信号 FLZCU_WALOpenStas=0x1:ON，则开关保持开启，否则开关弹回关闭；

        // 若触发条件为 2，ICC 连续发送三帧 ICC_WALOpenSetCmd=0x2: Off 给 FLZCU 之后发送 0x0:NotActive，计时 2s
        // 若检测到状态反馈信号 FLZCU_WALOpenStas =0x0:OFF，则开关保持关闭，否则开关弹回开启；

        if (MsgUtil.getInstance().supportPowerMode()) {
            if (state == CarQuickControl.ButtonSts.OFF && quickManager.departureLocking == CarQuickControl.GetDepartureLockingSts.OFF) {
                return
            }
            if (state == CarQuickControl.ButtonSts.ON && quickManager.departureLocking == CarQuickControl.GetDepartureLockingSts.ON) {
                return
            }
            quickManager.departureLocking =
                if (state == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetDepartureLockingSts.OFF else CarQuickControl.SetDepartureLockingSts.ON
            delayAndExecute { departureLocking }
        }
    }

    fun departureLockingUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_DEPARTURE_LOCKING
        if (topic == 0x0) {
            status = 0
            departureLockingLastUiState = 0
        } else if (topic == 0x1) {
            status = 1
            departureLockingLastUiState = 1
        } else if (departureLockingLastUiState != -1) {
            status = departureLockingLastUiState
        }
        return status
    }

    fun setLockAutoRaiseWindow(status: Int) {
        //显⽰名称：锁⻋⾃动升窗 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-点击开
        //2. 在中控屏⻋辆设置-点击关
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC 发送 ICC_LockCarWinCloseSw = 0x1：ON； ，然后发送
        //0x0:Not Active 给 FLZCU，⼤屏锁⻋升窗显⽰为开，计时 2s 若检测到状态反馈信号
        //FLZCU_LockCarWinCloseFb = 0x0:Enable，则⼤屏锁⻋升窗显⽰保持为开状态， 否则弹回关状
        //态；
        //2. 若触发条件为 2，ICC 连续发送三帧ICC_LockCarWinCloseSw= 0x2：OFF，然后发送 0x0:Not
        //Active 给 FLZCU，⼤屏锁⻋升窗显⽰为关，计时 2s 若检测到状态反馈信号
        //FLZCU_LockCarWinCloseFb = 0x1:Disable，则⼤屏锁⻋升窗显⽰保持为关状态，否则弹回开状
        //态；

        // ICC -> FLZCU 信号名：ICC_LockCarWinCloseSw
        // 0x0:Not Active
        // 0x1:ON
        // 0x2:OFF
        // 0x3:Reserved

        // FLZCU -> ICC 信号名：FLZCU_LockCarWinCloseFb
        // 0x0:Enable
        // 0x1:Disable

        Log.d(TAG, "setLockAutoRaiseWindow: 设置锁⻋⾃动升窗开关状态:$status")
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.lockAutoRaiseWindow == CarQuickControl.GetLockAutoRaiseWindowSts.DISABLE) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.lockAutoRaiseWindow == CarQuickControl.GetLockAutoRaiseWindowSts.ENABLE) {
                return
            }
            quickManager.lockAutoRaiseWindow =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetLockAutoRaiseWindowSts.OFF else CarQuickControl.SetLockAutoRaiseWindowSts.ON
            delayAndExecute { lockAutoRaiseWindow }
        }
    }

    fun lockAutoRaiseWindowUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_LOCK_AUTO_RAISE_WINDOW
        if (topic == 0x1) {
            status = 0
            lockAutoRaiseWindowLastUIState = 0
        } else if (topic == 0x0) {
            status = 1
            lockAutoRaiseWindowLastUIState = 1
        } else if (lockAutoRaiseWindowLastUIState != -1) {
            status = lockAutoRaiseWindowLastUIState
        }
        return status
    }

    fun setAutoRearMirrorFold(status: Int) {
        //显⽰名称：外后视镜⾃动折叠 开关设置：开启/关闭 开关默认值：开启
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 点击⼤屏上的软开关使外后视镜⾃动折叠功能开启；
        //2. 点击⼤屏上的软开关使外后视镜⾃动折叠功能关闭；
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_AutoFoldSts=0x1:Autofold mode 给 FLZCU， FLZCU控
        //制外后视镜⾃动折叠功能开启，之后发送 0x0:Not active，计时 2s 若检测到 状态反馈信号
        //FLZCU_AutoFoldSts=0x1:Autofold mode，则开关保持开启， 否则开关弹回关闭；
        //2. 若触发条件为 2，ICC 发送 ICC_AutoFoldSts=0x2:Not autofold mode 给 FLZCU， FLZCU控制外
        //后视镜⾃动折叠功能关闭，之后发送 0x0:Not active，计时 2s 若检测到状态 反馈信号
        //FLZCU_AutoFoldSts =0x0:Not autofold mode，则开关保持关闭， 否则开关弹回开启；

        // ICC -> FLZCU 信号名：IIC_AutoFoldSts
        // 0x0：Not Active
        // 0x1：Autofold mode
        // 0x2：Not autofold mode
        // 0x3：Not used

        // FLZCU -> ICC 信号名：FLZCU_AutoFoldSts
        // 0x0：Not Autofold
        // 0x1：Autofold

        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.autoRearMirrorFold == CarQuickControl.GetAutoRearMirrorFoldSts.NOT_AUTO_FOLD) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.autoRearMirrorFold == CarQuickControl.GetAutoRearMirrorFoldSts.AUTO_FOLD) {
                return
            }
            quickManager.autoRearMirrorFold =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetAutoRearMirrorFoldSts.NOT_AUTO_FOLD_MODE else CarQuickControl.SetAutoRearMirrorFoldSts.AUTO_FOLD_MODE
            delayAndExecute { autoRearMirrorFold }
        }
    }

    fun autoRearMirrorFoldUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_REAR_MIRROR_FOLD
        if (topic == 0x0) {
            status = 0
            rearMirrorFoldLastUiState = 0
        } else if (topic == 0x1) {
            status = 1
            rearMirrorFoldLastUiState = 1
        } else if (rearMirrorFoldLastUiState != -1) {
            status = rearMirrorFoldLastUiState
        }
        return status
    }

    fun setAutoHotRearMirror(status: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            //若触发条件为 1， ICC 连续发送三帧 ICC_AutoHeatingset =0x1:ON 给 FLZCU， FLZCU 控制开启雨天自动加热外后视镜功能，接着发送 0x0:Not Active，计时 2s 若接收到状态 反馈信号FLZCU_AutoHeatingFb = 0x1:Open，则雨天自动加热外后视镜开关保持开启，否则开关弹回关闭；2. 若触发条件为 2，ICC 连续发送三帧 ICC_AutoHeatingset=0x2:OFF 给FLZCU， FLZCU 控制关闭 雨天自动加热外后视镜功能，接着发送 0x0:Not Active，计时 2s 若接收到状态 反馈信号FLZCU_AutoHeatingFb = 0x0:Close，则雨天自动加热外后视镜开关保持关闭，否则开关弹回开启；
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.autoHotRearMirror == CarQuickControl.GetAutoHeatingRearMirrorSts.CLOSE) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.autoHotRearMirror == CarQuickControl.GetAutoHeatingRearMirrorSts.OPEN) {
                return
            }
            quickManager.autoHotRearMirror =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetAutoHeatingRearMirrorSts.OFF else CarQuickControl.SetAutoHeatingRearMirrorSts.ON
            delayAndExecute { autoHotRearMirror }
        }
    }

    fun autoHotRearMirrorUIState(topic: Int?): Int {
        var status = PrefsConst.DefaultValue.Q_AUTO_HOT_REAR_MIRROR
        if (topic == 0x0) {
            status = 0
            autoHotRearMirrorLastUIState = 0
        } else if (topic == 0x1) {
            status = 1
            autoHotRearMirrorLastUIState = 1
        } else if (autoHotRearMirrorLastUIState != -1) {
            status = autoHotRearMirrorLastUIState
        }
        return status
    }

    fun setSeatPortable(status: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.seatPortable == CarQuickControl.GetSeatPortableSts.OFF) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.seatPortable == CarQuickControl.GetSeatPortableSts.ON) {
                return
            }
            quickManager.seatPortable =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetSeatPortableSts.OFF else CarQuickControl.SetSeatPortableSts.ON
            delayAndExecute { seatPortable }
        }
    }

    fun seatPortableUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_SEAT_PORTABLE
        if (topic == 0x1) {
            status = 1
            seatPortableLastUIState = 1
        } else if (topic == 0x2) {
            status = 0
            seatPortableLastUIState = 0
        } else if (seatPortableLastUIState != -1) {
            status = seatPortableLastUIState
        }
        return status
    }

    fun sendMirrorAdjust() {
        // ICC 发送 ICC_MultiplexSignalStatusSet = 0x1:Mirror 给 MFS；
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "sendMirrorAdjuse: 发送后视镜调节命令:" + 0x1)
            quickManager.rearMirrorAdjust = CarQuickControl.SetMultiplexSignalStatusSts.MIRROR
            delayAndExecute { rearMirrorAdjust }
        }
    }

    fun mirrorAdjustUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_BACK_AUTO_REAR_MIRROR_ADJUST_WINDOW_STATE
        if (topic == 0x1) {
            status = 1
            rearMirrorAdjustLastUIState = 1
        } else if (topic == 0x0) {
            status = 0
            rearMirrorAdjustLastUIState = 0
        } else if (rearMirrorAdjustLastUIState != -1) {
            status = rearMirrorAdjustLastUIState;
        }
        return status
    }

    /**
     * 倒车时后视镜自动调节
     *
     * @param state
     */
    fun setBackAutoRearMirrorAdjust(state: Int) {
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (state == CarQuickControl.BackAutoRearMirrorAdjustUIState.OFF && quickManager.backRearAdjust == CarQuickControl.GetBackAutoRearMirrorAdjustSts.OFF) {
                return
            }
            if (state == CarQuickControl.BackAutoRearMirrorAdjustUIState.BOTH_SIDES && quickManager.backRearAdjust == CarQuickControl.GetBackAutoRearMirrorAdjustSts.BOTH_SIDES) {
                return
            }
            if (state == CarQuickControl.BackAutoRearMirrorAdjustUIState.ONLY_LEFT_SIDE && quickManager.backRearAdjust == CarQuickControl.GetBackAutoRearMirrorAdjustSts.ONLY_LEFT_SIDE) {
                return
            }
            if (state == CarQuickControl.BackAutoRearMirrorAdjustUIState.ONLY_RIGHT_SIDE && quickManager.backRearAdjust == CarQuickControl.GetBackAutoRearMirrorAdjustSts.ONLY_RIGHT_SIDE) {
                return
            }
            quickManager.backRearAdjust =
                if (state == CarQuickControl.BackAutoRearMirrorAdjustUIState.OFF) CarQuickControl.SetBackAutoRearMirrorAdjustSts.OFF
                else if (state == CarQuickControl.BackAutoRearMirrorAdjustUIState.ONLY_LEFT_SIDE) CarQuickControl.SetBackAutoRearMirrorAdjustSts.ONLY_LEFT_SIDE
                else if (state == CarQuickControl.BackAutoRearMirrorAdjustUIState.ONLY_RIGHT_SIDE) CarQuickControl.SetBackAutoRearMirrorAdjustSts.ONLY_RIGHT_SIDE
                else CarQuickControl.SetBackAutoRearMirrorAdjustSts.BOTH_SIDES
            delayAndExecute { backRearAdjust }
        }
    }

    fun backAutoRearMirrorAdjustUIState(topic: Int?): Int {
        var status = PrefsConst.DefaultValue.Q_BACK_AUTO_REAR_MIRROR_ADJUST
        if (topic == 0x0) {
            status = 0
            backRearAdjustLastUIState = 0
        } else if (topic == 0x3) {
            status = 1
            backRearAdjustLastUIState = 1
        } else if (topic == 0x2) {
            status = 2
            backRearAdjustLastUIState = 2
        } else if (topic == 0x1) {
            status = 3
            backRearAdjustLastUIState = 3
        } else if (backRearAdjustLastUIState != -1) {
            status = backRearAdjustLastUIState
        }
        return status
    }

    fun setVehiclePowerOff(state: Int) {
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (state == 1) {
                // 发送下线 BODYINFO_POWEROFF_SET
                Log.d(TAG, "setVehiclePowerOff: 车辆下电 发送下线命令:" + 0x1)
                quickManager.vehiclePowerOff = 0x1
            }
        }
    }

    fun getVehiclePowerOff(): Int {
        return quickManager.vehiclePowerOff
    }

    fun setRefuelSmallDoor(state: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setRefuelSmallDoor: 设置加油小门:$state")
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.EXTENDEDRANGE_OILLIDSWITCH_SET, state);
            if (state == CarQuickControl.ButtonSts.OFF && quickManager.oilLidSwitch == CarQuickControl.GetRefuelSmallDoorSts.CLOSE) {
                return
            }
            if (state == CarQuickControl.ButtonSts.ON && quickManager.seatPortable == CarQuickControl.GetRefuelSmallDoorSts.OPEN) {
                return
            }
            quickManager.oilLidSwitch =
                if (state == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetRefuelSmallDoorSts.NO_REQUEST else CarQuickControl.SetRefuelSmallDoorSts.REQUEST
        }
    }

    fun refuelSmallDoorUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_REFUEL_SMALL_DOOR
        if (topic == 0x0) {
            status = 0
            refuelSmallDoorLastUIState = 0
        } else if (topic == 0x1) {
            status = 1
            refuelSmallDoorLastUIState = 1
        } else if (refuelSmallDoorLastUIState != -1) {
            status = refuelSmallDoorLastUIState
        }
        return status
    }

    fun setRearScreenControl(status: Int) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setRearScreenControl: 后屏控制锁: $status")
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.rearScreenControl == CarQuickControl.GetRearScreenControlSts.CLOSE) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.rearScreenControl == CarQuickControl.GetRearScreenControlSts.OPEN) {
                return
            }
            quickManager.rearScreenControl =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetRearScreenControlSts.CLOSE else CarQuickControl.SetRearScreenControlSts.OPEN
            delayAndExecute { rearScreenControl }
        }
    }

    fun rearScreenControlUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_REAR_SCREEN_CONTROL
        if (topic == 0x1) {
            status = 1
            rearScreenControlLastUIState = 1
        } else if (topic == 0x0) {
            status = 0
            rearScreenControlLastUIState = 0
        } else if (rearScreenControlLastUIState != -1) {
            status = rearScreenControlLastUIState
        }
        return status
    }

    fun setWiperSens(status: Int) {
        //显⽰名称：⾬刮灵敏度 开关设置： 低、标准、⾼、最⾼ 开关默认值：⾼
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2||3||4）
        //1. 在⼤屏上点击⾬刮灵敏调节设置项为 低；
        //2. 在⼤屏上点击⾬刮灵敏调节设置项为 标准；
        //3. 在⼤屏上点击⾬刮灵敏调节设置项为 ⾼；
        //4. 在⼤屏上点击⾬刮灵敏调节设置项为 最⾼；
        //执⾏输出（1||2||3||4）
        //1. 若触发条件 1，ICC 连续发送三帧 ICC_WiprSnvty = 0x1:Level 1，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 低，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x1:Level 1，则⾬刮灵敏调节设置 项保持 level1，否则开关根据收
        //到的信号显⽰对应状态；
        //2. 若触发条件 2，ICC 连续发送三帧 ICC_WiprSnvty = 0x2:Level 2 ，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 标准，计时2s若检测到状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x2:Level 2，则⾬刮灵敏调节设置 项保持 level2，否则开关根据收
        //到的信号显⽰对应状态；
        //3. 若触发条件 3，ICC 连续发送三帧 ICC_WiprSnvty = 0x3:Level 3，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 ⾼，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x3:Level 3，则⾬刮灵敏调节设置 项保持 level3，否则开关根据收
        //到的信号显⽰对应状态；
        //4. 若触发条件 4，ICC 连续发送三帧 ICC_WiprSnvty = 0x4:Level 4，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 最⾼，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x4:Level4，则⾬刮灵敏调节设置 项保持 level4，否则开关根据收到
        //的信号显⽰对应状态；
        //5. 若未进⾏开关设置，FLZCU_WipeSensitivitySts对应等级信号发⽣变化，⾬刮灵敏调节设置为对应
        //等级。

        // ICC -> FLZCU 信号名：ICC_WiprSnvty
        // 0x0:Not Active
        // 0x1:Level 1
        // 0x2:Level 2
        // 0x3:Level 3
        // 0x4:Level 4
        // 0x5:Reserved
        // 0x6:Reserved

        // FLZCU -> ICC 信号名：FLZCU_WipeSensitivitySts
        // 0x0:Not Active
        // 0x1:Level 1
        // 0x2:Level 2
        // 0x3:Level 3
        // 0x4:Level 4
        // 0x5~0x7:Reserved

        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setCentralLocking: 发送雨刮器灵敏度:$status")
            if (status == CarQuickControl.WipeSensitivityUIState.LEVEL_1 && quickManager.wiperSensitivity == CarQuickControl.GetWipeSensitivitySts.LEVEL_1) {
                return
            }
            if (status == CarQuickControl.WipeSensitivityUIState.LEVEL_2 && quickManager.wiperSensitivity == CarQuickControl.GetWipeSensitivitySts.LEVEL_2) {
                return
            }
            if (status == CarQuickControl.WipeSensitivityUIState.LEVEL_3 && quickManager.wiperSensitivity == CarQuickControl.GetWipeSensitivitySts.LEVEL_3) {
                return
            }
            if (status == CarQuickControl.WipeSensitivityUIState.LEVEL_4 && quickManager.wiperSensitivity == CarQuickControl.GetWipeSensitivitySts.LEVEL_4) {
                return
            }
            quickManager.wiperSensitivity =
                if (status == CarQuickControl.WipeSensitivityUIState.LEVEL_1) CarQuickControl.SetWiperSensitivitySts.LEVEL_1
                else if (status == CarQuickControl.WipeSensitivityUIState.LEVEL_2) CarQuickControl.SetWiperSensitivitySts.LEVEL_2
                else if (status == CarQuickControl.WipeSensitivityUIState.LEVEL_3) CarQuickControl.SetWiperSensitivitySts.LEVEL_3
                else CarQuickControl.SetWiperSensitivitySts.LEVEL_4
            delayAndExecute { wiperSensitivity }
        }
    }

    fun wiperSensUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_WIPER_SENS
        if (topic == 0x1) {
            status = 0
            wiperSensitivityLastUIState = 0
        } else if (topic == 0x2) {
            status = 1
            wiperSensitivityLastUIState = 1
        } else if (topic == 0x3) {
            status = 2
            wiperSensitivityLastUIState = 2
        } else if (topic == 0x4) {
            status = 3
            wiperSensitivityLastUIState = 3
        } else if (wiperSensitivityLastUIState != -1) {
            status = wiperSensitivityLastUIState
        }
        return status
    }

    fun setHudRoate(status: Int) {
        val value = status + 50

        //显⽰名称：后尾⻔开启⾼度设置 开关设置：50%~95% 开关默认值：95%
        //配置：PLG 配置
        //前置条件：
        //1. 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件
        //1. 在中控屏设置后尾⻔开启⾼度（50%~95%），ICC 发送 ICC_Set_PLGOperateSts 给 PLG，⾄少发
        //送三帧之后发送 0x0:Not Active 给 FLZCU；
        //执⾏输出（1||2||3）
        //1. ICC 收到 PLG_Set_PLGOperateStsFb≤50，后尾⻔开启⾼度显⽰ 50%；
        //2. ICC 收到 PLG_Set_PLGOperateStsFb= 51-95，后尾⻔开启⾼度显⽰对应信号值 51%~95% ；
        //3. ICC 收到 PLG_Set_PLGOperateStsFb≥95，后尾⻔开启⾼度显⽰ 95%；
        //备注：
        //1. ICC显⽰界⾯设置为⽤⼾选择的状态，并在2秒后同步总线反馈的状态，否则返回上⼀设置值。

        // ICC -> PLG 信号名：ICC_Set_PLGOperateSts
        // 0x0:Not Active
        // 0x~0x65:0%~100%
        Log.d(TAG, "setHudRoate: 设置后尾门高度:$value")
        if (MsgUtil.getInstance().supportPowerMode()) {
            quickManager.hudRoate = value
            delayAndExecute { hudRoate }
        }
    }

    fun hudRoateUIState(topic: Int): Int {
        var value = PrefsConst.DefaultValue.Q_REAR_TAILGATE_ROATE
        if (topic in 0..49) {
            value = 50
            hudRoateLastUIState = 50
        } else if (topic in 96..100) {
            value = 95
            hudRoateLastUIState = 95
        } else if (topic in 50..95) {
            value = topic
        } else if (hudRoateLastUIState != -1) {
            value = hudRoateLastUIState
        }
        return value - 50
    }

    fun setDriveAirBag(status: Int) {
        // 显示名称：副驾安全气囊 开关设置：开启/关闭 开关默认值：开启
        // 开关设置：
        // 前置条件：
        // 电源模式:ON档，(信号：FLZCU_9_PowerMode=ON)；
        // 触发条件：（1||2）
        // 1. 在中控屏车辆设置-：副驾安全气囊，点击打开
        // 2. 在中控屏车辆设置-：副驾安全气囊，点击关闭
        // 执行输出：（1||2）
        // 1. 若触发条件为 1， ICC 连续发送三帧 ICC_PABSetCmd = 0x2: PAB ON Cmd 给 ACU，ACU 控制副
        // 驾气囊 PAB 开启，接着发送 0x0:Not Active ，计时 2s 若收到状态反馈
        // ABM_1_PABSetSts=0x1:PAB ON，则副驾气囊屏蔽开关保持开启；
        // 2. 若触发条件为 2， 弹框提醒“您是否确定要停用前排乘客安全气囊？”，点击确认后ICC发送
        //         ICC_PABSetCmd = 0x1: PAB OFF Cmd 给 ACU，ACU 控制副驾气囊 PAB 关闭，接着发送 0x0:NotActive ，
        // 计时 2s 若收到状态反馈ABM_1_PABSetSts=0x0:PAB OFF，则副驾气囊屏蔽开关保持关闭
        // 副驾气囊 PAB 状态栏图标显示
        // 前置条件：
        // 电源状态 ON 档(信号：FLZCU_9_PowerMode=0x2:ON)；
        // 触发条件：（1||2||3||4）
        // 1. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x0: 'PAB ON' lamp on”；
        // 2. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x1: 'PAB OFF' lamp on”；
        // 3. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x2: no lamp on”；
        // 4. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x3: Both lamp on”；
        // 执行输出：（1||2||3||4）
        // 1. 若触发条件 1，点亮副驾安全气囊开启指示灯，并在指示灯上方显示 “Passenger Airbag”字样；
        // 2. 若触发条件 2，点亮副驾安全气囊关闭指示灯 ，并在指示灯上方显示 “Passenger Airbag”字样 ；
        // 3. 若触发条件 3，熄灭副驾安全气囊指示灯，同时不再显示“Passenger Airbag”字样。
        // 4. 若触发条件 4，同时点亮副驾安全气囊开启和关闭指示灯，同时不再显示“Passenger Airbag”字样；
        // 异常处理：失去通讯，保持当前状态。
        // 信号描述：
        // ICC -> ACU 信号名：ICC_PABSetCmd
        // 0x0:NotActive
        // 0x1:PAB OFF
        // 0x2:PAB ON
        //
        // ACU -> ICC 信号名：ABM_1_PABSetSts
        // 0x0:PAB OFF
        // 0x1:PAB ON
        //
        // ACU -> ICC 信号名：ACU_1_PsngrBagSts
        // 0x0:'PAB ON' lamp on
        // 0x1:'PAB OFF' lamp on
        // 0x2:no pamp on
        // 0x3:Both lamp on
        Log.d(TAG, "setSwDriveAirBag: 开关驾驶安全气囊:$status")
        // 电源模式:ON档，(信号：FLZCU_9_PowerMode=ON)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == CarQuickControl.ButtonSts.OFF && quickManager.driveAirbag == CarQuickControl.GetDriveAirbagSts.PAB_OFF) {
                return
            }
            if (status == CarQuickControl.ButtonSts.ON && quickManager.driveAirbag == CarQuickControl.GetDriveAirbagSts.PAB_ON) {
                return
            }
            quickManager.driveAirbag =
                if (status == CarQuickControl.ButtonSts.OFF) CarQuickControl.SetDriveAirbagSts.PAB_OFF else CarQuickControl.SetDriveAirbagSts.PAB_ON
            delayAndExecute { driveAirbag }
        }
    }

    fun driveAirBagUIState(topic: Int): Int {
        var status = PrefsConst.DefaultValue.Q_DRIVE_AIR_BAG
        if (topic == 0x0) {
            status = 0
            driveAirbagLastUIState = 0
        } else if (topic == 0x1) {
            status = 1
            driveAirbagLastUIState = 1
        } else if (driveAirbagLastUIState != -1) {
            status = driveAirbagLastUIState
        }
        return status
    }

    fun setCarSpeed(v: Int) {
        Log.d(TAG, "setCarSpeed: 设置车速:$v")
        if (v == -1) {
            Log.d(TAG, "setCarSpeed: 车速设置失败")
            return
        }
        Prefs.put(PrefsConst.C_CAR_SPEED, v)
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_CAR_SPEED, v)
        Log.d(TAG, "setCarSpeed: 车速设置成功$v")
    }

    fun getCarSpeed(): Int {
        return quickManager.carSpeed
    }

    fun setCustomButton(state: Int) {
        // 设置自定义按键类型
        // 1 车辆设置, 2 DVR, 3 HUD, 4 方向盘调节HUD, 5 后视镜调节, 6 音源切换, 7 返回, 8 AVM
        Prefs.put(PrefsConst.C_CUSTOM_BUTTON, state)
        var customStatus = 1
        if (state == 0) {
            // 行车记录仪抓拍
            customStatus = 2
        } else if (state == 1) {
            // AVM进入/退出
            customStatus = 8
        } else if (state == 2) {
            // 方向盘调节HUD
            customStatus = 4
        } else if (state == 3) {
            // 后视镜调节
            customStatus = 5
        } else if (state == 4) {
            // 音源切换
            customStatus = 6
        } else if (state == 5) {
            // 方向盘调节
            // todo 暂无对应按键
//            customStatus = 3;
        } else if (state == 6) {
            // 全息影像
            // todo 暂无对应按键
//            customStatus = 1;
        }
        Log.d(TAG, "setCustomButton: 设置自定义按键类型: $state === $customStatus")
        Settings.System.putInt(mContext!!.contentResolver, "custom_input_event_1", customStatus)
    }

    fun getCustomButton(): Int {
        val status = Prefs.get(PrefsConst.C_CUSTOM_BUTTON, PrefsConst.DefaultValue.C_CUSTOM_BUTTON)
        Log.d(TAG, "getCustomButton: 获取自定义按键类型 $status")
        return status
    }

    /**
     * 延迟执行方法
     * @param delayTime 延迟时间，单位为毫秒
     * @param block 延迟后要执行的函数
     */
    fun delayAndExecute(delayTime: Long = 2000L, block: suspend () -> Unit) {
        viewModelScope.launch(Dispatchers.Default) {
            delay(delayTime)
            block()
        }
    }
}