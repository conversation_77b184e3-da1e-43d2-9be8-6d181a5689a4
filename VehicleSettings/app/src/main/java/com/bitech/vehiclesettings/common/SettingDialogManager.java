package com.bitech.vehiclesettings.common;

import android.content.Context;
import android.content.Intent;
import android.provider.Settings;

import com.bitech.vehiclesettings.utils.PFLog;

/**
 * @ClassName: SettingDialogManager
 * 
 * @Date: 2024/6/7 14:32
 * @Description: 车辆中心相关弹窗管理.
 **/
public class SettingDialogManager {

    // 日志标志位
    public static final String TAG = "SettingDialogManager";

    // 打断或恢复息屏模式广播action
    public final static String ACTION_SETTING_INTERRUPT_DIALOG_STANDBY
            = "action.settings.interrupt.standby.dialog";

    // 打断或恢复息屏模式系统属性KEY
    public final static String SETTING_INTERRUPT_DIALOG_STANDBY_KEY
            = "com.chery.settings.interrupt.standby.dialog";

    // 打断或恢复息屏模式广播action
    public final static String ACTION_SETTING_INTERRUPT_DIALOG_SCREEN_CLEAR
            = "action.settings.interrupt.screen.clear.dialog";

    // 打断或恢复屏幕清洁模式系统属性KEY
    public final static String SETTING_INTERRUPT_DIALOG_SCREEN_CLEAR_KEY
            = "com.chery.settings.interrupt.screen.clear.dialog";

    // 息屏模式霸屏广播action
    public final static String ACTION_SETTING_SCREEN_DOMINATION
            = "action.settings.screen.domination";

    // 清洁模式霸屏广播action
    public final static String ACTION_SETTING_CLEANING_MODE_DOMINATION
            = "action.settings.cleaning.mode.domination";

    // 息屏模式或清洁模式霸屏广播KEY  1->在霸屏中，0->不在霸屏中
    public final static String SETTING_SCREEN_DOMINATION_KEY
            = "com.chery.settings.screen.domination";

    // 当前是否处于息屏模式 1->在息屏模式，0->不在息屏模式状态
    public final static String IS_STANDBY_MODE = "com.chery.settings.is.standby.mode";

    // 当前是否处于屏幕清洁模式 1->处于屏幕清洁模式，0->不在屏幕清洁模式
    public final static String IS_SCREEN_CLEAR_MODE = "com.chery.settings.is.screen.clear.mode";

    /**
     * @ClassName: KEY
     * 
     * @Date: 2024/6/7 14:36
     * @Description: 打断或恢复相关弹窗事件.
     **/
    public static class KEY {

        // 恢复相关模式弹窗
        public final static int DIALOG_RECOVER = 1;

        // 打断相关模式弹窗
        public final static int DIALOG_INTERRUPT = 0;

        // 打断或恢复相关模式默认值
        public final static int DIALOG_DEFAULT = DIALOG_INTERRUPT;

        // 处于相关模式
        public final static int MODE_TRUE = 1;

        // 不处于相关模式
        public final static int MODE_FALSE = 0;
    }

    /**
     * 当前是否处于息屏模式.
     *
     * @param context 环境上下文
     * @return boolean
     */
    public static boolean isStandbyMode(Context context) {
        // 获取是否处于息屏模式状态
        int isStandbyMode = Settings.System.getInt(context.getContentResolver(),
                IS_STANDBY_MODE,
                KEY.MODE_FALSE);
        PFLog.i(TAG, "isStandbyMode : isStandbyMode = " + isStandbyMode);
        return isStandbyMode == KEY.MODE_TRUE;
    }

    /**
     * 当前是否处于屏幕清洁模式.
     *
     * @param context 环境上下文
     * @return boolean
     */
    public static boolean isScreenClearMode(Context context) {
        // 获取是否处于息屏模式状态
        int isScreenClearMode = Settings.System.getInt(context.getContentResolver(),
                IS_SCREEN_CLEAR_MODE,
                KEY.MODE_FALSE);
        PFLog.i(TAG, "isScreenClearMode : isScreenClearMode = " + isScreenClearMode);
        return isScreenClearMode == KEY.MODE_TRUE;
    }

    /**
     * 临时打断息屏模式.
     *
     * @param context 环境上下文
     */
    public static void interruptStandbyMode(Context context) {
        PFLog.d(TAG, "interruptStandbyMode : ");
        if (isStandbyMode(context)) {
            // 当前处于息屏模式,则临时打断息屏模式
            Intent intent =new Intent(ACTION_SETTING_INTERRUPT_DIALOG_STANDBY);
            intent.putExtra(
                    SETTING_INTERRUPT_DIALOG_STANDBY_KEY,
                    KEY.DIALOG_INTERRUPT
            );
            context.sendBroadcast(intent);
            PFLog.d(TAG, "interruptStandbyMode : sendBroadcast ");
        }
    }

    /**
     * 临时打断屏幕清洁模式.
     *
     * @param context 环境上下文
     */
    public static void interruptScreenClearMode(Context context) {
        PFLog.d(TAG, "interruptScreenClearMode : ");
        if (isScreenClearMode(context)) {
            // 当前处于屏幕清洁模式,则临时打断屏幕清洁模式
            Intent intent =new Intent(ACTION_SETTING_INTERRUPT_DIALOG_SCREEN_CLEAR);
            intent.putExtra(
                    SETTING_INTERRUPT_DIALOG_SCREEN_CLEAR_KEY,
                    KEY.DIALOG_INTERRUPT
            );
            context.sendBroadcast(intent);
            PFLog.d(TAG, "interruptScreenClearMode : sendBroadcast ");
        }
    }

    /**
     * 临时打断息屏模式后的恢复操作.
     *
     * @param context 环境上下文
     */
    public static void recoverStandbyMode(Context context) {
        PFLog.d(TAG, "recoverStandbyMode : ");
        if (isStandbyMode(context)) {
            // 当前处于息屏模式,则临时打断后进行恢复操作
            Intent intent =new Intent(ACTION_SETTING_INTERRUPT_DIALOG_STANDBY);
            intent.putExtra(
                    SETTING_INTERRUPT_DIALOG_STANDBY_KEY,
                    KEY.DIALOG_RECOVER
            );
            context.sendBroadcast(intent);
            PFLog.d(TAG, "recoverStandbyMode : sendBroadcast ");
        }
    }

    /**
     * 临时打屏幕清洁模式后的恢复操作.
     *
     * @param context 环境上下文
     */
    public static void recoverScreenClearMode(Context context) {
        PFLog.d(TAG, "recoverScreenClearMode : ");
        if (isScreenClearMode(context)) {
            // 当前处于屏幕清洁模式,则临时打断后进行恢复操作
            Intent intent =new Intent(ACTION_SETTING_INTERRUPT_DIALOG_SCREEN_CLEAR);
            intent.putExtra(
                    SETTING_INTERRUPT_DIALOG_SCREEN_CLEAR_KEY,
                    KEY.DIALOG_RECOVER
            );
            context.sendBroadcast(intent);
            PFLog.d(TAG, "recoverScreenClearMode : sendBroadcast ");
        }
    }

    /**
     * 息屏模式时，全屏window show或dismiss状态通知其他方.
     *
     * @param context 环境上下文
     * @param isShow 有window在覆盖
     */
    public static void sendDominationScreenBroadcast(Context context,Boolean isShow){
        PFLog.d(TAG, "sendDominationScreenBroadcast : isShow = "+isShow);
        Intent intent =new Intent(ACTION_SETTING_SCREEN_DOMINATION);
        if(isShow){
            intent.putExtra(
                    SETTING_SCREEN_DOMINATION_KEY,
                    KEY.MODE_TRUE
            );
        }else {
            intent.putExtra(
                    SETTING_SCREEN_DOMINATION_KEY,
                    KEY.MODE_FALSE
            );
        }
        context.sendBroadcast(intent);
    }

    /**
     * 屏幕清洁模式时，全屏window show或dismiss状态通知其他方.
     *
     * @param context 环境上下文
     * @param isShow 有window在覆盖
     */
    public static void sendDominationClearingModeBroadcast(Context context,Boolean isShow){
        PFLog.d(TAG, "sendDominationClearingModeBroadcast : isShow = "+isShow);
        Intent intent =new Intent(ACTION_SETTING_CLEANING_MODE_DOMINATION);
        if(isShow){
            intent.putExtra(
                    SETTING_SCREEN_DOMINATION_KEY,
                    KEY.MODE_TRUE
            );
        }else {
            intent.putExtra(
                    SETTING_SCREEN_DOMINATION_KEY,
                    KEY.MODE_FALSE
            );
        }
        context.sendBroadcast(intent);
    }
}
