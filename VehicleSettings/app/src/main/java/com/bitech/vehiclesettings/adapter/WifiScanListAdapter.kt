package com.bitech.vehiclesettings.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.adapter.WifiConnectedListAdapter.OnWifiItemClickIconCallback
import com.bitech.vehiclesettings.bean.WifiDeviceBean
import com.bitech.vehiclesettings.databinding.ItemWifiScannedBinding
import com.bitech.vehiclesettings.manager.CarWifiManager
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.LogUtil
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @ClassName: WifiScanListAdapter
 *
 * @Date:  2024/2/7 9:09
 * @Description: WIFI扫描可用列表适配器.
 **/
// 1. 修改Adapter的泛型为通用的 RecyclerView.ViewHolder
class WifiScanListAdapter(
    private var wifiDeviceList: CopyOnWriteArrayList<WifiDeviceBean>,
    private val onItemClick: (WifiDeviceBean) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    // 2. 定义两种视图类型
    private val VIEW_TYPE_ITEM = 0
    private val VIEW_TYPE_EMPTY = 1

    /**
     * 根据位置返回视图类型
     */
    override fun getItemViewType(position: Int): Int {
        return if (wifiDeviceList.isEmpty()) {
            VIEW_TYPE_EMPTY
        } else {
            VIEW_TYPE_ITEM
        }
    }

    // 蓝牙配对列表点击事件监听对象.
    private var wifiItemClickIconCallback: OnWifiItemClickIconCallback? = null

    /**
     * 视图创建
     *
     * @param parent 父布局
     * @param viewType 视图类型
     * @return
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        // 3. 根据不同的viewType加载不同的布局和ViewHolder
        return if (viewType == VIEW_TYPE_ITEM) {
            val binding = ItemWifiScannedBinding.bind(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_wifi_scanned, parent, false)
            )
            WifiDevicesHolder(binding)
        } else {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_wifi_empty, parent, false)
            EmptyViewHolder(view)
        }
    }
    /**
     * WIFI扫描列表尺寸.
     *
     * @return 数据内容大小
     */
    override fun getItemCount(): Int {
        return if (wifiDeviceList.isEmpty()) 1 else wifiDeviceList.size
    }

    /**
     * ViewHolder绑定.
     *
     * @param holder item
     * @param position 位置
     */
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is WifiDevicesHolder){
            wifiDeviceList.getOrNull(position)?.let {
                val wifiDeviceBean = wifiDeviceList[position]
                // 设置WIFI名称
                holder.wifiNameTv.text = wifiDeviceBean.wifiSSID.ifBlank { wifiDeviceBean.wifiBSSID }
                // 设置网络加密类型图标
                holder.showWifiCapabilitiesAndSignalUi(
                    wifiDeviceBean.wifiCapabilities,
                    wifiDeviceBean.wifiLevel,
                    wifiDeviceBean
                )
                // 设置是否显示WIFI连接中动画
                if (wifiDeviceBean.wifiConnectedState == NetworkInfo.State.CONNECTING) {
                    // 显示WIFI连接中动画
                    holder.showWifiConnectingAnimation(true)
                } else {
                    // 隐藏WIFI连接中动画
                    holder.showWifiConnectingAnimation(false)
                }
                holder.itemView.setOnClickListener {
                    // item点击回调
                    onItemClick(wifiDeviceList[holder.adapterPosition])
                }
            }
        }
    }

    /**
     * 设置WIFI可用扫描列表数据.
     *
     * @param wifiList WIFI可用扫描列表.
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setWifiScanList(wifiList: CopyOnWriteArrayList<WifiDeviceBean>) {
        LogUtil.d(TAG, "setWifiScanList : wifiList size = ${wifiList.size}")
        // 过滤掉名称为空的设备
        val filteredList = CopyOnWriteArrayList<WifiDeviceBean>()
        for (device in wifiList) {
            if (device.wifiSSID.isNotBlank()) {
                filteredList.add(device)
            }
        }
        wifiDeviceList = filteredList
        notifyDataSetChanged()
    }

    /**
     * @ClassName: OnWifiItemClickIconCallback
     *
     * @Date:  2024/2/7 10:20
     * @Description: 列表图标点击事件回调.
     **/
    interface OnWifiItemClickIconCallback {

        /**
         * 点击删除WIFI已连接设备图标回调.
         *
         * @param wifiDevice wifi对象
         */
        fun onWifiDeleteDevice(wifiDevice: WifiDeviceBean)
    }

    /**
     * 设置配对列表监听.
     *
     * @param itemClickIconCallback OnWifiItemClickIconCallback
     */
    fun setOnBtItemClickIconCallback(itemClickIconCallback: WifiScanListAdapter.OnWifiItemClickIconCallback?) {
        LogUtil.d(
            TAG,
            "setOnBtItemClickIconCallback : itemClickIconCallback = $itemClickIconCallback"
        )
        if (wifiItemClickIconCallback == null) {
            wifiItemClickIconCallback = itemClickIconCallback
        }
    }

    inner class EmptyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    /**
     * @ClassName: WifiDevicesHolder
     *
     * @Date:  2024/2/7 9:40
     * @Description: WIFI可用扫描列表对应的ViewHolder.
     **/
    inner class WifiDevicesHolder(binding: ItemWifiScannedBinding) :
        RecyclerView.ViewHolder(binding.root), View.OnClickListener {
        private var wifiSignalIv = binding.wifiSignalStrengthIv
        private var wifiLoadingPb = binding.wifiLoadingPb
        private var wifiLock = binding.wifiLock
        private var wifiDelete = binding.wifiDelete
        var wifiNameTv = binding.wifiNameTv
        val wifiManager = MyApplication.getContext()?.applicationContext?.getSystemService(Context.WIFI_SERVICE) as WifiManager

        init {
            wifiDelete.setOnClickListener(this)   // ← 加上这一行
        }

        /**
         * 根据WIFI加密方式和信号强度显示对应的图标.
         *
         * @param wifiCapabilities wifi加密方式.
         * @param level 信号强度
         */
        fun showWifiCapabilitiesAndSignalUi(wifiCapabilities: String, level: Int, wifiDeviceBean: WifiDeviceBean) {
            //加密类型设置
            if (wifiCapabilities.contains(Contacts.WIFI_CAP_WPA)
                || wifiCapabilities.contains(Contacts.WIFI_CAP_WEP)
                || wifiCapabilities.contains(Contacts.WIFI_CAP_EAP)
                || wifiCapabilities.contains(Contacts.WIFI_CAP_WPA3)
                || wifiCapabilities.contains(Contacts.WIFI_CAP_WPA2_3)
            ) {
                // 显示加密网络图标(WPA/WPA2/WPA3/WEP/EAP)
                wifiSignalIv.setImageResource(R.mipmap.ic_wifi)
                wifiLock.visibility=View.VISIBLE
            } else {
                // 显示开放网络图标
                wifiSignalIv.setImageResource(R.mipmap.ic_wifi)
                wifiLock.visibility=View.GONE
            }
            // 信号强度设置
            val signalIconResId = when {
                level in 0 downTo -25 -> R.mipmap.ic_wifi_signal_4
                level in -26 downTo -50 -> R.mipmap.ic_wifi_signal_3
                level in -51 downTo -75 -> R.mipmap.ic_wifi_signal_2
                else -> R.mipmap.ic_wifi_signal_1
            }
            wifiSignalIv.setImageResource(signalIconResId)
            val isSavedNetwork =isSavedNetwork(wifiDeviceBean)
            if (isSavedNetwork){
                wifiLock.visibility = View.GONE
                wifiDelete.visibility = View.VISIBLE
            }
        }
        // 判断扫描到的设备是否是历史连接过的
        private fun isSavedNetwork(wifiDeviceBean: WifiDeviceBean): Boolean {
            val carWifiManager = CarWifiManager.instance
            return carWifiManager.isWifiConnected(wifiDeviceBean)
        }

        /**
         * 显示网络连接动画.
         *
         * @param isShow
         */
        fun showWifiConnectingAnimation(isShow: Boolean) {
            if (isShow) {
                // 显示动画图标
                wifiLoadingPb.visibility = View.VISIBLE
                // 隐藏信号图标
                wifiSignalIv.visibility = View.GONE
            } else {
                // 隐藏动画图标
                wifiLoadingPb.visibility = View.GONE
                // 显示信号图标
                wifiSignalIv.visibility = View.VISIBLE
            }
        }

        /**
         * item图标绑定.
         *
         * @param view
         */
        override fun onClick(view: View?) {
            when (view?.id) {
                R.id.wifi_delete -> {
                    LogUtil.d(TAG, "onClick : wifi delete icon is click!")
                    // WIFI删除图标被点击.
                    wifiItemClickIconCallback?.onWifiDeleteDevice(wifiDeviceList[adapterPosition])
                }
            }
        }
    }

    companion object {
        // 日志标志位
        private const val TAG = "WifiScanListAdapter"
    }
}
