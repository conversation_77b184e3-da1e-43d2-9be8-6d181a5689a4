package com.bitech.vehiclesettings.repository;

import androidx.annotation.NonNull;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.subjects.BehaviorSubject;

public class DataManager<T> {
    private final BehaviorSubject<T> dataSubject = BehaviorSubject.create();

    public Observable<T> getObservable() {
        return dataSubject.hide().serialize();
    }

    public void updateData(@NonNull T newData) {
        if (dataSubject.hasObservers()) {
            dataSubject.onNext(newData);
        }
    }
}
