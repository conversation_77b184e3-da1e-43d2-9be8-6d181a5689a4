package com.bitech.vehiclesettings.presenter.quick;

import android.widget.SeekBar;
import android.widget.TextView;

public interface QuickPresenterListener {
    /**
     * 设置中控锁
     *
     * @param status 1 设置 0 不设置
     */
    void setCentralLocking(int status);

    int getCentralLocking();

    void setRearTailGate(int state);

    int getRearTailGate();

    void setRearMirror(int state);

    int getRearMirror();

    void setWindow(int state);
    void setWindowState(int state);
    void setFLWindow(int state);
    void setFRWindow(int state);
    void setRLWindow(int state);
    void setRRWindow(int state);

    int getWindow();
    int getFLWindow();
    int getFRWindow();
    int getRRWindow();
    int getRLWindow();

    int getFLDoor();
    int getFRDoor();
    int getRLDoor();
    int getRRDoor();

    void setWindowLock(int state);

    int getWindowLock();

    void setLockCarSunRoofShadeClose(int state);

    int getLockCarSunRoofShadeClose();

    void setDefenseReminder(int status);
    int getDefenseReminder();

    void setLeftChildLock(int status);
    int getLeftChildLock();

    void setRightChildLock(int status);
    int getRightChildLock();

    void setAutomaticLocking(int status);
    int getAutomaticLocking();

    void setAutomaticParkingUnlock(int status);
    int getAutomaticParkingUnlock();

    void setSunshade(int state);

    int getSunshade();

    void setSunshadeFront(int status);
    int getSunshadeFront();

    void setSunshadeRear(int status);
    int getSunshadeRear();

    void setAutoTail(int state);

    int getAutoTail();

    void setSkyWindow(int state);

    int getSkyWindow();

    void setApproachingUnlock(int state);

    int getApproachingUnlock();

    void setDepartureLocking(int state);

    int getDepartureLocking();

    void setLockAutoRaiseWindow(int status);

    int getLockAutoRaiseWindow();

    void setAutoRearMirrorFold(int state);

    int getAutoRearMirrorFold();


    void sendMirrorAdjuse();

    int getMirrorAdjuseStatus();

    // 倒车时后视镜自动调节
    void setBackAutoRearMirrorAdjust(int state);

    int getBackAutoRearMirrorAdjust();

    void setWiperSens(int status);
    void setAutoHotRearMirror(int state);

    int getAutoHotRearMirror();

    void setSeatPortable(int state);

    int getSeatPortable();




    int getWiperSens();

    void setHudRoate(int status);

    int getHudRoate();

    void setDriveAirBag(int status);
    int getDriveAirBag();

    int getCarSpeed();
    void setCarSpeed(int v);

    void setCustomButton(int state);
    int getCustomButton();

    // 车辆下单
    void setVehiclePowerOff(int state);

    void setRearScreenControl(int status);
    int getRearScreenControl();
    int getRefuelSmallDoor();
    void setRefuelSmallDoor(int state);
}
