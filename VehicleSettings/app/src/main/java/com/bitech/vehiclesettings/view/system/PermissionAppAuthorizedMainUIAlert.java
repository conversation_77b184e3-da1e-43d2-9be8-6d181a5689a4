package com.bitech.vehiclesettings.view.system;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionAppAuthoirzedMainBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class PermissionAppAuthorizedMainUIAlert extends BaseDialog {
    private static final String TAG = PermissionAppAuthorizedMainUIAlert.class.getSimpleName();
    private static PermissionAppAuthorizedMainUIAlert.onProgressChangedListener onProgressChangedListener;
    private int position = 0;


    public PermissionAppAuthorizedMainUIAlert(@NonNull Context context) {
        super(context);
    }

    public PermissionAppAuthorizedMainUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PermissionAppAuthorizedMainUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static PermissionAppAuthorizedMainUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(PermissionAppAuthorizedMainUIAlert.onProgressChangedListener onProgressChangedListener) {
        PermissionAppAuthorizedMainUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private PermissionAppAuthorizedMainUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSPermissionAppAuthoirzedMainBinding binding;

        int position, status;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private PermissionAppAuthorizedMainUIAlert dialog = null;

        public Builder(Context context, int position) {
            this.context = context;
            this.position = position;
        }


        public PermissionAppAuthorizedMainUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public PermissionAppAuthorizedMainUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PermissionAppAuthorizedMainUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSPermissionAppAuthoirzedMainBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 580;
            window.setAttributes(layoutParams);

            // 设置文本
            setText();
            // 初始化按钮
            initRadio();
            // 初始化保存
            initSave();

            dialog.position = position;

            return dialog;
        }

        private void initRadio() {
            // 获取当前授权的时间类型
            status = onProgressChangedListener.getStatus(position);
            Log.d(TAG, "initRadio: " + status);

            // 设置初始选中状态
            if (status == 0) {
                binding.rbOpen12Month.setImageResource(R.drawable.icon_set_light_choose);
                binding.rbOpenThisTime.setImageResource(R.drawable.icon_set_light_choose_no);
            } else if (status == 1) {
                binding.rbOpen12Month.setImageResource(R.drawable.icon_set_light_choose_no);
                binding.rbOpenThisTime.setImageResource(R.drawable.icon_set_light_choose);
            }

            // 为 ImageView 设置点击事件监听器
            binding.rbOpen12Month.setOnClickListener(v -> {
                status = 0;
                Log.d(TAG, "onImageViewClicked: " + status);
                binding.rbOpen12Month.setImageResource(R.drawable.icon_set_light_choose);
                binding.rbOpenThisTime.setImageResource(R.drawable.icon_set_light_choose_no);
            });

            binding.rbOpenThisTime.setOnClickListener(v -> {
                status = 1;
                Log.d(TAG, "onImageViewClicked: " + status);
                binding.rbOpen12Month.setImageResource(R.drawable.icon_set_light_choose_no);
                binding.rbOpenThisTime.setImageResource(R.drawable.icon_set_light_choose);
            });
        }



        private void initSave() {
            dialog.setOnDismissListener(d -> {
                if (onProgressChangedListener != null) {
                    Log.d(TAG, "initSave: " + status);
                    onProgressChangedListener.setPermissionTime(position, status);
                    onProgressChangedListener.updateText(position);
                    EToast.showToast(context, "授权时间修改成功", 1000, false);
                }
            });
        }

        private void setText() {
            if (position == 0) {
                binding.tvTitle.setText(context.getString(R.string.str_system_permission_camera_app_authorized_main));
            } else if (position == 1) {
                binding.tvTitle.setText(context.getString(R.string.str_system_permission_microphone_app_authorized_main));
            } else if (position == 2) {
                binding.tvTitle.setText(context.getString(R.string.str_system_permission_location_app_authorized_main));
            }
        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
        onProgressChangedListener.openPermissionDialog(position);

    }

    public interface onProgressChangedListener {

        void setPermissionTime(int position, int status);

        int getStatus(int position);

        void updateText(int position);

        void openPermissionDialog(int position);
    }
}
