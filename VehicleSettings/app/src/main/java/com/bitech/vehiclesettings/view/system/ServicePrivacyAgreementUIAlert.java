package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebSettings;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSBasicPravicyAgreementBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSServicePravicyAgreementBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class ServicePrivacyAgreementUIAlert extends BaseDialog {
    private static final String TAG = ServicePrivacyAgreementUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public ServicePrivacyAgreementUIAlert(@NonNull Context context) {
        super(context);
    }

    public ServicePrivacyAgreementUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ServicePrivacyAgreementUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        ServicePrivacyAgreementUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSServicePravicyAgreementBinding binding;
        private boolean isBlueOpen = false;
        public ServicePrivacyAgreementUIAlert dialog = null;
        private View layout;
        WindowManager.LayoutParams layoutParams;

        public Builder(Context context) {
            this.context = context;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public ServicePrivacyAgreementUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }

        public ServicePrivacyAgreementUIAlert create() {
            if (dialog == null)
                dialog = new ServicePrivacyAgreementUIAlert(context, R.style.Dialog);
            dialog.setCancelable(isCan);

            binding = DialogAlertSServicePravicyAgreementBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1912;
            layoutParams.height = 1080;
            window.setAttributes(layoutParams);

            // 初始化文本
            initText();
            // 初始化按钮
            initButton();

            return dialog;
        }

        private void initText() {
            binding.tvTitle.setText(context.getResources().getString(
                    R.string.str_system_permission_service_privacy_statement_title
            ));
            binding.tvContent.setText(context.getResources().getString(
                    R.string.str_system_permission_service_privacy_statement_content
            ));
            binding.tvTips.setText(context.getResources().getString(
                    R.string.str_system_permission_service_privacy_statement_tips
            ));
        }

        private void initButton() {
            // 同意按钮
            binding.tvAgree.setOnClickListener(v -> {
                int result = onProgressChangedListener.onAgree();
                if (result == 1) {
                    dialog.dismiss();
                }
            });
            // 拒绝按钮
            binding.tvRefuse.setOnClickListener(v -> {
                onProgressChangedListener.onRefuse();
                dialog.dismiss();
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        int onAgree();
        void onRefuse();
    }
}
