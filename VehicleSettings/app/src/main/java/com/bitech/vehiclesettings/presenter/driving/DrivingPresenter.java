package com.bitech.vehiclesettings.presenter.driving;

import android.content.Context;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.id.vehicle.VDEventVehicle;
import com.chery.ivi.vdb.event.id.vehicle.VDKeyVehicle;

public class DrivingPresenter implements DrivingPresenterListener {
    private static final String TAG = DrivingPresenter.class.getSimpleName();

    DrivingManager drivingManager = (DrivingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_DRIVING_MANAGER);
    NewEnergyManager newEnergyManager = (NewEnergyManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_ENERGY_MANAGER);
    private final Context context;

    public DrivingPresenter(Context context) {
        this.context = context;
    }

    @Override
    public void setCarMode(int state) {
        Log.d(TAG, "setCarMode:" + state);
        // 保存
        Prefs.put(PrefsConst.SYSTEM_AUTO_CALIBRATION, state);
        if (state == 0) {   // 纯电优先
            drivingManager.setDrivingModeSet(0x1);
        } else if (state == 1) { // 节能混动
            drivingManager.setDrivingModeSet(0x8);
        } else if (state == 2) { // 舒适混动
            drivingManager.setDrivingModeSet(0x2);
        } else if (state == 3) { // 风云GT
            drivingManager.setDrivingModeSet(0x3);
        } else if (state == 4) { // 雨雪模式
            drivingManager.setDrivingModeSet(0x4);
        } else if (state == 5) { // 自定义模式
            drivingManager.setDrivingModeSet(0x7);
        }
    }


    @Override
    public void setCarModeToICU(int state) {
        Log.d(TAG, "setCarModeToICU:" + state);
        if (state == 0) {   // 纯电优先
            drivingManager.setDrivingModeToICUSet(0x1);
        } else if (state == 1) { // 节能混动
            drivingManager.setDrivingModeToICUSet(0x8);
        } else if (state == 2) { // 舒适混动
            drivingManager.setDrivingModeToICUSet(0x2);
        } else if (state == 3) { // 风云GT
            drivingManager.setDrivingModeToICUSet(0x3);
        } else if (state == 4) { // 雨雪模式
            drivingManager.setDrivingModeToICUSet(0x4);
        } else if (state == 5) { // 自定义模式
            drivingManager.setDrivingModeToICUSet(0x7);
        }
    }

    @Override
    public void setPrivacyPolicyStatus(int status) {
        Prefs.put(PrefsConst.PRIVACY_POLICY_STATUS, status);
    }

    @Override
    public int getPrivacyPolicyStatus() {
        return Prefs.get(PrefsConst.PRIVACY_POLICY_STATUS, CarDriving.PrivacyPolicySts.NOT_AGREE);
    }

    @Override
    public int getCarMode() {
        int signalVal = drivingManager.getDriveMode();
        Log.d(TAG, "getCarMode:" + signalVal);
        int state = 0;
        if (signalVal == 0x1) {      // 纯电优先
            state = 0;
        } else if (signalVal == 0x7) { // 节能混动
            state = 1;
        } else if (signalVal == 0x2) { // 舒适混动
            state = 2;
        } else if (signalVal == 0x3) { // 风云GT
            state = 3;
        } else if (signalVal == 0x4) { // 雨雪模式
            state = 4;
        } else if (signalVal == 0x6) { // 个性化设置
            state = 5;
        }
        Log.d(TAG, "getCarMode:" + state);
        Prefs.put(PrefsConst.D_CAR_MODE, state);
//        Prefs.put(PrefsConst.D_CAR_MODE, 5);
//        return Prefs.rtnStatus(PrefsConst.D_CAR_MODE, state, 0);
        return state;
//        return 5;
    }

    // Case1：1. 前置条件（a&b）:a.电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
    // b.PDCS(HCU)满足切换条件信号HCU_ForcedEVAvailable=0x0:Available；
    // 2. 触发条件（a&b）:a.极致纯电为纯电优先的二级菜单设置，用戶进入驾驶设置页面，首先点击纯电优先软开关；
    // b.在纯电优先模式下，用戶再点击触控开关开启极致纯电模式；
    // 3. 执行输出（a）：a.发送极致纯电信号ICC_ForcedEVMode=0x1:On至FLZCU，同时收到FLZCU发送的ForcedEVMode=0x1:Active，显示极致纯电模式；
    @Override
    public void setExtremePureSignal(int status) {
        // TODO PDCS(HCU)满足切换条件信号HCU_ForcedEVAVAvailable=0x0:Available；
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 1) {
                drivingManager.setEVModeSet(0x1);
            } else if (status == 0) {
                drivingManager.setEVModeSet(0x2);
            }
        }
    }

    @Override
    public int getExtremePureSignal() {
        int signalVal = drivingManager.getEVMode();
        Log.d(TAG, "getExtremePureSignal:" + signalVal);
        int state = 0;
        if (signalVal == 0x0) {
            state = 0;
        } else if (signalVal == 0x1) {
            state = 1;
        }
        Prefs.put(PrefsConst.D_EXTREME_PURE_SIGNAL, state);
        return state;
    }

    @Override
    public void setSteepSlopeDescent(int status) {
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                // TODO 关闭 1）电源状态ON档(信号：FLFLZLZCZCUCU_2_PoPowowewererMrMoMododede=e=O=ONON)N)，在大屏点击陡坡缓降HDC功能开启，ICC连续发送三帧TITIHU_SeSetetHtHDCDCOCOnOnOfOff ff =0=0x0x1x1:1:O:ONON，然后发送0x0:Not Active 给IPB。
                drivingManager.setHillDescentControlSet(0x2);
            } else if (status == 1) {
                // TODO 开启 电源状态ON档(信号：FLZCU_2_PowerMode=ON)，在大屏点击陡坡缓降HDC功能关闭，ICC连续发送三帧TITIHU_SeSetetHtHDCDCOCOnOnOfOff ff =0=0x0x2x2:2:O:OFOFFFF，F，然后发送0x0:Not Active 给IPB。
                drivingManager.setHillDescentControlSet(0x1);
            }
        }
    }

    @Override
    public int getSteepSlopeDescent() {
        int signalVal = drivingManager.getHillDescentControl();
        int state = 0;
        if (signalVal == 0x0) {
            state = 0;
        } else if (signalVal == 0x1) {
            state = 1;
        } else if (signalVal == 0x2) {
            state = 1;
        }
        Prefs.put(PrefsConst.D_STEEP_SLOPE_DESCENT, state);
//        return Prefs.rtnStatus(PrefsConst.D_STEEP_SLOPE_DESCENT, state, 0);
        return state;
    }

    @Override
    public void setBodyStabilityControl(int status) {
        // TODO 电源信号为 ON 连续发三帧 然后发0x0
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                // 关闭
                drivingManager.setBodyInfoESCSet(0x2);
            } else if (status == 1) {
                // 开启
                drivingManager.setBodyInfoESCSet(0x1);
            }
        }
    }

    @Override
    public int getBodyStabilityControl() { // 车身稳定控制
        int signalVal = drivingManager.getBodyInfoESC();
        int state = 1;
        if (signalVal == 0x0) {
            state = 1;
        } else if (signalVal == 0x1) {
            state = 0;
        }
        Prefs.put(PrefsConst.D_BODY_STABILITY_CONTROL, state);
//        return Prefs.rtnStatus(PrefsConst.D_BODY_STABILITY_CONTROL, state, 0);
        return state;
    }

    @Override
    public void setAutoParking(int status) { // TODO 自动驻车信号错误
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                // 关闭
                drivingManager.setAutoHoldSet(0x2);
            } else if (status == 1) {
                // 开启
                drivingManager.setAutoHoldSet(0x1);
            }
        }
    }

    @Override
    public int getAutoParking() {
        int signalVal = drivingManager.getAutoHold();
        Log.d(TAG, "getAutoParking:" + signalVal);
        int state = 0;
        if (signalVal == 0x0) {
            state = 0;
        } else if (signalVal == 0x1) {
            state = 1;
        } else if (signalVal == 0x2) {
            state = 1;
        }
        Prefs.put(PrefsConst.D_AUTO_PARKING, state);
//        return Prefs.rtnStatus(PrefsConst.D_AUTO_PARKING, state, 0);
        return state;
    }

    @Override
    public void setParkingBrake(int status) {
        // 驻车制动
        if (MsgUtil.getInstance().supportPowerMode()) {
            try {
                VDEvent event = VDBus.getDefault().getOnce(VDEventVehicle.PERF_VEHICLE_SPEED);
                double[] data = event.getPayload().getDoubleArray(VDKeyVehicle.DOUBLE_VECTOR);
                double value = data[0];
                Log.d(TAG, "setParkingBrake: 车速:" + value);
                if (value >= 3) return;
                if (status == 1) {
                    drivingManager.setBodyInfoEPBSet(0x2);
                } else if (status == 0) {
                    // 释放
//                    boolean isBrakePedal = getBrakePedal() == 1 ? true : false;
//                    if (isBrakePedal) {
                    drivingManager.setBodyInfoEPBSet(0x1);
//                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.d(TAG, e.getMessage());
            }

        }
    }

    //Signal Size 30x264: EPB_1_ActrSt 信号
    // EPB状态0x0:Unknow / 0x1:Applied0x3:Applying / 0x6:Half Applied高亮（静态文字：解除驻车制动）
    // 0x2:Released / 0x4: Releasing 灰色（静态文字：激活驻车制动）0x5: CompletelyReleased 大屏按上一状态显示并置灰(进入诊断模式，由诊断控制EPB的夹紧释放)
    @Override
    public int getParkingBrake() {
        int signalVal = drivingManager.getEPBStatus();
        Log.d(TAG, "getParkingBrake:" + signalVal);
        int state = 2;
        if (signalVal == 0x0 || signalVal == 0x3 || signalVal == 0x4 || signalVal == 0x6 || signalVal == 0x7) {
            state = 2;
        } else if (signalVal == 0x1) { // 开启
//            state = 0;
            state = 1;
        } else if (signalVal == 0x5 || signalVal == 0x2) { // 锁紧
//            state = 1;
            state = 0;
        }
        Prefs.put(PrefsConst.D_PARKING_BRAKE, state);
//        return Prefs.rtnStatus(PrefsConst.D_PARKING_BRAKE, state, 0);
        return state;
    }

    // 舒适制动开关
    // 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
    // 配置参数 N/A/A在线配置参数 数据长度 默认值 参数含义
    // 备注在中控屏车辆设置-XXX提供舒适制动等级设置：
    // 显示名称：舒适制动等级设置和显示状态：通勤模式，防晕车模式，儿童模式默认值：
    // 通勤模式（初次上电大屏默认开关不点亮，当舒适制动开关点亮时，默认等级开关显示为”通勤模式”）
    // 该功能与舒适制动开关联动，舒适制动关闭时，舒适制动等级不点亮；舒适制动开启时，舒适制动等级点亮；
    @Override
    public void setComfortBraking(int status) {
        // TODO 电源模式:Comfort/ON档
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                // 关闭
                drivingManager.setCSTEnableStatusSet(0x1);
            } else if (status == 1) {
                // 开启
                drivingManager.setCSTEnableStatusSet(0x2);
            }
        }
    }

    // 0x4AD: CST_Status 信号
    // 舒适制动0x0:CST is disabled 关闭
    // 0x1:CST is Standby或0x2:CST is Active开启
    // 0x3:CST is Failure 开关置灰，文字告警“系统异常，功能不可用”，toast弹窗
    @Override
    public int getComfortBraking() {
        int singlVal = drivingManager.getCSTEnableStatus();
        int state = 0;
        if (singlVal == 0x0) {
            state = 0;
        } else if (singlVal == 0x1 || singlVal == 0x2) {
            state = 1;
        } else if (singlVal == 0x3) { // TODO 开关灰
            state = 2;
        }
        Prefs.put(PrefsConst.D_COMFORT_BRAKING, singlVal);
//        return Prefs.rtnStatus(PrefsConst.D_COMFORT_BRAKING, state, 0);
        return state;
    }


    // 设置舒适制动等级
    @Override
    public void setComfortBrakingRank(int status) {
        // TODO 电源模式: ON档 且舒适制动开启时 连发送三帧 然后发送0x0:Not Active 给IPB。
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                drivingManager.setCSTInterventionSet(0x1); // 通勤模式
            } else if (status == 1) {
                drivingManager.setCSTInterventionSet(0x2); // 防晕车模式
            } else if (status == 2) {
                drivingManager.setCSTEnableStatusSet(0x3);  // 儿童模式
            }
        }
    }

    // 获取舒适制动等级
    @Override
    public int getComfortBrakingRank() {
        int status = 0;
        int signalVal = drivingManager.getCSTIntervention();
        if (signalVal == 0x1 || signalVal == 0x0) { // 通勤模式
            status = 0;
        } else if (signalVal == 0x2) { // 防晕车模式
            status = 1;
        } else if (signalVal == 0x3) { // 儿童模式
            status = 2;
        }
        Prefs.put(PrefsConst.D_COMFORT_BRAKING_RANK, status);
        return status;
    }

    // 用戶是否踩下制动踏板
    // 0x0 未踩下 Not Applied
    // 0x1 踩下 Applied
    public int getBrakePedal() {
        int signVal = drivingManager.getBrakePedal();
        int state = 0;
        if (signVal == 0x0) {
            state = 0;
        } else if (signVal == 0x1) {
            state = 1;
        }
        return state;
    }

    //    电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；配置参数（智驾未定点，配置字组合待定）
    // 1）电源状态ON档(信号：FLZCU_9_PowerMode=ON)，在大屏上设置智能预瞄为开,ICC连续发三帧ICC_ASUPreviewCont=0x1: ON，然后发送0x0:Not Active 给FLZCU。
    // 2）电源状态ON档(信号：FLZCU_9_PowerMode=ON)，在大屏上设置智能预瞄为关闭，ICC连续发送三帧ICC_ASUPreviewCont=0x2：OFF，然后发送0x0:Not Active 给FLZCU。
    @Override
    public void setIntelligentSuspensionAiming(int status) {
        // TODO 电源模式:Comfort/ON档
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                drivingManager.setCSTASUPreviewContentSet(0x2); // 智能预瞄功能设置为关闭
            } else if (status == 1) {
                drivingManager.setCSTASUPreviewContentSet(0x1); // 智能预瞄功能设置为开启
            }
        }
    }

    // 悬架智能瞄准
    @Override
    public int getIntelligentSuspensionAiming() {
        int signalVal = drivingManager.getCSTASUPreviewContent();
        int state = 0;
        if (signalVal == 0x0) {
            state = 0;
        } else if (signalVal == 0x1) {
            state = 1;
        }
        Prefs.put(PrefsConst.D_INTELLIGENT_SUSPENSION_AIMING, state);
//        return Prefs.rtnStatus(PrefsConst.D_INTELLIGENT_SUSPENSION_AIMING, state, 0);
        return state;
    }
    // 牵引模式

    // 个性化-车辆模式
    @Override
    public void setDrivingMode(int status) {
        // TODO 电源模式为ON档
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                drivingManager.setPropulsionModeSet(0x1);
            } else if (status == 1) {
                drivingManager.setPropulsionModeSet(0x2);
            } else if (status == 2) {
                drivingManager.setPropulsionModeSet(0x3);
            } else if (status == 3) {
                drivingManager.setPropulsionModeSet(0x4);
            }
        }
    }

    // 个性化-车辆模式
    @Override
    public int getDrivingMode() {
        int signalVal = drivingManager.getPropulsionMode();
        int state = 0;
        if (signalVal == 0x1) {
            state = 0;
        } else if (signalVal == 0x2) {
            state = 1;
        } else if (signalVal == 0x3) {
            state = 2;
        } else if (signalVal == 0x4) {
            state = 3;
        }
        Prefs.put(PrefsConst.D_DRIVING_MODE, state);
//        return Prefs.rtnStatus(PrefsConst.D_DRIVING_MODE, state, 0);
        return state;
    }

    @Override
    public int enableChangeDrivingMode() {
        return 1;
    }

    // 个性化-保电电量设置
    @Override
    public void setPowerProtection(int status) {
        // TODO 前置条件： 1.电源模式为ON档 2.收到FLZCU发送的电池管理信息HCU_SocManageFed=0x3:SOC hold mode；
        if (MsgUtil.getInstance().supportPowerMode()) {
            int SocManage = drivingManager.getSocManage();
            if (SocManage == 0x3) {
                drivingManager.setSocSet(status);
            }
        }
    }

    // 个性化-保电电量获取
    @Override
    public int getPowerProtection() {
        int status = drivingManager.getSocSet();
//        return Prefs.rtnStatus(PrefsConst.D_POWER_PROTECTION, status, 30);
        Log.d(TAG, "getPowerProtection: " + status);
        if (status < 20 || status > 80) status = -1;
        Prefs.put(PrefsConst.D_POWER_PROTECTION, status);
        return status;
    }

    // 个性化-转向模式
    @Override
    public void setSwerveMode(int status) {
        // TODO 前置条件：电源模式为FLZCU_9_PowerMode =ON；
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                drivingManager.setSteeringModeSet(0x1);
                Log.d("drivingManager", "个性化-转向模式设置");
            } else if (status == 1) {
                drivingManager.setSteeringModeSet(0x2);
            }
        }
    }

    // 个性化-转向模式获取
    @Override
    public int getSwerveMode() {
        int signalVal = drivingManager.getSteeringMode();
        int status = 0;
        if (signalVal == 0x1) {
            status = 0;
        } else if (signalVal == 0x2) {
            status = 1;
        }
        Log.d(TAG, "getSwerveMode: " + status);
//        return Prefs.rtnStatus(PrefsConst.D_SWERVE_MODE, status, 0);
        Prefs.put(PrefsConst.D_SWERVE_MODE, status);
        return status;
    }

    // 个性化-悬架模式
    @Override
    public void setSuspensionMode(int status) {
        // TODO 前置条件：电源模式为ON档
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                drivingManager.setSuspensionDampingSet(0x1);
            } else if (status == 1) {
                drivingManager.setSuspensionDampingSet(0x3);
            }
        }
    }

    // 个性化-悬架模式
    @Override
    public int getSuspensionMode() {
        int signalVal = drivingManager.getSuspensionDamping();
        int status = 0;
        if (signalVal == 0x1) {
            status = 0;
        } else if (signalVal == 0x3) {
            status = 1;
        }
        Log.d(TAG, "getSuspensionMode: " + status);
        Prefs.put(PrefsConst.D_SUSPENSION_MODE, status);
//        return Prefs.rtnStatus(PrefsConst.D_SUSPENSION_MODE, status, 0);
        return status;
    }

    // 个性化-制动模式
    @Override
    public void setBrakingMode(int status) {
        // TODO 前置条件：电源模式为ON档
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
                drivingManager.setPedalComfortSet(0x1);
            } else if (status == 1) {
                drivingManager.setPedalComfortSet(0x2);
            }
        }
    }

    // 个性化-制动模式
    @Override
    public int getBrakingMode() {
        int signalVal = drivingManager.getPedalComfort();
        int status = 0;
        if (signalVal == 0x1) {
            status = 0;
        } else if (signalVal == 0x2) {
            status = 1;
        }
//        return Prefs.rtnStatus(PrefsConst.D_BRAKING_MODE, status, 0);
        Prefs.put(PrefsConst.D_BRAKING_MODE, status);
        return status;
    }

    // 设置牵引模式（拖车模式）
    @Override
    public void setTractionMode(int status) {
//  TODO      1. 车辆处于P/N档
//        2. 刹车踩下
//        3. 未插充/放电枪

        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {  // 关闭
                newEnergyManager.setTowingModeSwitch(0x1);
            } else if (status == 1) { // 开启
                newEnergyManager.setTowingModeSwitch(0x2);
            }
        }
    }

    // 获取牵引模式（拖车模式）
    @Override
    public int getTractionMode() {
        int signalVal = newEnergyManager.getTowingModeSwitch();
        int status = 0;
        if (signalVal == 0x0) {
            status = 0;
        } else if (signalVal == 0x1) {
            status = 1;
        }
        Prefs.put(PrefsConst.D_BRAKING_MODE, status);
        return status;
    }

    @Override
    public int enableTractionMode() {
        // 车辆是否属于P/N 档
        // 0x0:Init
        // 0x1:P
        // 0x2:R
        // 0x3:N
        // 0x4:D
        // 0x5:Reserved
        // 0x6:Reserved
        // 0x7:Reserved
        int PNStatus = newEnergyManager.getDrivingInfoGear();
        if (PNStatus != 0x1 && PNStatus != 0x3) return 0;
        // 用戶是否踩下制动踏板
        // 0x0 未踩下 Not Applied
        // 0x1 踩下 Applied
        int brakeSt = drivingManager.getBrakePedal();
        if (brakeSt != 0x1) return 0;
        // 3. 未插充/放电枪
        // 快充枪：
        int fastGunConnected = newEnergyManager.getChgWireConnectLightSts();
        if (fastGunConnected != 0x0) return 0;
        // 慢充枪：
        int slowGunConnected = newEnergyManager.getChargeGunStatus();
        if (slowGunConnected != 0x0) return 0;
        // 充电枪连接状态：
        int chargeGunConnected = newEnergyManager.getEvccGunConnectSts();
        if (chargeGunConnected != 0x0) return 0;
        return 1;
    }

    //    激活失败：则ICC_TowingMode=0x0->0x2ON->0x0(0x2发三帧)，中控接收到VCU的反馈VCU_TowingMode=0x0 NotActive，
//    及收到失败原因信号VCU_DrvGearShiftftFailureIndcn▪ 若VCU_DrvGearShiftftFailureIndcn=0x5:EPB Can Not Release，
//    此时提示文言为：“电子手刹无法释放，进入牵引模式失败”；▪ 若VCU_DrvGearShiftftFailureIndcn=0x1: Brake pedal applied，
//    此时提示文言为：“进入牵引模式需踩刹车”；▪ 若VCU_DrvGearShiftftFailureIndcn为其他值或ICC未接收到VCU_DrvGearShiftftFailureIndcn信号，
//    大屏均无需提示；
    @Override
    public int getTractionModeFailReason() {
        int signVal = newEnergyManager.getTowModeInfo();
        int state = 0;
        if (signVal == 0x5) { //
            state = 1;
        } else if (signVal == 0x1) {
            state = 2;
        }
        return state;
    }

    @Override
    public void setSwCamera(int status) {
        /*Log.d(TAG, "setSwCamera: " + status);

        try {
            Log.d(TAG, "setSwCamera: 开启摄像头");
            CameraManager manager = (CameraManager) mContext.getSystemService(Context.CAMERA_SERVICE);
            Log.d(TAG, "setSwCamera: manager: " + manager);
            String[] cameraIds = manager.getCameraIdList();

            if (status == 1 && mCameraDevice == null) { // 避免重复打开
                Log.d(TAG, "setSwCamera: mCameraDevice == null");
                if (cameraIds.length > 0 && checkPermission()) {
                    Log.d(TAG, "setSwCamera: cameraIds.length > 0");
                    manager.openCamera(cameraIds[0], new CameraDevice.StateCallback() {
                        @Override
                        public void onOpened(@NonNull CameraDevice camera) {
                            mCameraDevice = camera; // 正确保存实例
                            saveStatus(1);
                            Log.d(TAG, "摄像头已打开 | 设备ID: " + camera.getId());
                        }

                        @Override
                        public void onDisconnected(@NonNull CameraDevice cameraDevice) {

                        }

                        @Override
                        public void onError(@NonNull CameraDevice camera, int error) {
                            Log.e(TAG, "打开失败 ERROR_CODE: " + error);
                            mCameraDevice = null; // 失败时重置
                        }
                    }, null);
                }
            } else if (status == 0 && mCameraDevice != null) { // 避免重复关闭
                Log.d(TAG, "setSwCamera: 关闭摄像头 mCameraDevice != null");
                mCameraDevice.close();
                mCameraDevice = null; // 显式释放
                saveStatus(0);
                Log.d(TAG, "摄像头已关闭");
            }
        } catch (Exception e) {
            Log.e(TAG, "操作异常", e);
            mCameraDevice = null; // 异常时强制重置
        }*/
        // TODO DMS 摄像头开关
        Log.d(TAG, "setSwhCamera: 开关DMS摄像头:" + status);
        Prefs.put(PrefsConst.R_DMS_CAMERA_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_DMS_CAMERA_STATUS, status);
    }

    @Override
    public int getSwCamera() {
        // TODO DMS 摄像头开关
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.R_DMS_CAMERA_STATUS, PrefsConst.DefaultValue.R_DMS_CAMERA_STATUS);

        Log.d(TAG, "getSwhCamera: 获取DMS摄像头:" + status);
        return status;
//        return 0;
    }

    // 获取P档状态
    public int getPowerMode() {
        // 0x0:Init
        // 0x1:P
        // 0x2:R
        // 0x3:N
        // 0x4:D
        // 0x5:Reserved
        // 0x6:Reserved
        // 0x7:Reserved
        int signalVal = newEnergyManager.getDrivingInfoGear();
        int status = 0;
        if (signalVal == 0x1) {
            status = 0;
        } else if (signalVal == 0x2) {
            status = 1;
        }
        Prefs.put(PrefsConst.D_INFO_GEAR, status);
        return status;
    }

    public void reset() {
        // 驾驶模式默认 纯电优先
        setCarMode(0);

        // 个性化-驾驶模式
        setDrivingMode(0);

        // 个性化-保电电量 20%
        setPowerProtection(20);

        // 个性化-转向模式 默认 舒适
        setSwerveMode(0);

        // 个性化-悬架模式 默认 舒适
        setSuspensionMode(0);

        // 个性化-制动模式 默认 舒适
        setBrakingMode(0);
    }
}