package com.bitech.vehiclesettings.utils;

import android.content.Context;
import android.content.res.Configuration;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.bitech.vehiclesettings.R;

public class NightModeUtils {

    /**
     * 强制 View 使用黑夜模式资源（自动从 -night 目录加载）
     * @param view 目标组件
     * @param forceNight 是否强制黑夜模式
     */
    public static void applyNightModeResources(View view, boolean forceNight, int iconId) {
        Context originalContext = view.getContext();
        Context nightContext = createNightContext(originalContext, forceNight);

        // 示例：自动应用黑夜模式的背景和文字颜色
        view.setBackground(ContextCompat.getDrawable(nightContext, R.drawable.selector_menu));
        if (view instanceof TextView) {
            ((TextView) view).setTextColor(
                ContextCompat.getColor(nightContext, R.color.selector_function_tab)
            );
            ((TextView) view).setCompoundDrawablesRelativeWithIntrinsicBounds(
                    ContextCompat.getDrawable(nightContext, iconId), // start
                    null,     // top
                    null,     // end
                    null      // bottom
            );
        }
    }

    private static Context createNightContext(Context original, boolean forceNight) {
        Configuration newConfig = new Configuration(original.getResources().getConfiguration());
        newConfig.uiMode = forceNight ? 
            Configuration.UI_MODE_NIGHT_YES | (newConfig.uiMode & ~Configuration.UI_MODE_NIGHT_MASK) :
            Configuration.UI_MODE_NIGHT_NO | (newConfig.uiMode & ~Configuration.UI_MODE_NIGHT_MASK);
        return original.createConfigurationContext(newConfig);
    }
}