package com.bitech.vehiclesettings.carapi.constants;

public class CarConnect {
    /**
     * 前单无线充电设置
     * 0x0:CWC OFF
     * 0x1:CWC ON
     */
    public static final class CWC_workingSts {
        public static final int CWC_OFF = 0x0;
        public static final int CWC_ON = 0x1;
    }

    /**
     * 无线充电器充电状态
     * 0x0:No charging
     * 0x1:Charging
     * 0x2:Charging completed
     * 0x3:Charging fault
     */
    public static final class CWC_ChargingSts {
        public static final int NO_CHARGING = 0x0;
        public static final int CHARGING = 0x1;
        public static final int CHARGING_COMPLETED = 0x2;
        public static final int CHARGING_FAULT = 0x3;
    }

    /**
     * 通过DVD软按键控制无线充电器工作模式
     * 0x0:Not active
     * 0x1:OFF
     * 0x2:ON
     * 0x3:Invalid
     */
    public static final class ICC_CWCWorkingStsSet {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int INVALID = 0x3;
    }

    /**
     * 通过DVD软按键控制无线充电器手机遗忘提醒功能的开关
     * 0x0：不动作
     * 0x1: 手机遗忘提醒功能关闭
     * 0x2: 手机遗忘提醒功能打开
     * 0x3：无效
     * CWC收到DVD的0x0、0x3时保持CWC当前手机遗忘提醒功能开关状态
     * 触发一次快发三帧有效，之后返回0x0:Not Active
     */
    public static final class ICC_CWCPhoneforgottenFunStsSet{
        public static final int NOT_ACTIVE = 0x0;
        public static final int PHONE_FORGOTTEN_FUN_OFF = 0x1;
        public static final int PHONE_FORGOTTEN_FUN_ON = 0x2;
        public static final int INVALID = 0x3;
    }

    /**
     * 手机遗忘提醒功能是否打开
     * 0x0:功能关闭
     * 0x1:功能打开
     */
    public static final class CWC_Phoneforgotten_ON_OFF_Sts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 5G开关状态获取
     */
    public static final class FiveG_get {
        public static final int FIVEG_OFF = 0x0;
        public static final int FIVEG_ON = 0x1;
    }

    /**
     * 5G开关状态设置
     */
    public static final class FiveG_set {
        public static final int FIVEG_OFF = 0x0;
        public static final int FIVEG_ON = 0x1;
    }
}
