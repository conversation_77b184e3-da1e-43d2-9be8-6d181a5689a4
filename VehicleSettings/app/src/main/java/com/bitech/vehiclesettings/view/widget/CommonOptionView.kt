package com.bitech.vehiclesettings.view.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.CompoundButton
import androidx.constraintlayout.widget.ConstraintLayout
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.LayoutNewEnergyOptionBinding
import com.bitech.vehiclesettings.utils.DebounceListener
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.SingleSwitchListener
import com.bitech.vehiclesettings.view.widget.CommonOptionView.CommonOptionListener

/**
 * @Description: 通用选项View.
 **/
class CommonOptionView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var isEnable: Boolean = true
    private var haveSwitch: Boolean = true
    private var haveTips: Boolean = true
    private var haveMore: Boolean = true
    private var swChecked: Boolean = false
    private var textTitle: String = ""
    private var textContent: String = ""
    private val binding: LayoutNewEnergyOptionBinding
    private var optionListener: CommonOptionListener? = null
    private var isUserTouching = false

    init {

        binding = LayoutNewEnergyOptionBinding.bind(
            LayoutInflater.from(context).inflate(
                R.layout.layout_new_energy_option, this, true
            )
        )

        val ta = context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.CommonOptionView,
            0,
            0
        )
        val count = ta.indexCount
        for (i in 0 until count) {
            val index = ta.getIndex(i)
            if (index == R.styleable.CommonOptionView_enable) {
                isEnable = ta.getBoolean(index, true)
            } else if (index == R.styleable.CommonOptionView_haveSwitch) {
                haveSwitch = ta.getBoolean(index, true)
            } else if (index == R.styleable.CommonOptionView_haveTips) {
                haveTips = ta.getBoolean(index, true)
            } else if (index == R.styleable.CommonOptionView_haveMore) {
                haveMore = ta.getBoolean(index, true)
            } else if (index == R.styleable.CommonOptionView_swChecked) {
                swChecked = ta.getBoolean(index, false)
            } else if (index == R.styleable.CommonOptionView_textTitle) {
                textTitle = ta.getString(index) ?: ""
            } else if (index == R.styleable.CommonOptionView_textContent) {
                textContent = ta.getString(index) ?: ""
            }
        }
        ta.recycle()

    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        initView()
    }

    private fun initView() {
        setTitleText(textTitle)
        setContentText(textContent)
        setEnable(isEnable)
        binding.apply {
            swOption.isChecked = swChecked
            swOption.visibility = if (haveSwitch) View.VISIBLE else View.GONE
            ivTips.visibility = if (haveTips) View.VISIBLE else View.INVISIBLE
            ivMarkerMore.visibility = if (haveMore) View.VISIBLE else View.INVISIBLE
            tvContent.visibility = if (textContent.isNotBlank()) View.VISIBLE else View.GONE
        }
        binding.swOption.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                LogUtil.i(TAG, "swOption isUserTouching")
                isUserTouching = true
            }
            false
        }
        binding.swOption.setOnCheckedChangeListener(object : SingleSwitchListener() {
            override fun onSingleCheckedChanged(buttonView: CompoundButton, isChecked: Boolean) {
                LogUtil.i(TAG, "swOption isChecked: $isChecked")
                if (isUserTouching) {
                    isUserTouching = false
                    optionListener?.onOptionSwitchChange(buttonView, isChecked)
                }
            }
        })
        binding.ivTips.setOnClickListener(object : DebounceListener("OV_ivTips") {
            override fun onDebounceClick(v: View) {
                optionListener?.onTipsIconClick(v)
            }
        })
    }

    fun setEnable(enable: Boolean) {
//        LogUtil.i(TAG, "setEnable: $enable")
        binding.swOption.alpha = if (enable) 1f else 0.5f
        binding.tvTitle.alpha = if (enable) 1f else 0.5f
        binding.tvContent.alpha = if (enable) 1f else 0.5f
        if (!enable) {
            binding.swOption.isChecked = false
        }
        binding.swOption.isEnabled = enable
    }

    fun setSwitchOpen(open: Boolean) {
        if (binding.swOption.isChecked == open) {
//            LogUtil.i(TAG, "setSwitchOpen: ==")
            return
        }
//        LogUtil.i(TAG, "setSwitchOpen: $open")
        binding.swOption.isChecked = open
    }

    fun setTitleText(text: String) {
        binding.tvTitle.text = text
    }

    fun setContentText(text: String) {
        binding.tvContent.text = text
        binding.tvContent.visibility = if (text.isNotBlank()) View.VISIBLE else View.GONE
    }

    fun setCommonOptionListener(commonOptionListener: CommonOptionListener) {
        optionListener = commonOptionListener
    }

    interface CommonOptionListener {

        fun onTipsIconClick(view: View)

        fun onOptionSwitchChange(buttonView: CompoundButton, isChecked: Boolean)

    }


    companion object {

        private const val TAG = "CommonOptionView"

    }
}

fun CommonOptionView.onOptionsListener(block: OptionsChangeScope.() -> Unit) {
    val scope = OptionsChangeScope()
    block(scope)
    setCommonOptionListener(object : CommonOptionListener {
        override fun onTipsIconClick(view: View) {
            scope.onTipsIconClick?.invoke(view)
        }

        override fun onOptionSwitchChange(buttonView: CompoundButton, isChecked: Boolean) {
            scope.onOptionSwitchChange?.invoke(buttonView, isChecked)
        }
    })
}

class OptionsChangeScope {
    var onTipsIconClick: ((View) -> Unit)? = null
    var onOptionSwitchChange: ((CompoundButton, Boolean) -> Unit)? = null
}
