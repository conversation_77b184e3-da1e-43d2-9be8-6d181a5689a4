package com.bitech.vehiclesettings.view.carsetting;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertCLockTipsBinding;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;


public class LockTipsUIAlert extends BaseDialog {
    private static final String TAG = LockTipsUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;

    public LockTipsUIAlert(@NonNull Context context) {
        super(context);
    }

    public LockTipsUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    public LockTipsUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        LockTipsUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;

        protected DialogAlertCLockTipsBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private LockTipsUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public LockTipsUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new LockTipsUIAlert(context,
                        R.style.Dialog);
            binding = DialogAlertCLockTipsBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            layoutParams.height = 508;
            window.setAttributes(layoutParams);

            initPicker();

            return dialog;
        }

        private void initPicker() {
            binding.spvPicker.setItems(R.string.str_carsetting_lock_tips_dg_1, R.string.str_carsetting_lock_tips_dg_2);
            binding.spvPicker.setSelectedIndex(onProgressChangedListener.getInitStatus(), false);
            binding.spvPicker.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    onProgressChangedListener.onSwitch(index);
                }

                @Override
                public void onItemClicked(int index, String text) {

                }
            });
        }

        public void updateLockTipsUI(int status) {
            binding.spvPicker.setSelectedIndex(status, true);
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(int index);

        int getInitStatus();
    }
}
