package com.bitech.vehiclesettings.view.newenergy

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogDetailTipsBinding
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @Description: 自定义详情提示弹窗.
 **/
class ComDetailTipDialog(context: Context) : BaseDialog(context) {

    // 提示弹窗视图对象
    private lateinit var binding: DialogDetailTipsBinding

    // 提示弹窗消息
    private lateinit var message: String

    // 弹窗主题
    private var titleText: String = context.getString(R.string.app_name)

    private var gravity = Gravity.CENTER
    private var textSize = 40f
    private var y = 0
    private var width = 1242
    private var height = WindowManager.LayoutParams.WRAP_CONTENT

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        binding = DialogDetailTipsBinding.bind(
            layoutInflater.inflate(R.layout.dialog_detail_tips, null)
        )
        setContentView(binding.root)
        // 初始化视图
        initView()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        // 设置对话框窗口属性
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG
        attributes?.width = width
        attributes?.height = height
        attributes?.gravity = gravity
        if (y != 0) {
            attributes?.y = y
        }
        window?.attributes = attributes
        binding.apply {
            tvTitle.text = titleText
            tvContent.text = message
        }
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    /**
     * 设置标题警告提示语.
     *
     * @param title 弹窗标题
     * @param tips 提示内容
     */
    fun setTips(title: String?, tips: String) {
        if (title != null) {
            titleText = title
        }
        message = tips
    }

    fun setTextConfiguration(gravity: Int, textSize: Float) {
        this.gravity = gravity
        this.textSize = textSize
    }

    fun setDialogWidthHeight(width: Int, height: Int, y: Int) {
        this.width = width
        this.height = height
        this.y = y
    }

    companion object {
        // 日志标志位
        private const val TAG = "ComDetailTipDialog"
    }
}
