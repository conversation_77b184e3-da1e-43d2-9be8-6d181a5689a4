package com.bitech.vehiclesettings.utils

import android.content.Context
import android.content.Intent
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.activity.MainActivity

object DialogNavigationUtils {
    /**
     * 启动主活动并携带额外参数
     * @param context 上下文对象
     * @param targetTab 目标标签页索引
     * @param targetDialog 目标对话框标识
     * @param operation 操作类型标识
     */
    @JvmStatic
    fun launchMainActivity(
        context: Context,
        targetTab: Int,
        targetDialog: Int = CommonConst.INVALID_DIALOG,
        operation: Int
    ) {
        val intent = Intent(MyApplication.getContext(), MainActivity::class.java).apply {
            putExtra(CommonConst.TARGET_TAB, targetTab)
            putExtra(CommonConst.TARGET_DIALOG, targetDialog)
            putExtra(CommonConst.OPERATION, operation)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        MyApplication.getContext().startActivity(intent)
    }

    /**
     * 提供弹出热点的intent
     */
    @JvmStatic
    fun getHotspotIntent(
    ): Intent {
        val intent = Intent(MyApplication.getContext(), MainActivity::class.java).apply {
            putExtra(CommonConst.TARGET_TAB, MainActivity.MainTabIndex.CONDITION)
            putExtra(CommonConst.TARGET_DIALOG, 2)
            putExtra(CommonConst.OPERATION, CommonConst.DIALOG_OPEN)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        return intent
    }
}
