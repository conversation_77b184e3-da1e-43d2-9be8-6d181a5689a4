package com.bitech.vehiclesettings.service.atmosphere;

import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.atmosphere.AmbLigBean;
import com.bitech.platformlib.manager.LightManager;

import java.util.ArrayList;

/**
 * 渐变
 */
public class GradientEffectService {
    private static final String TAG = GradientEffectService.class.getSimpleName();
    private static final long TARGET_INTERVAL_MS = 1700;
    private final Handler mHandler;
    private Runnable mTaskRunnable;
    private volatile boolean isRunning = false;
    private long mLastExecutionTime;
    private ArrayList<AmbLigBean> list;
    private int count = 0;
    private LightManager manager = (LightManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_LIGHT_MANAGER);
    private static volatile GradientEffectService instance;

    public static GradientEffectService getInstance() {
        if (instance == null) {
            synchronized (GradientEffectService.class) {
                if (instance == null) {
                    instance = new GradientEffectService();
                }
            }
        }
        return instance;
    }

    public GradientEffectService() {
        initData();
        mHandler = new Handler(Looper.getMainLooper());

    }


    private void initData() {
        if (list == null) {
            list = new ArrayList<>();
        }
        // 0x7F=Invalid：（无论什么情况，亮度等级保持不变）
        //1级亮度：0x4（4%）   （乘员屏幕操作设定项）
        //2级亮度：0xF（15%）  （乘员屏幕操作设定项）
        //3级亮度：0x1D（29%） （乘员屏幕操作设定项）
        //4级亮度：0x3A（58%）  （乘员屏幕操作设定项）
        //5级亮度：0x64（100%）  （乘员屏幕操作设定项）
        //100级亮度值（音乐律动）：0x0-0x64=0~100%
        // 时长信号；
        // 渐亮渐灭信号为0时，渐亮渐灭时间为0，代表本帧指令无渐亮渐灭动作：
        // 渐亮渐灭信号为不为0时，存在渐亮渐灭时间设定，代表本帧指令有渐亮渐灭动作，相应节点按照设定执行：
        int color[] = {1, 4, 7, 10, 12, 15, 18, 20, 22, 25, 28, 32, 34, 36, 39, 42, 45, 48, 51, 53, 56, 59, 61, 63};
        for (int i = 0; i < color.length; i++) {
            list.add(getAmbLigUp(color[i]));
            list.add(getAmbLigDown(color[i]));
        }
    }

    /**
     * 渐亮
     *
     * @param colorIdx
     * @return
     */
    private AmbLigBean getAmbLigUp(int colorIdx) {
        AmbLigBean up1 = new AmbLigBean().setAll();
        up1.setAmbLigBriAdj(0x64).setAmbLigColorAdj(colorIdx).setAmbLigFadeINorOUTStepTi(30).setAmbLigFlngModSel(0);
        return up1;
    }

    private AmbLigBean getAmbLigDown(int colorIdx) {
        AmbLigBean down1 = new AmbLigBean().setAll();
        down1.setAmbLigBriAdj(0xF).setAmbLigColorAdj(colorIdx).setAmbLigFadeINorOUTStepTi(30);
        return down1;
    }

    // 启动任务
    public void start() {
        if (isRunning) {
            return;
        }
        isRunning = true;
        mLastExecutionTime = SystemClock.elapsedRealtime();
        mTaskRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isRunning) {
                    return;
                }
                // 1. 执行任务
                doTask();
                // 2. 计算下一次执行时间（动态补偿误差）
                long currentTime = SystemClock.elapsedRealtime();
                long elapsed = currentTime - mLastExecutionTime;
                long nextDelay = Math.max(0, TARGET_INTERVAL_MS - elapsed);
                // 3. 递归调度下一次任务
                if (count % 2 == 1) {
                    mHandler.postDelayed(this, nextDelay - 200);
                } else {
                    mHandler.postDelayed(this, nextDelay);
                }

                mLastExecutionTime = currentTime + nextDelay;
            }
        };
        mHandler.post(mTaskRunnable);
    }

    // 停止任务
    public void stop() {
        isRunning = false;
        if (mTaskRunnable != null) {
            mHandler.removeCallbacks(mTaskRunnable);
        }
    }

    // 自定义任务逻辑
    private void doTask() {
        //1.	渐变动作的亮度变化范围是20%~100%亮度范围；
        //2.	呼吸动作颜色变化顺序为:1,4,7,10,12,15,18,20,22,25,28,32,34,36,39,42,45,48,51,53,56,59,61,63，共计24个颜色循环往复；
        //3.	按颜色顺序循环动作：先换色+渐亮1.5s，然后保持200ms，下一步渐灭1.5s，然后换色+渐亮1.5s
        Log.d(TAG, "渐变功能 doTask: ");

        if (list != null && count < list.size()) {
            AmbLigBean ambLigBean = list.get(count);
            //Log.d(TAG, "ambLigBean: " + ambLigBean.getAmbLigBriAdj() + ",颜色：" + ambLigBean.getAmbLigColorAdj());
            //调用氛围灯接口
            manager.setLightAmbLightCan(ambLigBean);
        }
        count++;
        if (count >= 24) {
            count = 0;
        }
    }
}
