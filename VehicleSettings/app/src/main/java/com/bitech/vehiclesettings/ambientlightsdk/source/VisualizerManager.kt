package com.bitech.vehiclesettings.ambientlightsdk.source

import android.media.audiofx.Visualizer
import android.util.Log
import kotlin.math.hypot

/**
 * 创建单例：
 *      https://blog.csdn.net/sunshine_0707/article/details/135393708
 *
 * 使用单例模式防止Service销毁后重启，Visualizer被初始化两次
 */
class VisualizerManager private constructor() : Visualizer.OnDataCaptureListener {
    val TAG: String = this.javaClass.simpleName
    private var visualizer: Visualizer? = null
    private val audioVisualConverter = AudioVisualConverter()
    private var mMusicDataListener: MusicDataListener? = null

    //使用companion object懒加载方式创建单例
    companion object {
        val instance: VisualizerManager by lazy { VisualizerManager() }
    }

    fun initVisualizer() {
        try {
            visualizer = Visualizer(0)
            val captureSize = Visualizer.getCaptureSizeRange()[1]
            val captureRate = Visualizer.getMaxCaptureRate() * 3 / 4
            visualizer!!.captureSize = captureSize
            visualizer!!.setDataCaptureListener(this, captureRate, true, true)
            visualizer!!.scalingMode = Visualizer.SCALING_MODE_NORMALIZED
            visualizer!!.enabled = true

        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "请检查录音权限")

        }
    }



    fun registerMusicDataListener(listener: MusicDataListener) {
        mMusicDataListener?.forceDestroy()//将之前的Service销毁
        mMusicDataListener = listener
    }

    fun setVisualizerEnable(enable: Boolean) {
        visualizer!!.enabled = enable
    }

    override fun onWaveFormDataCapture(
        visualizer: Visualizer?,
        waveform: ByteArray,
        samplingRate: Int
    ) {
        val magnitudes = FloatArray(waveform.size / 2)
        var max = 0
        for (i in magnitudes.indices) {
            magnitudes[i] = hypot(
                waveform[2 * i].toDouble(),
                waveform[2 * i + 1].toDouble()
            ).toFloat()
            if (magnitudes[max] < magnitudes[i]) {
                max = i
            }
        }
        val size = Visualizer.getMaxCaptureRate() * 3 / 4

        val currentFrequency = max * size.toDouble() / waveform.size

        mMusicDataListener?.onWaveChange(currentFrequency)
    }

    override fun onFftDataCapture(visualizer: Visualizer?, fft: ByteArray, samplingRate: Int) {
//        AppLog.i(
//            TAG,
//            "onFftDataCapture(), fft size=${fft.size}  db: ${audioVisualConverter.getVoiceSize(fft)}"
//        )
        val db = audioVisualConverter.getVoiceSize(fft)
        mMusicDataListener?.onFftChange(db)
    }

    interface MusicDataListener {
        fun onWaveChange(freq: Double)
        fun onFftChange(db: Int)
        fun forceDestroy()
    }


}