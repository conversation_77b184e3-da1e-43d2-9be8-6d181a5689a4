package com.bitech.vehiclesettings.bean;

public class GlobalVar {
    private static boolean isSetDisplay;
    private static boolean isSetSystem;
    private static boolean isSetVoice;

    public static boolean isIsSetDisplay() {
        return isSetDisplay;
    }

    public static void setIsSetDisplay(boolean isSetDisplay) {
        GlobalVar.isSetDisplay = isSetDisplay;
    }
    public static boolean isIsSetSystem() {
        return isSetSystem;
    }
    public static void setIsSetSystem(boolean isSetSystem) {
        GlobalVar.isSetSystem = isSetSystem;
    }
    public static boolean isIsSetVoice() {
        return isSetVoice;
    }
    public static void setIsSetVoice(boolean isSetVoice) {
        GlobalVar.isSetVoice = isSetVoice;
    }
}
