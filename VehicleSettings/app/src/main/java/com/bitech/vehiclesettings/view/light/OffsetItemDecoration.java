package com.bitech.vehiclesettings.view.light;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

public class OffsetItemDecoration extends RecyclerView.ItemDecoration {
    private int leftoffset;
    private int rightOffset;

    public OffsetItemDecoration(int leftoffset, int rightOffset) {
        this.leftoffset = leftoffset;
        this.rightOffset = rightOffset;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        if (position == 0){// 第一个item
            outRect.left = leftoffset;
        }
        if (position == state.getItemCount() - 1) { // 最后一个item
            outRect.right = rightOffset;
        }
    }
}