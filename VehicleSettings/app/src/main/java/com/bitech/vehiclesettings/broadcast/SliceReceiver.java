package com.bitech.vehiclesettings.broadcast;

import android.annotation.SuppressLint;
import android.app.slice.Slice;
import android.car.Car;
import android.car.media.CarAudioManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;
import android.widget.Toast;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDisplay;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.manager.CarWifiManager;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.presenter.light.LightInPresenter;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.provider.ReceiverAction;
import com.bitech.vehiclesettings.service.NewEnergyLifeCycle;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.carsetting.ChildLockUIAlert;
import com.bitech.vehiclesettings.view.common.ComfirmUIAlert;
import com.bitech.vehiclesettings.view.display.CleanUIAlert;
import com.bitech.vehiclesettings.view.negative.ParkPowerCancelUIAlert;
import com.bitech.vehiclesettings.view.negative.ParkPowerCtrlUIAlert;
import com.bitech.vehiclesettings.view.negative.SunShadeUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.RearMirrorUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.WashCarModeUIAlert;
import com.bitech.vehiclesettings.viewmodel.DisplayViewModel;
import com.bitech.vehiclesettings.viewmodel.VoiceViewModel;

import java.util.concurrent.atomic.AtomicInteger;

@SuppressLint("StaticFieldLeak")
public class SliceReceiver extends BroadcastReceiver {

    private static final String TAG = SliceReceiver.class.getName();
    private QuickManager quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);
    private DrivingManager drivingManager = (DrivingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_DRIVING_MANAGER);
    private NewEnergyManager newEnergyManager = (NewEnergyManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_ENERGY_MANAGER);
    private LightManager lightManager = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
    private VoicePresenter voicePresenter = VoicePresenter.getInstance();
    private CarAudioManager carAudioManager = (CarAudioManager) Car.createCar(MyApplication.getContext()).getCarManager(Car.AUDIO_SERVICE);

    static RearMirrorUIAlert.Builder rearMirrorUIAlertBuilder;
    static ComfirmUIAlert.Builder vehiclePowerOffUIAlertBuilder;
    static SunShadeUIAlert.Builder sunShadeUIAlertBuilder;
    static ParkPowerCtrlUIAlert.Builder parkPowerCtrlUIAlertBuilder;
    static ParkPowerCancelUIAlert.Builder parkPowerCancelUIAlertBuilder;
    static ChildLockUIAlert.Builder childLockUIAlertBuilder;
    static WashCarModeUIAlert.Builder washCarModeUIAlertBuilder;

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "onReceive: " + intent.getAction());
        switch (intent.getAction()) {
            case ReceiverAction.ACTION_POWER_OFF_CLICK:
                Log.d(TAG, "setVehiclePowerOff: 车辆下电");
                showPowerOffDialog(context);
                break;
            case ReceiverAction.ACTION_LIGHT:
                Log.d(TAG, "light: 亮度");
                int light = intent.getIntExtra(Slice.EXTRA_RANGE_VALUE, -1);
                if (light >= 0) {
                    Log.d(TAG, "接收到亮度值: " + light);
                    light = light == 0 ? 1 : light;
                    DisplayPresenter.getInstance().setZKPBrightness(light);
                    DisplayViewModel.setZkp(light);
                }
                boolean isAutoMode = intent.getBooleanExtra("isAutoMode", false);
                Log.d(TAG, "自动亮度开关状态: " + isAutoMode);
                DisplayPresenter.getInstance().setZKPAuto(CommonUtils.BoolToInt(isAutoMode));
                break;
            case ReceiverAction.ACTION_VOICE:
                Log.d(TAG, "voice: 音量");
                int voice = intent.getIntExtra(Slice.EXTRA_RANGE_VALUE, -1);
                if (voice >= 0) {
                    Log.d(TAG, "接收到音量值: " + voice);
                    int groupId = carAudioManager.getCurActiveGroupId();
                    if (groupId == voicePresenter.groupMedia) {
                        Log.d(TAG, "媒体音频焦点");
                        voicePresenter.setVoiceMedia(voice);
                        VoiceViewModel.setMediaProgress(voice);
                    } else if (groupId == voicePresenter.groupPhone) {
                        Log.d(TAG, "电话音频焦点");
                        voicePresenter.setVoicePhone(voice);
                        VoiceViewModel.setPhoneProgress(voice);
                    } else if (groupId == voicePresenter.groupNavi) {
                        voicePresenter.setVoiceNavi(voice);
                        VoiceViewModel.setNaviProgress(voice);
                        Log.d(TAG, "导航音频焦点");
                    } else if (groupId == voicePresenter.groupVR) {
                        voicePresenter.setVoiceVR(voice);
                        VoiceViewModel.setVRProgress(voice);
                        Log.d(TAG, "语音音频焦点");
                    } else {
                        Log.d(TAG, "媒体音频焦点");
                        voicePresenter.setVoiceMedia(voice);
                        VoiceViewModel.setMediaProgress(voice);
                    }
                }
                break;
            case ReceiverAction.ACTION_CLEAN_SCREEN:
                Log.d(TAG, "clean_screen: 清屏");
                if (drivingManager.getVCU_PRNDGearAct() != CarDriving.VCU_PRNDGearAct.P) {
                    EToast.showToast(context, context.getString(R.string.str_system_international_privacy_statement_not_p_gear), 2000, true);
                    return;
                }
                showCleanUIAlert(context);
                break;
            case ReceiverAction.ACTION_UNLOCK_FUEL_PORT:
                Log.d(TAG, "unlock_fuel_port: 加油小门");
                setUnlockFuelPort();
                break;
            case ReceiverAction.ACTION_LOCK_SCREEN:
                Log.d(TAG, "lock_screen: 屏保");
                LockScreen();
                break;
            case ReceiverAction.ACTION_EPB:
                Log.d(TAG, "EPB: 驻车制动");
                epb(context);
                break;
            case ReceiverAction.ACTION_ESP:
                Log.d(TAG, "ESP");
                esp();
                break;
            case ReceiverAction.ACTION_HDC:
                Log.d(TAG, "HDC 陡坡缓降");
                hdc();
                break;
            case ReceiverAction.ACTION_AUTO_HOLD:
                Log.d(TAG, "AUTO HOLD 自动驻车");
                autoHold();
                break;
            case ReceiverAction.ACTION_AVAS:
                Log.d(TAG, "AVAS");
                avas();
                break;
            case ReceiverAction.ACTION_BOOK_CHARGE:
                Log.d(TAG, "BOOK_CHARGE 预约充电");
                bookCharge();
                break;
            case ReceiverAction.ACTION_WIPER_LEVEL:
                Log.d(TAG, "WIPER_LEVEL 雨刮灵敏度");
                wiperLevel();
                break;
            case ReceiverAction.ACTION_CENTER_LOCK:
                Log.d(TAG, "CENTER_LOCK 中控锁");
                centerLock();
                break;
            case ReceiverAction.ACTION_MIRROR_FOLD:
                Log.d(TAG, "MIRROR_FOLD 后视镜折叠");
                mirrorFold();
                break;
            case ReceiverAction.ACTION_MIRROR_ADJUST:
                Log.d(TAG, "MIRROR_ADJUST 后视镜调节");
                mirrorAdjust(context);
                break;
            case ReceiverAction.ACTION_TAILGATE:
                Log.d(TAG, "TAILGATE 后尾门");
                tailgate();
                break;
            case ReceiverAction.ACTION_WINDOW_LOCK:
                Log.d(TAG, "WINDOW_LOCK 车窗锁");
                windowLock();
                break;
            case ReceiverAction.ACTION_CHILD_LOCK:
                Log.d(TAG, "CHILD_LOCK 儿童锁");
                childLock(context);
                break;
            case ReceiverAction.ACTION_LIGHT_MODE:
                Log.d(TAG, "LIGHT_MODE 氛围灯");
                lightMode(context);
                break;
            case ReceiverAction.ACTION_BROADCAST:
                Log.d(TAG, "BROADCAST 热点");
                broadcast(context);
                break;
            case ReceiverAction.ACTION_DISPLAY_MODE_DAY:
                Log.d(TAG, "显示模式 浅色");
                DisplayPresenter.getInstance().setDisplayMode(CarDisplay.DAY, false);
                break;
            case ReceiverAction.ACTION_DISPLAY_MODE_NIGHT:
                Log.d(TAG, "显示模式 深色");
                DisplayPresenter.getInstance().setDisplayMode(CarDisplay.NIGHT, false);
                break;
            case ReceiverAction.ACTION_DISPLAY_MODE_AUTO:
                Log.d(TAG, "显示模式 自动");
                DisplayPresenter.getInstance().setDisplayMode(CarDisplay.AUTO, false);
                break;
            case ReceiverAction.ACTION_WINDOW_MODE_OPEN:
                Log.d(TAG, "升窗");
                windowModeOpen();
                break;
            case ReceiverAction.ACTION_WINDOW_MODE_AIR:
                Log.d(TAG, "透气");
                windowModeAir();
                break;
            case ReceiverAction.ACTION_WINDOW_MODE_CLOSE:
                Log.d(TAG, "降窗");
                windowModeClose();
                break;
            case ReceiverAction.ACTION_HEAD_LAMP_OFF:
                Log.d(TAG, "大灯关闭");
                headLampOFF();
                break;
            case ReceiverAction.ACTION_HEAD_LAMP_LOCATION:
                Log.d(TAG, "大灯位置");
                headLampLocation();
                break;
            case ReceiverAction.ACTION_HEAD_LAMP_NEAR:
                Log.d(TAG, "大灯近光");
                headLampNear();
                break;
            case ReceiverAction.ACTION_HEAD_LAMP_AUTO:
                Log.d(TAG, "大灯自动");
                headLampAuto();
                break;
            case ReceiverAction.ACTION_SUNSHADE:
                Log.d(TAG, "遮阳帘");
                sunshade(context);
                break;
            case ReceiverAction.ACTION_BATTERY_LIFE:
                Log.d(TAG, "驻车保电");
                batteryLife(context);
                break;
        }

    }

    private void batteryLife(Context context) {
        int parkPowerStatus = newEnergyManager.getParkPowerStatus();
        Log.d(TAG, "驻车保电获取信号: " + parkPowerStatus);
        switch (parkPowerStatus) {
            case CarNewEnergy.ParkPowerSet.OFF:
                int vcuPrndGearAct = drivingManager.getVCU_PRNDGearAct();
                if (vcuPrndGearAct != CarDriving.VCU_PRNDGearAct.P) {
                    // 挡位非P
                    EToast.showToast(context, context.getString(R.string.str_p_use), 2000, true);
                    return;
                }

                //电池SOC电量不足
                AtomicInteger mCurrentSOC = NewEnergyLifeCycle.Companion.getMCurrentSOC();
                Log.d(TAG, "SOC电量: " + mCurrentSOC.get());
                if (mCurrentSOC.get() < 5) {
                    EToast.showToast(context, context.getString(R.string.str_battery_low), 2000, true);
                    return;
                }


                if (parkPowerCtrlUIAlertBuilder != null && parkPowerCtrlUIAlertBuilder.isShowing()) {
                    return;
                }
                if (parkPowerCtrlUIAlertBuilder == null) {
                    parkPowerCtrlUIAlertBuilder = new ParkPowerCtrlUIAlert.Builder(context);
                }
                parkPowerCtrlUIAlertBuilder.create().show();
                break;
            case CarNewEnergy.ParkPowerSet.ON:
                if (parkPowerCancelUIAlertBuilder != null && parkPowerCancelUIAlertBuilder.isShowing()) {
                    return;
                }
                if (parkPowerCancelUIAlertBuilder == null) {
                    parkPowerCancelUIAlertBuilder = new ParkPowerCancelUIAlert.Builder(context);
                }
                parkPowerCancelUIAlertBuilder.create().show();
                break;
            default:
                Log.d("ParkPowerCtrlUIAlert", "无效信号: " + parkPowerStatus);
                EToast.showToast(context, "无效信号", 2000, true);
                break;
        }


    }

    private void sunshade(Context context) {
        if (sunShadeUIAlertBuilder != null && sunShadeUIAlertBuilder.isShowing()) {
            return;
        }
        if (sunShadeUIAlertBuilder == null) {
            sunShadeUIAlertBuilder = new SunShadeUIAlert.Builder(context);
        }
        sunShadeUIAlertBuilder.create().show();
    }

    private void windowModeClose() {
        quickManager.setWindowVentilate(CarQuickControl.SetWindowSts.CLOSE);
    }

    private void windowModeAir() {
        quickManager.setWindowVentilate(CarQuickControl.SetWindowSts.AIR);
    }

    private void windowModeOpen() {
        quickManager.setWindowVentilate(CarQuickControl.SetWindowSts.OPEN);
    }

    private void headLampOFF() {
        lightManager.setLightMainSwitchSts(CarLight.LightMainSwitchSts.OFF);
    }

    private void headLampLocation() {
        lightManager.setLightMainSwitchSts(CarLight.LightMainSwitchSts.POSITION_LAMP_SWITCH);
    }

    private void headLampNear() {
        lightManager.setLightMainSwitchSts(CarLight.LightMainSwitchSts.LOW_BEAM_SWITCH);
    }

    private void headLampAuto() {
        lightManager.setLightMainSwitchSts(CarLight.LightMainSwitchSts.AUTO);
    }

    private void broadcast(Context context) {
        CarWifiManager carWifiManager = CarWifiManager.Companion.getInstance();
        if (carWifiManager.getHotspotState()) {
            Log.d(TAG, "BROADCAST: 关闭热点");
            carWifiManager.closeWifiHotspot();
        } else {
            Log.d(TAG, "BROADCAST: 打开热点");
            carWifiManager.openWifiHotspot();
        }
    }

    private void lightMode(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_LIGHT_MODE);
        LightInPresenter presenter = new LightInPresenter(context);
        LightInBean bean = presenter.getlInBean();
        int pos;
        if (bean.getThemeMode() == 0) {
            pos = 0;
        } else {
            if (bean.getLightSel() == 0) {
                pos = 1;
            } else {
                pos = 2;
            }
        }
        switch (presenter.getLightSw()) {
            case CommonConst.AtmosphereSwitch.OPEN:
                Log.d(TAG, "氛围灯: 关闭");
                presenter.setAmbLightSw(CommonConst.AtmosphereSwitch.CLOSE, pos);
                break;
            case CommonConst.AtmosphereSwitch.CLOSE:
                Log.d(TAG, "氛围灯: 开启");
                presenter.setAmbLightSw(CommonConst.AtmosphereSwitch.OPEN, pos);
                break;
        }
    }

    private void childLock(Context context) {
        showChildLockDialog(context);

    }

    private void windowLock() {
        int windowLock = quickManager.getWindowLock();
        switch (windowLock) {
            case CarQuickControl.GetWindowLockSts.PERMIT:
                Log.d(TAG, "车窗锁: 关闭");
                quickManager.setWindowLock(CarQuickControl.SetWindowLockSts.INHIBIT);
                break;
            case CarQuickControl.GetWindowLockSts.INHIBIT:
                Log.d(TAG, "车窗锁: 锁车");
                quickManager.setWindowLock(CarQuickControl.SetWindowLockSts.PERMIT);
        }
    }

    private void tailgate() {
        int rearTailGate = quickManager.getRearTailGate();
        switch (rearTailGate) {
            case CarQuickControl.GetRearTailGateSts.OPEN:
            case CarQuickControl.GetRearTailGateSts.SECONDARY:
                Log.d(TAG, "后尾门: 关闭");
                quickManager.setRearTailGate(CarQuickControl.SetRearTailGateSts.OFF);
                break;
            case CarQuickControl.GetRearTailGateSts.LATCHED:
                Log.d(TAG, "后尾门: 打开");
                quickManager.setRearTailGate(CarQuickControl.SetRearTailGateSts.ON);
                break;
            default:
                Log.d(TAG, "无效信号");
        }
    }

    private void mirrorAdjust(Context context) {
        if (rearMirrorUIAlertBuilder != null && rearMirrorUIAlertBuilder.isShowing()) {
            return;
        }
        if (rearMirrorUIAlertBuilder == null) {
            rearMirrorUIAlertBuilder = new RearMirrorUIAlert.Builder(context);
            rearMirrorUIAlertBuilder.setGlobalAlert(true);

        }
        rearMirrorUIAlertBuilder.create().show();
    }

    private void mirrorFold() {
        int rearMirror = quickManager.getRearMirror();
        switch (rearMirror) {
            case CarQuickControl.GetRearMirrorFoldSts.FOLD:
                Log.d(TAG, "后视镜折叠: 展开");
                quickManager.setRearMirror(CarQuickControl.SetRearMirrorFoldSts.UNFOLD);
                break;
            case CarQuickControl.GetRearMirrorFoldSts.UNFOLD:
                Log.d(TAG, "后视镜折叠: 折叠");
                quickManager.setRearMirror(CarQuickControl.SetRearMirrorFoldSts.FOLD);
                break;
            default:
                Log.d(TAG, "无效信号");
        }

    }

    private void centerLock() {
        int centerLock = quickManager.getCenterLock();
        switch (centerLock) {
            case CarQuickControl.GetCentralLockSts.LOCKED:
            case CarQuickControl.GetCentralLockSts.SUPERLOCKED:
                Log.d(TAG, "中控锁: 解锁");
                quickManager.setCenterLock(CarQuickControl.SetCentralLockSts.UNLOCK);
                break;
            case CarQuickControl.GetCentralLockSts.UNLOCKED:
                Log.d(TAG, "中控锁: 锁定");
                quickManager.setCenterLock(CarQuickControl.SetCentralLockSts.LOCK);
                break;
            default:
                Log.d(TAG, "无效信号");
                break;
        }
    }

    private void wiperLevel() {
        int wiperSensitivity = quickManager.getWiperSensitivity();
        switch (wiperSensitivity) {
            case CarQuickControl.GetWipeSensitivitySts.LEVEL_1:
                Log.d(TAG, "雨刮灵敏度: 低");
                quickManager.setWiperSensitivity(CarQuickControl.SetWiperSensitivitySts.LEVEL_2);
                break;
            case CarQuickControl.GetWipeSensitivitySts.LEVEL_2:
                Log.d(TAG, "雨刮灵敏度: 中");
                quickManager.setWiperSensitivity(CarQuickControl.SetWiperSensitivitySts.LEVEL_3);
                break;
            case CarQuickControl.GetWipeSensitivitySts.LEVEL_3:
                Log.d(TAG, "雨刮灵敏度: 高");
                quickManager.setWiperSensitivity(CarQuickControl.SetWiperSensitivitySts.LEVEL_4);
                break;
            case CarQuickControl.GetWipeSensitivitySts.LEVEL_4:
                Log.d(TAG, "雨刮灵敏度: 最高");
                quickManager.setWiperSensitivity(CarQuickControl.SetWiperSensitivitySts.LEVEL_1);
                break;
            default:
                Log.d(TAG, "无效信号");
                break;
        }
    }

    private void bookCharge() {
        int bookChargeSwitch = newEnergyManager.getBookChargeSwitch();
        switch (bookChargeSwitch) {
            case CarNewEnergy.BookChargeSts.OFF:
                Log.d(TAG, "打开预约充电");
                newEnergyManager.setBookChargeSwitch(CarNewEnergy.BookChargeReq.BOOK_SET);
                break;
            case CarNewEnergy.BookChargeSts.ON:
                Log.d(TAG, "关闭预约充电");
                newEnergyManager.setBookChargeSwitch(CarNewEnergy.BookChargeReq.CANCEL_BOOK);
                break;
            default:
                Log.d(TAG, "无效信号");
                break;
        }
    }

    private void avas() {
        int avas = voicePresenter.getLowSpeedAnalog();
        switch (avas) {
            case CarVoice.AVAS.OFF:
                Log.d(TAG, "打开AVAS");
                voicePresenter.setLowSpeedAnalog(CarVoice.AVAS.ON);
                VoiceViewModel.setLowSpeedAnalog(CarVoice.AVAS.ON);
                break;
            case CarVoice.AVAS.ON:
                Log.d(TAG, "关闭AVAS");
                voicePresenter.setLowSpeedAnalog(CarVoice.AVAS.OFF);
                VoiceViewModel.setLowSpeedAnalog(CarVoice.AVAS.OFF);
                break;
        }
        SliceReceiver.notifyChange(ProviderURI.AVAS);

    }

    private void autoHold() {
        int autoHold = drivingManager.getAutoHold();
        switch (autoHold) {
            case CarDriving.AVHSts.OFF:
                Log.d(TAG, "打开自动驻车");
                drivingManager.setAutoHoldSet(CarDriving.IHU_AutHldSet.ON);
                break;
            case CarDriving.AVHSts.ACTIVE:
                Log.d(TAG, "关闭自动驻车");
                drivingManager.setAutoHoldSet(CarDriving.IHU_AutHldSet.OFF);
                break;
            default:
                Log.d(TAG, "无效信号");
                break;
        }
    }

    private void hdc() {
        int hillDescentControl = drivingManager.getHillDescentControl();
        switch (hillDescentControl) {
            case CarDriving.HDCCtrlSts.OFF:
                Log.d(TAG, "打开陡坡缓降");
                drivingManager.setHillDescentControlSet(CarDriving.TIHU_SetHDCOnOff.ON);
                break;
            case CarDriving.HDCCtrlSts.NOT_ACTIVE:
            case CarDriving.HDCCtrlSts.ON_ACTIVE_BRAKING:
                Log.d(TAG, "关闭陡坡缓降");
                drivingManager.setHillDescentControlSet(CarDriving.TIHU_SetHDCOnOff.OFF);
                break;
            default:
                Log.d(TAG, "无效信号");
                break;
        }
    }

    private void esp() {
        int bodyInfoESC = drivingManager.getBodyInfoESC();
        switch (bodyInfoESC) {
            case CarDriving.ESPSwitchStatus.OFF:
                Log.d(TAG, "打开车身稳定控制");
                drivingManager.setBodyInfoESCSet(CarDriving.Set_ESPFunctionSts.ON);
                break;
            case CarDriving.ESPSwitchStatus.ON:
                Log.d(TAG, "关闭车身稳定控制");
                drivingManager.setBodyInfoESCSet(CarDriving.Set_ESPFunctionSts.OFF);
                break;
            default:
                Log.d(TAG, "无效信号");
                break;
        }
    }

    private void epb(Context context) {
        int carSpeed = drivingManager.getCarSpeed();// 车速
        if (carSpeed >= 3) {
            EToast.showToast(context, context.getText(R.string.str_epb_warn_desc1), Toast.LENGTH_LONG, false);
            return;
        }
        int epb = drivingManager.getEPBStatus();
        switch (epb) {
            case CarDriving.EPBActrSt.APPLIED:
                // 关闭
                Log.d(TAG, "关闭驻车制动");
                int brakePedal = drivingManager.getBrakePedal();// 刹车状态
                if (brakePedal != CarDriving.BrakePedalSts.APPLIED) {
                    EToast.showToast(context, context.getText(R.string.str_epb_warn_desc2), Toast.LENGTH_LONG, false);
                    return;
                }
                Log.d(TAG, "已踩刹车，关闭驻车制动");
                drivingManager.setBodyInfoEPBSet(CarDriving.EPBSetCmd.RELEASE);
                break;
            case CarDriving.EPBActrSt.RELEASED:
            case CarDriving.EPBActrSt.COMPLETELY_RELEASED:
                Log.d(TAG, "打开驻车制动");
                drivingManager.setBodyInfoEPBSet(CarDriving.EPBSetCmd.APPLY);
                break;
            case CarDriving.EPBActrSt.UNKNOW:
            case CarDriving.EPBActrSt.APPLYING:
            case CarDriving.EPBActrSt.RELEASING:
            case CarDriving.EPBActrSt.HALF_APPLIED:
            case CarDriving.EPBActrSt.RESERVED:
                Log.d(TAG, "维持上一状态");
                break;
            default:
                EToast.showToast(context, "无效信号", Toast.LENGTH_LONG, false);
        }
    }

    private void setUnlockFuelPort() {
        int oilLidSwitch = quickManager.getOilLidSwitch();
        switch (oilLidSwitch) {
            case CarQuickControl.ButtonSts.OFF:
                Log.d(TAG, "打开加油小门");
                quickManager.setOilLidSwitch(CarQuickControl.ButtonSts.ON);
                break;
            case CarQuickControl.ButtonSts.ON:
                Log.d(TAG, "关闭加油小门");
                quickManager.setOilLidSwitch(CarQuickControl.ButtonSts.OFF);
                break;
            default:
                Log.d(TAG, "无效信号");
                break;
        }
    }

    private void LockScreen() {
        // TODO 临时方案 先发送信号
        MsgUtil.getInstance().setSignalVal("DrivingInfo/PowerKey", 1);
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            return;
        }
        MsgUtil.getInstance().setSignalVal("DrivingInfo/PowerKey", 0);
    }

    public void showPowerOffDialog(Context context) {
        int vcuPrndGearAct = drivingManager.getVCU_PRNDGearAct();
        if (vcuPrndGearAct != CarDriving.VCU_PRNDGearAct.P) {
            // 挡位非P
            EToast.showToast(context, context.getString(R.string.str_p_use2), 2000, true);
            return;
        }
        if (vehiclePowerOffUIAlertBuilder != null && vehiclePowerOffUIAlertBuilder.isShowing()) {
            return;
        }
        if (vehiclePowerOffUIAlertBuilder == null) {
            vehiclePowerOffUIAlertBuilder = new ComfirmUIAlert.Builder(context);
            vehiclePowerOffUIAlertBuilder.setTitle(context.getString(R.string.str_vehicle_power_off));
            vehiclePowerOffUIAlertBuilder.setContent(context.getString(R.string.str_vehicle_power_off_hint));
            vehiclePowerOffUIAlertBuilder.setFunction("vehicle_power_off");
            vehiclePowerOffUIAlertBuilder.setGlobalAlert(true);
            ComfirmUIAlert.setOnComfirmListener(function -> {
                if ("vehicle_power_off".equals(function)) {
                    quickManager.setVehiclePowerOff(1);
                }
            });
        }

        vehiclePowerOffUIAlertBuilder.create(1128, 508).show();
    }

    // 屏幕清洁
    public static void showCleanUIAlert(Context context) {
        CleanUIAlert alertOverlay = CleanUIAlert.getInstance(context);
        alertOverlay.show();
    }

    public static void notifyChange(String uri) {
        Uri sliceUri = Uri.parse(ProviderURI.PREFIX + uri);
        MyApplication.getContext().getContentResolver().notifyChange(sliceUri, null);
    }

    /**
     * 显示儿童锁弹窗
     */
    private void showChildLockDialog(Context context) {
        if (childLockUIAlertBuilder != null && childLockUIAlertBuilder.isShowing()) {
            return;
        }

        if (childLockUIAlertBuilder == null) {
            childLockUIAlertBuilder = new ChildLockUIAlert.Builder(context);
            childLockUIAlertBuilder.setGlobalAlert(true);
        }
        Log.d(TAG, "显示儿童锁弹窗");
        childLockUIAlertBuilder.create().show();

    }

    /**
     * 显示洗车模式弹窗
     */
    private void showWashModeDialog(Context context) {
        if (washCarModeUIAlertBuilder != null && washCarModeUIAlertBuilder.isShowing()) {
            return;
        }

        if (washCarModeUIAlertBuilder == null) {
            washCarModeUIAlertBuilder = new WashCarModeUIAlert.Builder(context);
            washCarModeUIAlertBuilder.setGlobalAlert(true);
        }
        Log.d(TAG, "显示洗车模式弹窗");
        washCarModeUIAlertBuilder.create().show();

    }
}
