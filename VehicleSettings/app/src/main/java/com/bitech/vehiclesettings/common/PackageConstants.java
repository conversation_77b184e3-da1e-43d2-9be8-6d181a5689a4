package com.bitech.vehiclesettings.common;

public class PackageConstants {
    /**
     * 提供各应用的包名，Activity，Service，启动的广播等常量
     */
    public PackageConstants() {
    }

    public static class Launcher {
        public static final String PACKAGE_NAME = "com.chery.launcher";
        public static final String MAIN_ACTIVITY_NAME = "com.chery.launcher.view.activity.LauncherHomeActivity";
        public static final String ALL_APP_ACTIVITY_NAME = "com.chery.launcher.view.activity.AllAppsActivity";
        public static final String SERVICE_ACTION = "com.chery.launcher.model.service.LauncherService";
    }

    public static class SystemUi {
        public static final String SYSTEM_UI_PACKAGE_NAME = "com.android.systemui";
        public static final String RECENT_ACTIVITY_NAME = "com.android.systemui.recents.view.RecentImpActivity";
        public static final String SERVICE_NAME_KEYGUARD_VIEWER = "com.android.systemui.keyguard.KeyguardViewerService";
        public static final String SERVICE_NAME_KEYGUARD = "com.android.systemui.keyguard.KeyguardService";
        public static final String SERVICE_NAME_SYSTEMUI = "com.android.systemui.SystemUIService";
        public static final String SERVICE_ACTION = "com.android.systemui.bluetooth.BluetoothPolicyService";
        public static final String SYSTEM_UI_ICON_CLICK_ACTION = "com.android.systemui.action.ICON_CLICK";
        /**
         * 关屏状态广播
         * Broadcast Action: The state of backlight has been changed.
         * When receiving this broadcast, app needs to {CarPowerClient.getBackLightStatus()} and
         * refresh ui
         *
         * @deprecated 此广播已废弃，想要监听“关屏”状态变化，请直接监听IPowerListener的onStateChanged，
         * 其中state = PowerStateListener.PWR_SCREEN_ON 为开屏；
         * PowerStateListener.PWR_SCREEN_OFF 为关屏
         */
        @Deprecated
        public static final String ACTION_BACKLIGHT_STATE_CHANGED = "com.android.systemui.power" +
                ".backlight";

        //关闭Recent页面
        public static final String ACTION_CLOSE_RECENT_ACT = "com.android.systemui.recent.CLOSE_RECENT_ACT";
        //移除Recent Task发送
        public static final String ACTION_RECENT_KILL = "com.android.systemui.recent.REQUEST_STOP_APP";
        //打开QuickPanel
        public static final String ACTION_OPEN_QUICK_PANEL = "com.android.systemui.qs.OPEN_QUICK_PANEL";
        public static final String KEY_OPEN_QUICK_PANEL_DOWN_X = "open_qs_down_x";
    }

    public static class Media {
        public static final String PACKAGE_NAME = "com.chery.media";
        public static final String MEDIA_ACTIVITY_NAME = "com.chery.media.view.activity.MediaActivity";
        public static final String VIDEO_ACTIVITY_NAME = "com.chery.media.view.activity.VideoActivity";
        public static final String WALLPAPER_ACTIVITY_NAME = "com.chery.media.view.activity.WallpaperAddActivity";
        public static final String WALLPAPER_PREVIEW_ACTIVITY_NAME = "com.chery.media.view.activity.WallpaperPreviewActivity";
        public static final String VIDEO_PLAYER_ACTIVITY_NAME = "com.chery.media.view.activity.VideoPlayerActivity";
        public static final String PICTURE_ACTIVITY_NAME = "com.chery.media.view.activity.PictureActivity";
        public static final String SERVICE_ACTION = "com.chery.media.model.service.MediaService";

        public static final String LOCAL_MUSICS_MAX_SIZE = "com.chery.media.localmusic.max_size";
        public static final String LOCAL_MUSICS_SIZE = "com.chery.media.localmusic.size";

        public static final String LOCAL_PICTURES_MAX_SIZE = "com.chery.media.localpicture.max_size";
        public static final String LOCAL_PICTURES_SIZE = "com.chery.media.localpicture.size";


        public static final String USB1_MUSIC_BROWSER_SERVICE_ACTION = "com.chery.media.model.service.Usb1MusicBrowserService";
        public static final String USB2_MUSIC_BROWSER_SERVICE_ACTION = "com.chery.media.model.service.Usb2MusicBrowserService";
        public static final String LOCAL_MUSIC_BROWSER_SERVICE_ACTION = "com.chery.media.model.service.LocalMusicBrowserService";
        public static final String USB_VIDEO_BROWSER_SERVICE_ACTION = "com.chery.media.model.service.UsbVideoBrowserService";

        public static final String RADIO_BROWSER_SERVICE_ACTION = "com.chery.media.model.service.RadioBrowserService";
        public static final String BAND_MEDIA_SESSION_FM = "fm";
        public static final String BAND_MEDIA_SESSION_AM = "am";

        public static final String DAB_BROWSER_SERVICE_ACTION = "com.chery.media.model.service.DabBrowserService";
        //自定义按键
        public static final String ACTION_CUSTOM = "com.chery.media.ACTION_HARDKEY_EVENT_REPORT";
    }

    public static class Hvac {
        public static final String PACKAGE_NAME = "com.chery.hvac";
        public static final String MAIN_ACTIVITY_NAME = "com.chery.hvac.view.activity.MainActivity";
        public static final String SERVICE_ACTION = "com.chery.hvac.model.service.HavcService";
        //AIDL  服务
        public static final String SERVICE_AIDL = "com.chery.hvac.view.viewpage.aidl.ACCAidlService";
    }

    public static class Settings {
        // settings 包名
        public static final String PACKAGE_NAME = "com.chery.settings";

        // settings MainActivity路径
        public static final String MAIN_CLASS_NAME = "com.chery.settings.ui.activity.SettingsActivity";

        // 进入Settings其他页面action key;启动方式：启动SettingsActivity时，使用intent.putExtra(PAGE_ACTION_KEY，PAGE_XXX_KEY_VALUE)传递Int类型页面数据
        public static final String PAGE_ACTION_KEY = "com.chery.settings.page.key";

        // PAGE_ACTION_KEY对应的“快捷控制“页面值
        public static final int PAGE_SHORTCUT_CONTROL_KEY_VALUE = 0;

        // PAGE_ACTION_KEY对应的”整车设置“页面值
        public static final int PAGE_VEHICLE_SETUP_KEY_VALUE = 1;

        // PAGE_ACTION_KEY对应的”辅助驾驶“页面值
        public static final int PAGE_ASSISTANCE_DRIVER_KEY_VALUE = 2;

        // PAGE_ACTION_KEY对应的“氛围灯”页面值
        public static final int PAGE_AMBIENT_LIGHT_KEY_VALUE = 3;

        // PAGE_ACTION_KEY对应的“车辆舒适”页面值
        public static final int PAGE_VEHICLE_COMFORT_KEY_VALUE = 4;

        // PAGE_ACTION_KEY对应的“蓝牙”页面值
        public static final int PAGE_BLUETOOTH_KEY_VALUE = 5;
        // 打开蓝牙和蓝牙可被发现开关 key
        public static final String PAGE_BLUETOOTH_AND_FOUND_OPEN_KEY = "bt_and_found_open";

        // PAGE_ACTION_KEY对应的“WIFI”页面值
        public static final int PAGE_WIFI_KEY_VALUE = 6;

        // PAGE_ACTION_KEY对应的“声音”页面值
        public static final int PAGE_SOUND_KEY_VALUE = 7;

        // PAGE_ACTION_KEY对应的“语音”页面值
        public static final int PAGE_VOICE_KEY_VALUE = 8;

        // PAGE_ACTION_KEY对应的“显示”页面值
        public static final int PAGE_DISPLAY_KEY_VALUE = 9;

        // PAGE_ACTION_KEY对应的“系统”页面值
        public static final int PAGE_SYSTEM_KEY_VALUE = 10;

        // PAGE_ACTION_KEY对应的“新能源”页面值
        public static final int PAGE_NEW_ENERGY_KEY_VALUE = 11;


        // PAGE_ACTION_KEY对应的“设备管理”页面值
        public static final int PAGE_DEVICE_MANAGE_KEY_VALUE = 11;
        public static final String VEHICLE_ACTIVITY_NAME = "com.chery.settings.view.vehicle.activity.VehicleActivity";
        public static final String SYSTEM_ACTIVITY_NAME = "com.chery.settings.ui.activity.SettingsActivity";
        public static final String WIFI_ACTION = "com.chery.settings.model.service.SettingService";
        // EOL声音设置服务
        public static final String EOL_SOUND_SERVICE
                = "com.chery.settings.service.SettingsSoundService";
    }

    public static class EngineeringMode {
        public static final String PACKAGE_NAME = "com.chery.engineeringmode";
        public static final String MAIN_CLASS_NAME = "com.chery.engineeringmode.MainActivity";
    }

    public static class Eol {
        public static final String PACKAGE_NAME = "com.chery.t19c_eol";
    }

    public static class CPAA {
        public static final String CONNECTION_LAUNCH_ACTION = "vehicle.system.broadcast";
        public static final String CONNECTION_LAUNCH_KEY = "launch";
        public static final String CONNECTION_LAUNCH_MUSIC = "music";
        public static final String CONNECTION_LAUNCH_PHONE = "telephone";
        public static final String CONNECTION_LAUNCH_NAVIGATION = "navigation";
        public static final String CONNECTION_APP_KEY = "notification";
        public static final String CONNECTION_APP_CP = "launch_cp";
        public static final String CONNECTION_APP_AA = "launch_aa";
    }

    public static class Dialer {
        public static final String PACKAGE_NAME = "com.chery.dialer";
        public static final String DIALER_ACTIVITY_NAME = "com.chery.dialer.DialerActivity";
    }

    public static class Camera {
        public static final String PACKAGE_NAME = "com.chery.avmapp";
        public static final String CAMERA_ACTIVITY_NAME = "com.chery.avmapp.MainActivity";
        /**
         * 360相关广播
         */
        public static final String USER_ACTION = "com.chery.avmapp.USER_ACTION";
        /**
         * isAvmOpening：true为前台显示，false为后台
         */
        public static final String EXTRA = "isAvmOpening";
        public static final boolean EXTRA_DEFAULT = false;
    }

    public static class CarPlay {
        public static final String PACKAGE_NAME = "com.chery.carplay";
        /**
         * @deprecated
         */
        @Deprecated
        public static final String CARPLAY_ACTIVITY_NAME = "com.chery.car.carplay.CarPlayActivity";
        public static final String CARPLAY_SERVICE_START = "com.chery.car.carplay.service.CarPlayService";
        public static final String CARPLAY_SERVICE_ACTION = "com.chery.carplay.action.START";
        public static final String PACKAGE_NAME_SOURCE = "com.chery.t19c.projection.carplay.service";
    }

    public static class AndroidAuto {
        public static final String PACKAGE_NAME = "com.chery.aauto";
        public static final String ANDROID_AUTO_ACTIVITY_NAME = "com.chery.car.androidauto.AndroidAutoActivity";
        public static final String PACKAGE_NAME_SOURCE = "vendor.cheryt.projection.androidauto";
    }

    public static class CpAaAdapter {
        public static final String PACKAGE_NAME = "com.chery.adapter";
    }

    public static class TurboDog {
        public static final String PACKAGE_NAME = "com.astrob.turbodog";
        public static final String TURBODOG_ACTIVITY_NAME = "com.astrob.turbodog.WelcomeActivity";
    }

    public static class PetalMap {
        public static final String PACKAGE_NAME = "com.huawei.maps.auto.app";
    }

    public static class ICM {
        public static final String PACKAGE_NAME = "com.chery.icm";
        public static final String SERVICE_ACTION = "com.chery.icm.services.IcmLowService";
        /**
         * btphone 发送电话信息绑定的Action
         */
        public static final String SERVICE_CALL_ACTION = "com.chery.icm.services.InCallServiceImpl";
    }

    public static class BT {
        public static final String PACKAGE_NAME = "com.android.bluetooth";
        public static final String SERVICE_ACTION = "com.android.bluetooth.avrcpcontroller.BluetoothMediaBrowserService";
    }

    public static class Upgrade {
        public static final String PACKAGE_NAME = "com.chery.upgrade";
        public static final String MAIN_ACTIVITY_NAME = "com.chery.upgrade.view.MainActivity";
        public static final String SERVICE_ACTION = "com.chery.update.model.service.UsbUpdateService";
    }

    public static class QDLink {
        public static final String PACKAGE_NAME = "com.neusoft.ssp.ces.c4.car.assistant";
        public static final String MAIN_ACTIVITY_NAME = "com.neusoft.ssp.ces.c4.car.assistant.SplashActivity";
    }

    public static class Phone {
        public static final String PACKAGE_NAME = "com.android.server.telecom";
    }

    public static class Help {
        public static final String PACKAGE_NAME = "com.chery.help";
        public static final String MAIN_ACTIVITY_NAME = "com.chery.help.view.activity.MainActivity";
        public static final String PICTURE_ACTIVITY_NAME = "com.chery.help.view.activity.PictureActivity";
    }

    public static class Avm {
        public static final String PACKAGE_NAME = "com.chery.avm";
        public static final String AVM_ACTION = "com.chery.avm.AvmEngService";
    }

    public static class Cerence {
        public static final String PACKAGE_NAME = "com.chery.cerence";
        public static final String SERVICE_ACTION = "com.chery.cerence.VrService";
    }

    public static class Iflytek {
        public static final String PACKAGE_NAME = "com.chery.iflytekvr";
    }

    public static class IflytekOs {
        public static final String PACKAGE_NAME = "com.chery.iflytekosservice";
        public static final String SERVICE_ACTION = "com.chery.iflytekosservice.IflytekOsService";
    }

    public static class Adas {
        public static final String PACKAGE_NAME = "com.chery.adas";
        public static final String MAIN_ACTIVITY_NAME = "com.chery.adas.MainActivity";
        public static final String ADAS_WINDOW_TO_CARD_ACTION = "adas.action.move_to_card";
        public static final String ADAS_INTENT_KEY = "adas_type";
        public static final String ADAS_TYPE_WINDOW = "window";
        public static final String ADAS_TYPE_CARD = "card";
    }

    public static class CompassOfWorship {
        public static final String PACKAGE_NAME = "com.chery.auto.worship_compass";
        public static final String MAIN_ACTIVITY_NAME = "com.chery.auto.worship_compass.page.activity.MainActivity";
    }
}
