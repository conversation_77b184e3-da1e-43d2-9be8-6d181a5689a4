package com.bitech.vehiclesettings.service;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;

public class GlobalDataObserver extends ContentObserver {
    private static final String TAG = "GlobalDataObserver";
    private final Context mContext;
    
    // 自定义全局数据的前缀，避免与其他应用冲突
    private static final String GLOBAL_DATA_PREFIX = "custom_global_";

    public static final String KEY_SYSTEM_COLOR = "system_color";

    // 监听器接口，用于回调数据变化
    public interface OnGlobalDataChangeListener {
        void onGlobalDataChanged(String key, String value);
    }
    
    private OnGlobalDataChangeListener mListener;

    public GlobalDataObserver(Context context, Handler handler) {
        super(handler);
        this.mContext = context.getApplicationContext();
    }

    public void setOnGlobalDataChangeListener(OnGlobalDataChangeListener listener) {
        this.mListener = listener;
    }

    @Override
    public void onChange(boolean selfChange, Uri uri) {
        super.onChange(selfChange, uri);
        
        if (uri != null) {
            String key = uri.getLastPathSegment();
            if (key != null && key.startsWith(GLOBAL_DATA_PREFIX)) {
                String value = getGlobalData(key, null);
                Log.d(TAG, "Global data changed - Key: " + key + ", Value: " + value);
                
                if (mListener != null) {
                    mListener.onGlobalDataChanged(key, value);
                }
            }
        }
    }

    /**
     * 注册监听指定的全局数据键
     * @param key 要监听的数据键(不需要包含前缀)
     */
    public void registerObserver(String key) {
        String fullKey = GLOBAL_DATA_PREFIX + key;
        Uri uri = Settings.Global.getUriFor(fullKey);
        mContext.getContentResolver().registerContentObserver(
                uri, false, this);
        Log.d(TAG, "Registered observer for key: " + fullKey);
    }

    /**
     * 取消注册监听
     */
    public void unregisterObserver() {
        mContext.getContentResolver().unregisterContentObserver(this);
        Log.d(TAG, "Unregistered all observers");
    }

    /**
     * 设置全局数据
     * @param key 数据键(不需要包含前缀)
     * @param value 要设置的值
     * @return 是否设置成功
     */
    public boolean setGlobalData(String key, String value) {
        String fullKey = GLOBAL_DATA_PREFIX + key;
        try {
            boolean result = Settings.Global.putString(
                    mContext.getContentResolver(), fullKey, value);
            Log.d(TAG, "Set global data - Key: " + fullKey + ", Value: " + value + ", Result: " + result);
            return result;
        } catch (SecurityException e) {
            Log.e(TAG, "Failed to set global data: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取全局数据
     * @param key 数据键(可以是完整键或短键)
     * @return 对应的值，如果不存在则返回null
     */
    public String getGlobalData(String key, String defaultValue) {
        // 如果key已经包含前缀，直接使用；否则添加前缀
        String fullKey = key.startsWith(GLOBAL_DATA_PREFIX) ? key : GLOBAL_DATA_PREFIX + key;
        try {
            String value = Settings.Global.getString(
                    mContext.getContentResolver(), fullKey);
            Log.d(TAG, "Get global data - Key: " + fullKey + ", Value: " + value);
            if (value == null) {
                setGlobalData(key, defaultValue);
                return defaultValue;
            }
            return value;
        } catch (SecurityException e) {
            Log.e(TAG, "Failed to get global data: " + e.getMessage());
            return defaultValue;
        }
    }

    /**
     * 删除全局数据
     * @param key 数据键(不需要包含前缀)
     * @return 是否删除成功
     */
    public boolean deleteGlobalData(String key) {
        String fullKey = GLOBAL_DATA_PREFIX + key;
        try {
            // 通过设置为null来删除
            boolean result = Settings.Global.putString(
                    mContext.getContentResolver(), fullKey, null);
            Log.d(TAG, "Delete global data - Key: " + fullKey + ", Result: " + result);
            return result;
        } catch (SecurityException e) {
            Log.e(TAG, "Failed to delete global data: " + e.getMessage());
            return false;
        }
    }
}