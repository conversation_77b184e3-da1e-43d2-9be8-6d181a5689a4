package com.bitech.vehiclesettings.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

public class GsonUtils {
    // 默认配置的Gson实例
    private static final Gson GSON = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss") // 设置日期格式
            .disableHtmlEscaping() // 禁用HTML转义
            .serializeNulls() // 序列化null值
            .setPrettyPrinting() // 美化输出
            .create();

    // 禁止实例化
    private GsonUtils() {
    }

    /**
     * 获取Gson实例（可扩展自定义配置）
     */
    public static Gson getGson() {
        return GSON;
    }

    /**
     * 对象转JSON字符串
     */
    public static String toJson(Object obj) {
        return GSON.toJson(obj);
    }

    /**
     * JSON字符串转对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        return GSON.fromJson(json, clazz);
    }

    /**
     * JSON字符串转泛型对象
     */
    public static <T> T fromJson(String json, Type typeOfT) {
        return GSON.fromJson(json, typeOfT);
    }

    /**
     * JSON转List集合
     */
    public static <T> List<T> fromJsonList(String json, Class<T> clazz) {
        return GSON.fromJson(json, TypeToken.getParameterized(List.class, clazz).getType());
    }

    /**
     * JSON转Map集合
     */
    public static <K, V> Map<K, V> fromJsonMap(String json, Class<K> keyClass, Class<V> valueClass) {
        return GSON.fromJson(json, TypeToken.getParameterized(Map.class, keyClass, valueClass).getType());
    }


}
