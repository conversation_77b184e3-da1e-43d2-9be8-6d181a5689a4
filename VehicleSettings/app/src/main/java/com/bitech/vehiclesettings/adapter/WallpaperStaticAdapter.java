package com.bitech.vehiclesettings.adapter;

import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.WallpaperBean;
import com.bitech.vehiclesettings.presenter.display.WallpaperPresenter;
import com.bitech.vehiclesettings.utils.EToast;
import com.chery.ivi.vdb.client.listener.VDNotifyListener;
import com.chery.ivi.vdb.event.id.wallpaper.VDWallpaperInfo;

import java.util.ArrayList;

public class WallpaperStaticAdapter extends RecyclerView.Adapter<WallpaperStaticAdapter.VH> {

    Context context;
    private static final int MIN_ITEM_COUNT = 6; // 默认显示 6 个，最后一个是主题商店
    private boolean isExpanded = false; // 是否展开
    WallpaperPresenter presenter = new WallpaperPresenter();

    public boolean isEditMode = false;

    public static ArrayList<VDWallpaperInfo> staticSelectedIndex;

    ArrayList<WallpaperBean> wallpaperBeans = new ArrayList<>();


    public VDNotifyListener mVDNotifyListener = (vdEvent, i) -> {
        if (vdEvent == null) return;

        new Handler(Looper.getMainLooper()).post(() -> {
            forceRefresh();
        });
    };


    public WallpaperStaticAdapter(Context context) {
        this.context = context;

        staticSelectedIndex = WallpaperPresenter.getPRSETWallpapers();
        for (VDWallpaperInfo staticSelectedIndex : staticSelectedIndex) {
            WallpaperBean wallpaperBean = new WallpaperBean(Uri.parse(staticSelectedIndex.uri));
            wallpaperBeans.add(wallpaperBean);
        }
    }

    class VH extends RecyclerView.ViewHolder {
        public ImageView ivBackground;
        public TextView tvTag;
        public ImageView ivCheckbox;

        public VH(@NonNull View itemView) {
            super(itemView);
            ivBackground = itemView.findViewById(R.id.iv_background);
            tvTag = itemView.findViewById(R.id.tv_tag);
            ivCheckbox = itemView.findViewById(R.id.iv_checkbox);
        }
    }

    @NonNull
    @Override
    public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new VH(LayoutInflater.from(context).inflate(R.layout.item_wallpaper_rv_list, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VH holder, int position) {
        WallpaperBean wallpaperBean;

        // 最后一个显示 主题商店
        boolean isThemeStore = (!isExpanded() && position == MIN_ITEM_COUNT - 1)
                || (isExpanded() && position == wallpaperBeans.size());

        if (wallpaperBeans.isEmpty()) {
            wallpaperBean = new WallpaperBean(R.mipmap.display_wp_store, R.string.str_wrapper_theme_store);
        } else {
            wallpaperBean = isThemeStore
                    ? new WallpaperBean(R.mipmap.display_wp_store, R.string.str_wrapper_theme_store)
                    : wallpaperBeans.get(position);
        }


        if (wallpaperBean.getName() == R.string.str_wrapper_theme_store) {
            holder.ivCheckbox.setVisibility(View.GONE);
            holder.ivBackground.setImageResource(R.mipmap.display_wp_store);
        } else {
            holder.ivCheckbox.setVisibility(View.VISIBLE);
        }

        if (wallpaperBean.getName() == R.string.str_wrapper_null) {
            holder.tvTag.setVisibility(View.GONE);
        } else {
            holder.tvTag.setVisibility(View.VISIBLE);
        }

        if (wallpaperBean.getUri() != null) {
            holder.ivBackground.setImageURI(wallpaperBean.getUri());
        }

        if (wallpaperBean.getName() != R.string.str_wrapper_null) {// 没有左上角名字
            holder.tvTag.setText(wallpaperBean.getName());
        }
        if (wallpaperBeans.isEmpty()) {
            return;
        }
        if (!isThemeStore) {
            holder.ivCheckbox.setImageResource(staticSelectedIndex.get(position).selected ?
                    R.mipmap.ic_checkbox_true_wallpaper : R.mipmap.ic_checkbox_false_wallpaper);
        }
        holder.itemView.setOnClickListener(view -> {
            // TODO 壁纸详情
            EToast.showToast(context, "壁纸详情", 0, false);
        });

        holder.ivCheckbox.setOnClickListener(v -> {
            updateSelection(position);
        });
    }

    private void updateSelection(int newIndex) {
        VDWallpaperInfo wallpaperInfo = staticSelectedIndex.get(newIndex);

        if (wallpaperInfo.selected) {
            if (WallpaperPresenter.getPRSETWallpapersSelectedSize() + WallpaperPresenter.getGalleryWallpapersSelectedSize() == 1) {
                EToast.showToast(context, context.getText(R.string.str_wrapper_toast_set_failure2), Toast.LENGTH_SHORT, false);
                return;
            }
            WallpaperPresenter.hidePreWallpaper(wallpaperInfo);
            wallpaperInfo.selected = false;
        } else {
            if (staticSelectedIndex.size() >= WallpaperPresenter.MAX_STATIC_SELECTION) {
                EToast.showToast(context, context.getText(R.string.str_wrapper_toast_set_failure), Toast.LENGTH_SHORT, false);
                return;
            }
            WallpaperPresenter.addWallpaper(wallpaperInfo);
            wallpaperInfo.selected = true;
        }
        notifyItemChanged(newIndex); // 局部刷新
    }


    @Override
    public int getItemCount() {
        if (wallpaperBeans.isEmpty()) {
            return 1;
        }
        return isExpanded ? wallpaperBeans.size() + 1 : Math.min(MIN_ITEM_COUNT, wallpaperBeans.size() + 1);
    }

    // 刷新数据源
    public void forceRefresh() {
        new Handler(Looper.getMainLooper()).post(() -> {
            ArrayList<VDWallpaperInfo> newData = WallpaperPresenter.getPRSETWallpapers();
            staticSelectedIndex = new ArrayList<>(newData);
            wallpaperBeans.clear();
            for (VDWallpaperInfo info : staticSelectedIndex) {
                wallpaperBeans.add(new WallpaperBean(Uri.parse(info.uri)));
            }
            notifyDataSetChanged();
        });
    }

    /**
     * 切换展开/收起状态
     */
    public void toggleExpand() {
        isExpanded = !isExpanded;
        forceRefresh();
    }

    /**
     * 是否处于展开状态
     */
    public boolean isExpanded() {
        return isExpanded;
    }

    public void editMode() {
        isEditMode = !isEditMode;
        forceRefresh();
    }
}

