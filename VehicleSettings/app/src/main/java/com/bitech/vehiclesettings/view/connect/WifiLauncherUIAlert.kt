package com.bitech.vehiclesettings.view.connect

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.ContextThemeWrapper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.manager.ConnectManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogAlertConnectWifiLauncherBinding
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import com.bitech.vehiclesettings.view.dialog.BaseDialog

class WifiLauncherUIAlert : BaseDialog {
    private var binding: DialogAlertConnectWifiLauncherBinding? = null
    private var isCancelable = true
    private var isGlobalAlert = false
    private var isActive = false
    private val mainHandler = Handler(Looper.getMainLooper())
    private var connectManager: ConnectManager
    private var positionX = 0
    private var positionY = 0


    @JvmOverloads
    constructor(context: Context, themeResId: Int = R.style.Dialog) : super(
        ContextThemeWrapper(
            context,
            Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue)
        ), themeResId
    ) {
        this.connectManager = BitechCar.getInstance()
            .getServiceManager(BitechCar.CAR_CONNECT_MANAGER) as ConnectManager
        initialize()
    }

    protected constructor(
        context: Context,
        cancelable: Boolean,
        cancelListener: DialogInterface.OnCancelListener?
    ) : super(context, cancelable, cancelListener) {
        this.connectManager = BitechCar.getInstance()
            .getServiceManager(BitechCar.CAR_CONNECT_MANAGER) as ConnectManager
        initialize()
    }

    private fun initialize() {
        binding = DialogAlertConnectWifiLauncherBinding.inflate(
            LayoutInflater.from(
                context
            )
        )
        setContentView(binding!!.root)
        setCancelable(isCancelable)

        val window = window
        if (window != null) {
            val layoutParams = window.attributes
            window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            layoutParams.dimAmount = 0f
            layoutParams.type = if (isGlobalAlert) WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
            else WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG
            layoutParams.width = 644
            layoutParams.gravity = Gravity.TOP or Gravity.START
            layoutParams.x = positionX
            layoutParams.y = positionY
            window.attributes = layoutParams
        }

        isActive = true
        initData()
        initListeners()
    }

    override fun setCancelable(cancelable: Boolean) {
        this.isCancelable = cancelable
        super.setCancelable(cancelable)
    }

    fun setGlobalAlert(globalAlert: Boolean) {
        this.isGlobalAlert = globalAlert
        updateWindowType()
    }

    fun setPosition(x: Int, y: Int) {
        this.positionX = x
        this.positionY = y
        updateWindowPosition()
    }

    private fun updateWindowType() {
        val window = window
        if (window != null) {
            val layoutParams = window.attributes
            layoutParams.type = if (isGlobalAlert) WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
            else WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG
            window.attributes = layoutParams
        }
    }

    private fun updateWindowPosition() {
        val window = window
        if (window != null) {
            val layoutParams = window.attributes
            layoutParams.x = positionX
            layoutParams.y = positionY
            window.attributes = layoutParams
        }
    }

    private fun initData() {
        // 初始化数据
    }

    private fun initListeners() {
        // 初始化监听器
    }

    override fun cancel() {
        super.cancel()
    }

    public override fun onStop() {
        val dismissIntent = Intent("com.chery.systemui.action.WIRELESS_CHARGE_DIALOG_DISMISS")
        context.sendBroadcast(dismissIntent)
        super.onStop()
    }

    override fun dismiss() {
        super.dismiss()
    }

    companion object {
        private val TAG: String = WifiLauncherUIAlert::class.java.simpleName
    }
}