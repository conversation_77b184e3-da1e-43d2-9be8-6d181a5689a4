package com.bitech.vehiclesettings.presenter.light;

import com.bitech.platformlib.bean.ColorBean;
import com.bitech.platformlib.bean.LightInBean;

import java.util.List;

public interface LightInPresenterListener {
    LightInBean getlInBean(boolean force);

    void setAmbLightSw(int state, int pos);

    LightInBean getlInBean();

    LightInBean getLightInData();


    int getLightSw();

    void chgThemeMode(int state);

    int getThemeMode();

    void brightAdj(int brightPos, boolean pressed);

    void brightAdj(int brightPos);

    void setBrightnessAuto(int state);

    int getAutoLamp();

    void setLightEffect(int state);

    int getLightEffect();


    void setLightFrontOrRear(int state);


    void setAutomaticCeiling(int state);

    void syncAtmosphere(int pos, int syncOpen);

    int getAutomaticCeiling();


    void selectSingleColor(int selColorPos, int progress, int colorHex, int lightLevel);

    void selectMutiColor(int selColorPos, int progress, int colorFrontLin, int colorRearLin, int lightLevel);

    void pickColor(int red, int green, int blue, int positionX);

    int getPickColorPosX();

    ColorBean getPickColor(int frontSel, int pos);

    void savePickColor(int pos, ColorBean color0);

    void pickColor(String colorHex, int positionX);

    List<ColorBean> getColors();

    int getCurColorLin();

    // 语音功能调用接口 2.颜色调节
    int getCurBrightness();

    //
    void changeColorLin(int colorLin);

    void changeBrightness(int bri);

    int getCurColorLin(int pos);

    // 语音功能调用接口 2.颜色调节
    int getCurBrightness(int pos);

    //
    void changeColorLin(int pos, int colorLin);

    void changeBrightness(int pos, int bri);
}
