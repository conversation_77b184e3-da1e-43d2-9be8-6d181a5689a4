package com.bitech.vehiclesettings.view.quickcontrol;

import android.car.Car;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.driving.IDrivingManagerListener;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.databinding.DialogAlertWashSetBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class WashCarModeUIAlert extends BaseDialog {
    private static final String TAG = WashCarModeUIAlert.class.getSimpleName();

    private View overlayView;

    public WashCarModeUIAlert(@NonNull Context context) {
        super(context);
    }

    public WashCarModeUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected WashCarModeUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    private Builder builderRef;

    /**
     * 外部直接设置洗车模式
     */
    public static void setCarWashMode(int mode) {
        if (currentBuilder != null && isShow) {
            currentBuilder.mainHandler.post(() -> { // 确保在主线程执行
                currentBuilder.carWashMode = mode;
                currentBuilder.updateConfirmUI();
                currentBuilder.updateCarWashMode();
            });
        }
    }

    private static Builder currentBuilder;

    public void setBuilderRef(Builder builder) {
        this.builderRef = builder;
    }

    public static class Builder implements SafeHandlerCallback {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertWashSetBinding binding;
        private SafeHandler handler;
        private volatile boolean isActive;
        RegularWashUIAlert.Builder regularWashUIAlertBuilder;
        WashCarModeDescribeUIAlert.Builder washCarModeDescribeUIAlert;
        boolean globalAlert = false;
        private final Handler mainHandler = new Handler(Looper.getMainLooper());


        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        public WashCarModeUIAlert dialog = null;

        // 档位
        private int gearPosition = 0;
        // 车速
        private int vehicleSpeed = 0;
        // 电源模式
        private int powerMode = 0;
        // 洗车模式
        public int carWashMode = CarDriving.Prefs_WASH_MODE.REGULAR_WASH;
        // 洗车模式失败原因
        public int carWashModeFailReason = -1;

        private final DrivingManager drivingManager = (DrivingManager) BitechCar.getInstance()
                .getServiceManager(BitechCar.CAR_DRIVING_MANAGER);

        public Builder(Context context) {
            this.context = context;
        }

        public void setGlobalAlert(boolean b) {
            globalAlert = b;
        }

        public WashCarModeUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void addCallback() {
            drivingManager.addCallback(TAG, new IDrivingManagerListener() {
                /**
                 * 档位
                 */
                @Override
                public void onGearPositionChanged(int signalVal) {
                    Log.d(TAG, "onGearPositionChanged: " + signalVal);
                    if (signalVal == Integer.MIN_VALUE) return;
                    gearPosition = signalVal;
                    mainHandler.post(() -> updateCarWashMode());
                }

                /**
                 * 车速
                 */
                @Override
                public void getVehicleSpeed(int signalVal) {
                    Log.d(TAG, "getVehicleSpeed: " + signalVal);
                    if (signalVal == Integer.MIN_VALUE) return;
                    vehicleSpeed = signalVal;
                    mainHandler.post(() -> {
                        updateCarWashMode();
                    });
                }

                /**
                 * 电源模式
                 */
                @Override
                public void onPowerModeChanged(int signalVal) {
                    Log.d(TAG, "onPowerModeChanged: " + signalVal);
                    if (signalVal == Integer.MIN_VALUE) return;
                    powerMode = signalVal;
                    mainHandler.post(() -> {
                        updateCarWashMode();
                    });
                }

                /**
                 * 获取洗车模式信号
                 */
                @Override
                public void onCarWashModeStatusChanged(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    mainHandler.post(() -> {
                        if (signalVal == CarDriving.FLZCU_CleanModeStatus.ON) {
                            if (regularWashUIAlertBuilder == null) {
                                regularWashUIAlertBuilder = new RegularWashUIAlert.Builder(context);
                            }
                            regularWashUIAlertBuilder.setGlobalAlert(true);
                            regularWashUIAlertBuilder.create().show();
                            regularWashUIAlertBuilder.setTitle(carWashMode);
                        }
                    });
                }

                /**
                 * 洗车模式失败原因
                 * @param signalVal
                 */
                @Override
                public void onCarWashModeFailure(int signalVal) {
                    if (signalVal == Integer.MIN_VALUE) return;
                    carWashModeFailReason = signalVal;
                }
            });
            drivingManager.registerListener();
        }

        private final SharedPreferences.OnSharedPreferenceChangeListener washModeListener =
                (sharedPreferences, key) -> {
                    if (PrefsConst.WASH_MODE.equals(key)) {
                        // WASH_MODE 被修改了，重新读取并更新 UI
                        carWashMode = Prefs.get(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.REGULAR_WASH);
                        mainHandler.post(() -> {
                            updateConfirmUI();
                            updateCarWashMode();
                        });
                    }
                };


        /**
         * Create the custom dialog
         */
        public WashCarModeUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new WashCarModeUIAlert(context,
                        R.style.Dialog);
            binding = DialogAlertWashSetBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            isActive = true;
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            handler = new SafeHandler(this);
            layoutParams.width = 1176;
            layoutParams.height = 792;
            layoutParams.type = globalAlert ? WindowManager.LayoutParams.TYPE_SYSTEM_ALERT : WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            dialog.setBuilderRef(this);
            window.setAttributes(layoutParams);
            WashCarModeUIAlert.currentBuilder = this;
            initListeners();
            initData();
            initView();
            addCallback();
            Prefs.registerListener(washModeListener);
            return dialog;
        }

        private void initView() {
            updateConfirmUI();
        }

        private void initData() {
            carWashMode = Prefs.get(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.REGULAR_WASH);
            // 获取档位
            gearPosition = drivingManager.getGearPosition();
            // 获取车速
            vehicleSpeed = drivingManager.getCarSpeed();
            // 获取电源模式
            powerMode = drivingManager.getPowerMode();
            // 洗车模式失败原因
            carWashModeFailReason = drivingManager.getFLZCU_CleanModeFailCause();

            // 使用 Handler 确保在主线程执行
            updateCarWashMode(); // 直接更新UI
        }

        public void initListeners() {
            // 开始洗车
            binding.btnStartCarWash.setOnClickListener(v -> {
                Log.d("TAG", "setListeners: ");
                drivingManager.setICC_CleanMode(CarDriving.ICC_CleanMode.ON);
                handler.sendMessageDelayed(MessageConst.CLEAN_MODE);
            });

            // 取消页面
            binding.btnCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });

            // 常规洗车
            binding.llRegularCarWash.setOnClickListener(v -> {
                Prefs.put(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.REGULAR_WASH);
                carWashMode = CarDriving.Prefs_WASH_MODE.REGULAR_WASH;
                updateConfirmUI();
                updateCarWashMode();
            });

            // 传送带洗车
            binding.llConveyorCarWash.setOnClickListener(v -> {
                carWashMode = CarDriving.Prefs_WASH_MODE.CONVEYOR_WASH;
                Prefs.put(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.CONVEYOR_WASH);
                updateConfirmUI();
                updateCarWashMode();
            });

            // 确认蒙层
            binding.btnConfirmView.setOnClickListener(v -> {
                if (carWashMode == 0) {
                    EToast.showToast(context, context.getString(R.string.wash_mode_regular_toast), 2000, false);
                } else if (carWashMode == 1) {
                    EToast.showToast(context, context.getString(R.string.wash_mode_conveyor_toast), 2000, false);
                }
            });
            // 信息弹窗
            binding.ivInfo.setOnClickListener(v -> {
                washCarModeDescribeUIAlert = new WashCarModeDescribeUIAlert.Builder(context);
                washCarModeDescribeUIAlert.setGlobalAlert(globalAlert);
                WashCarModeDescribeUIAlert dialog = washCarModeDescribeUIAlert.create();
                if (dialog != null && !dialog.isShowing()) {
                    dialog.show();
                }
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public void dismiss() {
            if (dialog != null && dialog.isShowing()) {
                WashCarModeUIAlert.currentBuilder = null;
                dialog.dismiss();
                destroy(); // 确保资源释放
            }
        }

        // 更新UI
        public void updateCarWashMode() {
            if ((powerMode == 0x1 || powerMode == 0x2) && vehicleSpeed == 0 && gearPosition == 0x1 && carWashMode == 0) {
                // 洗车模式可用
                GrayEffectUtils.removeGrayEffect(binding.btnStartCarWash);
                binding.btnConfirmView.setVisibility(View.GONE);
            } else if ((powerMode == 0x1 || powerMode == 0x2) && vehicleSpeed == 0 && gearPosition == 0x3 && carWashMode == 1) {
                // 洗车模式可用
                GrayEffectUtils.removeGrayEffect(binding.btnStartCarWash);
                binding.btnConfirmView.setVisibility(View.GONE);
            } else {
                GrayEffectUtils.applyGrayEffect(binding.btnStartCarWash, 0.39f);
                binding.btnConfirmView.setVisibility(View.VISIBLE);
            }
        }

        public void updateConfirmUI() {
            if (carWashMode == 0) {
                binding.ivCheckRegular.setVisibility(View.VISIBLE);
                binding.ivCheckConveyor.setVisibility(View.GONE);
            } else if (carWashMode == 1) {
                binding.ivCheckRegular.setVisibility(View.GONE);
                binding.ivCheckConveyor.setVisibility(View.VISIBLE);
            }
        }

        @Override
        public void handleSafeMessage(Message msg) {
            switch (msg.what) {
                case MessageConst.CLEAN_MODE:
                    // 洗车模式
                    Integer signalVal = drivingManager.getFLZCU_CleanModeStatus();
                    if (signalVal == CarDriving.FLZCU_CleanModeStatus.ON) {
                        if (regularWashUIAlertBuilder == null) {
                            regularWashUIAlertBuilder = new RegularWashUIAlert.Builder(context);
                        }
                        regularWashUIAlertBuilder.setGlobalAlert(true);
                        RegularWashUIAlert dialog = regularWashUIAlertBuilder.create();
                        if (dialog != null && !dialog.isShowing()) {
                            dialog.show();
                        }
                        regularWashUIAlertBuilder.setTitle(carWashMode);
                    } else {
                        if (carWashModeFailReason == CarDriving.FLZCU_CleanModeFailCause.FAIL) {
                            EToast.showToast(context, context.getString(R.string.wash_mode_on_fail_toast), 2000, false);
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        @Override
        public boolean isActive() {
            return isActive;
        }

        // 在Builder类中添加
        public void destroy() {
            Prefs.unregisterListener(washModeListener);
            isActive = false;
        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        if (builderRef != null) {
            builderRef.destroy(); // 销毁 builder 内部资源
        }
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (!hasFocus) {
            dismiss();
        }
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;
}