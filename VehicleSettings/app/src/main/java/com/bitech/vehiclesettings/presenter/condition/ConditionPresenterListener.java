package com.bitech.vehiclesettings.presenter.condition;

public interface ConditionPresenterListener {

    void setWiperRepairMode(int status);
    int getWiperRepairMode();

    // 复位保养里程
    void setMaintainReset();
    void setMaintainRemind(int state);
    int getMaintainRemind();
    int getMaintainKm();
    void setMaintainKm(int km);
    int getMaintainDays();
    void setMaintainDays(int day);
    int getWiperRepairStatus();
    // 获取电源
    int getPowerMode();
    // 遮阳帘是否处于静止状态
    int getSunShadeCurtainStopped();
}
