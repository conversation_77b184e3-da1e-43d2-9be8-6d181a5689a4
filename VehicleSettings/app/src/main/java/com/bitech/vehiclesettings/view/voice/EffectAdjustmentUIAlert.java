package com.bitech.vehiclesettings.view.voice;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundEffectAdjustmentBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.carsetting.ChildLockUIAlert;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.viewmodel.VoiceViewModel;

import java.util.logging.Handler;

public class EffectAdjustmentUIAlert extends BaseDialog {
    private static final String TAG = EffectAdjustmentUIAlert.class.getSimpleName();

    private static EffectAdjustmentUIAlert.ChangedListener changedListener;

    public EffectAdjustmentUIAlert(Context context) {
        super(context);
    }

    public EffectAdjustmentUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected EffectAdjustmentUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static EffectAdjustmentUIAlert.ChangedListener getOnChangedListener() {
        return changedListener;
    }

    public static void setOnChangedListener(EffectAdjustmentUIAlert.ChangedListener onProgressChangedListener) {
        EffectAdjustmentUIAlert.changedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private DialogAlertSoundEffectAdjustmentBinding binding;
        private VoicePresenter voicePresenter;
        private VoiceViewModel viewModel;
        private SafeHandler voiceHandler;
        private boolean init = true;

        final int gridCount = 17; // -7 ~ +7
        final int halfGrid = gridCount / 2; // 7
        final float[] gridSizeX = new float[1];
        final float[] gridSizeY = new float[1];


        public EqualizerUIAlert.Builder equalizerUIAlert;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private EffectAdjustmentUIAlert dialog = null;
        private TextView tvEffect;

        public Builder(Context context, VoicePresenter presenter, VoiceViewModel viewModel, SafeHandler voiceHandler) {
            this.context = context;
            this.voicePresenter = presenter;
            this.viewModel = viewModel;
            this.tvEffect = ((Activity) context).findViewById(R.id.tvEffect);
            this.voiceHandler = voiceHandler;
        }


        public EffectAdjustmentUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void updateEQUI(int status) {


            ((Activity) context).runOnUiThread(() -> {
                switch (status) {
                    case CarVoice.EQ.ALL:
                        binding.rbAllCar.setChecked(true);
                        binding.rbDriver.setChecked(false);
                        binding.rbFront.setChecked(false);
                        binding.rbRear.setChecked(false);
                        binding.rbCustom.setChecked(false);
                        binding.ivAudio.setVisibility(View.VISIBLE);
                        binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qcjh);
                        binding.ivCustom.setVisibility(View.GONE);
                        binding.btnBFReset.setAlpha(0.3f);
                        setSurroundClickable(true);
                        setVirtualClickable(voicePresenter.getSurroundSound() != CarVoice.SurroundSound.ON);
                        Prefs.put(PrefsConst.VOICE_EQ, status);
                        break;
                    case CarVoice.EQ.DRIVER:
                        binding.rbDriver.setChecked(true);
                        binding.rbAllCar.setChecked(false);
                        binding.rbFront.setChecked(false);
                        binding.rbRear.setChecked(false);
                        binding.rbCustom.setChecked(false);
                        binding.ivAudio.setVisibility(View.VISIBLE);
                        binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_zjjh);
                        binding.ivCustom.setVisibility(View.GONE);
                        binding.btnBFReset.setAlpha(0.3f);
                        setVirtualAndSurroundClickable(false);
                        Prefs.put(PrefsConst.VOICE_EQ, status);
                        break;
                    case CarVoice.EQ.SLEEP:
                        binding.rbFront.setChecked(true);
                        binding.rbAllCar.setChecked(false);
                        binding.rbDriver.setChecked(false);
                        binding.rbRear.setChecked(false);
                        binding.rbCustom.setChecked(false);
                        binding.ivAudio.setVisibility(View.VISIBLE);
                        binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qpjh);
                        binding.ivCustom.setVisibility(View.GONE);
                        binding.btnBFReset.setAlpha(0.3f);
                        setVirtualAndSurroundClickable(false);
                        Prefs.put(PrefsConst.VOICE_EQ, status);
                        break;
                    case CarVoice.EQ.VIP:
                        binding.rbRear.setChecked(true);
                        binding.rbAllCar.setChecked(false);
                        binding.rbDriver.setChecked(false);
                        binding.rbFront.setChecked(false);
                        binding.rbCustom.setChecked(false);
                        binding.ivAudio.setVisibility(View.VISIBLE);
                        binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_hpjh);
                        binding.ivCustom.setVisibility(View.GONE);
                        binding.btnBFReset.setAlpha(0.3f);
                        setVirtualAndSurroundClickable(false);
                        Prefs.put(PrefsConst.VOICE_EQ, status);
                        break;
                    case CarVoice.EQ.CUSTOM:
                        binding.rbCustom.setChecked(true);
                        binding.rbAllCar.setChecked(false);
                        binding.rbDriver.setChecked(false);
                        binding.rbFront.setChecked(false);
                        binding.rbRear.setChecked(false);
                        binding.ivAudio.setVisibility(View.GONE);
                        binding.ivCustom.setVisibility(View.VISIBLE);
                        binding.btnBFReset.setAlpha(1f);
                        setVirtualAndSurroundClickable(false);
                        Prefs.put(PrefsConst.VOICE_EQ, status);
                        break;
                    default:
                        binding.rbAllCar.setChecked(true);
                        binding.rbDriver.setChecked(false);
                        binding.rbFront.setChecked(false);
                        binding.rbRear.setChecked(false);
                        binding.rbCustom.setChecked(false);
                        binding.ivAudio.setVisibility(View.VISIBLE);
                        binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qcjh);
                        binding.ivCustom.setVisibility(View.GONE);
                        binding.btnBFReset.setAlpha(0.3f);
                        setSurroundClickable(true);
                        setVirtualClickable(false);
                        Prefs.put(PrefsConst.VOICE_EQ, CarVoice.EQ.ALL);
                        break;
                }
            });
            Prefs.put(PrefsConst.VOICE_EQ, status);
            checkEQAndHeadrest();
        }

        public void updateSurroundSound(int status) {
            Log.d(TAG, "updateSurroundSound status: " + status);
            ((Activity) context).runOnUiThread(() -> {
                binding.swSurroundSound.setChecked(status == CarVoice.SurroundSound.ON);
            });
            Prefs.put(PrefsConst.VOICE_SURROUND_SOUND, status);
            setVirtualClickable(status != CarVoice.SurroundSound.ON);
            checkEQAndHeadrest();
        }

        public void updateVirtualScene(int status) {
            ((Activity) context).runOnUiThread(() -> {
                binding.spvVirtual.setSelectedIndex(status, true);
            });
            Prefs.put(PrefsConst.VOICE_VIRTUAL_SCENE, status);
        }

        public void checkEQAndHeadrest() {
            int headRest = Prefs.get(PrefsConst.VOICE_HEADREST, CarVoice.HeadRest.DEFAULT);
            int eq = Prefs.get(PrefsConst.VOICE_EQ, CarVoice.EQ.DEFAULT);
            int surroundSound = Prefs.get(PrefsConst.VOICE_SURROUND_SOUND, CarVoice.SurroundSound.DEFAULT);
            if (headRest == CarVoice.HeadRest.PRIVATE) {
                setAllClickable(false);
            } else {
                if (eq != CarVoice.EQ.ALL) {
                    setVirtualAndSurroundClickableInit(false);
                } else {
                    setVirtualClickable(surroundSound == CarVoice.SurroundSound.OFF);
                }
            }
        }

        /**
         * Create the custom dialog
         */
        public EffectAdjustmentUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new EffectAdjustmentUIAlert(context, R.style.Dialog);
            binding = DialogAlertSoundEffectAdjustmentBinding.inflate(LayoutInflater.from(context));
            init = true;
            setupDragListener();
            onCLick();
            initData();
            initView();
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1912; // 或者使用具体的像素值
            window.setAttributes(layoutParams);
            init = false;
            changedListener.initObserve(binding);
            return dialog;
        }

        public void initData() {
            viewModel.setHeadRest(voicePresenter.getHeadRest());
            viewModel.setEq(voicePresenter.getEQ());
            viewModel.setSurroundSound(voicePresenter.getSurroundSound());
            viewModel.setPositionX(voicePresenter.getPosition()[0]);
            viewModel.setPositionY(voicePresenter.getPosition()[1]);
            viewModel.setVirtualScene(VoicePresenter.virtualSceneSignal2UI(voicePresenter.getVirtualScene()));
            // 当前是私享模式
            checkEQAndHeadrest();
        }

        public void initView() {

            binding.spvVirtual.setItems(R.string.str_sound_effect_item1, R.string.str_sound_effect_item2, R.string.str_sound_effect_item3, R.string.str_sound_effect_item4);
            binding.swSurroundSound.setChecked(Prefs.get(PrefsConst.VOICE_SURROUND_SOUND, CarVoice.SurroundSound.DEFAULT) == CarVoice.SurroundSound.ON);

            binding.hotArea.post(() -> {
                paintHotArea();

//                binding.tvCoordinates.setText(String.format(Locale.getDefault(), "坐标: (%d,%d)", viewModel.getPositionX().getValue(), viewModel.getPositionY().getValue()));
            });
            binding.sv.setOverScrollMode(View.OVER_SCROLL_NEVER);
        }

        @SuppressLint("ClickableViewAccessibility")
        private void setupDragListener() {
            binding.ivCustom.setOnTouchListener(new View.OnTouchListener() {
                private float touchOffsetX, touchOffsetY;

                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            // 手指在 ivCustom 内部的位置
                            touchOffsetX = event.getX();
                            touchOffsetY = event.getY();
                            break;

                        case MotionEvent.ACTION_MOVE:
                            float rawX = event.getRawX();
                            float rawY = event.getRawY();

                            // ivCustom 父布局的屏幕位置
                            int[] parentLoc = new int[2];
                            ((View) v.getParent()).getLocationOnScreen(parentLoc);
                            float parentX = parentLoc[0];
                            float parentY = parentLoc[1];

                            // hotArea 的屏幕位置
                            int[] hotAreaLoc = new int[2];
                            binding.hotArea.getLocationOnScreen(hotAreaLoc);
                            float hotLeft = hotAreaLoc[0];
                            float hotTop = hotAreaLoc[1];
                            float hotRight = hotLeft + binding.hotArea.getWidth();
                            float hotBottom = hotTop + binding.hotArea.getHeight();

                            // 限制不超出 hotArea
                            float clampedRawX = Math.max(hotLeft, Math.min(rawX - touchOffsetX, hotRight - v.getWidth()));
                            float clampedRawY = Math.max(hotTop, Math.min(rawY - touchOffsetY, hotBottom - v.getHeight()));
                            float finalX = clampedRawX - parentX;
                            float finalY = clampedRawY - parentY;

                            v.setX(finalX);
                            v.setY(finalY);

                            // 计算逻辑坐标（中心为 0,0）
                            float hotCenterX = hotLeft + binding.hotArea.getWidth() / 2f;
                            float hotCenterY = hotTop + binding.hotArea.getHeight() / 2f;

                            float centerXInParent = hotCenterX - parentX;
                            float centerYInParent = hotCenterY - parentY;

                            float offsetX = (finalX + v.getWidth() / 2f) - centerXInParent;
                            float offsetY = (finalY + v.getHeight() / 2f) - centerYInParent;

                            int logicX = Math.round(offsetX / gridSizeX[0]);
                            int logicY = Math.round(-offsetY / gridSizeY[0]);

                            logicX = Math.max(-halfGrid, Math.min(halfGrid, logicX));
                            logicY = Math.max(-halfGrid, Math.min(halfGrid, logicY));

//                            binding.tvCoordinates.setText("坐标: (" + logicX + ", " + logicY + ")");
                            voicePresenter.setPosition(logicX, logicY);
                            break;
                    }
                    return true;
                }
            });
        }

        @SuppressLint("ClickableViewAccessibility")
        public void onCLick() {
            binding.mg.setOnClickListener(v -> {
                if (viewModel.getHeadRest().getValue() == CarVoice.HeadRest.PRIVATE) {
                    EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                }
            });
            // 全车均衡
            binding.rbAllCar.setOnClickListener(v -> {
                int eq = viewModel.getEq().getValue();
                if (viewModel.getHeadRest().getValue() == CarVoice.HeadRest.PRIVATE) {
                    EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                    viewModel.setEq(eq);
                    return;
                }
                viewModel.setEq(CarVoice.EQ.ALL);
                voicePresenter.setEQ(CarVoice.EQ.ALL);
                voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
                binding.ivAudio.setVisibility(View.VISIBLE);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qcjh);
                binding.ivCustom.setVisibility(View.GONE);
                setSurroundClickable(true);
                setVirtualClickable(!CommonUtils.IntToBool(voicePresenter.getSurroundSound()));
            });
            // 主驾均衡
            binding.rbDriver.setOnClickListener(v -> {
                int eq = viewModel.getEq().getValue();
                if (viewModel.getHeadRest().getValue() == CarVoice.HeadRest.PRIVATE) {
                    EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                    viewModel.setEq(eq);
                    return;
                }
                viewModel.setEq(CarVoice.EQ.DRIVER);
                voicePresenter.setEQ(CarVoice.EQ.DRIVER);
                voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
                binding.ivAudio.setVisibility(View.VISIBLE);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_zjjh);
                binding.ivCustom.setVisibility(View.GONE);
                tvEffect.setText(R.string.str_sound_effect_item1);
                setVirtualAndSurroundClickable(false);
            });
            // 前排均衡
            binding.rbFront.setOnClickListener(v -> {
                int eq = viewModel.getEq().getValue();
                if (viewModel.getHeadRest().getValue() == CarVoice.HeadRest.PRIVATE) {
                    EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                    viewModel.setEq(eq);
                    return;
                }
                viewModel.setEq(CarVoice.EQ.SLEEP);
                voicePresenter.setEQ(CarVoice.EQ.SLEEP);
                binding.ivAudio.setVisibility(View.VISIBLE);
                voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_qpjh);
                binding.ivCustom.setVisibility(View.GONE);
                tvEffect.setText(R.string.str_sound_effect_item1);
                setVirtualAndSurroundClickable(false);
            });
            // 后排均衡
            binding.rbRear.setOnClickListener(v -> {
                int eq = viewModel.getEq().getValue();
                if (viewModel.getHeadRest().getValue() == CarVoice.HeadRest.PRIVATE) {
                    EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                    viewModel.setEq(eq);
                    return;
                }
                viewModel.setEq(CarVoice.EQ.VIP);
                voicePresenter.setEQ(CarVoice.EQ.VIP);
                binding.ivAudio.setVisibility(View.VISIBLE);
                voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
                binding.ivAudio.setImageResource(R.mipmap.ic_sound_effect_adjustment_hpjh);
                binding.ivCustom.setVisibility(View.GONE);
                tvEffect.setText(R.string.str_sound_effect_item1);
                setVirtualAndSurroundClickable(false);
            });
            // 自定义
            binding.rbCustom.setOnClickListener(v -> {
                int eq = viewModel.getEq().getValue();
                if (viewModel.getHeadRest().getValue() == CarVoice.HeadRest.PRIVATE) {
                    EToast.showToast(context, context.getText(R.string.str_headrest_sound_false), Toast.LENGTH_SHORT, false);
                    viewModel.setEq(eq);
                    return;
                }
                viewModel.setEq(CarVoice.EQ.CUSTOM);
                voicePresenter.setEQ(CarVoice.EQ.CUSTOM);
                binding.ivAudio.setVisibility(View.GONE);
                voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ);
                binding.ivCustom.setVisibility(View.VISIBLE);
                binding.rbCustom.post(this::paintHotArea);
                tvEffect.setText(R.string.str_sound_effect_item1);
                setVirtualAndSurroundClickable(false);
            });
            // 均衡调节
            binding.rlSoundEqualizerDesc.setOnClickListener(v -> {
                if (equalizerUIAlert == null) {
                    equalizerUIAlert = new EqualizerUIAlert.Builder(context, voicePresenter, viewModel, voiceHandler);
                }
                equalizerUIAlert.create().show();
            });

            binding.swSurroundSound.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (init) {
                    return;
                }
                if (isChecked) {
                    viewModel.setSurroundSound(CarVoice.SurroundSound.ON);
                    voicePresenter.setSurroundSound(CarVoice.SurroundSound.ON);
                    binding.spvVirtual.setSelectedIndex(0, true);
                    setVirtualClickable(false);

                } else {
                    viewModel.setSurroundSound(CarVoice.SurroundSound.OFF);
                    voicePresenter.setSurroundSound(CarVoice.SurroundSound.OFF);
                    setVirtualClickable(true);
                }
                voiceHandler.sendMessageDelayed(MessageConst.VOICE_SURROUND_SOUND);
            });

            binding.touchBlocker.setOnTouchListener((v, event) -> {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    if (voicePresenter.getEQ() != CarVoice.EQ.ALL) {
                        EToast.showToast(context, context.getText(R.string.str_headrest_eq_false), Toast.LENGTH_SHORT, false);
                    }
                    if (voicePresenter.getSurroundSound() == PrefsConst.TRUE
                            && voicePresenter.getEQ() == CarVoice.EQ.ALL) {
                        EToast.showToast(context, context.getText(R.string.str_headrest_surround_false), Toast.LENGTH_SHORT, false);
                    }
                }
                return false;
            });
            binding.spvVirtual.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    viewModel.setVirtualScene(index);
                    switch ( index){
                        case CarVoice.VirtualSceneUI.HIFI -> voicePresenter.setVirtualScene(CarVoice.VirtualScene.HIFI);
                        case CarVoice.VirtualSceneUI.HALL -> voicePresenter.setVirtualScene(CarVoice.VirtualScene.HALL);
                        case CarVoice.VirtualSceneUI.STADIUM -> voicePresenter.setVirtualScene(CarVoice.VirtualScene.STADIUM);
                        case CarVoice.VirtualSceneUI.VOCAL -> voicePresenter.setVirtualScene(CarVoice.VirtualScene.VOCAL);
                    }

                    voiceHandler.sendMessageDelayed(MessageConst.VOICE_VIRTUAL_SCENE);
                    tvEffect.setText(text);
                }

                @Override
                public void onItemClicked(int index, String text) {

                }
            });


            binding.btnBFReset.setOnClickListener(v -> {
                resetPosition();
                voiceHandler.sendMessageDelayed(MessageConst.VOICE_EQ_POSITION);
            });
        }

        public void dismiss() {
            if (dialog != null) {
                dialog.dismiss();
            }
        }

        private void setSurroundClickable(boolean b) {
            if (b) {
                binding.swSurroundSound.setAlpha(1f);
            } else {
                binding.swSurroundSound.setAlpha(0.5f);
            }
            ((Activity) context).runOnUiThread(() -> binding.swSurroundSound.setEnabled(b));
        }

        public void setVirtualClickable(boolean b) {
            if (b) {
                binding.spvVirtual.setAlpha(1f);
            } else {
                binding.spvVirtual.setAlpha(0.5f);
            }
            ((Activity) context).runOnUiThread(() -> binding.spvVirtual.setEnabled(b));
        }

        public void setVirtualAndSurroundClickable(boolean b) {
            binding.swSurroundSound.setEnabled(b);
            if (b) {
                binding.spvVirtual.setAlpha(1f);
                binding.swSurroundSound.setAlpha(1f);
            } else {
                binding.spvVirtual.setAlpha(0.5f);
                binding.swSurroundSound.setAlpha(0.5f);
                // 关闭环绕音
                binding.swSurroundSound.setChecked(false);
                //关闭虚拟声场
                binding.spvVirtual.setSelectedIndex(0, true);

            }
            ((Activity) context).runOnUiThread(() -> binding.spvVirtual.setEnabled(b));
        }

        public void setVirtualAndSurroundClickableInit(boolean b) {
            binding.swSurroundSound.setEnabled(b);
            if (b) {
                binding.spvVirtual.setAlpha(1f);
                binding.swSurroundSound.setAlpha(1f);
            } else {
                binding.spvVirtual.setAlpha(0.5f);
                binding.swSurroundSound.setAlpha(0.5f);
            }
            ((Activity) context).runOnUiThread(() -> binding.spvVirtual.setEnabled(b));
        }

        public void setAllClickable(boolean b) {
            setVirtualAndSurroundClickableInit(b);
            binding.mg.setAlpha(b ? 1f : 0.5f);
            for (int i = 0; i < binding.mg.getChildCount(); i++) {
                LinearLayout child = (LinearLayout) binding.mg.getChildAt(i);
                for (int j = 0; j < child.getChildCount(); j++) {
                    child.getChildAt(j).setClickable(b);
                    child.getChildAt(j).setFocusable(b);
                    child.getChildAt(j).setEnabled(b);
                }
            }
        }

        private void resetPosition() {

            // 计算 hotArea 的中心点（相对于 parent）
            int[] hotAreaLoc = new int[2];
            binding.hotArea.getLocationOnScreen(hotAreaLoc);

            int[] parentLoc = new int[2];
            ((View) binding.ivCustom.getParent()).getLocationOnScreen(parentLoc);

            float centerX = hotAreaLoc[0] + binding.hotArea.getWidth() / 2f;
            float centerY = hotAreaLoc[1] + binding.hotArea.getHeight() / 2f;

            float centerXInParent = centerX - parentLoc[0];
            float centerYInParent = centerY - parentLoc[1];

            // 将 ivCustom 移动到中心位置（减去自身宽高的一半）
            float pixelX = centerXInParent - binding.ivCustom.getWidth() / 2f;
            float pixelY = centerYInParent - binding.ivCustom.getHeight() / 2f;

            binding.ivCustom.setX(pixelX);
            binding.ivCustom.setY(pixelY);
//
//            // 更新坐标显示
//            binding.tvCoordinates.setText("坐标: (0, 0)");

            // 通知 presenter 重置为 0,0
            voicePresenter.setPosition(CarVoice.EQ.DEFAULT_POSITION_X, CarVoice.EQ.DEFAULT_POSITION_Y);
        }

        void paintHotArea() {
            gridSizeX[0] = binding.hotArea.getWidth() / (float) gridCount;
            gridSizeY[0] = binding.hotArea.getHeight() / (float) gridCount;

            float logicX = viewModel.getPositionX().getValue(); // -7 ~ +7
            float logicY = viewModel.getPositionY().getValue();

            float centerX = binding.hotArea.getX() + binding.hotArea.getWidth() / 2f;
            float centerY = binding.hotArea.getY() + binding.hotArea.getHeight() / 2f;

            float pixelX = centerX + logicX * gridSizeX[0] - binding.ivCustom.getWidth() / 2f;
            float pixelY = centerY - logicY * gridSizeY[0] - binding.ivCustom.getHeight() / 2f;

            binding.ivCustom.setX(pixelX);
            binding.ivCustom.setY(pixelY);
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface ChangedListener {
        void initObserve(DialogAlertSoundEffectAdjustmentBinding binding);
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;
}
