package com.bitech.vehiclesettings.service.system;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.bitech.platformlib.interfaces.display.IDisplayManagerListener;
import com.bitech.platformlib.manager.DisplayManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.utils.PrefsConst;

import java.util.concurrent.atomic.AtomicInteger;

public class SystemLifeCycle implements LifecycleEventObserver {
    private static final String TAG = SystemLifeCycle.class.getName() + "wzh2whu";

    SystemPresenter systemPresenter;

    @Override
    public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
        switch (event) {
            case ON_CREATE:
                Log.i(TAG, "onCreate: " + source);
                systemPresenter = SystemPresenter.getInstance();
                int autoCalibration = systemPresenter.getAutoCalibration();
                Log.d(TAG, "初始化设置---自动校准时间: " + autoCalibration);
                systemPresenter.setAutoCalibration(autoCalibration);
                break;
            case ON_DESTROY:
                Log.i(TAG, "onDestroy: " + source);
                break;
            default:
                break;
        }
    }
}
