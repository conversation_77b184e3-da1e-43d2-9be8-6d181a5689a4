package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSGestureNavigationBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class GestureNavigationUIAlert extends BaseDialog {

    public GestureNavigationUIAlert(Context context) {
        super(context);
    }

    public GestureNavigationUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected GestureNavigationUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder implements View.OnClickListener {

        private final Context context;
        private boolean isCan = true;
        public GestureNavigationUIAlert dialog = null;
        private DialogAlertSGestureNavigationBinding binding;
        private String s;
        private int selMode;

        public Builder(Context context) {
            this.context = context;
            this.s = "android.resource://" + context.getPackageName() + "/";
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public Builder setMode(int selMode) {
            this.selMode = selMode;
            return this;
        }

        public GestureNavigationUIAlert create() {
            if (dialog == null) {
                dialog = new GestureNavigationUIAlert(context, R.style.Dialog);
            }
            binding = DialogAlertSGestureNavigationBinding.inflate(LayoutInflater.from(context));

            // 根据 selMode 设置初始状态和视频
            switch (selMode) {
                case 0:
                    binding.llBack.setSelected(true);
                    binding.vv.setVideoURI(Uri.parse(s + R.raw.iv_system_gesture_navigation_1));
                    break;
                case 1:
                    binding.llControl.setSelected(true);
                    binding.vv.setVideoURI(Uri.parse(s + R.raw.iv_system_gesture_navigation_2));
                    break;
                case 2:
                    binding.llQuick.setSelected(true);
                    binding.vv.setVideoURI(Uri.parse(s + R.raw.iv_system_gesture_navigation_3));
                    break;
                case 3:
                    binding.llScroll.setSelected(true);
                    binding.vv.setVideoURI(Uri.parse(s + R.raw.iv_system_gesture_navigation_4));
                    break;
            }

            // 启动播放
            binding.vv.start();
            binding.vv.setOnPreparedListener(mp -> {
                mp.setLooping(true);
                binding.vv.start();
            });

            // 绑定点击事件
            BindingUtil.bindClicks(this, binding.llBack, binding.llControl, binding.llQuick, binding.llScroll);

            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());

            // 设置对话框大小
            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1992;
                layoutParams.height = 963;
                window.setAttributes(layoutParams);
            }

            // 添加销毁释放逻辑
            dialog.setOnDismissListener(d -> safeStopPlayback());

            return dialog;
        }

        @SuppressLint("NonConstantResourceId")
        @Override
        public void onClick(View view) {
            switch (view.getId()) {
                case R.id.llBack:
                    selMode = 0;
                    binding.scrollView.smoothScrollTo(0, binding.llBack.getTop());
                    setTexView(binding.llBack, binding.llControl, binding.llQuick, binding.llScroll);
                    switchVideo(R.raw.iv_system_gesture_navigation_1);
                    break;
                case R.id.llControl:
                    selMode = 1;
                    setTexView(binding.llControl, binding.llBack, binding.llQuick, binding.llScroll);
                    switchVideo(R.raw.iv_system_gesture_navigation_2);
                    break;
                case R.id.llQuick:
                    selMode = 2;
                    setTexView(binding.llQuick, binding.llBack, binding.llControl, binding.llScroll);
                    switchVideo(R.raw.iv_system_gesture_navigation_3);
                    break;
                case R.id.llScroll:
                    selMode = 3;
                    setTexView(binding.llScroll, binding.llBack, binding.llControl, binding.llQuick);
                    switchVideo(R.raw.iv_system_gesture_navigation_4);
                    break;
            }
        }

        private void setTexView(LinearLayout... layouts) {
            for (int i = 0; i < layouts.length; i++) {
                layouts[i].setSelected(i == 0);
            }
        }

        // 视频切换前安全释放旧资源
        private void switchVideo(int rawId) {
            safeStopPlayback();
            binding.vv.setVideoURI(Uri.parse(s + rawId));
            binding.vv.start();
        }

        // 防止系统崩溃的播放释放方法
        private void safeStopPlayback() {
            try {
                if (binding != null && binding.vv != null) {
                    binding.vv.stopPlayback();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;
}
