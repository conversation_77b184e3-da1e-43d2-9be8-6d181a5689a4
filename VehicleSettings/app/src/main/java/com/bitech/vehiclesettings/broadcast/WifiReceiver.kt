package com.bitech.vehiclesettings.broadcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.MacAddress
import android.net.NetworkInfo
import android.net.wifi.SupplicantState
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.bitech.vehiclesettings.manager.CarWifiManager
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: WifiReceiver
 *
 * @Date:  2024/2/4 17:49
 * @Description: WIFI相关广播接收器.
 **/
class WifiReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        LogUtil.d(TAG, "onReceive : action = $action")
        when (action) {
            WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                // 获取WIFI状态
                val wifiState =
                    intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
                LogUtil.d(TAG, "onReceive : wifi state = $wifiState")
                // WIFI状态监听回调
                for (listener in wifiStateListenerMap!!.values) {
                    listener.onWifiStateChange(wifiState)
                }
            }

            WifiManager.WIFI_AP_STATE_CHANGED_ACTION -> {
                // 获取WIFI热点状态
                val hotspotState = intent.getIntExtra(WifiManager.EXTRA_WIFI_AP_STATE, -1)
                LogUtil.d(TAG, "onReceive : zhc6whu wifi hotspot state = $hotspotState")
                // WIFI热点状态监听回调
                for (listener in wifiStateListenerMap!!.values) {
                    listener.onWifiHotspotStateChange(hotspotState)
                }
            }

            WifiManager.SCAN_RESULTS_AVAILABLE_ACTION -> {
                LogUtil.d(TAG, "onReceive : wifi scan list can use!")
                // WIFI扫描列表可用时监听
                for (listener in wifiStateListenerMap!!.values) {
                    listener.onWifiScanResult()
                }
            }

            WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                // WIFI连接过程中，网络状态改变回调时，获取当前网络状态信息
                val wifiInfo = CarWifiManager.instance.getConnectionInfo()
                val wifiNetWorkInfo: NetworkInfo? =
                    intent.getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO)
                if (wifiNetWorkInfo != null && wifiInfo != null) {
                    LogUtil.d(
                        TAG,
                        "onReceive : wifi connected name = ${wifiInfo.ssid} , bssid = ${wifiInfo.bssid}" +
                                " , state = ${wifiNetWorkInfo.state}"
                    )
                    // WIFI扫描列表可用时监听
                    for (listener in wifiStateListenerMap!!.values) {
                        listener.onNetworkStateChange(wifiInfo, wifiNetWorkInfo)
                    }
                }
            }

            WifiManager.SUPPLICANT_STATE_CHANGED_ACTION -> {
                // 获取错误码
                val error = intent.getIntExtra(WifiManager.EXTRA_SUPPLICANT_ERROR, -1)
                val supplicantState =
                    intent.getParcelableExtra<SupplicantState>(WifiManager.EXTRA_NEW_STATE)
                LogUtil.d(
                    TAG,
                    "onReceive : wifi error = $error , supplicantState = $supplicantState"
                )
                if (error == WifiManager.ERROR_AUTHENTICATING
                    && supplicantState == SupplicantState.DISCONNECTED
                ) {
                    LogUtil.d(TAG, "onReceive : wifi password error!")
                    // WIFI密码错误时监听
                    for (listener in wifiStateListenerMap!!.values) {
                        listener.onWifiPasswordError()
                    }
                }
            }

            Contacts.WIFI_AP_STA_JOIN_ACTION -> {
                // 获取接入WIFI热点的MAC地址
//                Log.d(TAG, "onReceive: zhc6whu:有设备加入热点")
//                val hotspotMac = intent.getStringExtra("macInfo")
//                LogUtil.d(TAG, "onReceive : zhc6whu wifi hotspot join device mac = $hotspotMac")
//                if (hotspotMac != null) {
//                    // 热点接入监听回调,因后续需要通过MAC获取接入设备的名称，需要延迟1~2s方能获取到
//                    handler.postDelayed({
//                        for (listener in wifiStateListenerMap!!.values) {
//                            listener.onWifiHotspotJoin(hotspotMac)
//                        }
//                    }, DELAY_TIME)
//                }
            }

            "android.net.wifi.WIFI_AP_STA_JOIN_WITH_NAME" -> {
                // 获取接入WIFI热点的MAC地址
                Log.d(TAG, "onReceive: :有设备加入热点+$intent")
                val macAddress = intent.getParcelableExtra<MacAddress>("macInfo")
                val macString = macAddress.toString() // 转换成字符串格式，如 "56:43:36:c2:18:54"
                val hotspotName = intent.getStringExtra("hostname")
                LogUtil.d(TAG, "onReceive :wifi hotspot join device Mac = $macString name = $hotspotName")
                handler.postDelayed({
                    for (listener in wifiStateListenerMap!!.values) {
                        if (hotspotName != null) {
                            listener.onWifiHotspotJoin(macString,hotspotName)
                        }
                    }
                }, DELAY_TIME)
            }

            Contacts.WIFI_AP_STA_LEAVE_ACTION -> {
                // 获取断开WIFI热点的MAC地址
                Log.d(TAG, "onReceive: zhc6whu:有设备离开热点")
                val hotspotMac = intent.getStringExtra("macInfo")
                LogUtil.d(TAG, "onReceive : zhc6whu wifi hotspot leave device mac = $hotspotMac")
                if (hotspotMac != null) {
                    // 热点断开监听回调,因后续需要通过MAC获取断开设备的名称，需要延迟1~2s方能获取到
                    handler.postDelayed({
                        for (listener in wifiStateListenerMap!!.values) {
                            listener.onWifiHotspotLeave(hotspotMac)
                        }
                    }, DELAY_TIME)
                }
            }
        }
    }

    /**
     * 注册WIFI状态监听.
     *
     * @param strClassName 类名
     * @param wifiStateListener wifi状态监听
     */
    fun registerWifiStateListener(strClassName: String, wifiStateListener: WifiStateListener) {
        LogUtil.d(
            TAG, "registerWifiStateListener : " +
                    "strClassName = $strClassName , " +
                    "wifiStateListener = $wifiStateListener"
        )
        // 添加监听
        wifiStateListenerMap?.set(strClassName, wifiStateListener)
    }

    /**
     * 注销WIFI状态监听.
     *
     * @param strClassName 类名
     */
    fun unregisterWifiStateListener(strClassName: String) {
        LogUtil.d(TAG, "unregisterWifiStateListener : strClassName = $strClassName")
        // 移除监听
        wifiStateListenerMap?.remove(strClassName)
    }

    /**
     * @ClassName: WifiStateListener
     *
     * @Date:  2024/2/4 17:52
     * @Description: WIFI状态监听接口.
     **/
    interface WifiStateListener {

        /**
         * 当WIFI开关状态发生改变时回调.
         *
         * @param state WIFI状态
         */
        fun onWifiStateChange(state: Int)

        /**
         * 当WIFI热点开关状态发生改变时回调.
         *
         * @param state WIFI热点状态
         */
        fun onWifiHotspotStateChange(state: Int)

        /**
         * 当有设备接入WIFI热点时.
         *
         * @param hotspotMac 接入设备的MAC地址
         */
        fun onWifiHotspotJoin(hotspotMac: String, hotspotName: String)

        /**
         * 当有设备断开WIFI热点时.
         *
         * @param hotspotMac 断开设备的MAC地址
         */
        fun onWifiHotspotLeave(hotspotMac: String)

        /**
         * WIFI扫描列表可用时回调.
         *
         */
        fun onWifiScanResult()

        /**
         * WIFI网络状态发生改变时.
         *
         * @param wifiInfo 当前连接的网络信息
         * @param networkInfo 网络连接状态信息
         */
        fun onNetworkStateChange(wifiInfo: WifiInfo, networkInfo: NetworkInfo)

        /**
         * WIFI密码错误时回调.
         *
         */
        fun onWifiPasswordError()
    }

    companion object {
        // 日志标志位
        private const val TAG = "WifiReceiver"

        // 延时时间
        private const val DELAY_TIME = 2000L

        // 监听列表
        private var wifiStateListenerMap: MutableMap<String, WifiStateListener>? = HashMap()

        // 用于热点设备接入或断开监听回调，需要延时1~2s
        private val handler = Handler(Looper.getMainLooper())
    }
}
