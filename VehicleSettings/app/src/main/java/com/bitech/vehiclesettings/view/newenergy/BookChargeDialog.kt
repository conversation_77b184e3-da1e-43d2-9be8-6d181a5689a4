package com.bitech.vehiclesettings.view.newenergy

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogBookChargeBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel.Companion.formatWithZero
import com.shawnlin.numberpicker.NumberPicker
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * @Description: 预约充电
 **/
class BookChargeDialog(context: Context, val duration: Int, val hour: Int, val minute: Int) :
    BaseDialog(context) {

    private lateinit var binding: DialogBookChargeBinding
    private var bookChargeCallback: OnBookChargeCallback? = null
    private var titleText: String = context.getString(R.string.app_name)

    private var gravity = Gravity.CENTER
    private var y = 0
    private var width = 1584
    private var height = 916

    private var prevAmPmValue = 0
    private val is24Hour = is24HourFormat(context)

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : duration = $duration, hour = $hour, minute = $minute")
        binding = DialogBookChargeBinding.bind(
            layoutInflater.inflate(R.layout.dialog_book_charge, null)
        )
        setContentView(binding.root)
        // 初始化视图
        initView()
        initData()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        // 设置对话框窗口属性
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG
        attributes?.width = width
        attributes?.height = height
        attributes?.gravity = gravity
        if (y != 0) {
            attributes?.y = y
        }
        window?.attributes = attributes

    }

    private fun initData() {
        var hour = this.hour
        val minute = this.minute
        //时长单位为分钟，UI显示为1-23小时
        val chargeDuration = (this.duration / 60).coerceAtLeast(1)
        // 预约升级的时间区间
        val startUpdateTime = LocalTime.of(20, 30)
        val endUpdateTime = LocalTime.of(22, 30)
        // 时间格式化为 HH:mm
        val formatter = DateTimeFormatter.ofPattern("HH:mm")

        binding.apply {
            tvTitle.text = titleText
            tvTips.text = context.getString(
                R.string.ne_book_charge_error,
                "${startUpdateTime.format(formatter)}-${endUpdateTime.format(formatter)}"
            )
            tvTips.visibility = View.GONE

            //取消/确定
            btnCancel.setOnClickListener { dismiss() }
            btnConfirm.setOnClickListener {
                dismiss()
            }
            //上午/下午
            npAmPm.visibility = if (is24Hour) View.GONE else View.VISIBLE
            npAmPm.value = if (!is24Hour) 1 else 0 // 初始AM/PM值
            prevAmPmValue = npAmPm.value // 初始化：记录初始AM/PM值

            npAmPm.minValue = 0
            npAmPm.maxValue = 1
            npAmPm.displayedValues = arrayOf(
                context.getString(R.string.ne_am),
                context.getString(R.string.ne_pm)
            )
            npAmPm.value = if (!is24Hour) 1 else 0
            npHour.minValue = 0
            npHour.maxValue = 23
            npHour.displayedValues = if (is24Hour) {
                // 24小时制 对应00
                (0..23).map { it.formatWithZero() }.toTypedArray()
            } else {
                //用24个显示数字的数组 AM 0-11  PM 12-23
                val amHours =
                    listOf("12") + (1..11).map { it.formatWithZero() } // AM: 12,01-11（索引0-11）
                val pmHours =
                    listOf("12") + (1..11).map { it.formatWithZero() } // PM: 12,01-11（索引12-23）
                (amHours + pmHours).toTypedArray()
            }

            npHour.value = hour

            //分钟
            npMinute.minValue = 0
            npMinute.maxValue = 59
            npMinute.displayedValues = (0..59).map { it.formatWithZero() }.toTypedArray()

            npMinute.value = minute
            //充电时长
            npDuration.minValue = 1
            npDuration.maxValue = 23
            npDuration.value = chargeDuration
            //确定按钮
//            binding.enableConfirm = !isCurrentTimeInRange(startUpdateTime, endUpdateTime)
            binding.enableConfirm = true

            npHour.setOnScrollListener { _, scrollState ->
                if (scrollState == NumberPicker.OnScrollListener.SCROLL_STATE_IDLE) {
                    val currentIndex = npHour.value
                    LogUtil.d(TAG, "npHour value = $currentIndex")
                    if (!is24Hour) {
                        npAmPm.value =
                            if (currentIndex < 12) 0 else 1 // 0=AM（0-11 display显示数组中AM下标范围） 1=PM（12-23 display显示数组中PM下标范围）
                    }
                }
            }
            npMinute.setOnScrollListener { _, scrollState ->
                if (scrollState == NumberPicker.OnScrollListener.SCROLL_STATE_IDLE) {
                    LogUtil.d(TAG, "npMinute value = ${npMinute.value}")
//                    binding.enableConfirm = !isCurrentTimeInRange(startUpdateTime, endUpdateTime)
                }
            }

            //监控AM PM切换 调整对应小时为AM/PM索引 防止切换后 滑动小时 am pm回位
            npAmPm.setOnScrollListener { _, scrollState ->
                if (scrollState == NumberPicker.OnScrollListener.SCROLL_STATE_IDLE && !is24Hour) {
                    val currentAmPm = npAmPm.value
                    if (currentAmPm != prevAmPmValue) { // AM/PM发生切换
                        npHour.value = when (currentAmPm) {
                            1 -> npHour.value + 12 // AM→PM：索引+12
                            0 -> npHour.value - 12 // PM→AM：索引-12
                            else -> npHour.value
                        }
                        prevAmPmValue = currentAmPm // 更新记录值
                    }
                }
            }

            btnConfirm.setOnClickListener {
                var npHourValue = binding.npHour.value
                val npMinuteValue = binding.npMinute.value
                val npAmPmValue = binding.npAmPm.value
                val npDurationValue = binding.npDuration.value

                LogUtil.d(
                    TAG, "btnConfirm click npHourValue = $npHourValue npMinuteValue = " +
                            "$npMinuteValue npAmPmValue = $npAmPmValue npDurationValue = $npDurationValue"
                )
                bookChargeCallback?.onConfirmBookCharge(npDurationValue, npHourValue, npMinuteValue)
                dismiss()
                //Toast提示
                SettingsToast.showToast(R.string.ne_reservation_success)
            }

            btnCancel.setOnClickListener {
                LogUtil.d(TAG, "btnCancel click")
                cancel()
                //Toast提示
                SettingsToast.showToast(R.string.ne_cancel_reservation)
            }
        }
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    fun setTitle(title: String) {
        titleText = title
    }

    fun setDialogGravity(gravity: Int) {
        this.gravity = gravity
    }

    fun setDialogWidthHeight(width: Int, height: Int, y: Int) {
        this.width = width
        this.height = height
        this.y = y
    }

    /**
     * 判断当前时间是否处于指定的时间区间内
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 如果当前时间在区间内返回 true，否则返回 false
     */
    fun isCurrentTimeInRange(startTime: LocalTime, endTime: LocalTime): Boolean {
        var npHourValue = binding.npHour.value
        val npMinuteValue = binding.npMinute.value
        if (!is24Hour) {
            val npAmPmValue = binding.npAmPm.value
            if (npHourValue == 12) {
                npHourValue = if (npAmPmValue == 1) 12 else 0
            } else {
                if (npAmPmValue == 1) {
                    npHourValue += 12
                }
            }
        }
        val selectedTime = LocalTime.of(npHourValue, npMinuteValue)
        val inRange = if (startTime.isBefore(endTime)) {
            selectedTime.isAfter(startTime) && selectedTime.isBefore(endTime)
        } else {
            // 处理跨天的情况
            selectedTime.isAfter(startTime) || selectedTime.isBefore(endTime)
        }
        LogUtil.d(TAG, "isCurrentTimeInRange : selectedTime = $selectedTime isInRange = $inRange")
        return inRange
    }

    fun setDialogClickCallback(callback: OnBookChargeCallback) {
        bookChargeCallback = callback
    }

    interface OnBookChargeCallback {
        fun onConfirmBookCharge(duration: Int, hour: Int, minute: Int)
    }


    companion object {
        // 日志标志位
        private const val TAG = "BookChargeDialog"

        fun is24HourFormat(context: Context?): Boolean {
            return android.text.format.DateFormat.is24HourFormat(context)
        }

    }
}
