package com.bitech.vehiclesettings.view.display;


import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.adapter.WallpaperCustomAdapter;
import com.bitech.vehiclesettings.adapter.WallpaperSceneAdapter;
import com.bitech.vehiclesettings.adapter.WallpaperStaticAdapter;
import com.bitech.vehiclesettings.databinding.DialogAlertDisplayWallpaperBinding;
import com.bitech.vehiclesettings.presenter.display.WallpaperPresenter;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;

import java.util.ArrayList;
import java.util.List;

public class WallpaperUIAlert extends Dialog {
    private static final String TAG = WallpaperUIAlert.class.getSimpleName();


    public WallpaperUIAlert(Context context, int theme) {
        super(context, theme);
    }

    public static class Builder {
        public interface OpenGalleryListener {
            void openGallery();
        }

        public void setOpenGalleryListener(OpenGalleryListener openGalleryListener) {
            this.openGalleryListener = openGalleryListener;
        }

        private final Context context;
        private boolean isCan = true;
        private DialogAlertDisplayWallpaperBinding binding;

        private WallpaperSceneAdapter wallpaperSceneAdapter;
        WallpaperCustomAdapter wallpaperCustomAdapter;
        WallpaperStaticAdapter wallpaperStaticAdapter;

        private OpenGalleryListener openGalleryListener;


        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        public WallpaperUIAlert dialog = null;
        private WallpaperDetailUIAlert.Builder wallpaperAlert;
        private WallpaperPresenter wallpaperPresenter;

        public Builder(Context context) {
            this.context = context;
            if (wallpaperPresenter == null) {
                wallpaperPresenter = new WallpaperPresenter();
            }
        }


        public WallpaperUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public WallpaperUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new WallpaperUIAlert(context, R.style.Dialog);
            binding = DialogAlertDisplayWallpaperBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1992; // 或者使用具体的像素值
            layoutParams.height = 1204;
            window.setAttributes(layoutParams);
            binding.spvWallpaper.setItems(R.string.str_wrapper_scene, R.string.str_wrapper_static);
            init();
            onViewClicked();
            return dialog;
        }

        private void setSwitchView(FrameLayout flRecommend1, FrameLayout flRecommend2, boolean isChecked) {
            if (isChecked) {
                flRecommend1.setVisibility(View.GONE);
                flRecommend2.setVisibility(View.VISIBLE);
                WallpaperPresenter.switch2D();
            } else {
                flRecommend1.setVisibility(View.VISIBLE);
                flRecommend2.setVisibility(View.GONE);
                WallpaperPresenter.switch3D();
            }
        }

        @SuppressLint("NotifyDataSetChanged")
        public void init() {
            // 判断壁纸模式
            int wallpaperState = WallpaperPresenter.getWallpaperState();
            binding.spvWallpaper.setSelectedIndex(wallpaperState == WallpaperPresenter.WALLPAPER_STATE_2D ? WallpaperPresenter.WALLPAPER_STATE_3D : WallpaperPresenter.WALLPAPER_STATE_2D, false);
            binding.fmStatic.setVisibility(wallpaperState == WallpaperPresenter.WALLPAPER_STATE_2D ? View.VISIBLE : View.GONE);
            binding.fmScene.setVisibility(wallpaperState == WallpaperPresenter.WALLPAPER_STATE_3D ? View.VISIBLE : View.GONE);


            GridLayoutManager gridLayoutManagerScene = new GridLayoutManager(this.context, 3);
            gridLayoutManagerScene.setOrientation(LinearLayoutManager.VERTICAL);
            binding.rvScene.setLayoutManager(gridLayoutManagerScene);
            this.wallpaperSceneAdapter = new WallpaperSceneAdapter(this.context);
            binding.rvScene.setAdapter(this.wallpaperSceneAdapter);
            binding.rvScene.setOverScrollMode(View.OVER_SCROLL_NEVER);

            GridLayoutManager gridLayoutManagerStatic = new GridLayoutManager(this.context, 3);
            gridLayoutManagerStatic.setOrientation(LinearLayoutManager.VERTICAL);
            binding.rvStatic.setLayoutManager(gridLayoutManagerStatic);
            wallpaperStaticAdapter = new WallpaperStaticAdapter(this.context);
            wallpaperPresenter.addWallpaperSubscribe(wallpaperStaticAdapter.mVDNotifyListener);
            binding.rvStatic.setOverScrollMode(View.OVER_SCROLL_NEVER);
            binding.rvStatic.setAdapter(wallpaperStaticAdapter);

            GridLayoutManager gridLayoutManagerCustom = new GridLayoutManager(this.context, 3);
            gridLayoutManagerCustom.setOrientation(LinearLayoutManager.VERTICAL);
            binding.rvCustom.setLayoutManager(gridLayoutManagerCustom);
            wallpaperCustomAdapter = new WallpaperCustomAdapter(this.context);
            wallpaperPresenter.addWallpaperSubscribe(wallpaperCustomAdapter.mVDNotifyListener);
            wallpaperCustomAdapter.setOnGo2GalleryClickListener(() -> {
                if (openGalleryListener != null) {
                    openGalleryListener.openGallery();
                    wallpaperCustomAdapter.notifyDataSetChanged();
                }
            });
            binding.rvCustom.setOverScrollMode(View.OVER_SCROLL_NEVER);
            binding.rvCustom.setAdapter(wallpaperCustomAdapter);

            binding.getRoot().post(this::updateStaticWallpaper);

            binding.llWpMore.setOnClickListener(v -> {
                wallpaperStaticAdapter.toggleExpand();
                binding.tvWpMore.setText(wallpaperStaticAdapter.isExpanded() ? R.string.str_wrapper_less : R.string.str_wrapper_more);
                binding.ivWpMore.setImageResource(wallpaperStaticAdapter.isExpanded() ? R.mipmap.ic_retract : R.mipmap.ic_expand);
                updateStaticWallpaper();
            });


            binding.tvCustomEdit.setOnClickListener(v -> {
                updateTvCustomEditUI();
                wallpaperCustomAdapter.editMode();
            });

        }

        public void onViewClicked() {
            binding.ivWallpaperDetail.setOnClickListener(v -> {
                if (wallpaperAlert == null) {
                    wallpaperAlert = new WallpaperDetailUIAlert.Builder(context);
                }
                wallpaperAlert.create().show();
            });
            binding.spvWallpaper.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    wallpaperCustomAdapter.isEditMode = false;
                    binding.tvCustomEdit.setText(R.string.str_edit);
                    switch (index) {
                        case 0:
                            setSwitchView(binding.fmScene, binding.fmStatic, false);
                            break;
                        case 1:
                            setSwitchView(binding.fmScene, binding.fmStatic, true);
                            break;
                    }
                }
            });
        }

        private void updateStaticWallpaper() {
            RecyclerView recyclerView = binding.rvStatic;
            RecyclerView.Adapter adapter = recyclerView.getAdapter();
            GridLayoutManager layoutManager = (GridLayoutManager) recyclerView.getLayoutManager();

            if (adapter == null || layoutManager == null || adapter.getItemCount() == 0) return;

            recyclerView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    recyclerView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    View itemView = layoutManager.findViewByPosition(0);
                    if (itemView == null) return;

                    int itemHeight = itemView.getHeight();
                    int rowCount = (int) Math.ceil((double) adapter.getItemCount() / layoutManager.getSpanCount());
                    int totalHeight = rowCount * itemHeight + rowCount * 40;

                    ViewGroup.LayoutParams params = recyclerView.getLayoutParams();
                    params.height = totalHeight;
                    recyclerView.setLayoutParams(params);
                }
            });
        }

        void updateTvCustomEditUI() {
            if (wallpaperCustomAdapter.isEditMode) {
                binding.tvCustomEdit.setText(R.string.str_edit);
            } else {
                binding.tvCustomEdit.setText(R.string.str_done);
            }
        }

        void updateTvStaticEditUI() {
            if (wallpaperStaticAdapter.isExpanded()) {
                binding.tvCustomEdit.setText(R.string.str_edit);
            } else {
                binding.tvCustomEdit.setText(R.string.str_done);
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public void dismiss() {
            if (dialog != null) {
                dialog.dismiss();
            }
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }
    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;

}
