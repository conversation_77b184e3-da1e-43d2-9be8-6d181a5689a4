package com.bitech.vehiclesettings.presenter.light;

import static com.bitech.vehiclesettings.MyApplication.getContext;

import android.content.Context;
import android.provider.Settings;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.ColorBean;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.bean.pick.FrontBean;
import com.bitech.platformlib.bean.pick.PickFrontBean;
import com.bitech.platformlib.bean.pick.PickRearBean;
import com.bitech.platformlib.bean.pick.RearBean;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.presenter.light.manager.LightInManager;
import com.bitech.vehiclesettings.utils.AppEnum;
import com.bitech.vehiclesettings.utils.ColorUtils;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;

import java.util.ArrayList;
import java.util.List;

public class LightInPresenter implements LightInPresenterListener {
    private static final String TAG = LightInPresenter.class.getSimpleName();
    private LightInBean lInBean;
    private LightInManager lightManager;
    private Context context;
    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(getContext(), BitechCar.CAR_LIGHT_MANAGER);

    public LightInPresenter(Context context) {
        this.context = context;
        if (lInBean == null) {
            lInBean = getLightInData();
        }
        lightManager = LightInManager.getInstance();
    }

    @Override
    public LightInBean getlInBean(boolean force) {
        if (force) {
            lInBean = getLightInData();
        } else {
            if (lInBean == null) {
                lInBean = getLightInData();
            }
        }
        return lInBean;
    }

    @Override
    public LightInBean getlInBean() {
        return getlInBean(false);
    }

    @Override
    public LightInBean getLightInData() {
        return carServer.getLightInData();
    }

    /**
     * 开关
     *
     * @param open
     * @param pos
     */
    @Override
    public void setAmbLightSw(int open, int pos) {
        new Thread(() -> {
            if (lInBean != null) {
                lInBean.setLightSw(open);
                lInBean.setLightFontSw(open);
                lInBean.getFront().setFront(open);
                lInBean.setLightRearSw(open);
                lInBean.getRear().setRear(open);
                lInBean.save(getContext());
            }
            Log.d(TAG, "[氛围灯开关]开关: " + lInBean.getLightSw() + ",前排开关：" + lInBean.getLightFontSw() + ",后排开关：" + lInBean.getLightRearSw());
            lightManager.atmosphereSwitch(open, lInBean);
        }).start();
    }

    /**
     * 开关
     *
     * @return
     */
    @Override
    public int getLightSw() {
        return lInBean.getLightSw();
    }

    /**
     * 设置氛围灯主题
     *
     * @param state
     */
    @Override
    public void chgThemeMode(int state) {
        Prefs.put(PrefsConst.SELECT_THEME, state);
        if (lInBean != null) {
            lInBean.setThemeMode(state);
            Log.d(TAG, "切换主题: " + state);
            if (CommonConst.AtmosphereTheme.THEME == state) {
                int pos = lInBean.getColorPos();
                if (pos >= 5) {
                    // 单色
                    lInBean.setCurScene(AppEnum.Scene.STATIC_SINGLE.getValue());
                    if (lInBean.getLampEffect() == 0) {
                        lightManager.atmosphereAdj(lInBean.getBrightPos(), lInBean.getSingleColorLin());
                    } else if (lInBean.getLampEffect() == 1) {
                        lightManager.atmosphereAdj(CarLight.AmbLigBriAdj.Invalid, lInBean.getSingleColorLin());
                    } else {
                        Log.d(TAG, "【切换主题】 单色getLampEffect：" + lInBean.getLampEffect());
                    }

                } else {
                    // 多色
                    if (lInBean.getLampEffect() == 0) {
                        lightManager.atmosphereFrontAdj(lInBean.getBrightPos(), lInBean.getMutiFrontColorLin());
                        lightManager.atmosphereRearAdj(lInBean.getBrightPos(), lInBean.getMutiRearColorLin());
                    } else if (lInBean.getLampEffect() == 1) {
                        lightManager.atmosphereFrontAdj(CarLight.AmbLigBriAdj.Invalid, lInBean.getMutiFrontColorLin());
                        lightManager.atmosphereRearAdj(CarLight.AmbLigBriAdj.Invalid, lInBean.getMutiRearColorLin());
                    } else {
                        Log.d(TAG, "【切换主题】 多色getLampEffect：" + lInBean.getLampEffect());
                    }
                    lInBean.setCurScene(AppEnum.Scene.STATCIC_MUTI.getValue());
                }
            }
            if (CommonConst.AtmosphereTheme.CUSTOMIZE == state) {
                // 自定义
                if (lInBean.getLampEffect() == 0) {
                    if (lInBean.getFront().getFront() == CommonConst.OPEN) {
                        lightManager.atmosphereFrontAdj(lInBean.getFront().getFrontBrightness(), lInBean.getFront().getFrontColorLin());
                    } else if (lInBean.getFront().getFront() == CommonConst.CLOSE) {
                        lightManager.atmosphereFrontAdj(CarLight.AmbLigBriAdj.LEVEL_0, lInBean.getFront().getFrontColorLin());
                    }

                    if (lInBean.getRear().getRear() == CommonConst.OPEN) {
                        lightManager.atmosphereRearAdj(lInBean.getFront().getFrontBrightness(), lInBean.getRear().getRearColorLin());
                    } else if (lInBean.getRear().getRear() == CommonConst.CLOSE) {
                        lightManager.atmosphereRearAdj(CarLight.AmbLigBriAdj.LEVEL_0, lInBean.getRear().getRearColorLin());
                    }
                } else if (lInBean.getLampEffect() == 1) {
                    if (lInBean.getFront().getFront() == CommonConst.OPEN) {
                        lightManager.atmosphereFrontAdj(CarLight.AmbLigBriAdj.Invalid, lInBean.getFront().getFrontColorLin());
                    } else if (lInBean.getFront().getFront() == CommonConst.CLOSE) {
                        lightManager.atmosphereFrontAdj(CarLight.AmbLigBriAdj.LEVEL_0, lInBean.getFront().getFrontColorLin());
                    }

                    if (lInBean.getRear().getRear() == CommonConst.OPEN) {
                        lightManager.atmosphereRearAdj(CarLight.AmbLigBriAdj.Invalid, lInBean.getRear().getRearColorLin());
                    } else if (lInBean.getRear().getRear() == CommonConst.CLOSE) {
                        lightManager.atmosphereRearAdj(CarLight.AmbLigBriAdj.LEVEL_0, lInBean.getRear().getRearColorLin());
                    }
                } else {
                    Log.d(TAG, "【切换主题】 自定义：" + lInBean.getLampEffect());
                }

                lInBean.setCurScene(AppEnum.Scene.STATCIC_CUST.getValue());
            }
            lInBean.save(getContext());
        }
    }

    /**
     * 单色
     *
     * @param selColorPos
     * @param progress
     * @param colorLin
     * @param lightLevel
     */
    @Override
    public void selectSingleColor(int selColorPos, int progress, int colorLin, int lightLevel) {
        if (lInBean != null) {
            lInBean.setColorPos(selColorPos);
            lInBean.setBrightPos(progress);
            lInBean.setSingleColorLin(colorLin);
            lInBean.setSingleColor(AppEnum.ambientLightColor.getHexByLin(colorLin));
            lInBean.save(getContext());
        }
        lightManager.atmosphereAdj(lightLevel, colorLin);

    }

    /**
     * 多色
     *
     * @param selColorPos
     * @param progress
     * @param colorFrontLin
     * @param colorRearLin
     * @param lightLevel
     */
    @Override
    public void selectMutiColor(int selColorPos, int progress, int colorFrontLin, int colorRearLin, int lightLevel) {
        if (lInBean != null) {
            lInBean.setColorPos(selColorPos);
            lInBean.setBrightPos(progress);
            lInBean.setMutiFrontColorLin(colorFrontLin);

            lInBean.setMutiRearColorLin(colorRearLin);
            lInBean.setMutiFrontColor(AppEnum.ambientLightColor.getHexByLin(colorFrontLin));
            lInBean.setMutiRearColor(AppEnum.ambientLightColor.getHexByLin(colorRearLin));
            lInBean.save(getContext());
        }
        lightManager.atmosphereFrontAdj(lightLevel, colorFrontLin);
        lightManager.atmosphereRearAdj(lightLevel, colorRearLin);
    }

    @Override
    public void brightAdj(int brightPos) {
        brightAdj(brightPos, false);
    }

    /**
     * 亮度调整
     *
     * @param brightPos
     */
    @Override
    public void brightAdj(int brightPos, boolean pressed) {
        if (lInBean != null) {
            lInBean.setLightSync(CommonConst.CLOSE);
            // 1.主题模式亮度调节
            if (CommonConst.AtmosphereTheme.THEME == lInBean.getThemeMode()) {
                // 单色
                if (lInBean.getColorPos() >= CommonConst.COLOR_POS_MAX) {
                    lightManager.atmosphereAdj(brightPos, lInBean.getSingleColorLin());
                } else {
                    lightManager.atmosphereFrontAdj(brightPos, lInBean.getMutiFrontColorLin());
                    lightManager.atmosphereRearAdj(brightPos, lInBean.getMutiRearColorLin());
                }
                lInBean.setBrightPos(brightPos);
                if (pressed) {
                    lInBean.setBrightnessAuto(CommonConst.CLOSE);
                }

                Log.d(TAG, "主题亮度调节: " + brightPos);
            }
            if (CommonConst.AtmosphereTheme.CUSTOMIZE == lInBean.getThemeMode()) {
                if (CommonConst.POS_0 == lInBean.getLightSel()) {
                    // 2.前排亮度调节
                    lightManager.atmosphereFrontAdj(brightPos, lInBean.getFront().getFrontColorLin());
                    lInBean.getFront().setFrontBrightness(brightPos);
                    if (pressed) {
                        lInBean.setLightSync(CommonConst.CLOSE);
                        lInBean.getFront().setFrontBrightnessAuto(CommonConst.CLOSE);
                    }

                    Log.d(TAG, "前排亮度调节: " + brightPos);
                }
                if (CommonConst.POS_1 == lInBean.getLightSel()) {
                    // 3.后排亮度调节
                    lightManager.atmosphereRearAdj(brightPos, lInBean.getRear().getRearColorLin());
                    lInBean.getRear().setRearBrightness(brightPos);
                    if (pressed) {
                        lInBean.setLightSync(CommonConst.CLOSE);
                        lInBean.getRear().setRearBrightnessAuto(CommonConst.CLOSE);
                    }
                    Log.d(TAG, "后排亮度调节: " + brightPos);
                }
            }

            lInBean.save(getContext());
        }


    }


    @Override
    public void syncAtmosphere(int selPos, int syncOpen) {
        Log.d(TAG, "同步氛围灯 位置: " + selPos + ",状态：" + syncOpen);
        if (lInBean != null) {
            lInBean.setLightSync(CommonConst.OPEN);
            if (CommonConst.LIGHT_SW_FRONT == selPos) {
                RearBean rear = syncFontToRear(lInBean.getFront());
                if (rear != null) {
                    lInBean.setRear(rear);
                }
                PickRearBean pickRear = syncPickFrontToPickRear(lInBean.getPickFront());
                if (pickRear != null) {
                    lInBean.setPickRear(pickRear);
                }
            }
            if (CommonConst.LIGHT_SW_REAR == selPos) {
                FrontBean front = syncReaarToFont(lInBean.getRear());
                if (front != null) {
                    lInBean.setFront(front);
                }
                PickFrontBean pickFront = syncPickRearToPickFront(lInBean.getPickRear());
                if (pickFront != null) {
                    lInBean.setPickFront(pickFront);
                }
            }
            lInBean.save(getContext());
        }
        if (CommonConst.LIGHT_SW_FRONT == selPos) {
            // 前排 :前台同步到后排
            lightManager.atmosphereRearAdj(lInBean.getFront().getFrontBrightness(), lInBean.getFront().getFrontColorLin());
        }

        if (CommonConst.LIGHT_SW_REAR == selPos) {
            // 后排 :
            lightManager.atmosphereFrontAdj(lInBean.getRear().getRearBrightness(), lInBean.getRear().getRearColorLin());
        }
    }


    @Override
    public int getThemeMode() {
        int vmode = PrefsConst.DefaultValue.DEFAULT_VALUE_CLOSE;
        if (lInBean != null) {
            vmode = lInBean.getThemeMode();
        }
        return vmode;
    }

    @Override
    public void setLightFrontOrRear(int pos) {
        if (lInBean != null) {
            lInBean.setLightSel(pos);
            Log.d(TAG, "设置前排后排位置： " + pos);
            lInBean.save(getContext());
        }
        int open = 0;
        if (pos == 0) {
            open = lInBean.getFront().getFront();
        } else {
            open = lInBean.getRear().getRear();
        }
        Prefs.put(PrefsConst.SELECT_FRONT_REAR, pos);

        lightManager.OpenorClose(open);
    }


    @Override
    public void setBrightnessAuto(int state) {
        int autoBrightStatus = carServer.getAutoLamp();
        int lightLevel = 0;
        if (autoBrightStatus == CarLight.SAutoLamp.ACTIVATE) {
            // 发送连亮度100%
            lightLevel = 0x64;
        } else if (autoBrightStatus == CarLight.SAutoLamp.DEAC_TIVATE) {
            // 发送亮度15%
            lightLevel = 15;
        }
        // 主题亮度调节
        if (lInBean != null) {
            if (CommonConst.AtmosphereTheme.THEME == lInBean.getThemeMode()) {
                // 主题
                int pos = lInBean.getColorPos();
                if (pos >= 5) {
                    // 单色
                    lightManager.atmosphereAdj(lightLevel, lInBean.getSingleColorLin());
                } else {
                    // 多色
                    lightManager.atmosphereFrontAdj(lightLevel, lInBean.getMutiFrontColorLin());
                    lightManager.atmosphereRearAdj(lightLevel, lInBean.getMutiRearColorLin());
                }
                lInBean.setBrightPos(lightLevel);
                lInBean.setBrightnessAuto(state);
                Log.d(TAG, "【亮度】主题亮度调节: " + lightLevel + ",自动亮度状态:" + state + ",autoBrightStatus:" + autoBrightStatus);
            } else {
                if (CommonConst.CLOSE == lInBean.getLightSel()) {
                    // 前排
                    lightManager.atmosphereFrontAdj(lightLevel, lInBean.getFront().getFrontColorLin());
                    lInBean.getFront().setFrontBrightness(lightLevel);
                    lInBean.getFront().setFrontBrightnessAuto(state);
                    Log.d(TAG, "【亮度】前排亮度调节: " + lightLevel + ",自动亮度状态:" + state + ",autoBrightStatus:" + autoBrightStatus);
                } else {
                    // 后排
                    lightManager.atmosphereRearAdj(lightLevel, lInBean.getRear().getRearColorLin());
                    lInBean.getRear().setRearBrightness(lightLevel);
                    lInBean.getRear().setRearBrightnessAuto(state);
                    Log.d(TAG, "【亮度】后排亮度调节: " + lightLevel + ",自动亮度状态:" + state + ",autoBrightStatus:" + autoBrightStatus);
                }
            }
            lInBean.save(getContext());
        }
    }

    @Override
    public int getAutoLamp() {
        int autoBrightStatus = carServer.getAutoLamp();
        return autoBrightStatus;
    }

    @Override
    public void setLightEffect(int state) {
        // 灯效
        if (MsgUtil.getInstance().supportPowerMode()) {
            int effet = PrefsConst.DefaultValue.L_LIGHT_EFFECT;
            if (lInBean != null) {
                switch (state) {
                    case 0:
                        // 静态
                        Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_0);
                        lInBean.setLampEffect(CommonConst.AtmosphereEffect.EFFECT_0);
                        if (lInBean.getThemeMode() == 0) {
                            if (lInBean.getColorPos() >= 5) {
                                lInBean.setCurScene(AppEnum.Scene.STATIC_SINGLE.getValue());
                            } else {
                                lInBean.setCurScene(AppEnum.Scene.STATCIC_MUTI.getValue());
                            }

                        } else {
                            lInBean.setCurScene(AppEnum.Scene.STATCIC_CUST.getValue());
                        }
                        break;
                    case 1:
                        // 呼吸
                        Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_1);
                        lInBean.setLampEffect(CommonConst.AtmosphereEffect.EFFECT_1);
                        lInBean.setCurScene(AppEnum.Scene.BREATHE.getValue());
                        break;
                    case 2:
                        // 渐变
                        Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_2);
                        lInBean.setLampEffect(CommonConst.AtmosphereEffect.EFFECT_2);
                        lInBean.setCurScene(AppEnum.Scene.GRADIENT.getValue());
                        break;
                    case 3:
                        // 音乐律动
                        Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_3);
                        lInBean.setLampEffect(CommonConst.AtmosphereEffect.EFFECT_3);
                        lInBean.setCurScene(AppEnum.Scene.RHYTHM.getValue());
                        // adb shell settings get global vehiclesetting_rhythm_and_cadence.
                        break;
                    default:
                        Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_0);
                        lInBean.setLampEffect(CommonConst.AtmosphereEffect.EFFECT_0);
                        if (lInBean.getThemeMode() == 0) {
                            if (lInBean.getColorPos() >= 5) {
                                lInBean.setCurScene(AppEnum.Scene.STATIC_SINGLE.getValue());
                            } else {
                                lInBean.setCurScene(AppEnum.Scene.STATCIC_MUTI.getValue());
                            }

                        } else {
                            lInBean.setCurScene(AppEnum.Scene.STATCIC_CUST.getValue());
                        }
                        break;
                }
                lInBean.save(getContext());
            }
        }
    }

    @Override
    public int getLightEffect() {
        return lInBean.getLampEffect();
    }

    @Override
    public void pickColor(int red, int green, int blue, int positionX) {
        String colorHex = ColorUtils.rgbToHex(red, green, blue);
        pickColor(colorHex, positionX);
    }

    @Override
    public int getPickColorPosX() {
        if (lInBean != null) {
            if (lInBean.getLightSel() == CommonConst.POS_0) {
                PickFrontBean frontBean = lInBean.getPickFront();
                return frontBean.getPositionX();
            }
            if (lInBean.getLightSel() == CommonConst.POS_1) {
                PickRearBean rearBean = lInBean.getPickRear();
                return rearBean.getPositionX();
            }
        }
        return 0;
    }

    @Override
    public ColorBean getPickColor(int frontSel, int pos) {
        ColorBean color = new ColorBean();
        if (lInBean != null) {
            if (frontSel == CommonConst.POS_0) {
                PickFrontBean frontBean = lInBean.getPickFront();
                switch (pos) {
                    case 0:
                        color = frontBean.getColor0();
                        break;
                    case 1:
                        color = frontBean.getColor1();
                        break;
                    case 2:
                        color = frontBean.getColor2();
                        break;
                    case 3:
                        color = frontBean.getColor3();
                        break;
                    case 4:
                        color = frontBean.getColor4();
                        break;
                    case 5:
                        color = frontBean.getColor5();
                        break;
                }
            }
            if (frontSel == CommonConst.POS_1) {
                PickRearBean rearBean = lInBean.getPickRear();
                switch (pos) {
                    case 0:
                        color = rearBean.getColor0();
                        break;
                    case 1:
                        color = rearBean.getColor1();
                        break;
                    case 2:
                        color = rearBean.getColor2();
                        break;
                    case 3:
                        color = rearBean.getColor3();
                        break;
                    case 4:
                        color = rearBean.getColor4();
                        break;
                    case 5:
                        color = rearBean.getColor5();
                        break;
                }
            }
        }
        return color;
    }

    /**
     * 保存值
     *
     * @param color
     */
    @Override
    public void savePickColor(int pos, ColorBean color) {
        if (lInBean != null) {
            lInBean.setLightSync(CommonConst.CLOSE);
            if (lInBean.getLightSel() == CommonConst.POS_0) {
                PickFrontBean frontBean = lInBean.getPickFront();
                switch (pos) {
                    case 0:
                        frontBean.setColor0(color);
                        break;
                    case 1:
                        frontBean.setColor1(color);
                        break;
                    case 2:
                        frontBean.setColor2(color);
                        break;
                    case 3:
                        frontBean.setColor3(color);
                        break;
                    case 4:
                        frontBean.setColor4(color);
                        break;
                    case 5:
                        frontBean.setColor5(color);
                        break;
                }
                lInBean.setPickFront(frontBean);
            }
            if (lInBean.getLightSel() == CommonConst.POS_1) {
                PickRearBean rearBean = lInBean.getPickRear();
                switch (pos) {
                    case 0:
                        rearBean.setColor0(color);
                        break;
                    case 1:
                        rearBean.setColor1(color);
                        break;
                    case 2:
                        rearBean.setColor2(color);
                        break;
                    case 3:
                        rearBean.setColor3(color);
                        break;
                    case 4:
                        rearBean.setColor4(color);
                        break;
                    case 5:
                        rearBean.setColor5(color);
                        break;
                }
                lInBean.setPickRear(rearBean);
            }
        }
        lInBean.save(getContext());
    }

    @Override
    public void pickColor(String colorHex, int positionX) {
        Log.d(TAG, "pickColor colorHex:  " + colorHex);
        int colorLin = AppEnum.ambientLightColor.getLinByHex(colorHex);
        int color = ColorUtils.hexColorToInt(colorHex);
        if (lInBean != null) {
            lInBean.setLightSync(CommonConst.CLOSE);
            PickFrontBean frontBean = lInBean.getPickFront();
            PickRearBean rearBean = lInBean.getPickRear();
            if (lInBean.getLightSel() == CommonConst.POS_0) {
                frontBean.setPickColor(colorHex);
                frontBean.setPositionX(positionX);
                frontBean.setPickLin(colorLin);
                lInBean.setPickFront(frontBean);
                lInBean.getFront().setFrontColor(color);
                lInBean.getFront().setFrontColorLin(colorLin);
                Log.d(TAG, "[颜色选中]前排: positionX" + positionX + ",colorHex:" + colorHex + ",colorLin:" + colorLin + ",rgb:" + ColorUtils.hexToRgbStr(colorHex));
            }
            if (lInBean.getLightSel() == CommonConst.POS_1) {
                rearBean.setPickColor(colorHex);
                rearBean.setPositionX(positionX);
                rearBean.setPickLin(colorLin);
                lInBean.setPickRear(rearBean);
                lInBean.getRear().setRearColor(color);
                lInBean.getRear().setRearColorLin(colorLin);
                Log.d(TAG, "[颜色选中]后排: positionX" + positionX + ",colorHex:" + colorHex + ",colorLin:" + colorLin);
            }
            lInBean.save(getContext());
        }
        if (colorLin > 0) {
            pickColor(colorLin);
        } else {
            Log.d(TAG, "[颜色选中转换过滤] 色值：" + colorHex + ",colorLin: " + colorLin);
        }

    }

    private void pickColor(int colorLin) {
        // 发送数据
        if (lInBean != null) {
            if (lInBean.getLightSel() == 0 && lInBean.getFront().getFront() == CommonConst.OPEN) {

                lightManager.atmosphereFrontAdj(lInBean.getFront().getFrontBrightness(), colorLin);
            }
            if (lInBean.getLightSel() == 1 && lInBean.getRear().getRear() == CommonConst.OPEN) {
                lightManager.atmosphereRearAdj(lInBean.getRear().getRearBrightness(), colorLin);
            }
            Log.d(TAG, "[颜色选中]自定义选中颜色: " + colorLin + ",亮度：" + lInBean.getRear().getRearBrightness());
        }
    }

    /**
     * @param state
     */
    @Override
    public void setAutomaticCeiling(int state) {
        carServer.setDoorControlSW(state);
    }

    /**
     * @return
     */
    @Override
    public int getAutomaticCeiling() {
        int status = carServer.getAutoReadLightSts();
        if (lInBean != null) {
            lInBean.setAutomaticCeiling(status);
            lInBean.save(getContext());
        }
        return status;
    }

    @Override
    public List<ColorBean> getColors() {
        List<ColorBean> colorBeans = new ArrayList<>();
        // 多色
        ColorBean colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_8;
        colorBean.name = AppEnum.Color.PURPLE.getName();
        colorBean.xPosition = 527;
        colorBean.yPosition = 100;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.MULTI_COLOR_1);
        colorBean.colorFrontHex = AppEnum.ambientLightColor.getHexByLin(22); // 绿
        colorBean.colorRearHex = AppEnum.ambientLightColor.getHexByLin(1); // 红
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_9;
        colorBean.name = AppEnum.Color.PURPLE.getName();
        colorBean.xPosition = 527;
        colorBean.yPosition = 100;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.MULTI_COLOR_2);
        colorBean.colorFrontHex = AppEnum.ambientLightColor.getHexByLin(48); // 紫
        colorBean.colorRearHex = AppEnum.ambientLightColor.getHexByLin(36);  //青
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_10;
        colorBean.name = AppEnum.Color.PURPLE.getName();
        colorBean.xPosition = 527;
        colorBean.yPosition = 100;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.MULTI_COLOR_3);
        colorBean.colorFrontHex = AppEnum.ambientLightColor.getHexByLin(1);
        colorBean.colorRearHex = AppEnum.ambientLightColor.getHexByLin(43);
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_11;
        colorBean.name = AppEnum.Color.PURPLE.getName();
        colorBean.xPosition = 527;
        colorBean.yPosition = 100;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.MULTI_COLOR_4);
        colorBean.colorFrontHex = AppEnum.ambientLightColor.getHexByLin(36);
        colorBean.colorRearHex = AppEnum.ambientLightColor.getHexByLin(12);
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_12;
        colorBean.name = AppEnum.Color.PURPLE.getName();
        colorBean.xPosition = 527;
        colorBean.yPosition = 100;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.MULTI_COLOR_5);
        colorBean.colorFrontHex = AppEnum.ambientLightColor.getHexByLin(43);
        colorBean.colorRearHex = AppEnum.ambientLightColor.getHexByLin(7);
        colorBeans.add(colorBean);
        // 单色
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_1;
        colorBean.xPosition = -3;
        colorBean.yPosition = 101;
        colorBean.name = AppEnum.Color.RED.getName();
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.RED);
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_2;
        colorBean.name = AppEnum.Color.ORANGE.getName();
        colorBean.xPosition = 49;
        colorBean.yPosition = -2;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.ORANGE);
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_3;
        colorBean.name = AppEnum.Color.YELLOW.getName();
        colorBean.xPosition = 123;
        colorBean.yPosition = 127;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.YELLOW);
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_4;
        colorBean.name = AppEnum.Color.GREEN.getName();
        colorBean.xPosition = 232;
        colorBean.yPosition = 127;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.GREEN);
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_5;
        colorBean.name = AppEnum.Color.YOUNG.getName();
        colorBean.xPosition = 365;
        colorBean.yPosition = 127;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.YOUNG);
        colorBeans.add(colorBean);
        colorBean = new ColorBean();

        colorBean.resBgId = R.mipmap.ic_card_6;
        colorBean.name = AppEnum.Color.BLUE.getName();
        colorBean.xPosition = 499;
        colorBean.yPosition = 60;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.BLUE);
        colorBeans.add(colorBean);
        colorBean = new ColorBean();
        colorBean.resBgId = R.mipmap.ic_card_7;
        colorBean.name = AppEnum.Color.PURPLE.getName();
        colorBean.xPosition = 527;
        colorBean.yPosition = 100;
        colorBean.colorHex = ColorUtils.defaultRgbToHex(CommonConst.PURPLE);
        colorBeans.add(colorBean);
        return colorBeans;
    }

    /**
     * 获取当前亮度
     *
     * @return
     */
    public int getCurColorLin() {
        if (lInBean != null) {
            return lInBean.getBrightPos();
        }
        return 0;
    }

    // 语音功能调用接口 2.颜色调节
    public int getCurBrightness() {
        if (lInBean != null) {
            List<ColorBean> colorBeanList = getColors();
            int position = lInBean.getColorPos();
            return AppEnum.ambientLightColor.getLinByHex(colorBeanList.get(position).colorHex);
        }
        return 0;
    }

    //
    public void changeColorLin(int colorLin) {
        if (lInBean != null) {
            lightManager.atmosphereAdj(lInBean.getBrightPos(), colorLin);
            lInBean.setThemeMode(0);
        }
    }

    public void changeBrightness(int bri) {
        if (lInBean != null) {
            if (lInBean.getThemeMode() == 0) {
                if (lInBean.getColorPos() >= CommonConst.COLOR_POS_MAX) {
                    //单色
                    lightManager.atmosphereAdj(bri, lInBean.getSingleColorLin());
                    Log.d(TAG, "单色亮度调整: " + bri);
                } else {

                    lightManager.atmosphereFrontAdj(bri, lInBean.getFront().getFrontColorLin());
                    lightManager.atmosphereFrontAdj(bri, lInBean.getRear().getRearColorLin());
                    Log.d(TAG, "多色亮度调整: " + bri);
                }
            } else {
                if (lInBean.getFront().getFront() == CommonConst.OPEN) {
                    lightManager.atmosphereFrontAdj(bri, lInBean.getFront().getFrontColorLin());
                    Log.d(TAG, "前排亮度调整: " + bri);
                }
                if (lInBean.getRear().getRear() == CommonConst.OPEN) {
                    lightManager.atmosphereFrontAdj(bri, lInBean.getRear().getRearColorLin());
                    Log.d(TAG, "后排亮度调整: " + bri);
                }
            }
        }
    }

    /**
     * 获取当前亮度
     *
     * @return
     */
    public int getCurColorLin(int pos) {
        if (lInBean != null) {
            if (pos == CommonConst.Theme.THEME) {
                return lInBean.getBrightPos();
            }
            if (pos == CommonConst.Theme.FRONT) {
                return lInBean.getFront().getFrontBrightness();
            }
            if (pos == CommonConst.Theme.REAR) {
                return lInBean.getRear().getRearBrightness();
            }
        }
        return 0;
    }

    // 语音功能调用接口 2.颜色调节
    public int getCurBrightness(int pos) {
        if (lInBean != null) {
            if (pos == CommonConst.Theme.THEME) {
                List<ColorBean> colorBeanList = getColors();
                int position = lInBean.getColorPos();
                return AppEnum.ambientLightColor.getLinByHex(colorBeanList.get(position).colorHex);
            } else {
                if (pos == CommonConst.Theme.FRONT) {
                    return lInBean.getFront().getFrontColorLin();
                }
                if (pos == CommonConst.Theme.REAR) {
                    return lInBean.getRear().getRearColorLin();
                }
            }

        }
        return 0;
    }

    //
    public void changeColorLin(int pos, int colorLin) {
        if (lInBean != null) {
            if (pos == CommonConst.Theme.THEME) {
                lightManager.atmosphereAdj(lInBean.getBrightPos(), colorLin);
                lInBean.setThemeMode(0);
            } else {
                if (pos == CommonConst.Theme.FRONT) {
                    lightManager.atmosphereFrontAdj(lInBean.getFront().getFrontColorLin(), colorLin);
                    lInBean.setThemeMode(1);
                    lInBean.setLightSel(0);
                }
                if (pos == CommonConst.Theme.REAR) {
                    lightManager.atmosphereRearAdj(lInBean.getRear().getRearColorLin(), colorLin);
                    lInBean.setThemeMode(1);
                    lInBean.setLightSel(1);
                }
            }

        }
    }

    public void changeBrightness(int pos, int bri) {
        if (lInBean != null) {
            if (CommonConst.Theme.THEME == pos) {
                if (lInBean.getColorPos() >= CommonConst.COLOR_POS_MAX) {
                    //单色
                    lightManager.atmosphereAdj(bri, lInBean.getSingleColorLin());
                    Log.d(TAG, "单色亮度调整: " + bri);
                } else {

                    lightManager.atmosphereFrontAdj(bri, lInBean.getFront().getFrontColorLin());
                    lightManager.atmosphereFrontAdj(bri, lInBean.getRear().getRearColorLin());
                    Log.d(TAG, "多色亮度调整: " + bri);
                }
                lInBean.setThemeMode(0);
            } else {
                if (pos == CommonConst.Theme.FRONT) {
                    lightManager.atmosphereFrontAdj(bri, lInBean.getFront().getFrontColorLin());
                    lInBean.setThemeMode(1);
                    lInBean.setLightSel(0);
                    Log.d(TAG, "前排亮度调整: " + bri);
                }
                if (pos == CommonConst.Theme.REAR) {
                    lightManager.atmosphereFrontAdj(bri, lInBean.getRear().getRearColorLin());
                    lInBean.setThemeMode(1);
                    lInBean.setLightSel(1);
                    Log.d(TAG, "后排亮度调整: " + bri);
                }
            }
        }
    }

    private RearBean syncFontToRear(FrontBean front) {
        RearBean rear = null;
        if (front != null) {
            rear = new RearBean();
            rear.setRear(front.getFront());
            rear.setRearBrightness(front.getFrontBrightness());
            rear.setRearColor(front.getFrontColor());
            rear.setRearColorLin(front.getFrontColorLin());
            rear.setRearBrightnessAuto(front.getFrontBrightnessAuto());
            rear.setRearAtmosphere(front.getFrontAtmosphere());
        }
        return rear;
    }

    private PickRearBean syncPickFrontToPickRear(PickFrontBean pickFront) {
        PickRearBean pickRear = null;
        if (pickFront != null) {
            pickRear = new PickRearBean();
            pickRear.setColor0(copyColorBean(pickFront.getColor0()));
            pickRear.setColor1(copyColorBean(pickFront.getColor1()));
            pickRear.setColor2(copyColorBean(pickFront.getColor2()));
            pickRear.setColor3(copyColorBean(pickFront.getColor3()));
            pickRear.setColor4(copyColorBean(pickFront.getColor4()));
            pickRear.setColor5(copyColorBean(pickFront.getColor5()));
            pickRear.setPickColor(pickFront.getPickColor());
            pickRear.setPickLin(pickFront.getPickLin());
            pickRear.setPositionX(pickFront.getPositionX());
        }
        return pickRear;
    }

    private ColorBean copyColorBean(ColorBean colorBean) {
        ColorBean colorB = new ColorBean();
        if (colorBean != null) {
            colorB.resBgId = colorBean.resBgId;
            colorB.isSelected = colorBean.isSelected;
            colorB.xPosition = colorBean.xPosition;
            colorB.yPosition = colorBean.yPosition;
            colorB.name = colorBean.name;
            colorB.colorHex = colorBean.colorHex;
            colorB.colorFrontHex = colorBean.colorFrontHex;
            colorB.colorRearHex = colorBean.colorRearHex;
            colorB.hasColor = colorBean.hasColor;
        }
        return colorB;
    }

    private FrontBean syncReaarToFont(RearBean rear) {
        FrontBean front = null;
        if (rear != null) {
            front = new FrontBean();
            front.setFront(rear.getRear());
            front.setFrontBrightness(rear.getRearBrightness());
            front.setFrontColor(rear.getRearColor());
            front.setFrontColorLin(rear.getRearColorLin());
            front.setFrontBrightnessAuto(rear.getRearBrightnessAuto());
            front.setFrontAtmosphere(rear.getRearAtmosphere());
        }
        return front;
    }

    private PickFrontBean syncPickRearToPickFront(PickRearBean pickRear) {
        PickFrontBean pickFront = null;
        if (pickRear != null) {
            pickFront = new PickFrontBean();
            pickFront.setColor0(copyColorBean(pickRear.getColor0()));
            pickFront.setColor1(copyColorBean(pickRear.getColor1()));
            pickFront.setColor2(copyColorBean(pickRear.getColor2()));
            pickFront.setColor3(copyColorBean(pickRear.getColor3()));
            pickFront.setColor4(copyColorBean(pickRear.getColor4()));
            pickFront.setColor5(copyColorBean(pickRear.getColor5()));
            pickFront.setPickColor(pickRear.getPickColor());
            pickFront.setPickLin(pickRear.getPickLin());
            pickFront.setPositionX(pickRear.getPositionX());
        }
        return pickFront;
    }

}
