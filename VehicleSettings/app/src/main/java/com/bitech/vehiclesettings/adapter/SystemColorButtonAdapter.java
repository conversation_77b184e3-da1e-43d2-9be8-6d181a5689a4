package com.bitech.vehiclesettings.adapter;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;

import java.util.List;

/**
 * 主题色选择器 Adapter，支持程序化切换选中项并回调
 */
public class SystemColorButtonAdapter extends RecyclerView.Adapter<SystemColorButtonAdapter.ViewHolder> {
    private final List<Integer> colorList;
    private int selectedIndex = 0; // 默认选中第一个
    private final OnColorSelectedListener listener;

    /**
     * 选色回调接口
     */
    public interface OnColorSelectedListener {
        void onColorSelected(int color);
    }

    public SystemColorButtonAdapter(List<Integer> colorList, OnColorSelectedListener listener) {
        this.colorList = colorList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_system_color_button, parent, false);
        return new ViewHolder(view);
    }

    @SuppressLint("UseCompatLoadingForColorStateLists")
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        int color = colorList.get(position);
        holder.btnColor.setBackgroundTintList(
                holder.itemView.getContext().getResources().getColorStateList(color)
        );
        // 设置选中状态
        holder.btnColor.setSelected(position == selectedIndex);
        // 点击切换选中
        holder.btnColor.setOnClickListener(v -> updateSelection(position));
    }

    @Override
    public int getItemCount() {
        return colorList.size();
    }

    /**
     * 手动切换到指定下标
     * 调用此方法可在代码里切换选中项并触发回调
     *
     * @param index 要选中的下标
     */
    public void setSelectedIndex(int index) {
        if (index < 0 || index >= colorList.size() || index == selectedIndex) {
            return;
        }
        updateSelection(index);
    }

    /**
     * 更新选中状态并回调
     */
    private void updateSelection(int index) {
        selectedIndex = index;
        notifyDataSetChanged();
        // 通知监听器颜色已选中
        listener.onColorSelected(colorList.get(index));
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        Button btnColor;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            btnColor = itemView.findViewById(R.id.btnColor);
        }
    }
}
