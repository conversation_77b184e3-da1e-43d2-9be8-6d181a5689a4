package com.bitech.vehiclesettings.presenter.display;

import android.content.Context;
import android.provider.Settings;

import com.bitech.vehiclesettings.bean.screensaver.ScreensaverConfig;
import com.bitech.vehiclesettings.bean.screensaver.ScreensaverInfo;
import com.google.gson.Gson;

public class ScreensaverConfigHelper {
    private static final String SETTINGS_KEY = "screensaver_config";

    public static ScreensaverConfig getConfig(Context context) {
        String json = Settings.Global.getString(context.getContentResolver(), SETTINGS_KEY);
        if (json == null) {
            return createDefaultConfig();
        }
        return new Gson().fromJson(json, ScreensaverConfig.class);
    }

    public static void saveConfig(Context context, ScreensaverConfig config) {
        String json = new Gson().toJson(config);
        Settings.Global.putString(context.getContentResolver(), SETTINGS_KEY, json);
    }

    private static ScreensaverConfig createDefaultConfig() {
        ScreensaverConfig config = new ScreensaverConfig();
        // 初始化默认配置
        return config;
    }

    /**
     * 获取当前屏保
     * @param context
     * @return
     */
    public ScreensaverInfo getCurrentScreensaver(Context context) {
        ScreensaverConfig config = ScreensaverConfigHelper.getConfig(context);
        return config.getCurrent();
    }

    /**
     * 设置当前屏保
     * @param context
     * @param path
     * @param type
     */
    public void setCurrentScreensaver(Context context, String path, int type) {
        ScreensaverConfig config = ScreensaverConfigHelper.getConfig(context);
        ScreensaverInfo current = new ScreensaverInfo(path, type, "", "");
        config.setCurrent(current);
        ScreensaverConfigHelper.saveConfig(context, config);
    }

    /**
     * 添加屏保
     * @param context
     * @param info
     */
    public void addScreensaver(Context context, ScreensaverInfo info) {
        ScreensaverConfig config = ScreensaverConfigHelper.getConfig(context);
        config.getList().add(info);
        ScreensaverConfigHelper.saveConfig(context, config);
    }
}