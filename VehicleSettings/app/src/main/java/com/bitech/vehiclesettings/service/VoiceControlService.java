package com.bitech.vehiclesettings.service;


import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.lifecycle.LifecycleService;

import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.presenter.light.LightInPresenter;
import com.bitech.vehiclesettings.presenter.light.LightOutPresenter;
import com.bitech.vehiclesettings.presenter.light.LightPresenter;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenter;
import com.bitech.vehiclesettings.service.voice.ActivityControl;
import com.bitech.vehiclesettings.service.voice.CarWindowControl;
import com.bitech.vehiclesettings.service.voice.ConditionControl;
import com.bitech.vehiclesettings.service.voice.ConnectControl;
import com.bitech.vehiclesettings.service.voice.DisplayControl;
import com.bitech.vehiclesettings.service.voice.LightControl;
import com.bitech.vehiclesettings.service.voice.NewEnergyControl;
import com.bitech.vehiclesettings.service.voice.SystemControl;
import com.bitech.vehiclesettings.service.voice.VehicleControl;
import com.bitech.vehiclesettings.service.voice.VoiceControl;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.client.bind.VDServiceDef;
import com.chery.ivi.vdb.client.listener.VDNotifyListener;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.id.vr.VDEventVR;
import com.chery.ivi.vdb.event.id.vr.VDVRFunctionID;
import com.chery.ivi.vdb.event.id.vr.VDValueVR;
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P;


public class VoiceControlService extends LifecycleService {
    private final String TAG = getClass().getSimpleName();
    // 选项值/自定义（选项类语音） @param 自定义
    private static VoiceControlService instanceSvr;
    private ConnectControl connectControl = new ConnectControl();
    private VehicleControl vehicleControl;
    private CarWindowControl carWindowControl = new CarWindowControl();
    private LightInPresenter mLightInPresenter;
    private LightPresenter mLightPresenter;
    private LightOutPresenter mLightOutPresenter;
    private QuickPresenter mQuickPresenter;
    private ConditionControl conditionControl = new ConditionControl();
    private VoiceControl voicecontrol = new VoiceControl();
    private DisplayControl displayControl = new DisplayControl(MyApplication.getContext());
    private NewEnergyControl newEnergyControl = new NewEnergyControl(MyApplication.getContext());
    private SystemControl systemControl = new SystemControl(MyApplication.getContext());

    private LightControl lightControl = new LightControl();
    private ActivityControl activityControl = new ActivityControl();

    @Override
    public void onCreate() {
        super.onCreate();
        setForeground();
        mLightInPresenter = new LightInPresenter(this);
        mLightPresenter = new LightPresenter(this);
        mLightOutPresenter = new LightOutPresenter(this);
        mQuickPresenter = new QuickPresenter(this);
        vehicleControl = new VehicleControl(this);
    }

    public static VoiceControlService getInstance() {
        return instanceSvr;
    }

    @SuppressLint("ForegroundServiceType")
    private void setForeground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            final String CHANNEL_ID = "3";
            final CharSequence CHANNEL_NAME = "VehicleService";
            NotificationManager notificationManager = (NotificationManager)
                    this.getSystemService(Context.NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_MIN);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
            Notification notification = new Notification.Builder(
                    this, CHANNEL_ID).build();
            startForeground(3, notification);
        } else {
            Notification notification = new Notification();
            startForeground(3, notification);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 初始化工作
        registerCallback();
        Log.d(TAG, "语音服务 onStartCommand: ");
        return super.onStartCommand(intent, flags, startId);
    }

    private void registerCallback() {
        VDBus.getDefault().init(this);
        VDBus.getDefault().bindService(VDServiceDef.ServiceType.VR);
//        // 注册需要监听的事件
        VDBus.getDefault().addSubscribe(VDEventVR.VR_AIRCONDITION);
        VDBus.getDefault().addSubscribe(VDEventVR.VR_VEHICLE_CONTROL);
        VDBus.getDefault().addSubscribe(VDEventVR.VR_VEHICLE_INFO);
        VDBus.getDefault().addSubscribe(VDEventVR.VR_SETTING);
        VDBus.getDefault().addSubscribe(VDEventVR.VR_LAUNCHER);
        VDBus.getDefault().addSubscribe(VDEventVR.VR_PHONE);
        VDBus.getDefault().addSubscribe(VDEventVR.VR_P2P_INTERFACE);
        VDBus.getDefault().addSubscribe(VDEventVR.VR_SYSTEM_UI);
        VDBus.getDefault().subscribeCommit();

        // 注册通知监听器
        VDBus.getDefault().registerVDNotifyListener(mVDNotifyListener);
        Log.d(TAG, "注册通知监听器: ");
    }

    private VDNotifyListener mVDNotifyListener = new VDNotifyListener() {
        public void onVDNotify(VDEvent vdEvent, int threadType) {
            VDP2P param = VDP2P.getValue(vdEvent);
            // 判断收到事件带有 semanticType 为讯飞类型才处理
            if (param == null || !param.getSemanticType().equals(VDValueVR.VRTtsSupplier.TTS_IFLYTEK)) {
                return;
            }
            Log.d("VoiceControlService", "svvr: vdEvent = " + vdEvent.getId() + ", vdEvent.getPayload().toString()=" + vdEvent.getPayload().toString());
            int functionId = param.getFunctionId();
            boolean flag = param.isEnable();
            String operation = param.isEnable() ? "OPERATION_OPEN" : "OPERATION_CLOSE";
            boolean isEnable = param.isEnable();
            String value = param.getValue();
            String position = param.getPosition();
            String user = param.getUser();
//
            switch (vdEvent.getId()) {
                case VDEventVR.VR_VEHICLE_INFO:
                    switch (functionId) {
                        //打开/关闭胎压查询界面
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_TIRE_PRESS:
                            Log.d(TAG, "语音服务: " + operation + "胎压");
                            activityControl.setTirePressureQuery(flag);
                            break;
                        case VDVRFunctionID.VehicleInfo.INFO_QUERY_PRESSURE:
                            Log.d(TAG, "语音服务" + operation + "胎压查询");
                            conditionControl.queryTirePressure();
                            break;
                        case VDVRFunctionID.VehicleInfo.INFO_QUERY_ELECTRICITY:
                            Log.d(TAG, "语音服务: " + operation + "电量查询");
                            displayControl.setElectricityQuery();
                            break;
                        case VDVRFunctionID.VehicleInfo.INFO_QUERY_CUMULATE_MILEAGE:
                            Log.d(TAG, "语音服务" + operation + "查询车累计里程");
                            newEnergyControl.queryTotalMileage();
                            break;
                        case VDVRFunctionID.VehicleInfo.INFO_QUERY_CURRENT_MILEAGE:
                            Log.d(TAG, "语音服务" + operation + "查询本次行程信息");
                            newEnergyControl.queryThisTripInfo();
                            break;
                        case VDVRFunctionID.VehicleInfo.INFO_QUERY_RANGE:
                            Log.d(TAG, "语音服务: " + operation + "续航查询");
                            displayControl.queryRange();
                            break;
                        case VDVRFunctionID.VehicleInfo.INFO_QUERY_FUEL_LEVEL:
                            Log.d(TAG, "语音服务: " + operation + "油量查询");
                            conditionControl.queryFuelLevel();
                            break;
                        case VDVRFunctionID.VehicleInfo.INFO_QUERY_MAINTENANCE_INFO:
                            Log.d(TAG, "语音服务: " + operation + "保养信息查询");
                            conditionControl.queryMaintenanceInfo();
                            break;
                        default:
                            Log.d(TAG, "VDEventVR.VR_VEHICLE_INFO--未匹配functionId: " + functionId);
                            break;
                    }
                    break;
                case VDEventVR.VR_SYSTEM_UI:
                    switch (functionId) {
                        case VDVRFunctionID.SystemUi.SYSTEM_UI_POWER_OFF:
                            Log.d(TAG, "语音服务: " + operation + "下电开关");
                            activityControl.setPowerReset(flag);
                            break;
                        case VDVRFunctionID.SystemUi.SYSTEM_UI_P_GEAR_POWER_MODE:
                            Log.d(TAG, "语音服务: " + operation + "驻车保电");
                            activityControl.parkingPower(flag);
                            break;
                        case VDVRFunctionID.SystemUi.SYSTEM_UI_SCREEN_SAVER:
                            Log.d(TAG, "语音服务: " + operation + "屏保");
                            activityControl.setScreenSaver(flag);
                            break;
                    }
                    break;
                case VDEventVR.VR_SETTING:
                    switch (functionId) {
                        case VDVRFunctionID.Setting.SETTING_CONTROL_WIFI:
                            Log.d(TAG, "语音服务: " + operation + "WIFI");
                            connectControl.setWifi(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_NETWORK_NOTIFICATION:
                            Log.d(TAG, "语音服务: " + operation + "网络通知");
                            connectControl.setNetWorkNotification(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CONTROL_BLUETOOTH:
                            Log.d(TAG, "语音服务: " + operation + "蓝牙");
                            connectControl.setBluetooth(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_VISIBILITY_BLUETOOTH:
                            Log.d(TAG, "语音服务: " + operation + "蓝牙可见性");
                            connectControl.setBtFoundStatus(flag);
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_TIMING:
                            Log.d(TAG, "语音服务: " + operation + "系统时制");
                            systemControl.setTime(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_AUTO_CALIBRATION_DATE_TIME:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭自动校准日期时间");
                            systemControl.open0rcloseCalibrationTime(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CALL_NOTIFICATION:
                            Log.d(TAG, "语音服务: " + operation + "来电播报");
                            connectControl.setCallNotification(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_LANGUAGE:
                            Log.d(TAG, "语音服务" + operation + "系统语言");
                            systemControl.setSystemLanguage(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SWITCH_ELECTRICITY_UNIT:
                            Log.d(TAG, "语音服务" + operation + "切换电耗单位");
                            activityControl.switchPowerConsumptionUnit(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_OIL_UNIT:
                            Log.d(TAG, "语音服务" + operation + "油耗显示单位");
                            systemControl.setFuelDisplayUnit(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_OIL_CONSUME_UNIT_RANDOM:
                            Log.d(TAG, "语音服务" + operation + "切换油耗显示单位");
                            activityControl.switchFuelDisplayUnit(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_RESET_ACCUMULATED_MILEAGE:
                            Log.d(TAG, "语音服务" + operation + "重置累计里程");
                            conditionControl.resetMileageAccrual();
                            break;
                        case VDVRFunctionID.Setting.SETTING_NAV_LOW_MEDIA:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭导航时压低媒体音");
                            voicecontrol.openOrcloseNavigatemeDiatones(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_INS_SHOW_LYRIC:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭仪表显示歌词");
                            displayControl.openOrcloseShowLyrics(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_FONT_SIZE:
                            Log.d(TAG, "语音服务" + operation + "设置字体大小");
                            displayControl.voiceSetFontSize(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_GESTURE_TURN_ACTIVITY:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭手势导航界面");
                            activityControl.setGestureNavigationInterface(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_PAGE_SYSTEM:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭系统设置");
                            activityControl.setSystemSettings(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_RESTORE_FACTORY_SETTING:
                            Log.d(TAG, "语音服务" + operation + "打开恢复出厂设置界面");
                            activityControl.setSystemReset();
                            break;
                        case VDVRFunctionID.Setting.SETTING_SWITCH_EQUIP_NAME:
                            Log.d(TAG, "语音服务" + operation + "修改车机设备名称");
                            activityControl.setSystemInfo(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CONTROL_UNMUTE:
                            Log.d(TAG, "语音服务: " + operation + "取消静音");
                            voicecontrol.setUnmute(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_MODE_SOUND:
                            Log.d(TAG, "语音服务: " + operation + "设置按键音");
                            voicecontrol.setModeSound(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_VOLUME_COMPENSATION:
                            Log.d(TAG, "语音服务: " + operation + "音量补偿");
                            voicecontrol.setVolumeCompensation(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SPEED_REWARD_SET:
                            Log.d(TAG, "语音服务: " + operation + "设置音量补偿模式");
                            voicecontrol.setSpeedRewardSet(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SURROUND_SOUND_MODE:
                            Log.d(TAG, "语音服务: " + operation + "设置环绕音效模式");
                            voicecontrol.setSurroundSoundMode(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_EXTERNAL_SOUND_MODE:
                            Log.d(TAG, "语音服务: " + operation + "设置车外音效外放模式");
                            voicecontrol.setExternalSoundMode(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_VIRTUAL_SCENE_MODE:
                            Log.d(TAG, "语音服务: " + operation + "设置虚拟现场模式");
                            voicecontrol.setVirtualSceneMode(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SOUND_MODE:
                            Log.d(TAG, "语音服务: " + operation + "设置音效模式");
                            voicecontrol.setSoundMode(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CONTROL_CAMERA:
                            Log.d(TAG, "语音服务: " + operation + "打开or关闭摄像头");
                            displayControl.setControlCamera(flag);
                            break;
//                        case VDVRFunctionID.Setting.SETTING_ADJUST_SOUND_EFFECT:
//                            Log.d(TAG, "语音服务: " + operation + "音效调节");
//                            voicecontrol.setAdjustSoundEffect(value);
//                            break;
//                        case VDVRFunctionID.Setting.SETTING_SWITCH_SOUND_MODE:
//                            Log.d(TAG, "语音服务: " + operation + "切换音效模式");
//                            voicecontrol.setSwitchSoundMode();
//                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_TYPE_ALARM:
                            Log.d(TAG, "语音服务: " + operation + "设置报警音量");
                            voicecontrol.setTypeAlarm(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_MODE_SOUND_FIELD:
                            Log.d(TAG, "语音服务: " + operation + "设置声场模式");
                            voicecontrol.setModeSoundField(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SPECIFIC_THEME:
                            Log.d(TAG, "语音服务: " + operation + "设置特定的主题");
                            displayControl.setSpecificTheme(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SPECIFIC_WALLPAPER:
                            Log.d(TAG, "语音服务: " + operation + "设置桌面壁纸模式");
                            displayControl.setSpecificmode(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CLOSE_SPECIFIC_WALLPAPER:
                            Log.d(TAG, "语音服务: " + operation + "关闭特定壁纸模式");
                            displayControl.closeSpecificmode(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SWITCH_WALLPAPER:
                            Log.d(TAG, "语音服务: " + operation + "切换壁纸模式-无指定值");
                            displayControl.switchWallpaper(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SWITCH_PREV_WALLPAPER:
                            Log.d(TAG, "语音服务: " + operation + "切换壁纸模式-上一张");
                            displayControl.switchPrevWallpaper(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_PAGE_DISPLAY:
                            Log.d(TAG, "语音服务: " + operation + "打开显示界面");
                            activityControl.switchPage(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_PAGE_WALLPAPER:
                            Log.d(TAG, "语音服务: " + operation + "打开壁纸界面");
                            activityControl.switchPageWallpaper(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SEARCH_PAIRED_BT_DEVICE:
                            Log.d(TAG, "语音服务: " + operation + "搜索配对蓝牙设备");
                            connectControl.startScanBtDevice();
                            break;
                        case VDVRFunctionID.Setting.SETTING_LINK_BLUETOOTH:
                            Log.d(TAG, "语音服务: " + operation + "跳转蓝牙界面");
                            connectControl.openBluetoothDialog(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CONTROL_HOTSPOT:
                            Log.d(TAG, "语音服务: " + operation + "热点设置");
                            connectControl.setHotspot(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_HOTSPOT_PASSWORD:
                            Log.d(TAG, "语音服务: " + operation + "设置热点密码");
                            connectControl.setHotspotPassword();
                            break;
                        case VDVRFunctionID.Setting.SETTING_LINK_WIFI:
                            Log.d(TAG, "语音服务: " + operation + "跳转wifi界面");
                            connectControl.connectWifi(flag);
                            break;
                        //打开/关闭连接界面
                        case VDVRFunctionID.Setting.SETTING_PAGE_LINK:
                            connectControl.setConnectPage(flag);
                            break;
                        //打开/关闭wifi界面
                        case VDVRFunctionID.Setting.SETTING_PAGE_WIFI:
                            connectControl.setConnectWifiPage(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_VIEW_HISTORY_BT:
                            Log.d(TAG, "语音服务: " + operation + "查看配对蓝牙设备");
                            connectControl.viewBtHistory();
                            break;
                        case VDVRFunctionID.Setting.SETTING_CHANGE_BT_NAME:
                            Log.d(TAG, "语音服务: " + operation + "修改蓝牙名称");
                            connectControl.changeBtName();
                            break;
                        case VDVRFunctionID.Setting.SETTING_DELETE_PAIRED_BT_DEVICE:
                            Log.d(TAG, "语音服务: " + operation + "删除配对蓝牙设备");
                            connectControl.deleteBtDevice();
                            break;
                        case VDVRFunctionID.Setting.SETTING_HOTPOT_LIST:
                            Log.d(TAG, "语音服务: " + operation + "热点列表");
                            connectControl.openHotspot(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_MOBILE_DATA:
                            Log.d(TAG, "语音服务: " + operation + "移动数据");
                            connectControl.set5G(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_HOTSPOT_NAME:
                            Log.d(TAG, "语音服务: " + operation + "设置热点名称");
                            connectControl.changeBtName();
                            connectControl.setDeviceName(flag);
                        case VDVRFunctionID.Setting.SETTING_SWITCH_SCREEN_DISPLAY_MODE:
                            Log.d(TAG, "语音服务: " + operation + "切换显示模式");
                            displayControl.setDisplayMode();
                            break;
                        case VDVRFunctionID.Setting.SETTING_SCREEN_CLEAN:
                            Log.d(TAG, "语音服务: " + operation + "屏幕清洁");
                            displayControl.setScreenClean(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SPLIT_SCREEN_MODE:
                            Log.d(TAG, "语音服务: " + operation + "分屏模式");
                            displayControl.setSplitScreenMode(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_LITTLE_LIGHT_SCREEN:
                            Log.d(TAG, "语音服务: " + operation + "屏幕亮度挡位");
                            displayControl.setScreenBrightnessLevel(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_NUM_LIGHT_SCREEN:
                            Log.d(TAG, "语音服务: " + operation + "屏幕亮度调到具体数值");
                            displayControl.setScreenBrightness(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_PERCENT_LIGHT_SCREEN:
                            Log.d(TAG, "语音服务: " + operation + "屏幕亮度百分比");
                            displayControl.setScreenBrightnessPercentage(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_TIME_TOUCH_SWITCH:
                            Log.d(TAG, "语音服务: " + operation + "设置触摸开关延迟时间");
                            vehicleControl.setTouchSwitchDelayTime(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_MODE_BUTTON_LIGHT:
                            Log.d(TAG, "语音服务: " + operation + "按键亮度调到高中低挡");
                            vehicleControl.adjustHudBrightnessLevel(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_AUTO_SCREEN_OFF:
                            Log.d(TAG, "语音服务: " + operation + "关闭屏幕亮度自动调节");
                            displayControl.setClosedScreenBrightnessAuto(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_AUTO_SCREEN_ON:
                            Log.d(TAG, "语音服务: " + operation + "打开屏幕亮度自动调节");
                            displayControl.setScreenBrightnessAuto(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_NUM_UP_LIGHT_SCREEN:
                            Log.d(TAG, "语音服务: " + operation + "屏幕亮度调大具体数值(整数)");
                            displayControl.setScreenValueUp(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_NUM_DOWN_LIGHT_SCREEN:
                            Log.d(TAG, "语音服务: " + operation + "屏幕亮度调小数值(整数)");
                            displayControl.setScreenValueDown(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_BALANCE_CONTROL:
                            Log.d(TAG, "语音服务: " + operation + "设置系统颜色，指定模式");
                            displayControl.setSystemColorSpecify(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SYSTEM_COLOR_RANDOM:
                            Log.d(TAG, "语音服务: " + operation + "设置系统颜色，无指定模式");
                            displayControl.setSystemColorNoSpecify(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SCREEN_BLUR_FILTER:
                            Log.d(TAG, "语音服务: " + operation + "屏幕蓝光过滤功能开关");
                            displayControl.setScreenBlueClean(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_LEVEL_BLUR_FILTER:
                            Log.d(TAG, "语音服务: " + operation + "设置蓝光过滤强度等级");
                            displayControl.setBlueLightFilterLevel(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SCREEN_NAVIGATION_EFFECT:
                            Log.d(TAG, "语音服务: " + operation + "屏幕导航光效开关");
                            displayControl.setScreenNavigationLight(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SWITCH_CROSS_SCREEN_CONTENT:
                            Log.d(TAG, "语音服务: " + operation + "跨屏具体内容切换");
                            displayControl.setScreenCheck();
                            break;
                        case VDVRFunctionID.Setting.SETTING_MOVE_CSCREEN_CONTENT:
                            Log.d(TAG, "语音服务: " + operation + "跨屏具体内容移动");
                            displayControl.setScreenCheckMove(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CHANGE_CSCREEN_CONTENT:
                            Log.d(TAG, "语音服务: " + operation + "跨屏具体内容交换");
                            displayControl.setScreenCheckExchange(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_MOVE_SCREEN_CONTENT:
                            Log.d(TAG, "语音服务: " + operation + "屏幕内容移动");
                            displayControl.screenValueMove(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SWITCH_SCREEN_CONTENT:
                            Log.d(TAG, "语音服务: " + operation + "屏幕内容切换");
                            displayControl.screenValueCheck(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_LITTLE_SCREEN_CONTENT:
                            Log.d(TAG, "语音服务: " + operation + "屏幕内容换成指定个数");
                            displayControl.screenValueNumber(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SWITCH_THEME:
                            Log.d(TAG, "语音服务: " + operation + "切换主题，无指定值");
                            displayControl.switchThemes();
                            break;
                        case VDVRFunctionID.Setting.SETTING_TOPIC_CATEGORY:
                            Log.d(TAG, "语音服务: " + operation + "主题类别开关");
                            displayControl.openThemeCategory(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_REMOVE_THEME:
                            Log.d(TAG, "语音服务: " + operation + "移除主题");
                            displayControl.removeTheme(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_DOWNLOAD_THEME:
                            Log.d(TAG, "语音服务: " + operation + "下载主题");
                            displayControl.downloadTheme(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_AUTO_THEME_SWITCH:
                            Log.d(TAG, "语音服务: " + operation + "主题自动切换功能开关");
                            displayControl.openThemeAutoSwitch(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SWITCH_CAMERA:
                            Log.d(TAG, "语音服务: " + operation + "切换摄像头，无指定值");
                            displayControl.setCamera(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_PAGE_STAR_DANCE:
                            Log.d(TAG, "语音服务: " + operation + "星动吧台界面开关");
                            displayControl.openStarDanceTable(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SYSTEM_DEBUG_MODE:
                            Log.d(TAG, "语音服务: " + operation + "系统调试模式开关");
                            displayControl.openSystemDebugMode(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_AUTO_TIMEZONE:
                            Log.d(TAG, "语音服务: " + operation + "自动时区开关");
                            displayControl.openAutoTimeZone(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_TIMEZONE:
                            Log.d(TAG, "语音服务: " + operation + "系统时区设置为指定区域");
                            displayControl.setSystemTimeZone(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_TEMP_UNIT:
                            Log.d(TAG, "语音服务: " + operation + "设置温度单位");
                            displayControl.setTemperatureUnit(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_PRESSURE_UNIT:
                            Log.d(TAG, "语音服务: " + operation + "设置压强单位");
                            displayControl.setPressureUnit(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_ELECTRICITY_UNIT:
                            Log.d(TAG, "语音服务: " + operation + "设置电耗单位");
                            displayControl.setEnergyUnit(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_POWER_UNIT:
                            Log.d(TAG, "语音服务: " + operation + "设置功率显示单位");
                            displayControl.setPowerUnit(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_SYSTEM_DISTANCE_UNIT:
                            Log.d(TAG, "语音服务: " + operation + "设置距离显示单位");
                            displayControl.setDistanceUnit(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_COUNTDOWN:
                            Log.d(TAG, "语音服务: " + operation + "倒计时设置");
                            displayControl.setCountDown(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CONTROL_COUNTDOWN:
                            Log.d(TAG, "语音服务: " + operation + "暂停、继续、删除倒计时");
                            displayControl.controlCountDown(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_SET_DEFAULT_APP:
                            Log.d(TAG, "语音服务: " + operation + "将指定业务的默认软件设置成指定app");
                            displayControl.setDefaultApp(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CONTROL_SCREEN_APP_ICON:
                            Log.d(TAG, "语音服务: " + operation + "屏幕应用图标缩放");
                            displayControl.setScreenAppZoom(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_PAGE_POP_UP:
                            Log.d(TAG, "语音服务: " + operation + "弹窗开关");
                            displayControl.openSystemAlert(flag);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_UP_BUTTON_LIGHT:
                            Log.d(TAG, "语音服务: " + operation + "按键亮度调高具体数值");
                            displayControl.screenValueUp(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_DOWN_BUTTON_LIGHT:
                            Log.d(TAG, "语音服务: " + operation + "按键亮度调低具体数值");
                            displayControl.screenValueDown(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_INSTRUMENT_SHOW_TYPE:
                            Log.d(TAG, "语音服务: " + operation + "仪表电量显示类型设置为xxx");
                            displayControl.screenValueRegulate(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_CONTROL_MUTE:
                            Log.d(TAG, "语音服务: " + operation + "设置静音");
                            voicecontrol.setMute(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_QUERY_CUR_VOLUME:
                            Log.d(TAG, "语音服务: " + operation + "查询音量");
                            voicecontrol.getVolume(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_VOLUME:
                            Log.d(TAG, "语音服务: " + operation + "调到数值");
                            voicecontrol.setVolume(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_SOUND_SOURCE:
                            Log.d(TAG, "语音服务: " + operation + "百分比调节音源音量");
                            voicecontrol.setVolumePrint(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_VOLUME_LITTLE:
                            Log.d(TAG, "语音服务: " + operation + "音量调大一点");
                            voicecontrol.setVolumeUpLit(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_VOLUME_UP:
                            Log.d(TAG, "语音服务: " + operation + "音量调大具体数值");
                            voicecontrol.setVolumeUp(value);
                            break;
                        case VDVRFunctionID.Setting.SETTING_ADJUST_VOLUME_DOWN:
                            Log.d(TAG, "语音服务: " + operation + "音量调小具体数值");
                            voicecontrol.setVolumeDown(value);
                            break;
                        //打开/关闭蓝牙无缝切换
                        case VDVRFunctionID.Setting.SETTING_CHANGE_BLUETOOTH:
                            voicecontrol.setChangeBluetooth(flag);
                            break;
                        //切换蓝牙连接设备，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_BT_DEVICE:
                            voicecontrol.setSwitchBtDevice();
                            break;
                        //查看蓝牙设备信息
                        case VDVRFunctionID.Setting.SETTING_VIEW_BT_DEVICE_INFO:
                            voicecontrol.setBtDeviceInfo();
                            break;
                        //打开/关闭蓝牙自动连接功能
                        case VDVRFunctionID.Setting.SETTING_AUTOLINK_BLUETOOTH:
                            voicecontrol.setAutolinkBluetooth(flag);
                            break;
                        //wifi密码设置为可见
                        case VDVRFunctionID.Setting.SETTING_VISIBILITY_WIFI_PASSWORD:
                            voicecontrol.setVisiblityWifiPassword(flag);
                            break;
                        //切换wifi连接，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_WIFI_LINK:
                            voicecontrol.setSwitchWifiLink();
                            break;
                        //设置WiFi连接频段
                        case VDVRFunctionID.Setting.SETTING_SET_WIFI_FREQUENCY:
                            voicecontrol.setWifiFrequency(value);
                            break;
                        //切换WiFi连接频段，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_WIFI_FREQUENCY:
                            voicecontrol.setSwitchWifiFrequency();
                            break;
                        //热点密码设置为不可见
                        case VDVRFunctionID.Setting.SETTING_VISIBILITY_HOTSPOT_PASSWORD:
                            voicecontrol.setVisiblityHotspotPassword(flag);
                            break;
                        //设置热点频段
                        case VDVRFunctionID.Setting.SETTING_SET_HOTSPOT_FREQUENCY:
                            voicecontrol.setHotspotFrequency(value);
                            break;
                        //切换热点频段，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_HOTSPOT_FREQUENCY:
                            voicecontrol.setSwitchHotspotFrequency();
                            break;
                        //还原网络设置
                        case VDVRFunctionID.Setting.SETTING_RESTORE_NETWORK_SETTING:
                            voicecontrol.setRestoreNetworkSetting();
                            break;
                        //切换声音模式，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_VOLUME_MODE:
                            voicecontrol.setSwitchVolumeMode();
                            break;
                        //打开/关闭主动声浪
                        case VDVRFunctionID.Setting.SETTING_DRIVING_SOUND_WAVE:
                            voicecontrol.setDrivingSoundWave(flag);
                            break;
                        //主动声浪设置为指定模式
                        case VDVRFunctionID.Setting.SETTING_SET_MODE_DRIVING_WAVE:
                            voicecontrol.setSetModeDrivingWave(value);
                            break;
                        //切换主动声浪模式，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_DRIVING_WAVE:
                            voicecontrol.setSwitchDrivingWave();
                            break;
                        //主动声浪音量调高/调低/调最高/调最低
                        case VDVRFunctionID.Setting.SETTING_ADJUST_DRIVING_WAVE:
                            voicecontrol.setAdjustDrivingWave(value);
                            break;
                        //打开/关闭震动反馈
                        case VDVRFunctionID.Setting.SETTING_VIBRATION_FEEDBACK:
                            voicecontrol.setVibrationFeedback(flag);
                            break;
                        //打开/关闭声音渐入渐出功能
                        case VDVRFunctionID.Setting.SETTING_SOUND_FADE:
                            voicecontrol.setSoundFade(flag);
                            break;
                        //打开/关闭音量平衡
                        case VDVRFunctionID.Setting.SETTING_VOLUME_BALANCE:
                            voicecontrol.setVolumeBalance(flag);
                            break;
                        //打开/关闭等响度
                        case VDVRFunctionID.Setting.SETTING_LOUDNESS:
                            voicecontrol.setLoudness(flag);
                            break;
                        //打开/关闭车内声音交流补偿
                        case VDVRFunctionID.Setting.SETTING_SOUND_REPAIR:
                            voicecontrol.setSoundRepair(flag);
                            break;
                        //打开/关闭低品质音源修复
                        case VDVRFunctionID.Setting.SETTING_AUDIO_REPAIR:
                            voicecontrol.setAudioRepair(flag);
                            break;
                        //打开最佳听音位为指定位置/关闭最佳听音位
                        case VDVRFunctionID.Setting.SETTING_BEST_LISTEN_POSITION:
                            voicecontrol.setBestListenPosition(flag);
                            break;
                        //切换最佳听音位，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_BEST_LISTEN_POSITION:
                            voicecontrol.setSwitchBestListenPosition();
                            break;
                        //打开/关闭自动降音功能
                        case VDVRFunctionID.Setting.SETTING_AUTOMATIC_REDUCTION:
                            voicecontrol.setAutomaticReduction(flag);
                            break;
                        //打开/关闭均衡器
                        case VDVRFunctionID.Setting.SETTING_EQUALIZER:
                            voicecontrol.setEqualizer(flag);
                            break;
                        //设置均衡器预设EQ为指定音效,关闭均衡器预设EQ
                        case VDVRFunctionID.Setting.SETTING_SET_PRESET_EQ:
                            voicecontrol.setSetPresetEq(value);
                            break;
                        //切换均衡器预设EQ，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_PRESET_EQ:
                            voicecontrol.setSwitchPresetEq();
                            break;
                        //开启/关闭主动降噪
                        case VDVRFunctionID.Setting.SETTING_NOISE_SUPPRESSION:
                            voicecontrol.setNoiseSuppression(flag);
                            break;
                        //屏幕位置移动
                        case VDVRFunctionID.Setting.SETTING_SCREEN_MOVE:
                            voicecontrol.setScreenMove(value);
                            break;
                        //旋转屏幕，无指定值
                        case VDVRFunctionID.Setting.SETTING_ROTATE_SCREEN:
                            voicecontrol.setRotateScreen();
                            break;
                        //向指定方向旋转屏幕
                        case VDVRFunctionID.Setting.SETTING_SCREEN_ROTATE:
                            voicecontrol.setScreenRotate(value);
                            break;
                        //设置屏幕显示模式
                        case VDVRFunctionID.Setting.SETTING_SET_MODE_SCREEN_DISPLAY:
                            voicecontrol.setSetModeScreenDisplay(value);
                            break;
                        //关闭屏幕显示模式
                        case VDVRFunctionID.Setting.SYSTEM_UI_SCREEN_SHOW:
                            voicecontrol.setSystemUiScreenShow(flag);
                            break;
                        //设置/关闭屏幕色彩模式
                        case VDVRFunctionID.Setting.SETTING_SET_MODE_SCREEN_COLOR:
                            voicecontrol.setSetModeScreenColor(value);
                            break;
                        //切换屏幕色彩模式，无指定值
                        case VDVRFunctionID.Setting.SETTING_SWITCH_SCREEN_COLOR_MODE:
                            voicecontrol.setSwitchScreenColorMode();
                            break;
                        //锁屏/解锁屏幕
                        case VDVRFunctionID.Setting.SETTING_CONTROL_SCREEN:
                            voicecontrol.setControlScreen(flag);
                            break;
                        //打开/关闭桌面歌词
                        case VDVRFunctionID.Setting.SETTING_PASSENGER_DESKTOP_LYRICS:
                            voicecontrol.setPassengerDesktopLyrics(flag);
                            break;
//                      声音设置界面
                        case VDVRFunctionID.Setting.SETTING_PAGE_SOUND:
                            activityControl.setVoiceSettingUI(flag, position);
                            break;
//                            打开隐私设置界面
                        case VDVRFunctionID.Setting.SETTING_PAGE_PRIVACY:
                            activityControl.setPrivacySettingsUI(flag);
                            break;
                        default:
                            Log.d(TAG, "VDEventVR.VR_SETTING--未匹配functionId: " + functionId);
                            break;
                    }
                    break;
                case VDEventVR.VR_VEHICLE_CONTROL:
                    // 车辆控制
                    switch (functionId) {
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CONTROL_WINDOW:
                            Log.d(TAG, "设置车窗" + flag + position);
                            carWindowControl.setWindow(flag, position, user);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LOCKCAR_LIFTWINDOW:
                            Log.d(TAG, "VEHICLE_LOCKCAR_LIFTWINDOW:" + flag);
                            carWindowControl.setLockAutoRaiseWindow(flag);
                            break;
                        //车窗锁
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LITTLE_WINDOW:
                            Log.d(TAG, "VEHICLE_ADJUST_LITTLE_WINDOW:" + flag + position);
                            carWindowControl.setAdjustLittleWindow(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_DEGREE_WINDOW:
                            Log.d(TAG, "VEHICLE_OPEN_DEGREE_WINDOW:" + value + position);
                            carWindowControl.setOpenDegreeWindow(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CLOSE_DEGREE_WINDOW:
                            Log.d(TAG, "VEHICLE_OPEN_DEGREE_WINDOW:" + value + position);
                            carWindowControl.setCloseDegreeWindow(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_WINDOW_VENTILATE:
                            Log.d(TAG, "VEHICLE_WINDOW_VENTILATE:" + flag + position);
                            carWindowControl.setWindowVentilate(flag, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_WINDOW_RISER:
                            Log.d(TAG, "VEHICLE_WINDOW_RISER:" + flag + position);
                            carWindowControl.setWindowRiser(flag, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_RISER_SYSTEM:
                            Log.d(TAG, "VEHICLE_RISER_SYSTEM:" + flag + position);
                            carWindowControl.setRiserSystem(flag);
                            break;
                        //车窗锁
                        case VDVRFunctionID.VehicleSetting.VEHICLE_WINDOW_LOCK:
                            Log.d(TAG, "VEHICLE_WINDOW_LOCK:" + flag);
                            carWindowControl.setWindowLock(flag);
                            break;
//                            氛围灯xx位置开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ATMOSPHERE_LIGHT:
                            lightControl.setdfLight(flag, position);
                            break;
//                            氛围灯区域控制
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AMBIENT_LIGHT_ZONE_CONTROL:
                            lightControl.setAmbientLightZoneControl(flag);
                            break;
//                            氛围灯分位置控制
//                        case VDVRFunctionID.VehicleSetting.VEHICLE_ATMOSPHERE_LIGHT:
//                            lightControl.setdflight(flag,position);
//                            break;
//                            切换氛围灯效果
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AMBIENT_LIGHT_EFFECT:
                            lightControl.setInLightEffect(value);
                            break;
//                            音乐律动开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ATMOSPHERE_LIGHT_WITH_RHYTHM:
                            lightControl.setMusicLightControl(flag);
                            break;
//                            动态氛围灯律动模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DYNAMIC_ATMOSPHERE_MODE:
                            lightControl.setDynamicInLight(value);
                            break;
//                            无指定模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_AMBIENT_LIGHT_MODE:
                            lightControl.setNullMode();
                            break;
//                            动态氛围灯
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DYNAMIC_ATMOSPHERE_LIGHT:
                            lightControl.setDyInLight(flag);
                            break;
//                            改变指定颜色
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_ATMOSPHERE_COLOR:
                            lightControl.setcolorInLight(value);
                            break;
//                            随机氛围灯颜色
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_AMBIENT_LIGHT_COLOR:
                            lightControl.setRandomColor();
                            break;
//                            主题颜色颜色
                        case VDVRFunctionID.VehicleSetting.VEHICLE_THEME_AMBIENT_LIGHT_RANDOM:
                            lightControl.setNodesignationColor();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_THEME_AMBIENT_LIGHT:
                            lightControl.setThemeInLight(value);
                            break;
//                        切换无指定颜色
                        case VDVRFunctionID.VehicleSetting.VEHICLE_RANDOM_AMBIENT_LIGHT_COLOR:
                            lightControl.setNodesignationColor2(value);
                            break;
//                            氛围灯自动亮度模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AMBIENT_LIGHT_AUTO_BRIGHTNESS:
                            lightControl.setAmbientLightAutoBrightnessMode(flag);
                            break;
//                        氛围灯切换为指定模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AMBIENT_SWITCH_MODE:
                            lightControl.themeInLight(value);
                            break;
//                            调节亮度最大、最小
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_LIGHT_ATMOSPHERE:
                            lightControl.setBrightMax_Min(value);
                            break;
//                            百分比调节亮度
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AMBIENT_LIGHT_ADJUST_PERCENT:
                            lightControl.setBrightPoint(value, position);
                            break;
//                            调高百分比亮度
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AMBIENT_LIGHT_PLUS_PERCENT:
                            lightControl.setPlusBrightness(value);
                            break;
//                            调低百分比亮度
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AMBIENT_LIGHT_MINUS_PERCENT:
                            lightControl.setMinusBrightness(value);
                            break;
//                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_MUL_ATMOSPHERE_COLOR:
//                            Log.d(TAG, "氛围灯: " + operation + "双色调节");
//                            lightControl.setBiColor(value);
//                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AUTO_TOP_LIGHT:
                            Log.d(TAG, "VEHICLE_AUTO_TOP_LIGHT:" + flag);
                            vehicleControl.autoTopLightControl(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_READ_FOG_LIGHT:
                            Log.d(TAG, "VEHICLE_READ_FOG_LIGHT:" + flag);
                            vehicleControl.readFogLightControl(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INTELLIGENT_WELCOME:
                            Log.d(TAG, "VEHICLE_INTELLIGENT_WELCOME:" + flag);
                            vehicleControl.setIntelligentWelcome(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_APPROACH_WELCOME:
                            Log.d(TAG, "VEHICLE_APPROACH_WELCOME:" + flag);
                            vehicleControl.approachWelcomeControl(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INTELLIGENT_BEAM_LIGHT:
                            Log.d(TAG, "VEHICLE_INTELLIGENT_BEAM_LIGHT:" + flag);
                            vehicleControl.setIntelligentBeamLight(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_EXTERIOR_ANGLE:
                            Log.d(TAG, "VEHICLE_ADJUST_MODE_EXTERIOR_ANGLE:" + value);
                            vehicleControl.adjustModeExteriorAngle(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_EXTERIOR_ANGLE:
                            Log.d(TAG, "VEHICLE_ADJUST_UP_EXTERIOR_ANGLE:" + value);
                            vehicleControl.adjustUpExteriorAngle(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_EXTERIOR_ANGLE:
                            Log.d(TAG, "VEHICLE_ADJUST_DOWN_EXTERIOR_ANGLE:" + value);
                            vehicleControl.adjustDownExteriorAngle(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_EXTERIOR_ANGLE:
                            Log.d(TAG, "VEHICLE_ADJUST_EXTERIOR_ANGLE:" + value);
                            vehicleControl.adjustExteriorAngle(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_INSTRUMENT_THEME:
                            Log.d(TAG, "VEHICLE_SET_INSTRUMENT_THEME:" + value);
                            vehicleControl.setInstrumentTheme(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_LOCK_MODE:
                            Log.d(TAG, "VEHICLE_OPEN_LOCK_MODE:" + value);
                            vehicleControl.openLockModeControl(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CLOSE_LOCK_MODE:
                            Log.d(TAG, "VEHICLE_LOCK_MODE:" + value);
                            vehicleControl.lockModeControl(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CAR_LOCK:
//                            TODO
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHILD_LOCK:
                            Log.d(TAG, "VEHICLE_CHILD_LOCK:" + isEnable + ":" + position);
                            vehicleControl.childLockControl(isEnable, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_UNLOCK_MODE:
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SENSOR_UNLOCK:
                            Log.d(TAG, "VEHICLE_SENSOR_UNLOCK:" + isEnable);
                            vehicleControl.sensorUnlockControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INDUCTION_LOCK:
                            Log.d(TAG, "VEHICLE_INDUCTION_LOCK:" + isEnable);
                            vehicleControl.inductionLockControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CENTRAL_LOCK:
                            Log.d(TAG, "VEHICLE_CENTRAL_LOCK:" + isEnable);
                            vehicleControl.centralLockControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DRIVING_LOCK:

                            Log.d(TAG, "VEHICLE_DRIVING_LOCK:" + isEnable);
                            vehicleControl.drivingLockControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DRIVE_CONVENIENT_ENTRY_EXIT:
                        case VDVRFunctionID.VehicleSetting.VEHICLE_COMFORT_ENTRY_EXIT:
                            Log.d(TAG, "VEHICLE_DRIVE_CONVENIENT_ENTRY_EXIT:" + isEnable);
                            vehicleControl.driveConvenientEntryExitControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_COMFORT_BRAKE:
                            Log.d(TAG, "VEHICLE_COMFORT_BRAKE:" + isEnable);
                            vehicleControl.comfortBrakeControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_COMFORT_BRAKE_MODE:
                            Log.d(TAG, "VEHICLE_SET_COMFORT_BRAKE_MODE:" + value);
                            vehicleControl.setComfortBrakeMode(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CAR_PEDAL_ASSIST_MODE:
                            Log.d(TAG, "VEHICLE_CAR_PEDAL_ASSIST_MODE:" + isEnable);
                            vehicleControl.carPedalAssistModeControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_CAR_PEDAL_ASSIST_MODE:
                            Log.d(TAG, "VEHICLE_SET_CAR_PEDAL_ASSIST_MODE:" + value);
                            vehicleControl.setCarPedalAssistMode(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_PEDAL_ASSIST_SENSITIVITY:
                            Log.d(TAG, "VEHICLE_ADJUST_MODE_PEDAL_ASSIST_SENSITIVITY:" + value);
                            vehicleControl.setCarPedalAssistSensitivity(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_CAR_PEDAL_ASSIST_MODE:
                            Log.d(TAG, "VEHICLE_CHANGE_CAR_PEDAL_ASSIST_MODE:");
                            vehicleControl.changeCarPedalAssistModeControl();
                            break;
                        // 打开or关闭雨刮维修
                        case VDVRFunctionID.VehicleSetting.VEHICLE_WIPER_REPAIR:
                            vehicleControl.setWipeRepair(flag);
                            break;
                        //雨刷灵敏度调高/低/最高/最低/调到高中低挡
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_WIPER_SENSITIVITY:
                            vehicleControl.setWiperSensMode(value);
                            break;
                        //雨刷灵敏度调高具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_WIPER_SENSITIVITY:
                            vehicleControl.setWiperSensUp(value);
                            break;

                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_STEERING_MODE:
                            Log.d(TAG, "VEHICLE_SET_STEERING_MODE:" + value);
                            vehicleControl.setSteeringMode();
                            break;
                        //雨刷灵敏度调低具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_WIPER_SENSITIVITY:
                            Log.d(TAG, "VEHICLE_ADJUST_DOWN_WIPER_SENSITIVITY:" + value);
                            vehicleControl.setWiperSensDown(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ESC:
                            Log.d(TAG, "VEHICLE_ESC:" + isEnable);
                            vehicleControl.escControl(isEnable);
                            break;
                        //雨刮灵敏度调到指定数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_WIPER_SENSITIVITY:
                            vehicleControl.setWiperSens(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DRIVING_ENTERTAINMENT_LIMIT:
                        case VDVRFunctionID.VehicleSetting.VEHICLE_VIDEO_LIMIT:
                            Log.d(TAG, "VEHICLE_DRIVING_ENTERTAINMENT_LIMIT:" + isEnable);
                            vehicleControl.videoLimitControl(isEnable);
//                        case VDVRFunctionID.VehicleSetting.VEHICLE_VIDEO_LIMIT:
//                            Log.d(TAG, "VEHICLE_DRIVING_ENTERTAINMENT_LIMIT:" + isEnable);
//                            vehicleControl.videoLimitControl(isEnable);
                            //方向盘自定义按键设置为具体功能
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STEERING_CUSTOM_KEY_SETTING:
                            vehicleControl.setCustomButtons(value);
                            break;

                        //方向盘按键短按自定义设置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STEERING_CUSTOM_KEY_SHORT_PRESS_SETTING:
                            vehicleControl.setCustomButtonShortPress(value);
                            break;
                        //方向盘按键长按自定义设置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STEERING_CUSTOM_KEY_LONG_PRESS_SETTING:
                            vehicleControl.setCustomButtonLongPress(value);
                            break;
                        //打开安全气囊
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SAFETY_BUCKLE:
                            vehicleControl.setAirBag(flag);
                            break;
                        //打开/关闭便捷控制界面
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_CONVENIENT_CONTROL:
                            activityControl.setConvenientControl(flag);
                            break;
                        //打开/关闭驾驶设置界面
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_DRIVER:
                            activityControl.setDrivingSetting(flag);
                            break;
                        //后视镜往指定方向调节一点
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DIRECTION_REAR_MIRROR:
                            vehicleControl.setMirrorAdjustment(value);
                            break;
                        //后视镜方向调到指定方向极值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MOST_DIRECTION_REAR_MIRROR:
                            vehicleControl.setMirrorAdjustmentMost(value);
                            break;
                        //流媒体后视镜亮度调节
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LIGHT_STREAM_REAR_MIRROR:
                            vehicleControl.setMirrorBrightness(value);
                            break;
                        //打开/关闭喷嘴加热
                        case VDVRFunctionID.VehicleSetting.VEHICLE_NOZZLE_HEATING:
                            vehicleControl.setNozzleHeating(flag);
                            break;
                        //设置喷嘴加热模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_MODE_NOZZLE_HEATING:
                            vehicleControl.setNozzleHeatingMode();
                            break;
                        //打开/关闭雨刮器
                        case VDVRFunctionID.VehicleSetting.VEHICLE_WIPER:
                            vehicleControl.setWiper(flag);
                            break;
                        //雨刮器挡位调高具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_WIPER_GEAR:
                            vehicleControl.setWiperUp();
                            break;
                        //雨刮器挡位调低具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_WIPER_GEAR:
                            vehicleControl.setWiperDown();
                            break;
                        //雨刮器挡位调高/低一点/高中低/最大/最小
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_WIPER_GEAR:
                            vehicleControl.setWiperMode(value);
                            break;
                        //雨刮器挡位调到具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_WIPER_GEAR:
                            vehicleControl.setAdjustWiperGear();
                            break;
                        //打开/关闭手套箱
                        case VDVRFunctionID.VehicleSetting.VEHICLE_GLOVE_BOX:
                            vehicleControl.GloveBox(flag);
                            break;
                        //打开/关闭手套箱密码
                        case VDVRFunctionID.VehicleSetting.VEHICLE_GLOVE_BOX_PASSWORD:
                            vehicleControl.GloveBoxPassword(flag);
                            break;
                        //设置手套箱密码
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_GLOVES_BOX_PASSWORD:
                            vehicleControl.setGloveBoxPassword();
                            break;
                        //打开/关闭方向盘位置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STEERING_WHEEL_POSITION:
                            vehicleControl.setSteeringWheelPosition(flag);
                            break;
                        //打开/关闭方向盘未回正提醒
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STEERING_WHEEL_REMIND:
                            vehicleControl.setSteeringWheelRemind(flag);
                            break;
                        //方向盘向指定方向调节
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DIRECTION_STEERING_WHEEL:
                            vehicleControl.setSteeringWheelAdjust();
                            break;
                        //方向盘向指定方向调节到极值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MOST_DIRECTION_STEERING_WHEEL:
                            vehicleControl.setSteeringWheelAdjustMost();
                            break;
                        //保存方向盘位置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SAVE_POSITION_STEERING_WHEEL:
                            vehicleControl.saveSteeringWheelPosition();
                            break;
                        //打开方向盘自动回位
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STEERING_WHEEL_AUTO_POSITION:
                            vehicleControl.setSteeringWheelAutoPosition(flag);
                            break;
                        //设置方向盘加热具体模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_MODE_DIRECTION_STEERING_HEATING:
                            vehicleControl.setSteeringWheelHeatingMode();
                            break;
                        //打开/关闭方向盘通风
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STEERING_WHEEL_VENTILATE:
                            vehicleControl.setSteeringWheelVentilate(flag);
                            break;
                        //打开方向盘调节HUD
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STEERING_WHEEL_ADJUST_HUD:
                            vehicleControl.setWheelAdjustHUD(flag);
                            break;
                        //打开/关闭主驾安全带舒适
                        case VDVRFunctionID.VehicleSetting.VEHICLE_COMFORT_BUCKLE:
                            vehicleControl.setComfortBuckle();
                            break;
                        //打开/关闭主驾安全带震动提醒
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DRIVER_BUCKLE_VIBRATION:
                            vehicleControl.setDriverBuckleVibration(flag);
                            break;
                        //打开/关闭安全带加热
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SEAT_BELT_HEATING:
                            vehicleControl.setBuckleHeating(flag);
                            break;
                        //安全带加热温度设置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_SEAT_HEATING:
                            vehicleControl.setSeatHeating();
                            break;
                        //切换hud显示模式，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_HUD_DISPLAY:
                            vehicleControl.changeHUDDisplay();
                            break;
                        //打开hud显示具体内容
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HUD_SHOW_DRIVER_MODE:
                            vehicleControl.setHUDShowDriverMode();
                            break;
                        //关闭hud显示具体内容
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HUD_MISS_DRIVER_MODE:
                            vehicleControl.setHUDMissDriverMode();
                            break;
                        //切换hud显示具体内容，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_HUD_CONTENT:
                            vehicleControl.changeHUDContent();
                            break;
                        //打开HUD雪地模式关联驾驶模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HUD_SNOW_MODE_WITH_DRIVE:
                            vehicleControl.setHUDSnowModeWithDrive(flag);
                            break;
                        //打开/关闭HUD雪地模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HUD_SNOW_MODE:
                            vehicleControl.setHUDSnowMode(flag);
                            break;
                        //油箱盖解锁
                        case VDVRFunctionID.VehicleSetting.VEHICLE_FUEL_TANK_LOCK:
                            vehicleControl.setOilTankUnlock(flag);
                            break;
//                        case VDVRFunctionID.VehicleSetting.VEHICLE_WIRELESS_CHARGE:
//                            Log.d(TAG, "VEHICLE_WIRELESS_CHARGE:" + isEnable);
//                            vehicleControl.wirelessChargeControl(isEnable);
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CONTROL_TRUNK:
                            //开关后尾门
                            vehicleControl.setAutoTail(flag);
                            break;
//                        case VDVRFunctionID.VehicleSetting.VEHICLE_TRAILER_MODE:
//                            Log.d(TAG, "VEHICLE_TRAILER_MODE:" + isEnable);
//                            vehicleControl.trailerModeControl(isEnable);
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_HEIGHT_TAILGATE_PERCENT:
                            //设置尾门开启上限高度百分比
                            vehicleControl.setTailHeight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_FUEL_TANK:
                            //打开油箱盖
                            vehicleControl.setFuelTankCap(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_FOLD_EXTERIOR_REAR_MIRROR:
                            //外后视镜自动折叠开关
                            vehicleControl.setAutoRearviewMirrorsFolded(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HEATING_REAR_MIRROR:
                            //加热后视镜
                            vehicleControl.setHeatedMirrors(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_RAINAUTO_HEATING_REAR_MIRROR:
                            //雨天后视镜自动加热
                            vehicleControl.setAutoHeatedRearviewMirror(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REAR_MIRROR:
                            //后视镜折叠展开
                            vehicleControl.setRearviewMirrorsFolded(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REVERSE_VEHICLE_MIRROR_AUTO_DOWN_FLIP:
                            //倒车后视镜自动调节
                            vehicleControl.setRearviewMirrorAutomaticallyAdjusted(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_STOP_SLID_SUNSHADE_CURTAIN:
                            //暂停遮阳帘
                            vehicleControl.setSunShadeStopped();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AVM_DOWN_REAR_MIRROR:
                            //倒车外后视镜自动调节设置为xxx
                            vehicleControl.setRearviewMirrorAutomaticallyAdjusted(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SUNSHADE_CURTAIN:
                            //遮阳帘开关
                            vehicleControl.setSunshadeSwitch(flag, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_DEGREE_SUNSHADE_CURTAIN:
                            //遮阳帘打开到具体数值
                            vehicleControl.setSunshadeSwitchValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CLOSE_DEGREE_SUNSHADE_CURTAIN:
                            //遮阳帘关闭到具体数值
                            vehicleControl.setSunshadeCloseValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LITTLE_SUNSHADE_CURTAIN:
                            //遮阳帘打开关闭一点
                            vehicleControl.setSunshadeSwitchLittle(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ABNORMAL_SUNSHADE_CURTAIN:
                            //遮阳帘异常
                            vehicleControl.sunshadeAbnormal();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LOCK_CAR_RETRACT_SUNSHADE:
                            //锁车收起遮阳帘开关
                            vehicleControl.lockCarSunRoofShadeClose(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.SETTING_WASH_CAR_DISPLAY:
//                            //洗车模式界面开关
                            vehicleControl.carWashMode(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_WASH_CAR_MODE:
                            //洗车模式设置指定值
                            vehicleControl.setCarWashMode(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CONTROL_WASH_CAR:
                            //洗车模式开关
                            vehicleControl.washCarMode(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SAVE_POSITION_REAR_MIRROR:
                            //保存后视镜位置
                            vehicleControl.saveMirrorPosition();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_POSITION_REAR_MIRROR:
                            //打开后视镜位置
                            vehicleControl.openMirrorPosition(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_DRIVER_MODE:
                            Log.d(TAG, "VEHICLE_PAGE_DRIVER_MODE:" + "打开驾驶模式设置界面");
                            activityControl.openDriveModeSetting(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_DRIVER_CUSTOM:
                            Log.d(TAG, "VEHICLE_PAGE_DRIVER_CUSTOM:" + "打开驾驶模式个性化设置界面");
                            activityControl.openThemePersonalizationSetting(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_WHEEL_ADJUST:
                            Log.d(TAG, "VEHICLE_PAGE_WHEEL_ADJUST:" + "打开方向盘调节界面");
                            activityControl.openSteeringWheelAdjustment(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_CUSTOM_WHEEL:
                            Log.d(TAG, "VEHICLE_PAGE_CUSTOM_WHEEL:" + "打开自定义方向盘界面");
                            activityControl.openCustomSteeringWheel(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_REAR_VIEW_ADJUST:
                            Log.d(TAG, "VEHICLE_PAGE_REAR_VIEW_ADJUST:" + "打开后视镜调节界面");
                            activityControl.openMirrorAdjustment(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_ELECT_PASSWORD:
                            Log.d(TAG, "VEHICLE_PAGE_ELECT_PASSWORD:" + "打开雨刮灵敏度调节界面");
                            activityControl.openInLightSensitivityAdjustment(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_DOOR_CONTROL:
                            Log.d(TAG, "语音服务: " + operation + " 打开车门控制界面");
                            displayControl.openDoorControl(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_CAR_LIGHT:
                            Log.d(TAG, "VEHICLE_PAGE_CAR_LIGHT:" + "打开车灯设置界面");
                            activityControl.lightingSetting(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_SUSPENSION:
                            Log.d(TAG, "VEHICLE_PAGE_SUSPENSION:" + "打开悬架设置界面");
                            displayControl.openSuspensionControl(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_DIGITAL_INTERACTION_LIGHT:
                            Log.d(TAG, "VEHICLE_PAGE_DIGITAL_INTERACTION_LIGHT:" + "打开数字交互灯界面");
                            displayControl.digitSignalLightControl(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_DOOR_WARNING:
                            Log.d(TAG, "VEHICLE_PAGE_DOOR_WARNING:" + "打开开门预警界面");
                            displayControl.openDoorWarning(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_ACCELERATOR_PRESSURE_WARNING:
                            Log.d(TAG, "VEHICLE_PAGE_ACCELERATOR_PRESSURE_WARNING:" + "打开加速踏板防误踩界面");
                            displayControl.openAcceleratorPedalAnti(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_EMERGE_TURN_ASSISTANCE:
                            Log.d(TAG, "VEHICLE_PAGE_EMERGE_TURN_ASSISTANCE:" + "打开紧急转向辅助界面");
                            displayControl.openEmergencyTurnAssist(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_SPEED_OFFSET:
                            Log.d(TAG, "VEHICLE_PAGE_SPEED_OFFSET:" + "打开限速偏移界面");
                            displayControl.openSpeedOffset(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SWITCH_ELECT_PASSWORD:
                            Log.d(TAG, "VEHICLE_SWITCH_ELECT_PASSWORD:" + "打开电动手套箱密码软开关");
                            displayControl.openElectricGloveBoxPasswordSoftSwitch(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_SECURITY:
                            Log.d(TAG, "VEHICLE_PAGE_SECURITY:" + "打开安全设置界面");
                            displayControl.openSecuritySettings(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_ELECT_COLUMN_ADJUST:
                            Log.d(TAG, "VEHICLE_PAGE_ELECT_COLUMN_ADJUST:" + "电动管柱调节界面");
                            displayControl.electricColumn(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_SMART_KEY:
                            Log.d(TAG, "VEHICLE_PAGE_SMART_KEY:" + "打开智能钥匙界面");
                            displayControl.openIntelligentKey(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_COMFORT:
                            Log.d(TAG, "VEHICLE_PAGE_COMFORT:" + "打开环境舒适界面");
                            displayControl.openEnvironmentComfort(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_VEHICLE_STATUS:
                            Log.d(TAG, "VEHICLE_PAGE_VEHICLE_STATUS:" + "打开车辆状态显示界面");
                            displayControl.openVehicleStatusDisplay(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_EMERGENCY_DISCHARGE:
                            Log.d(TAG, "VEHICLE_EMERGENCY_DISCHARGE:" + "打开应急放电");
                            newEnergyControl.openEmergencyDischarge(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_EXTERNAL_VEHICLE_FAST_POWER_SUPPLY:
                            Log.d(TAG, "VEHICLE_EXTERNAL_VEHICLE_FAST_POWER_SUPPLY:" + "对外车辆快速供电开关");
                            newEnergyControl.externalPowerSupply(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_EXTERNAL_VEHICLE_SLOW_POWER_SUPPLY:
                            Log.d(TAG, "VEHICLE_EXTERNAL_VEHICLE_SLOW_POWER_SUPPLY:" + "对外车辆慢速供电开关");
                            newEnergyControl.externalDevicePowerSupply(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_NOW_CHARGE:
                            Log.d(TAG, "VEHICLE_NOW_CHARGE:" + "立即充电开关");
                            newEnergyControl.chargingSwitch(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_CHARGE_MODE:
                            Log.d(TAG, "VEHICLE_CHARGE_MODE:" + "充电模式开关");
                            newEnergyControl.setChargeMode(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_SET_CHARGE_WARMING_END_TIME:
                            Log.d(TAG, "VEHICLE_SET_CHARGE_WARMING_END_TIME:" + "设置充电保温结束时间");
                            newEnergyControl.reserveChargeTime(value);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_BATTERY_HEAT_REMINDER:
                            Log.d(TAG, "VEHICLE_BATTERY_HEAT_REMINDER:" + "电池温度预控加热提醒开关");
                            newEnergyControl.temperatureReminders(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_SMART_CHARGE_ON:
                            Log.d(TAG, "VEHICLE_SMART_CHARGE_ON:" + "智能上电开关");
                            newEnergyControl.smartPower(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_SMART_CHARGE_OFF:
                            Log.d(TAG, "VEHICLE_SMART_CHARGE_OFF:" + "智能下电开关");
                            newEnergyControl.smartDischarge(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_HUD_HEIGHT:
                            Log.d(TAG, "VEHICLE_ADJUST_MODE_HUD_HEIGHT:" + "调节hud高度");
                            vehicleControl.adjustHeightHud(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_HUD_HEIGHT:
                            Log.d(TAG, "VEHICLE_ADJUST_DOWN_HUD_HEIGHT:" + "hud高度调低具体数值");
                            vehicleControl.adjustDownLittleHeightHud(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_HUD_HEIGHT:
                            Log.d(TAG, "VEHICLE_WIRELESS_CHARGE:" + "hud高度调高具体数值");
                            vehicleControl.adjustUpHeightHud(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HUD_HIGH_AUTO_ADJUSTMENT:
                            Log.d(TAG, "VEHICLE_HUD_HIGH_AUTO_ADJUSTMENT:" + "hud高度自动调节");
                            vehicleControl.autoAdjustHeightHud(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_HUD_TILTANGLE:
                            Log.d(TAG, "VEHICLE_ADJUST_HUD_TILTANGLE:" + "hud倾斜度调到具体数值");
                            vehicleControl.adjustAngleHud(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LITTLE_HUD_TILTANGLE:
                            Log.d(TAG, "VEHICLE_ADJUST_LITTLE_HUD_TILTANGLE:" + "hud倾斜度向指定方向调节一点");
                            vehicleControl.adjustModeAngleHud(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MOST_HUD_TILTANGLE:
                            Log.d(TAG, "VEHICLE_ADJUST_MOST_HUD_TILTANGLE:" + "hud倾斜度往指定方向调到极值");
                            vehicleControl.adjustModeAngleHudToExtreme(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LITTLE_HUD_TRANSLATE:
                            Log.d(TAG, "VEHICLE_ADJUST_LITTLE_HUD_TRANSLATE:" + "hud位置横向调节");
                            vehicleControl.adjustPositionHud(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MOST_HUD_TRANSLATE:
                            Log.d(TAG, "VEHICLE_ADJUST_MOST_HUD_TRANSLATE:" + "hud位置向指定方向调节到极值");
                            vehicleControl.adjustPositionHudToExtreme(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_WIRELESS_CHARGE:
                            Log.d(TAG, "VEHICLE_WIRELESS_CHARGE:" + isEnable);
                            vehicleControl.wirelessChargeControl(isEnable, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TRAILER_MODE:
                            Log.d(TAG, "VEHICLE_TRAILER_MODE:" + isEnable);
                            vehicleControl.trailerModeControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HILL_DESCENT_SYSTEM:
                            Log.d(TAG, "VEHICLE_HILL_DESCENT_SYSTEM:" + isEnable);
                            vehicleControl.hillDescentSystemControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PHONE_FORGET_REMINDER:
                            Log.d(TAG, "VEHICLE_PHONE_FORGET_REMINDER:" + isEnable);
                            vehicleControl.phoneForgetReminderControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_ALARM_MODE:
                            Log.d(TAG, "VEHICLE_SET_ALARM_MODE:");
                            vehicleControl.setAlarmModeControl();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AUTO_PARKING:
                            Log.d(TAG, "VEHICLE_AUTO_PARKING:" + isEnable);
                            vehicleControl.autoParkingControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_MAINTENANCE_TIME_REMINDER:
                            Log.d(TAG, "VEHICLE_MAINTENANCE_TIME_REMINDER:" + isEnable);
                            vehicleControl.maintenanceTimeReminderControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HOLD_MILEAGE_RESET_DIALOG:
                            Log.d(TAG, "VEHICLE_HOLD_MILEAGE_RESET_DIALOG:" + isEnable);
                            vehicleControl.holdMileageResetDialogControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_ENERGY:
                            Log.d(TAG, "VEHICLE_ADJUST_ENERGY:" + value);
                            vehicleControl.setAdjustModeEnergy(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_ENERGY:
                            Log.d(TAG, "VEHICLE_ADJUST_MODE_ENERGY:" + value);
                            vehicleControl.adjustEnergyControl(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AUTO_PARKING_UNLOCK:
                            Log.d(TAG, "VEHICLE_AUTO_PARKING_UNLOCK:" + isEnable);
                            vehicleControl.autoParkingUnlockControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ULTIMATE_ELECTRIC:
                            vehicleControl.ultimateElectricControl(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_APPOINT_DRIVING_MODE:
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_POWER_MODE:
                            Log.d(TAG, "VEHICLE_SET_POWER_MODE:" + value);
                            vehicleControl.setDrivingMode(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_DRIVING_MODE:
                            Log.d(TAG, "VEHICLE_CHANGE_DRIVING_MODE:");
                            vehicleControl.changeDrivingMode();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_DRIVING_MODE:
                        case VDVRFunctionID.VehicleSetting.VEHICLE_APPOINT_POWER_MODE:
                            Log.d(TAG, "VEHICLE_SET_DRIVING_MODE:" + value);
                            vehicleControl.setCarMode(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SWITCH_RANDOM_CAR_MODE:
                            vehicleControl.switchRandomCarMode();
                            break;
//                        case VDVRFunctionID.VehicleSetting.VEHICLE_SWITCH_RANDOM_CAR_MODE:
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_POWER_MODE:
                            vehicleControl.switchRandomCarMode();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DMS:
                            Log.d(TAG, "VEHICLE_DMS:" + value);
                            vehicleControl.dmsControl(isEnable);
                            break;
                        //对外放电开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_EXTERNAL_POWER_SUPPLY:
                            newEnergyControl.setExternalPowerSupply(isEnable);
                            break;
                        //对外设备供电开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_EXTERNAL_DEVICE_POWER_SUPPLY:
                            newEnergyControl.setExternalDevicePowerSupply(isEnable);
                            break;
                        //对外车辆供电开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_EXTERNAL_VEHICLE_POWER_SUPPLY:
                            newEnergyControl.setExternalVehiclePowerSupply(isEnable);
                            break;
                        //驻车发电开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_HOLD_POWER:
                            newEnergyControl.setParkingPowerGeneration(isEnable);
                            break;
                        //预约充电开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_RESERVE_CHARGE:
                            newEnergyControl.setReserveCharge(isEnable);
                            break;
                        //预约充电时间时长周期循环
                        case VDVRFunctionID.NewEnergy.VEHICLE_RESERVE_CHARGE_TIME_DURATION:
                            newEnergyControl.setReserveChargeTime(value);
                            break;
                        //预约出行开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_RESERVATION_TRIP:
                            newEnergyControl.setReservationTrip(isEnable);
                            break;
                        //充电开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_CONTROL_CHARGE:
                            newEnergyControl.controlCharge(value);
                            break;
                        //调整充电上限值
                        case VDVRFunctionID.NewEnergy.VEHICLE_MAX_CHARGE:
                            newEnergyControl.setMaxCharge(value);
                            break;
                        //调整放电下限值
                        case VDVRFunctionID.NewEnergy.VEHICLE_MIN_CHARGE:
                            newEnergyControl.setMinCharge(value);
                            break;
                        //调整SOC目标值
                        case VDVRFunctionID.NewEnergy.VEHICLE_ADJUST_SOC:
                            newEnergyControl.adjustSOC(value);
                            break;
                        //打开/关闭能量流
                        case VDVRFunctionID.NewEnergy.VEHICLE_ENERGY_FLOW:
                            newEnergyControl.setEnergyFlowVisible(isEnable);
                            break;
                        //打开/关闭能量信息
                        case VDVRFunctionID.NewEnergy.VEHICLE_ENERGY_INFO:
                            newEnergyControl.setEnergyInfoVisible(isEnable);
                            break;
                        //SOC目标值调小具体数值
                        case VDVRFunctionID.NewEnergy.VEHICLE_ADJUST_DOWN_SOC:
                            newEnergyControl.adjustDownSOC(value);
                            break;
                        //SOC目标值调到最大/最小
                        case VDVRFunctionID.NewEnergy.VEHICLE_ADJUST_MODE_SOC:
                            newEnergyControl.adjustModeSOC(value);
                            break;
                        //SOC设置为指定模式
                        case VDVRFunctionID.NewEnergy.VEHICLE_SET_SOC_MODE:
                            newEnergyControl.setSOCMode(value);
                            break;
                        //充电曲线开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_CHARGE_CURVE:
                            newEnergyControl.setChargeCurve(isEnable);
                            break;
                        //能耗曲线开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_ENERGY_CURVE:
                            newEnergyControl.setEnergyCurve(isEnable);
                            break;
                        //电耗曲线开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_ELECTRICITY_CURVE:
                            newEnergyControl.setElectricityCurve(isEnable);
                            break;
                        //油耗曲线开关
                        case VDVRFunctionID.NewEnergy.VEHICLE_OIL_CURVE:
                            newEnergyControl.setOilCurve(isEnable);
                            break;
                        //安全驾驶模式开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SAFETY_DRIVING_MODE:
                            vehicleControl.setSafetyDrivingMode(isEnable);
                            break;
                        //安全驾驶提醒开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SAFETY_DRIVING_REMINDER:
                            vehicleControl.setSafetyDrivingReminder(isEnable);
                            break;
                        //行车锁屏开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DRIVING_LOCK_SCREEN:
                            vehicleControl.setDrivingLockScreen(isEnable);
                            break;
                        // READY开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_READY:
                            vehicleControl.setReady(isEnable);
                            break;
                        // 越野热管理开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_THERMAL_MODE:
                            vehicleControl.setThermalMode(isEnable);
                            break;
                        // 行车语音播报开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DRIVING_BROADCAST:
                            vehicleControl.setDrivingBroadcast(isEnable);
                            break;
                        // NFC开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_NFC:
                            vehicleControl.setNFC(isEnable);
                            break;
                        // 极致节能模式开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ULTRIMATE_SAVING_MODE:
                            vehicleControl.setUltimateSavingMode(isEnable);
                            break;
                        //设置车模颜色
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_CAR_COLOR:
                            vehicleControl.setCarColor(value);
                            break;
                        //后排检测开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REAR_SEAT_DETECTION:
                            vehicleControl.setRearSeatDetection(isEnable);
                            break;
                        //调光玻璃顶开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DIMMING_GLASS_TOP:
                            vehicleControl.setDimmingGlassTop(isEnable);
                            break;
                        //设置调光玻璃顶为指定模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_GLASS_TOP_MODE:
                            vehicleControl.setGlassTopMode(value);
                            break;
                        //切换调光玻璃顶模式，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_GLASS_TOP_MODE:
                            vehicleControl.changeGlassTopMode();
                            break;
                        //设置调光玻璃顶为具体颜色
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_GLASS_TOP_COLOR:
                            vehicleControl.setGlassTopColor(value);
                            break;
                        //切换调光玻璃顶颜色，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_GLASS_TOP_COLOR:
                            vehicleControl.changeGlassTopColor();
                            break;
                        //调光玻璃顶透明度调节
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_GLASS_TOP_ALPHA:
                            vehicleControl.adjustModeGlassTopAlpha(value);
                            break;
                        //调光玻璃顶透明度调到指定数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_GLASS_TOP_ALPHA:
                            vehicleControl.adjustGlassTopAlpha(value);
                            break;
                        //调光玻璃顶透明度调高相对数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_GLASS_TOP_ALPHA:
                            vehicleControl.adjustUpGlassTopAlpha(value);
                            break;
                        //调光玻璃顶透明度调低相对数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_GLASS_TOP_ALPHA:
                            vehicleControl.adjustDownGlassTopAlpha(value);
                            break;
                        //调光玻璃顶透明度调到指定挡位
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_GLASS_TOP_BRIGHTNESS:
                            vehicleControl.adjustGlassTopBrightness(value);
                            break;
                        //调光玻璃顶亮度调高具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_GLASS_TOP_BRIGHTNESS:
                            vehicleControl.adjustUpGlassTopBrightness(value);
                            break;
                        //调光玻璃顶亮度调高一点
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_GLASS_TOP_BRIGHTNESS:
                            vehicleControl.adjustModeGlassTopBrightness(value);
                            break;
                        //调光玻璃顶亮度调低具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_GLASS_TOP_BRIGHTNESS:
                            vehicleControl.adjustDownGlassTopBrightness(value);
                            break;
                        //控制顶棚开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CEILING:
                            vehicleControl.setCeiling(isEnable);
                            break;
                        //顶棚调到具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_PERCENTAGE_GLASS_TOP:
                            vehicleControl.adjustPercentageGlassTop(value);
                            break;
                        //顶棚开大/小一点
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_GLASS_TOP:
                            vehicleControl.adjustModeGlassTop(value);
                            break;
                        //顶棚挡位调到具体挡位
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_VALUE_GLASS_TOP:
                            vehicleControl.adjustValueGlassTop(value);
                            break;
                        //内饰加热开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INTERIOR_HEATING:
                            vehicleControl.setInteriorHeating(isEnable);
                            break;
                        //内饰加热调高具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_INT_HEATING:
                            vehicleControl.adjustUpIntHeating(value);
                            break;
                        //内饰加热调高一点
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_INT_HEATING:
                            vehicleControl.adjustModeIntHeating(value);
                            break;
                        //内饰加热调低具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_INT_HEATING:
                            vehicleControl.adjustDownIntHeating(value);
                            break;
                        //内饰加热调到具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_INT_HEATING:
                            vehicleControl.adjustIntHeating(value);
                            break;
                        //设防提示开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DEFENSION_ALARM:
                            vehicleControl.setDefenseReminder(isEnable);
                            break;
                        //光线传感器开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LIGHT_SENSOR:
                            vehicleControl.setLightSensor(isEnable);
                            break;
                        //背光关联调节开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_BACK_LIGHT_ASSOCIATION_ADJUSTMENT:
                            vehicleControl.setBackLightAssociationAdjustment(isEnable);
                            break;
                        //背光自动调节开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_BACK_LIGHT_AUTO_ADJUSTMENT:
                            vehicleControl.setBackLightAutoAdjustment(isEnable);
                            break;
                        //鼓风机延迟关闭开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DELAYED_CLOSE_FAN:
                            vehicleControl.setDelayedCloseFan(isEnable);
                            break;
                        //夜视开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_NIGHT_VISION:
                            vehicleControl.setNightVision(isEnable);
                            break;
                        //倒车雷达开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PDC:
                            vehicleControl.setPdc(isEnable);
                            break;
                        //智能启停开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INTELLIGENT_START_STOP:
                            vehicleControl.setIntelligentStartStop(isEnable);
                            break;
                        //车辆启动/熄火
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CONTROL_VEHICLE:
                            vehicleControl.controlVehicle(isEnable);
                            break;
                        //电子手刹开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ELCTRONIC_BRAKE:
                            vehicleControl.electronicBrake(isEnable);
                            break;
                        //灯光秀开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LIGHT_SHOW:
                            lightControl.openOrcloseRandomMusicalGrooves(flag);
                            break;
                        //切换灯光秀，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_LIGHT_SHOW:
                            vehicleControl.changeLightShow();
                            break;
                        //灯光表达开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LIGHT_EXPRESSION:
                            vehicleControl.lightExpression(isEnable);
                            break;
                        //切换灯光表达，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_LIGHT_EXPRESSION:
                            vehicleControl.changeLightExpression();
                            break;
                        //灯光主题开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LIGHT_THEME:
                            vehicleControl.lightTheme(isEnable);
                            break;
                        //切换灯光主题，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_LIGHT_THEME:
                            vehicleControl.changeLightTheme();
                            break;
                        //灯光游戏开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LIGHT_GAME:
                            vehicleControl.lightGame(isEnable);
                            break;
                        //保养里程提醒距离设置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_MAINTAIN_DISTANCE:
                            vehicleControl.setMaintainDistance(value);
                            break;
                        //车辆检测开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_VEHICLE_DETECTION:
                            vehicleControl.vehicleDetection(isEnable);
                            break;
                        //车况异常提醒开关
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ABNORMAL_REMINDER:
                            vehicleControl.abnormalReminder(isEnable);
                            break;
                        //打开/关闭电耗信息
                        case VDVRFunctionID.NewEnergy.VEHICLE_ELECTRICITY_INFO:
                            newEnergyControl.setElectricityInfo(flag);
                            break;
                        //打开/关闭能耗清单
                        case VDVRFunctionID.NewEnergy.VEHICLE_ENERGY_LIST_INTERFACE:
                            newEnergyControl.setEnergyList(flag);
                            break;
                        //行驶里程显示调节
                        case VDVRFunctionID.NewEnergy.VEHICLE_DRIVE_MILEAGE_DISPLAY:
                            newEnergyControl.setDriveMileageDisplay(value);
                            break;
                        //打开/关闭油量信息
                        case VDVRFunctionID.NewEnergy.VEHICLE_OIL_INFO:
                            newEnergyControl.setOilInfo(flag);
                            break;
                        //纯电模式
                        case VDVRFunctionID.NewEnergy.VEHICLE_PURE_ELECTRICITY_DISPLAY:
                            newEnergyControl.setPureElectricMode(value);
                            break;
                        //重置电耗信息
                        case VDVRFunctionID.NewEnergy.VEHICLE_RESET_ENERGY_INFO:
                            newEnergyControl.resetEnergyInfo();
                            break;
                        case VDVRFunctionID.VehicleSetting.SETTING_RANGE_CONDITION_DISPLAY:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭续航工况显示模式");
                            activityControl.setEnduranceDisplay(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.SETTING_RANGE_CONDITION_FUNCTION:
                            Log.d(TAG, "语音服务" + operation + "设置续航工况模式");
                            newEnergyControl.setEnduranceMode(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.SETTING_RANGE_CONDITION_RANDOM:
                            Log.d(TAG, "语音服务" + operation + "切换续航工况显示模式");
                            newEnergyControl.switchEnduranceDisplay(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ATMOSPHERE_LIGHT_WITH_UI:
                            Log.d(TAG, "语音服务" + operation + "关联主题");
                            conditionControl.relatedtopics(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ATMOSPHERE_LIGHT_WITH_DRIVE:
                            Log.d(TAG, "语音服务" + operation + "关联驾驶模式");
                            conditionControl.relatedLinkedDrivingModes(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_INTERIOR_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开指定车内灯");
                            lightControl.openDesignateInsideLight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CLOSE_INTERIOR_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "关闭指定车内灯");
                            lightControl.closeDesignateInsideLight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_GLOVE_MODE:
                            Log.d(TAG, "语音服务" + operation + "设置手套箱灯");
                            lightControl.setGloveBoxLight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_ARMREST_MODE:
                            Log.d(TAG, "语音服务" + operation + "设置扶手箱灯");
                            lightControl.setHandrailBoxLight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_FOOT_LAMP_MODE:
                            Log.d(TAG, "语音服务" + operation + "设置照脚灯");
                            lightControl.setFootlight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_GLOVE:
                            Log.d(TAG, "语音服务" + operation + "手套箱灯调到具体挡位");
                            lightControl.gloveBoxLightValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_ARMREST:
                            Log.d(TAG, "语音服务" + operation + "扶手箱灯调到具体挡位");
                            lightControl.handrailBoxLightValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_FOOT_LAMP:
                            Log.d(TAG, "语音服务" + operation + "照脚灯调到具体挡位");
                            lightControl.footlightValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_LIGHT_GLOVE:
                            Log.d(TAG, "语音服务" + operation + "手套箱灯调大、小一点，最大、最低");
                            lightControl.gloveBoxLightTime(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_LIGHT_ARMREST:
                            Log.d(TAG, "语音服务" + operation + "扶手箱灯调调大、小一点，最大、最低");
                            lightControl.handrailBoxLightTime(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_LIGHT_FOOT_LAMP:
                            Log.d(TAG, "语音服务" + operation + "照脚灯调调大、小一点，最大、最低");
                            lightControl.footlightTime(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_INTERIOR_TIME_GLOVE:
                            Log.d(TAG, "语音服务" + operation + "手套箱灯调到具体数值");
                            lightControl.gloveBoxLightTimeValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_INTERIOR_TIME_ARMREST:
                            Log.d(TAG, "语音服务" + operation + "扶手箱灯调到具体数值");
                            lightControl.handrailBoxLightTimeValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LIGHT_INTERIOR_TIME_FOOT_LAMP:
                            Log.d(TAG, "语音服务" + operation + "照脚灯调到具体数值");
                            lightControl.footlightTimeValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_LIGHT_INTERIOR_TIME_GLOVE:
                            Log.d(TAG, "语音服务" + operation + "手套箱灯调高数值");
                            lightControl.gloveBoxLightTimeValueUp(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_LIGHT_INTERIOR_TIME_ARMREST:
                            Log.d(TAG, "语音服务" + operation + "扶手箱灯调高数值");
                            lightControl.handrailBoxLightTimeValueUp(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_LIGHT_INTERIOR_TIME_FOOT_LAMP:
                            Log.d(TAG, "语音服务" + operation + "照脚灯调高数值");
                            lightControl.footlightTimeValueUp(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_LIGHT_INTERIOR_TIME_GLOVE:
                            Log.d(TAG, "语音服务" + operation + "手套箱灯调低数值");
                            lightControl.gloveBoxLightTimeValueDown(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_LIGHT_INTERIOR_TIME_ARMREST:
                            Log.d(TAG, "语音服务" + operation + "扶手箱灯调低数值");
                            lightControl.handrailBoxLightTimeValueDown(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_LIGHT_INTERIOR_TIME_FOOT_LAMP:
                            Log.d(TAG, "语音服务" + operation + "照脚灯调低数值");
                            lightControl.footlightTimeValueDown(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_OUTSIDE_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开指定车外灯");
                            lightControl.openDesignateOuterLight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CLOSE_OUTSIDE_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "关闭指定车外灯");
                            lightControl.closeDesignateOuterLight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AUTO_HEAD_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭自动大灯");
                            lightControl.openOrcloseAutoLamp(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_POSITION_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭位置灯");
                            lightControl.openOrclosePositionLight(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DIPPED_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭位近光灯");
                            lightControl.openOrcloseNearLight(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_INTELLIGENT_GREETING:
                            Log.d(TAG, "语音服务" + operation + "智能迎宾设置为xxx");
                            lightControl.setIntelligentWelcome(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INTELLIGENT_HIGH_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭智能远光灯");
                            lightControl.openorcloseSmartHighLamp(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_EXTERNAL_MUSIC_RHYTHM:
                            Log.d(TAG, "语音服务" + operation + "外音乐律动模式设置为xxx");
                            lightControl.setOuterMusicLawMode(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_RANDOM_MUSIC_RHYTHM:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭随机音乐律动");
                            lightControl.openOrcloseRandomMusicalGrooves(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DIGITAL_INTERACTION_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭数字交互信号灯");
                            lightControl.openOrclosedigitSignalLight(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_DIGITAL_LIGHT_SIGNAL:
                            Log.d(TAG, "语音服务" + operation + "数字交互信号灯设置为xxx");
                            lightControl.setDigitSignalLight(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_OUTSIDE_LIGHT_MODE:
                            Log.d(TAG, "语音服务" + operation + "切换指定车外灯模式，无指定值");
                            lightControl.switchDesignateOuterLightMode();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_EXTERIOR_TIME:
                            Log.d(TAG, "语音服务" + operation + "车外灯照明时长调大一点，小一点，最小，最大");
                            lightControl.setOuterLightTime(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_EXTERIOR_TIME:
                            Log.d(TAG, "语音服务" + operation + "车外灯照明时长调高相对数值");
                            lightControl.raiseDesignativeOutLampTimeValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_EXTERIOR_TIME:
                            Log.d(TAG, "语音服务" + operation + "车外灯照明时长调低相对数值");
                            lightControl.lowerDesignativeOutLampTimeValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_EXTERIOR_TIME:
                            Log.d(TAG, "语音服务" + operation + "车外灯照明时长调到具体数值");
                            lightControl.setOuterLightTimeValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_DISTANCE_SENSITIVITY:
                            Log.d(TAG, "语音服务" + operation + "前方车距灯光提醒灵敏度");
                            lightControl.setFrontDistanceLightSensitivity(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_DISTANCE_SENSITIVITY:
                            Log.d(TAG, "语音服务" + operation + "前方车距灯光提醒灵敏度调高具体数值");
                            lightControl.raiseFrontDistanceLightSensitivityValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_DISTANCE_SENSITIVITY:
                            Log.d(TAG, "语音服务" + operation + "前方车距灯光提醒灵敏度调低具体数值");
                            lightControl.lowerFrontDistanceLightSensitivityValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DISTANCE_SENSITIVITY:
                            Log.d(TAG, "语音服务" + operation + "前方车距灯光提醒灵敏度调到具体数值");
                            lightControl.setFrontDistanceLightSensitivityValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_HUD_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "hud亮度调高一点,低一点,最高,最低");
                            vehicleControl.adjustHudBrightnessValue(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_HUD_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "hud亮度调高具体数值");
                            vehicleControl.adjustUpHudBrightness(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_HUD_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "hud亮度调低具体数值");
                            vehicleControl.adjustDownHudBrightness(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_HUD_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "hud亮度调到具体数值");
                            vehicleControl.adjustHudBrightness(value);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_CHARGE_GUN_SECURITY:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭充电枪防盗");
                            newEnergyControl.chargingCableBurglar(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_ELECTRICITY_KEEP:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭用电保持");
                            newEnergyControl.openOrcloseHoldPower(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_WHOLE_CAR_POWER:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭整车电源");
                            newEnergyControl.openOrcloseVehiclePowerSupply(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_ADJUST_UP_SOC:
                            Log.d(TAG, "语音服务" + operation + "SOC目标值调大具体数值");
                            newEnergyControl.adjustSOCValueUp(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_HEADLAMP:
                            Log.d(TAG, "语音服务" + operation + "打开大灯设置界面");
                            activityControl.openSetInsideLight(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_HEADLAMP_HEIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开大灯高度设置界面");
                            activityControl.openLightHeightInterface(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REAR_SCREEN:
                            Log.d(TAG, "语音服务" + operation + "后排屏锁");
                            vehicleControl.setScreenLock(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SLOW_TRAFFIC_AHEAD:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭减速缓行");
                            vehicleControl.setDecelerationSlowly(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DIGITAL_REARVIEW_MIRROR_IMAGE:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭数字后视镜影像");
                            vehicleControl.setDigitalMirror(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REMOTE_SWITCH:
                            Log.d(TAG, "语音服务" + operation + "打开遥控车窗开关功能");
                            vehicleControl.setRemoteSwitch(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_RAINAUTO_LIFTWINDOW:
                            Log.d(TAG, "语音服务" + operation + "雨天自动关窗");
                            vehicleControl.setRainAutoLiftWindow(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SPECCONDITION_LIFTWINDOW:
                            Log.d(TAG, "语音服务" + operation + "特殊工况自动关窗");
                            vehicleControl.setSpecConditionLiftWindow(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_WINDOW_CURTAIN:
                            Log.d(TAG, "语音服务" + operation + "车窗帘功能");
                            vehicleControl.setWindowCurtain(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SUNROOF_VENTILATE:
                            Log.d(TAG, "语音服务" + operation + "天窗通风功能");
                            vehicleControl.setSunroofVentilate(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SUNROOF_RISER:
                            Log.d(TAG, "语音服务" + operation + "天窗透气功能");
                            vehicleControl.setSunroofRiser(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SUNROOF_RAISE:
                            Log.d(TAG, "语音服务" + operation + "天窗翘起");
                            vehicleControl.setSunroofRaise(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TIMED_VENTILATE:
                            Log.d(TAG, "语音服务" + operation + "定时通风");
                            vehicleControl.timedVentilate(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TIMED_VENTILATE_INTERVAL:
                            Log.d(TAG, "语音服务" + operation + "定时通风时间间隔");
                            vehicleControl.timedVentilateInterval(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AUXILIARY_INSTRUMENT:
                            Log.d(TAG, "语音服务" + operation + "星动吧台");
                            vehicleControl.setAuxiliaryInstrument(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CONTROL_DOOR:
                            Log.d(TAG, "语音服务" + operation + "控制车门");
                            vehicleControl.controlDoor(flag, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TEMPORARY_CONTROL_DOOR:
                            Log.d(TAG, "语音服务" + operation + "暂停打开/关闭车门");
                            vehicleControl.temporaryControlDoor(isEnable, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LITTLE_DOOR:
                            Log.d(TAG, "语音服务" + operation + "车门微调");
                            vehicleControl.adjustLittleDoor(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_DEGREE_DOOR:
                            Log.d(TAG, "语音服务" + operation + "车门调到到具体数值");
                            vehicleControl.openDegreeDoor(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REOPEN_DEGREE_DOOR:
                            Log.d(TAG, "语音服务" + operation + "车门开到相对数值");
                            vehicleControl.reOpenDegreeDoor(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_RECLOSE_DEGREE_DOOR:
                            Log.d(TAG, "语音服务" + operation + "车门关到相对数值");
                            vehicleControl.reCloseDegreeDoor(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_DEGREE_VALUE_DOOR:
                            Log.d(TAG, "语音服务" + operation + "车门最大开度为指定值");
                            vehicleControl.openDegreeValueDoor(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_SPEED_DOOR:
                            Log.d(TAG, "语音服务" + operation + "车门开关速度设置为指定值");
                            vehicleControl.openSpeedDoor(value, position);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TEMPORARY_CONTROL_TRUNK:
                            Log.d(TAG, "语音服务" + operation + "暂停打开关闭后尾门");
                            vehicleControl.temporaryControlTrunk(isEnable);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TAILGATE_ELECTRIC:
                            Log.d(TAG, "语音服务" + operation + "打开后尾门电动开关");
                            vehicleControl.tailGateElectric(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_OPEN_METHOD_TAILGATE:
                            Log.d(TAG, "语音服务" + operation + "设置尾门开启方式");
                            vehicleControl.openMethodTailGate(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_HEIGHT_TAILGATE:
                            Log.d(TAG, "语音服务" + operation + "尾门调高具体数值");
                            vehicleControl.adjustUpHeightTailGate(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_LITTLE_HEIGHT_TAILGATE:
                            Log.d(TAG, "语音服务" + operation + "尾门调低具体高度");
                            vehicleControl.adjustDownLittleHeightTailGate(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_HEIGHT_TAILGATE:
                            Log.d(TAG, "语音服务" + operation + "尾门高度微调");
                            vehicleControl.adjustModeHeightTailGate(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_HEIGHT_TAILGATE:
                            Log.d(TAG, "语音服务" + operation + "尾门调到具体数值");
                            vehicleControl.adjustHeightTailGate(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TAILGATE_INDUCTION:
                            Log.d(TAG, "语音服务" + operation + "尾门感应");
                            vehicleControl.tailGateInduction(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SMARTKEY_TAILGATE:
                            Log.d(TAG, "语音服务" + operation + "尾门智能钥匙");
                            vehicleControl.smartKeyTailGate(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CONTROL_TAILWING:
                            Log.d(TAG, "语音服务" + operation + "电动尾翼");
                            vehicleControl.controlTailWing(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AUTOMODE_TAILWING:
                            Log.d(TAG, "语音服务" + operation + "尾翼自动模式");
                            vehicleControl.autoModeTailWing(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_HEIGHT_TAILWING:
                            Log.d(TAG, "语音服务" + operation + "尾翼调高具体数值");
                            vehicleControl.adjustHeightTailWing(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_ANGLE_TAILWING:
                            Log.d(TAG, "语音服务" + operation + "尾翼调到指定角度");
                            vehicleControl.adjustAngleTailWing(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_ANGLE_TAILWING:
                            Log.d(TAG, "语音服务" + operation + "尾翼角度微调");
                            vehicleControl.adjustModeAngleTailWing(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_HEIGHT_TAILWING:
                            Log.d(TAG, "语音服务" + operation + "尾翼高度微调");
                            vehicleControl.adjustModeHeightTailWing(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_WELCOME_TAILWING:
                            Log.d(TAG, "语音服务" + operation + "电动尾翼迎宾");
                            vehicleControl.welcomeTailWing(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LUMINOUS_BAGGAGE:
                            Log.d(TAG, "语音服务" + operation + "发光行李架");
                            vehicleControl.luminousBaggage(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_COLOR_LUMINOUS_BAGGAGE:
                            Log.d(TAG, "语音服务" + operation + "发光行李架颜色");
                            vehicleControl.colorLuminousBaggage(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_COLOR_LUMINOUS_BAGGAGE:
                            Log.d(TAG, "语音服务" + operation + "改变发光行李架颜色");
                            vehicleControl.changeColorLuminousBaggage(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LIGHT_LUMINOUS_BAGGAGE:
                            Log.d(TAG, "语音服务" + operation + "发光行李架灯光调节");
                            vehicleControl.lightLuminousBaggage(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HOOD:
                            Log.d(TAG, "语音服务" + operation + "引擎盖");
                            vehicleControl.hood(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHARGING_COVER:
                            Log.d(TAG, "语音服务" + operation + "充电盖");
                            vehicleControl.chargingCover(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_HEIGHT_TAILGATE:
                            Log.d(TAG, "语音服务" + operation + "尾门开启上限高度");
                            vehicleControl.setHeightTailGate(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HUD:
                            Log.d(TAG, "语音服务" + operation + "HUD");
                            vehicleControl.hud(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HUD_DISPLAY_MODE:
                            Log.d(TAG, "语音服务" + operation + "HUD显示模式");
                            vehicleControl.hudDisplayMode(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TEMPORARY_CONTROL_WINDOW:
                            Log.d(TAG, "语音服务" + operation + "暂停打开或者关闭车窗");
                            vehicleControl.temporaryControlWindow(isEnable, position);
                            break;
//                  兜底回复cqz
//                        自动大灯灵敏度调到最高、最低、高一点、低一点
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_AUTO_LIGHT_SENSITIVITY:
                            Log.d(TAG, "语音服务" + operation + "自动大灯灵敏度调到最高、最低、高一点、低一点");
                            lightControl.autoLightControl(value);
                            break;
                        //自动大灯灵敏度调到具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_AUTO_LIGHT_SENSITIVITY:
                            Log.d(TAG, "语音服务" + operation + "自动大灯灵敏度调到具体数值");
                            lightControl.autoLightToNumber(value);
                            break;
                        //自动大灯灵敏度调到挡位
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_DOWN_AUTO_LIGHT_SENSITIVITY:
                            Log.d(TAG, "语音服务" + operation + "自动大灯灵敏度调到挡位");
                            lightControl.autoLightToGears(value);
                            break;
//                        设置仪表屏显示内容  关闭仪表屏显示内容
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_INSTRUMENT_DISPLAY:
                            Log.d(TAG, "语音服务" + operation + "设置仪表屏显示内容  关闭仪表屏显示内容");
                            vehicleControl.setDashboardDisplay(value);
                            break;
//                        切换仪表显示内容，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_DASHBOARD_CONTENT:
                            Log.d(TAG, "语音服务" + operation + "切换仪表显示内容，无指定值");
                            vehicleControl.switchDashboardDisplay();
                            break;
//                                打开仪表背光/关闭仪表背光
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INSIDE_BACKLIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开仪表背光/关闭仪表背光");
                            vehicleControl.openDashboardBacklight(flag);
                            break;
                        //	解闭锁声光设置 (静音模式)
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_LOCK_SOUND:
                            Log.d(TAG, "语音服务" + operation + "解闭锁声光设置（静音模式）");
                            vehicleControl.setSunVoice(value);
                            break;
//                        打开悬架指定模式/关闭悬架指定模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SUSPENSION_SPECIFIC_MODE:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭悬架指定模式");
                            vehicleControl.setSuspensionMode(flag);
                            break;
//                                悬架模式设置为xxx
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_SUSPENSION_MODE:
                            Log.d(TAG, "语音服务" + operation + "悬架模式设置为XX");
                            vehicleControl.setSuspensionMode(value);
                            break;
//                        切换悬架模式，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_SUSPENSION_MODE:
                            Log.d(TAG, "语音服务" + operation + "切换悬架模式，无指定");
                            vehicleControl.switchSuspensionMode();
                            break;
//                                打开悬架维修
//                        关闭悬架维修
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SUSPENSION_REPAIR:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭悬架维修");
                            vehicleControl.setSuspensionRepair(flag);
                            break;
//                                打开高速悬架智控
//                        关闭高速悬架智控
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INTELLIGENCE_HIGHWAY_SUSPENSION:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭高速悬架智控");
                            vehicleControl.setHighwaySuspensionControl(flag);
                            break;
//                                打开悬架智能预瞄
//                        关闭悬架智能预瞄
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SUPPLY_PREVIEW:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭悬架智能预瞄");
                            vehicleControl.setSuspensionAiming(flag);
                            break;
//                                悬架高度设置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_SUSPENSION_HEIGHT:
                            Log.d(TAG, "语音服务" + operation + "悬架高度设置");
                            vehicleControl.setSuspensionHeight(value);
                            break;
//                        悬架高度调高一点
//                                悬架高度调低一点
//                        悬架高度调到顶
//                                悬架高度调到底
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_SUSPENSION_HEIGHT:
                            Log.d(TAG, "语音服务" + operation + "悬架高度最高、最低、高一点、低一点");
                            vehicleControl.setSuspensionHeightMax_Min(value);
                            break;
//                        悬架高度调到指定挡位
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_SUSPENSION_HEIGHT:
                            Log.d(TAG, "语音服务" + operation + "悬架高度调到指定挡位");
                            vehicleControl.setSuspensionHeightToGears(value);
                            break;
//                                悬架高度调到指定数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_UP_SUSPENSION_HEIGHT:
                            Log.d(TAG, "语音服务" + operation + "悬架高度调到指定数值");
                            vehicleControl.setSuspensionHeightToNumber(value);
                            break;
//                        悬架舒适度设置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_SUSPENSION_COMFORT:
                            Log.d(TAG, "语音服务" + operation + "悬架舒适度设置");
                            vehicleControl.setSuspensionComfort(value);
                            break;
//                                悬架调软一点
//                        悬架调硬一点
//                                悬架调最软
//                        悬架调最硬
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_SUSPENSION:
                            Log.d(TAG, "语音服务" + operation + "悬架软一点、硬一点、最软、最硬");
                            vehicleControl.setSuspensionSoft_Hard(value);
                            break;
//                        打开保存驾驶偏好功能
//                                关闭保存驾驶偏好功能
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SAVE_DRIVE_PREFERENCE:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭保存驾驶偏好功能");
                            vehicleControl.setSaveDrivingPreference(flag);
                            break;
//                        打开便捷装载
//                                关闭便捷装载
                        case VDVRFunctionID.VehicleSetting.VEHICLE_COMFORT_LOADING:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭保存驾驶偏好功能");
                            vehicleControl.setConvenientLoad(flag);
                            break;
//                        便捷下车
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CONVENIENT_OFF:
                            Log.d(TAG, "语音服务" + operation + "便捷下车");
                            vehicleControl.setConvenientGetOff(flag);
                            break;
//                        调低车身高度
//                                抬高车身高度
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_VEHICLE_HEIGHT:
                            Log.d(TAG, "语音服务" + operation + "调节车身");
                            vehicleControl.setAdjustCarHeight(value);
                            break;
//                                打开自动便捷上车
//                        关闭自动便捷上车
                        case VDVRFunctionID.VehicleSetting.VEHICLE_AUTO_CONVENIENT_ONBOARD:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭自动便捷上车");
                            vehicleControl.setAutoConvenientGetOn(flag);
                            break;
//                        打开电动侧踏板
//                                关闭电动侧踏板
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ELECTRIC_SIDE_PEDAL:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭电动侧踏板");
                            vehicleControl.setElectricSideBrake(flag);
                            break;
//                        打开N挡防误触
//                                关闭N挡防误触
                        case VDVRFunctionID.VehicleSetting.VEHICLE_N_GEAR_MISOPERATION:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭N挡防误触");
                            vehicleControl.setNgearPrevention(flag);
                            break;
//                        打开防油门误踩
//                                关闭防油门误踩
                        case VDVRFunctionID.VehicleSetting.VEHICLE_THROTTLE_MISOPERATION:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭N挡防误触");
                            vehicleControl.setPreventionOfOilPistonMistake(flag);
                            break;
//                        关闭指定驾驶模式
//                                关闭指定车辆模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_POINT_CAR_MODE:
                            Log.d(TAG, "语音服务" + operation + "关闭指定驾驶模式、车辆模式");
                            vehicleControl.setCloseSpecifiedDrivingMode(value);
                            break;
//                        关闭驾驶模式无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CLOSE_RANDOM_CAR_MODE:
                            Log.d(TAG, "语音服务" + operation + "关闭驾驶模式无指定值");
                            vehicleControl.setCloseSpecifiedDrivingModeNoValue();
                            break;
//                                打开驾驶模式记忆
//                        关闭驾驶模式记忆
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DIRECTIVE_MODE_REMEMBER:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭驾驶模式记忆");
                            vehicleControl.setDrivingModeMemory(flag);
                            break;
//                        打开转向助力模式
//                                关闭转向助力模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_POWER_ASSIST_MODE:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭转向助力模式");
                            vehicleControl.setTurnAssistMode(flag);
                            break;
//                        切换转向助力模式，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_TORQUE_ASSIST_MODE:
                            Log.d(TAG, "语音服务" + operation + "切换转向助力模式，无指定值");
                            vehicleControl.setTurnAssistModeNoValue();
                            break;
//                                打开转向助力关联驾驶模式
//                        关闭转向助力关联驾驶模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_POWER_ASSIST_WITH_DRIVE:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭转向助力关联驾驶模式");
                            vehicleControl.setTurnAssistRelatedDrivingMode(flag);
                            break;
//                        预测型碰撞报警系统等级设置（高中低）
//                        预测型碰撞报警系统等级调高
//                                预测型碰撞报警系统等级调低
//                        预测型碰撞报警系统等级调到最高
//                                预测型碰撞报警系统等级调到最低
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_PREDICTION_WARNING_LEVEL:
                            Log.d(TAG, "语音服务" + operation + "预测型碰撞报警系统等级");
                            vehicleControl.setPredictionWarningLevel(value);
                            break;
//                        打开交通标示识别TSR
//                                关闭交通标示识别TSR
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TSR:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭交通标示识别TSR");
                            vehicleControl.setTrafficSignRecognition(flag);
                            break;
//                        打开动态交通场景显示
//                                关闭动态交通场景显示
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DYNAMIC_TRAFFIC_DISPLAY:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭动态交通场景显示");
                            vehicleControl.setDynamicTrafficSceneDisplay(flag);
                            break;
//                        行人低速提示音设置成指定话术
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_PEDESTRIAN_ALARM_SOUND:
                            Log.d(TAG, "语音服务" + operation + "行人低速提示音设置成指定话术");
                            vehicleControl.setPedestrianLowSpeedTips(value);
                            break;
                        //	打开行人安全辅助
                        //	关闭行人安全辅助
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PEDESTRIAN_SAFE_ASSIST:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭行人安全辅助");
                            vehicleControl.setPedestrianSafetyAssistance(flag);
                            break;
                        //	行人安全辅助模式设置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_PEDESTRIAN_ASSIST_MODE:
                            Log.d(TAG, "语音服务" + operation + "行人安全辅助模式设置");
                            vehicleControl.setPedestrianSafetyAssistanceMode(value);
                            break;
                        //	打开起步提醒
                        //	关闭起步提醒
                        case VDVRFunctionID.VehicleSetting.VEHICLE_START_REMINDER:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭起步提醒");
                            vehicleControl.setStartUpReminder(flag);
                            break;
//                        设置Ibooster模式为指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_IBOOSTER_MODE:
                            Log.d(TAG, "语音服务" + operation + "设置Ibooster模式为指定值");
                            vehicleControl.setIboosterMode(value);
                            break;
//                                打开穿行制动
//                        关闭穿行制动
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TRANSPORT_BRAKE:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭穿行制动");
                            vehicleControl.setTreadBrake(flag);
                            break;
//                                打开盲区影像画质增强
//                        关闭盲区影像画质增强
                        case VDVRFunctionID.VehicleSetting.VEHICLE_BLIND_AREA_IMAGE_ENHANCE:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭盲区影像画质增强");
                            vehicleControl.setBlindAreaImageQualityEnhancement(flag);
                            break;
//                                打开主动车速限制
//                        关闭主动车速限制
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ACTIVE_SPEED_LIMIT:
                            Log.d(TAG, "语音服务" + operation + "打开、关闭主动车速限制");
                            vehicleControl.setActiveSpeedLimit(flag);
                            break;
//                                主动车速限制调到具体数值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_SPEED_LIMIT:
                            Log.d(TAG, "语音服务" + operation + "主动车速限制调到具体数值");
                            vehicleControl.setActiveSpeed(value);
                            break;
//                        车速限制调大一点
//                                车速限制调小一点
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_MODE_SPEED_LIMIT:
                            Log.d(TAG, "语音服务" + operation + "车速限制调大一点、车速限制调小一点");
                            vehicleControl.setActiveSpeedLimit(value);
                            break;
//                        车身电子稳定控制系统模式设置
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_STABILITY_CONTROL_MODE:
                            Log.d(TAG, "语音服务" + operation + "车身电子稳定控制系统模式设置");
                            vehicleControl.setElectronicStabilityControlSystemMode(value);
                            break;
//                        切换电子车身稳定系统模式，无指定值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_STABLE_SYSTEM_MODE:
                            Log.d(TAG, "语音服务" + operation + "车身电子稳定控制系统模式设置、切换电子车身稳定系统模式，无指定值");
                            vehicleControl.setElectronicStabilityControlSystemModeNoValue();
                            break;
//                                打开动态方向稳定辅助
//                        关闭动态方向稳定辅助
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DYNAMIC_DIRECTION_STABLE_ASSIST:
                            Log.d(TAG, "语音服务" + operation + "车身电子稳定控制系统模式设置、切换电子车身稳定系统模式，无指定值、打开动态方向稳定辅助、关闭动态方向稳定辅助");
                            vehicleControl.setDynamicDirectionStabilityAssistance(flag);
                            break;
                        // 界面打开
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_INSIDE_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "车内灯光设置");
                            activityControl.setInsideLight(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_OUTSIDE_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "车外灯光设置");
                            activityControl.setOutsideLight(flag);
                            break;
                        case VDVRFunctionID.NewEnergy.VEHICLE_PAGE_CHARGE_MANAGEMENT:
                            Log.d(TAG, "语音服务" + operation + "充电管理");
                            activityControl.setChargeManagement(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_WINDOW_CONTROL:
                            Log.d(TAG, "语音服务" + operation + "车窗控制");
                            activityControl.setWindowControl(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_ATMOSPHERE_LIGHT:
                            Log.d(TAG, "语音服务" + operation + "打开氛围灯设置界面");
                            activityControl.openLightUI(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_ATMOSPHERE_LIGHT_COLOR:
                            Log.d(TAG, "语音服务" + operation + "打开氛围灯颜色设置界面");
                            activityControl.openInLightColorUI(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_ATMOSPHERE_LIGHT_RHYTHM:
                            Log.d(TAG, "语音服务" + operation + "打开氛围灯音乐律动设置界面");
                            activityControl.openInLightMusicUI(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_ATMOSPHERE_LIGHT_BRIGHTNESS:
                            Log.d(TAG, "语音服务" + operation + "打开氛围灯亮度设置界面");
                            activityControl.openInLightBrightUI(flag);
                            break;
                        //HUD显示
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_HUD_DISPLAY:
                            vehicleControl.setHUDDisplay(value);
                            break;
                        //HUD恢复默认值
                        case VDVRFunctionID.VehicleSetting.VEHICLE_RESTORE_HUD_DEFAULT:
                            vehicleControl.restoreHUDDefault();
                            break;
                        //左HUD图片旋转
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_LEFT_HUD_IMAGE_ROTATE:
                            vehicleControl.setLeftHUDImageRotate(value);
                            break;
                        //右HUD图片旋转
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_RIGHT_HUD_IMAGE_ROTATE:
                            vehicleControl.setRightHUDImageRotate(value);
                            break;
                        //关闭胎压监测
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TIRE_PRESSURE_MONITORING:
                            vehicleControl.setTirePressureMonitoring(flag);
                            break;
                        //打开/关闭胎压校准功能
                        case VDVRFunctionID.VehicleSetting.VEHICLE_TIRE_PRESSURE_CALIBRATION:
                            vehicleControl.setTirePressureCalibration(flag);
                            break;
                        //打开/关闭差速锁
                        case VDVRFunctionID.VehicleSetting.VEHICLE_DIFFERENTIAL_LOCK:
                            vehicleControl.setDifferentialLock(flag);
                            break;
                        //打开/关闭能量回收
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ENGINE_RECOVERY:
                            vehicleControl.setEngineRecovery(flag);
                            break;
                        //能源模式设置为xxx
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_ENERGY_MODE:
                            vehicleControl.setEnergyMode(value);
                            break;
                        //切换能源模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_POWER_MODE_RANDOM:
                            vehicleControl.switchEnergyMode(flag);
                            break;
                        //打开/关闭强制EV模式
                        case VDVRFunctionID.VehicleSetting.VEHICLE_FORCE_EV_MODE:
                            vehicleControl.setForceEVMode(flag);
                            break;
                        //打开/关闭车载冰箱
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CAR_REFRIGERATOR:
                            vehicleControl.setCarRefrigerator(flag);
                            break;
                        //充电口盖解锁
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHARGING_COVER_LOCK:
                            vehicleControl.setChargingCoverLock(flag);
                            break;
                        //打开/关闭冰箱门
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REFRIGERATOR_DOOR:
                            vehicleControl.setRefrigeratorDoor(flag);
                            break;
                        //打开/关闭冰箱电源
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REFRIGERATOR_POWER:
                            vehicleControl.setRefrigeratorPower(flag);
                            break;
                        //打开冰箱XX功能
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_REFRIGERATOR_MODE:
                            vehicleControl.setRefrigeratorMode(value);
                            break;
                        //冰箱温度设置为XX
                        case VDVRFunctionID.VehicleSetting.VEHICLE_ADJUST_REFRIGERATOR_TEMPERATURE:
                            vehicleControl.setRefrigeratorTemperature(value);
                            break;
                        //冰箱温度设置为最高/最低
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_REFRIGERATOR_TEMPERATURE_MODE:
                            vehicleControl.setRefrigeratorTemperatureUp(value);
                            break;
                        //打开/关闭离车冰箱持续工作
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REFRIGERATOR_CONTINUES_WORK:
                            vehicleControl.setRefrigeratorContinuesWork(flag);
                            break;
                        //离车冰箱持续工作设置为XX
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_REFRIGERATOR_CONTINUES_WORK_TIME:
                            vehicleControl.setRefrigeratorContinuesWorkTime(value);
                            break;
                        //打开/关闭冰箱儿童锁
                        case VDVRFunctionID.VehicleSetting.VEHICLE_REFRIGERATOR_CHILD_LOCK:
                            vehicleControl.setRefrigeratorChildLock(flag);
                            break;
                        //打开/关闭限速辅助
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SPEED_LIMIT_REMINDER:
                            vehicleControl.setSpeedLimitReminder(flag);
                            break;
//                        沉浸视频
                        case VDVRFunctionID.VehicleSetting.VEHICLE_IMMERSIVE_VIDEO:
                            vehicleControl.setImmersiveVideo(flag);
                            break;
                        //打开/关闭保养里程提醒
                        case VDVRFunctionID.VehicleSetting.VEHICLE_MAINTENANCE_MILEAGE_REMINDER:
                            vehicleControl.setMaintenanceMileageReminder(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_LOCK_CLOSE_DAY_SHADE:
                            vehicleControl.lockCloseDayShadeControl(flag);
                            break;
                        //初始化遮阳帘
                        case VDVRFunctionID.VehicleSetting.VEHICLE_INIT_SHADE:
                            vehicleControl.initShade();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_SET_HEAD_PILLOW:// 设置头枕音响的指定模式
                            voicecontrol.setHeadrestdilog(value);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_CHANGE_HEADREST_AUDIO_MODE:// 随机头枕音响模式
                            voicecontrol.randomHeadrestdilog();
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_PAGE_VEHICLE_MAINTAIN:
                            Log.d(TAG, "onVDNotify: " + operation + "set vehicle maintain page");
                            conditionControl.setVehicleMaintainPage(flag);
                            break;
                        default:
                            Log.d(TAG, "VDEventVR.VR_VEHICLE_CONTROL--未匹配functionId: " + functionId);
                            break;
                    }
                    break;
                case VDEventVR.VR_AIRCONDITION:
                    switch (functionId) {
                        //打开/关闭香氛迎宾
                        case VDVRFunctionID.VehicleSetting.VEHICLE_FRAGRANCE_WELCOME:
                            conditionControl.setFragranceWelcome(flag);
                            break;
                        //打开/关闭香氛
                        case VDVRFunctionID.Hvac.HVAC_FRAGRANCE:
                            conditionControl.setFragrance(flag);
                            break;
                        //查看目前的香氛类型
                        case VDVRFunctionID.Hvac.HVAC_QUERY_FRAGRANCE_TYPE:
                            conditionControl.getFragranceType();
                            break;
                        //查询香氛余量
                        case VDVRFunctionID.Hvac.HVAC_QUERY_FRAGRANCE_LEFT:
                            conditionControl.getFragranceLeft();
                            break;
                        //切换香氛模式，无指定值
                        case VDVRFunctionID.Hvac.HVAC_RANDOM_FRAGRANCE_MODE:
                            conditionControl.setRandomFragranceMode();
                            break;
                        //切换香氛模式，指定值
                        case VDVRFunctionID.Hvac.HVAC_SET_MODE_FRAGRANCE:
                            conditionControl.setModeFragrance();
                            break;
                        //香氛浓度(风速)调高具体数值
                        case VDVRFunctionID.Hvac.HVAC_ADJUST_UP_FRAGRANCE_CONCENTRATION:
                            conditionControl.FragranceConcentrationUp();
                            break;
                        //香氛浓度(风速)调低具体数值
                        case VDVRFunctionID.Hvac.HVAC_ADJUST_DOWN_FRAGRANCE_CONCENTRATION:
                            conditionControl.FragranceConcentrationDown();
                            break;
                        case VDVRFunctionID.Hvac.HVAC_ADJUST_FRAGRANCE_CONCENTRATION:
                            Log.d(TAG, "语音服务" + operation + "香氛浓度设置到具体数值");
                            conditionControl.setFragranceIntensityNumber(value);
                            break;
                        case VDVRFunctionID.Hvac.HVAC_ADJUST_MODE_FRAGRANCE_CONCENTRATION:
                            Log.d(TAG, "语音服务" + operation + "香氛浓度调到高中低挡");
                            conditionControl.setFragranceIntensityGears(value);
                            break;
                        case VDVRFunctionID.Hvac.HVAC_AUTO_AIR_PURIFICATION:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭空气进化自动运行");
                            conditionControl.openOrcloseAirpurification(flag);
                            break;
                        case VDVRFunctionID.Hvac.HVAC_HUMIDITY_FUNCTION:
                            Log.d(TAG, "语音服务" + operation + "打开or关闭加湿功能");
                            conditionControl.openOrcloseHumidificationfunction(flag);
                            break;
                        case VDVRFunctionID.VehicleSetting.VEHICLE_HOLD_BRAKE:
                            vehicleControl.setHoldBrake(flag);
                            break;
                        default:
                            Log.d(TAG, "VDEventVR.VR_AIRCONDITION--未匹配functionId: " + functionId);
                            break;
                    }
                    break;
                case VDEventVR.VR_PHONE:
                    switch (functionId) {
                        //拨打救援电话(B-call)
                        case VDVRFunctionID.Phone.PHONE_B_CALL:
                            systemControl.setBCall();
                            break;
                        default:
                            Log.d(TAG, "VDEventVR.VR_PHONE--未匹配functionId: " + functionId);
                            break;
                    }
                    break;
                default:
                    Log.d(TAG, "未匹配vdEvent: " + vdEvent.getId());
                    break;
            }
        }
    };

    @Override
    public IBinder onBind(Intent intent) {
        // 如果服务需要绑定，则返回一个IBinder实例
        // 如果不需要绑定，则返回null
        super.onBind(intent);
        return null;
    }


}
