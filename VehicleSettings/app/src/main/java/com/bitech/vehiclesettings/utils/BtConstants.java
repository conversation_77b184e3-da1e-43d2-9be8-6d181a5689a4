package com.bitech.vehiclesettings.utils;

/**
 * Created by <PERSON><PERSON><PERSON> on 18-12-5.
 */

public class BtConstants {

    /**
     * the request code of enable bluetooth
     */
    public static final int REQUEST_ENABLE_BT = 0;
    public static final int MESSAGE_START_SCAN = 0;
    public static final int MESSAGE_STOP_SCAN = 1;
    public static final int MESSAGE_START_CONNECT = 2;
    public static final int MESSAGE_START_DISCONNECT = 3;
    public static final int MESSAGE_ACTION_FOUND = 4;
    public static final int MESSAGE_CONNECTION_STATE_CHANGED = 5;
    public static final String EXTRA_DEVICE = "extra_device";
    public static final String EXTRA_RSSI = "extra_rssi";
    public static final String EXTRA_CLASS = "extra_class";
    public static final String EXTRA_CONNECTION_STATE = "extra_connection_state";

    static final String PREF_CONNECTED_DEVICE = "pref_connected_device";
    static final String PREF_DEVICE_ADDRESS = "pref_device_address";
    static final String PREF_DEVICE_NAME = "pref_device_name";

    public static final int RSSI_UNKNOWN = -1;

    public static final int BONDING = 1;
    public static final int BONDED = 2;
    public static final int UNBONDED = 3;
    public static final int STATE_DISCONNECTED = 4; //未连接
    public static final int STATE_CONNECTING = 5; //连接中
    public static final int STATE_CONNECTED = 6; //连接成功
    public static final int SCAN = 7;
    public static final int SCANED = 8;
    public static final int STATE_OFF = 10; //蓝牙关闭
    public static final int STATE_ON = 12; //蓝牙打开
    public static final int STATE_TURNING_OFF = 13; //蓝牙正在关闭
    public static final int STATE_TURNING_ON = 11; //蓝牙正在打开
}
