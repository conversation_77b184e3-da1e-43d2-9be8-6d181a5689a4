package com.bitech.vehiclesettings.view.system;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSTemperatureSettingBinding;
import com.bitech.vehiclesettings.presenter.system.SystemPresenterListener;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.Locale;

public class TemperatureUnitUIAlert extends BaseDialog {
    private static final String TAG = TemperatureUnitUIAlert.class.getSimpleName();
    private static TemperatureUnitUIAlert.OnProgressChangedListener onProgressChangedListener;

    public TemperatureUnitUIAlert(@NonNull Context context) {
        super(context);
    }

    public TemperatureUnitUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected TemperatureUnitUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static TemperatureUnitUIAlert.OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(TemperatureUnitUIAlert.OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = true;
        private DialogAlertSTemperatureSettingBinding binding;
        private String phoneNumber;
        private boolean isBlueOpen = false;
        private TemperatureUnitUIAlert dialog;
        public Builder(Context context) {
            this.context = context;
        }

        public TemperatureUnitUIAlert.Builder setCancelable(boolean isCancelable) {
            this.isCancelable = isCancelable;
            return this;
        }

        public TemperatureUnitUIAlert.Builder setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
            return this;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public TemperatureUnitUIAlert create() {
            dialog = new TemperatureUnitUIAlert(context, R.style.Dialog);
            binding = DialogAlertSTemperatureSettingBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1128; // 或者使用具体的像素值
                layoutParams.height = 508;
                window.setAttributes(layoutParams);
            }
            setupListeners();
            initData();
            return dialog;
        }

        private void initData() {
            int state = onProgressChangedListener.getTemperatureUnit();
            binding.tvTemperatureUnitCelsius.setSelected(state == 0 ? true : false);
            binding.tvTemperatureUnitFahrenheit.setSelected(state == 1 ? true : false);
        }

        private void setupListeners() {
            binding.tvTemperatureUnitCelsius.setOnClickListener(view -> {
                onProgressChangedListener.setTemperatureUnit(0);
                binding.tvTemperatureUnitCelsius.setSelected(true);
                binding.tvTemperatureUnitFahrenheit.setSelected(false);
            });
            binding.tvTemperatureUnitFahrenheit.setOnClickListener(v -> {
                onProgressChangedListener.setTemperatureUnit(1);
                binding.tvTemperatureUnitCelsius.setSelected(false);
                binding.tvTemperatureUnitFahrenheit.setSelected(true);
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void dismiss() {
        unregisterReceiver(getContext());
        super.dismiss();
    }

    private void unregisterReceiver(Context context) {
        // Implementation for unregistering receivers
    }

    public interface OnProgressChangedListener {
        int getTemperatureUnit();
        void setTemperatureUnit(int state);
    }
}
