package com.bitech.vehiclesettings.view.negative;

import android.os.Handler;
import android.util.Log;

import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;

public class ParkPowerTimer {
    private static final long ONE_HOUR = 3600000L;
    private Handler handler;
    private NewEnergyManager energyManager;
    private Runnable powerOffTask = () -> {
        Log.d("ParkPowerTimer", "倒计时结束，驻车保电退出");
        energyManager.setParkPowerStatus(CarNewEnergy.ParkPowerSet.OFF);
    };

    public ParkPowerTimer(Handler handler, NewEnergyManager manager) {
        this.handler = handler;
        this.energyManager = manager;
    }

    public void schedulePowerOff(int hours) {
        // 取消之前的计时
        handler.removeCallbacks(powerOffTask);

        if(hours > 0) {
            Log.d("ParkPowerTimer", "开始驻车保电倒计时: " + hours + "小时");
            handler.postDelayed(powerOffTask, hours * ONE_HOUR);
        }
    }

    public void cancel() {
        handler.removeCallbacks(powerOffTask);
    }
}
