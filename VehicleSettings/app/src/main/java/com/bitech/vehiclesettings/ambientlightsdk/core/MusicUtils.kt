package com.bitech.vehiclesettings.ambientlightsdk.core

import android.util.Log
import androidx.annotation.Keep
import kotlin.math.log

/**
 *    <AUTHOR> Ｃｏｏｋ
 *    @Date   : 2022/10/9
 *    @Desc   :
 *    @Version:
 */
object MusicUtils {
    @Keep
    const val ACTION_MUSIC_MODEL = "ACTION_MUSIC_MODEL"

    @Keep
    const val ACTION_MUSIC_MODEL_STATUS = "ACTION_MUSIC_MODEL_STATUS"
    var lastFrequency: Double = 0.0
    var lastDb: Int = 0
    var lastColor: Int = 0
    private lateinit var ambientlightAPI: AmbientlightAPI

//    private const val QUEUE_SIZE = 10

//    private val queue = ArrayDeque<Int>(QUEUE_SIZE)
//    var lastAvg: Double = 0.0
//    var nowAvg: Double = 0.0
//    private fun getAverage(): Double {
//        if (queue.isEmpty()) return 0.0
//        return queue.sum().toDouble() / queue.size
//    }


    @Synchronized
    fun sendMusicColor(currentFrequency: Double, currentDb: Int) {
//        AppLog.i("MusicUtils", "sendMusicColor = $currentFrequency $currentDb")
        val lightLevel = when {
            currentDb - 20 < 5 -> {
                5
            }

            currentDb - 20 > 100 -> {
                100
            }

            else -> {
                currentDb - 20
            }
        }

//        if (queue.size < QUEUE_SIZE) {
//            queue.addLast(currentDb)
//        } else {
//            lastAvg = getAverage()
//            queue.removeFirst()
//            queue.addLast(currentDb)
//            nowAvg = getAverage()
//        }


        val colorIndex = when (currentFrequency) {
            in 0.0..23.2 -> {
                137
            }

            in 23.3..26.0 -> {
                117
            }

            in 26.1..29.2 -> {
                159
            }

            in 29.3..31.8 -> {
                179
            }

            in 31.9..34.7 -> {
                75
            }

            in 34.8..39.0 -> {
                85
            }

            in 39.1..42.4 -> {
                95
            }

            in 42.5..46.3 -> {
                136
            }

            in 46.4..52.0 -> {
                116
            }


            in 52.1..58.4 -> {
                158
            }

            in 58.5..63.6 -> {
                178
            }

            in 63.7..69.4 -> {
                74
            }

            in 69.5..77.9 -> {
                84
            }

            in 78.0..84.9 -> {
                94
            }

            in 85.0..92.7 -> {
                135
            }

            in 92.8..104.0 -> {
                115
            }


            in 104.1..116.7 -> {
                157
            }

            in 116.8..127.1 -> {
                177
            }

            in 127.2..138.8 -> {
                73
            }

            in 138.9..155.8 -> {
                83
            }

            in 155.9..169.7 -> {
                93
            }

            in 169.8..185.3 -> {
                134
            }

            in 185.4..208.0 -> {
                114
            }


            in 208.1..233.5 -> {
                156
            }

            in 233.6..254.3 -> {
                176
            }

            in 254.4..277.6 -> {
                72
            }

            in 277.7..311.6 -> {
                82
            }

            in 311.7..339.4 -> {
                92
            }

            in 339.5..370.6 -> {
                133
            }

            in 370.7..416.0 -> {
                113
            }


            in 416.1..466.9 -> {
                155
            }

            in 467.0..508.6 -> {
                175
            }

            in 508.7..555.3 -> {
                71
            }

            in 555.4..623.3 -> {
                81
            }

            in 623.4..678.8 -> {
                91
            }

            in 678.9..741.2 -> {
                132
            }

            in 741.3..832.0 -> {
                112
            }


            in 832.1..933.9 -> {
                154
            }

            in 934.0..1017.1 -> {
                174
            }

            in 1017.2..1110.6 -> {
                70
            }

            in 1110.7..1246.6 -> {
                80
            }

            in 1246.7..1357.7 -> {
                90
            }

            in 1357.8..1482.4 -> {
                131
            }

            in 1482.5..1664.0 -> {
                111
            }


            in 1664.1..1867.8 -> {
                153
            }

            in 1867.9..2034.3 -> {
                173
            }

            in 2034.4..2221.1 -> {
                69
            }

            in 2221.2..2493.1 -> {
                79
            }

            in 2493.2..2715.4 -> {
                89
            }

            in 2715.5..2964.8 -> {
                130
            }

            in 2964.9..3328.0 -> {
                110
            }

            in 3328.1..3735.5 -> {
                152
            }

            in 3735.6..4068.5 -> {
                172
            }

            in 4068.6..4442.3 -> {
                68
            }

            in 4442.4..4986.3 -> {
                78
            }

            in 4986.4..5430.8 -> {
                88
            }

            in 5430.9..5929.7 -> {
                129
            }

            in 5929.8..6655.9 -> {
                109
            }


            in 6656.0..7471.1 -> {
                151
            }

            in 7471.2..8137.0 -> {
                171
            }

            in 8137.1..8884.6 -> {
                67
            }

            in 8884.7..9972.6 -> {
                77
            }

            in 9972.7..10861.6 -> {
                87
            }

            in 10861.7..11859.4 -> {
                128
            }

            in 11859.5..13311.8 -> {
                108
            }

            in 13311.9..14942.1 -> {
                150
            }

            in 14942.2..16274.1 -> {
                170
            }

            in 16274.2..17769.2 -> {
                66
            }

            in 17769.3..19397.2 -> {
                76
            }

            in 19397.3..25000.0 -> {
                86
            }

            else -> {
                86
            }
        }


        try {
//            val AppConstants = Class.forName("com.simba.atmospherelamp.utils.AppConstants")
//            val channelField = AppConstants.getField("MUSIC_RHYTHM")
//            val music_rhythm = channelField.get(null) as Int
////            Log.d("SDK", "Loaded MUSIC_RHYTHM=$music_rhythm")
//
//
//            val clazz = Class.forName("com.simba.atmospherelamp.utils.CacheHelper")
//            val instance = clazz.getField("INSTANCE").get(null)
//
//            val method1 = clazz.getMethod("getLightModel")
//            val light_model = method1.invoke(instance) as Int
//
//            val method2 = clazz.getMethod("isOpen")
//            val is_open = method2.invoke(instance) as Boolean
//
//            val method3 = clazz.getMethod("isMusicRhythmStart")
//            val is_musicRhythmStart = method3.invoke(instance) as Boolean


//            Log.d(
//                "SDK",
//                "Loaded light_model=$light_model,is_open=$is_open,is start=$is_musicRhythmStart"
//            )
//            val setter1 = clazz.getMethod("setMusicLastcolorIndex", Int::class.javaPrimitiveType)
//            setter1.invoke(instance, colorIndex)
//            val setter2 = clazz.getMethod("setMusicLastLightLevel", Int::class.javaPrimitiveType)
//            setter2.invoke(instance, lightLevel)

//            val method4 = clazz.getMethod("getMusicLastcolorIndex")
//            val testmusicIndex = method4.invoke(instance) as Int

//            Log.i("SDK","colorIdex$colorIndex  xiugai$testmusicIndex")


//        CacheHelper.musicLastcolorIndex = colorIndex
//        CacheHelper.musicLastLightLevel = lightLevel
//        Log.i(
//            "MusicUtils", "sendMusicColor: " + currentFrequency + "   " + currentDb +
//                    "  CacheHelper.isOpen:" + CacheHelper.isOpen + "     CacheHelper.lightModel:" + CacheHelper.lightModel + "rhythmIsStart:" + CacheHelper.isMusicRhythmStart
//        )

            var n: Int = 0
            n = if (currentFrequency > lastFrequency) {
                12 * (log((currentFrequency / lastFrequency), 2.0)).toInt()
            } else {
                12 * (log((lastFrequency / currentFrequency), 2.0)).toInt()
            }


//        if (CacheHelper.lightModel == AppConstants.MUSIC_RHYTHM &&
//            CacheHelper.isOpen && CacheHelper.isMusicRhythmStart
//        ) {

            if (lastDb == 0 && lastFrequency == 0.0) {
                lastColor = colorIndex
                ambientlightAPI.setAmbientLightMusicColor(colorIndex, lightLevel)
            }
//                else if (lastAvg != 0.0 && nowAvg != 0.0 && (Math.abs(lastAvg - nowAvg) / lastAvg) < 0.005) {
//                    ambientlightAPI.setAmbientLightMusicColor(lastColor, lightLevel)
//                }
            else if ((Math.abs(lastDb - currentDb) / lastDb) > 0.3) {
                lastColor = colorIndex
                ambientlightAPI.setAmbientLightMusicColor(colorIndex, lightLevel)
            } else if (n > 24) {
                ambientlightAPI.setAmbientLightMusicColor(colorIndex, lightLevel)
                lastColor = colorIndex
            } else {
                ambientlightAPI.setAmbientLightMusicColor(lastColor, lightLevel)
            }

            lastDb = currentDb
            lastFrequency = currentFrequency


        } catch (e: ClassNotFoundException) {
            Log.w("SDK", "ExternalConfig not found")
        } catch (e: Exception) {
            Log.e("SDK", "Error loading constants: ${e.message}")
        }


    }

    @Keep
    @JvmStatic
    fun setCallback(ambientlightAPI: AmbientlightAPI) {
        MusicUtils.ambientlightAPI = ambientlightAPI
    }


}