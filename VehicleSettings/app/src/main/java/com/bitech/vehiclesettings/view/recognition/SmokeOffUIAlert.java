package com.bitech.vehiclesettings.view.recognition;

import android.app.Dialog;
import android.content.Context;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertRCameraSwitchOnBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertRSmokeOffBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertRSmokeOnBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.EToast;

public class SmokeOffUIAlert extends Dialog {
    private static final String TAG = SmokeOffUIAlert.class.getSimpleName();
    private static SmokeOffUIAlert.onProgressChangedListener onProgressChangedListener;


    public SmokeOffUIAlert(@NonNull Context context) {
        super(context);
    }

    public SmokeOffUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected SmokeOffUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static SmokeOffUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        SmokeOffUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private boolean cameraConfirmFlag = false;
        protected DialogAlertRSmokeOffBinding binding;
        private CountDownTimer countDownTimer; // 计时器
        private boolean isBlueOpen = false;
        private SmokeOffUIAlert dialog = null;
        private View layout;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public Builder(Context context) {
            this.context = context;
        }

        public SmokeOffUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }

        public SmokeOffUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new SmokeOffUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertRSmokeOffBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            layoutParams.height = 378;
            // 设置位置
            layoutParams.gravity = Gravity.TOP | Gravity.LEFT; // 左上角
            layoutParams.y = 144; // 距顶部偏移量（单位：像素）
            layoutParams.x = 80; // 距左部偏移量（单位：像素）
            window.setAttributes(layoutParams);

            // 初始化倒计时
            startCountDown();

            // 设置按钮效果
            // 设置确定按钮效果
            clickConfirmCamera();
            // 设置取消按钮效果
            clickCancelCamera();

            return dialog;
        }

        // 新增倒计时方法
        private void startCountDown() {
            // 初始设置为15秒
            binding.tvSmokeConfirm.setText("确认 (15s)");

            countDownTimer = new CountDownTimer(15000, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    int secondsLeft = (int) (millisUntilFinished / 1000);
                    binding.tvSmokeConfirm.setText("确认 (" + secondsLeft + "s)");
                }

                @Override
                public void onFinish() {
                    binding.tvSmokeConfirm.setText("确认 (0s)");
                    // 自动触发点击事件
                    binding.tvSmokeConfirm.performClick();
                }
            }.start();
        }

        // 点击确认按钮
        private void clickConfirmCamera() {
            BindingUtil.bindClick(binding.tvSmokeConfirm, v -> {
                // 取消倒计时，防止重复触发
                if (countDownTimer != null) {
                    countDownTimer.cancel();
                }
                // 确认逻辑

                dialog.dismiss();
            });
        }

        // 点击取消按钮
        private void clickCancelCamera() {
            binding.tvSmokeCancel.setOnClickListener(v -> {
                // 取消倒计时
                if (countDownTimer != null) {
                    countDownTimer.cancel();
                }
                dialog.dismiss();
            });
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }
}
