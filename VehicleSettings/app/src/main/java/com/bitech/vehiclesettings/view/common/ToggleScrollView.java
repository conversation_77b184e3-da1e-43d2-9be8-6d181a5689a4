package com.bitech.vehiclesettings.view.common;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.ScrollView;

public class ToggleScrollView extends ScrollView {
    private boolean isScrollable = true;

    public ToggleScrollView(Context context) {
        super(context);
    }

    public ToggleScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ToggleScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setScrollable(boolean scrollable) {
        this.isScrollable = scrollable;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return isScrollable && super.onInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        return isScrollable && super.onTouchEvent(ev);
    }
}
