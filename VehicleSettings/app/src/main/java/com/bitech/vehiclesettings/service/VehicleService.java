/**
 * Copyright (c) 2023—2025 BiTECH Automotive (Wuhu) Co.,Ltd. All rights reserved.
 */
package com.bitech.vehiclesettings.service;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleService;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.interfaces.intelligentdriving.IIntelligentDrivingListener;
import com.bitech.platformlib.interfaces.warning.IWarnManagerListener;
import com.bitech.platformlib.manager.IntelligentDrivingManager;
import com.bitech.platformlib.manager.WarningManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.carapi.constants.CarDisplay;
import com.bitech.vehiclesettings.fragment.ConnectFragment;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.service.display.DisplayLifeCycle;
import com.bitech.vehiclesettings.service.earlywarning.WarnDispatcher;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.SendICUTopicsUtil;
import com.bitech.vehiclesettings.view.connect.BtLauncherUIAlert;
import com.bitech.vehiclesettings.view.connect.WifiLauncherUIAlert;
import com.bitech.vehiclesettings.view.connect.WirelessChargingLauncherUIAlert;
import com.bitech.vehiclesettings.view.dialog.GlobalBtDialog;
import com.bitech.vehiclesettings.view.dialog.GlobalWifiDialog;

import java.util.Arrays;

public class VehicleService extends LifecycleService {
    private static final String TAG = VehicleService.class.getSimpleName();
    private static VehicleService instanceSvr;
    private static final String SETTING_KEY = Settings.Global.AIRPLANE_MODE_ON;
    private GlobalSettingsObserver mSettingsObserver;
    private SceneModeObserver sceneModeObserver;
    private VehicleServiceHandler serviceHandler;
    private NewEnergyLifeCycle newEnergyLifeCycle;
    private ConnectLifecycle connectLifecycle;
    private DrivingLifeCycle drivingLifeCycle;
    private ConditionLifeCycle conditionLifeCycle;
    private DisplayLifeCycle displayLifeCycle;
    private NegativeScreenLifecycle negativeScreenLifecycle;
    private DataPointReportLifeCycle dataPointReportLifeCycle;
    private LightInLifeCycle lightInLifeCycle;
    private ConnectFragment connectFragment;
    //wifi全局对话框
    private static GlobalWifiDialog wifiDialog;
    //Bt全局对话框
    private static GlobalBtDialog btDialog;
    private IntelligentDrivingManager ildmanager;
    private WarningManager manager;
    private WarnDispatcher dispatcher = WarnDispatcher.getInstance();


    private WirelessChargingLauncherUIAlert.Builder wirelessChargingLauncherUIAlertBuilder; // 无线充电弹窗
    private WirelessChargingLauncherUIAlert wirelessChargingLauncherDialog;

    // Wifi Launcher弹窗
    private WifiLauncherUIAlert wifiLauncherDialog;
    private BtLauncherUIAlert btLauncherDialog;

    private boolean isDisplayInitialized = false;


    // 状态栏广播
    public static final String ACTION_CLOSE_DIALOG_BAR = "action_close_dialog_bar";
    public static final String ACTION_SWIPE_FROM_LEFT = "global_swipe_from_left";
    public static final String ACTION_SWIPE_FROM_RIGHT = "global_swipe_from_right";
    public static final String ACTION_GO_HOME = "action.GO_HOME";
    public static final String ACTION_GO_PHONE = "action.GO_PHONE";
    public static final String ACTION_GO_APP_LIST = "action.GO_APP_LIST";
    public static final String ACTION_GO_NAVI = "action.GO_NAVI";
    public static final String ACTION_GO_MUSIC = "action.GO_MUSIC";
    public static final String ACTION_CLOSE_DIALOG = "mega_action_close_dialog";


    // SystemUI Action数组
    private static final String[] SYSTEM_UI_ACTIONS = {ACTION_CLOSE_DIALOG_BAR, ACTION_SWIPE_FROM_LEFT, ACTION_SWIPE_FROM_RIGHT, ACTION_GO_HOME, ACTION_GO_PHONE, ACTION_GO_APP_LIST, ACTION_GO_NAVI, ACTION_GO_MUSIC, ACTION_CLOSE_DIALOG};

    // 状态栏弹窗开启状态
    private boolean isWirelessChargingDialogShowing = false;

    public static VehicleService getInstance() {
        return instanceSvr;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        // 初始化工作
        setForeground();
        instanceSvr = this;
        manager = (WarningManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_WARN_MANAGER);
        ildmanager = (IntelligentDrivingManager) BitechCar.getInstance().getServiceManager(getApplicationContext(), BitechCar.INTELLIGENT_DRIVING_MANAGER);
        serviceHandler = new VehicleServiceHandler(this, Looper.getMainLooper());
        newEnergyLifeCycle = new NewEnergyLifeCycle(serviceHandler, this);
        drivingLifeCycle = new DrivingLifeCycle();
        connectLifecycle = new ConnectLifecycle(serviceHandler, this);
        conditionLifeCycle = new ConditionLifeCycle(serviceHandler, this);
        displayLifeCycle = new DisplayLifeCycle();
        negativeScreenLifecycle = new NegativeScreenLifecycle(MyApplication.getContext(), serviceHandler, this);
        dataPointReportLifeCycle = new DataPointReportLifeCycle(this);
        lightInLifeCycle = new LightInLifeCycle(serviceHandler, this);

        getLifecycle().addObserver(newEnergyLifeCycle);
        getLifecycle().addObserver(drivingLifeCycle);
        getLifecycle().addObserver(connectLifecycle);
        getLifecycle().addObserver(conditionLifeCycle);
        getLifecycle().addObserver(displayLifeCycle);
        getLifecycle().addObserver(negativeScreenLifecycle);
        getLifecycle().addObserver(dataPointReportLifeCycle);
        getLifecycle().addObserver(lightInLifeCycle);
        //SystemUI监听
        MyServiceBroadcastReceiver receiver = new MyServiceBroadcastReceiver();
        IntentFilter filter = new IntentFilter(); // 状态栏监听信号
        filter.addAction("com.chery.systemui.action.WIFI_DIALOG_CLICK");
        filter.addAction("com.chery.systemui.action.BLUETOOTH_DIALOG_CLICK");
        filter.addAction("com.chery.systemui.action.WIRELESS_CHARGE_DIALOG_CLICK");

        for (String action : SYSTEM_UI_ACTIONS) {
            filter.addAction(action);
        }
        // 氛围灯监听
        registerReceiver(receiver, filter);
        Log.d(TAG, "onCreate: zhc6whu:SystemUI监听");
    }


    private void initDisplay() {
        if (isDisplayInitialized) {
            Log.d(TAG, "显示已初始化，跳过重复初始化");
            return;
        }

        DisplayPresenter displayPresenter = DisplayPresenter.getInstance();
        boolean displayMode = DisplayPresenter.getDisplayMode();
        if (DisplayPresenter.getAutoMode()) {
            Log.d(TAG, "车机上电 非自动模式");
            if (!displayMode) {
                Log.d(TAG, "车机上电 白天模式");
                displayPresenter.setDisplayMode(CarDisplay.DAY, false);
            } else {
                Log.d(TAG, "车机上电 黑夜模式");
                displayPresenter.setDisplayMode(CarDisplay.NIGHT, false);
            }
        } else {
            Log.d(TAG, "车机上电 自动模式");
            displayPresenter.setDisplayMode(CarDisplay.AUTO, false);
        }
        int zkp = Prefs.get(PrefsConst.DISPLAY_ZKP, CarDisplay.DEFAULT_ZKP_BRIGHTNESS_DAY);
        Log.d(TAG, "车机上电 中控屏亮度: " + zkp);
        displayPresenter.setZKPBrightness(zkp);
        int fp = Prefs.getGlobalValue(PrefsConst.GlobalValue.DISPLAY_FP, PrefsConst.DefaultValue.DISPLAY_FP);
        Log.d(TAG, "车机上电 分屏: " + fp);
        displayPresenter.setFp(fp);
        int zkpAuto = displayPresenter.getZKPAuto();
        Log.d(TAG, "车机上电 中控屏自动亮度: " + zkpAuto);
        displayPresenter.setZKPAuto(zkpAuto);
        boolean timeDisplay = SystemPresenter.getInstance().getTimeDisplay(MyApplication.getContext());
        Log.d(TAG, "车机上电 中控发送仪表Topic 当前时制: " + (!timeDisplay ? 0 : 1));
        SendICUTopicsUtil.sendTopics(SendICUTopicsUtil.Topics.Vehiclesettings_AMPM_SET, !timeDisplay ? 0 : 1);
        isDisplayInitialized = true;
    }


    @SuppressLint("ForegroundServiceType")
    private void setForeground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            final String CHANNEL_ID = "2";
            final CharSequence CHANNEL_NAME = "VehicleService";
            NotificationManager notificationManager = (NotificationManager) this.getSystemService(Context.NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_MIN);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
            Notification notification = new Notification.Builder(this, CHANNEL_ID).build();
            startForeground(2, notification);
        } else {
            Notification notification = new Notification();
            startForeground(2, notification);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 执行服务任务
        Log.d(TAG, "onStartCommand: ");
        initDisplay();
        globalUrlObserver();
        regWarnListen();
        return super.onStartCommand(intent, flags, startId);
    }

    private void regWarnListen() {
        manager.addCallback(TAG, new IWarnManagerListener() {
            @Override
            public void getLHFdoorStsCallback(int sign) {
                Log.d(TAG, "[warn] getLHFdoorStsCallback: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_1, CommonConst.doorOpenWarn.fLWarn, sign);

            }

            @Override
            public void getRHFDoorStsCallback(int sign) {
                Log.d(TAG, "[warn] getRHFDoorStsCallback: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_1, CommonConst.doorOpenWarn.fRWarn, sign);
            }

            @Override
            public void getLHRdoorStsCallback(int sign) {
                Log.d(TAG, "[warn] getLHRdoorStsCallback: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_1, CommonConst.doorOpenWarn.rLWarn, sign);
            }

            @Override
            public void getRHRDoorStsCallback(int sign) {
                Log.d(TAG, "[warn] getRHRDoorStsCallback: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_1, CommonConst.doorOpenWarn.rRWarn, sign);
            }

            /**
             * 转向
             * @param sign
             */
            @Override
            public void getCsa2TurnSiglvrCmd(int sign) {
                Log.d(TAG, "[warn] getCsa2TurnSiglvrCmd: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_8, CommonConst.TurnSigL.all, sign);
            }

            /**
             * 倒车雷达辅助
             * @param sign
             */
            @Override
            public void getPdcFrontWarning(int sign) {
                Log.d(TAG, "[warn] getPdcFrontWarning: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_7, CommonConst.Pdc.frontWarning, sign);
            }

            /**
             * 倒车雷达辅助
             * @param sign
             */
            @Override
            public void getPdcRearWarning(int sign) {
                Log.d(TAG, "[warn] getPdcRearWarning: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_7, CommonConst.Pdc.rearWarning, sign);
            }

            /**
             * 倒车雷达辅助
             * @param sign
             */
            @Override
            public void getPdcLeftWarning(int sign) {
                Log.d(TAG, "[warn] getPdcLeftWarning: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_7, CommonConst.Pdc.leftWarning, sign);
            }

            /**
             * 倒车雷达辅助
             * @param sign
             */
            @Override
            public void getPdcRightWarning(int sign) {
                Log.d(TAG, "[warn] getPdcRightWarning: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_7, CommonConst.Pdc.rightWarning, sign);
            }

            /**
             * Dow 后左
             * @param sign
             */
            @Override
            public void getRLCR_1_DOWWarn(int sign) {
                Log.d(TAG, "[warn] getRLCR_1_DOWWarn: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_9, CommonConst.Dow.left, sign);
            }

            /**
             * Dow 后右
             * @param sign
             */
            @Override
            public void getRRCR_1_DOWWarn(int sign) {
                Log.d(TAG, "[warn] getRRCR_1_DOWWarn: " + sign);
                dispatcher.dispatch(CommonConst.priority.priority_9, CommonConst.Dow.right, sign);
            }

        });
        manager.registerListener();
        // 智驾预警
        ildmanager.addCallback(TAG, new IIntelligentDrivingListener() {
            /**
             * @param lksModValue             车道偏离辅助 ADAS_enum_LKSMod
             * @param lksStsValue             车道偏离辅助 ADAS_enum_LKSSts:0x3: Failure（故障，开关OFF）
             * @param elkStsValue             紧急车道保持 ADAS_enum_ELKSts
             * @param lksLeftTrackingStValue  车道左侧偏离 ADAS_enum_LKSLeftTrackingSt
             * @param lksRightTrackingStValue 车道右侧偏离 ADAS_enum_LKSRightTrackingSt
             */
            @Override
            public void onNtfLSSInfoDrvg2(int lksModValue, int lksStsValue, int elkStsValue, int lksLeftTrackingStValue, int lksRightTrackingStValue) {
                if (lksLeftTrackingStValue == CarLight.TrackingSt.WARNING || (lksRightTrackingStValue == CarLight.TrackingSt.WARNING)) {
                    if (lksLeftTrackingStValue == CarLight.TrackingSt.WARNING) {
                        dispatcher.dispatch(CommonConst.priority.priority_11, CommonConst.trackingWarn.left, lksLeftTrackingStValue);
                    }
                    if (lksRightTrackingStValue == CarLight.TrackingSt.WARNING) {
                        dispatcher.dispatch(CommonConst.priority.priority_11, CommonConst.trackingWarn.right, lksRightTrackingStValue);
                    }
                } else {
                    dispatcher.dispatch(CommonConst.priority.priority_11, CommonConst.trackingWarn.all, 0);
                }
            }

            /**
             * @param fcwStsValue      前向碰撞预警 ADAS_enum_FcwSts
             * @param fcwWarnDistValue 前向碰撞预警 ADAS_enum_AebSts
             * @param fcwAcitveStValue 前碰预警信号 ADAS_enum_FcwAcitveSt
             * @param aebStsValue      自动紧急制动 ADAS_enum_AebSts
             * @param fctaWarnTypeSts  前方交通穿行辅助 ADAS_enum_FCTAWarnTypeSts
             */
            @Override
            public void onNtfActiveSafetyFcnInfoAct2(int fcwStsValue, int fcwWarnDistValue, int fcwAcitveStValue, int aebStsValue, int fctaWarnTypeSts) {
                // （ADAS_enum_FcwAcitveSt=0x2/0x3 触发）
                if (fcwAcitveStValue == CarLight.FcwAcitveSt.LEVEL_2 || fcwAcitveStValue == CarLight.FcwAcitveSt.LEVEL_3) {
                    dispatcher.dispatch(CommonConst.priority.priority_12, CommonConst.trackingWarn.left, fcwAcitveStValue);
                } else {
                    dispatcher.dispatch(CommonConst.priority.priority_12, CommonConst.fcwAcitveWarn.all, 0);
                }
            }
        });

    }

    private void globalUrlObserver() {
        // 获取要监听的 URI 创建观察者实例 注册观察者
        Uri globalUri = Settings.Global.getUriFor(PrefsConst.GlobalValue.L_RHYTHM_CADENC);
        Uri swUri = Settings.Global.getUriFor(PrefsConst.GlobalValue.L_LIGHT_SW);
        Uri recoverUri = Settings.Global.getUriFor(PrefsConst.GlobalValue.L_LIGHT_WARNING_TRIGGERED);
        if (mSettingsObserver == null) {
            mSettingsObserver = new GlobalSettingsObserver(this, PrefsConst.GlobalValue.L_RHYTHM_CADENC, new Handler(Looper.myLooper()));
            this.getContentResolver().registerContentObserver(globalUri, false, mSettingsObserver);
            this.getContentResolver().registerContentObserver(swUri, false, mSettingsObserver);
            this.getContentResolver().registerContentObserver(recoverUri, false, mSettingsObserver);
        }
        Uri scenemodeUri = Settings.Global.getUriFor(PrefsConst.GlobalValue.L_SCENEMODE_ATMOSPHERE);
        if (sceneModeObserver == null) {
            sceneModeObserver = new SceneModeObserver(this, PrefsConst.GlobalValue.L_SCENEMODE_ATMOSPHERE, new Handler(Looper.myLooper()));
            this.getContentResolver().registerContentObserver(scenemodeUri, false, sceneModeObserver);
        }
    }

    // 新增一个标志位
    private boolean isDialogClosedByBarClick = false;

    private class MyServiceBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            // 在这里处理接收到的 Intent 相关逻辑
            String action = intent.getAction();
            if ("com.chery.systemui.action.WIFI_DIALOG_CLICK".equals(action)) {
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    int offsetX = bundle.getInt("offsetX");
                    int offsetY = bundle.getInt("offsetY");
                    wifiLauncherDialog = new WifiLauncherUIAlert(context);
                    wifiLauncherDialog.setGlobalAlert(true);
                    wifiLauncherDialog.setPosition(offsetX, offsetY);
                    wifiLauncherDialog.show();
                }
            } else if ("com.chery.systemui.action.BLUETOOTH_DIALOG_CLICK".equals(action)) {
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    int offsetX = bundle.getInt("offsetX");
                    int offsetY = bundle.getInt("offsetY");
                    btLauncherDialog = new BtLauncherUIAlert(context);
                    btLauncherDialog.setGlobalAlert(true);
                    btLauncherDialog.setPosition(offsetX, offsetY);
                    btLauncherDialog.show();
                }
            } else if ("com.chery.systemui.action.WIRELESS_CHARGE_DIALOG_CLICK".equals(action)) {
                if (isDialogClosedByBarClick) return;
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    int offsetX = bundle.getInt("offsetX");
                    int offsetY = bundle.getInt("offsetY");
                    Log.d(TAG, "onReceive: offsetX:" + offsetX);
                    Log.d(TAG, "onReceive: offsetY:" + offsetY);
                    // 对 offsetX 进行相应处理
                    if (wirelessChargingLauncherUIAlertBuilder == null) {
                        wirelessChargingLauncherUIAlertBuilder = new WirelessChargingLauncherUIAlert.Builder(context);
                    }
                    wirelessChargingLauncherUIAlertBuilder.setGlobalAlert(true);
                    wirelessChargingLauncherDialog = wirelessChargingLauncherUIAlertBuilder.create();
                    wirelessChargingLauncherUIAlertBuilder.setPosition(offsetX, offsetY);
                    wirelessChargingLauncherDialog.show();

                    // 发送弹窗显示广播
                    Intent showIntent = new Intent("com.chery.systemui.action.WIRELESS_CHARGE_DIALOG_SHOW");
                    context.sendBroadcast(showIntent);
                }
            }

            // 检查action是否在关闭对话框的action数组中
            if (Arrays.asList(SYSTEM_UI_ACTIONS).contains(action)) {
                if (wirelessChargingLauncherDialog != null && wirelessChargingLauncherDialog.isShowing()) {
                    wirelessChargingLauncherDialog.dismiss();
                    // 发送弹窗关闭广播
                    Intent dismissIntent = new Intent("com.chery.systemui.action.WIRELESS_CHARGE_DIALOG_DISMISS");
                    context.sendBroadcast(dismissIntent);
                    isDialogClosedByBarClick = true;
                } else {
                    isDialogClosedByBarClick = false;
                }
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mSettingsObserver != null) {
            getContentResolver().unregisterContentObserver(mSettingsObserver);
            mSettingsObserver = null;
        }
        // 清理工作
        instanceSvr = null;
        getLifecycle().removeObserver(newEnergyLifeCycle);
        getLifecycle().removeObserver(drivingLifeCycle);
        getLifecycle().removeObserver(connectLifecycle);
        getLifecycle().removeObserver(conditionLifeCycle);
        getLifecycle().removeObserver(displayLifeCycle);
        getLifecycle().removeObserver(negativeScreenLifecycle);
        getLifecycle().removeObserver(dataPointReportLifeCycle);

        // 确保在 Service 销毁时关闭对话框
        if (wifiDialog != null && wifiDialog.isShowing()) {
            wifiDialog.dismiss();
        }
        if (btDialog != null && btDialog.isShowing()) {
            btDialog.dismiss();
        }
    }

    @Override
    public IBinder onBind(@NonNull Intent intent) {
        // 如果服务需要绑定，则返回一个IBinder实例
        // 如果不需要绑定，则返回null
        super.onBind(intent);
        return null;
    }

}