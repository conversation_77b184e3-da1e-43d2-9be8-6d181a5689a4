package com.bitech.vehiclesettings.viewmodel

import android.text.SpannableString
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.interfaces.newenergy.INewEnergyListener
import com.bitech.platformlib.manager.NewEnergyManager
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.base.kt.BaseViewModel
import com.bitech.vehiclesettings.bean.report.Content
import com.bitech.vehiclesettings.bean.report.DataPoint
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy
import com.bitech.vehiclesettings.service.DataPointReportLifeCycle
import com.bitech.vehiclesettings.utils.BitUtils
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.LogUtil
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lion.datapoint.log.LogDataUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * @Description: 新能源ViewModel.
 **/
class NewEnergyViewModel : BaseViewModel() {

    // 新能源管理对象
    private var carNewEnergyManager: NewEnergyManager = newEnergyManager

    // 能量流状态 LiveData
    val energyFlowStatusLiveData = MutableLiveData(0)

    // 慢充枪/放电枪连接状态 LiveData
    val slowGunStatusLiveData = MutableLiveData<Int>()

    // 快充枪连接状态 LiveData
    val fastGunStatusLiveData = MutableLiveData<Boolean>()

    // 充电状态 LiveData
    val chargingStatusLiveData = MutableLiveData<Int>()

    // 电池状态 LiveData
    val batteryStatusLiveData = MutableLiveData(0)

    // 充电电流 LiveData
    val bmsPackCurrentDisLiveData = MutableLiveData<Float>()

    // 充电电压 LiveData
    val bmsPackVoltageDisLiveData = MutableLiveData<Float>()

    // 当前放电功率 LiveData
    val dischargingPowerLiveData = MutableLiveData<Float>()

    // 最大放电功率 LiveData
    val dischargingPowerLimitLiveData = MutableLiveData<Float>()

    // 剩余充电时间 LiveData
    val leftChargingTimeLiveData = MutableLiveData<Int>()

    // 当前电量SOC LiveData
    val currentSOCLiveData = MutableLiveData(0)

    // 充电停止SOC LiveData
    val chargeStopSOCLiveData = MutableLiveData(0)

    // 放电停止SOC LiveData
    val dischargeStopSOCLiveData = MutableLiveData(0)

    // 对外放电功能开启状态 LiveData
    val dischargeStatusLiveData = MutableLiveData(false)

    // 能量回收强度LiveData
    val energyRecoveryLiveData = MutableLiveData(CarNewEnergy.EnergyRecoverySts.LOW_LEVEL)

    // 行驶里程显示
    val mileageDisplayLiveData = MutableLiveData(0)

    // 纯电续航显示方式
    val pureElectricDisplayLiveData = MutableLiveData(0)

    // 续航工况显示方式
    val rangeConditionDisplayLiveData = MutableLiveData(0)

    // 预约充电开关状态
    val bookChargeSwitchLiveData = MutableLiveData(false)

    // 预约充电等待状态
    val bookChargeStatusLiveData = MutableLiveData(0)

    // 预约出行状态
    val bookTravelLiveData = MutableLiveData(false)

    // 驻车发电状态
    val forceChargeModeLiveData = MutableLiveData(false)

    // 电子锁闭锁开关状态 LiveData
    var electronicLockStatusLiveData = MutableLiveData<Boolean>()

    // 档位状态 LiveData
    var gearStatusLiveData = MutableLiveData<Int>()

    //功率文字
    val tvChargePower = ObservableField<SpannableString>()

    //最大功率文字
    val tvDischargePowerLimit = ObservableField<SpannableString>()

    //电流文字
    val tvElectricCurrent = ObservableField<SpannableString>()

    //电压文字
    val tvVoltage = ObservableField<SpannableString>()

    //操作按钮文字: 停止充电/解锁
    val textFunctionButton = ObservableField<String>()

    //操作按钮显示状态
    val functionButtonVisible = ObservableBoolean()

    //预计剩余时间
    val textRemainingTime = ObservableField<String>()

    //预约充电内容文字
    var bookChargeHour = ObservableInt(22)
    var bookChargeMinutes = ObservableInt(0)
    var bookChargeDuration = ObservableInt(6 * 60)
    val textBookCharge = object : ObservableField<String>(
        bookChargeDuration, bookChargeHour, bookChargeMinutes
    ) {
        override fun get(): String {
            if (bookChargeSwitchLiveData.value == false) {
                return ""
            }
            return "每天  ${bookChargeHour.get().formatWithZero()}" + ":${
                bookChargeMinutes.get().formatWithZero()
            }  ${(bookChargeDuration.get() / 60).coerceAtLeast(1)}小时"
        }
    }

    //预约出行内容文字，默认7:00
    var bookTravelHour = ObservableInt(7)
    var bookTravelMinutes = ObservableInt(0)
    var bookTravelWeek = ObservableInt(127)
    val textBookTravel = object : ObservableField<String>(
        bookTravelWeek, bookTravelHour, bookTravelMinutes
    ) {
        override fun get(): String {
            if (bookTravelLiveData.value == false) {
                return ""
            }
            val weekValue = bookTravelWeek.get()
            val weekStr = StringBuilder().apply {
                append(if (BitUtils.getBit(weekValue, 0) == 1) "周一 " else "")
                append(if (BitUtils.getBit(weekValue, 1) == 1) "周二 " else "")
                append(if (BitUtils.getBit(weekValue, 2) == 1) "周三 " else "")
                append(if (BitUtils.getBit(weekValue, 3) == 1) "周四 " else "")
                append(if (BitUtils.getBit(weekValue, 4) == 1) "周五 " else "")
                append(if (BitUtils.getBit(weekValue, 5) == 1) "周六 " else "")
                append(if (BitUtils.getBit(weekValue, 6) == 1) "周日" else "")
            }
            LogUtil.d(TAG, "weekValue = ${Integer.toBinaryString(weekValue)} , weekStr = $weekStr")
            if (weekStr.isEmpty()) {
                return ""
            }
            return bookTravelHour.get().formatWithZero() + ":${
                bookTravelMinutes.get().formatWithZero()
            }  $weekStr"
        }
    }

    //充放电类型：充电中、放电中、快充枪已连接、慢充枪已连接、放电枪已连接、充电已完成（5秒后隐藏）
    val textChargeType = ObservableField<String>()

    //对外放电功能开启
    val dischargeBtnEnable = ObservableBoolean()

    // 属性监听回调
    private val carPropertyCallback: INewEnergyListener by lazy {
        object : INewEnergyListener {

            override fun onHcuEnergyFlow(value: Int) {
                energyFlowStatusLiveData.postValue(value.coerceAtLeast(0))
            }

            override fun onBmsPackCurrentDis(value: Float) {
                bmsPackCurrentDisLiveData.postValue(value.coerceIn(-2047f, 2047f))
            }

            override fun onBmsPackVoltageDis(value: Float) {
                bmsPackVoltageDisLiveData.postValue(value.coerceIn(0f, 1000f))
            }

            override fun onChargeGunStatus(value: Int) {
                slowGunStatusLiveData.postValue(value)
            }

            override fun onChgWireConnectLightSts(value: Int) {
                fastGunStatusLiveData.postValue(value == CarNewEnergy.FastGunConnectSts.CHARGE_CONNECTED)
            }

            override fun onBmsChargeSts(value: Int) {
                chargingStatusLiveData.postValue(value)
            }

            override fun onDischargeSts(value: Int) {
                dischargeStatusLiveData.postValue(value == CarNewEnergy.V2LFunctionSts.ON)
            }

            override fun onRemainChgTime(value: Int) {
                leftChargingTimeLiveData.postValue(value)
            }

            override fun onBatteryLevel(value: Int) {
                currentSOCLiveData.postValue(value.coerceIn(0, 100))
            }

            override fun onChargeSocThreshold(value: Int) {
                LogUtil.d(TAG, "充电上限值: $value")
                chargeStopSOCLiveData.postValue(value.coerceIn(80, 100))
            }

            override fun onDischargeSocLowThreshold(value: Int) {
                LogUtil.d(TAG, "放电下限值: $value")
                dischargeStopSOCLiveData.postValue(value.coerceIn(30, 100))
            }

            override fun onEnergyRecoveryMode(value: Int) {
                energyRecoveryLiveData.postValue(
                    value.coerceIn(
                        CarNewEnergy.EnergyRecoverySts.LOW_LEVEL, CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL
                    )
                )
            }

            override fun onChargeGunELockSwitch(value: Int) {
                electronicLockStatusLiveData.postValue(value == CarNewEnergy.ElectricLockSts.LOCK)
            }

            override fun onForceChargeMode(value: Int) {
                forceChargeModeLiveData.postValue(value == CarNewEnergy.ForceChargeSts.ACTIVE)
            }

            override fun onBookTravelStatus(value: Int) {
                val statusOn = value == CarNewEnergy.BookTravelSts.ON
                bookTravelLiveData.postValue(statusOn)
                if (statusOn) {
                    execute { getBookTravelWeekHourMin() }
                } else {
                    textBookTravel.set("")
                    textBookTravel.notifyChange()
                }
            }

            override fun onBookTravelWeek(value: Int) {
                bookTravelWeek.set(value.coerceAtLeast(0))
            }

            override fun onBookTravelHour(value: Int) {
                bookTravelHour.set(value.coerceIn(0, 23))
            }

            override fun onBookTravelMinutes(value: Int) {
                bookTravelMinutes.set(value.coerceIn(0, 59))
            }

            override fun onBookChargeSwitch(value: Int) {
                val statusOn = value == CarNewEnergy.BookChargeSts.ON
                bookChargeSwitchLiveData.postValue(statusOn)
                if (statusOn) {
                    execute { getBookChargeDurHourMin() }
                } else {
                    textBookCharge.set("")
                    textBookCharge.notifyChange()
                }
            }

            override fun onBookChargeStatus(value: Int) {
                bookChargeStatusLiveData.postValue(value)
            }

            override fun onBookChargeDuration(value: Int) {
                bookChargeDuration.set(value.coerceIn(60, 1380))
            }

            override fun onBookChargeTimeHour(value: Int) {
                bookChargeHour.set(value.coerceIn(0, 23))
            }

            override fun onBookChargeTimeMinutes(value: Int) {
                bookChargeMinutes.set(value.coerceIn(0, 59))
            }

            override fun onDischargePower(value: Float) {
                dischargingPowerLiveData.postValue(value.coerceIn(0f, 25.5f))
            }

            override fun onDischargePowerLimit(value: Float) {
                dischargingPowerLimitLiveData.postValue(value.coerceIn(0f, 25.5f))
            }

            override fun onBmsBatteryState(value: Int) {
                batteryStatusLiveData.postValue(value.coerceAtLeast(0))
            }

            override fun onPRNDGearAct(value: Int) {
                gearStatusLiveData.postValue(value.coerceAtLeast(0))
            }

            override fun onMileageDisplay(value: Int) {
                mileageDisplayLiveData.postValue(value.coerceAtLeast(0))
            }

            override fun onPureElectricDisplay(value: Int) {
                pureElectricDisplayLiveData.postValue(value.coerceAtLeast(0))
            }

            override fun onRangeConditionDisplay(value: Int) {
                rangeConditionDisplayLiveData.postValue(value.coerceAtLeast(0))
            }
        }
    }

    /**
     * ViewModel初始化.
     */
    init {
        viewModelScope.launch(Dispatchers.Default) {
            // 注册快捷控制监听
            LogUtil.i(TAG, "init : $TAG")
            carNewEnergyManager.addCallback(TAG, carPropertyCallback)
        }
    }

    /**
     * 快捷控制页面初始数据初始化.
     *
     */
    fun initData() {
        viewModelScope.launch(Dispatchers.Default) {
            LogUtil.d(TAG, "initData: ")
            // 能量流状态
            getEnergyFlowStatus()
            // 充电状态
            getChargingStatus()
            getBmsBatteryState()
            // 放电状态
            getDischargingStatus()
            // 快充电枪插入
            getFastGunStatus()
            // 慢充或放电枪插入
            getSlowGunStatus()
            // 充电电流、电压
            getChargingCurrent()
            getChargingVoltage()
            // 放电功率
            getDischargingPower()
            getDischargingPowerLimit()
            // 充电剩余时间
            getLeftChargingTime()
            // SOC电量
            getCurrentSOCValue()
            // 电子锁状态
            getElectronicLockStatus()
            // 充电停止SOC
            getChargeStopSOCValue()
            // 放电停止SOC
            getDischargeStopSOCValue()
            // 能量回收强度
            getEnergyRecoveryType()
            // 预约充电
            getBookChargeSwitchStatus()
            // 预约出行
            getBookTravelStatus()
            //档位状态
            getGearStatue()
            // 驻车发电
            getForceEvChargeMode()
            // 行驶里程显示
            getMileageDisplay()
            // 纯电续航显示方式
            getPureElectricDisplay()
            // 续航工况显示
            getRangeConditionDisplay()
        }
    }

    /**
     * 获取能量流状态
     */
    fun getEnergyFlowStatus() {
        val status = carNewEnergyManager.hcuEnergyFlow
        LogUtil.i(TAG, "getEnergyFlowStatus : status = $status")
        energyFlowStatusLiveData.postValue(status.coerceAtLeast(0))
    }

    /**
     * 获取慢充枪连接状态.
     */
    fun getSlowGunStatus() {
        val status = carNewEnergyManager.chargeGunStatus
        LogUtil.i(TAG, "getSlowGunStatus : status = $status")
        slowGunStatusLiveData.postValue(status)
    }

    /**
     * 获取快充枪连接状态.
     *
     */
    fun getFastGunStatus() {
        val status = carNewEnergyManager.chgWireConnectLightSts
        LogUtil.i(TAG, "getFastGunStatus : status = $status")
        fastGunStatusLiveData.postValue(status == CarNewEnergy.FastGunConnectSts.CHARGE_CONNECTED)
    }

    /**
     * 获取充电状态.
     *
     */
    fun getChargingStatus() {
        val status = carNewEnergyManager.bmsChargeSts
        LogUtil.i(TAG, "getChargingStatus: $status")
        chargingStatusLiveData.postValue(status)
    }

    /**
     * 获取电池状态.
     *
     */
    fun getBmsBatteryState() {
        val status = carNewEnergyManager.bmsBatteryState
        LogUtil.i(TAG, "getBmsBatteryState: $status")
        batteryStatusLiveData.postValue(status)
    }

    /**
     * 获取充电电流
     *
     */
    fun getChargingCurrent() {
        val value = carNewEnergyManager.bmsPackCurrentDis
        LogUtil.i(TAG, "getChargingCurrent: $value")
        bmsPackCurrentDisLiveData.postValue(value.coerceIn(-2047f, 2047f))
    }

    /**
     * 获取充电电压
     *
     */
    fun getChargingVoltage() {
        val value = carNewEnergyManager.bmsPackVoltageDis
        LogUtil.i(TAG, "getChargingVoltage: $value")
        bmsPackVoltageDisLiveData.postValue(value.coerceIn(0f, 1000f))
    }

    /**
     * 获取放电功率.
     */
    fun getDischargingPower() {
        val value = carNewEnergyManager.dischargePower
        LogUtil.i(TAG, "getDischargingPower : value = $value")
        dischargingPowerLiveData.postValue(value.coerceIn(0f, 25.5f))
    }

    /**
     * 获取最大放电功率.
     */
    fun getDischargingPowerLimit() {
        val value = carNewEnergyManager.dischargePwrLimit
        LogUtil.i(TAG, "getDischargingPowerLimit : value = $value")
        dischargingPowerLimitLiveData.postValue(value.coerceIn(0f, 25.5f))
    }

    /**
     * 获取剩余充电时间.
     */
    fun getLeftChargingTime() {
        val minute = carNewEnergyManager.remainChgTime
        LogUtil.i(TAG, "getLeftChargingTime : minute = $minute")
        leftChargingTimeLiveData.postValue(minute)
    }

    /**
     * 获取当前电量SOC.
     *
     */
    fun getCurrentSOCValue() {
        val value = carNewEnergyManager.batteryLevel
        LogUtil.i(TAG, "getCurrentSOCValue = $value")
        currentSOCLiveData.postValue(value.coerceIn(0, 100))
    }

    /**
     * 充电停止SOC获取.
     */
    fun getChargeStopSOCValue() {
        val value = carNewEnergyManager.chargeSocThreshold
        LogUtil.i(TAG, "getChargeStopSOCValue = $value")
        chargeStopSOCLiveData.postValue(value.coerceIn(80, 100))
    }

    /**
     * 放电停止SOC获取.
     *
     */
    fun getDischargeStopSOCValue() {
        val value = carNewEnergyManager.getDischargeSocLowThreshold(MyApplication.getContext())
        LogUtil.i(TAG, "getDischargeStopSOCValue = $value")
        dischargeStopSOCLiveData.postValue(value.coerceIn(30, 100))
    }

    /**
     * 根据对外放电开关状态，下发对外放电.
     *
     */
    fun setDischargeSwitchStatus(isChecked: Boolean) {
        LogUtil.i(TAG, "setDischargeSwitchStatus : isChecked = $isChecked")
        if (dischargeStatusLiveData.value == isChecked) {
            LogUtil.i(TAG, "setDischargeSwitchStatus value ==")
            return
        }
        carNewEnergyManager.setDischargeSwitch(
            if (isChecked) CarNewEnergy.V2LSwitchSts.ON else CarNewEnergy.V2LSwitchSts.OFF
        )
        delayAndExecute { getDischargingStatus() }
    }

    /**
     * 获取对外放电功能状态.
     *
     */
    fun getDischargingStatus() {
        // 获取对外放电功能开启状态
        val status = carNewEnergyManager.dischargeSts
        LogUtil.i(TAG, "getDischargingStatus : status = $status")
        dischargeStatusLiveData.postValue(status == CarNewEnergy.V2LFunctionSts.ON)
    }

    /**
     * 获取能量回收强度类型.
     *
     */
    fun getEnergyRecoveryType() {
        val value = carNewEnergyManager.energyRecoveryMode
        LogUtil.i(TAG, "getEnergyRecoveryType : $value")
        energyRecoveryLiveData.postValue(
            value.coerceIn(
                CarNewEnergy.EnergyRecoverySts.LOW_LEVEL, CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL
            )
        )
    }

    /**
     * 设置能量回收强度类型.
     *
     * 0x0:Not Active
     * 0x1:Off
     * 0x2:Low
     * 0x3:Middle
     * 0x4:High
     */
    fun setEnergyRecoveryType(index: Int) {
        val value = index + 2
        LogUtil.i(TAG, "setEnergyRecoveryType : type = $value")
        carNewEnergyManager.energyRecoveryMode = value
        energyRecoveryLiveData.postValue(value)
        delayAndExecute { getEnergyRecoveryType() }
    }

    /**
     * 获取行驶里程显示方式的设置（总里程/HEV/EV）
     * value: （默认值 0）
     * 0：总里程
     * 1：HEV
     * 2：EV
     *
     */
    fun getMileageDisplay() {
        val value = carNewEnergyManager.mileageDisplay.coerceAtLeast(0)
        LogUtil.i(TAG, "getMileageDisplay : $value")
        //UI转换
        mileageDisplayLiveData.postValue(value)
    }

    /**
     * 设置行驶里程显示方式的设置（总里程/HEV/EV）
     *
     *
     */
    fun setMileageDisplay(index: Int) {
        LogUtil.i(TAG, "setMileageDisplay : index = $index")
        val value = if (index == 2) 0 else if (index == 0) 2 else index
        carNewEnergyManager.mileageDisplay = value
        mileageDisplayLiveData.postValue(value)
        delayAndExecute { getMileageDisplay() }
    }

    /**
     * 获取纯电续航显示方式的设置（电量百分比/电续航⾥程）
     * value: （默认值 0）
     * 0：电量百分比
     * 1：电续航里程
     */
    fun getPureElectricDisplay() {
        val value = carNewEnergyManager.pureElectricDisplay.coerceAtLeast(0)
        LogUtil.i(TAG, "getPureElectricDisplay : $value")
        pureElectricDisplayLiveData.postValue(value)
    }

    /**
     * 设置纯电续航显示方式的设置（电量百分比/电续航⾥程）
     *
     */
    fun setPureElectricDisplay(index: Int) {
        LogUtil.i(TAG, "setPureElectricDisplay : index = $index")
        carNewEnergyManager.pureElectricDisplay = index
        pureElectricDisplayLiveData.postValue(index)
        delayAndExecute { getPureElectricDisplay() }
    }

    /**
     * 获取续航工况显示方式的设置（CLTC/WLTC/动态）
     * 默认值：WLTC
     * 0x0:Default
     * 0x1:Dynamics
     * 0x2:CLTC
     * 0x3:WLTC
     *
     */
    fun getRangeConditionDisplay() {
        val value = carNewEnergyManager.rangeConditionDisplay.coerceAtLeast(0)
        LogUtil.i(TAG, "getRangeConditionDisplay : $value")
        //UI转换
        rangeConditionDisplayLiveData.postValue(value)
    }

    /**
     * 设置续航工况显示方式的设置（CLTC=0->2/WLTC=1->3/动态=2->1）
     */
    fun setRangeConditionDisplay(index: Int) {
        LogUtil.i(TAG, "setRangeConditionDisplay : index = $index")
        val value = if (index == 0) 2 else if (index == 1) 3 else 1
        carNewEnergyManager.rangeConditionDisplay = value
        rangeConditionDisplayLiveData.postValue(value)
        delayAndExecute { getRangeConditionDisplay() }
    }

    /**
     * 下发电子锁解锁.
     *
     */
    fun setElectricLockUnlock() {
        LogUtil.i(TAG, "setElectricLockUnlock")
        carNewEnergyManager.chargeGunELockSwitch = CarNewEnergy.ElectricLockSet.UNLOCK
    }

    /**
     *停止充电
     *
     */
    fun setStopCharge() {
        LogUtil.i(TAG, "setStopCharge")
        carNewEnergyManager.setStopCharge(1)
    }

    /**
     * 获取电子锁闭锁开关状态.
     */
    fun getElectronicLockStatus() {
        //获取电子锁闭锁开关状态
        val status = carNewEnergyManager.chargeGunELockSwitch
        LogUtil.i(TAG, "getElectronicLockStatus : status = $status")
        electronicLockStatusLiveData.postValue(status == CarNewEnergy.ElectricLockSts.LOCK)
    }

    /**
     * 获取档位.
     */
    fun getGearStatue() {
        val value = carNewEnergyManager.drivingInfoGear
        LogUtil.i(TAG, "getGearStatue = $value")
        gearStatusLiveData.postValue(value)
    }

    /**
     * 获取预约充电状态.
     *
     */
    fun getBookChargeSwitchStatus() {
        val statusOn = carNewEnergyManager.bookChargeSwitch == CarNewEnergy.BookChargeSts.ON
        LogUtil.i(TAG, "getBookChargeSwitchStatus = $statusOn")
        bookChargeSwitchLiveData.postValue(statusOn)
        if (statusOn) {
            getBookChargeDurHourMin()
        }
    }

    /**
     * 设置预约充电
     *
     */
    fun setBookChargeSwitch(checked: Boolean) {
        LogUtil.i(TAG, "setBookChargeSwitch = $checked")
        if (bookChargeSwitchLiveData.value == checked) {
            LogUtil.i(TAG, "setBookChargeSwitch value ==")
            return
        }
        carNewEnergyManager.bookChargeSwitch = if (checked) CarNewEnergy.BookChargeReq.BOOK_SET
        else CarNewEnergy.BookChargeReq.CANCEL_BOOK
        delayAndExecute { getBookChargeSwitchStatus() }
    }

    /**
     * 获取预约充电时间-小时、分钟、时长
     *
     */
    fun getBookChargeDurHourMin() {
        val duration = carNewEnergyManager.bookChargeDuration
        //默认22:00 6小时
        bookChargeDuration.set(if (duration <= 0) 6 * 60 else duration.coerceIn(60, 1380))
        val hour = carNewEnergyManager.bookChargeTimeHour
        bookChargeHour.set(if (hour < 0) 22 else hour.coerceIn(0, 23))
        val minutes = carNewEnergyManager.bookChargeTimeMinutes
        bookChargeMinutes.set(if (minutes < 0) 0 else minutes.coerceIn(0, 59))
        //刷新文字
        textBookCharge.notifyChange()
        LogUtil.i(
            TAG,
            "getBookCharge duration:${bookChargeDuration.get()}, hour:${bookChargeHour.get()}, min:${bookChargeMinutes.get()}"
        )
    }

    /**
     * 设置预约充电时间-小时、分钟、时长
     *
     */
    fun setBookChargeDurHourMin(duration: Int, hour: Int, minute: Int) {
        LogUtil.i(TAG, "setBookChargeDurHourMin = $duration, $hour, $minute")
        //根据对手件反馈
//        bookChargeDuration.set(duration)
//        bookChargeHour.set(hour)
//        bookChargeMinutes.set(minute)

        carNewEnergyManager.bookChargeDuration = duration * 60
        carNewEnergyManager.setBookChargeStartTimeHour(hour)
        carNewEnergyManager.setBookChargeStartTimeMinute(minute)
    }

    /**
     * 获取预约出行状态
     *
     */
    fun getBookTravelStatus() {
        val statusOn = carNewEnergyManager.rsvTravelSts == CarNewEnergy.BookTravelSts.ON
        LogUtil.i(TAG, "getBookTravelStatus = $statusOn")
        bookTravelLiveData.postValue(statusOn)
        if (statusOn) {
            getBookTravelWeekHourMin()
        }
    }

    /**
     * 设置预约出行状态
     *
     */
    fun setBookTravelStatus(checked: Boolean) {
        LogUtil.i(TAG, "setBookTravelStatus = $checked")
        if (bookTravelLiveData.value == checked) {
            LogUtil.i(TAG, "setBookTravelStatus value ==")
            return
        }
        carNewEnergyManager.setRsvTravel(
            if (checked) CarNewEnergy.BookTravelSts.ON
            else CarNewEnergy.BookTravelSts.OFF
        )
        delayAndExecute { getBookTravelStatus() }
    }

    /**
     * 获取预约出行时间-周、小时、分钟
     * 出行日期: 周一周日,默认周一~周日;
     * 出行时间: 00-23时,00-59分,出厂默认7:00
     */
    fun getBookTravelWeekHourMin() {
        val rsvTravelWeek = carNewEnergyManager.rsvTravelWeek
        bookTravelWeek.set(if (rsvTravelWeek <= 0) 127 else rsvTravelWeek.coerceAtLeast(0))
        val rsvTravelHour = carNewEnergyManager.rsvTravelHour
        bookTravelHour.set(if (rsvTravelHour < 0) 7 else rsvTravelHour.coerceIn(0, 23))
        val rsvTravelMinute = carNewEnergyManager.rsvTravelMinute
        bookTravelMinutes.set(if (rsvTravelMinute < 0) 0 else rsvTravelMinute.coerceIn(0, 59))
        //刷新文字
        textBookTravel.notifyChange()
        LogUtil.i(
            TAG,
            "getBookTravel week:${bookTravelWeek.get()}, hour:${bookTravelHour.get()}, min:${bookTravelMinutes.get()}"
        )
    }

    /**
     * 设置预约出行时间-周、小时、分钟
     *
     */
    fun setBookTravelWeekHourMin(week: Int, hour: Int, minute: Int) {
        LogUtil.i(TAG, "setBookTravelWeekHourMin = $week, $hour, $minute")
        //根据对手件反馈
//        bookTravelWeek.set(week)
//        bookTravelHour.set(hour)
//        bookTravelMinutes.set(minute)

        carNewEnergyManager.rsvTravelWeek = week
        carNewEnergyManager.rsvTravelHour = hour
        carNewEnergyManager.rsvTravelMinute = minute
    }

    /**
     * 获取驻车发电状态.
     *
     */
    fun getForceEvChargeMode() {
        val type = carNewEnergyManager.forceEvChargeMode == CarNewEnergy.ForceChargeSts.ACTIVE
        LogUtil.i(TAG, "getForceEvChargeMode = $type")
        forceChargeModeLiveData.postValue(type)
    }

    /**
     * 获取驻车发电状态.
     *
     */
    fun setForceEvChargeMode(checked: Boolean) {
        LogUtil.i(TAG, "setForceEvChargeMode = $checked")
        if (forceChargeModeLiveData.value == checked) {
            LogUtil.i(TAG, "setForceEvChargeMode value ==")
            return
        }
        carNewEnergyManager.forceEvChargeMode = if (checked) CarNewEnergy.ForceChargeSwitchSts.ON
        else CarNewEnergy.ForceChargeSwitchSts.OFF
        delayAndExecute { getForceEvChargeMode() }
    }

    /**
     * 延迟执行方法
     * @param delayTime 延迟时间，单位为毫秒
     * @param block 延迟后要执行的函数
     */
    fun delayAndExecute(delayTime: Long = 2000L, block: suspend () -> Unit) {
        viewModelScope.launch(Dispatchers.Default) {
            delay(delayTime)
            block()
        }
    }

    fun execute(block: suspend () -> Unit) {
        viewModelScope.launch(Dispatchers.Default) {
            block()
        }
    }

    /**
     *埋点上报数据
     */
    fun sendDataPointData() {
        val dataPoint = DataPoint()
        dataPoint.id = CommonConst.DataPoint.id
        dataPoint.eventId = CommonConst.EventId.NEW_ENERGY_SET
        dataPoint.timestamp = System.currentTimeMillis()
        dataPoint.supplierCode = CommonConst.DataPoint.supplierCode
        dataPoint.platformCode = CommonConst.DataPoint.platformCode
        dataPoint.nodeType = LogDataUtil.NODE_TYPE_ADD_OPERATION

        val contentList = ArrayList<Content>()
        //预约充电设置	BookSw	int	"0：关； 1：开；"
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141401, CommonConst.Att.BookSw, bookChargeStatusLiveData.value ?: 0
            )
        )
        //点击设置预约充电成功时间	BookTime	string	年/月/日  时:分:秒
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141401,
                CommonConst.Att.BookTime,
                bookChargeHour.get().toString() + ":" + bookChargeMinutes.get().toString()
            )
        )
        //对外放电	CHGSw	int	"0：关；1：开；"
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141401,
                CommonConst.Att.CHGSw,
                if (dischargeStatusLiveData.value == true) 1 else 0
            )
        )
        //设置时间	SetTime	string	年/月/日  时:分:秒

        //充电上限值	CULVSet	string	0%~100%
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141401, CommonConst.Att.CULVSet, chargeStopSOCLiveData.value.toString()
            )
        )
        //放电下限值	LLDSet	string	0%~100%
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141401, CommonConst.Att.LLDSet, dischargeStopSOCLiveData.value.toString()
            )
        )
        //能量回收强度	KERSw	int	"0：关闭； //1：低； //2：中； //3：高；"
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141402, CommonConst.Att.KERSw,
                energyRecoveryLiveData.value ?: 1
            )
        )

        //纯电续航显示	PERDSet	int	"1：电量； //2：标准里程； //3：动态里程； //4：电续航里程；"
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141402, CommonConst.Att.PERDSet,
                pureElectricDisplayLiveData.value ?: 1
            )
        )

        //行驶里程显示	MileageDisSw	int	"1：总里程； //2：HEV里程； //3：EV里程； //4：燃油里程；"
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141402, CommonConst.Att.MileageDisSw,
                mileageDisplayLiveData.value ?: 1
            )
        )

        //预约出行	BookTripSw	int	"0：关； //1：开；"
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141403, CommonConst.Att.BookTripSw,
                bookTravelLiveData.value ?: 0
            )
        )

        //续航里程工况	RangeCSet	int	"1：标准工况（CLTC）； //2：动态工况； //3：WLTC；"
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141407, CommonConst.Att.RangeCSet,
                rangeConditionDisplayLiveData.value ?: 1
            )
        )

        //N档防误触提醒	NAMSw	int	"0：关； //1：开；"
//        contentList.add(
//            genContent(
//                CommonConst.CodeId.ZB141408, CommonConst.Att.NAMSw, 0
//            )
//        )

        //驻车发电	ParkGenSw	int	"0：关； //1：开；"
        contentList.add(
            genContent(
                CommonConst.CodeId.ZB141401,
                CommonConst.Att.ParkGenSw,
                if (forceChargeModeLiveData.value == true) 1 else 0
            )
        )

        dataPoint.content = contentList

        LiveEventBus.get<Any>(DataPointReportLifeCycle.KEY_DATA_POINT).post(dataPoint)
    }

    private fun genContent(
        locationId: String, attributeId: String, attributeValue: Any
    ): Content {
        return Content().apply {
            this.locationId = locationId
            this.attributeId = attributeId
            this.attributeValue = attributeValue
        }
    }

    /**
     * ViewModel销毁时调用.
     *
     */
    override fun onCleared() {
        super.onCleared()
        LogUtil.d(TAG, "onCleared : ")
        carNewEnergyManager.removeCallback(TAG)
    }

    companion object {
        // 日志标志位
        private const val TAG = "NewEnergyViewModel"

        /**
         * 将整数格式化为两位数的字符串，若数字小于 10 则在前面补零。
         */
        fun Int.formatWithZero(): String {
            return String.format("%02d", this)
        }

        val newEnergyManager: NewEnergyManager by lazy(LazyThreadSafetyMode.PUBLICATION) {
            BitechCar.getInstance().getServiceManager(BitechCar.CAR_ENERGY_MANAGER) as NewEnergyManager
        }
    }
}
