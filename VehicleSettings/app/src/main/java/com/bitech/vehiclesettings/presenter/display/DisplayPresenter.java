package com.bitech.vehiclesettings.presenter.display;

import android.app.UiModeManager;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.ColorRes;

import com.bitech.platformlib.manager.DisplayManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.broadcast.TimeChangeReceiver;
import com.bitech.vehiclesettings.bean.GlobalVar;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.carapi.constants.CarDisplay;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.service.GlobalDataObserver;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.SendICUTopicsUtil;
import com.bitech.vehiclesettings.utils.SystemColorUtil;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.base.VDKey;
import com.chery.ivi.vdb.event.id.carstate.VDEventCarState;
import com.chery.ivi.vdb.event.id.carstate.VDValueCarState;

import java.util.Objects;

public class DisplayPresenter {
    private static final String TAG = DisplayPresenter.class.getSimpleName() + "wzh2whu";
    public static final int ZKP_MAX_VAL = 100;
    public static final int DEFAULT_SYSTEM_COLOR = R.color.system_color_blue;
    private Context mContext;
    NewEnergyManager newEnergyManager = NewEnergyManager.getInstance();
    private static volatile DisplayPresenter instance;


    public static DisplayPresenter getInstance() {
        if (instance == null) {
            synchronized (DisplayPresenter.class) {
                if (instance == null) {
                    instance = new DisplayPresenter();
                }
            }
        }
        return instance;
    }

    public DisplayPresenter() {
        this.mContext = MyApplication.getContext();
    }

    public static void setShowLyrics(int status) {
        Log.d(TAG, "setShowLyrics: 仪表显示歌词:" + (status == PrefsConst.TRUE ? "开启" : "关闭"));
        Prefs.put(PrefsConst.DISPLAY_SHOW_LYRICS, status);
        // TODO 仪表显示歌词 待联调 （在线电台
        Prefs.setGlobalValue(PrefsConst.GlobalValue.DISPLAY_SHOW_LYRICS, status);
        SendICUTopicsUtil.sendTopics(SendICUTopicsUtil.Topics.Vehiclesettings_ShowLyrics_SET, status);
    }


    public static int getShowLyrics() {
        int status = Prefs.get(PrefsConst.DISPLAY_SHOW_LYRICS, PrefsConst.TRUE);
        Log.d(TAG, "getShowLyrics: 仪表显示歌词:" + (status == PrefsConst.TRUE ? "开启" : "关闭"));
        return status;
    }


    public void setVideoLimit(int status) {
        Log.d(TAG, "setVideoLimit: 视频限制:" + (status == PrefsConst.TRUE ? "开启" : "关闭"));
        Prefs.put(PrefsConst.DISPLAY_VIDEO_LIMIT, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.DISPLAY_VIDEO_LIMIT, status);
    }


    public int getVideoLimit() {
        int status = Prefs.get(PrefsConst.DISPLAY_VIDEO_LIMIT, PrefsConst.TRUE);
        Log.d(TAG, "getVideoLimit: 视频限制:" + (status == PrefsConst.TRUE ? "开启" : "关闭"));
        return Prefs.getGlobalValue(PrefsConst.GlobalValue.DISPLAY_VIDEO_LIMIT, PrefsConst.TRUE);
//        return status;
    }


    public void setZKPBrightness(int progress) {
        int zkpBrightnessLimit = getZKPBrightnessLimit(progress);
        Prefs.put(PrefsConst.DISPLAY_ZKP, zkpBrightnessLimit);
        Log.d(TAG, "setZKPBrightness:" + zkpBrightnessLimit);
        setDisplayScreen(VDValueCarState.ScreenBackLight.MANUAL_MODE, zkpBrightnessLimit, VDValueCarState.DisplayID.MASTER_SCREEN);
    }

    public int getZKPBrightnessLimit(int progress) {
        progress *= 10;
        int apcLevelLimit = newEnergyManager.getAPCLevelLimit();
        int max = progress;
        if (apcLevelLimit == CarNewEnergy.ApcLevelLimit.LEVEL_5) {
            max = (int) (ZKP_MAX_VAL * 0.3f);
        }
        if (apcLevelLimit == CarNewEnergy.ApcLevelLimit.LEVEL_4) {
            max = (int) (ZKP_MAX_VAL * 0.6f);
        }
        progress = Math.min(progress, max);
        return progress / 10;
    }

    public void setZKPAuto(int state) {
        Log.d(TAG, "setZKPAuto: " + state);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.DISPLAY_AUTO_ZKP, state);
        SliceReceiver.notifyChange(ProviderURI.LIGHT);
    }

    public int getZKPAuto() {
        int state = Prefs.getGlobalValue(PrefsConst.GlobalValue.DISPLAY_AUTO_ZKP, CarDisplay.DEFAULT_AUTO);
        Log.d(TAG, "getZKPAuto: " + state);
        return state;
    }

    public int getZKPBrightness() {
        int progress = getDisplayID(VDValueCarState.DisplayID.MASTER_SCREEN);
        progress /= 10;
        Log.d(TAG, "getZKPBrightness: " + progress);
        return progress;
    }

    public void setYBPBrightness(int progress) {
        Prefs.put(PrefsConst.DISPLAY_YBP, progress);
        setDisplayScreen(VDValueCarState.ScreenBackLight.MANUAL_MODE, progress, VDValueCarState.DisplayID.Meter_SCREEN);
    }

    public void setYBPAuto(int state) {
        Log.d(TAG, "setYBPAuto: " + state);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.DISPLAY_AUTO_YBP, state);
    }

    public int getYBPAuto() {
        int state = Prefs.getGlobalValue(PrefsConst.GlobalValue.DISPLAY_AUTO_YBP, CarDisplay.DEFAULT_AUTO);
        Log.d(TAG, "getYBPAuto: " + state);
        return state;
    }

    public int getYBPBrightness() {
        int displayID = getDisplayID(VDValueCarState.DisplayID.Meter_SCREEN);
        displayID /= 10;
        Log.d(TAG, "getYBPBrightness: " + displayID);
        return displayID;
    }

    public void setXDPBrightness(int progress) {
        // TODO 吸顶屏亮度
        setDisplayScreen(VDValueCarState.ScreenBackLight.MANUAL_MODE, progress, VDValueCarState.DisplayID.AIR_SCREEN);// TODO
    }

    public void setXDPAuto() {
//        setDisplayScreen(VDValueCarState.ScreenBackLight.AUTO_MODE, 0, VDValueCarState.DisplayID.AIR_SCREEN);
    }


    public int getXDPBrightness() {
        return getDisplayID(VDValueCarState.DisplayID.AIR_SCREEN);
    }


    public void setFontSize(float newFontScale) {
        Log.d(TAG, "setFontSize: " + newFontScale);
        Prefs.setSystemFloat(Settings.System.FONT_SCALE, newFontScale);
        Intent intent = new Intent(Intent.ACTION_CONFIGURATION_CHANGED);
        mContext.sendBroadcast(intent);
        GlobalVar.setIsSetDisplay(true);
    }


    public float getFontSize() {
        return Prefs.getSystemFloat(Settings.System.FONT_SCALE, CarDisplay.DEFAULT_FONT_SIZE);
    }

    /**
     * 设置显示模式
     * @param mode 浅色0，深色1，自动2
     * @param isFragment 是否来自Fragment
     */
    public void setDisplayMode(int mode, boolean isFragment) {
        Prefs.setGlobalValue(PrefsConst.DISPLAY_MODE, mode);
        UiModeManager uiModeManager = (UiModeManager) mContext.getSystemService(Context.UI_MODE_SERVICE);
        if (uiModeManager != null) {
            switch (mode){
                case CarDisplay.DAY -> {
                    uiModeManager.setNightMode(UiModeManager.MODE_NIGHT_NO);
                    Log.d(TAG, "setDayNightMode: " + "白天");
                    SendICUTopicsUtil.sendDayNightTopic(CarDisplay.DAY);
                }
                case CarDisplay.NIGHT -> {
                    uiModeManager.setNightMode(UiModeManager.MODE_NIGHT_YES);
                    Log.d(TAG, "setDayNightMode: " + "黑夜");
                    SendICUTopicsUtil.sendDayNightTopic(CarDisplay.NIGHT);
                }
                case CarDisplay.AUTO -> {
                    boolean b = TimeChangeReceiver.getIsDay();
                    // b == true 设为白天
                    if (!b) {
                        uiModeManager.setNightMode(UiModeManager.MODE_NIGHT_YES);
                        Log.d(TAG, "自动模式: " + "黑夜");
                        SendICUTopicsUtil.sendDayNightTopic(CarDisplay.NIGHT);
                    } else {
                        uiModeManager.setNightMode(UiModeManager.MODE_NIGHT_NO);
                        Log.d(TAG, "自动模式: " + "白天");
                        SendICUTopicsUtil.sendDayNightTopic(CarDisplay.DAY);
                    }
                }
            }
        }
        if(isFragment){
            GlobalVar.setIsSetDisplay(true);
            GlobalVar.setIsSetSystem(false);
        }
        SliceReceiver.notifyChange(ProviderURI.DISPLAY_MODE);
    }

    /**
     * 是否为自动模式
     * @return
     */
    public static boolean getAutoMode() {
        int displayMode = Prefs.getGlobalValue(PrefsConst.DISPLAY_MODE, CarDisplay.AUTO);
        return displayMode == CarDisplay.AUTO;
    }

    /**
     * 是否浅色深色模式
     * @return true：深色 false：浅色
     */
    public static boolean getDisplayMode() {
        UiModeManager uiModeManager = (UiModeManager) MyApplication.getContext().getSystemService(Context.UI_MODE_SERVICE);
        if (uiModeManager != null) {
            return uiModeManager.getNightMode() == UiModeManager.MODE_NIGHT_YES;
        }
        return false;
    }

    public void setSystemColor(@ColorRes int selectedColor) {
        GlobalDataObserver globalDataObserver = MyApplication.getInstance().getGlobalDataObserver();
        Log.d(TAG, "颜色选择: " + selectedColor);
        if (selectedColor == R.color.system_color_blue) {
            Prefs.put(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue);
            DisplayManager.getInstance().setSystemColor(0);
        } else if (selectedColor == R.color.system_color_purple) {
            Prefs.put(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemePurple);
            DisplayManager.getInstance().setSystemColor(1);
        } else if (selectedColor == R.color.system_color_cyan) {
            Prefs.put(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeCyan);
            DisplayManager.getInstance().setSystemColor(2);
        } else if (selectedColor == R.color.system_color_green) {
            Prefs.put(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeGreen);
            DisplayManager.getInstance().setSystemColor(3);
        } else if (selectedColor == R.color.system_color_orange) {
            Prefs.put(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeOrange);
            DisplayManager.getInstance().setSystemColor(4);
        } else if (selectedColor == R.color.system_color_red) {
            Prefs.put(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeRed);
            DisplayManager.getInstance().setSystemColor(5);
        }
        globalDataObserver.setGlobalData(GlobalDataObserver.KEY_SYSTEM_COLOR, SystemColorUtil.SystemColorValueIntToString(selectedColor));
    }

    public void setFp(int status) {
        Prefs.setGlobalValue(PrefsConst.GlobalValue.DISPLAY_FP, status);
        Log.d(TAG, "setFp: 分屏:" + (status == PrefsConst.TRUE ? "开启" : "关闭"));
    }

    public int getFp() {
        int globalValue = Prefs.getGlobalValue(PrefsConst.GlobalValue.DISPLAY_FP, PrefsConst.DefaultValue.DISPLAY_FP);
        Log.d(TAG, "getFp: 分屏:" + (globalValue == PrefsConst.TRUE ? "开启" : "关闭") + " " + globalValue);
        return globalValue;
    }

    public void reset() {
        // TODO 壁纸重置

        Log.d(TAG, "reset: 字体");
        setFontSize(CarDisplay.DEFAULT_FONT_SIZE);
        Log.d(TAG, "reset: 显示模式");
        setDisplayMode(CarDisplay.AUTO,true);
        Log.d(TAG, "reset: 分屏");
        setFp(CarDisplay.DEFAULT_FP);
        Log.d(TAG, "reset: 仪表显示歌词");
        setShowLyrics(CarDisplay.Lyrics.DEFAULT);
        Log.d(TAG, "reset: 视频限制");
        setVideoLimit(CarDisplay.DEFAULT_VIDEO_LIMIT);
        if (DisplayPresenter.getDisplayMode()) {
            Log.d(TAG, "reset: 中控屏亮度 深色");
            setZKPBrightness(CarDisplay.DEFAULT_ZKP_BRIGHTNESS_NIGHT);
        } else {
            Log.d(TAG, "reset: 中控屏亮度 浅色");
            setZKPBrightness(CarDisplay.DEFAULT_ZKP_BRIGHTNESS_DAY);
        }
        Log.d(TAG, "reset: 仪表盘亮度");
        setYBPBrightness(CarDisplay.DEFAULT_YBP_BRIGHTNESS);
        Log.d(TAG, "reset: 中控屏自动亮度");
        setZKPAuto(CarDisplay.DEFAULT_AUTO);
        Log.d(TAG, "reset: 仪表盘自动亮度");
        setYBPAuto(CarDisplay.DEFAULT_AUTO);
        GlobalVar.setIsSetDisplay(true);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param isAutoMode 自动亮度
     * @param progress   亮度
     * @param displayID  屏ID
     */
    private void setDisplayScreen(int isAutoMode, int progress, int displayID) {
        progress = progress == 0 ? 1 : progress;
        Log.d(TAG, "setDisplayScreen:setDisplayID: " + displayID + "----setDisplayProgress: " + progress * 10);
        Bundle payload = new Bundle();
        int[] data = new int[]{
                isAutoMode,
                getDisplayMode() ? VDValueCarState.ScreenBackLight.DARKNIGHT_MODE : VDValueCarState.ScreenBackLight.DAY_MODE,
                progress * 10,
                displayID
        };
        payload.putIntArray(VDKey.DATA, data);
        VDEvent event = new VDEvent(VDEventCarState.SCREEN_BACKLIGHT, payload);
        VDBus.getDefault().set(event);
    }

    private int getDisplayID(int screenType) {
        VDEvent getEvent = new VDEvent(VDEventCarState.SCREEN_BACKLIGHT);
        Bundle bundle = new Bundle();
        bundle.putInt(VDKey.TYPE, screenType);
        getEvent.setPayload(bundle);
        VDEvent event = VDBus.getDefault().getOnce(getEvent);
        if (event != null && event.getPayload().getIntArray(VDKey.DATA) != null && event.getPayload().getIntArray(VDKey.DATA).length > 2) {
            return Objects.requireNonNull(event.getPayload().getIntArray(VDKey.DATA))[2];
        }
        return 0;
    }

    public static int zkpAutoLightReverseDay(int signal) {
        if (signal >= 0 && signal <= 80) {
            return 1;
        } else if (signal > 80 && signal <= 120) {
            return 1;
        } else if (signal > 120 && signal <= 500) {
            return 2;
        } else if (signal > 500 && signal <= 600) {
            return 2;
        } else if (signal > 600 && signal <= 1000) {
            return 3;
        } else if (signal > 1000 && signal <= 1200) {
            return 3;
        } else if (signal > 1200 && signal <= 1800) {
            return 4;
        } else if (signal > 1800 && signal <= 2000) {
            return 4;
        } else if (signal > 2000 && signal <= 3000) {
            return 5;
        } else if (signal > 3000 && signal <= 3400) {
            return 5;
        } else if (signal > 3400 && signal <= 6000) {
            return 6;
        } else if (signal > 6000 && signal <= 6400) {
            return 6;
        } else if (signal > 6400 && signal <= 10000) {
            return 7;
        } else if (signal > 10000 && signal <= 12000) {
            return 7;
        } else if (signal > 12000 && signal <= 20000) {
            return 8;
        } else if (signal > 20000 && signal <= 22000) {
            return 8;
        } else if (signal > 22000 && signal <= 30000) {
            return 9;
        } else if (signal > 30000 && signal <= 32000) {
            return 9;
        } else return 10;
    }

    public static int ybpAutoLightReverseDay(int signal) {
        if (signal >= 0 && signal <= 50) {
            return 1;
        } else if (signal > 50 && signal <= 100) {
            return 1;
        } else if (signal > 100 && signal <= 500) {
            return 2;
        } else if (signal > 500 && signal <= 600) {
            return 2;
        } else if (signal > 600 && signal <= 1000) {
            return 3;
        } else if (signal > 1000 && signal <= 1100) {
            return 3;
        } else if (signal > 1200 && signal <= 1800) {
            return 4;
        } else if (signal > 1800 && signal <= 2000) {
            return 4;
        } else if (signal > 2000 && signal <= 3000) {
            return 6;
        } else if (signal > 3000 && signal <= 3400) {
            return 6;
        } else if (signal > 3400 && signal <= 6000) {
            return 7;
        } else if (signal > 6000 && signal <= 6400) {
            return 7;
        } else if (signal > 6400 && signal <= 10000) {
            return 8;
        } else if (signal > 10000 && signal <= 12000) {
            return 8;
        } else if (signal > 12000 && signal <= 20000) {
            return 9;
        } else if (signal > 20000 && signal <= 22000) {
            return 9;
        } else return 10;
    }

    public static int zkpAutoLightReverseNight(int signal) {
        if (signal >= 0 && signal <= 50) {
            return 4;
        } else if (signal > 50 && signal <= 100) {
            return 4;
        } else if (signal > 100 && signal <= 600) {
            return 6;
        } else if (signal > 600 && signal <= 800) {
            return 6;
        } else {
            return 8;
        }
    }
    public static int ybpAutoLightReverseNight(int signal) {
        if (signal >= 0 && signal <= 50) {
            return 2;
        } else if (signal > 50 && signal <= 100) {
            return 2;
        } else if (signal > 100 && signal <= 600) {
            return 3;
        } else if (signal > 600 && signal <= 800) {
            return 3;
        } else {
            return 4;
        }
    }
}
