package com.bitech.vehiclesettings.view.condition;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertConditionNextMaintenanceBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertEnergyConsumptionListBinding;
import com.bitech.vehiclesettings.databinding.DialogEnergyConsumptionListBinding;
import com.bitech.vehiclesettings.presenter.driving.DrivingAnime;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class EnergyConsumptionListUIAlert extends BaseDialog {
    private static final String TAG = EnergyConsumptionListUIAlert.class.getSimpleName();
    private static EnergyConsumptionListUIAlert.onProgressChangedListener onProgressChangedListener;


    public EnergyConsumptionListUIAlert(@NonNull Context context) {
        super(context);
    }

    public EnergyConsumptionListUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected EnergyConsumptionListUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static EnergyConsumptionListUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(EnergyConsumptionListUIAlert.onProgressChangedListener onProgressChangedListener) {
        EnergyConsumptionListUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private EnergyConsumptionListUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertEnergyConsumptionListBinding binding;
        private static final String ERROR_VALUE_TEXT = "---";
        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private EnergyConsumptionListUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public EnergyConsumptionListUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public EnergyConsumptionListUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new EnergyConsumptionListUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertEnergyConsumptionListBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1992;
            layoutParams.height = 1089;
            window.setAttributes(layoutParams);
            initData();
            return dialog;
        }

        private void initData() {

        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
    }
}
