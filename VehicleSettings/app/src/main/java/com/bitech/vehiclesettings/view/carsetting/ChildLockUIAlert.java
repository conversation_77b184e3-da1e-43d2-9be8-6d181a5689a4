package com.bitech.vehiclesettings.view.carsetting;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.database.ContentObserver;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.quick.IQuickManagerListener;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl;
import com.bitech.vehiclesettings.carapi.constants.CarSystemColor;
import com.bitech.vehiclesettings.databinding.DialogAlertCChildLockBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenter;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenterListener;
import com.bitech.vehiclesettings.repository.QuickRepository;
import com.bitech.vehiclesettings.service.GlobalDataObserver;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.SystemColorUtil;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.viewmodel.QuickViewModel;

public class ChildLockUIAlert extends BaseDialog {
    private static final String TAG = ChildLockUIAlert.class.getSimpleName();
    private static final String REAR_SCREEN_LOCK = "rear_screen_lock";
    private static ChildLockUIAlert.onProgressChangedListener onProgressChangedListener;

    public ChildLockUIAlert(@NonNull Context context) {
        super(context);
    }

    public ChildLockUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ChildLockUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static ChildLockUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(ChildLockUIAlert.onProgressChangedListener onProgressChangedListener) {
        ChildLockUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder implements SafeHandlerCallback {
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertCChildLockBinding binding;
        private boolean globalAlert = false;
        private boolean isBlueOpen = false;
        private ChildLockUIAlert dialog = null;
        private QuickPresenterListener quickPresenter;
        private QuickViewModel viewModel;
        private SafeHandler quickHandler;
        private static QuickManager quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);
        private boolean isChildLockLeftClicked = false;
        private boolean isChildLockRightClicked = false;

        private ContentObserver rearScreenLockObserver = new ContentObserver(new Handler()) {
            @Override
            public void onChange(boolean selfChange) {
                int status = Settings.System.getInt(
                        context.getContentResolver(),
                        REAR_SCREEN_LOCK,
                        0);
                updateRearScreenControlUI(status);
            }
        };

        public void setGlobalAlert(boolean globalAlert) {
            this.globalAlert = globalAlert;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private void quickControllerRegLightListen() {
            if (quickManager == null) {
                quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);
            }
            quickManager.addCallback(TAG, new IQuickManagerListener() {
                @Override
                public void leftChildLockCallback(int status) {
                    Log.d(TAG, "leftChildLockCallback: 左前锁: " + status);
                    if (status == CarQuickControl.GetLeftChildLockSts.LOCKED || status == CarQuickControl.GetLeftChildLockSts.SUPERLOCKED) {
                        status = CarQuickControl.ButtonSts.ON;
                    } else if (status == CarQuickControl.GetLeftChildLockSts.UNLOCKED) {
                        status = CarQuickControl.ButtonSts.OFF;
                    } else {
                        status = Integer.MIN_VALUE;
                    }
                    if (viewModel.getLeftChildLock().getValue() != null && viewModel.getLeftChildLock().getValue() != status) {
                        if (status != Integer.MIN_VALUE) {
                            viewModel.setLeftChildLock(status);
                        }
                    }
                }

                @Override
                public void rightChildLockCallback(int status) {
                    Log.d(TAG, "rightChildLockCallback: 右前锁: " + status);
                    if (status == CarQuickControl.GetRightChildLockSts.LOCKED || status == CarQuickControl.GetRightChildLockSts.SUPERLOCKED) {
                        status = CarQuickControl.ButtonSts.ON;
                    } else if (status == CarQuickControl.GetRightChildLockSts.UNLOCKED) {
                        status = CarQuickControl.ButtonSts.OFF;
                    } else {
                        status = Integer.MIN_VALUE;
                    }
                    if (viewModel.getRightChildLock().getValue() != null && viewModel.getRightChildLock().getValue() != status) {
                        if (status != Integer.MIN_VALUE) {
                            viewModel.setRightChildLock(status);
                        }
                    }
                }

                @Override
                public void rearScreenControlCallback(int status) {
                    // 不再使用此回调，改为使用Settings监听
                }
            });
            quickManager.registerListener();
        }

        public Builder(Context context) {
            this.context = context;
            checkAndRequestSettingsPermission();
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public ChildLockUIAlert create() {
            int themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue);
            Context themedContext = new ContextThemeWrapper(context, themeId);
            if (dialog == null) {
                dialog = new ChildLockUIAlert(themedContext, R.style.Dialog);
            }
            binding = DialogAlertCChildLockBinding.inflate(LayoutInflater.from(themedContext));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());

//            context.setTheme(Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue));
            context.setTheme(SystemColorUtil.SystemColorValueToStyle(MyApplication.getInstance().getGlobalDataObserver().getGlobalData(GlobalDataObserver.KEY_SYSTEM_COLOR, CarSystemColor.SystemColorValue.BLUE)));
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 799;
            layoutParams.type = globalAlert ? WindowManager.LayoutParams.TYPE_SYSTEM_ALERT :
                    WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            window.setAttributes(layoutParams);

            quickPresenter = new QuickPresenter<QuickRepository>(context);
            viewModel = new QuickViewModel();
            quickHandler = new SafeHandler(this);

            initObserver();
            initSwitch();
            quickControllerRegLightListen();

            updateLeftChildLockUI(quickPresenter.getLeftChildLock());
            updateRightChildLockUI(quickPresenter.getRightChildLock());


            // 初始化后排屏锁状态
            int rearScreenStatus = Settings.System.getInt(
                    context.getContentResolver(),
                    REAR_SCREEN_LOCK,
                    0);
            updateRearScreenControlUI(rearScreenStatus);

            // 注册Settings观察者
            context.getContentResolver().registerContentObserver(
                    Settings.System.getUriFor(REAR_SCREEN_LOCK),
                    false,
                    rearScreenLockObserver);

            return dialog;
        }

        private void initObserver() {
            viewModel.getLeftChildLock().observeForever(status -> {
                updateLeftChildLockUI(status);
            });
            viewModel.getRightChildLock().observeForever(status -> {
                updateRightChildLockUI(status);
            });
        }

        private void initSwitch() {
            binding.swChildLockLeft.setChecked(CommonUtils.IntToBool(quickPresenter.getLeftChildLock()));
            binding.swChildLockLeft.setOnTouchListener((v, event) -> {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    isChildLockLeftClicked = true;
                }
                return false;
            });
            binding.swChildLockLeft.setOnCheckedChangeListener((buttonView, b) -> {
                if (isChildLockLeftClicked) {
                    isChildLockLeftClicked = false;
                    if(b){
                        onLeftSwitch(1);
                        Log.d(TAG, "左儿童锁开启事件：1");
                    } else {
                        onLeftSwitch(0);
                        Log.d(TAG, "左儿童锁关闭事件：0");
                    }
                }
            });

            binding.swChildLockRight.setChecked(CommonUtils.IntToBool(quickPresenter.getRightChildLock()));
            binding.swChildLockRight.setOnTouchListener((v, event) -> {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    isChildLockRightClicked = true;
                }
                return false;
            });
            binding.swChildLockRight.setOnCheckedChangeListener((buttonView, b) -> {
                if (isChildLockRightClicked) {
                    isChildLockRightClicked = false;
                    if(b){
                        onRightSwitch(1);
                        Log.d(TAG, "右儿童锁开启事件：1");
                    } else {
                        onRightSwitch(0);
                        Log.d(TAG, "右儿童锁关闭事件：0");
                    }
                }
            });

            binding.swRearScreenControl.setOnCheckedChangeListener((buttonView, b) -> {
                if(b){
                    onRearScreenControl(1);
                    Log.d(TAG, "后屏控制开启事件：1");
                } else {
                    onRearScreenControl(0);
                    Log.d(TAG, "后屏控制关闭事件：0");
                }
            });
        }

        private void onLeftSwitch(int status) {
            viewModel.setLeftChildLock(status);
            if (quickPresenter.getLeftChildLock() != status) {
                quickPresenter.setLeftChildLock(status);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_LEFT_CHILD_LOCK);
            }
            Log.d(TAG, "左儿童锁接口setListener: " + status);
        }

        private void onRightSwitch(int status) {
            viewModel.setRightChildLock(status);
            if (quickPresenter.getRightChildLock() != status) {
                quickPresenter.setRightChildLock(status);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_RIGHT_CHILD_LOCK);
            }
            Log.d(TAG, "右儿童锁接口setListener: " + status);
        }

        private void onRearScreenControl(int status) {
            Log.d(TAG, "后屏控制锁: " + status);
            Settings.System.putInt(
                    context.getContentResolver(),
                    REAR_SCREEN_LOCK,
                    status
            );
            updateRearScreenControlUI(status);
        }

        public void updateChildLockUI(int leftStatus, int rightStatus) {
            binding.swChildLockLeft.setChecked(CommonUtils.IntToBool(leftStatus));
            binding.swChildLockRight.setChecked(CommonUtils.IntToBool(rightStatus));
        }

        public void updateLeftChildLockUI(int leftStatus) {
            binding.swChildLockLeft.setChecked(CommonUtils.IntToBool(leftStatus));
            binding.tvChildLockLeft.setText(
                    leftStatus == 1 ? context.getString(R.string.str_carsetting_child_lock_1) : context.getString(R.string.str_carsetting_child_lock_1_close));
        }

        public void updateRightChildLockUI(int rightStatus) {
            binding.swChildLockRight.setChecked(CommonUtils.IntToBool(rightStatus));
            binding.tvChildLockRight.setText(
                    rightStatus == 1 ? context.getString(R.string.str_carsetting_child_lock_3) : context.getString(R.string.str_carsetting_child_lock_3_close));
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public void updateRearScreenControlUI(int status) {
            binding.swRearScreenControl.setChecked(CommonUtils.IntToBool(status));
            binding.tvRearScreenControl.setText(status == 1 ? context.getString(R.string.str_carsetting_child_lock_3) : context.getString(R.string.str_carsetting_rear_sreen_control_close));
        }

        @Override
        public void handleSafeMessage(Message msg) {
            switch (msg.what) {
                case MessageConst.QUICK_LEFT_CHILD_LOCK:
                    leftChildLockHandle();
                    break;
                case MessageConst.QUICK_RIGHT_CHILD_LOCK:
                    rightChildLockHandle();
                    break;
                default:
                    break;
            }
        }

        private void leftChildLockHandle() {
            int leftCLockStatus = quickPresenter.getLeftChildLock();
            if (leftCLockStatus != viewModel.getLeftChildLock().getValue()) {
                viewModel.setLeftChildLock(leftCLockStatus);
            }
        }

        private void rightChildLockHandle() {
            int rCLockStatus = quickPresenter.getRightChildLock();
            if (rCLockStatus != viewModel.getRightChildLock().getValue()) {
                viewModel.setRightChildLock(rCLockStatus);
            }
        }

        private void checkAndRequestSettingsPermission() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!Settings.System.canWrite(context)) {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS);
                    intent.setData(Uri.parse("package:" + context.getPackageName()));
                    context.startActivity(intent);
                }
            }
        }

        @Override
        public boolean isActive() {
            return isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface onProgressChangedListener {
    }
}