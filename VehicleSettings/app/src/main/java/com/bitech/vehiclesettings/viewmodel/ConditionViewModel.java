package com.bitech.vehiclesettings.viewmodel;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.interfaces.condition.IConditionManagerListener;
import com.bitech.platformlib.manager.CarSettingManager;
import com.bitech.platformlib.manager.ConditionManager;
import com.bitech.platformlib.manager.PowerManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarCondition;
import com.bitech.vehiclesettings.common.SiganlConstans;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

public class ConditionViewModel extends ViewModel {
    public static final String TAG = "ConditionViewModel";
    // 雨刮器维修模式
    public final MutableLiveData<Integer> wiperRepairModeLiveData = new MutableLiveData<>(0);
    // 雨刮器维修禁用
    public final MutableLiveData<Integer> wiperRepairDisableLiveData = new MutableLiveData<>(0);
    // 保养里程
    public final MutableLiveData<Integer> maintainKmLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 保养天数
    public final MutableLiveData<Integer> maintainDaysLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 保养提醒
    public final MutableLiveData<Integer> maintainTipsLiveData = new MutableLiveData<>(CarCondition.MaintainRemind.NOT_ACTIVE);
    // 左前胎压
    public final MutableLiveData<Integer> leftFrontTyrePressureLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 右前胎压
    public final MutableLiveData<Integer> rightFrontTyrePressureLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 左后胎压
    public final MutableLiveData<Integer> leftRearTyrePressureLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 右后胎压
    public final MutableLiveData<Integer> rightRearTyrePressureLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 左前温度
    public final MutableLiveData<Integer> leftFrontTyreTemperatureLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 右前温度
    public final MutableLiveData<Integer> rightFrontTyreTemperatureLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 左后温度
    public final MutableLiveData<Integer> leftRearTyreTemperatureLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 右后温度
    public final MutableLiveData<Integer> rightRearTyreTemperatureLiveData = new MutableLiveData<>(Integer.MIN_VALUE);
    // 左前胎压状态
    public final MutableLiveData<Integer> leftFrontTyrePressureStatusLiveData = new MutableLiveData<>(CarCondition.TirePositionWarning_LHFTire.NORMAL);
    // 右前胎压状态
    public final MutableLiveData<Integer> rightFrontTyrePressureStatusLiveData = new MutableLiveData<>(CarCondition.TirePositionWarning_RHFTire.NORMAL);
    // 左后胎压状态
    public final MutableLiveData<Integer> leftRearTyrePressureStatusLiveData = new MutableLiveData<>(CarCondition.TirePositionWarning_LHRTire.NORMAL);
    // 右后胎压状态
    public final MutableLiveData<Integer> rightRearTyrePressureStatusLiveData = new MutableLiveData<>(CarCondition.TirePositionWarning_RHRTire.NORMAL);
    // 电源模式
    public final MutableLiveData<Integer> powerMode = new MutableLiveData<>(0);
    // 遮阳帘维修模式
    public final MutableLiveData<Integer> sunShadeRepairModeLiveData = new MutableLiveData<>(0);
    // 遮阳帘当前状态
    public final MutableLiveData<Integer> SunShadeCurtainStatusLiveData = new MutableLiveData<>(CarCondition.SRFR_Movement.STOPPED);
    // 遮阳帘学习状态
    public final MutableLiveData<Integer> sunShadeTeachRunStatusLiveData = new MutableLiveData<>(CarCondition.SRFR_TeachRun.NO_TEACH_RUN);
    // 胎压单位
    public final MutableLiveData<Integer> tyrePressureUnitLiveData = new MutableLiveData<>(Prefs.get(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, 0));
    private final ConditionManager conditionManager = (ConditionManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_CONDITION_MANAGER);
    private final CarSettingManager carSettingManager = (CarSettingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_SETTING_MANAGER);

    public final AtomicBoolean supportPowerMode = new AtomicBoolean();

    private final ExecutorService executor = Executors.newCachedThreadPool();

    public void initData() {
        addCallback();
        Prefs.registerListener(prefsListener);
        executor.execute(() -> {
            // 保养里程
            getMaintainKm();
            // 保养天数
            getMaintainDays();
            // 保养信息
            getMaintainInfo();
            // 保养提醒
            getMaintainRemind();
            // 雨刮维修模式
            getWiperRepairMode();
            // 雨刮维修禁用
            getWiperModeAvailable();
            // 遮阳帘当前状态
            getSunShadeCurtainStatus();
            // 遮阳帘学习状态
            getSunShadeTeachRunStatus();
            // 获取左前胎压
            getLeftFrontTyrePressure();
            // 获取右前胎压
            getRightFrontTyrePressure();
            // 获取左后胎压
            getLeftRearTyrePressure();
            // 获取右后胎压
            getRightRearTyrePressure();
            // 获取左前胎温
            getLeftFrontTyreTemperature();
            // 获取右前胎温
            getRightFrontTyreTemperature();
            // 获取左后胎温
            getLeftRearTyreTemperature();
            // 获取右后胎温
            getRightRearTyreTemperature();
            // 获取左前胎压状态
            getLeftFrontTyrePressureStatus();
            // 获取右前胎压状态
            getRightFrontTyrePressureStatus();
            // 获取左后胎压状态
            getLeftRearTyrePressureStatus();
            // 获取右后胎压状态
            getRightRearTyrePressureStatus();
            // 获取胎压单位
            getTyrePressureUnit();
        });
    }

    /**
     * 获取保养信息
     */
    private void getMaintainInfo() {
        byte[] reMaintainInfo = conditionManager.getReMaintainInfo();
        updateMaintainInfo(reMaintainInfo);
    }

    /**
     * 更新保养信息
     * @param infoValue
     */
    private void updateMaintainInfo(byte[] infoValue) {
        try {

            // 1.数据有效性检查
            if (infoValue == null || infoValue.length < 5) {
                Log.e("MaintainInfo", "Invalid data: length must be >= 5 bytes");
                maintainKmLiveData.postValue(Integer.MIN_VALUE);  // 默认值
                maintainDaysLiveData.postValue(Integer.MIN_VALUE); // 默认值
                return;
            }

            // 2.解析数据
            // 2.1 保养天数
            int firstMaintenanceDays = infoValue[0] & 0xFF;
            // 2.2 保养里程
            int firstMaintenanceMileage = ByteBuffer.wrap(infoValue, 1, 4)
                    .order(ByteOrder.BIG_ENDIAN)
                    .getInt();

            // 日志输出
            Log.d("MaintainInfo", "首次保养天数: " + firstMaintenanceDays + " 天");
            Log.d("MaintainInfo", "首次保养里程: " + firstMaintenanceMileage + " km");

            // 3.更新LiveData
            maintainKmLiveData.postValue(firstMaintenanceMileage);
            maintainDaysLiveData.postValue(firstMaintenanceDays);

        } catch (Exception e) {
            Log.e(TAG, "解析保养信息异常: " + e.getMessage());
            maintainKmLiveData.postValue(Integer.MIN_VALUE);
            maintainDaysLiveData.postValue(Integer.MIN_VALUE);
        }
    }

    private void getTyrePressureUnit() {
        tyrePressureUnitLiveData.postValue(Prefs.get(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, 0));
    }

    /**
     * 左前胎压状态获取
     */
    private void getLeftFrontTyrePressureStatus() {
        int leftFrontTireStatus = conditionManager.getLeftFrontTireStatus();
        if (leftFrontTireStatus != Integer.MIN_VALUE) {
            leftFrontTyrePressureStatusLiveData.postValue(leftFrontTireStatus);
        } else {
            Integer value = leftFrontTyrePressureStatusLiveData.getValue();
            leftFrontTyrePressureStatusLiveData.postValue(value);
        }
    }

    /**
     * 右前胎压状态获取
     */
    private void getRightFrontTyrePressureStatus() {
        int rightFrontTireStatus = conditionManager.getRightFrontTireStatus();
        if (rightFrontTireStatus != Integer.MIN_VALUE) {
            rightFrontTyrePressureStatusLiveData.postValue(rightFrontTireStatus);
        } else {
            Integer value = rightFrontTyrePressureStatusLiveData.getValue();
            rightFrontTyrePressureStatusLiveData.postValue(value);
        }
    }

    /**
     * 左后胎压状态获取
     */
    private void getLeftRearTyrePressureStatus() {
        int leftFrontTireStatus = conditionManager.getLeftFrontTireStatus();
        if (leftFrontTireStatus != Integer.MIN_VALUE) {
            leftRearTyrePressureStatusLiveData.postValue(leftFrontTireStatus);
        } else {
            Integer value = leftRearTyrePressureStatusLiveData.getValue();
            leftRearTyrePressureStatusLiveData.postValue(value);
        }
    }

    /**
     * 右后胎压状态获取
     */
    private void getRightRearTyrePressureStatus() {
        int rightRearTireStatus = conditionManager.getRightRearTireStatus();
        if (rightRearTireStatus != Integer.MIN_VALUE) {
            rightRearTyrePressureStatusLiveData.postValue(rightRearTireStatus);
        } else {
            Integer value = rightRearTyrePressureStatusLiveData.getValue();
            rightRearTyrePressureStatusLiveData.postValue(value);
        }
    }

    private void addCallback() {
        conditionManager.addCallback(TAG, new IConditionManagerListener() {
            @Override
            public void onConfigChanged(boolean success) {
                Log.d(TAG, "onConfigChanged status: " + success);
            }

            @Override
            public void MaintainMileageCallback(int signalVal) {
                // 获取保养里程
                Log.d(TAG, "MaintainMileageCallback: " + signalVal);
                maintainKmLiveData.postValue(signalVal);
            }

            @Override
            public void MaintainTimeCallback(int signalVal) {
                // 获取保养时间
                Log.d(TAG, "MaintainTimeCallback: " + signalVal);
                maintainDaysLiveData.postValue(signalVal);
            }

            @Override
            public void MaintainRemindCallback(int signalVal) {
                // 获取保养提醒
                Log.d(TAG, "MaintainRemindCallback: " + signalVal);
                maintainTipsLiveData.postValue(signalVal);
            }

            @Override
            public void MaintainInfoCallback(byte[] signalVal) {
                // 获取保养里程
                Log.d(TAG, "MaintainInfoCallback: ");
                updateMaintainInfo(signalVal);
            }

            @Override
            public void onLeftFrontTyrePressureChanged(int pressure) {
                // 左前胎压
                Log.d(TAG, "onLeftFrontTyrePressureChanged: " + pressure);
                if (pressure == Integer.MIN_VALUE) return;
                leftFrontTyrePressureLiveData.postValue(pressure);
            }

            @Override
            public void onRightFrontTyrePressureChanged(int pressure) {
                // 右前胎压
                Log.d(TAG, "onRightFrontTyrePressureChanged: " + pressure);
                if (pressure == Integer.MIN_VALUE) return;
                rightFrontTyrePressureLiveData.postValue(pressure);
            }

            @Override
            public void onLeftRearTyrePressureChanged(int pressure) {
                // 左后胎压
                Log.d(TAG, "onLeftRearTyrePressureChanged: " + pressure);
                if (pressure == Integer.MIN_VALUE) return;
                leftRearTyrePressureLiveData.postValue(pressure);
            }

            @Override
            public void onRightRearTyrePressureChanged(int pressure) {
                // 右后胎压
                Log.d(TAG, "onRightRearTyrePressureChanged: " + pressure);
                if (pressure == Integer.MIN_VALUE) return;
                rightRearTyrePressureLiveData.postValue(pressure);
            }

            @Override
            public void onLeftFrontTyreTemperatureChanged(int temperature) {
                // 左前温度
                Log.d(TAG, "onLeftFrontTyreTemperatureChanged: " + temperature);
                if (temperature == Integer.MIN_VALUE) return;
                leftFrontTyreTemperatureLiveData.postValue(temperature);
            }

            @Override
            public void onRightFrontTyreTemperatureChanged(int temperature) {
                // 右前温度
                Log.d(TAG, "onRightFrontTyreTemperatureChanged: " + temperature);
                if (temperature == Integer.MIN_VALUE) return;
                rightFrontTyreTemperatureLiveData.postValue(temperature);
            }

            @Override
            public void onLeftRearTyreTemperatureChanged(int temperature) {
                // 左后温度
                Log.d(TAG, "onLeftRearTyreTemperatureChanged: " + temperature);
                if (temperature == Integer.MIN_VALUE) return;
                leftRearTyreTemperatureLiveData.postValue(temperature);
            }

            @Override
            public void onRightRearTyreTemperatureChanged(int temperature) {
                // 右后温度
                Log.d(TAG, "onRightRearTyreTemperatureChanged: " + temperature);
                if (temperature == Integer.MIN_VALUE) return;
                rightRearTyreTemperatureLiveData.postValue(temperature);
            }

            @Override
            public void onWiperRepairModeDisableSignal(int state) {
                // 雨刮维修模式禁用指示信号
                Log.d(TAG, "onWiperRepairModeDisableSignal: " + state);
                wiperRepairDisableLiveData.postValue(state);
            }

            @Override
            public void onWiperRepairModeSignal(int signVal) {
                // 雨刮维修模式
                Log.d(TAG, "onWiperRepairModeChanged: " + signVal);
                wiperRepairModeLiveData.postValue(signVal);
            }

            @Override
            public void onPowerModeSignal(int signVal) {
                Log.d(TAG, "onPowerModeSignal: " + signVal);
                powerMode.postValue(signVal);
            }

            @Override
            public void onSunShadeRepairModeSignal(int signVal) {
                Log.d(TAG, "onSunShadeRepairModeSignal: " + signVal);
//                sunShadeRepairModeLiveData.postValue(signVal);
//                int status = Integer.MIN_VALUE;
//                if (signVal == 0x0) {
//                    status = 0;
//                } else if (signVal == 0x1) {
//                    status = 1;
//                }
//                // 检查是否修复成功
//                if (isRepairInProgress && status == 1) {
//                    // 确保在主线程更新UI
//                    if (getActivity() != null) {
//                        getActivity().runOnUiThread(() -> {
//                            viewModel.setSunShadeRepairMode(0);
//                            handler.removeCallbacks(timeoutRunnable);
//                            EToast.showToast(mContext, getString(R.string.str_sun_shade_curtain_repair_success), 2000, false);
//                            isRepairInProgress = false;
//                        });
//                    }
//                }
            }

            @Override
            public void onSunShadeTeachRunStatus(int signVal) {
                sunShadeTeachRunStatusLiveData.postValue(signVal);
            }

            /**
             * 左前胎压状态信号
             */
            @Override
            public void LeftFrontTyrePressureStatusCallback(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                leftFrontTyrePressureStatusLiveData.postValue(signalVal);
            }

            /**
             * 右前胎压状态信号
             */
            @Override
            public void onRightFrontTyrePressureStatus(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                rightFrontTyrePressureStatusLiveData.postValue(signalVal);
            }

            /**
             * 左后胎压状态信号
             */
            @Override
            public void onLeftRearTyrePressureStatus(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                leftRearTyrePressureStatusLiveData.postValue(signalVal);
            }

            /**
             * 右后胎压状态信号
             */
            @Override
            public void onRightRearTyrePressureStatus(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                rightRearTyrePressureStatusLiveData.postValue(signalVal);
            }
        });
        conditionManager.registerListener();
    }

    /**
     * pref监听
     */
    private final SharedPreferences.OnSharedPreferenceChangeListener prefsListener =
            (sharedPreferences, key) -> {
                if (PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT.equals(key)) {
                    // 当轮胎压力单位设置变化时更新LiveData
                    tyrePressureUnitLiveData.postValue(
                            Prefs.get(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, 1)
                    );
                }
            };

    @Override
    protected void onCleared() {
        super.onCleared();
        executor.shutdown();
        // 注销监听防止内存泄漏
        Prefs.unregisterListener(prefsListener);
    }

    /**
     * 设置雨刮维修模式
     *
     * @param isChecked
     */
    public void setWiperRepairMode(boolean isChecked) {
        //显⽰名称：⾬刮维修模式 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在⼤屏上点击⾬刮维修模式开关按键使打开
        //2. 在⼤屏上点击⾬刮维修模式开关按键使其关闭
        //执⾏输出（1||2）MaintenanceMode/WiperMode/Set
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_MaintenanceMode =0x2:Entrance & ICC_WiperID
        //=0x3:Both Wiper，然后发送 0x0:Not Active 给 FLZCU，FLZCU 控制进⼊⾬刮 维修模式，计时 2s
        //若检测到状态反馈信号 FLZCU_WipeMaintenanceSWSts=0x3:Both Wiper，则⾬刮维修模式开关
        //保 持开启，否则开关弹回关闭；
        //2. 若触发条件为 1，ICC 连续发送三帧 ICC_MaintenanceMode =0x1:Exit & ICC_WiperID =0x3:Both
        //Wiper，然后发送 0x0:Not Active 给 FLZCU，FLZCU 控制退出⾬刮 维修模式，计时 2s 若检测到状
        //态反馈信号 FLZCU_WipeMaintenanceSWSts≠0x3:Both Wiper，则⾬刮维修模式开关保 持关闭，
        //否则开关弹回开启；；
        //3. ICC 收到 FLZCU_WipeMaintenanceSWSts = 0x3:Both Wiper，⼤屏显⽰⾬刮维修模式开启
        //ICC 收到 FLZCU_WipeMaintenanceSWSts =0x2:Rear Wiper||0x1:Front Wiper||0x0:Not
        //Active，⼤屏显⽰⾬刮维修模式关闭

        // ICC -> FLZCU 信号名：ICC_MaintenanceMode
        // 0x0:Not Active
        // 0x1:Exit
        // 0x2:Entrance

        // FLZCU -> ICC 信号名：FLZCU_WipeMaintenanceSWSts
        // 0x0:Not Active
        // 0x1:Front Wiper
        // 0x2:Rear Wiper
        // 0x3:Both Wiper
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (isChecked && wiperRepairModeLiveData.getValue() == CarCondition.FLZCU_WipeMaintenanceSWSts.BOTH_WIPER) {
                return;
            } else if (!isChecked && wiperRepairModeLiveData.getValue() != CarCondition.FLZCU_WipeMaintenanceSWSts.BOTH_WIPER) {
                return;
            }
            carSettingManager.setWiperMode(isChecked ?
                    CarCondition.ICC_MaintenanceMode.ENTRANCE :
                    CarCondition.ICC_MaintenanceMode.EXIT);
            carSettingManager.setWiperId(CarCondition.ICC_WiperID.BOTH_WIPER);
        }
    }

    /**
     * 复位保养里程
     */
    public void setMaintainReset() {
        // TODO 在线配置
        Log.d(TAG, "setSwMaintainReset: 保养里程复位");
        if (MsgUtil.getInstance().supportPowerMode()) {
            conditionManager.setMaintainResetSet(0x1);
        }
    }

    /**
     * 获取雨刮维修模式
     *
     * @return
     */
    public void getWiperRepairMode() {
        int signalVal = carSettingManager.getWiperMode();
        wiperRepairModeLiveData.postValue(signalVal);
    }

    /**
     * 保养提醒
     *
     * @param isChecked
     */
    public void setMaintainRemind(boolean isChecked) {
        if (isChecked && maintainTipsLiveData.getValue() == CarCondition.MaintainRemind.ACTIVE) {
            return;
        } else if (!isChecked && maintainTipsLiveData.getValue() == CarCondition.MaintainRemind.NOT_ACTIVE) {
            return;
        }
        conditionManager.setMaintainTipSet(isChecked ?
                CarCondition.MaintainRemind_Set.ACTIVE :
                CarCondition.MaintainRemind_Set.NOT_ACTIVE);
    }

    /**
     * 获取保养提醒
     */
    public void getMaintainRemind() {
        int signalVal = conditionManager.getMaintainTip();
        maintainTipsLiveData.postValue(signalVal);
    }

    /**
     * 获取保养里程
     */
    public void getMaintainKm() {
        int signalVal = conditionManager.getReMaintainMileage();
        Log.d(TAG, "getSwMaintainKm: 获取保养里程" + signalVal);
        maintainKmLiveData.postValue(signalVal);
    }

    /**
     * 获取保养天数
     */
    public void getMaintainDays() {
        int day = conditionManager.getReMaintainTime();
        Log.d(TAG, "getSwMaintainDays: 获取保养天数:" + day);
        maintainDaysLiveData.postValue(day);
    }

    /**
     * 获取雨刮维修禁用状态
     *
     * @return
     */
    public void getWiperModeAvailable() {
        int signalVal = carSettingManager.getWiperModeAvailable();
        wiperRepairDisableLiveData.postValue(signalVal);
    }

    public int getPowerMode() {
        int signVal = conditionManager.getPowerMode();
        return signVal;
    }

    /**
     * 遮阳帘当前状态
     */
    public void getSunShadeCurtainStatus() {
        int signalVal = conditionManager.getSunShadeCurtainStopped();
        SunShadeCurtainStatusLiveData.postValue(signalVal);
    }

    /**
     * 获取遮阳帘学习状态
     */
    private void getSunShadeTeachRunStatus() {
        int signalVal = conditionManager.getSunShadeTeachRunStatus();
        sunShadeTeachRunStatusLiveData.postValue(signalVal);
    }

    /**
     * 设置遮阳帘维修模式
     * 0: 静止
     * 1: 运行
     */
    public void setSunShadeRepairMode(int status) {
        sunShadeRepairModeLiveData.postValue(status);
    }

    /**
     * 开始遮阳帘维修
     */
    public void startSunShadeRepairMode() {
        if (MsgUtil.getInstance().supportPowerMode()) {
            conditionManager.setSunShadeCurtainModeSet(0x67);
        }
    }

    /**
     * 左前胎压获取
     */
    private void getLeftFrontTyrePressure() {
        int leftFrontTireKpaNum = conditionManager.getLeftFrontTireKpaNum();
        leftFrontTyrePressureLiveData.postValue(leftFrontTireKpaNum);
    }

    /**
     * 右前胎压获取
     */
    private void getRightFrontTyrePressure() {
        int rightFrontTireKpaNum = conditionManager.getRightFrontTireKpaNum();
        rightFrontTyrePressureLiveData.postValue(rightFrontTireKpaNum);
    }

    /**
     * 左后胎压获取
     */
    private void getLeftRearTyrePressure() {
        int leftRearTireKpaNum = conditionManager.getLeftRearTireKpaNum();
        leftRearTyrePressureLiveData.postValue(leftRearTireKpaNum);
    }

    /**
     * 右后胎压获取
     */
    private void getRightRearTyrePressure() {
        int rightRearTireKpaNum = conditionManager.getRightRearTireKpaNum();
        rightRearTyrePressureLiveData.postValue(rightRearTireKpaNum);
    }

    /**
     * 左前胎温获取
     */
    private void getLeftFrontTyreTemperature() {
        int leftFrontTireTempNum = conditionManager.getLeftFrontTireTemp();
        leftFrontTyreTemperatureLiveData.postValue(leftFrontTireTempNum);
    }

    /**
     * 右前胎温获取
     */
    private void getRightFrontTyreTemperature() {
        int rightFrontTireTempNum = conditionManager.getRightFrontTireTemp();
        rightFrontTyreTemperatureLiveData.postValue(rightFrontTireTempNum);
    }

    /**
     * 左后胎温获取
     */
    private void getLeftRearTyreTemperature() {
        int leftRearTireTempNum = conditionManager.getLeftRearTireTemp();
        leftRearTyreTemperatureLiveData.postValue(leftRearTireTempNum);
    }

    /**
     * 右后胎温获取
     */
    private void getRightRearTyreTemperature() {
        int rightRearTireTempNum = conditionManager.getRightRearTireTemp();
        rightRearTyreTemperatureLiveData.postValue(rightRearTireTempNum);
    }
}
