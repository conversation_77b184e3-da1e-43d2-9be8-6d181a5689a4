package com.bitech.vehiclesettings.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.WallpaperBean;
import com.bitech.vehiclesettings.bean.WallpaperDatas;
import com.bitech.vehiclesettings.presenter.display.WallpaperPresenter;
import com.bitech.vehiclesettings.utils.EToast;

import java.util.ArrayList;
import java.util.List;

public class WallpaperSceneAdapter extends RecyclerView.Adapter<WallpaperSceneAdapter.VH> {

    Context context;

    private int sceneSelectedIndex = 0; // 场景化默认选中


    List<WallpaperBean> wallpaperBeans = new ArrayList<>();

    public WallpaperSceneAdapter(Context context) {
        this.context = context;
        wallpaperBeans.addAll(WallpaperDatas.Scene.data);
        sceneSelectedIndex = WallpaperPresenter.getCurrentSceneWallpaper();
    }

    class VH extends RecyclerView.ViewHolder {

        public ImageView ivBackground;
        public TextView tvTag;
        public ImageView ivCheckbox;

        public VH(@NonNull View itemView) {
            super(itemView);
            ivBackground = itemView.findViewById(R.id.iv_background);
            tvTag = itemView.findViewById(R.id.tv_tag);
            ivCheckbox = itemView.findViewById(R.id.iv_checkbox);
        }
    }

    @NonNull
    @Override
    public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new VH(LayoutInflater.from(context).inflate(R.layout.item_wallpaper_rv_list, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VH holder, int position) {
        WallpaperBean wallpaperBean = wallpaperBeans.get(position);

        if (wallpaperBean.getName() == R.string.str_wrapper_to_be_continue) {
            holder.ivCheckbox.setVisibility(View.GONE);
            holder.ivBackground.setImageResource(R.mipmap.display_wp_store);
        }

        switch (position) {
            case 0: //  3D车模
                holder.ivBackground.setImageResource(R.mipmap.display_wp_3d);
                holder.ivCheckbox.setOnClickListener(v -> {
                    updateSelection(position);
                    WallpaperPresenter.setCurrentSceneWallpaper(position);
                });
                break;
            case 1: // 猫猫壁纸
                holder.ivBackground.setImageResource(R.mipmap.display_wp_cat);
                holder.ivCheckbox.setOnClickListener(v -> {
                    updateSelection(position);
                    WallpaperPresenter.setCurrentSceneWallpaper(position);
                });
                break;
            case 2: // 音乐壁纸
                holder.ivBackground.setImageResource(R.mipmap.display_wp_5g);
                holder.ivCheckbox.setOnClickListener(v -> {
                    updateSelection(position);
                });
                break;
        }

        if (position == sceneSelectedIndex) {
            holder.ivCheckbox.setImageResource(R.mipmap.icon_set_light_choose);
        } else {
            holder.ivCheckbox.setImageResource(R.mipmap.icon_set_light_choose_no);
        }


        holder.ivBackground.setImageURI(wallpaperBean.getUri());
        holder.tvTag.setText(wallpaperBean.getName());

        holder.itemView.setOnClickListener(view -> {
            // TODO 壁纸详情
            EToast.showToast(context, "壁纸详情", 0, false);
        });
    }

    private void updateSelection(int newIndex) {
        if (newIndex == sceneSelectedIndex) {
            return;
        }
        int oldIndex = sceneSelectedIndex;
        sceneSelectedIndex = newIndex;
        notifyItemChanged(oldIndex);
        notifyItemChanged(newIndex);
    }

    @Override
    public int getItemCount() {
        return wallpaperBeans.size();
    }
}
