package com.bitech.vehiclesettings.view.display;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDisplay;
import com.bitech.vehiclesettings.databinding.DialogAlertDisplayShowLyricsBinding;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.view.widget.UniversalSwitch;

public class ShowLyricsUIAlert extends BaseDialog {
    private static final String TAG = ShowLyricsUIAlert.class.getSimpleName();

    private UniversalSwitch swShowLyrics;
    private final Context context;
    private DialogAlertDisplayShowLyricsBinding binding;

    public ShowLyricsUIAlert(Context context, UniversalSwitch swShowLyrics) {
        super(context, R.style.Dialog);
        this.context = context;
        this.swShowLyrics = swShowLyrics;
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DialogAlertDisplayShowLyricsBinding.inflate(LayoutInflater.from(context));
        binding.btnConfirm.setOnClickListener(v -> {
            DisplayPresenter.setShowLyrics(CarDisplay.Lyrics.SHOW);
            swShowLyrics.setChecked(true);
            Log.d(TAG, "仪表显示歌词 确认");
            dismiss();
        });
        binding.btnCancel.setOnClickListener(v -> {
            Log.d(TAG, "仪表显示歌词 取消");
            swShowLyrics.setChecked(false);
            dismiss();
        });

        setContentView(binding.getRoot());
        // 获取对话框的Window对象
        Window window = getWindow();
        WindowManager.LayoutParams layoutParams = window.getAttributes();
        layoutParams.width = 1128; // 或者使用具体的像素值
        window.setAttributes(layoutParams);
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    public void cancel() {
        super.cancel();
        swShowLyrics.setChecked(false);
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

}
