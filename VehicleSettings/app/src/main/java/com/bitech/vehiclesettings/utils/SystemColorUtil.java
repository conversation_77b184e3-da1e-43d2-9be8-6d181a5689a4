package com.bitech.vehiclesettings.utils;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarSystemColor;

import java.util.Objects;

public class SystemColorUtil {
    public static int SystemColorValueToStyle(String value) {
        if (Objects.equals(value, CarSystemColor.SystemColorValue.BLUE)) {
            return CarSystemColor.SystemColorStyle.BLUE;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.PURPLE)) {
            return CarSystemColor.SystemColorStyle.PURPLE;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.CYAN)) {
            return CarSystemColor.SystemColorStyle.CYAN;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.GREEN)) {
            return CarSystemColor.SystemColorStyle.GREEN;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.ORANGE)) {
            return CarSystemColor.SystemColorStyle.ORANGE;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.RED)) {
            return CarSystemColor.SystemColorStyle.RED;
        }
        return -1;
    }

    public static int SystemColorStyleToIndex(int style) {
        if (style == CarSystemColor.SystemColorStyle.BLUE) {
            return 0;
        } else if (style == CarSystemColor.SystemColorStyle.PURPLE) {
            return 1;
        } else if (style == CarSystemColor.SystemColorStyle.CYAN) {
            return 2;
        } else if (style == CarSystemColor.SystemColorStyle.GREEN) {
            return 3;
        } else if (style == CarSystemColor.SystemColorStyle.ORANGE) {
            return 4;
        } else if (style == CarSystemColor.SystemColorStyle.RED) {
            return 5;
        }
        return -1;
    }

    public static String SystemColorValueIntToString(int value) {
        if (value == R.color.system_color_blue) {
            return CarSystemColor.SystemColorValue.BLUE;
        } else if (value == R.color.system_color_purple) {
            return CarSystemColor.SystemColorValue.PURPLE;
        } else if (value == R.color.system_color_cyan) {
            return CarSystemColor.SystemColorValue.CYAN;
        } else if (value == R.color.system_color_green) {
            return CarSystemColor.SystemColorValue.GREEN;
        } else if (value == R.color.system_color_orange) {
            return CarSystemColor.SystemColorValue.ORANGE;
        } else if (value == R.color.system_color_red) {
            return CarSystemColor.SystemColorValue.RED;
        }
        return CarSystemColor.SystemColorValue.BLUE;
    }

    public static int SystemColorStringToValueInt(String value) {
        if (Objects.equals(value, CarSystemColor.SystemColorValue.BLUE)) {
            return R.color.system_color_blue;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.PURPLE)) {
            return R.color.system_color_purple;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.CYAN)) {
            return R.color.system_color_cyan;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.GREEN)) {
            return R.color.system_color_green;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.ORANGE)) {
            return R.color.system_color_orange;
        } else if (Objects.equals(value, CarSystemColor.SystemColorValue.RED)) {
            return R.color.system_color_red;
        }
        return R.color.system_color_blue;
    }
}
