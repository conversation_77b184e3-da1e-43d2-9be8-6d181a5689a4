package com.bitech.vehiclesettings.view.connect;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.blankj.utilcode.util.StringUtils;

/**
 * FileName: NoTitleUIAlert
 * Author: WUY1WHU
 * Date: 2024/6/19 10:29
 * Description:通用对话框
 */
public class SimplyPromptUIAlert extends BaseDialog {

    private static onProgressChangedListener onProgressChangedListener;

    public SimplyPromptUIAlert(Context context) {
        super(context);
    }

    public SimplyPromptUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected SimplyPromptUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static SimplyPromptUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(SimplyPromptUIAlert.onProgressChangedListener onProgressChangedListener) {
        SimplyPromptUIAlert.onProgressChangedListener = onProgressChangedListener;
    }


    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private String promptText;
        private String promptColor;
        private SimplyPromptUIAlert dialog = null;
        private String confirmTxt;
        private int confirmColor = 0;

        public int getConfirmColor() {
            return confirmColor;
        }

        public void setConfirmColor(int confirmColor) {
            this.confirmColor = confirmColor;
        }

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public Builder setContent(String text) {
            this.promptText = text;
            return this;
        }

        public String getConfirmTxt() {
            return confirmTxt;
        }

        public void setConfirmTxt(String confirmTxt) {
            this.confirmTxt = confirmTxt;
        }

        public Builder setConfirmAndCancel(boolean flag) {

            return this;
        }

        /**
         * Create the custom dialog
         */
        public SimplyPromptUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new SimplyPromptUIAlert(context,
                        R.style.Dialog);
            }
            View layout = View.inflate(context, R.layout.dialog_alert_simply_prompt, null);
            TextView context = layout.findViewById(R.id.tv_simply_text);
            context.setText(promptText);


            TextView confirmBtn = layout.findViewById(R.id.tv_simply_confirm);
            if (!StringUtils.isEmpty(confirmTxt)) {
                confirmBtn.setText(confirmTxt);
            }
            if (confirmColor!=0){
                confirmBtn.setBackground(ContextCompat.getDrawable(this.context, confirmColor));
            }
            confirmBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getOnProgressChangedListener().onProgress(v.getId());
                }
            });
            TextView cancelBtn = layout.findViewById(R.id.tv_simply_cancel);
            cancelBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getOnProgressChangedListener().onProgress(v.getId());
                }
            });
            dialog.setCancelable(isCan);

            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);
            return dialog;
        }

    }

    public interface onProgressChangedListener {
        void onProgress(int progress);
    }

}
