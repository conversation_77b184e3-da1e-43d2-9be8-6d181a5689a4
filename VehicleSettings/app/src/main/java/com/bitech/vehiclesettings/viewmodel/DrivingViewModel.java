package com.bitech.vehiclesettings.viewmodel;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.interfaces.driving.IDrivingManagerListener;
import com.bitech.platformlib.manager.ConfigManager;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.bean.atmosphere.AmbLigBean;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.manager.CarConfigInfoManager;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.id.vehicle.VDEventVehicle;
import com.chery.ivi.vdb.event.id.vehicle.VDKeyVehicle;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public class DrivingViewModel extends ViewModel {
    public static final String TAG = "DrivingViewModel";

    // 车辆模式
    public final MutableLiveData<Integer> carModeLiveData = new MutableLiveData<>(CarDriving.VCC_1_DriveMode.ECO);
    // 极致纯电
    public final MutableLiveData<Integer> extremePureElectricLiveData = new MutableLiveData<>(CarDriving.ForcedEVMode.NOT_ACTIVE);
    // 陡坡缓降
    public final MutableLiveData<Integer> steepSlopeDescentLiveData = new MutableLiveData<>(CarDriving.HDCCtrlSts.OFF);
    int preSteepSlopeDescent = CarDriving.HDCCtrlSts.OFF;
    // 车身稳定控制
    public final MutableLiveData<Integer> bodyStabilityControlLiveData = new MutableLiveData<>(CarDriving.ESPSwitchStatus.ON);
    int preBodyStabilityControl = CarDriving.ESPSwitchStatus.ON;
    // 自动驻车
    public final MutableLiveData<Integer> autoParkingLiveData = new MutableLiveData<>(CarDriving.AVHSts.OFF);
    int preAutoParking = CarDriving.AVHSts.OFF;
    // 驻车制动
    public final MutableLiveData<Integer> parkingBrakeLiveData = new MutableLiveData<>(CarDriving.EPBActrSt.RELEASED);
    // 舒适制动
    public final MutableLiveData<Integer> comfortBrakingLiveData = new MutableLiveData<>(CarDriving.CST_Status.DISABLED);
    // 舒适制动等级
    public final MutableLiveData<Integer> comfortBrakingRankLiveData = new MutableLiveData<>(CarDriving.CST_SensitivitySts.LOW);
    // 悬架智能瞄准
    public final MutableLiveData<Integer> intelligentSuspensionAimingLiveData = new MutableLiveData<>(CarDriving.ASU_1_PreviewContFb.INACTIVE);
    int preIntelligentSuspensionAiming = CarDriving.ASU_1_PreviewContFb.INACTIVE;
    // 个性化-驾驶模式
    public final MutableLiveData<Integer> drivingModeLiveData = new MutableLiveData<>(CarDriving.FLZCU_PropulsionMode.COMFORTABLE);
    int preDrivingMode = CarDriving.FLZCU_PropulsionMode.COMFORTABLE;
    // 个性化-保电电量
    public final MutableLiveData<Integer> powerProtectionLiveData = new MutableLiveData<>(20);
    // 个性化-转向模式
    public final MutableLiveData<Integer> swerveModeLiveData = new MutableLiveData<>(CarDriving.FLZCU_SteeringMode.NORMAL);
    int preSwerveMode = CarDriving.FLZCU_SteeringMode.NORMAL;
    // 个性化-悬架模式
    public final MutableLiveData<Integer> suspensionModeLiveData = new MutableLiveData<>(CarDriving.FLZCU_SuspensionDamping.SOFT);
    int preSuspensionMode = CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE;
    // 个性化-制动模式
    public final MutableLiveData<Integer> brakingModeLiveData = new MutableLiveData<>(CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE);
    int preBrakingMode = CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE;
    // 牵引模式
    public final MutableLiveData<Integer> tractionModeLiveData = new MutableLiveData<>(CarDriving.VCU_TowingMode.NOT_ACTIVE);
    int preTractionMode = CarDriving.VCU_TowingMode.NOT_ACTIVE;
    // 牵引模式置灰信号
    public final MutableLiveData<Integer> tractionModeAvailableLiveData = new MutableLiveData<>(0);
    // 刹车信号
    public final MutableLiveData<Integer> brakePedalLiveData = new MutableLiveData<>(0);
    // 车速
    public final MutableLiveData<Integer> vehicleSpeedLiveData = new MutableLiveData<>(0);
    // 挡位
    public final MutableLiveData<Integer> gearPositionLiveData = new MutableLiveData<>(0);
    // 电源模式
    public final MutableLiveData<Integer> powerModeLiveData = new MutableLiveData<>(CarDriving.FLZCU_9_PowerMode.OFF);
    // 洗车模式 (常规洗车模式和传送带洗车模式)
    public final MutableLiveData<Integer> carWashModeLiveData = new MutableLiveData<>(0);
    // 洗车模式开启状态
    public final MutableLiveData<Integer> cleanModeStatusLiveData = new MutableLiveData<>(0);
    // 洗车模式错误原因
    public final MutableLiveData<Integer> carWashModeFailReasonLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 左前车窗状态
    public MutableLiveData<Integer> leftFrontCarWindowLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 左后车窗状态
    public MutableLiveData<Integer> leftRearCarWindowLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 右前车窗状态
    public MutableLiveData<Integer> rightFrontCarWindowLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 右后车窗状态
    public MutableLiveData<Integer> rightRearCarWindowLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 自动重上锁禁用
    public MutableLiveData<Integer> autoReLockInhibitLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 外后视镜
    public MutableLiveData<Integer> outRearMirrorLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 自动雨刮
    public MutableLiveData<Integer> autoWipingInhibitLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 远离上锁
    public MutableLiveData<Integer> onLeaveLockInhibitLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 电动尾翼 - 需求取消
    // public MutableLiveData<Integer> sopilerInhibitLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 电释放 - 外部自动开门
    public MutableLiveData<Integer> outAutoOpenLiveData = new MutableLiveData<>(0);
    // 洗车模式 - 空调内循环
    public MutableLiveData<Integer> autoCirculationModeDisplayStsLiveData = new MutableLiveData<>(0);
    // 车辆设置 - KeyInputEvent
    public final MutableLiveData<Integer> keyInputEventLiveData = new MutableLiveData<>();
    // 牵引模式失败原因
    public final MutableLiveData<Integer> tractionModeFailReasonLiveData = new MutableLiveData<>(0);
    // 驾驶员状态检测
    public MutableLiveData<Integer> driveStateLiveData = new MutableLiveData<>(0);
    // 疲劳检测
    public MutableLiveData<Integer> fatigueDetectionLiveData = new MutableLiveData<>(1);
    // 视线分心提醒
    public MutableLiveData<Integer> distractionLiveData = new MutableLiveData<>(1);
    // 主驾打电话提醒
    public MutableLiveData<Integer> callLiveData = new MutableLiveData<>(1);

    // 车辆设置 - 车辆模式配置字
    public MutableLiveData<Integer> drivingModeConfigLiveData = new MutableLiveData<>(0x9);

    // 方控按键
    private long pressStartTime = 0; // 记录按键按下时刻的时间戳
    private static final int SHORT_PRESS_THRESHOLD = 200; // 短按时间阈值100毫秒
    private static final int LONG_PRESS_THRESHOLD = 1200; // 长按时间阈值2000毫秒(2秒)
    private boolean isProcessingPress = false; // 防止重复处理标志

    private final DrivingManager drivingManager = (DrivingManager) BitechCar.getInstance()
            .getServiceManager(BitechCar.CAR_DRIVING_MANAGER);
    private final NewEnergyManager newEnergyManager = (NewEnergyManager) BitechCar.getInstance()
            .getServiceManager(BitechCar.CAR_ENERGY_MANAGER);
    private final ConfigManager configInfoManager = (ConfigManager) BitechCar.getInstance()
            .getServiceManager(BitechCar.CONFIG_MANAGER);

    public final AtomicBoolean supportPowerMode = new AtomicBoolean();

    private final ExecutorService executor = Executors.newCachedThreadPool();

    private Handler handler = new Handler(Looper.getMainLooper());
    private Runnable getCarModeRunnable;

    public void initData() {
        //添加信号监听
        addCallback();
        Log.d(TAG, "DrivingViewModel 初始化");
        //获取车辆模式
        executor.execute(() -> {
            supportPowerMode.set(MsgUtil.getInstance().supportPowerMode());
            // 获取车辆模式
            getCarMode();
            // 获取车辆模式配置字
            getCarModeConfig();
            getBrakePedal();
            // 电源模式
            getPowerMode();
            //获取驻车制动
            getParkingBrake();
            // 悬架模式
            getSuspensionMode();
            // 极致纯电
            getExtremePureSignal();
            // 驾驶模式
            getDrivingMode();
            // 自动驻车
            getAutoParking();
            // 悬架智能瞄准
            getIntelligentSuspensionAiming();
            // 陡坡缓降
            getSteepSlopeDescent();
            // 车身稳定控制
            getBodyStabilityControl();
            // 舒适制动
            getComfortBraking();
            // 舒适制动等级
            getComfortBrakingRank();
            // 转向模式
            getSwerveMode();
            // 制动模式
            getBrakingMode();
            // 保电电量
            getPowerProtection();
            // 驾驶模式
            getDrivingMode();
            // 车速
            getCarSpeed();
            // 挡位
            getGearPosition();
            // 牵引模式
            getTractionMode();
            // 牵引模式状态
            getTowModeAvailable();
            // 洗车模式
            // 0 常规洗车 1 传送带洗车
            setCarWashMode(0);
            // 洗车模式开启状态
            getCleanModeStatus();
            // 洗车模式 - 左前车窗
            getLeftFrontCarWindow();
            // 洗车模式 - 右前车窗
            getRightFrontCarWindow();
            // 洗车模式 - 左后车窗
            getLeftRearCarWindow();
            // 洗车模式 - 右后车窗
            getRightRearCarWindow();
            // 洗车模式 - 自动重上锁禁用
            getAutoReLockInhibit();
            // 洗车模式 - 外后视镜
            getOutRearMirror();
            // 洗车模式 - 自动雨刮
            getAutoWipingInhibit();
            // 洗车模式 - 远离上锁
            getOnLeaveLockInhibit();
            // 洗车模式 - 电动尾翼 -- 需求取消
//            getSopilerInhibit();
            // 洗车模式 - 电释放 - 外部自动开门
            getOutAutoOpen();
            // 洗车模式 - 空调内循环
            getAutoCirculationModeDisplaySts();
        });
    }

    private void getCarModeConfig() {
        int signalVal = configInfoManager.getModelCode();
        drivingModeConfigLiveData.postValue(signalVal);
    }

    private void getTowModeAvailable() {
        int signalVal = newEnergyManager.getTowModeAvailable();
        tractionModeAvailableLiveData.postValue(signalVal);
    }

    /**
     * 洗车模式 - 空调内循环
     */
    private void getAutoCirculationModeDisplaySts() {
        int signalVal = drivingManager.getTMS_CirculationModeDisplaySts();
        if (signalVal == CarDriving.TMS_CirculationModeDisplaySts.RECIRCULATION_MODE
                || signalVal == CarDriving.TMS_CirculationModeDisplaySts.AIR_CIRCULATION_MODE
                || signalVal == CarDriving.TMS_CirculationModeDisplaySts.TWO_LAYER_FLOW_MODE
                || signalVal == CarDriving.TMS_CirculationModeDisplaySts.AUTO) {
            autoCirculationModeDisplayStsLiveData.postValue(signalVal);
        } else {
            autoCirculationModeDisplayStsLiveData.postValue(autoReLockInhibitLiveData.getValue() == null ? CarDriving.TMS_CirculationModeDisplaySts.RECIRCULATION_MODE : autoReLockInhibitLiveData.getValue());
        }
    }

    /**
     * 洗车模式 - 电动门释放 - 外部自动开门
     */
    private void getOutAutoOpen() {
        int signalVal = drivingManager.getFLZCU_OutsideReleaseInhibit();
        if (signalVal == CarDriving.FLZCU_OutsideReleaseInhibit.INHIBIT || signalVal == CarDriving.FLZCU_OutsideReleaseInhibit.NOT_INHIBIT) {
            outAutoOpenLiveData.postValue(signalVal);
        } else {
            outAutoOpenLiveData.postValue(outAutoOpenLiveData.getValue() == null ? CarDriving.FLZCU_OutsideReleaseInhibit.NOT_INHIBIT : outAutoOpenLiveData.getValue());
        }
    }

    /**
     * 洗车模式 - 电动尾翼 -- 需求取消
     */
//    private void getSopilerInhibit() {
//        sopilerInhibitLiveData.postValue(drivingManager.getFLZCU_SopilerInhibit());
//    }

    /**
     * 洗车模式 - 远离上锁
     */
    public void getOnLeaveLockInhibit() {
        int signalVal = drivingManager.getFLZCU_LeavelockInhibit();
        if (signalVal == CarDriving.FLZCU_LeavelockInhibit.INHIBIT || signalVal == CarDriving.FLZCU_LeavelockInhibit.NOT_INHIBIT) {
            onLeaveLockInhibitLiveData.postValue(signalVal);
        } else {
            onLeaveLockInhibitLiveData.postValue(onLeaveLockInhibitLiveData.getValue() == null ? CarDriving.FLZCU_LeavelockInhibit.NOT_INHIBIT : onLeaveLockInhibitLiveData.getValue());
        }
    }

    /**
     * 洗车模式 - 自动雨刮
     */
    private void getAutoWipingInhibit() {
        int signalVal = drivingManager.getFLZCU_AutoWipingInhibit();
        if (signalVal == CarDriving.FLZCU_AutoWipingInhibit.INHIBIT || signalVal == CarDriving.FLZCU_AutoWipingInhibit.NOT_INHIBIT) {
            autoWipingInhibitLiveData.postValue(signalVal);
        } else {
            autoWipingInhibitLiveData.postValue(autoWipingInhibitLiveData.getValue() == null ? CarDriving.FLZCU_AutoWipingInhibit.NOT_INHIBIT : autoWipingInhibitLiveData.getValue());
        }
    }

    /**
     * 洗车模式 - 外后视镜
     */
    private void getOutRearMirror() {
        int signalVal = drivingManager.getRearViewFoldSts();
        if (signalVal == CarDriving.RearViewFoldSts.FOLD || signalVal == CarDriving.RearViewFoldSts.UNFOLD || signalVal == CarDriving.RearViewFoldSts.INVALID) {
            outRearMirrorLiveData.postValue(signalVal);
        } else {
            outRearMirrorLiveData.postValue(outRearMirrorLiveData.getValue() == null ? CarDriving.RearViewFoldSts.INVALID : outRearMirrorLiveData.getValue());
        }
    }

    /**
     * 洗车模式 - 自动重上锁禁用
     */
    private void getAutoReLockInhibit() {
        int signalVal = drivingManager.getFLZCU_RelockInhibit();
        if (signalVal == CarDriving.FLZCU_LeavelockInhibit.INHIBIT || signalVal == CarDriving.FLZCU_LeavelockInhibit.NOT_INHIBIT) {
            autoReLockInhibitLiveData.postValue(signalVal);
        } else {
            autoReLockInhibitLiveData.postValue(autoReLockInhibitLiveData.getValue() == null ? CarDriving.FLZCU_LeavelockInhibit.NOT_INHIBIT : autoReLockInhibitLiveData.getValue());
        }
    }

    /**
     * 洗车模式 - 右后车窗
     */
    private void getRightRearCarWindow() {
        int signalVal = drivingManager.getFRZCU_RRWinSts();
        if (signalVal >= 0 && signalVal <= 100) rightRearCarWindowLiveData.postValue(signalVal);
        else rightRearCarWindowLiveData.postValue(0);
    }

    /**
     * 洗车模式 - 左后车窗
     */
    private void getLeftRearCarWindow() {
        int signalVal = drivingManager.getFLZCU_RLWinSts();
        if (signalVal >= 0 && signalVal <= 100) leftRearCarWindowLiveData.postValue(signalVal);
        else leftRearCarWindowLiveData.postValue(0);
    }

    /**
     * 洗车模式 - 右前车窗
     */
    private void getRightFrontCarWindow() {
        int signalVal = drivingManager.getFRZCU_FRWinSts();
        if (signalVal >= 0 && signalVal <= 100) rightFrontCarWindowLiveData.postValue(signalVal);
        else rightFrontCarWindowLiveData.postValue(0);
    }

    /**
     * 洗车模式 - 左前车窗
     */
    public void getLeftFrontCarWindow() {
        int signalVal = drivingManager.getFLZCU_FLWinSts();
        if (signalVal >= 0 && signalVal <= 100) leftFrontCarWindowLiveData.postValue(signalVal);
        else leftFrontCarWindowLiveData.postValue(0);
    }

    public void getDriveState() {

    }

    public void setCleanModeStatus(int signalVal) {
        drivingManager.setICC_CleanMode(signalVal);
    }

    public void getCleanModeStatus() {
        int signalVal = drivingManager.getFLZCU_CleanModeStatus();
        cleanModeStatusLiveData.postValue(signalVal);
    }

    // 设置洗车模式
    public void setCarWashMode(int status) {
        carWashModeLiveData.postValue(status);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        executor.shutdown();
    }

    private void addCallback() {
        drivingManager.addCallback(TAG, new IDrivingManagerListener() {

            @Override
            public void driveModeCallback(int signalVal) {
                Log.d(TAG, "车辆模式 carModeCallback signalVal: " + signalVal);
                if (signalVal == CarDriving.VCC_1_DriveMode.ECO
                        || signalVal == CarDriving.VCC_1_DriveMode.NORMAL
                        || signalVal == CarDriving.VCC_1_DriveMode.SPORT
                        || signalVal == CarDriving.VCC_1_DriveMode.SNOW
                        || signalVal == CarDriving.VCC_1_DriveMode.INDIVIDUAL
                        || signalVal == CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID) {
                    carModeLiveData.postValue(signalVal);
                } else {
                    Log.w(TAG, "收到无效的车辆模式信号: " + signalVal);
                    // 可以选择保持之前的状态或处理无效值
                    Integer carModeLiveDataValue = carModeLiveData.getValue();
                    carModeLiveData.postValue(carModeLiveDataValue == null ? CarDriving.VCC_1_DriveMode.ECO : carModeLiveDataValue);
                }
            }

            @Override
            public void steeringModeCallback(int signalVal) {
                // 转向模式
                Log.d(TAG, "转向模式 steeringModeCallback signalVal: " + signalVal);
                if (signalVal == CarDriving.FLZCU_SteeringMode.NORMAL || signalVal == CarDriving.FLZCU_SteeringMode.SPORT) {
                    swerveModeLiveData.postValue(signalVal);
                    preSwerveMode = signalVal;
                } else {
                    swerveModeLiveData.postValue(preSwerveMode);
                }
            }

            @Override
            public void suspensionDampingCallback(int signalVal) {
                Log.d(TAG, "悬架模式 suspensionDampingCallback signalVal: " + signalVal);
                if (signalVal == CarDriving.FLZCU_SuspensionDamping.SOFT || signalVal == CarDriving.FLZCU_SuspensionDamping.HARD) {
                    suspensionModeLiveData.postValue(signalVal);
                    preSuspensionMode = signalVal;
                } else {
                    suspensionModeLiveData.postValue(preSuspensionMode);
                }
            }

            @Override
            public void pedalComfortCallback(int signalVal) {
                Log.d(TAG, "制动模式 pedalComfortCallback signalVal: " + signalVal);
                if (signalVal == CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE || signalVal == CarDriving.FLZCU_BrakePedalFeelMode.SPORT) {
                    brakingModeLiveData.postValue(signalVal);
                    preBrakingMode = signalVal;
                } else {
                    brakingModeLiveData.postValue(preBrakingMode);
                }
            }

            @Override
            public void propulsionModeCallback(int signalVal) {
                Log.d(TAG, "动力模式（驾驶模式） propulsionModeCallback signalVal: " + signalVal);
                if (signalVal == CarDriving.FLZCU_PropulsionMode.COMFORTABLE
                        || signalVal == CarDriving.FLZCU_PropulsionMode.NORMAL
                        || signalVal == CarDriving.FLZCU_PropulsionMode.SPORT
                        || signalVal == CarDriving.FLZCU_PropulsionMode.SNOW) {
                    drivingModeLiveData.postValue(signalVal);
                    preDrivingMode = signalVal;
                } else {
                    drivingModeLiveData.postValue(preDrivingMode);
                }
            }

            @Override
            public void towModeAvailableCallback(int signVal) {
                Log.d(TAG, "牵引模式置灰 towModeAvailableCallback signalVal: " + signVal);
                tractionModeAvailableLiveData.postValue(signVal);
            }

            /**
             * 陡坡缓降
             * 当用戶设置该功能后，IHU显示界面设置为用戶选择的状态，并在2秒后同步总线反馈的状态。HDCCtrlSts=0x1:
             * flash not active braking/0x2: On active braking，
             * 功能开启HDCCtrlSts=0x0: OFF，功能关闭
             * @param signVal
             */
            @Override
            public void hillDescentControlCallback(int signVal) {
                Log.d(TAG, "陡坡缓降 hillDescentControlCallback signalVal: " + signVal);
                if (signVal == CarDriving.HDCCtrlSts.OFF || signVal == CarDriving.HDCCtrlSts.ON_ACTIVE_BRAKING || signVal == CarDriving.HDCCtrlSts.NOT_ACTIVE) {
                    steepSlopeDescentLiveData.postValue(signVal);
                    preSteepSlopeDescent = signVal;
                } else {
                    steepSlopeDescentLiveData.postValue(preSteepSlopeDescent);
                }
            }

            // 驻车制动
            @Override
            public void getEPBStatusCallback(int signalVal) {
                Log.d(TAG, "驻车制动 getEPBStatusCallback: " + signalVal);
                if (signalVal == CarDriving.EPBActrSt.APPLIED
                        || signalVal == CarDriving.EPBActrSt.RELEASED
                        || signalVal == CarDriving.EPBActrSt.COMPLETELY_RELEASED) {
                    parkingBrakeLiveData.postValue(signalVal);
                } else {
                    if (signalVal == CarDriving.EPBActrSt.RELEASING || signalVal == CarDriving.EPBActrSt.APPLYING)
                        return;
                    parkingBrakeLiveData.postValue(parkingBrakeLiveData.getValue());
                }
            }

            // 牵引模式回调
            @Override
            public void towModeCallback(int signalVal) {
                Log.d(TAG, "牵引模式 towModeCallback: " + signalVal);
                if (signalVal == CarDriving.VCU_TowingMode.NOT_ACTIVE || signalVal == CarDriving.VCU_TowingMode.ACTIVE) {
                    tractionModeLiveData.postValue(signalVal);
                    preTractionMode = signalVal;
                } else {
                    tractionModeLiveData.postValue(preTractionMode);
                }
            }

            @Override
            public void cstASUPreviewContentCallback(int signalVal) {
                // 悬架智能预瞄
                Log.d(TAG, "悬架智能预瞄 cstASUPreviewContentCallback: " + signalVal);
                if (signalVal == CarDriving.ASU_1_PreviewContFb.INACTIVE || signalVal == CarDriving.ASU_1_PreviewContFb.ACTIVE) {
                    intelligentSuspensionAimingLiveData.postValue(signalVal);
                    preIntelligentSuspensionAiming = signalVal;
                } else {
                    intelligentSuspensionAimingLiveData.postValue(preIntelligentSuspensionAiming);
                }
            }

            @Override
            public void bodyInfoESCCallback(int signalVal) {
                // 车身稳定控制
                Log.d(TAG, "车身稳定控制 bodyInfoESCCallback: " + signalVal);
                if (signalVal == CarDriving.ESPSwitchStatus.ON || signalVal == CarDriving.ESPSwitchStatus.OFF) {
                    bodyStabilityControlLiveData.postValue(signalVal);
                    preBodyStabilityControl = signalVal;
                } else {
                    bodyStabilityControlLiveData.postValue(preBodyStabilityControl);
                }
            }

            @Override
            public void cstInterventionCallback(int signalVal) {
                // 舒适制动等级
                Log.d(TAG, "舒适制动等级 cstInterventionCallback: " + signalVal);
                if (signalVal == CarDriving.CST_SensitivitySts.LOW || signalVal == CarDriving.CST_SensitivitySts.MEDIUM || signalVal == CarDriving.CST_SensitivitySts.HIGH) {
                    comfortBrakingRankLiveData.postValue(signalVal);
                } else {
                    comfortBrakingRankLiveData.postValue(comfortBrakingRankLiveData.getValue());
                }
            }

            @Override
            public void cstEnableStatusCallback(int signalVal) {
                // 舒适制动
                Log.d(TAG, "舒适制动 cstInterventionCallback: " + signalVal);
                if (signalVal == CarDriving.CST_Status.DISABLED || signalVal == CarDriving.CST_Status.STANDBY || signalVal == CarDriving.CST_Status.ACTIVE || signalVal == CarDriving.CST_Status.FAILURE) {
                    comfortBrakingLiveData.postValue(signalVal);
                } else {
                    comfortBrakingLiveData.postValue(comfortBrakingLiveData.getValue());
                }
            }

            @Override
            public void autoHoldCallback(int signalVal) {
                // 自动驻车
                Log.d(TAG, "自动驻车 autoHoldCallback: " + signalVal);
                if (signalVal == CarDriving.AVHSts.OFF || signalVal == CarDriving.AVHSts.ACTIVE || signalVal == CarDriving.AVHSts.STANDBY) {
                    autoParkingLiveData.postValue(signalVal);
                    preAutoParking = signalVal;
                } else {
                    autoParkingLiveData.postValue(preAutoParking);
                }
            }

            @Override
            public void drivingKeyInputEventCallback(int signalVal) {
                Log.d(TAG, "方控键切换驾驶模式 drivingKeyInputEventCallback: " + signalVal);
                switch (signalVal) {
                    case 0x1: // 按键按下
                        if (!isProcessingPress) {
                            pressStartTime = System.currentTimeMillis(); // 记录按下时刻
                            isProcessingPress = true;
                            keyInputEventLiveData.postValue(CarDriving.DrivingKeyInputEvent.LONG_PRESS);
                        }
                        break;
                    case 0x0: // 按键释放
                        if (isProcessingPress) {
                            long pressDuration = System.currentTimeMillis() - pressStartTime;
                            isProcessingPress = false;
                            if (pressDuration >= LONG_PRESS_THRESHOLD) {
                                // 长按处理逻辑（2秒以上）
                                Log.d(TAG, "检测到长按操作，持续时间: " + pressDuration + "ms");

                            } else if (pressDuration >= SHORT_PRESS_THRESHOLD) {
                                // 短按处理逻辑（200ms-1200ms之间）
                                Log.d(TAG, "检测到短按操作，持续时间: " + pressDuration + "ms");
                                keyInputEventLiveData.postValue(CarDriving.DrivingKeyInputEvent.SHORT_PRESS);
                            }
                        }
                        break;
                }
            }

            @Override
            public void brakePedalCallback(int signalVal) {
                // 刹车信号
                Log.d(TAG, "刹车信号 brakePedalCallback: " + signalVal);
                if (signalVal == CarDriving.BrakePedalSts.APPLIED || signalVal == CarDriving.BrakePedalSts.NOT_APPLIED) {
                    brakePedalLiveData.postValue(signalVal);
                }
            }

            @Override
            public void powerProtectionCallback(int signalVal) {
                Log.d(TAG, "powerProtectionCallback: " + signalVal);
                if (signalVal == Integer.MIN_VALUE) return;
                if (signalVal < 20 || signalVal > 80) return;
                powerProtectionLiveData.postValue(signalVal);
            }

            /**
             * 电源模式
             */
            @Override
            public void onPowerModeChanged(int signalVal) {
                Log.d(TAG, "onPowerModeChanged: " + signalVal);
                if (signalVal == Integer.MIN_VALUE) return;
                powerModeLiveData.postValue(signalVal);
            }

            /**
             * 档位
             */
            @Override
            public void onGearPositionChanged(int signalVal) {
                Log.d(TAG, "onGearPositionChanged: " + signalVal);
                if (signalVal == Integer.MIN_VALUE) return;
                gearPositionLiveData.postValue(signalVal);
            }

            /**
             * 车速
             */
            @Override
            public void getVehicleSpeed(int signalVal) {
                Log.d(TAG, "getVehicleSpeed: " + signalVal);
                if (signalVal == Integer.MIN_VALUE) return;
                vehicleSpeedLiveData.postValue(signalVal);
            }

            /**
             * 洗车模式状态
             */
            @Override
            public void onCarWashModeStatusChanged(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                cleanModeStatusLiveData.postValue(signalVal);
            }

            /**
             * 车窗 - 左前
             */
            @Override
            public void onLeftFrontWindowStateChanged(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                if (signalVal >= 0 && signalVal <= 100)
                    leftFrontCarWindowLiveData.postValue(signalVal);
                else leftFrontCarWindowLiveData.postValue(0);
            }

            /**
             * 车窗 - 右前
             */
            @Override
            public void onRightFrontWindowStateChanged(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                if (signalVal >= 0 && signalVal <= 100)
                    rightFrontCarWindowLiveData.postValue(signalVal);
                else rightFrontCarWindowLiveData.postValue(0);
            }

            /**
             * 车窗 - 左后
             */
            @Override
            public void onLeftRearWindowStateChanged(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                if (signalVal >= 0 && signalVal <= 100)
                    leftRearCarWindowLiveData.postValue(signalVal);
                else leftRearCarWindowLiveData.postValue(0);
            }

            /**
             * 车窗 - 右后
             */
            @Override
            public void onRightRearWindowStateChanged(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                if (signalVal >= 0 && signalVal <= 100)
                    rightRearCarWindowLiveData.postValue(signalVal);
                else rightRearCarWindowLiveData.postValue(0);
            }

            /**
             * 洗车模式 - 自动重上锁
             */
            @Override
            public void onAutoReLockInhibit(int signalVal) {
                if (signalVal == CarDriving.FLZCU_LeavelockInhibit.INHIBIT || signalVal == CarDriving.FLZCU_LeavelockInhibit.NOT_INHIBIT) {
                    autoReLockInhibitLiveData.postValue(signalVal);
                } else {
                    autoReLockInhibitLiveData.postValue(autoReLockInhibitLiveData.getValue() == null ? CarDriving.FLZCU_LeavelockInhibit.NOT_INHIBIT : autoReLockInhibitLiveData.getValue());
                }
            }

            /**
             * 洗车模式 - 后视镜折叠
             */
            @Override
            public void onRearViewMirrorFoldStateChanged(int signalVal) {
                if (signalVal == CarDriving.RearViewFoldSts.FOLD || signalVal == CarDriving.RearViewFoldSts.UNFOLD || signalVal == CarDriving.RearViewFoldSts.INVALID) {
                    outRearMirrorLiveData.postValue(signalVal);
                } else {
                    outRearMirrorLiveData.postValue(outRearMirrorLiveData.getValue() == null ? CarDriving.RearViewFoldSts.INVALID : outRearMirrorLiveData.getValue());
                }
            }

            /**
             * 洗车模式 - 自动雨刮
             */
            @Override
            public void onAutoWiperInhibit(int signalVal) {
                if (signalVal == CarDriving.FLZCU_AutoWipingInhibit.INHIBIT || signalVal == CarDriving.FLZCU_AutoWipingInhibit.NOT_INHIBIT) {
                    autoWipingInhibitLiveData.postValue(signalVal);
                } else {
                    autoWipingInhibitLiveData.postValue(autoWipingInhibitLiveData.getValue() == null ? CarDriving.FLZCU_AutoWipingInhibit.NOT_INHIBIT : autoWipingInhibitLiveData.getValue());
                }
            }

            /**
             * 洗车模式 - 远离上锁
             */
            @Override
            public void onLeaveLockInhibit(int signalVal) {
                if (signalVal == CarDriving.FLZCU_LeavelockInhibit.INHIBIT || signalVal == CarDriving.FLZCU_LeavelockInhibit.NOT_INHIBIT) {
                    onLeaveLockInhibitLiveData.postValue(signalVal);
                } else {
                    onLeaveLockInhibitLiveData.postValue(onLeaveLockInhibitLiveData.getValue() == null ? CarDriving.FLZCU_LeavelockInhibit.NOT_INHIBIT : onLeaveLockInhibitLiveData.getValue());
                }
            }
            /**
             * 洗车模式 - 电动尾翼 - 需求取消
             */
//            @Override
//            public void onSpoilerInhibit(int signalVal) {
//                sopilerInhibitLiveData.postValue(signalVal);
//            }

            /**
             * 洗车模式 - 空调内循环
             */
            @Override
            public void onAirCirculationModeChanged(int signalVal) {
                if (signalVal == CarDriving.TMS_CirculationModeDisplaySts.RECIRCULATION_MODE
                        || signalVal == CarDriving.TMS_CirculationModeDisplaySts.AIR_CIRCULATION_MODE
                        || signalVal == CarDriving.TMS_CirculationModeDisplaySts.TWO_LAYER_FLOW_MODE
                        || signalVal == CarDriving.TMS_CirculationModeDisplaySts.AUTO) {
                    Log.d(TAG, "空调内循环 onAirCirculationModeChanged: " + signalVal);
                    autoCirculationModeDisplayStsLiveData.postValue(signalVal);
                } else {
                    autoCirculationModeDisplayStsLiveData.postValue(autoReLockInhibitLiveData.getValue() == null ? CarDriving.TMS_CirculationModeDisplaySts.RECIRCULATION_MODE : autoReLockInhibitLiveData.getValue());
                }
            }

            /**
             * 洗车模式 - 电释放状态
             */
            @Override
            public void onOutsideReleaseInhibit(int signalVal) {
                if (signalVal == CarDriving.FLZCU_OutsideReleaseInhibit.INHIBIT || signalVal == CarDriving.FLZCU_OutsideReleaseInhibit.NOT_INHIBIT) {
                    outAutoOpenLiveData.postValue(signalVal);
                } else {
                    outAutoOpenLiveData.postValue(outAutoOpenLiveData.getValue() == null ? CarDriving.FLZCU_OutsideReleaseInhibit.NOT_INHIBIT : outAutoOpenLiveData.getValue());
                }
            }

            /**
             * tractionModeFailReasonCallback
             * 牵引模式失败原因
             * @param signalVal
             */
            @Override
            public void tractionModeFailReasonCallback(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                tractionModeFailReasonLiveData.postValue(signalVal);
            }

            /**
             * 极致纯电模式
             * @param signalVal
             */
            @Override
            public void evModeCallback(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                extremePureElectricLiveData.postValue(signalVal);
            }

            @Override
            public void dmsCameraStatusCallback(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                driveStateLiveData.postValue(signalVal);
            }
        });
        drivingManager.registerListener();
    }

    /**
     * 车辆模式-发送
     *
     * @param signalVal
     */
    public void setCarMode(int signalVal) {
        // 舒适制动
        if (signalVal == CarDriving.ICC_DriveModeSet_Req.ECO && carModeLiveData.getValue() == CarDriving.VCC_1_DriveMode.ECO)
            return;
            // 节能混动
        else if (signalVal == CarDriving.ICC_DriveModeSet_Req.ENERGY_SAVING_HYBRID && carModeLiveData.getValue() == CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID)
            return;
            // 舒适制动
        else if (signalVal == CarDriving.ICC_DriveModeSet_Req.NORMAL && carModeLiveData.getValue() == CarDriving.VCC_1_DriveMode.NORMAL)
            return;
            // 风云GT
        else if (signalVal == CarDriving.ICC_DriveModeSet_Req.SPORT && carModeLiveData.getValue() == CarDriving.VCC_1_DriveMode.SPORT)
            return;
            // 雨雪模式
        else if (signalVal == CarDriving.ICC_DriveModeSet_Req.RAIN_SNOW && carModeLiveData.getValue() == CarDriving.VCC_1_DriveMode.SNOW)
            return;
            // 个性化模式
        else if (signalVal == CarDriving.ICC_DriveModeSet_Req.INDIVIDUAL && carModeLiveData.getValue() == CarDriving.VCC_1_DriveMode.INDIVIDUAL)
            return;
        drivingManager.setDrivingModeSet(signalVal);
    }

    public void setPrivacyPolicyStatus(int status) {

    }

    public int getPrivacyPolicyStatus() {
        return Prefs.get(PrefsConst.PRIVACY_POLICY_STATUS, CarDriving.PrivacyPolicySts.NOT_AGREE);
    }

    /**
     * 获取车辆模式
     */
    public void getCarMode() {
        int signalVal = drivingManager.getDriveMode();
        if (signalVal == CarDriving.VCC_1_DriveMode.ECO
                || signalVal == CarDriving.VCC_1_DriveMode.NORMAL
                || signalVal == CarDriving.VCC_1_DriveMode.SPORT
                || signalVal == CarDriving.VCC_1_DriveMode.SNOW
                || signalVal == CarDriving.VCC_1_DriveMode.INDIVIDUAL
                || signalVal == CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID) {
            Log.d(TAG, "收到车辆模式信号: " + signalVal);
            carModeLiveData.postValue(signalVal);
        } else {
            Log.w(TAG, "收到无效的车辆模式信号: " + signalVal);
            Integer value = carModeLiveData.getValue();
            carModeLiveData.postValue(value == null ? CarDriving.VCC_1_DriveMode.ECO : value);
        }
    }

    /**
     * Case1：1. 前置条件（a&b）:a.电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
     * b.PDCS(HCU)满足切换条件信号HCU_ForcedEVAvailable=0x0:Available；
     * 2. 触发条件（a&b）:a.极致纯电为纯电优先的二级菜单设置，用戶进入驾驶设置页面，首先点击纯电优先软开关；
     * b.在纯电优先模式下，用戶再点击触控开关开启极致纯电模式；
     * 3. 执行输出（a）：a.发送极致纯电信号ICC_ForcedEVMode=0x1:On至FLZCU，同时收到FLZCU发送的ForcedEVMode=0x1:Active，显示极致纯电模式；
     *
     * @param isChecked
     */
    public void setExtremePureSignal(boolean isChecked) {
        // TODO PDCS(HCU)满足切换条件信号HCU_ForcedAVAvailable=0x0:Available；
        if (!MsgUtil.getInstance().supportPowerMode()) {
            return;
        }
        int status = extremePureElectricLiveData.getValue();
        if (isChecked) {
            if (status == CarDriving.ForcedEVMode.ACTIVE) {
                return;
            }
        } else {
            if (status == CarDriving.ForcedEVMode.NOT_ACTIVE) {
                return;
            }
        }
        drivingManager.setEVModeSet(isChecked ? CarDriving.ICC_ForcedEVMode.ON : CarDriving.ICC_ForcedEVMode.OFF);
    }


    public void getExtremePureSignal() {
        int signalVal = drivingManager.getEVMode();
        extremePureElectricLiveData.postValue(signalVal);
    }


    /**
     * 陡坡缓降发送
     *
     * @param isChecked
     */
    public void setSteepSlopeDescent(boolean isChecked) {
        if (!MsgUtil.getInstance().supportPowerMode()) { // 获取电源状态
            return;
        }
        int status = steepSlopeDescentLiveData.getValue();
        if (isChecked) {
            if (status == CarDriving.HDCCtrlSts.NOT_ACTIVE || status == CarDriving.HDCCtrlSts.ON_ACTIVE_BRAKING) {
                return;
            }
        } else {
            if (status == CarDriving.HDCCtrlSts.OFF) {
                return;
            }
        }
        drivingManager.setHillDescentControlSet(isChecked ? CarDriving.TIHU_SetHDCOnOff.ON : CarDriving.TIHU_SetHDCOnOff.OFF);
    }


    /**
     * 陡坡缓降获取
     */
    public void getSteepSlopeDescent() {
        int signVal = drivingManager.getHillDescentControl();
        if (signVal == CarDriving.HDCCtrlSts.OFF || signVal == CarDriving.HDCCtrlSts.ON_ACTIVE_BRAKING || signVal == CarDriving.HDCCtrlSts.NOT_ACTIVE) {
            steepSlopeDescentLiveData.postValue(signVal);
            preSteepSlopeDescent = signVal;
        } else {
            steepSlopeDescentLiveData.postValue(preSteepSlopeDescent);
        }
    }

    /**
     * 车身稳定控制
     *
     * @param isChecked
     */

    public void setBodyStabilityControl(boolean isChecked) {
        // TODO 电源信号为 ON 连续发三帧 然后发0x0
        Log.d(TAG, "setBodyStabilityControl: " + isChecked);
        Log.d(TAG, "bodyStabilityControlLiveData.getValue(): " + bodyStabilityControlLiveData.getValue());
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (isChecked && bodyStabilityControlLiveData.getValue() == CarDriving.ESPSwitchStatus.ON) {
            return;
        } else if (!isChecked && bodyStabilityControlLiveData.getValue() == CarDriving.ESPSwitchStatus.OFF) {
            return;
        }
        drivingManager.setBodyInfoESCSet(isChecked ? CarDriving.Set_ESPFunctionSts.ON : CarDriving.Set_ESPFunctionSts.OFF);
    }


    /**
     * 车身稳定控制
     */
    public void getBodyStabilityControl() { // 车身稳定控制
        int signalVal = drivingManager.getBodyInfoESC();
        if (signalVal == CarDriving.ESPSwitchStatus.ON || signalVal == CarDriving.ESPSwitchStatus.OFF) {
            bodyStabilityControlLiveData.postValue(signalVal);
            preBodyStabilityControl = signalVal;
        } else {
            bodyStabilityControlLiveData.postValue(preBodyStabilityControl);
        }
    }


    public void setAutoParking(boolean isChecked) {
        //TODO BCM_4_KeySts = ON
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (isChecked) {
            if (autoParkingLiveData.getValue() == CarDriving.AVHSts.ACTIVE || autoParkingLiveData.getValue() == CarDriving.AVHSts.STANDBY)
                return;
        } else {
            if (autoParkingLiveData.getValue() == CarDriving.AVHSts.OFF)
                return;
        }
        drivingManager.setAutoHoldSet(isChecked ? CarDriving.IHU_AutHldSet.ON : CarDriving.IHU_AutHldSet.OFF);
    }


    public void getAutoParking() {
        int signalVal = drivingManager.getAutoHold();
        if (signalVal == CarDriving.AVHSts.OFF || signalVal == CarDriving.AVHSts.ACTIVE || signalVal == CarDriving.AVHSts.STANDBY) {
            autoParkingLiveData.postValue(signalVal);
            preAutoParking = signalVal;
        } else {
            autoParkingLiveData.postValue(preAutoParking);
        }
    }

    public void setParkingBrake(int signalVal) {
        // 驻车制动
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (signalVal == CarDriving.EPBSetCmd.APPLY && parkingBrakeLiveData.getValue() == CarDriving.EPBActrSt.APPLIED)
            return;
        if (signalVal == CarDriving.EPBSetCmd.RELEASE && (parkingBrakeLiveData.getValue() == CarDriving.EPBActrSt.RELEASED || parkingBrakeLiveData.getValue() == CarDriving.EPBActrSt.COMPLETELY_RELEASED))
            return;
        try {
            int carSpeed = drivingManager.getCarSpeed();
            Log.d(TAG, "驻车制动: 车速:" + carSpeed);
            if (carSpeed >= 3) return;
            drivingManager.setBodyInfoEPBSet(signalVal);
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        }
    }


    /**
     * Signal Size 30x264: EPB_1_ActrSt 信号
     * EPB状态0x0:Unknow / 0x1:Applied0x3:Applying / 0x6:Half Applied高亮（静态文字：解除驻车制动）
     * 0x2:Released / 0x4: Releasing 灰色（静态文字：激活驻车制动）0x5: CompletelyReleased 大屏按上一状态显示并置灰(进入诊断模式，由诊断控制EPB的夹紧释放)
     */
    public void getParkingBrake() {
        int signalVal = drivingManager.getEPBStatus();
        Log.d(TAG, "getParkingBrake: " + signalVal);
        if (signalVal == CarDriving.EPBActrSt.APPLIED
                || signalVal == CarDriving.EPBActrSt.RELEASED
                || signalVal == CarDriving.EPBActrSt.COMPLETELY_RELEASED) {
            parkingBrakeLiveData.postValue(signalVal);
        } else {
            parkingBrakeLiveData.postValue(parkingBrakeLiveData.getValue());
        }
    }

    /**
     * 舒适制动开关
     * 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
     * 配置参数 N/A/A在线配置参数 数据长度 默认值 参数含义
     * 备注在中控屏车辆设置-XXX提供舒适制动等级设置：
     * 显示名称：舒适制动等级设置和显示状态：通勤模式，防晕车模式，儿童模式默认值：
     * 通勤模式（初次上电大屏默认开关不点亮，当舒适制动开关点亮时，默认等级开关显示为”通勤模式”）
     * 该功能与舒适制动开关联动，舒适制动关闭时，舒适制动等级不点亮；舒适制动开启时，舒适制动等级点亮；
     *
     * @param isChecked
     */
    public void setComfortBraking(boolean isChecked) {
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (isChecked) {
            if (comfortBrakingLiveData.getValue() == CarDriving.CST_Status.STANDBY || comfortBrakingLiveData.getValue() == CarDriving.CST_Status.ACTIVE) {
                return;
            }
        } else {
            if (comfortBrakingLiveData.getValue() == CarDriving.CST_Status.DISABLED) {
                return;
            }
        }
        drivingManager.setCSTEnableStatusSet(isChecked ? CarDriving.Set_CSTFunctionSts.CST_ON : CarDriving.Set_CSTFunctionSts.CST_OFF);
    }

    // 0x4AD: CST_Status 信号
    // 舒适制动0x0:CST is disabled 关闭
    // 0x1:CST is Standby或0x2:CST is Active开启
    // 0x3:CST is Failure 开关置灰，文字告警“系统异常，功能不可用”，toast弹窗

    public void getComfortBraking() {
        int signalVal = drivingManager.getCSTEnableStatus();
        if (signalVal == CarDriving.CST_Status.DISABLED || signalVal == CarDriving.CST_Status.STANDBY || signalVal == CarDriving.CST_Status.ACTIVE || signalVal == CarDriving.CST_Status.FAILURE) {
            comfortBrakingLiveData.postValue(signalVal);
        } else {
            Integer value = comfortBrakingLiveData.getValue();
            comfortBrakingLiveData.postValue(value == null ? CarDriving.CST_Status.DISABLED : value);
        }
    }


    // 设置舒适制动等级
    public void setComfortBrakingRank(int signalVal) {
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (signalVal == CarDriving.CST_SensitivityReq.LOW && comfortBrakingRankLiveData.getValue() == CarDriving.CST_SensitivitySts.LOW) {
            return;
        } else if (signalVal == CarDriving.CST_SensitivityReq.HIGH && comfortBrakingRankLiveData.getValue() == CarDriving.CST_SensitivitySts.HIGH) {
            return;
        } else if (signalVal == CarDriving.CST_SensitivityReq.MEDIUM && comfortBrakingRankLiveData.getValue() == CarDriving.CST_SensitivitySts.MEDIUM) {
            return;
        }
        drivingManager.setCSTInterventionSet(signalVal);
    }

    // 获取舒适制动等级
    public void getComfortBrakingRank() {
        int signalVal = drivingManager.getCSTIntervention();
        if (signalVal == CarDriving.CST_SensitivitySts.LOW || signalVal == CarDriving.CST_SensitivitySts.MEDIUM || signalVal == CarDriving.CST_SensitivitySts.HIGH) {
            comfortBrakingRankLiveData.postValue(signalVal);
        } else {
            Integer value = comfortBrakingRankLiveData.getValue();
            comfortBrakingRankLiveData.postValue(value == null ? CarDriving.CST_SensitivitySts.LOW : value);
        }
    }

    // 用戶是否踩下制动踏板
    // 0x0 未踩下 Not Applied
    // 0x1 踩下 Applied
    private void getBrakePedal() {
        int signVal = drivingManager.getBrakePedal();
        brakePedalLiveData.postValue(signVal);
    }

    //    电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；配置参数（智驾未定点，配置字组合待定）
    // 1）电源状态ON档(信号：FLZCU_9_PowerMode=ON)，在大屏上设置智能预瞄为开,ICC连续发三帧ICC_ASUPreviewCont=0x1: ON，然后发送0x0:Not Active 给FLZCU。
    // 2）电源状态ON档(信号：FLZCU_9_PowerMode=ON)，在大屏上设置智能预瞄为关闭，ICC连续发送三帧ICC_ASUPreviewCont=0x2：OFF，然后发送0x0:Not Active 给FLZCU。
    public void setIntelligentSuspensionAiming(boolean isChecked) {
        // TODO 电源模式:Comfort/ON档
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (isChecked && intelligentSuspensionAimingLiveData.getValue() == CarDriving.ASU_1_PreviewContFb.ACTIVE) {
            return;
        } else if (!isChecked && intelligentSuspensionAimingLiveData.getValue() == CarDriving.ASU_1_PreviewContFb.INACTIVE) {
            return;
        }
        drivingManager.setCSTASUPreviewContentSet(isChecked ? CarDriving.ICC_ASUPreviewCont.ON : CarDriving.ICC_ASUPreviewCont.OFF);
    }

    // 悬架智能瞄准
    public void getIntelligentSuspensionAiming() {
        int signalVal = drivingManager.getCSTASUPreviewContent();
        if (signalVal == CarDriving.ASU_1_PreviewContFb.INACTIVE || signalVal == CarDriving.ASU_1_PreviewContFb.ACTIVE) {
            intelligentSuspensionAimingLiveData.postValue(signalVal);
            preIntelligentSuspensionAiming = signalVal;
        } else {
            intelligentSuspensionAimingLiveData.postValue(preIntelligentSuspensionAiming);
        }
    }
    // 牵引模式

    // 个性化-车辆模式
    public void setDrivingMode(int status) {
        // TODO 电源模式为ON档
        if (!MsgUtil.getInstance().supportPowerMode()) {
            return;
        }
        if (status == 0 && drivingModeLiveData.getValue() != CarDriving.FLZCU_PropulsionMode.COMFORTABLE) {
            // 经济
            drivingManager.setPropulsionModeSet(CarDriving.ICC_PropulsionMode.COMFORTABLE);
        } else if (status == 1 && drivingModeLiveData.getValue() != CarDriving.FLZCU_PropulsionMode.NORMAL) {
            // 舒适
            drivingManager.setPropulsionModeSet(CarDriving.ICC_PropulsionMode.NORMAL);
        } else if (status == 2 && drivingModeLiveData.getValue() != CarDriving.FLZCU_PropulsionMode.SPORT) {
            // 运动
            drivingManager.setPropulsionModeSet(CarDriving.ICC_PropulsionMode.SPORT);
        }
    }

    // 个性化-驾驶模式
    public void getDrivingMode() {
        int signalVal = drivingManager.getPropulsionMode();
        if (signalVal == CarDriving.FLZCU_PropulsionMode.COMFORTABLE
                || signalVal == CarDriving.FLZCU_PropulsionMode.NORMAL
                || signalVal == CarDriving.FLZCU_PropulsionMode.SPORT
                || signalVal == CarDriving.FLZCU_PropulsionMode.SNOW) {
            drivingModeLiveData.postValue(signalVal);
            preDrivingMode = signalVal;
        } else {
            drivingModeLiveData.postValue(preDrivingMode);
        }
    }

    // 个性化-保电电量设置
    public void setPowerProtection(int status) {
        // TODO 前置条件： 1.电源模式为ON档

        //  2.收到FLZCU发送的电池管理信息HCU_SocManageFed=0x3:SOC hold mode；
        if (MsgUtil.getInstance().supportPowerMode()) {
            int SocManage = drivingManager.getSocManage();
            if (SocManage == 0x3) {
                drivingManager.setSocSet(status);
            }
        }
    }

    // 个性化-保电电量获取

    public void getPowerProtection() {
        int status = drivingManager.getSocSet();
//        return Prefs.rtnStatus(PrefsConst.D_POWER_PROTECTION, status, 30);
        Log.d(TAG, "getPowerProtection: " + status);
        if (powerProtectionLiveData.getValue() == status) return;
        if (status < 20 || status > 80) return;
        powerProtectionLiveData.postValue(status);
    }

    // 个性化-转向模式

    public void setSwerveMode(int signalVal) {
        // TODO 前置条件：电源模式为FLZCU_9_PowerMode =ON；
        if (!MsgUtil.getInstance().supportPowerMode()) {
            return;
        }
        if (signalVal == CarDriving.ICC_SteeringMode.SPORT && swerveModeLiveData.getValue() == CarDriving.FLZCU_SteeringMode.SPORT) {
            return;
        } else if (signalVal == CarDriving.ICC_SteeringMode.NORMAL && swerveModeLiveData.getValue() == CarDriving.FLZCU_SteeringMode.NORMAL) {
            return;
        }
        drivingManager.setSteeringModeSet(signalVal);
    }

    // 个性化-转向模式获取

    public void getSwerveMode() {
        int signalVal = drivingManager.getSteeringMode();
        if (signalVal == CarDriving.FLZCU_SteeringMode.NORMAL || signalVal == CarDriving.FLZCU_SteeringMode.SPORT) {
            swerveModeLiveData.postValue(signalVal);
            preSwerveMode = signalVal;
        } else {
            swerveModeLiveData.postValue(preSwerveMode);
        }
    }

    // 个性化-悬架模式

    public void setSuspensionMode(int signalVal) {
        if (!MsgUtil.getInstance().supportPowerMode()) return;

        if (signalVal == CarDriving.ICC_SuspensionMode.SOFT && suspensionModeLiveData.getValue() == CarDriving.FLZCU_SuspensionDamping.SOFT)
            return;
        else if (signalVal == CarDriving.ICC_SuspensionMode.HARD && suspensionModeLiveData.getValue() == CarDriving.FLZCU_SuspensionDamping.HARD)
            return;

        drivingManager.setSuspensionDampingSet(signalVal);
    }

    // 个性化-悬架模式

    public void getSuspensionMode() {
        int signalVal = drivingManager.getSuspensionDamping();
        if (signalVal == CarDriving.FLZCU_SuspensionDamping.SOFT || signalVal == CarDriving.FLZCU_SuspensionDamping.HARD) {
            suspensionModeLiveData.postValue(signalVal);
            preSuspensionMode = signalVal;
        } else {
            suspensionModeLiveData.postValue(preSuspensionMode);
        }
    }

    // 个性化-制动模式

    public void setBrakingMode(int signalVal) {
        // TODO 前置条件：电源模式为ON档
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (signalVal == CarDriving.ICC_BrakePedalFeelMode.COMFORTABLE && brakingModeLiveData.getValue() == CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE) {
            return;
        } else if (signalVal == CarDriving.ICC_BrakePedalFeelMode.SPORT && brakingModeLiveData.getValue() == CarDriving.FLZCU_BrakePedalFeelMode.SPORT) {
            return;
        }
        drivingManager.setPedalComfortSet(signalVal);
    }

    // 个性化-制动模式

    public void getBrakingMode() {
        int signalVal = drivingManager.getPedalComfort();
        if (signalVal == CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE || signalVal == CarDriving.FLZCU_BrakePedalFeelMode.SPORT) {
            brakingModeLiveData.postValue(signalVal);
            preBrakingMode = signalVal;
        } else {
            brakingModeLiveData.postValue(preBrakingMode);
        }
    }

    // 设置牵引模式（拖车模式）
    public void setTractionMode(int signalVal) {
//  TODO      1. 车辆处于P/N档
//        2. 刹车踩下
//        3. 未插充/放电枪
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (signalVal == CarDriving.ICC_TowingMode.ON && tractionModeLiveData.getValue() == CarDriving.VCU_TowingMode.ACTIVE)
            return;
        if (signalVal == CarDriving.ICC_TowingMode.OFF && tractionModeLiveData.getValue() == CarDriving.VCU_TowingMode.NOT_ACTIVE)
            return;
        newEnergyManager.setTowingModeSwitch(signalVal);
    }

    // 获取牵引模式（拖车模式）

    public void getTractionMode() {
        int signalVal = newEnergyManager.getTowingModeSwitch();
        if (signalVal == CarDriving.VCU_TowingMode.NOT_ACTIVE || signalVal == CarDriving.VCU_TowingMode.ACTIVE) {
            tractionModeLiveData.postValue(signalVal);
            preTractionMode = signalVal;
        } else {
            tractionModeLiveData.postValue(preTractionMode);
        }
    }


    public int enableTractionMode() {
        // 车辆是否属于P/N 档
        // 0x0:Init
        // 0x1:P
        // 0x2:R
        // 0x3:N
        // 0x4:D
        // 0x5:Reserved
        // 0x6:Reserved
        // 0x7:Reserved
        int PNStatus = newEnergyManager.getDrivingInfoGear();
        if (PNStatus != 0x1 && PNStatus != 0x3) return 0;
        // 用戶是否踩下制动踏板
        // 0x0 未踩下 Not Applied
        // 0x1 踩下 Applied
        int brakeSt = drivingManager.getBrakePedal();
        if (brakeSt != 0x1) return 0;
        // 3. 未插充/放电枪
        // 快充枪：
        int fastGunConnected = newEnergyManager.getChgWireConnectLightSts();
        if (fastGunConnected != 0x0) return 0;
        // 慢充枪：
        int slowGunConnected = newEnergyManager.getChargeGunStatus();
        if (slowGunConnected != 0x0) return 0;
        // 充电枪连接状态：
        int chargeGunConnected = newEnergyManager.getEvccGunConnectSts();
        if (chargeGunConnected != 0x0) return 0;
        return 1;
    }

    //    激活失败：则ICC_TowingMode=0x0->0x2ON->0x0(0x2发三帧)，中控接收到VCU的反馈VCU_TowingMode=0x0 NotActive，
//    及收到失败原因信号VCU_DrvGearShiftftFailureIndcn▪ 若VCU_DrvGearShiftftFailureIndcn=0x5:EPB Can Not Release，
//    此时提示文言为：“电子手刹无法释放，进入牵引模式失败”；▪ 若VCU_DrvGearShiftftFailureIndcn=0x1: Brake pedal applied，
//    此时提示文言为：“进入牵引模式需踩刹车”；▪ 若VCU_DrvGearShiftftFailureIndcn为其他值或ICC未接收到VCU_DrvGearShiftftFailureIndcn信号，
//    大屏均无需提示；

    public int getTractionModeFailReason() {
        int signVal = newEnergyManager.getTowModeInfo();
        return signVal;
    }

    public void getPowerMode() {
        int signalVal = drivingManager.getPowerMode();
        powerModeLiveData.postValue(signalVal);
    }


    /**
     * 获取P档状态
     * 0x0:Init
     * 0x1:P
     * 0x2:R
     * 0x3:N
     * 0x4:D
     * 0x5:Reserved
     * 0x6:Reserved
     * 0x7:Reserved
     */
    public void getGearPosition() {
        int signalVal = drivingManager.getGearPosition();
        if (signalVal == Integer.MIN_VALUE) return;
        gearPositionLiveData.postValue(signalVal);
    }

    // 车速
    public void getCarSpeed() {
        int signalVal = drivingManager.getCarSpeed();
        if (signalVal == Integer.MIN_VALUE) return;
        vehicleSpeedLiveData.postValue(signalVal);
    }
}
