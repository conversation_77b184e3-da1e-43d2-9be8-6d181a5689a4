package com.bitech.vehiclesettings.service;

import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.interfaces.connect.IConnectManager;
import com.bitech.platformlib.interfaces.connect.IConnectManagerListener;
import com.bitech.platformlib.manager.ConnectManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarCondition;
import com.bitech.vehiclesettings.carapi.constants.CarConnect;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.TtsHelper;
import com.bitech.vehiclesettings.view.connect.ChargingRemindLauncherUIAlert;

import java.util.concurrent.atomic.AtomicInteger;

public class ConnectLifecycle implements LifecycleEventObserver {
    private static final String TAG = ConnectLifecycle.class.getName();
    private final VehicleServiceHandler handler;
    private final LifecycleOwner lifecycleOwner;
    private ConnectManager connectManager;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private AtomicInteger currentPhoneChargeStatus = new AtomicInteger(CarConnect.CWC_ChargingSts.NO_CHARGING);
    private AtomicInteger currentWorkStatus = new AtomicInteger(CarConnect.CWC_workingSts.CWC_OFF);
    // 手机遗忘提醒状态
    private AtomicInteger ForgetRemindStatus = new AtomicInteger(0);

    // 弹窗
    public static ChargingRemindLauncherUIAlert.Builder chargingRemindLauncherUIAlert;
    private Integer isShowChargingLauncher = 0;

    @Override
    public void onStateChanged(@NonNull LifecycleOwner lifecycleOwner, @NonNull Lifecycle.Event event) {
        switch (event) {
            case ON_CREATE:
                Log.d(TAG, "onCreate: ConnectLifecycle " + lifecycleOwner);
                connectManager = (ConnectManager) BitechCar.getInstance()
                        .getServiceManager(BitechCar.CAR_CONNECT_MANAGER);
                if (connectManager != null) {
                    connectManager.addCallback(TAG, msgCallback);
                    connectManager.registerListener();
                }
                // 获取无线充电是否开启
                Integer value = connectManager.getWirelessCharge();
                currentWorkStatus.set(value == Integer.MIN_VALUE ? CarConnect.CWC_workingSts.CWC_OFF : value);
                // 获取手机充电状态
                value = connectManager.getPhoneChargeStatus();
                currentPhoneChargeStatus.set(value == Integer.MIN_VALUE ? CarConnect.CWC_ChargingSts.NO_CHARGING : value);
                // 获取手机遗忘提醒开关状态
                value = connectManager.getPhoneLeaveAlert();
                ForgetRemindStatus.set(value == Integer.MIN_VALUE ? CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.OFF : value);
                break;
            case ON_DESTROY:
                connectManager.removeCallback(TAG);
                TtsHelper.getInstance().release();
                break;
            default:
                break;
        }
    }

    public ConnectLifecycle(VehicleServiceHandler handler, LifecycleOwner lifecycleOwner) {
        this.handler = handler;
        this.lifecycleOwner = lifecycleOwner;
    }

    private IConnectManagerListener msgCallback = new IConnectManagerListener() {
        @Override
        public void onPhoneChargeChanged(int signalVal) {
            Log.d(TAG, "onPhoneChargeChanged: " + signalVal);
            currentPhoneChargeStatus.set(signalVal);
            if (signalVal == CarConnect.CWC_ChargingSts.NO_CHARGING) {
                isShowChargingLauncher = 0;
            }
            mainHandler.post( () -> {
                updatePhoneChargeStatus();
            });
        }

        @Override
        public void onFrontChargingChanged(int signalVal) {
            Log.d(TAG, "onFrontChargingChanged: " + signalVal);
            if (currentWorkStatus.get() != signalVal) {
                isShowChargingLauncher = 0;
                currentWorkStatus.set(signalVal);
                mainHandler.post( () -> {
                    updatePhoneChargeStatus();
                });
            }
        }

        @Override
        public void onForgetReminderChanged(int signalVal) {
            Log.d(TAG, "onForgetReminderChanged: " + signalVal);
            if (signalVal == Integer.MIN_VALUE) return;
            ForgetRemindStatus.set(signalVal);
        }

        @Override
        public void onPhoneForgetReminderVoice(int signalVal) {
            Log.d(TAG, "onPhoneForgetReminderVoice: " + signalVal);
            if (signalVal == 1) {
                if (ForgetRemindStatus.get() == 1 && currentWorkStatus.get() == CarConnect.CWC_workingSts.CWC_ON) {
                    // TTS播报
                    Context context = (Context) lifecycleOwner;
                    if (context == null) {
                        Log.e(TAG, "Context is null, cannot play TTS");
                        return;
                    }

                    TtsHelper.getInstance().init(context, new TtsHelper.TtsInitListener() {
                        @Override
                        public void onInitSuccess() {
                            Log.d("TTS", "初始化成功");

                            // 初始化成功后开始播报
                            TtsHelper.getInstance().speak(context.getString(R.string.str_phone_forget_reminder_voice), new TtsHelper.TtsPlayListener() {
                                @Override public void onPlayBegin() {
                                    Log.d("TTS", "开始播报 -- ConnectLifecycle");
                                }

                                @Override public void onPlayCompleted() {
                                    Log.d("TTS", "播报完成");
                                    TtsHelper.getInstance().release();
                                }

                                @Override
                                public void onPlayInterrupted() {
                                    TtsHelper.getInstance().release();
                                }
                            });
                        }

                        @Override
                        public void onInitFailed(int errorCode) {
                            Log.e("TTS", "初始化失败，错误码: " + errorCode);
                        }
                    });
                }
            }
        }
    };

    private void updatePhoneChargeStatus() {
        if (isShowChargingLauncher == 1) return;
        // 当前是充电状态
        if (currentWorkStatus.get() == CarConnect.CWC_workingSts.CWC_ON) {
            // 手机是充电状态
            if (currentPhoneChargeStatus.get() != CarConnect.CWC_ChargingSts.NO_CHARGING) {
                if (Prefs.get(PrefsConst.CONNECT_CHARGING_TIPS_LAUNCHER, 0) == 0) {
                    isShowChargingLauncher = 1;
                    if (chargingRemindLauncherUIAlert == null) {
                        chargingRemindLauncherUIAlert = new ChargingRemindLauncherUIAlert.Builder((Context) lifecycleOwner);
                    }
                    chargingRemindLauncherUIAlert.setGlobalAlert(true);
                    Dialog chargingRemindLauncherDialog = chargingRemindLauncherUIAlert.create();
                    if (chargingRemindLauncherDialog!= null && !chargingRemindLauncherDialog.isShowing()) {
                        chargingRemindLauncherDialog.show();
                    }
                }
            }
        }
    }
}
