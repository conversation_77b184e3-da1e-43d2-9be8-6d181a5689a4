package com.bitech.vehiclesettings.view.system;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSTirePressureUnitBinding;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class TirePressureUnitUIAlert extends BaseDialog {
    private static final String TAG = TirePressureUnitUIAlert.class.getSimpleName();
    private static TirePressureUnitUIAlert.OnProgressChangedListener onProgressChangedListener;

    public TirePressureUnitUIAlert(@NonNull Context context) {
        super(context);
    }

    public TirePressureUnitUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected TirePressureUnitUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static TirePressureUnitUIAlert.OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(TirePressureUnitUIAlert.OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = true;
        private DialogAlertSTirePressureUnitBinding binding;
        private String phoneNumber;
        private boolean isBlueOpen = false;
        public TirePressureUnitUIAlert dialog;

        public Builder(Context context) {
            this.context = context;
        }

        public TirePressureUnitUIAlert.Builder setCancelable(boolean isCancelable) {
            this.isCancelable = isCancelable;
            return this;
        }

        public TirePressureUnitUIAlert.Builder setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
            return this;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public TirePressureUnitUIAlert create() {
            dialog = new TirePressureUnitUIAlert(context, R.style.Dialog);
            binding = DialogAlertSTirePressureUnitBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1128; // 或者使用具体的像素值
                layoutParams.height = 508;
                window.setAttributes(layoutParams);
            }
            initPicker();
            return dialog;
        }

        private void initPicker() {
            binding.spvPicker.setItems(R.string.str_tire_pressure_unit_kpa, R.string.str_tire_pressure_unit_psi, R.string.str_tire_pressure_unit_bar);
            binding.spvPicker.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    onProgressChangedListener.onSwitch(index);
                }

                @Override
                public void onItemClicked(int index, String text) {
                    // 点击事件入口
                }
            });

            binding.spvPicker.setSelectedIndex(onProgressChangedListener.getTirePressureUnit(), false);
        }

        public void updateTirePressureUnitUI(int tirePressureUnit) {
            if (dialog != null) {
                binding.spvPicker.setSelectedIndex(tirePressureUnit, true);
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface OnProgressChangedListener {
        int getTirePressureUnit();

        void setTirePressureUnit(int state);

        void onSwitch(int index);
    }
}
