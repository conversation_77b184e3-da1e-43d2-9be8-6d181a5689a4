package com.bitech.vehiclesettings.view.connect

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.RadioButton
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.adapter.WifiConnectedListAdapter
import com.bitech.vehiclesettings.adapter.WifiScanListAdapter
import com.bitech.vehiclesettings.base.kt.BaseDialogFragment
import com.bitech.vehiclesettings.bean.WifiDeviceBean
import com.bitech.vehiclesettings.databinding.FragmentWifiKtBinding
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.EToast
import com.bitech.vehiclesettings.utils.GrayEffectHelper
import com.bitech.vehiclesettings.utils.GrayEffectUtils
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.TextUtil
import com.bitech.vehiclesettings.view.dialog.ConfirmDialog
import com.bitech.vehiclesettings.view.dialog.WifiConnectedInputDialog
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.bitech.vehiclesettings.viewmodel.WifiViewModel
import java.util.concurrent.CopyOnWriteArrayList


/**
 * @ClassName: WifiFragment
 *
 * @Date:  2024/1/19 12:22
 * @Description: WIFI页面Fragment.
 **/
class WifiFragment : BaseDialogFragment<FragmentWifiKtBinding, WifiViewModel>(), View.OnClickListener,
    WifiConnectedListAdapter.OnWifiItemClickIconCallback,
    WifiScanListAdapter.OnWifiItemClickIconCallback {

    // 页面内部所有TextView集合
    private val textViews = mutableListOf<TextView>()

    // WIFI扫描列表适配器
    private var wifiScanListAdapter: WifiScanListAdapter? = null

    // WIFI已连接列表适配器
    private var wifiConnectedListAdapter: WifiConnectedListAdapter? = null

    // 自定义Toast
    private var toast: SettingsToast? = null

    // WIFI连接密码输入弹窗
    private var wifiConnectedInputDialog: WifiConnectedInputDialog? = null

    // wifi页面提示弹窗
    private var confirmDialog: ConfirmDialog? = null

    // 内部NestedScrollView相对于屏幕顶部的距离
    private var nestedScrollViewTop = -1

    //密码隐藏可见性
    private var isPasswordVisible = false

    // 用于标记是否是第一次扫描
    var isFirstScan:Boolean = true

    // wifi连接当前密码是否可视化
    private var isVisual = false

    //密码输入弹窗
    private var passwordDialog: Dialog? = null
    private var addDialog: Dialog? = null

    private var isSelfHidden = false
    /**
     * 视图绑定.
     *
     * @return xml文件id
     */
    override fun getLayoutId(container: ViewGroup?): FragmentWifiKtBinding {
        return FragmentWifiKtBinding.bind(
            layoutInflater.inflate(
                R.layout.fragment_wifi_kt,
                container,
                false
            )
        )

    }

    /**
     * viewModel绑定.
     *
     * @return viewModel类名
     */
    override fun getViewModel(): Class<WifiViewModel> {
        return WifiViewModel::class.java
    }

    /**
     * liveData订阅.
     *
     */
    override fun initObserve() {
        LogUtil.d(TAG, "initObserve : ")
        viewModel.apply {
            // WIFI开关状态订阅
            wifiSwitchLiveData.observe(this@WifiFragment) {
                // 更新WIFI开关状态Ui
                updateWifiStatusUi(it)
            }
            // 网络通知开关状态订阅
            networkNotSwitchLiveData.observe(this@WifiFragment) {
                LogUtil.d(TAG, "networkNotificationLiveData : network notification state = $it")
                // 设置网络通知开关状态
                viewBinding.settingsNetworkNotificationSw.isChecked = it
            }
            // WIFI扫描列表订阅
            wifiScanListLiveData.observe(this@WifiFragment) {
                LogUtil.d(TAG, "wifiScanListLiveData : 当前扫描到wifi数量scan size = ${it.size}")
                // WIFI扫描列表UI更新
                wifiScanListUiUpdate(it)
            }
            // WIFI扫描状态订阅
            wifiScanningState.observe(this@WifiFragment) {
                LogUtil.d(TAG, "wifiScanningState : scanning state = $it")
                // 根据扫描状态，更新扫描中的视图
                showWifiScanIcon(it)
            }
            // WIFI已连接列表订阅
            wifiConnectedListLiveData.observe(this@WifiFragment) {
                LogUtil.d(
                    TAG,
                    "wifiConnectedListLiveData :wifiConnectedList connected list size = ${it.size}"
                )
                // WIFI已连接列表UI更新
                wifiConnectedListUiUpdate(it)
            }
            // WIFI连接错误订阅
            wifiConnectedResultLiveData.observe(this@WifiFragment) {
                LogUtil.d(TAG, "wifiConnectedErrorLiveData : error code = $it")
                // 根据WIFI错误类型，进行对应错误提示
                showWifiConnectedErrorTips(it)
            }
            // 搜索关键词订阅
            searchKeywordLiveData.observe(this@WifiFragment) {
                // 更新搜索关键词Ui
                updateSearchKeywordUi(it)
            }
        }
    }

    /**
     *相关视图初始化.
     *
     */
    override fun initView() {
        LogUtil.d(TAG, "initView : ")
        // 支持WIFI，则WIFI页面显示
        setWifiUiVisible(true)
        // 初始化可搜索TextView
        initSearchTextView()
        // 注册应用状态监听
        requireActivity().application.registerActivityLifecycleCallbacks(appSwitchListener)
        viewModel.startWifiScan(isFirstScan&&viewModel.getNetWorkNotification())
    }

    /**
     * 控件监听注册.
     *
     */
    override fun intiListener() {
        LogUtil.d(TAG, "intiListener : ")
        // WIFI页面相关控件监听。
        viewBinding.apply {
            // WIFI开关监听
            settingsWifiSw.isEnabled = true
            settingsWifiSw.setOnCheckedChangeListener { _, isChecked ->
                LogUtil.d(TAG, "settingsWifiSw : isChecked = $isChecked")
                if (settingsWifiSw.isPressed) {
                    // 打开或关闭WIFI
                    settingsWifiSw.isEnabled = false
                    viewModel.setWifiState(isChecked)
                    wifiConnectedListUiUpdate(viewModel.getWifiConnectedList())
                    // 使用 Handler 延迟恢复按钮状态
                    Handler(Looper.getMainLooper()).postDelayed({
                        settingsWifiSw.isEnabled = true
                    }, 1000) // 0.5 秒后恢复可点击状态
                }
            }
            // 网络通知开关监听
            settingsNetworkNotificationSw.setOnCheckedChangeListener { _, isChecked ->
                LogUtil.d(TAG, "settingsNetworkNotificationSw : isChecked = $isChecked")
                if (settingsNetworkNotificationSw.isPressed) {
                    // 打开或关闭网络通知
                    viewModel.setNetWorkNotification(isChecked)
                }
            }
            // wifi扫描列表滑动事件监听处理.
            settingsCanUseWifiRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    // 解决与ScanScrollView的滑动冲突事件
                    setBtScrollViewCanScroll(newState)
                }
            })
            // wifi已连接列表滑动事件监听处理
            settingsConnectedWifiRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    // 解决与ScanScrollView的滑动冲突事件
                    setBtScrollViewCanScroll(newState)
                }
            })
            //手动添加网络监听
            llSettingWifiAdd.setOnClickListener {
                LogUtil.d(TAG, "onClick : wifi add is click!")
                hideSelf()
                // 显示添加WIFI弹窗
                showAddWifiDialog()
            }
            // 设置页面图标点击事件监听
            settingsWifiStartScanIv.setOnClickListener(this@WifiFragment)
        }
    }

    /**
     * 相关数据初始化.
     *
     */
    override fun initData() {
        LogUtil.d(TAG, "initData : ")
        // 初始化页面开关状态
        isFirstScan= true
        initSwStatusUi()
        // 初始化页面数据
        viewModel.initData()
        // 初始化已连接WIFI列表适配器
        wifiConnectedListAdapter =
            WifiConnectedListAdapter(requireContext(), viewModel.getWifiConnectedList())
        wifiConnectedListAdapter!!.setOnBtItemClickIconCallback(this)
        // 初始化可用WIFI扫描列表适配器
        wifiScanListAdapter = WifiScanListAdapter(viewModel.getWifiScanList()) { wifiDeviceBean ->
            // 开始连接WIFI
            startConnectedWifi(wifiDeviceBean)
        }
        wifiScanListAdapter!!.setOnBtItemClickIconCallback(this)
        viewBinding.apply {
            // 设置已连接WIFI列表布局及适配器
            settingsConnectedWifiRv.layoutManager = LinearLayoutManager(requireContext())
            settingsConnectedWifiRv.adapter = wifiConnectedListAdapter
            // 设置可用WIFI列表布局及适配器
            settingsCanUseWifiRv.layoutManager = LinearLayoutManager(requireContext())
            settingsCanUseWifiRv.adapter = wifiScanListAdapter
        }
    }
    override fun onStart() {
        isShow = true
        super.onStart()
    }
    override fun onStop() {
        // 在退出的时候，停止WIFI扫描
        viewModel.stopWifiScan()
        // 注销已连接列表适配器监听
        wifiConnectedListAdapter!!.setOnBtItemClickIconCallback(null)
        wifiScanListAdapter!!.setOnBtItemClickIconCallback(null)
        // 注销监听器
        requireActivity().application.unregisterActivityLifecycleCallbacks(appSwitchListener)
        isShow = false
        super.onStop()
    }

    override fun getDialogTag(): String {
        return "WifiFragment"
    }

    /**
     * 订阅移除.
     *
     */
    override fun removeObserve() {
        LogUtil.d(TAG, "removeObserve : ")
        viewModel.apply {
            // 注销订阅
            searchKeywordLiveData.removeObservers(this@WifiFragment)
            wifiConnectedResultLiveData.removeObservers(this@WifiFragment)
            wifiConnectedListLiveData.removeObservers(this@WifiFragment)
            wifiScanningState.removeObservers(this@WifiFragment)
            wifiScanListLiveData.removeObservers(this@WifiFragment)
            hotspotConnectedListLiveData.removeObservers(this@WifiFragment)
            hotspotPasswordErrorLD.removeObservers(this@WifiFragment)
            hotspotPasswordLiveData.removeObservers(this@WifiFragment)
            hotspotNameLiveData.removeObservers(this@WifiFragment)
            networkNotSwitchLiveData.removeObservers(this@WifiFragment)
            hotspotSwitchLiveData.removeObservers(this@WifiFragment)
            wifiSwitchLiveData.removeObservers(this@WifiFragment)
        }
        cancelDialog()
    }

    /**
     * 初始化可搜索TextView数据.
     *
     */
    private fun initSearchTextView() {
        LogUtil.d(TAG, "initSearchTextView : ")
        // 初始化页面内TextView集合
        textViews.addAll(TextUtil.getAllTextViewInFragment(viewBinding.root))
        // 构建页面内搜索菜单相关数据
        Contacts.wifiMenuBean.secondMenuList.forEach { secondMenu ->
            val textView = textViews.find { TextUtils.equals(it.text, secondMenu.secondMenuName) }
            if (textView != null) {
                // 二级菜单选项在页面TextView找到对应，则更新滑动距离和与之对应的TextView
                secondMenu.nameTextView = textView
                secondMenu.nameTextView!!.viewTreeObserver.addOnGlobalLayoutListener(object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        if (nestedScrollViewTop == -1) {
                            nestedScrollViewTop =
                                TextUtil.viewToTopScreenDistance(viewBinding.settingsWifiSsv)
                        }
                        // 设置滑动到对应TextView需要滑动的距离
                        secondMenu.scrollDistance =
                            TextUtil.viewToTopScreenDistance(textView) - nestedScrollViewTop
                        // 移除监听器，确保只调用一次
                        secondMenu.nameTextView!!.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }
                })
            }
        }
    }

    /**
     * 清空可搜索TextView数据.
     *
     */
    private fun clearSearchTextView() {
        LogUtil.d(TAG, "clearSearchTextView : ")
        Contacts.wifiMenuBean.secondMenuList.forEach { secondMenu ->
            secondMenu.nameTextView = null
        }
    }

    /**
     * 初始化页面开关状态.
     *
     */
    private fun initSwStatusUi() {
        LogUtil.d(TAG, "initSwStatusUi : ")
        if (viewModel.isSupportWifi()) {
            // 更新WIFI开关状态Ui
            updateWifiStatusUi(viewModel.getWifiState())
            // 网络通知开关状态
            viewBinding.settingsNetworkNotificationSw.isChecked = viewModel.getNetWorkNotification()
        }
    }

    /**
     * WIFI页面点击事件监听.
     *
     * @param view
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.settings_wifi_start_scan_iv -> {
                // 开始扫描
                Log.d(TAG, "onClick: zhc6whu:"+viewBinding.settingsNetworkNotificationSw.isChecked)
                viewModel.startWifiScan(isFirstScan&&viewModel.getNetWorkNotification())
            }
        }
    }

    /*----------------------------WIFI已连接列表各控件点击事件监听回调----------------------------------*/

    @SuppressLint("SetTextI18n")
    private fun showAddWifiDialog() {
        // 创建一个自定义的 Dialog
        addDialog = context?.let { Dialog(it, R.style.AppTheme_Dialog) }
        addDialog?.setContentView(R.layout.dialog_add_wifi) // 设置自定义布局

        //加密类型
        var wifiEncryptionType = "WPA"
        // 获取布局中的控件
        val wifiEncryption = addDialog?.findViewById<TextView>(R.id.wifi_encryption_type)
        val wifiNameEditText = addDialog?.findViewById<EditText>(R.id.et_wifi_name)
        val wifiNameClear = addDialog?.findViewById<ImageView>(R.id.ic_wifi_name_clear)
        val wifiPasswordHide = addDialog?.findViewById<ImageView>(R.id.ic_wifi_hide)
        val wifiPasswordEditText = addDialog?.findViewById<EditText>(R.id.et_wifi_password)
        val addWifiEncryption = addDialog?.findViewById<ConstraintLayout>(R.id.cl_encryption)
        val connectButton = addDialog?.findViewById<Button>(R.id.btn_connect)
        val cancelButton = addDialog?.findViewById<Button>(R.id.btn_cancel)
        GrayEffectUtils.applyGrayEffect(connectButton)
        GrayEffectHelper.setEnable(connectButton, false)
        addWifiEncryption?.setOnClickListener {
            showAddWifiEncryptionDialog(wifiEncryption?.text?.toString() ?: R.string.wifi_add_wifi_encryption_2.toString()) { encryptionType ->
                // 这里接收用户选择的加密类型
                Log.d(TAG, "Selected encryption type log中: $encryptionType")
                // 在这里处理加密类型
                wifiEncryptionType= encryptionType
                wifiEncryption?.text = wifiEncryptionType
            }
        }


        // 监听 EditText 的文本变化
        wifiNameEditText!!.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                s.isNotEmpty().run {
                    wifiNameClear?.visibility = if (this) View.VISIBLE else View.GONE
                    GrayEffectHelper.setEnable(connectButton, this)
                }
            }

            override fun afterTextChanged(s: Editable) {
            }
        })

        // 监听密码输入框的文本变化
        wifiPasswordEditText?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                // 如果输入框为空，隐藏可见性图标
                wifiPasswordHide?.visibility = if (s.isNullOrEmpty()) {
                    View.GONE
                } else {
                    View.VISIBLE
                }
                // 根据密码长度设置按钮颜色
                val passwordLength = s?.length ?: 0
                passwordLength.run {
                    GrayEffectHelper.setEnable(connectButton, this >= 8)
                    if (this >= 8)   GrayEffectUtils.removeGrayEffect(connectButton) else GrayEffectUtils.applyGrayEffect(connectButton)
                }
            }

            override fun afterTextChanged(s: Editable?) {}
        })

        // 点击可见性图标切换密码可见性
        wifiPasswordHide?.setOnClickListener {
            isPasswordVisible = !isPasswordVisible
            if (isPasswordVisible) {
                // 显示密码
                wifiPasswordEditText?.transformationMethod = HideReturnsTransformationMethod.getInstance()
                wifiPasswordHide.setImageResource(R.mipmap.img_eyes)
            } else {
                // 隐藏密码
                wifiPasswordEditText?.transformationMethod = PasswordTransformationMethod.getInstance()
                wifiPasswordHide.setImageResource(R.mipmap.img_closeeyes)
            }
            wifiPasswordEditText?.let {
                it.setSelection(it.text.length)
            }
            wifiPasswordEditText?.invalidate()
        }

        // 点击清空图标清空wifi名输入框的内容
        wifiNameClear?.setOnClickListener { wifiNameEditText.setText("") }

        // 取消按钮点击事件
        cancelButton?.setOnClickListener {
            showSelf()
            addDialog?.dismiss() // 关闭弹窗
        }
        // 连接按钮点击事件
        connectButton?.setOnClickListener {
            Log.d(TAG, "showAddWifiDialog: zhc6whu:$wifiEncryptionType")
            connectButton.isEnabled = false  // 禁用按钮防止重复点击
            connectButton.text = "正在加入..."
            viewModel.setIsAddWifi(true)
            //连接时间超过60秒时，按钮禁用，并弹窗提示，未搜索到网络
// 初始化一个计时器
            val timeoutHandler = Handler()
            var timeoutRunnable: Runnable? = null
            val TIMEOUT_DURATION = 30000 // 30秒超时

            timeoutRunnable = Runnable {
                // 超时逻辑
                connectButton.isEnabled = true
                connectButton.text = "加入"
                viewModel.setIsAddWifi(false)
                // 添加网络失败
                if (addDialog != null && addDialog!!.isShowing) {
                    EToast.showToast(MyApplication.getContext(), "添加网络失败", Toast.LENGTH_SHORT, false)
                }
            }

            // 启动计时器
            timeoutHandler.postDelayed(timeoutRunnable, TIMEOUT_DURATION.toLong())
            if (wifiEncryptionType!="无"){
                if (wifiNameEditText.text.toString().isNotEmpty() && ((wifiPasswordEditText?.text.toString().length>=8)&&(wifiPasswordEditText?.text.toString().length<=16))){
                    val wifiConfig = WifiConfiguration()
                    wifiConfig.SSID = "\"${wifiNameEditText.text}\""
                    wifiConfig.preSharedKey = "\"${wifiPasswordEditText?.text}\""
                    wifiConfig.hiddenSSID = true
                    Log.d(TAG, "手动添加的wifi信息:zhc6whu: $wifiEncryptionType"+"\"${wifiNameEditText.text}\""+"\"${wifiPasswordEditText?.text}\"")

                    val wifiManager = context?.applicationContext?.getSystemService(Context.WIFI_SERVICE) as WifiManager

                    val success = wifiManager.addNetwork(wifiConfig)

                    val list = wifiManager.configuredNetworks
                    for (config in list) {
                        if (config.SSID != null && config.SSID == "\"${wifiNameEditText.text}\"") {
                            wifiManager.disconnect()
                            wifiManager.enableNetwork(config.networkId, true)
                            wifiManager.reconnect()
                            break
                        }
                    }
                    // 关闭弹窗
//                    addDialog.dismiss()
                }else {
                    EToast.showToast(context, "请输入wifi名并且密码格式合规", Toast.LENGTH_SHORT, false);
                    connectButton.text = "加入"
                    connectButton.isEnabled = true
                    timeoutHandler.removeCallbacks(timeoutRunnable)
                }
            }
            else if (wifiNameEditText.text.toString().isNotEmpty()){
                val wifiManager = context?.applicationContext?.getSystemService(Context.WIFI_SERVICE) as WifiManager
                // 创建 WifiConfiguration 对象
                val wifiConfig = WifiConfiguration().apply {
                    SSID = "\"${wifiNameEditText.text}\"" // 设置 SSID，必须用双引号包裹
                    allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE) // 设置为未加密网络
                    hiddenSSID = true
                }
                // 添加网络配置
                val networkId = wifiManager.addNetwork(wifiConfig)
//                EToast.showToast(context, "添加网络成功", Toast.LENGTH_SHORT, false)
                if (networkId != -1) {
                    // 连接到网络
                    val success = wifiManager.enableNetwork(networkId, true)

                    if (success) {
                        // 连接成功
                        EToast.showToast(MyApplication.getContext(), "添加网络成功", Toast.LENGTH_SHORT, false)
                    } else {
                        showWifiConnectedErrorTips(Contacts.WIFI_CONNECTED_FAIL)
                    }
                }
                // 关闭弹窗
//                addDialog?.dismiss()
            }
            else{
                EToast.showToast(context, "请输入wifi名", Toast.LENGTH_SHORT, false)
                connectButton.text = "加入"
            }
//            timeoutHandler.removeCallbacks(timeoutRunnable) // 取消计时器
        }
        addDialog?.setOnDismissListener {
            showSelf()
        }
        // 显示弹窗
        addDialog?.show()
    }

    private fun showAddWifiEncryptionDialog(currentEncryptionType: String, callback: (String) -> Unit) {
        // 创建一个自定义的 Dialog
        val dialog = context?.let { Dialog(it, R.style.AppTheme_Dialog) }
        dialog?.setContentView(R.layout.dialog_add_wifi_encryption) // 设置自定义布局

        // 获取布局中的控件
        val wifiAddWifiEncryption2 =
            dialog?.findViewById<ImageView>(R.id.wifi_add_wifi_encryption_2)
        val wifiAddWifiEncryption3 =
            dialog?.findViewById<ImageView>(R.id.wifi_add_wifi_encryption_3)
        val wifiAddWifiEncryption4 =
            dialog?.findViewById<ImageView>(R.id.wifi_add_wifi_encryption_4)

        updateEncryptionSelectionUI(
            currentEncryptionType,
            wifiAddWifiEncryption2,
            wifiAddWifiEncryption3,
            wifiAddWifiEncryption4
        )

        wifiAddWifiEncryption2?.setOnClickListener {
            val encryptionType = getString(R.string.wifi_add_wifi_encryption_2)
            Log.d(TAG, "showAddWifiEncryptionDialog: $encryptionType")
            dialog.dismiss() // 关闭弹窗
            callback(encryptionType) // 调用回调
        }
        wifiAddWifiEncryption3?.setOnClickListener {
            val encryptionType = getString(R.string.wifi_add_wifi_encryption_3)
            Log.d(TAG, "showAddWifiEncryptionDialog: $encryptionType")
            dialog.dismiss() // 关闭弹窗
            callback(encryptionType) // 调用回调
        }
        wifiAddWifiEncryption4?.setOnClickListener {
            val encryptionType = getString(R.string.wifi_add_wifi_encryption_4)
            Log.d(TAG, "showAddWifiEncryptionDialog: $encryptionType")
            dialog.dismiss() // 关闭弹窗
            callback(encryptionType) // 调用回调
        }
        dialog?.setOnDismissListener {
            showSelf()
        }

        // 显示弹窗
        dialog?.show()
        hideSelf()
    }

    /**
     * 更新加密类型选择的UI状态（模拟单选效果）
     */
    private fun updateEncryptionSelectionUI(
        currentEncryptionType: String,
        imageView2: ImageView?,
        imageView3: ImageView?,
        imageView4: ImageView?
    ) {
        // 重置所有ImageView的状态
        listOf(imageView2, imageView3, imageView4).forEach { it?.setSelected(false) }

        // 根据当前选中的加密方式设置对应的ImageView为选中状态
        when (currentEncryptionType) {
            getString(R.string.wifi_add_wifi_encryption_2) -> imageView2?.setSelected(true)
            getString(R.string.wifi_add_wifi_encryption_3) -> imageView3?.setSelected(true)
            getString(R.string.wifi_add_wifi_encryption_4) -> imageView4?.setSelected(true)
            else -> imageView2?.setSelected(true) // 默认选中WPA
        }
    }


    /**
     * 点击item 删除图标时回调.
     *
     * @param wifiDevice wifi对象
     */
    override fun onWifiDeleteDevice(wifiDevice: WifiDeviceBean) {
        LogUtil.d(TAG, "onWifiDeleteDevice : start delete wifi = ${wifiDevice.wifiSSID}")
        // 显示确认删除弹窗
        hideSelf()
        showDeleteWifiDialog(wifiDevice)
    }

    /**
     * 点击item 进行连接时回调.
     *
     * @param wifiDevice wifi对象
     */
    override fun onWifiConnected(wifiDevice: WifiDeviceBean) {
        LogUtil.d(TAG, "onWifiConnected : start connected wifi = ${wifiDevice.wifiSSID}")
        if (!viewModel.isWifiConnecting()) {
            // 连接WIFI
            viewModel.connectedWifi(wifiDevice, Contacts.WIFI_CONNECTED_FROM_CONNECTED_LIST)
        } else {
            LogUtil.d(TAG, "onWifiConnected : wifi is connecting...")
        }
    }

    /**
     * 点击item 进行断开连接时回调.
     *
     * @param wifiDevice wifi对象
     */
    override fun onWifiDisconnected(wifiDevice: WifiDeviceBean) {
        LogUtil.d(TAG, "onWifiDisconnected : start disconnected wifi = ${wifiDevice.wifiSSID}")
        // 显示断开连接弹窗
        hideSelf()
        showDisConnectDialog(wifiDevice)
    }
    /*----------------------------WIFI已连接列表各控件点击事件监听回调----------------------------------*/

    /**
     * 设置ScanScrollView是否可以滑动.
     *
     * @param state recycleView滑动状态
     */
    private fun setBtScrollViewCanScroll(state: Int) {
        when (state) {
            RecyclerView.SCROLL_STATE_DRAGGING -> {
                viewBinding.settingsWifiSsv.setIntercepted(true)
            }

            RecyclerView.SCROLL_STATE_IDLE -> {
                viewBinding.settingsWifiSsv.setIntercepted(false)
            }
        }
    }

    /**
     * 显示提示Toast.
     *
     * @param message 提示信息
     */
    private fun showToast(message: String) {
        SettingsToast.showToast(message)
    }

    /**
     * 显示WIFI错误提示.
     *
     * @param errorCode 错误码
     */
    private fun showWifiConnectedErrorTips(errorCode: Int) {
        when (errorCode) {
            Contacts.WIFI_PASSWORD_ERROR -> {
                // WIFI密码错误
                EToast.showToast(context, getString(R.string.wifi_password_error_tips), Toast.LENGTH_SHORT, false)
                viewModel.startWifiScan(isFirstScan&&viewModel.getNetWorkNotification())
                passwordDialog?.findViewById<TextView>(R.id.errorText)?.visibility = View.VISIBLE
                passwordDialog?.findViewById<Button>(R.id.dialog_connected_btn)?.isEnabled = true
                passwordDialog?.findViewById<Button>(R.id.dialog_connected_btn)?.text= getString(R.string.dialog_confirm_wifi_text)
                addDialog?.findViewById<Button>(R.id.btn_connect)?.isEnabled = true
                addDialog?.findViewById<Button>(R.id.btn_connect)?.text= getString(R.string.dialog_confirm_wifi_text)
            }
            Contacts.WIFI_CONNECTED_TIMEOUT -> {
                // WIFI连接超时
                EToast.showToast(context, getString(R.string.wifi_connected_timeout_tips), Toast.LENGTH_SHORT, false)
                viewModel.startWifiScan(isFirstScan&&viewModel.getNetWorkNotification())
                passwordDialog?.findViewById<Button>(R.id.dialog_connected_btn)?.isEnabled = true
                passwordDialog?.findViewById<Button>(R.id.dialog_connected_btn)?.text= getString(R.string.dialog_confirm_wifi_text)
                addDialog?.findViewById<Button>(R.id.btn_connect)?.isEnabled = true
                addDialog?.findViewById<Button>(R.id.btn_connect)?.text= getString(R.string.dialog_confirm_wifi_text)
            }
            Contacts.WIFI_CONNECTED_FAIL -> {
                // WIFI连接失败
                EToast.showToast(context, getString(R.string.wifi_connected_fail_tips), Toast.LENGTH_SHORT, false)
                viewModel.startWifiScan(isFirstScan&&viewModel.getNetWorkNotification())
                passwordDialog?.findViewById<Button>(R.id.dialog_connected_btn)?.isEnabled = true
                passwordDialog?.findViewById<Button>(R.id.dialog_connected_btn)?.text= getString(R.string.dialog_confirm_wifi_text)
                addDialog?.findViewById<Button>(R.id.btn_connect)?.isEnabled = true
                addDialog?.findViewById<Button>(R.id.btn_connect)?.text= getString(R.string.dialog_confirm_wifi_text)
            }
            Contacts.WIFI_CONNECTED_COMPLETE -> {
                //密码正确，关闭弹窗
                passwordDialog?.dismiss()
                addDialog?.dismiss()
            }
        }
    }

    /**
     * 显示删除WIFI配置时提示弹窗.
     *
     * @param wifiDeviceBean 待断开的WIFI
     */
    private fun showDeleteWifiDialog(wifiDeviceBean: WifiDeviceBean) {
        cancelDialog()
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "showDisConnectDialog : disconnect!")
                // 删除WIFI配置
                viewModel.deleteWifiConfig(wifiDeviceBean)
                showSelf()
                cancelDialog()
            }

            override fun onCancelClick() {
                // 取消按钮被点击
                showSelf()
                cancelDialog()
            }
        })
        confirmDialog?.setTips(
            resources.getString(R.string.wifi_delete_tips, wifiDeviceBean.wifiSSID)
        )
        confirmDialog?.setOnDismissListener(object : DialogInterface.OnDismissListener {
            override fun onDismiss(dialog: DialogInterface) {
                showSelf()
            }
        })
        confirmDialog?.show()
    }

    /**
     * 显示断开WIFI时提示弹窗.
     *
     * @param wifiDeviceBean 待断开的WIFI
     */
    private fun showDisConnectDialog(wifiDeviceBean: WifiDeviceBean) {
        cancelDialog()
        confirmDialog = ConfirmDialog(requireContext())
        confirmDialog?.setDialogClickCallback(object : ConfirmDialog.OnConfirmDialogClickCallback {
            override fun onConfirmClick() {
                LogUtil.d(TAG, "showDisConnectDialog : disconnect!")
                // 断开wifi连接
                viewModel.disconnectedWifi(wifiDeviceBean)
                showSelf()
                cancelDialog()
            }

            override fun onCancelClick() {
                // 取消按钮被点击
                showSelf()
                cancelDialog()
            }
        })
        confirmDialog?.setOnDismissListener(object : DialogInterface.OnDismissListener {
            override fun onDismiss(dialog: DialogInterface) {
                showSelf()
            }
        })
        confirmDialog?.setTips(
            resources.getString(R.string.wifi_disconnect_tips, wifiDeviceBean.wifiSSID)
        )
        confirmDialog?.show()
    }

    /**
     * 显示WIFI密码输入弹窗.
     *
     * @param wifiDeviceBean wifi连接对象.
     */
    @SuppressLint("SetTextI18n")
    private fun showWifiConnectedInputPasswordDialog(wifiDeviceBean: WifiDeviceBean) {
        passwordDialog = context?.let { Dialog(it, R.style.AppTheme_Dialog) }
        passwordDialog?.setContentView(R.layout.dialog_wifi_connected_input) // 设置自定义布局
        val wifiName =passwordDialog?.findViewById<TextView>(R.id.dialog_title_tv)
        val errorText =passwordDialog?.findViewById<TextView>(R.id.errorText)
        val passwordEditText =passwordDialog?.findViewById<EditText>(R.id.dialog_password_input_et)
        val dialogWifiIconIv =passwordDialog?.findViewById<ImageView>(R.id.dialog_wifi_icon_iv)
        val connectButton = passwordDialog?.findViewById<Button>(R.id.dialog_connected_btn)
        val cancelButton = passwordDialog?.findViewById<Button>(R.id.dialog_cancel_btn)
        wifiName?.text = wifiDeviceBean.wifiSSID
        GrayEffectHelper.setEnable(connectButton, false)
        GrayEffectUtils.applyGrayEffect(connectButton)
        // 移除所有中文字符
        passwordEditText?.filters = arrayOf(InputFilter { source, _, _, _, _, _ ->
            source?.replace(Regex("[\\u4E00-\\u9FA5]"), "")
        })

        passwordEditText?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(
                textString: CharSequence?,
                start: Int,
                before: Int,
                count: Int
            ) {
                val isLength = textString?.length!! >= 8
                GrayEffectHelper.setEnable(connectButton, isLength)
                if (isLength) GrayEffectUtils.removeGrayEffect(connectButton) else GrayEffectUtils.applyGrayEffect(connectButton)

                if (textString.isEmpty()){
                    if (errorText != null) {
                        errorText.visibility=View.GONE
                    }
                }

            }

            override fun afterTextChanged(s: Editable?) {

            }
        })
        dialogWifiIconIv?.setOnClickListener {
            if (isVisual) {
                // 当前密码可见，点击后，密码不可见
                isVisual = false
                // 设置输入框内容不可见
                passwordEditText?.transformationMethod =
                    PasswordTransformationMethod.getInstance()
                // 设置图标为不可见图标
                dialogWifiIconIv.setImageResource(R.mipmap.img_closeeyes)
            } else {
                isVisual = true
                // 设置输入框内容可见
                passwordEditText?.transformationMethod =
                    HideReturnsTransformationMethod.getInstance()
                // 设置图标为可见图标
                dialogWifiIconIv.setImageResource(R.mipmap.img_eyes)
            }
            // 将光标放在密码最后面
            passwordEditText?.setSelection(passwordEditText.text.toString().length)
        }

        connectButton?.setOnClickListener {
            wifiDeviceBean.wifiPassword = passwordEditText?.text.toString()
            connectButton.isEnabled = false  // 禁用按钮防止重复点击
            passwordEditText?.isEnabled = false
            connectButton.text = "正在加入..."
            //5 秒后恢复
            passwordEditText?.postDelayed({
                passwordEditText.isEnabled = true
                connectButton.text = "连接"
            }, 5_000)
            viewModel.connectedWifi(wifiDeviceBean, Contacts.WIFI_CONNECTED_FROM_SCAN_LIST)

        }
        cancelButton?.setOnClickListener {
            passwordDialog?.dismiss()
        }
        passwordDialog?.show()
        // 设置输入框获取焦点
        passwordEditText?.requestFocus()
// 弹出软键盘
        passwordDialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
    }

    /**
     * 关闭当前提示WIFI弹窗.
     *
     */
    private fun cancelDialog() {
        LogUtil.d(TAG, "cancelDialog : ")
        if (confirmDialog != null) {
            confirmDialog?.dismiss()
            confirmDialog = null
        }
    }

    /**
     * 更新WIFI开关状态对应的ui
     *
     * @param isChecked 是否选中
     */
    private fun updateWifiStatusUi(isChecked: Boolean) {
        LogUtil.i(TAG, "updateWifiStatusUi : wifi switch state = $isChecked")
        // 设置WIFI开关状态
        viewBinding.settingsWifiSw.isChecked = isChecked
        if (isChecked) {
            // WIFI开关打开时，界面UI显示
            onWifiSwitchOnUiShow()
        } else {
            // WIFI开关关闭时，界面UI显示
            onWifiSwitchOffUiShow()
        }
    }

    /**
     * 扫描列表开始连接WIFI.
     *
     * @param wifiDevice
     */
    private fun startConnectedWifi(wifiDevice: WifiDeviceBean) {
        // 判断当前是否存在连接中的WIFI，没有则可进行连接；存在连接中则不能进行连接
        if (!viewModel.isWifiConnecting()) {
            if (wifiDevice.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA)
                || wifiDevice.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA3)
                || wifiDevice.wifiCapabilities.contains(Contacts.WIFI_CAP_WPA2_3)
                || wifiDevice.wifiCapabilities.contains(Contacts.WIFI_CAP_WEP)
                || wifiDevice.wifiCapabilities.contains(Contacts.WIFI_CAP_EAP)
            ) {
                // 加密网络，弹出密码弹出，进行连接,进行WIFI连接
                val wifiConnected=viewModel.isWifiConnected(wifiDevice)
                Log.d(TAG, "startConnectedWifi: zhc6whu:是否点击的是已连接过的wifi$wifiConnected")
                if (wifiConnected){
                    val mWifiManager =
                        MyApplication.getContext().getSystemService(Context.WIFI_SERVICE) as WifiManager
                    val savedNetworks: List<WifiConfiguration> = mWifiManager.getConfiguredNetworks()
                    val targetSsid = "\"${wifiDevice.wifiSSID}\""
                    val cfg: WifiConfiguration? = savedNetworks.find {
                        it.SSID.equals(targetSsid, ignoreCase = true)
                    }
                    if (cfg != null) {
                        // 找到了
                        Log.d("WiFi", "zhc6whu networkId = ${cfg.networkId}, SSID = ${cfg.SSID}")
                        // 比如可以直接连接
                        mWifiManager.enableNetwork(wifiDevice.wifiNetWorkId, true)
                        mWifiManager.connect(cfg, object : WifiManager.ActionListener {
                            override fun onSuccess() {
                                Log.d("WiFi", "zhc6whu 连接成功"+cfg.SSID)
                            }

                            override fun onFailure(reason: Int) {
                                Log.d("WiFi", "zhc6whu 连接失败")
                                viewModel.deleteWifiConfig(wifiDevice)
                                showWifiConnectedInputPasswordDialog(wifiDevice)
                            }
                        })
                    } else {
                        // 没找到
                        Log.d("WiFi", "zhc6whu 未找到该 SSID 的已保存配置")
                    }
                    //获取之前连结果的wifi密码并尝试连接，这样报错就会显示让重新输入弹窗了
                }else{
                    showWifiConnectedInputPasswordDialog(wifiDevice)
                }
            } else {
                // 开放网络，直接进行连接
                viewModel.connectedWifi(wifiDevice, Contacts.WIFI_CONNECTED_FROM_SCAN_LIST)
            }
        } else {
            // 当前存在WIFI正在连接
            LogUtil.d(TAG, "showWifiConnectedInputPasswordDialog : wifi is connecting,not show!")
        }
    }

    /**
     * 设置WIFI页面是否可见.
     *
     * @param isVisible 可见性
     */
    private fun setWifiUiVisible(isVisible: Boolean) {
        LogUtil.i(TAG, "setWifiUiVisible : isVisible = $isVisible")
        if (isVisible) {
            // 显示WIFI页面
            viewBinding.settingsWifiCl.visibility = View.VISIBLE
        } else {
            // 隐藏WIFI页面
            viewBinding.settingsWifiScanPb.visibility = View.GONE
            viewBinding.settingsWifiCl.visibility = View.GONE
        }
    }

    /**
     * 更新WIFI扫描过程中图标.
     *
     * @param isScanning 是否在扫描中
     */
    private fun showWifiScanIcon(isScanning: Boolean) {
        if (isScanning && viewModel.getWifiState()) {
            // 处于扫描中，显示扫描图标
            viewBinding.settingsWifiScanPb.visibility = View.VISIBLE
            isFirstScan=false
        } else {
            // 未在扫描中，隐藏扫描图标
            viewBinding.settingsWifiScanPb.visibility = View.GONE
            isFirstScan=true
        }
    }

    /**
     * WIFI扫描列表是否可见.
     *
     * @param wifiScanList WIFI扫描列表
     */
    @SuppressLint("SetTextI18n")
    private fun wifiScanListUiUpdate(wifiScanList: CopyOnWriteArrayList<WifiDeviceBean>) {
        viewBinding.settingsCanUseWifiRv.visibility = View.VISIBLE;
        // 更新适配器数据
        wifiScanListAdapter?.setWifiScanList(wifiScanList)
    }

    /**
     *  WIFI已连接列表是否可见.
     *
     * @param wifiConnectedList WIFI已连接列表
     */
    private fun wifiConnectedListUiUpdate(wifiConnectedList: CopyOnWriteArrayList<WifiDeviceBean>) {
        Log.d(
            TAG,
            "wifiConnectedListUiUpdate: zhc6whu:当前已连接设备列表大小：是否隐藏wifi列表" + wifiConnectedList.size
        )
        if (wifiConnectedList.size == 0) {
            // 已连接WIFI列表内容空，则隐藏该列表
            viewBinding.apply {
                settingsWifiConnectedCl.visibility = View.GONE
                settingsWifiConnectedTv.visibility = View.GONE
            }
        } else {
            if (viewBinding.settingsWifiSw.isChecked) {
                // 已连接WIFI列表内容不为空，则显示该列表
                viewBinding.apply {
                    settingsWifiConnectedCl.visibility = View.VISIBLE
                    settingsWifiConnectedTv.visibility = View.VISIBLE
                }
            } else {
                // wifi开关关闭，已连接列表隐藏
                viewBinding.apply {
                    settingsWifiConnectedCl.visibility = View.GONE
                    settingsWifiConnectedTv.visibility = View.GONE
                }
            }
        }
        // 过滤出当前正在连接的 WiFi
//        val filteredList = CopyOnWriteArrayList<WifiDeviceBean>()
//        for (wifiDevice in wifiConnectedList) {
//            if (wifiDevice.wifiConnectedState == NetworkInfo.State.CONNECTED ||
//                wifiDevice.wifiConnectedState == NetworkInfo.State.CONNECTING) {
//                filteredList.add(wifiDevice)
//            }
//        }
        // 更新已连接列表
        wifiConnectedListAdapter?.setWifiConnectedList(wifiConnectedList)
//        viewModel.updateWifiConnectionListState(networkInfo)
    }

    /**
     * WIFI开关打开时，界面UI显示.
     */
    private fun onWifiSwitchOnUiShow() {
        viewBinding.apply {
            // 显示已连接WIFI列表
//            showWifiConnectedList()
            // 显示可用WIFI列表
            settingsWifiUseTv.visibility = View.VISIBLE
            settingsWifiStartScanIv.visibility = View.VISIBLE
            settingsCanUseWifiCl.visibility = View.VISIBLE
            // 网络通知开关显示
            settingsNetworkNotificationCl.visibility = View.VISIBLE
            llSettingWifiAdd.visibility = View.VISIBLE
        }
        Log.d(TAG, "onWifiSwitchOnUiShow: 触发一次刷新")
        // 开始扫描
//        viewModel.startWifiScan(isFirstScan&&viewModel.getNetWorkNotification())
    }

    /**
     * WIFI开关关闭时，界面UI显示.
     *
     */
    private fun onWifiSwitchOffUiShow() {
        // 停止WIFI扫描
        viewModel.stopWifiScan()
        viewBinding.apply {
            settingsWifiStartScanIv.visibility = View.GONE
            // 隐藏已连接WIFI列表
            hideWifiConnectedList()
            // 隐藏可用WIFI列表
            settingsWifiUseTv.visibility = View.GONE
            settingsCanUseWifiCl.visibility = View.GONE
            // 网络通知文言置灰
            settingsNetworkNotificationCl.visibility = View.GONE
            llSettingWifiAdd.visibility = View.GONE
//            settingNetworkNotificationTv
//                .setTextAppearance(R.style.settings_switch_tv_disable_style)
//            settingNetworkNotificationTipsTv
//                .setTextAppearance(R.style.settings_text_30_regular_d3dbe3_style)
        }
    }

    /**
     * 显示已连接WIFI列表.
     *
     */
    private fun showWifiConnectedList() {
        viewBinding.apply {
            // 显示已连接WIFI列表
            settingsWifiConnectedTv.visibility = View.VISIBLE
            settingsWifiConnectedCl.visibility = View.VISIBLE
        }
    }

    /**
     * 隐藏已连接WIFI列表
     *
     */
    private fun hideWifiConnectedList() {
        viewBinding.apply {
            // 隐藏已连接WIFI列表
            settingsWifiConnectedTv.visibility = View.GONE
            settingsWifiConnectedCl.visibility = View.GONE
        }
    }

    /**
     * 更新搜索关键词Ui.
     *
     * @param keyword
     */
    private fun updateSearchKeywordUi(keyword: String) {
        LogUtil.i(TAG, "updateSearchKeywordUi : keyword = $keyword")
        // 在页面菜单集合中进行遍历，找到搜索结果对应的TextView
        val secondMenu = Contacts.wifiMenuBean.secondMenuList.find {
            TextUtils.equals(it.nameTextView?.text, keyword)
        }
        if (secondMenu != null) {
            // 计算滑动距离，向上还是向下滑动
            val scrollDistance =
                secondMenu.scrollDistance - viewBinding.settingsWifiSsv.scrollY - 80
            LogUtil.i(TAG, "updateSearchKeywordUi : scrollDistance = $scrollDistance")
            // 滑动对应搜索结果TextView控件的位置
            viewBinding.settingsWifiSsv.fling(scrollDistance)
            viewBinding.settingsWifiSsv.smoothScrollBy(0, scrollDistance)
        }
    }
    // 添加应用切换监听器
    private val appSwitchListener = object : Application.ActivityLifecycleCallbacks {
        override fun onActivityPaused(activity: Activity) {
            if (activity == <EMAIL> && isAdded) {
                dismiss() // 应用切后台时关闭
            }
        }
        override fun onActivityResumed(activity: Activity) {}
        override fun onActivityStarted(activity: Activity) {}
        override fun onActivityDestroyed(activity: Activity) {}
        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
        override fun onActivityStopped(activity: Activity) {}
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
    }
    override fun onDestroyView() {
        LogUtil.d(TAG, "onDestroyView : ")
        clearSearchTextView()
        //销毁passwordDialog
        passwordDialog?.dismiss()
        super.onDestroyView()
    }

    companion object {
        // 日志标志位
        private const val TAG = "WifiFragment"
        var isShow = false
    }

    fun hideSelf() {
        val dialog = dialog
        if (dialog != null && dialog.isShowing) {
            dialog.hide()
            isSelfHidden = true
        }
        Log.d(TAG, "hideSelf: zhc6whu: is hide")
    }

    // 显示自身
    fun showSelf() {
        val dialog = dialog
        if (dialog != null && !dialog.isShowing && isSelfHidden) {
            dialog.show()
            isSelfHidden = false
        }
        Log.d(TAG, "showSelf: zhc6whu: is show")
    }

}
