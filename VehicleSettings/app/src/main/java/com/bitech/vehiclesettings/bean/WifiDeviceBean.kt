package com.bitech.vehiclesettings.bean

import android.net.NetworkInfo
import android.net.wifi.WifiConfiguration

/**
 * @ClassName: WifiDeviceBean
 * 
 * @Date:  2024/2/7 9:27
 * @Description: this is a WifiDeviceBean class.
 **/
data class WifiDeviceBean(var wifiBSSID: String) : Comparable<WifiDeviceBean> {

    // WIFI名称
    var wifiSSID = ""

    // WIFI连接状态
    var wifiConnectedState = NetworkInfo.State.DISCONNECTED

    // WIFI加密方式
    var wifiCapabilities = ""

    // WIFI信号强度
    var wifiLevel = 0

    // 扫描到该WIFI的时间
    var wifiScanTime = 0L

    // WIFI的连接密码
    var wifiPassword = ""

    // 网络连接id
    var wifiNetWorkId = -1

    // 网络配置
    var wifiConfiguration: WifiConfiguration? = null

    /**
     * 自定义排序对比.
     *
     * @param other 待对比的WIFI对象.
     * @return  <0,this对象>other对象；=0,this对象=other对象；>0.this对象>other对象
     */
    override fun compareTo(other: WifiDeviceBean): Int {
        return if (this.wifiConnectedState == NetworkInfo.State.CONNECTING
            || this.wifiConnectedState == NetworkInfo.State.CONNECTED
        ) {
            -1
        } else if (other.wifiConnectedState == NetworkInfo.State.CONNECTING
            || other.wifiConnectedState == NetworkInfo.State.CONNECTED
        ) {
            1
        } else {
            other.wifiLevel - this.wifiLevel
        }
    }

    /**
     * 对象比对重写
     *
     * @param other other
     * @return Boolean
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass)
            return false
        other as WifiDeviceBean
        if (this.wifiBSSID != other.wifiBSSID)
            return false
        return true
    }

    override fun hashCode(): Int {
        return 31 * wifiBSSID.hashCode()
    }
}
