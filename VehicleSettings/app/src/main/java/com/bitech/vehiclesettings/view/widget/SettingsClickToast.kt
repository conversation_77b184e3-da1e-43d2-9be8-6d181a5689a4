package com.bitech.vehiclesettings.view.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.ToastSettingsClickTipsBinding
import com.bitech.vehiclesettings.databinding.ToastSettingsDmTipsBinding
import com.bitech.vehiclesettings.manager.CarConfigInfoManager

/**
 * @ClassName: SettingsDmTipToast
 *
 * @Date:  2024/3/30 13:25
 * @Description: Setting dm自定义Toast.
 **/
@SuppressLint("InflateParams")
class SettingsClickToast(context: Context) : Toast(context) {
    // window对象
    private var windowManager: WindowManager? = null

    // window属性对象
    private val layoutParams: WindowManager.LayoutParams

    // 提示文言
    private var tips: String? = null
    private var toastView: ToastSettingsClickTipsBinding
    private var clickCallback: OnClickCallback? = null

    init {
        toastView = ToastSettingsClickTipsBinding.bind(LayoutInflater.from(context).inflate(R.layout.toast_settings_click_tips, null))
        // 初始化windowManager对象
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        // 设置窗口参数
        layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_STATUS_BAR_PANEL,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                    or WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        )
        // 设置Toast位置
        layoutParams.token = toastView.root.windowToken
        layoutParams.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
        layoutParams.y = 130
        toastView.root.setOnClickListener {
            if (toastView.root.windowToken != null) {
                clickCallback?.onClick()
                clickCallback = null
                windowManager?.removeView(toastView.root)
            }
        }

    }

    /**
     * 显示Toast提示.
     *
     * @param message 消息
     */
    fun showToast(message: String) {
        Handler(Looper.getMainLooper()).post {
            tips = message
            toastView.toastClickTipsTv.text= message
            windowManager?.addView(toastView.root, layoutParams)
            Handler(Looper.getMainLooper()).postDelayed({
                if (toastView.root.windowToken != null) {
                    windowManager?.removeView(toastView.root)
                }
            }, 5000)
        }
    }

    /**
     * 设置确认按钮点击事件监听.
     *
     * @param callback
     */
    fun setClickCallback(callback: OnClickCallback) {
        clickCallback = callback
    }

    interface OnClickCallback {
        /**
         * 确认按钮被点击.
         *
         */
        fun onClick()
    }
}
