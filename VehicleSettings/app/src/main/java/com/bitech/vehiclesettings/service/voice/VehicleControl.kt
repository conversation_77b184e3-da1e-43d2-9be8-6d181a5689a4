package com.bitech.vehiclesettings.service.voice

import android.annotation.StringRes
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.constants.CarLight
import com.bitech.platformlib.constants.ConfigureWordParameter
import com.bitech.platformlib.constants.IntelligentDriving
import com.bitech.platformlib.manager.CarSettingManager
import com.bitech.platformlib.manager.ConditionManager
import com.bitech.platformlib.manager.ConfigManager
import com.bitech.platformlib.manager.ConnectManager
import com.bitech.platformlib.manager.DrivingManager
import com.bitech.platformlib.manager.IntelligentDrivingManager
import com.bitech.platformlib.manager.LightManager
import com.bitech.platformlib.manager.NewEnergyManager
import com.bitech.platformlib.manager.QuickManager
import com.bitech.platformlib.manager.RearviewManager
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarDriving
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.carapi.constants.Voice
import com.bitech.vehiclesettings.fragment.ConditionFragment
import com.bitech.vehiclesettings.fragment.DrivingFragment
import com.bitech.vehiclesettings.fragment.QuickControlFragment
import com.bitech.vehiclesettings.presenter.connect.ConnectPresenter
import com.bitech.vehiclesettings.presenter.driving.DrivingPresenter
import com.bitech.vehiclesettings.presenter.recognition.RecognitionPresenter
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.DialogNavigationUtils
import com.bitech.vehiclesettings.utils.EToast
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import com.bitech.vehiclesettings.utils.TtsHelper
import com.bitech.vehiclesettings.utils.TtsHelper.TtsInitListener
import com.bitech.vehiclesettings.utils.TtsHelper.TtsPlayListener
import com.bitech.vehiclesettings.view.quickcontrol.RegularWashUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.WashCarModeUIAlert
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel.Companion.newEnergyManager
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P


class VehicleControl(context: Context) {
	private val TAG = "VehicleControl"
	private val mQuickManager: QuickManager = QuickManager.getInstance()
	private val mRearviewManager: RearviewManager = RearviewManager.getInstance()
	private val mConfigManager: ConfigManager? = ConfigManager.getInstance()
	var mCarSettingManager: CarSettingManager = BitechCar.getInstance()
		.getServiceManager(BitechCar.CAR_SETTING_MANAGER) as CarSettingManager
	val mDrivingManager: DrivingManager = BitechCar.getInstance()
		.getServiceManager(BitechCar.CAR_DRIVING_MANAGER) as DrivingManager
	private val mContext: Context = context.applicationContext
	private val context: Context = MyApplication.getInstance()
	private val mDrivingPresenter = DrivingPresenter(context)
	private val BACK_LEFT: String = "LB"
	private val BACK_RIGHT: String = "RB"
	private val mConnectPresenter = ConnectPresenter(context)
	private var carNewEnergyManager: NewEnergyManager = newEnergyManager
	private var mRecognitionPresenter = RecognitionPresenter<Any>(context)
	private val lightManager: LightManager = LightManager.getInstance(context)
	private val mIntelligentDrivingManager: IntelligentDrivingManager =
		IntelligentDrivingManager.getInstance(context)
	private val connectManager: ConnectManager = ConnectManager.getInstance()
	private val conditionManager: ConditionManager = ConditionManager.getInstance()


	//单回复提示语id
	private fun sendResultCode(respondId: String) {
		val param = VDP2P()
		param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
		param.respondId = respondId
		val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
		VDBus.getDefault().set(event)
		Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id" + param.respondId)
	}

	//特殊提示语id
	private fun sendResultCode(respondId: String, mValue: String) {
		val param = VDP2P()
		param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
		param.respondId = respondId
		param.value = mValue

		val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
		VDBus.getDefault().set(event)
		Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id" + param.respondId)
	}

	fun inductionLockControl(flag: Boolean) {


		val departureLocking = mQuickManager.getDepartureLocking()
		if (flag) {
			if (departureLocking == CarQuickControl.GetDepartureLockingSts.OFF) {
				mQuickManager.setDepartureLocking(CarQuickControl.SetDepartureLockingSts.ON)
				sendResultCode(VDVRRespondID.open_proximity_lock_when_leaving_the_car_1)
			} else if (departureLocking == CarQuickControl.GetDepartureLockingSts.ON) {
				sendResultCode(VDVRRespondID.open_proximity_lock_when_leaving_the_car_2)
			}
		} else {
			if (departureLocking == CarQuickControl.GetDepartureLockingSts.OFF) {
				sendResultCode(VDVRRespondID.close_proximity_lock_when_leaving_the_car_1)
			} else if (departureLocking == CarQuickControl.GetDepartureLockingSts.ON) {
				mQuickManager.setDepartureLocking(CarQuickControl.SetDepartureLockingSts.OFF)
				sendResultCode(VDVRRespondID.close_proximity_lock_when_leaving_the_car_2)
			}
		}
	}

	fun centralLockControl(flag: Boolean) {
		val centerLock = mQuickManager.getCenterLock()
		if (flag) {
			if (centerLock == CarQuickControl.GetCentralLockSts.UNLOCKED) {
				mQuickManager.setCenterLock(CarQuickControl.SetCentralLockSts.LOCK)
				sendResultCode(VDVRRespondID.open_central_lock_1)
			} else if (centerLock == CarQuickControl.GetCentralLockSts.LOCKED) {
				sendResultCode(VDVRRespondID.open_central_lock_2)
			}
		} else {
			if (centerLock == CarQuickControl.GetCentralLockSts.UNLOCKED) {
				sendResultCode(VDVRRespondID.close_central_lock_1)
			} else if (centerLock == CarQuickControl.GetCentralLockSts.LOCKED) {
				mQuickManager.setCenterLock(CarQuickControl.SetCentralLockSts.UNLOCK)
				sendResultCode(VDVRRespondID.close_central_lock_2)
			}
		}
	}

	fun sensorUnlockControl(flag: Boolean) {
		val approachingUnlock = mQuickManager.getApproachingUnlock()

		if (flag) {
			if (approachingUnlock == CarQuickControl.GetApproachingUnlockSts.OFF) {
				mQuickManager.setApproachingUnlock(CarQuickControl.SetDepartureLockingSts.ON)
				sendResultCode(VDVRRespondID.open_inductive_proximity_unlock_1)
			} else if (approachingUnlock == CarQuickControl.GetApproachingUnlockSts.ON) sendResultCode(
				VDVRRespondID.open_inductive_proximity_unlock_2
			)
		} else {
			if (approachingUnlock == CarQuickControl.GetApproachingUnlockSts.OFF) {
				sendResultCode(VDVRRespondID.close_inductive_proximity_unlock_1)
			} else if (approachingUnlock == CarQuickControl.GetApproachingUnlockSts.ON) {
				mQuickManager.setApproachingUnlock(CarQuickControl.SetDepartureLockingSts.OFF)
				sendResultCode(VDVRRespondID.close_inductive_proximity_unlock_2)
			}

		}
	}

	fun setHoldBrake(flag: Boolean) {
		sendResultCode(VDVRRespondID.open_auto_hold_1)
	}

	fun childLockControl(flag: Boolean, position: String) {
//      TODO:判断是否支持功能 open_child_lock_3 close_child_lock_3
		val leftChildLockState = mQuickManager.getLeftChildLock()
		val rightChildLockState = mQuickManager.getRightChildLock()
		if (flag) {
			if (position == BACK_LEFT) {
				if (leftChildLockState == Voice.LockConstants.LOCKED || leftChildLockState == Voice.LockConstants.SUPERLOCK) {
					sendResultCode(VDVRRespondID.open_child_lock_2)
				} else if (leftChildLockState == Voice.LockConstants.UNLOCK) {
					mQuickManager.setLeftChildLock(Voice.ChildLockConstants.CHILD_LOCK_RL);
					sendResultCode(VDVRRespondID.open_child_lock_1)
				}
			} else if (position == BACK_RIGHT) {
				if (rightChildLockState == Voice.LockConstants.LOCKED || rightChildLockState == Voice.LockConstants.SUPERLOCK) {

					sendResultCode(VDVRRespondID.open_child_lock_2)
				} else if (rightChildLockState == Voice.LockConstants.UNLOCK) {
					mQuickManager.setRightChildLock(Voice.ChildLockConstants.CHILD_LOCK_RR);
					sendResultCode(VDVRRespondID.open_child_lock_1)
				}
			}
		} else {
			if (position == BACK_LEFT) {
				if (leftChildLockState == 0) {
					mQuickManager.setLeftChildLock(Voice.ChildLockConstants.CHILD_LOCK_RL);
					sendResultCode(VDVRRespondID.close_child_lock_2)
				} else if (leftChildLockState == 1) sendResultCode(VDVRRespondID.close_child_lock_1)
			} else if (position == BACK_RIGHT) {
				if (rightChildLockState == 0) {
					mQuickManager.setLeftChildLock(Voice.ChildLockConstants.CHILD_LOCK_RR);
					sendResultCode(VDVRRespondID.close_child_lock_2)
				} else if (rightChildLockState == 1) sendResultCode(VDVRRespondID.close_child_lock_1)
			}
		}

	}

	fun openLockModeControl(value: String) {
		if (value == "1") {
			val automaticLocking = mQuickManager.getAutomaticLocking()
			if (automaticLocking == CarQuickControl.GetAutoLockSts.NOT_AUTOLOCK_MODE) {
				mQuickManager.setAutomaticLocking(CarQuickControl.SetAutoLockSts.AUTOLOCK_MODE)
				sendResultCode(VDVRRespondID.open_the_automatic_driving_lock_1)
			} else if (automaticLocking == CarQuickControl.GetAutoLockSts.AUTOLOCK_MODE) {
				sendResultCode(VDVRRespondID.open_the_automatic_driving_lock_2)
			}
		}

	}

	fun lockModeControl(value: String) {
		if (value == "1") {
			val automaticLocking = mQuickManager.getAutomaticLocking()
			if (automaticLocking == CarQuickControl.GetAutoLockSts.NOT_AUTOLOCK_MODE) {
				sendResultCode(VDVRRespondID.close_the_automatic_driving_lock_1)
			} else if (automaticLocking == CarQuickControl.GetAutoLockSts.AUTOLOCK_MODE) {
				mQuickManager.setAutomaticLocking(CarQuickControl.SetAutoLockSts.NOT_AUTOLOCK_MODE)
				sendResultCode(VDVRRespondID.close_the_automatic_driving_lock_2)
			}
		}
	}

	fun drivingLockControl(flag: Boolean) {
//		TODO:判断是否支持 open_the_automatic_driving_lock_3 close_the_automatic_driving_lock_3
		val automaticLocking = mQuickManager.getAutomaticLocking()
		if (flag) {
			if (automaticLocking == CarQuickControl.GetAutoLockSts.NOT_AUTOLOCK_MODE) {
				mQuickManager.setAutomaticLocking(CarQuickControl.SetAutoLockSts.AUTOLOCK_MODE)
				sendResultCode(VDVRRespondID.open_the_automatic_driving_lock_1)
			} else if (automaticLocking == CarQuickControl.GetAutoLockSts.AUTOLOCK_MODE) {
				sendResultCode(VDVRRespondID.open_the_automatic_driving_lock_2)
			}
		} else {
			if (automaticLocking == CarQuickControl.GetAutoLockSts.NOT_AUTOLOCK_MODE) {
				sendResultCode(VDVRRespondID.close_the_automatic_driving_lock_1)
			} else if (automaticLocking == CarQuickControl.GetAutoLockSts.AUTOLOCK_MODE) {
				mQuickManager.setAutomaticLocking(CarQuickControl.SetAutoLockSts.NOT_AUTOLOCK_MODE)
				sendResultCode(VDVRRespondID.close_the_automatic_driving_lock_2)
			}
		}

	}

	/**
	 * 打开座椅便捷进入/退出
	 * @param flag true:打开
	 */
	fun driveConvenientEntryExitControl(flag: Boolean) {
//		TODO:open_main_driver_convenience_of_entering_or_exiting_3 close_main_driver_convenience_of_entering_or_exiting_3
		val seatPortable = mQuickManager.seatPortable
		if (flag) {
			if (seatPortable == CarQuickControl.GetSeatPortableSts.OFF) {
				mQuickManager.seatPortable = CarQuickControl.SetSeatPortableSts.ON
				sendResultCode(VDVRRespondID.open_main_driver_convenience_of_entering_or_exiting_1)
			} else if (seatPortable == CarQuickControl.GetSeatPortableSts.ON) {
				sendResultCode(VDVRRespondID.open_main_driver_convenience_of_entering_or_exiting_2)
			}
		} else {
			if (seatPortable == CarQuickControl.GetSeatPortableSts.OFF) {
				sendResultCode(VDVRRespondID.close_main_driver_convenience_of_entering_or_exiting_1)
			} else if (seatPortable == CarQuickControl.GetSeatPortableSts.ON) {
				mQuickManager.seatPortable = CarQuickControl.SetSeatPortableSts.OFF
				sendResultCode(VDVRRespondID.close_main_driver_convenience_of_entering_or_exiting_2)
			}
		}
	}

	/**
	 * 舒适制动开关
	 * @param flag true 开启 false 关闭
	 */
	fun comfortBrakeControl(flag: Boolean) {
		val comfortBraking = mDrivingManager.getCSTEnableStatus()
		if (flag) {
			if (comfortBraking == CarDriving.CST_Status.DISABLED) {
				mDrivingManager.setCSTEnableStatusSet(CarDriving.Set_CSTFunctionSts.CST_ON)
				sendResultCode(VDVRRespondID.open_comfortable_park_1)
			} else if (comfortBraking == CarDriving.CST_Status.ACTIVE || comfortBraking == CarDriving.CST_Status.STANDBY) {
				sendResultCode(VDVRRespondID.open_comfortable_park_2)
			} else if (comfortBraking == CarDriving.CST_Status.FAILURE) {
				sendResultCode(VDVRRespondID.open_comfortable_park_3)
			}
		} else {
			if (comfortBraking == CarDriving.CST_Status.DISABLED) {
				sendResultCode(VDVRRespondID.close_comfortable_park_1)
			}
			if (comfortBraking == CarDriving.CST_Status.ACTIVE || comfortBraking == CarDriving.CST_Status.STANDBY) {
				mDrivingManager.setCSTEnableStatusSet(CarDriving.Set_CSTFunctionSts.CST_OFF)
				sendResultCode(VDVRRespondID.close_comfortable_park_2)
			}
			if (comfortBraking == CarDriving.CST_Status.FAILURE) {
				sendResultCode(VDVRRespondID.close_comfortable_park_3)
			}
		}
	}

	/**
	 * 舒适制动模式设置为xxx
	 * @param value 0=低，1=中，2=高，3=通勤，4=防晕车，5=儿童
	 */
	fun setComfortBrakeMode(value: String) {
		fun mapComfortMode(value: String): String {
			return when (value) {
				"0" -> "低"
				"1" -> "中"
				"2" -> "高"
				"3" -> "通勤"
				"4" -> "防晕车"
				"5" -> "儿童"
				else -> "未知"
			}
		}

		val modeName = mapComfortMode(value)
		val comfortBraking = mDrivingManager.cstEnableStatus
		val comfortBrakingRank = mDrivingManager.cstIntervention
		if (comfortBraking == CarDriving.CST_Status.FAILURE) {
			sendResultCode(VDVRRespondID.set_comfort_braking_rating_to_gear_4)
		} else if (value == Voice.ComfortLevel.LOW || value == Voice.ComfortLevel.COMMUTER) {
			if (comfortBrakingRank == CarDriving.CST_SensitivitySts.LOW) {
				sendResultCode(VDVRRespondID.set_comfort_braking_rating_to_gear_2, modeName)
			} else {
				mDrivingManager.setCSTInterventionSet(CarDriving.CST_SensitivityReq.LOW)
				sendResultCode(VDVRRespondID.set_comfort_braking_rating_to_gear_1, modeName)
			}
		} else if (value == Voice.ComfortLevel.MIDDLE || value == Voice.ComfortLevel.ANTI_MOTION) {
			if (comfortBrakingRank == CarDriving.CST_SensitivitySts.MEDIUM) {
				sendResultCode(VDVRRespondID.set_comfort_braking_rating_to_gear_2, modeName)
			} else {
				mDrivingManager.setCSTInterventionSet(CarDriving.CST_SensitivityReq.MEDIUM)
				sendResultCode(VDVRRespondID.set_comfort_braking_rating_to_gear_1, modeName)
			}
		} else if (value == Voice.ComfortLevel.HIGH || value == Voice.ComfortLevel.CHILD) {
			if (comfortBrakingRank == CarDriving.CST_SensitivitySts.HIGH)
				sendResultCode(VDVRRespondID.set_comfort_braking_rating_to_gear_2, modeName)
			else {
				mDrivingManager.setCSTInterventionSet(CarDriving.CST_SensitivityReq.HIGH)
				sendResultCode(VDVRRespondID.set_comfort_braking_rating_to_gear_1, modeName)
			}
		} else
			sendResultCode(VDVRRespondID.set_comfort_braking_rating_to_gear_3)
	}

	/**
	 * 车内踏板助力模式开关
	 * @param flag true 开启 false 关闭
	 */
	fun carPedalAssistModeControl(flag: Boolean) {
		when (flag) {
			true -> sendResultCode(VDVRRespondID.open_pedal_assist_mode_1)
			false -> sendResultCode(VDVRRespondID.close_pedal_assist_mode_1)
		}
	}

	/**
	 * 切换车内踏板模式的内容，无指定值
	 */
	fun changeCarPedalAssistModeControl() {
		sendResultCode(VDVRRespondID.switch_pedal_assist_mode_1)
	}

	/**
	 * 车内踏板灵敏度调高一点
	 */
	fun setCarPedalAssistSensitivity(value: String) {
		when (value) {
			"0" -> sendResultCode(VDVRRespondID.raise_pedal_assist_sensitivity_little_1)
			"1" -> sendResultCode(VDVRRespondID.lower_pedal_assist_sensitivity_little_1)
		}
	}

	/**
	 * 车内踏板助力模式的内容设置
	 */
	fun setCarPedalAssistMode(value: String) {
		when (value) {
			"0", "1", "2" -> sendResultCode(VDVRRespondID.set_pedal_assist_mode_1)

		}
	}

	fun setInstrumentTheme(value: String) {
		when (value) {
			"0", "1", "2", "3", "4" -> sendResultCode(VDVRRespondID.set_instrument_screen_mode_1);

		}
	}

	fun setInstrumentTheme() {
		sendResultCode(VDVRRespondID.set_instrument_screen_mode_1);
	}

	fun autoTopLightControl(flag: Boolean) {
//      TODO:判断车辆是否支持
		val status: Int = lightManager.autoReadLightSts

		Log.d(TAG, "status:$status")
		if (flag) {
			when (status) {
				CarLight.AutoReadLightSts.OPEN -> {
					sendResultCode(VDVRRespondID.open_automatic_ceiling_lamp_2)
				}

				CarLight.AutoReadLightSts.CLOSE -> {
					lightManager.setDoorControlSW(CarLight.DoorControlSW.ON)
					sendResultCode(VDVRRespondID.open_automatic_ceiling_lamp_1)
				}
			}
		} else {
			when (status) {
				CarLight.AutoReadLightSts.OPEN -> {
					lightManager.setDoorControlSW(CarLight.DoorControlSW.OFF)
					sendResultCode(VDVRRespondID.close_automatic_ceiling_lamp_2)
				}

				CarLight.AutoReadLightSts.CLOSE -> {
					sendResultCode(VDVRRespondID.close_automatic_ceiling_lamp_1)
				}
			}

		}
	}

	fun readFogLightControl(flag: Boolean) {

//      TODO:判断是否支持 open_rear_fog_lamp_3
		val status = lightManager.rearFogLightSts
		Log.d(TAG, "status:$status")
		if (flag) {
			when (status) {
				CarLight.RearFogLightSts.ON -> {
					sendResultCode(VDVRRespondID.open_rear_fog_lamp_2)
				}

				CarLight.RearFogLightSts.OFF -> {
					lightManager.setRearFogSw(CarLight.RearFogSw.ON)
					sendResultCode(VDVRRespondID.open_rear_fog_lamp_1)
				}
			}
		} else {
			when (status) {
				CarLight.RearFogLightSts.ON -> {
					lightManager.setRearFogSw(CarLight.RearFogSw.OFF)
					sendResultCode(VDVRRespondID.close_rear_fog_lamp_2)
				}

				CarLight.RearFogLightSts.OFF -> {
					sendResultCode(VDVRRespondID.close_rear_fog_lamp_1)
				}
			}

		}
	}

	// TODO
	fun setIntelligentWelcome(flag: Boolean) {

//      TODO:判断是否支持 open_intelligent_welcome_3
		val status = Prefs.get(
			PrefsConst.L_INTELLIGENT_WELCOME,
			PrefsConst.DefaultValue.L_INTELLIGENT_WELCOME
		)
		Log.d(TAG, "status:$status")

		if (flag) {
			when (status) {
				Voice.LightConstants.ON -> {
					sendResultCode(VDVRRespondID.open_intelligent_welcome_2)
				}

				Voice.LightConstants.OFF -> {
					setIntelligentWelcome(Voice.LightConstants.ON)
					sendResultCode(VDVRRespondID.open_intelligent_welcome_1)
				}
			}

		} else {
			when (status) {
				Voice.LightConstants.ON -> {
					setIntelligentWelcome(Voice.LightConstants.OFF)
					sendResultCode(VDVRRespondID.close_intelligent_welcome_2)
				}

				Voice.LightConstants.OFF -> {
					sendResultCode(VDVRRespondID.close_intelligent_welcome_1)
				}
			}
			if (status == Voice.LightConstants.ON) {
				setIntelligentWelcome(Voice.LightConstants.OFF)
				sendResultCode(VDVRRespondID.close_intelligent_welcome_2)
			} else if (status == Voice.LightConstants.OFF) {
				sendResultCode(VDVRRespondID.close_intelligent_welcome_1)
			}
		}
	}

	private fun setIntelligentWelcome(state: Int) {
		// 设置只能迎宾灯信号，无返回信号 LiShowMod
		if (state == 1) {
			lightManager.setLiShowMod(CarLight.LiShowMod.Mode1)
		} else {
			lightManager.setLiShowMod(CarLight.LiShowMod.OFF)
		}
		// 与MCU对接接口
		lightManager.intelligentWelcomeLight(state)
		Prefs.put(PrefsConst.L_INTELLIGENT_WELCOME, state)
	}

	/**
	 * 打开or关闭靠近迎宾
	 * @param flag true:打开  false:关闭
	 */
	fun approachWelcomeControl(flag: Boolean) {
//      TODO:判断是否支持
		val status = lightManager.welcomeOpenStas;
		Log.d(TAG, "status:$status")
		if (flag) {
			when (status) {
				CarLight.WelcomeOpenStas.ON -> {
					sendResultCode(VDVRRespondID.open_approach_welcome_2)
				}

				CarLight.WelcomeOpenStas.OFF -> {
					lightManager.setWelcomeOpenSetCmd(CarLight.WelcomeOpenSetCmd.ON)
					sendResultCode(VDVRRespondID.open_approach_welcome_1)
				}
			}

		} else {
			when (status) {
				CarLight.WelcomeOpenStas.ON -> {
					lightManager.setWelcomeOpenSetCmd(CarLight.WelcomeOpenSetCmd.OFF)
					sendResultCode(VDVRRespondID.close_approach_welcome_2)
				}

				CarLight.WelcomeOpenStas.OFF -> {
					sendResultCode(VDVRRespondID.close_approach_welcome_1)
				}
			}
		}
	}

	/**
	 * 打开or关闭智能远近光灯切换
	 * @param flag true:打开  false:关闭
	 */
	//TODO
	fun setIntelligentBeamLight(flag: Boolean) {
		//TODO:判断是否支持 close_intelligent_high_lamp_3 Turn_on_smart_high_and_low_beam_switch_3
		val highLowSwitch = mIntelligentDrivingManager.getIHCSts()
		if (flag) {
			if (highLowSwitch == IntelligentDriving.ADASEnumIHCStatus.STANDBY || highLowSwitch == IntelligentDriving.ADASEnumIHCStatus.ACTIVE || highLowSwitch == IntelligentDriving.ADASEnumIHCStatus.FAULT) {
				sendResultCode(VDVRRespondID.Turn_on_smart_high_and_low_beam_switch_2);
			} else if (highLowSwitch == IntelligentDriving.ADASEnumIHCStatus.OFF) {
				mIntelligentDrivingManager.setIHCEnableBtnSt(1)
				sendResultCode(VDVRRespondID.Turn_on_smart_high_and_low_beam_switch_1);
			}
		} else {
			if (highLowSwitch == IntelligentDriving.ADASEnumIHCStatus.STANDBY || highLowSwitch == IntelligentDriving.ADASEnumIHCStatus.ACTIVE || highLowSwitch == IntelligentDriving.ADASEnumIHCStatus.FAULT) {
				mIntelligentDrivingManager.setIHCEnableBtnSt(2)
				sendResultCode(VDVRRespondID.Turn_off_smart_high_and_low_beam_switch_2);
			} else if (highLowSwitch == IntelligentDriving.ADASEnumIHCStatus.OFF) {
				sendResultCode(VDVRRespondID.Turn_off_smart_high_and_low_beam_switch_1);
			}
		}
	}

	fun adjustModeExteriorAngle(value: String) {
		val lampStatus = lightManager.getLowBeamHighSts()
		val lampControl = lightManager.getLightMainSwitchSts()
		Log.d(TAG, "lampStatus:$lampStatus lampControl:$lampControl")
		if (value == Voice.LevelConstants.HIGHER) {
			if (lampControl != CarLight.LightMainSwitchSts.OFF) {
				if (lampStatus != CarLight.LowBeamHighSts.LEVEL_0) {
					if (lampStatus - 1 > CarLight.LowBeamHighSts.LEVEL_0) {
						lightManager.setHeadlampHeightSts(lampStatus)
						sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_angle_little_1);
					} else if (lampStatus - 1 == CarLight.LowBeamHighSts.LEVEL_0) {
						lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_0)
						sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_angle_little_5);
					}
				} else {
					sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_angle_little_2);
				}
			} else {
				sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_angle_little_3);
			}
		} else if (value == Voice.LevelConstants.LOWER) {
			if (lampStatus != CarLight.LowBeamHighSts.LEVEL_3) {
				if (lampStatus + 1 < CarLight.LowBeamHighSts.LEVEL_3) {
					lightManager.setHeadlampHeightSts(lampStatus + 2)
					sendResultCode(VDVRRespondID.lower_designative_out_car_lamp_angle_little_1);
				} else if (lampStatus + 1 == CarLight.LowBeamHighSts.LEVEL_3) {
					lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_3)
					sendResultCode(VDVRRespondID.lower_designative_out_car_lamp_angle_little_5);
				}
			} else {
				sendResultCode(VDVRRespondID.lower_designative_out_car_lamp_angle_little_2);
			}
		} else if (value == Voice.LevelConstants.HIGH) {
			sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_gear_1)
		} else if (value == Voice.LevelConstants.LOW) {
			sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_gear_1)
		} else if (value == Voice.LevelConstants.MEDIUM) {
			sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_gear_1)
		} else if (value == Voice.LevelConstants.HIGHEST) {
			if (lampStatus != CarLight.LowBeamHighSts.LEVEL_0) {
				lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_0)
				sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_max_2)
			} else {
				sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_max_3)
			}
		} else if (value == Voice.LevelConstants.LOWEST) {
			if (lampStatus != CarLight.LowBeamHighSts.LEVEL_3) {
				lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_3)
				sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_min_2)
			} else {
				sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_min_3)
			}
		}
	}

	fun adjustUpExteriorAngle(value: String) {
		val lampStatus = lightManager.getLowBeamHighSts()
		try {
			val level = value.toInt()
			if (lampStatus != CarLight.LowBeamHighSts.LEVEL_0) {
				if (lampStatus - level > CarLight.LowBeamHighSts.LEVEL_0) {
					if (lampStatus - level == CarLight.LowBeamHighSts.LEVEL_1) {
						lightManager.setHeadlampHeightSts(lampStatus - level + 1)
						sendResultCode(
							VDVRRespondID.raise_designative_out_car_lamp_angle_by_number_1,
							"1"
						)
					} else if (lampStatus - level == CarLight.LowBeamHighSts.LEVEL_2) {
						lightManager.setHeadlampHeightSts(lampStatus - level + 1)
						sendResultCode(
							VDVRRespondID.raise_designative_out_car_lamp_angle_by_number_1,
							"2"
						)
					} else if (lampStatus - level == CarLight.LowBeamHighSts.LEVEL_3) {
						lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_3)
						sendResultCode(
							VDVRRespondID.raise_designative_out_car_lamp_angle_by_number_3,
							"3"
						)
					}

				} else if (lampStatus - level == CarLight.LowBeamHighSts.LEVEL_0) {
					lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_0)
					sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_angle_by_number_5)
				} else if (lampStatus - level < CarLight.LowBeamHighSts.LEVEL_0) {
					sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_angle_by_number_6)
				}
			} else {
				sendResultCode(VDVRRespondID.raise_designative_out_car_lamp_angle_by_number_2)
			}
		} catch (e: NumberFormatException) {
			Log.e(TAG, "VEHICLE_ADJUST_UP_EXTERIOR_ANGLE,value" + e.message)
		}
	}

	fun adjustDownExteriorAngle(value: String) {
		val lampStatus = lightManager.getLowBeamHighSts()

		try {
			val level = value.toInt()

			if (lampStatus != CarLight.LowBeamHighSts.LEVEL_3) {
				if (lampStatus + level < CarLight.LowBeamHighSts.LEVEL_3) {
					if (lampStatus + level == CarLight.LowBeamHighSts.LEVEL_2) {
						lightManager.setHeadlampHeightSts(lampStatus + level + 1)
						sendResultCode(
							VDVRRespondID.lower_designative_out_car_lamp_angle_by_number_1,
							"2"
						)
					} else if (lampStatus + level == CarLight.LowBeamHighSts.LEVEL_1) {
						lightManager.setHeadlampHeightSts(lampStatus + level + 1)
						sendResultCode(
							VDVRRespondID.lower_designative_out_car_lamp_angle_by_number_1,
							"1"
						)
					} else if (lampStatus + level == CarLight.LowBeamHighSts.LEVEL_0) {
						lightManager.setHeadlampHeightSts(lampStatus + level + 1)
						sendResultCode(
							VDVRRespondID.lower_designative_out_car_lamp_angle_by_number_3,
							"0"
						)
					}

				} else if (lampStatus + level == CarLight.LowBeamHighSts.LEVEL_3) {
					lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_3)
					sendResultCode(VDVRRespondID.lower_designative_out_car_lamp_angle_by_number_5)
				} else if (lampStatus + level > CarLight.LowBeamHighSts.LEVEL_3) {
					sendResultCode(VDVRRespondID.lower_designative_out_car_lamp_angle_by_number_6)
				}
			} else {
				sendResultCode(VDVRRespondID.lower_designative_out_car_lamp_angle_by_number_2)
			}
		} catch (e: java.lang.NumberFormatException) {
			Log.e(TAG, "VEHICLE_ADJUST_DOWN_EXTERIOR_ANGLE,value转换失败" + e.message)
		}
	}

	fun adjustExteriorAngle(value: String) {
		val lampStatus = lightManager.getLowBeamHighSts()
		try {
			val level = value.toInt()
			val angle = level
			// 讯飞给到value从1开始
			if (angle < CarLight.LowBeamHighSts.LEVEL_0 || angle > CarLight.LowBeamHighSts.LEVEL_3) {
				sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_number_5)
			} else {
				if (angle != lampStatus) {
					if (angle == CarLight.LowBeamHighSts.LEVEL_0) {
						lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_0)
						sendResultCode(
							VDVRRespondID.adjust_designative_out_car_lamp_angle_to_number_1,
							"0"
						)
					} else if (angle == CarLight.LowBeamHighSts.LEVEL_1) {
						lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_1)
						sendResultCode(
							VDVRRespondID.adjust_designative_out_car_lamp_angle_to_number_1,
							mValue = "1"
						)
					} else if (angle == CarLight.LowBeamHighSts.LEVEL_2) {
						lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_2)
						sendResultCode(
							VDVRRespondID.adjust_designative_out_car_lamp_angle_to_number_1,
							mValue = "2"
						)
					} else if (angle == CarLight.LowBeamHighSts.LEVEL_3) {
						lightManager.setHeadlampHeightSts(CarLight.HeadlampHeightSts.LEVEL_3)
						sendResultCode(
							VDVRRespondID.adjust_designative_out_car_lamp_angle_to_number_1,
							mValue = "3"
						)
					}


				} else {
					sendResultCode(VDVRRespondID.adjust_designative_out_car_lamp_angle_to_number_2)
				}
			}
		} catch (e: java.lang.NumberFormatException) {
			Log.e(TAG, "VEHICLE_ADJUST_EXTERIOR_ANGLE,value转换失败" + e.message)
		}
	}

	fun setAppointPowerMode() {
		sendResultCode(VDVRRespondID.open_power_mode_1)
	}

	fun changePowerMode() {
		sendResultCode(VDVRRespondID.switch_power_mode_1)
	}

	fun setSteeringMode() {
//		TODO:拉起对应页面
		sendResultCode(VDVRRespondID.set_specific_eps_mode_5)
	}

	fun escControl(flag: Boolean) {
		val bodyStabilityControl = mDrivingManager.getBodyInfoESC()
		if (flag) {
			if (bodyStabilityControl == CarDriving.ESPSwitchStatus.ON)
				sendResultCode(VDVRRespondID.open_esc_2)
			else if (bodyStabilityControl == CarDriving.ESPSwitchStatus.OFF) {
				mDrivingManager.setBodyInfoESCSet(CarDriving.Set_ESPFunctionSts.ON)
				sendResultCode(VDVRRespondID.open_esc_1)
			}
		} else {
			if (bodyStabilityControl == CarDriving.ESPSwitchStatus.ON) {
				mDrivingManager.setBodyInfoESCSet(CarDriving.Set_ESPFunctionSts.OFF)
				sendResultCode(VDVRRespondID.close_esc_2)
			} else if (bodyStabilityControl == CarDriving.ESPSwitchStatus.OFF)
				sendResultCode(VDVRRespondID.close_esc_1)
		}
	}

	/**
	 * 打开or关闭雨刮维修
	 * @param flag true 打开 false 关闭
	 */
	fun setWipeRepair(flag: Boolean) {
		// 获取雨刮维修模式开关是否置灰
		val state = mCarSettingManager.getWiperModeAvailable()
		// 获取雨刮维修模式开关是否打开
		val status = mCarSettingManager.getWiperMode()
		// 获取是否处于洗车模式
		val wash_status = mDrivingManager.getFLZCU_CleanModeStatus()
		Log.d(TAG, "WiperRepair.status:" + status)
		if (flag) {
			if (wash_status == CarSettingConstant.WASH_MODE_OPEN) {
				sendResultCode(VDVRRespondID.open_wiper_maintenance_4)
			} else if (state == CarSettingConstant.FAIL_CAUSE_1) {
				Log.d(TAG, "WiperRepair.WIPER_REPAIR_MODE_OPEN")
				sendResultCode(VDVRRespondID.open_wiper_maintenance_5)
			} else if (state == CarSettingConstant.FAIL_CAUSE_2) {
				sendResultCode(VDVRRespondID.open_wiper_maintenance_6)
			} else if (state == CarSettingConstant.FAIL_CAUSE_3) {
				sendResultCode(VDVRRespondID.open_wiper_maintenance_7)
			} else {
				//判断雨刮维修模式开关为关时
				if (status == CarSettingConstant.WIPER_CLOSE_SIGNAL_0 || status == CarSettingConstant.WIPER_CLOSE_SIGNAL_1 || status == CarSettingConstant.WIPER_CLOSE_SIGNAL_2) {
					Log.d(TAG, "WiperRepair.WIPER_REPAIR_MODE_OPEN");
					mCarSettingManager.setWiperMode(CarSettingConstant.WIPER_REPAIR_MODE_OPEN)
					sendResultCode(VDVRRespondID.open_wiper_maintenance_2)
				} else {
					sendResultCode(VDVRRespondID.open_wiper_maintenance_1)
				}
			}
		} else {
			if (state == CarSettingConstant.FAIL_CAUSE_1) {
				sendResultCode(VDVRRespondID.close_wiper_maintenance_4)
			} else if (state == CarSettingConstant.FAIL_CAUSE_2) {
				sendResultCode(VDVRRespondID.close_wiper_maintenance_5)
			} else if (state == CarSettingConstant.FAIL_CAUSE_3) {
				sendResultCode(VDVRRespondID.close_wiper_maintenance_6)
			} else {
				if (status == CarSettingConstant.WIPER_OPEN_SIGNAL) {
					Log.d(TAG, "WiperRepair.WIPER_REPAIR_MODE_CLOSE");
					mCarSettingManager.setWiperMode(CarSettingConstant.WIPER_REPAIR_MODE_CLOSE)
					sendResultCode(VDVRRespondID.close_wiper_maintenance_2)
				} else {
					sendResultCode(VDVRRespondID.close_wiper_maintenance_1)
				}
			}
		}
	}

	/**
	 * 雨刷灵敏度调高/低/最高/最低/调到高中低挡
	 * @param value 0=高一点，1=低一点，2=高档，3=中档，4=低档，5=最高，6最低
	 */
	fun setWiperSensMode(value: String) {
		val status = mCarSettingManager.getWiperSensitivity()
		Log.d(TAG, "WiperSensMode.status:" + status)
		when (value.toInt()) {
			//雨刮灵敏度调高一点
			VehicleConstant.UP_LITTLE -> {
				if (status != CarSettingConstant.WIPER_SENS_MODE_4) {
					//雨刮灵敏度档位+1
					mCarSettingManager.setWiperSensitivity(status + 1)
					sendResultCode(VDVRRespondID.raise_wiper_sensitivity_little_1)
				} else {
					sendResultCode(VDVRRespondID.raise_wiper_sensitivity_little_2)
				}
			}
			//雨刮灵敏度调低一点
			VehicleConstant.LOW_LITTLE -> {
				if (status != CarSettingConstant.WIPER_SENS_MODE_1) {
					//雨刮灵敏度档位-1
					mCarSettingManager.setWiperSensitivity(status - 1)
					sendResultCode(VDVRRespondID.lower_wiper_sensitivity_little_1)
				} else {
					sendResultCode(VDVRRespondID.lower_wiper_sensitivity_little_2)
				}
			}
			//雨刮灵敏度调到高档
			VehicleConstant.HIGH_LEVEL -> {
				if (status != CarSettingConstant.WIPER_SENS_MODE_3) {
					mCarSettingManager.setWiperSensitivity(CarSettingConstant.WIPER_SENS_MODE_3)
					Log.d(TAG, "WiperSensMode.WIPER_SENS_MODE_3");
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_gear_1, "高")
				} else {
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_gear_3, "高")
				}
			}
			//雨刮灵敏度调到中档
			VehicleConstant.MID_LEVEL -> {
				if (status != CarSettingConstant.WIPER_SENS_MODE_2) {
					mCarSettingManager.setWiperSensitivity(CarSettingConstant.WIPER_SENS_MODE_2)
					Log.d(TAG, "WiperSensMode.WIPER_SENS_MODE_2");
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_gear_1, "中")
				} else {
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_gear_3, "中")
				}
			}
			//雨刮灵敏度调到低档
			VehicleConstant.LOW_LEVEL -> {
				if (status != CarSettingConstant.WIPER_SENS_MODE_1) {
					mCarSettingManager.setWiperSensitivity(CarSettingConstant.WIPER_SENS_MODE_1)
					Log.d(TAG, "WiperSensMode.WIPER_SENS_MODE_1");
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_gear_1, "低")
				} else {
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_gear_3, "低")
				}
			}
			//雨刮灵敏度调到最高
			VehicleConstant.HIGHEST_LEVEL -> {
				if (status != CarSettingConstant.WIPER_SENS_MODE_4) {
					mCarSettingManager.setWiperSensitivity(CarSettingConstant.WIPER_SENS_MODE_4)
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_max_1)
				} else {
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_max_2)
				}
			}
			//雨刮灵敏度调到最低
			VehicleConstant.MINIMUM_LEVEL -> {
				if (status != CarSettingConstant.WIPER_SENS_MODE_1) {
					mCarSettingManager.setWiperSensitivity(CarSettingConstant.WIPER_SENS_MODE_1)
					Log.d(TAG, "WiperSensMode.WIPER_SENS_MODE_1");
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_min_1)
				} else {
					sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_min_2)
				}
			}

			else -> {
				Log.d(TAG, "Invalid value");
			}
		}
	}

	/**
	 * 雨刷灵敏度调高具体数值
	 * @param value
	 */
	fun setWiperSensUp(value: String) {
		// 判断雨刮灵敏度调高数值是否为负值
		if (value.toInt() < 0) {
			sendResultCode(VDVRRespondID.raise_wiper_sensitivity_by_number_3)
		} else {
			val status = mCarSettingManager.getWiperSensitivity()
			//调节后雨刮灵敏度具体数值
			val sum = value.toInt() + status
			if (status != CarSettingConstant.WIPER_SENS_MODE_4) {
				if (sum <= CarSettingConstant.WIPER_SENS_MODE_4) {
					mCarSettingManager.setWiperSensitivity(sum)
					sendResultCode(VDVRRespondID.raise_wiper_sensitivity_by_number_1)
				} else {
					sendResultCode(VDVRRespondID.raise_wiper_sensitivity_by_number_3)
				}
			} else {
				sendResultCode(VDVRRespondID.raise_wiper_sensitivity_by_number_2)
			}
		}
	}

	/**
	 * 雨刷灵敏度调低具体数值
	 * @param value
	 */
	fun setWiperSensDown(value: String) {
		// 判断雨刷灵敏度调低数值是否为负值
		if (value.toInt() < 0) {
			sendResultCode(VDVRRespondID.lower_wiper_sensitivity_by_number_3)
		} else {
			val status = mCarSettingManager.getWiperSensitivity()
			Log.d(TAG, "status:$status")
			//调节后雨刮灵敏度具体数值
			val sum = status - value.toInt()
			if (status != CarSettingConstant.WIPER_SENS_MODE_1) {
				if (sum >= CarSettingConstant.WIPER_SENS_MODE_1) {
					mCarSettingManager.setWiperSensitivity(sum)
					sendResultCode(VDVRRespondID.lower_wiper_sensitivity_by_number_1)
				} else {
					sendResultCode(VDVRRespondID.lower_wiper_sensitivity_by_number_3)
				}
			} else {
				sendResultCode(VDVRRespondID.lower_wiper_sensitivity_by_number_2)
			}
		}
	}

	/**
	 * 雨刮灵敏度调到指定数值
	 * @param value
	 */
	fun setWiperSens(value: String) {
		val status = mCarSettingManager.getWiperSensitivity()
		Log.d(TAG, "WiperSensMode.status:" + status)
		//判断雨刮灵敏度是否已为要求数值
		if (value.toInt() == status) {
			sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_number_1, value)
		} else if (value.toInt() >= CarSettingConstant.WIPER_SENS_MODE_1 && value.toInt() <= CarSettingConstant.WIPER_SENS_MODE_4) {
			mCarSettingManager.setWiperSensitivity(value.toInt())
			sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_number_2, value)
		} else {
			sendResultCode(VDVRRespondID.adjust_wiper_sensitivity_to_number_3)
		}
	}

	/**
	 * 方向盘自定义按键设置为具体功能
	 */
	fun setCustomButton(state: Int) {
		// 设置自定义按键类型
		// 1 车辆设置, 2 DVR, 3 HUD, 4 方向盘调节HUD, 5 后视镜调节, 6 音源切换, 7 返回, 8 AVM
		Prefs.put(PrefsConst.C_CUSTOM_BUTTON, state)
		var customStatus = 1
		if (state == 0) {
			// 行车记录仪抓拍
			customStatus = 2
		} else if (state == 1) {
			// AVM进入/退出
			customStatus = 8
		} else if (state == 2) {
			// 方向盘调节HUD
			customStatus = 4
		} else if (state == 3) {
			// 后视镜调节
			customStatus = 5
		} else if (state == 4) {
			// 音源切换
			customStatus = 6
		} else if (state == 5) {
			// 方向盘调节
			// todo 暂无对应按键
//            customStatus = 3;
		} else if (state == 6) {
			// 全息影像
			// todo 暂无对应按键
//            customStatus = 1;
		}
		Log.d(TAG, "setCustomButton: 设置自定义按键类型: $state === $customStatus")
		Settings.System.putInt(context.contentResolver, "custom_input_event_1", customStatus)
	}

	fun getCustomButton(): Int {
		val status = Prefs.get(PrefsConst.C_CUSTOM_BUTTON, PrefsConst.DefaultValue.C_CUSTOM_BUTTON)
		Log.d(TAG, "getCustomButton: 获取自定义按键类型 $status")
		return status
	}

	/**
	 * 方向盘自定义按键设置为具体功能
	 * @param value
	 */
	fun setCustomButtons(value: String) {
		val status = getCustomButton()
		when (value.toInt()) {
			//后视镜调节
			VehicleConstant.MIRROR_ADJUSTMENT -> {
				if (status == CarSettingConstant.MIRROR_ADJUSTMENT) {
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_2, "后视镜调节")
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.MIRROR_ADJUSTMENT)
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.QUICK_CONTROL,
						QuickControlFragment.REAR_MIRROR_UI_ALERT,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_1, "后视镜调节")
				} else {
					setCustomButton(CarSettingConstant.MIRROR_ADJUSTMENT)
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.QUICK_CONTROL,
						QuickControlFragment.REAR_MIRROR_UI_ALERT,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_4, "后视镜调节")
				}
			}
			//HUD调节
			VehicleConstant.HUD -> {
				if (status == CarSettingConstant.HUD) {
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_2, "HUD调节")
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.HUD)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_1, "HUD调节")
				} else {
					setCustomButton(CarSettingConstant.HUD)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_4, "HUD调节")
				}
			}
			//方向盘调节
			VehicleConstant.STEERING_WHEEL_ADJUSTMENT -> {
				if (status == CarSettingConstant.STEERING_WHEEL_ADJUSTMENT) {
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_2, "方向盘调节")
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.STEERING_WHEEL_ADJUSTMENT)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_1, "方向盘调节")
				} else {
					setCustomButton(CarSettingConstant.STEERING_WHEEL_ADJUSTMENT)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_4, "方向盘调节")
				}
			}
			//AVM
			VehicleConstant.AVM -> {
				if (status == CarSettingConstant.AVM) {
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_2, "AVM")
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.AVM)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_1, "AVM")
				} else {
					setCustomButton(CarSettingConstant.AVM)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_4, "AVM")
				}
			}
			//行车记录仪抓拍
			VehicleConstant.DASH_CAM -> {
				if (status == CarSettingConstant.DASH_CAM) {
					Log.d(TAG, "DASH_CAM");
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_2,
						"行车记录仪抓怕"
					)
				} else {
					setCustomButton(CarSettingConstant.DASH_CAM)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_4,
						"行车记录仪抓怕"
					)
				}
			}
			//全景影像
			VehicleConstant.PANORAMIC_IMAGERY -> {
				if (status == CarSettingConstant.PANORAMIC_IMAGERY) {
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_2, "全景影像")
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.PANORAMIC_IMAGERY)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_1, "全景影像")
				} else {
					setCustomButton(CarSettingConstant.PANORAMIC_IMAGERY)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_4, "全景影像")
				}
			}
			//音源切换
			VehicleConstant.SWITCHING_OF_AUDIO_SOURCES -> {
				if (status == CarSettingConstant.SWITCHING_OF_AUDIO_SOURCES) {
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_2, "音源切换")
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.SWITCHING_OF_AUDIO_SOURCES)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_1, "音源切换")
				} else {
					setCustomButton(CarSettingConstant.SWITCHING_OF_AUDIO_SOURCES)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_4, "音源切换")
				}
			}
			else -> {
				Log.d(TAG, "setCustomButtons: 获取自定义按键类型 $status");
				sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_3)
			}
		}
	}

	/**
	 * 方向盘按键短按自定义设置
	 * @param value
	 */
	fun setCustomButtonShortPress(value: String) {
		val status = getCustomButton()
		when (value.toInt()) {
			//后视镜调节
			VehicleConstant.MIRROR_ADJUSTMENT -> {
				if (status == CarSettingConstant.MIRROR_ADJUSTMENT) {
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_3,
						"后视镜调节"
					)
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.MIRROR_ADJUSTMENT)
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.QUICK_CONTROL,
						QuickControlFragment.REAR_MIRROR_UI_ALERT,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"后视镜调节"
					)
				} else {
					setCustomButton(CarSettingConstant.MIRROR_ADJUSTMENT)
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.QUICK_CONTROL,
						QuickControlFragment.REAR_MIRROR_UI_ALERT,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"后视镜调节"
					)
				}
			}
			//HUD调节
			VehicleConstant.HUD -> {
				if (status == CarSettingConstant.HUD) {
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_3,
						"HUD调节"
					)
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.HUD)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"HUD调节"
					)
				} else {
					setCustomButton(CarSettingConstant.HUD)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"HUD调节"
					)
				}
			}
			//方向盘调节
			VehicleConstant.STEERING_WHEEL_ADJUSTMENT -> {
				if (status == CarSettingConstant.STEERING_WHEEL_ADJUSTMENT) {
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_3,
						"方向盘调节"
					)
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.STEERING_WHEEL_ADJUSTMENT)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"方向盘调节"
					)
				} else {
					setCustomButton(CarSettingConstant.STEERING_WHEEL_ADJUSTMENT)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"方向盘调节"
					)
				}
			}
			//AVM
			VehicleConstant.AVM -> {
				if (status == CarSettingConstant.AVM) {
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_short_3, "AVM")
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.AVM)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_short_1, "AVM")
				} else {
					setCustomButton(CarSettingConstant.AVM)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_short_1, "AVM")
				}
			}
			//行车记录仪抓拍
			VehicleConstant.DASH_CAM -> {
				if (status == CarSettingConstant.DASH_CAM) {
					Log.d(TAG, "DASH_CAM");
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_3,
						"行车记录仪抓怕"
					)
				} else {
					setCustomButton(CarSettingConstant.DASH_CAM)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"行车记录仪抓怕"
					)
				}
			}
			//全景影像
			VehicleConstant.PANORAMIC_IMAGERY -> {
				if (status == CarSettingConstant.PANORAMIC_IMAGERY) {
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_3,
						"全景影像"
					)
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.PANORAMIC_IMAGERY)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"全景影像"
					)
				} else {
					setCustomButton(CarSettingConstant.PANORAMIC_IMAGERY)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"全景影像"
					)
				}
			}
			//音源切换
			VehicleConstant.SWITCHING_OF_AUDIO_SOURCES -> {
				if (status == CarSettingConstant.SWITCHING_OF_AUDIO_SOURCES) {
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_3,
						"音源切换"
					)
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.SWITCHING_OF_AUDIO_SOURCES)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"音源切换"
					)
				} else {
					setCustomButton(CarSettingConstant.SWITCHING_OF_AUDIO_SOURCES)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_short_1,
						"音源切换"
					)
				}
			}

			else -> {
				sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_short_4, value)
			}
		}
	}

	/**
	 * 方向盘按键长按自定义设置
	 * @param value
	 */
	fun setCustomButtonLongPress(value: String) {
		val status = getCustomButton()
		when (value.toInt()) {
			//行车记录仪抓拍
			VehicleConstant.DASH_CAM_LONG_PRESS -> {
				if (status == CarSettingConstant.DASH_CAM) {
					Log.d(TAG, "DASH_CAM");
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_long_3,
						"行车记录仪抓怕"
					)
				} else {
					setCustomButton(CarSettingConstant.DASH_CAM)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_long_1,
						"行车记录仪抓怕"
					)
				}
			}
			//后视镜调节
			VehicleConstant.MIRROR_ADJUSTMENT_LONG_PRESS -> {
				if (status == CarSettingConstant.MIRROR_ADJUSTMENT) {
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_long_3,
						"后视镜调节"
					)
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.MIRROR_ADJUSTMENT)
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.QUICK_CONTROL,
						QuickControlFragment.REAR_MIRROR_UI_ALERT,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_long_1,
						"后视镜调节"
					)
				} else {
					setCustomButton(CarSettingConstant.MIRROR_ADJUSTMENT)
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.QUICK_CONTROL,
						QuickControlFragment.REAR_MIRROR_UI_ALERT,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(
						VDVRRespondID.set_steering_wheel_custom_button_long_1,
						"后视镜调节"
					)
				}
			}
			//AVM
			VehicleConstant.AVM_LONG_PRESS -> {
				if (status == CarSettingConstant.AVM) {
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_long_3, "AVM")
				} else if (status == CarSettingConstant.DASH_CAM) {
					setCustomButton(CarSettingConstant.AVM)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_long_1, "AVM")
				} else {
					setCustomButton(CarSettingConstant.AVM)
					sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_long_1, "AVM")
				}
			}

			else -> {
				sendResultCode(VDVRRespondID.set_steering_wheel_custom_button_long_4, value)
			}
		}
	}

	/**
	 * 打开安全气囊
	 * @param flag true:打开
	 */
    fun setAirBag(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_airbag_1)
        } else {
			Log.d(TAG, "无法关闭安全气囊")
		}
	}

	/**
	 * 后视镜往指定方向调节一点
	 * @param value
	 */
	fun setMirrorAdjustment(value: String) {
		//获取左侧外后视镜上下调节
		val status1 = mQuickManager.getLeftRearMirrorUpDown()
		//获取左侧外后视镜左右调节
		val status2 = mQuickManager.getLeftRearMirrorLeftRight()
		//获取右侧外后视镜上下调节
		val status3 = mQuickManager.getRightRearMirrorUpDown()
		//获取右侧外后视镜左右调节
		val status4 = mQuickManager.getRightRearMirrorLeftRight()
		val index = value.indexOf("-")
		val result = value.substring(0, index)
		//向上调节的极值1，向下调节的极值100；向左调节的极值1，向右调节的极值100
		when (value) {
			//两侧后视镜向上调节一点
			VehicleConstant.ALL_UP -> {
				if (status1 == CarSettingConstant.MIN && status3 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "上")
				} else if (status1 != CarSettingConstant.MIN && status3 == CarSettingConstant.MIN) {
					//左侧后视镜向上调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorUpDown(status1 - 10)
					if (status1 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "上")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				} else if (status1 == CarSettingConstant.MIN && status3 != CarSettingConstant.MIN) {
					//右侧后视镜向上调节一点，调节步进10%
					mQuickManager.setRightRearMirrorUpDown(status3 - 10)
					if (status3 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "上")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				} else {
					//左右后视镜向上调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorUpDown(status1 - 10)
					mQuickManager.setRightRearMirrorUpDown(status3 - 10)
					if (status1 - 10 == CarSettingConstant.MIN && status3 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "上")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//两侧后视镜向下调节一点
			VehicleConstant.ALL_LOW -> {
				if (status1 == CarSettingConstant.MAX && status3 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "下")
				} else if (status1 != CarSettingConstant.MAX && status3 == CarSettingConstant.MAX) {
					//左侧后视镜向下调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorUpDown(status1 + 10)
					if (status1 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "下")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				} else if (status1 == CarSettingConstant.MAX && status3 != CarSettingConstant.MAX) {
					//右侧后视镜向下调节一点，调节步进10%
					mQuickManager.setRightRearMirrorUpDown(status3 + 10)
					if (status3 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "下")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				} else {
					//左右后视镜向下调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorUpDown(status1 + 10)
					mQuickManager.setRightRearMirrorUpDown(status3 + 10)
					if (status1 + 10 == CarSettingConstant.MAX && status3 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "下")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//两侧后视镜向左调节一点
			VehicleConstant.ALL_LEFT -> {
				if (status2 == CarSettingConstant.MIN && status4 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "左")
				} else if (status2 != CarSettingConstant.MIN && status4 == CarSettingConstant.MIN) {
					//左后视镜向左调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorLeftRight(status2 - 10)
					if (status2 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "左")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				} else if (status2 == CarSettingConstant.MIN && status4 != CarSettingConstant.MIN) {
					//右后视镜向左调节一点，调节步进10%
					mQuickManager.setRightRearMirrorLeftRight(status4 - 10)
					if (status4 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "左")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				} else {
					//左右后视镜向左调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorLeftRight(status2 - 10)
					mQuickManager.setRightRearMirrorLeftRight(status4 - 10)
					if (status2 - 10 == CarSettingConstant.MIN && status4 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "左")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//两侧后视镜向右调节一点
			VehicleConstant.ALL_RIGHT -> {
				if (status2 == CarSettingConstant.MAX && status4 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "右")
				} else if (status2 != CarSettingConstant.MAX && status4 == CarSettingConstant.MAX) {
					//左后视镜向右调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorLeftRight(status2 + 10)
					if (status2 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "右")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				} else if (status2 == CarSettingConstant.MAX && status4 != CarSettingConstant.MAX) {
					//右后视镜向右调节一点，调节步进10%
					mQuickManager.setRightRearMirrorLeftRight(status4 + 10)
					if (status4 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "右")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				} else {
					//左右后视镜向右调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorLeftRight(status2 + 10)
					mQuickManager.setRightRearMirrorLeftRight(status4 + 10)
					if (status2 + 10 == CarSettingConstant.MAX && status4 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "右")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//两侧后视镜向前调节一点
			VehicleConstant.ALL_FRONT -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_9)
			}
			//两侧后视镜向后调节一点
			VehicleConstant.ALL_BACK -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_9)
			}
			//左后视镜向上调节一点
			VehicleConstant.LEFT_UP -> {
				if (status1 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "上")
				} else {
					//左后视镜向上调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorUpDown(status1 - 10)
					if (status1 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "上")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//左后视镜向下调节一点
			VehicleConstant.LEFT_LOW -> {
				if (status1 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "下")
				} else {
					//左后视镜向下调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorUpDown(status1 + 10)
					if (status1 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "下")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}

				}
			}
			//左后视镜向左调节一点
			VehicleConstant.LEFT_LEFT -> {
				if (status2 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "左")
				} else {
					//左后视镜向左调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorLeftRight(status2 - 10)
					if (status2 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "左")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//左后视镜向右调节一点
			VehicleConstant.LEFT_RIGHT -> {
				if (status2 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "右")
				} else {
					//左后视镜向右调节一点，调节步进10%
					mQuickManager.setLeftRearMirrorLeftRight(status2 + 10)
					if (status2 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "右")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//左后视镜向前调节一点
			VehicleConstant.LEFT_FRONT -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_9)
			}
			//左后视镜向右调节一点
			VehicleConstant.LEFT_BACK -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_9)
			}
			//右后视镜向上调节一点
			VehicleConstant.RIGHT_UP -> {
				if (status3 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "上")
				} else {
					//右后视镜向上调节一点，调节步进10%
					mQuickManager.setRightRearMirrorUpDown(status1 - 10)
					if (status3 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "上")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//右后视镜向下调节一点
			VehicleConstant.RIGHT_LOW -> {
				if (status3 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "下")
				} else {
					//右后视镜向下调节一点，调节步进10%
					mQuickManager.setRightRearMirrorUpDown(status3 + 10)
					if (status3 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "下")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//右后视镜向左调节一点
			VehicleConstant.RIGHT_LEFT -> {
				if (status4 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "左")
				} else {
					//右后视镜向左调节一点，调节步进10%
					mQuickManager.setRightRearMirrorLeftRight(status4 - 10)
					if (status4 - 10 == CarSettingConstant.MIN) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "左")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//右后视镜向右调节一点
			VehicleConstant.RIGHT_RIGHT -> {
				if (status4 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_5, "右")
				} else {
					//右后视镜向右调节一点，调节步进10%
					mQuickManager.setRightRearMirrorLeftRight(status4 + 10)
					if (status4 + 10 == CarSettingConstant.MAX) {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_3, "右")
					} else {
						sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_4)
					}
				}
			}
			//右后视镜向前调节一点
			VehicleConstant.RIGHT_FRONT -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_9)
			}
			//右后视镜向后调节一点
			VehicleConstant.RIGHT_BACK -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_9)
			}

			else -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_8, result)
			}
		}
	}

	/**
	 * 设置后视镜方向调到指定方向极值
	 * @param value
	 */
	fun setMirrorAdjustmentMost(value: String) {
		//获取左侧外后视镜上下调节
		val status1 = mQuickManager.getLeftRearMirrorUpDown()
		//获取左侧外后视镜左右调节
		val status2 = mQuickManager.getLeftRearMirrorLeftRight()
		//获取右侧外后视镜上下调节
		val status3 = mQuickManager.getRightRearMirrorUpDown()
		//获取右侧外后视镜左右调节
		val status4 = mQuickManager.getRightRearMirrorLeftRight()
		val index = value.indexOf("-")
		val result = value.substring(0, index)
		//向上调节的极值1，向下调节的极值100；向左调节的极值1，向右调节的极值100
		when (value) {
			//两侧后视镜向上调节到极值
			VehicleConstant.ALL_UP -> {
				if (status1 == CarSettingConstant.MIN && status3 == CarSettingConstant.MIN) {
					sendResultCode(
						VDVRRespondID.adjust_rearview_mirror_direction_6,
						"两侧后视镜-上"
					)
				} else if (status1 != CarSettingConstant.MIN && status3 == CarSettingConstant.MIN) {
					mQuickManager.setLeftRearMirrorUpDown(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				} else if (status1 == CarSettingConstant.MIN && status3 != CarSettingConstant.MIN) {
					mQuickManager.setRightRearMirrorUpDown(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				} else {
					mQuickManager.setLeftRearMirrorUpDown(CarSettingConstant.MIN)
					mQuickManager.setRightRearMirrorUpDown(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//两侧后视镜向下调节到极值
			VehicleConstant.ALL_LOW -> {
				if (status1 == CarSettingConstant.MAX && status3 == CarSettingConstant.MAX) {
					sendResultCode(
						VDVRRespondID.adjust_rearview_mirror_direction_6,
						"两侧后视镜-下"
					)
				} else if (status1 != CarSettingConstant.MAX && status3 == CarSettingConstant.MAX) {
					mQuickManager.setLeftRearMirrorUpDown(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				} else if (status1 == CarSettingConstant.MAX && status3 != CarSettingConstant.MAX) {
					mQuickManager.setRightRearMirrorUpDown(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				} else {
					mQuickManager.setLeftRearMirrorUpDown(CarSettingConstant.MAX)
					mQuickManager.setRightRearMirrorUpDown(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//两侧后视镜向左调节到极值
			VehicleConstant.ALL_LEFT -> {
				if (status2 == CarSettingConstant.MIN && status4 == CarSettingConstant.MIN) {
					sendResultCode(
						VDVRRespondID.adjust_rearview_mirror_direction_6,
						"两侧后视镜-左"
					)
				} else if (status2 != CarSettingConstant.MIN && status4 == CarSettingConstant.MIN) {
					mQuickManager.setLeftRearMirrorLeftRight(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				} else if (status2 == CarSettingConstant.MIN && status4 != CarSettingConstant.MIN) {
					mQuickManager.setRightRearMirrorLeftRight(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				} else {
					mQuickManager.setLeftRearMirrorLeftRight(CarSettingConstant.MIN)
					mQuickManager.setRightRearMirrorLeftRight(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//两侧后视镜向右调节到极值
			VehicleConstant.ALL_RIGHT -> {
				if (status2 == CarSettingConstant.MAX && status4 == CarSettingConstant.MAX) {
					sendResultCode(
						VDVRRespondID.adjust_rearview_mirror_direction_6,
						"两侧后视镜-右"
					)
				} else if (status2 != CarSettingConstant.MAX && status4 == CarSettingConstant.MAX) {
					mQuickManager.setLeftRearMirrorLeftRight(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				} else if (status2 == CarSettingConstant.MAX && status4 != CarSettingConstant.MAX) {
					mQuickManager.setRightRearMirrorLeftRight(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				} else {
					mQuickManager.setLeftRearMirrorLeftRight(CarSettingConstant.MAX)
					mQuickManager.setRightRearMirrorLeftRight(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//两侧后视镜向前调节到极值
			VehicleConstant.ALL_FRONT -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_11)
			}
			//两侧后视镜向后调节到极值
			VehicleConstant.ALL_BACK -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_11)
			}
			//左后视镜向上调节到极值
			VehicleConstant.LEFT_UP -> {
				if (status1 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_6, "左后视镜-上")
				} else {
					mQuickManager.setLeftRearMirrorUpDown(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//左后视镜向下调节到极值
			VehicleConstant.LEFT_LOW -> {
				if (status1 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_6, "左后视镜-下")
				} else {
					mQuickManager.setLeftRearMirrorUpDown(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//左后视镜向左调节到极值
			VehicleConstant.LEFT_LEFT -> {
				if (status2 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_6, "左后视镜-左")
				} else {
					mQuickManager.setLeftRearMirrorLeftRight(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//左后视镜向右调节到极值
			VehicleConstant.LEFT_RIGHT -> {
				if (status2 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_6, "左后视镜-右")
				} else {
					mQuickManager.setLeftRearMirrorLeftRight(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//左后视镜向前调节到极值
			VehicleConstant.LEFT_FRONT -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_11)
			}
			//左后视镜向后调节到极值
			VehicleConstant.LEFT_BACK -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_11)
			}
			//右后视镜向上调节到极值
			VehicleConstant.RIGHT_UP -> {
				if (status3 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_6, "右后视镜-上")
				} else {
					mQuickManager.setRightRearMirrorUpDown(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//右后视镜向下调节到极值
			VehicleConstant.RIGHT_LOW -> {
				if (status3 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_6, "右后视镜-下")
				} else {
					mQuickManager.setRightRearMirrorUpDown(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//右后视镜向左调节到极值
			VehicleConstant.RIGHT_LEFT -> {
				if (status4 == CarSettingConstant.MIN) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_6, "右后视镜-左")
				} else {
					mQuickManager.setRightRearMirrorLeftRight(CarSettingConstant.MIN)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//右后视镜向右调节到极值
			VehicleConstant.RIGHT_RIGHT -> {
				if (status4 == CarSettingConstant.MAX) {
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_6, "右后视镜-右")
				} else {
					mQuickManager.setRightRearMirrorLeftRight(CarSettingConstant.MAX)
					sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_7)
				}
			}
			//右后视镜向前调节到极值
			VehicleConstant.RIGHT_FRONT -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_11)
			}
			//右后视镜向后调节到极值
			VehicleConstant.RIGHT_BACK -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_11)
			}

			else -> {
				sendResultCode(VDVRRespondID.adjust_rearview_mirror_direction_8, result)
			}
		}
    }

	/**
	 * 流媒体后视镜亮度调节
	 * @param value
	 */
    fun setMirrorBrightness(value: String) {
        when (value.toInt()) {
			//流媒体后视镜亮度调高一点
			VehicleConstant.UP_LITTLE -> {
				DialogNavigationUtils.launchMainActivity(
					context,
					MainActivity.MainTabIndex.QUICK_CONTROL,
					QuickControlFragment.REAR_MIRROR_UI_ALERT,
					CommonConst.DIALOG_OPEN
				)
				sendResultCode("adjust_streaming_rearview_mirror_direction_1")
            }
			//流媒体后视镜亮度调低一点
			VehicleConstant.LOW_LITTLE -> {
				DialogNavigationUtils.launchMainActivity(
					context,
					MainActivity.MainTabIndex.QUICK_CONTROL,
					QuickControlFragment.REAR_MIRROR_UI_ALERT,
					CommonConst.DIALOG_OPEN
				)
				sendResultCode("adjust_streaming_rearview_mirror_direction_2")
            }
			//流媒体后视镜亮度调到高中低挡
			VehicleConstant.HIGH_LEVEL, VehicleConstant.MID_LEVEL, VehicleConstant.LOW_LEVEL -> {
				DialogNavigationUtils.launchMainActivity(
					context,
					MainActivity.MainTabIndex.QUICK_CONTROL,
					QuickControlFragment.REAR_MIRROR_UI_ALERT,
					CommonConst.DIALOG_OPEN
				)
                sendResultCode(VDVRRespondID.adjus_stream_rearview_mirror_brightness_to_gear_1)
            }
			//流媒体后视镜亮度调到最大
			VehicleConstant.HIGHEST_LEVEL -> {
				DialogNavigationUtils.launchMainActivity(
					context,
					MainActivity.MainTabIndex.QUICK_CONTROL,
					QuickControlFragment.REAR_MIRROR_UI_ALERT,
					CommonConst.DIALOG_OPEN
				)
                sendResultCode(VDVRRespondID.adjus_stream_rearview_mirror_brightness_to_max_1)
            }
			//流媒体后视镜亮度调到最小
			VehicleConstant.MINIMUM_LEVEL -> {
				DialogNavigationUtils.launchMainActivity(
					context,
					MainActivity.MainTabIndex.QUICK_CONTROL,
					QuickControlFragment.REAR_MIRROR_UI_ALERT,
					CommonConst.DIALOG_OPEN
				)
                sendResultCode(VDVRRespondID.adjus_stream_rearview_mirror_brightness_to_min_1)
            }

			else -> {
				Log.d(TAG, "不支持此调节")
			}
		}
	}

	/**
	 * 打开or关闭喷嘴加热
	 * @param flag true:打开  false:关闭
	 */
    fun setNozzleHeating(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_nozzle_heat_1)
        } else {
            sendResultCode(VDVRRespondID.close_nozzle_heat_1)
        }
    }

	/**
	 * 设置喷嘴加热模式
	 */
	fun setNozzleHeatingMode() {
		sendResultCode(VDVRRespondID.set_nozzle_heat_mode_1)
	}

	/**
	 * 打开or关闭雨刮器
	 * @param flag true:打开  false:关闭
	 */
    fun setWiper(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_wiper_1)
        } else {
            sendResultCode(VDVRRespondID.close_wiper_1)
        }
    }

	/**
	 * 雨刮器调高具体数值
	 */
    fun setWiperUp() {
        sendResultCode(VDVRRespondID.raise_wiper_speed_by_number_1)
    }

	/**
	 * 雨刮器调低具体数值
	 */
    fun setWiperDown() {
        sendResultCode(VDVRRespondID.lower_wiper_speed_by_number_1)
    }

	/**
	 * 雨刮器挡位调高一点/低一点/高/低/中/最大/最小
	 * @param value
	 */
    fun setWiperMode(value: String) {
        when (value.toInt()) {
			//雨刮器挡位调高一点
			VehicleConstant.UP_LITTLE -> {
                sendResultCode(VDVRRespondID.raise_wiper_speed_little_1)
            }
			//雨刮器挡位调低一点
			VehicleConstant.LOW_LITTLE -> {
                sendResultCode(VDVRRespondID.lower_wiper_speed_little_1)
            }
			//雨刮器挡位调到高中低档
			VehicleConstant.HIGH_LEVEL, VehicleConstant.MID_LEVEL, VehicleConstant.LOW_LEVEL -> {
                sendResultCode(VDVRRespondID.adjust_wiper_speed_to_gear_1)
            }
			//雨刮器挡位调到最高档
			VehicleConstant.HIGHEST_LEVEL -> {
                sendResultCode(VDVRRespondID.adjust_wiper_speed_to_max_1)
            }
			//雨刮器挡位调到最低档
			VehicleConstant.MINIMUM_LEVEL -> {
                sendResultCode(VDVRRespondID.adjust_wiper_speed_to_min_1)
            }
        }
    }

	/**
	 * 雨刮器挡位调到具体数值
	 */
    fun setAdjustWiperGear() {
        sendResultCode(VDVRRespondID.adjust_wiper_speed_to_number_1)
    }

	/**
	 * 打开or关闭手套箱
	 * @param flag true:打开  false:关闭
	 */
    fun GloveBox(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_glove_compartment_5)
        } else {
            sendResultCode(VDVRRespondID.close_glove_compartment_1)
        }
    }

	/**
	 * 打开or关闭手套箱密码
	 * @param flag true:打开  false:关闭
	 */
    fun GloveBoxPassword(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_glove_compartment_password_2)
        } else {
            sendResultCode(VDVRRespondID.close_glove_compartment_password_2)
        }
    }

	/**
	 * 设置手套箱密码
	 */
    fun setGloveBoxPassword() {
        sendResultCode(VDVRRespondID.set_glove_compartment_password_2)
    }

	/**
	 * 打开or关闭方向盘位置
	 * @param flag true:打开  false:关闭
	 */
    fun setSteeringWheelPosition(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_steering_wheel_position_2)
        } else {
            sendResultCode(VDVRRespondID.close_steering_wheel_position_1)
        }
    }

	/**
	 * 打开or关闭方向盘未回正提醒
	 * @param flag true:打开  false:关闭
	 */
    fun setSteeringWheelRemind(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_steering_wheel_unaligned_remind_1)
        } else {
            sendResultCode(VDVRRespondID.close_steering_wheel_unaligned_remind_1)
        }
    }

	/**
	 * 方向盘向指定方向调节
	 */
    fun setSteeringWheelAdjust() {
        sendResultCode(VDVRRespondID.adjust_steering_wheel_direction_1)
    }

	/**
	 * 方向盘向指定方向调节到极值
	 */
    fun setSteeringWheelAdjustMost() {
        sendResultCode(VDVRRespondID.adjust_steering_wheel_direction_to_extremum_1)
    }

	/**
	 * 保存方向盘位置
	 */
    fun saveSteeringWheelPosition() {
        sendResultCode(VDVRRespondID.save_steering_wheel_position_2)
    }

	/**
	 * 打开方向盘自动回位
	 * @param flag true:打开  false:关闭
	 */
	fun setSteeringWheelAutoPosition(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_steering_wheel_auto_return_1)
		} else {
			sendResultCode(VDVRRespondID.close_steering_wheel_auto_return_1)
		}
	}

	/**
	 * 设置方向盘加热具体模式
	 */
	fun setSteeringWheelHeatingMode() {
		sendResultCode(VDVRRespondID.steering_wheel_heating_mode_1)
	}

	/**
	 * 打开or关闭方向盘通风
	 * @param flag true:打开  false:关闭
	 */
	fun setSteeringWheelVentilate(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_steering_wheel_ventilation_1)
		} else {
			sendResultCode(VDVRRespondID.close_steering_wheel_ventilation_1)
		}
	}

	/**
	 * 打开or关闭方向盘调节HUD
	 * @param flag true:打开  false:关闭
	 */
	fun setWheelAdjustHUD(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_steering_wheel_adjust_hud_3)
		} else {
			sendResultCode(VDVRRespondID.close_steering_wheel_adjust_hud_3)
		}
	}

	/**
	 * 打开/关闭主驾安全带舒适
	 */
	fun setComfortBuckle() {
		sendResultCode(VDVRRespondID.open_pretensioner_seat_belt_3)
	}

	/**
	 * 打开or关闭主驾安全带震动提醒
	 * @param flag true:打开  false:关闭
	 */
	fun setDriverBuckleVibration(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_fastener_reminder_3)
		} else {
			sendResultCode(VDVRRespondID.close_fastener_reminder_3)
		}
	}

	/**
	 * 打开or关闭安全带加热
	 * @param flag true:打开  false:关闭
	 */
	fun setBuckleHeating(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.safe_belt_heat_1)
		} else {
			sendResultCode(VDVRRespondID.safe_belt_heat_3)
		}
	}

	/**
	 * 安全带加热温度设置
	 */
	fun setSeatHeating() {
		sendResultCode(VDVRRespondID.safe_belt_heat_5)
	}

	/**
	 * 切换hud显示模式，无指定值
	 */
	fun changeHUDDisplay() {
		sendResultCode(VDVRRespondID.switch_hud_display_mode_1)
	}

	/**
	 * 打开hud显示具体内容
	 */
	fun setHUDShowDriverMode() {
		sendResultCode(VDVRRespondID.open_hud_display_content_1)
	}

	/**
	 * 关闭hud显示具体内容
	 */
	fun setHUDMissDriverMode() {
		sendResultCode(VDVRRespondID.close_hud_display_content_1)
	}

	/**
	 * 切换hud显示具体内容，无指定值
	 */
	fun changeHUDContent() {
		sendResultCode(VDVRRespondID.switch_hud_display_content_1)
	}

	/**
	 * 打开HUD雪地模式关联驾驶模式
	 * @param flag true:打开  false:关闭
	 */
	fun setHUDSnowModeWithDrive(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_hud_snow_mode_and_associate_with_driving_mode_4)
		} else {
			sendResultCode(VDVRRespondID.close_hud_snow_mode_and_associate_with_driving_mode_4)
		}
	}

	/**
	 * 打开or关闭HUD雪地模式
	 * @param flag true:打开  false:关闭
	 */
	fun setHUDSnowMode(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_hud_snow_mode_3)
		} else {
			sendResultCode(VDVRRespondID.close_hud_snow_mode_3)
		}
	}

	/**
	 * 油箱盖解锁
	 * @param flag true:打开  false:关闭
	 */
	fun setOilTankUnlock(flag: Boolean) {
		val status = mQuickManager.getOilLidSwitch()
		//0x0:上锁 0x1:解锁
		if (flag) {
			if (status == CarSettingConstant.LOCKED) {
				mQuickManager.setOilLidSwitch(CarSettingConstant.UNLOCK)
				sendResultCode(VDVRRespondID.Unlocking_charging_port_tank_cap_3)
			} else {
				sendResultCode(VDVRRespondID.Unlocking_charging_port_tank_cap_2)
			}
		} else {
			Log.d(TAG, "不支持上锁操作")
		}
	}


	/**
	 * 开关后尾门
	 * @param flag true:打开 false:关闭
	 */

	fun setAutoTail(flag: Boolean) {
		val plg_value = mConfigManager?.getPLGConfig()
		if (plg_value == CarSettingConstant.TAIL_GATE_CONFIG_N) {
			sendResultCode(VDVRRespondID.open_tailgate_5)
		} else {
			val signal = mQuickManager?.getRearTailGate()
			val speed = mQuickManager.getCarSpeed()
			val status = mDrivingManager.getFLZCU_CleanModeStatus()//0X1 0FF 0X2 ON
			if (speed == CarSettingConstant.SPEED_0) {
				if (signal != null) {
					if (flag) {
						if (status == CarSettingConstant.WASH_CAR_MODE_ON) {
							sendResultCode(VDVRRespondID.open_tailgate_6)
						} else if (signal == CarSettingConstant.TailGate_OPEN) {
							sendResultCode(VDVRRespondID.open_tailgate_2)
						} else {
							mQuickManager.rearTailGate = CarSettingConstant.SET_TailGate_ON
							sendResultCode(VDVRRespondID.open_tailgate_3)
						}
					} else {
						if (signal == CarSettingConstant.TailGate_OPEN) {
							mQuickManager.rearTailGate = CarSettingConstant.SET_TailGate_OFF
							sendResultCode(VDVRRespondID.close_tailgate_2)
						} else {
							sendResultCode(VDVRRespondID.close_tailgate_3)
						}
					}
				}
			} else {
				if (flag) {
					sendResultCode(VDVRRespondID.open_tailgate_4)
				} else {
					sendResultCode(VDVRRespondID.close_tailgate_4)
				}
			}
		}
	}

	/**
	 * 设置尾门开启上限高度百分比
	 * @param value 50-95
	 */

	fun setTailHeight(value: String) {
		val single = mQuickManager?.getHudRoate()
		if (value.toInt() > CarSettingConstant.TAIL_DOOR_MAX) {
			mQuickManager.hudRoate = CarSettingConstant.TAIL_DOOR_MAX
			sendResultCode(VDVRRespondID.set_tailgate_opening_upper_limit_4, "95%")
		} else if (value.toInt() < CarSettingConstant.TAIL_DOOR_MIN) {
			mQuickManager.hudRoate = CarSettingConstant.TAIL_DOOR_MIN
			sendResultCode(VDVRRespondID.set_tailgate_opening_upper_limit_5, "50%")
		} else {
			if (value.toInt() == single) {
				sendResultCode(VDVRRespondID.set_tailgate_opening_upper_limit_3, value + "%")
			} else {
				mQuickManager.hudRoate = value.toInt()
				sendResultCode(VDVRRespondID.set_tailgate_opening_upper_limit_2, value + "%")
			}
		}
	}

	/**
	 * 打开油箱盖
	 */
	fun setFuelTankCap(flag: Boolean) {
		sendResultCode(VDVRRespondID.close_fuel_tank_cap_1)
	}

	/**
	 * 外后视镜自动折叠开关
	 * @param flag true:打开 false:关闭
	 */
	fun setAutoRearviewMirrorsFolded(flag: Boolean) {
		val status = mRearviewManager.getAutoFoldSts()
		if (flag) {
			if (status == CarSettingConstant.MIRROR_FOLD_CLOSE) {
				mRearviewManager.autoFoldSts = CarSettingConstant.MIRROR_FOLD_OPEN
				sendResultCode(VDVRRespondID.open_rearview_mirror_mode_2)
			} else {
				sendResultCode(VDVRRespondID.open_rearview_mirror_mode_3)
			}
		} else {
			if (status == CarSettingConstant.MIRROR_FOLD_CLOSE) {
				sendResultCode(VDVRRespondID.close_rearview_mirror_mode_2)
			} else {
				mRearviewManager.autoFoldSts = CarSettingConstant.MIRROR_FOLD_CLOSE
				sendResultCode(VDVRRespondID.close_rearview_mirror_mode_3)
			}
		}
	}

	/**
	 * 后视镜加热
	 * @param flag true:打开 false:关闭
	 */

	fun setHeatedMirrors(flag: Boolean) {
		val heat_value = mConfigManager?.getMirrorHeatingConfig()
		val status = mRearviewManager?.autoHeatingFb
		if (heat_value == CarSettingConstant.MIRROR_HEAT_CONFIG_N) {
			sendResultCode(VDVRRespondID.open_rearview_mirror_mode_6)
		} else {
			if (status != null) {
				if (flag) {
					if (status == CarSettingConstant.MIRROR_HOT_CLOSE) {
						mRearviewManager.setAutoHeatingset(CarSettingConstant.MIRROR_HOT_OPEN)
						sendResultCode(VDVRRespondID.open_rearview_mirror_mode_5)
					} else {
						sendResultCode(VDVRRespondID.open_rearview_mirror_mode_4)
					}
				} else {
					if (status == CarSettingConstant.MIRROR_HOT_CLOSE) {
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_4)
					} else {
						mRearviewManager.setAutoHeatingset(CarSettingConstant.MIRROR_HOT_CLOSE)
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_5)
					}
				}
			}
		}
	}

	/**
	 * 雨天后视镜加热开关
	 * @param flag true:打开 false:关闭
	 */
	fun setAutoHeatedRearviewMirror(flag: Boolean) {
		val heat_value = mConfigManager?.mirrorHeatingConfig
		val status = mRearviewManager?.autoHeatingFb
		if (heat_value == CarSettingConstant.MIRROR_HEAT_CONFIG_N) {
			sendResultCode(VDVRRespondID.open_rearview_mirror_mode_12)
		} else {
			if (status != null) {
				if (flag) {
					if (status == CarSettingConstant.MIRROR_HOT_CLOSE) {
						mRearviewManager.setAutoHeatingset(CarSettingConstant.MIRROR_HOT_OPEN)
						sendResultCode(VDVRRespondID.open_rearview_mirror_mode_7)
					} else {
						sendResultCode(VDVRRespondID.open_rearview_mirror_mode_8)
					}
				} else {
					if (status == CarSettingConstant.MIRROR_HOT_CLOSE) {
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_8)
					} else {
						mRearviewManager.setAutoHeatingset(CarSettingConstant.MIRROR_HOT_CLOSE)
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_7)
					}
				}
			}
		}
	}

	/**
	 * 后视镜折叠展开
	 * @param flag true:展开 false:折叠
	 */
	fun setRearviewMirrorsFolded(flag: Boolean) {
		val state = mQuickManager.rearMirror
		val speed = mQuickManager.carSpeed
		val status = mDrivingManager.flzcU_CleanModeStatus
		if (speed == CarSettingConstant.SPEED_0) {
			if (flag) {
				if (status == CarSettingConstant.WASH_CAR_MODE_ON) {
					sendResultCode(VDVRRespondID.unfold_rearview_mirror_5)
				} else if (state == CarSettingConstant.MIRROR_UNFOLD) {
					sendResultCode(VDVRRespondID.unfold_rearview_mirror_1)
				} else {
					mQuickManager.rearMirror = CarSettingConstant.MIRROR_UNFOLD
					sendResultCode(VDVRRespondID.unfold_rearview_mirror_2)
				}
			} else {
				if (state == CarSettingConstant.MIRROR_FOLD) {
					sendResultCode(VDVRRespondID.fold_rearview_mirror_1)
				} else {
					mQuickManager.rearMirror = CarSettingConstant.MIRROR_FOLD
					sendResultCode(VDVRRespondID.fold_rearview_mirror_2)
				}
			}
		} else {
			if (flag) {
				sendResultCode(VDVRRespondID.unfold_rearview_mirror_4)
			} else {
				sendResultCode(VDVRRespondID.fold_rearview_mirror_4)
			}
		}
	}

	/**
	 * 倒车后视镜自动调节开关
	 * @param flag true:打开 false:关闭
	 */
	fun setRearviewMirrorAutomaticallyAdjusted(flag: Boolean) {
//		val configure_value = mConfigManager?.mirrorTurnDownConfig
		val configure_value = 1
		val signal = mRearviewManager.rvsExtMirrFbSts
		if (configure_value == CarSettingConstant.AUTO_MIRROR_NO) {
			sendResultCode(VDVRRespondID.Open_reverse_rearview_mirror_4)
		} else if (configure_value == CarSettingConstant.AUTO_MIRROR_YES) {
			if (flag) {
				if (signal == CarSettingConstant.MIRROR_OFF || signal == CarSettingConstant.MIRROR_RIGHT || signal == CarSettingConstant.MIRROR_LEFT) {
					mRearviewManager.setReverseExtMirrorSts(CarSettingConstant.SET_MIRROR_BOTH)
					sendResultCode(VDVRRespondID.Open_reverse_rearview_mirror_sound_1)
				}
				if (signal == CarSettingConstant.MIRROR_BOTH) {
					sendResultCode(VDVRRespondID.Open_reverse_rearview_mirror_2)
				}
			} else {
				if (signal == CarSettingConstant.MIRROR_RIGHT || signal == CarSettingConstant.MIRROR_LEFT || signal == CarSettingConstant.MIRROR_BOTH) {
					mRearviewManager.setReverseExtMirrorSts(CarSettingConstant.SET_MIRROR_OFF)
					sendResultCode(VDVRRespondID.close_reverse_rearview_mirror_sound_2)
				}
				if (signal == CarSettingConstant.MIRROR_OFF) {
					sendResultCode(VDVRRespondID.close_reverse_rearview_mirror_sound_1)
				}
			}
		}
	}

	/**
	 * 倒遮阳帘暂停/停止滑动
	 */
	fun setSunShadeStopped() {
//		val configure_value = mConfigManager?.sunshadeCtrlMode
		val configure_value = 3
		val signal = mQuickManager.sunshadeFront
		val signal2 = mQuickManager.sunshadeRear
		if (configure_value == ConfigureWordParameter.SunroofControlModeParameter.NO_SUNROOF || configure_value == ConfigureWordParameter.SunroofControlModeParameter.MANUAL_CONTROL_SUNROOF) {
			sendResultCode(VDVRRespondID.pause_sunshade_slide_3)
		} else if (configure_value == ConfigureWordParameter.SunroofControlModeParameter.FRONT_ELECTRIC_CONTROL_SUNROOF_REAR_MANUAL_CONTROL_SUNROOF) {
			if (signal == CarSettingConstant.SUNSHADE_CLOSING || signal == CarSettingConstant.SUNSHADE_OPENING) {
				mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_STOP
				sendResultCode(VDVRRespondID.pause_sunshade_slide_1)
			}
			if (signal == CarSettingConstant.Sunshade_NO) {
				sendResultCode(VDVRRespondID.pause_sunshade_slide_3_1)
			} else {
				sendResultCode(VDVRRespondID.pause_sunshade_slide_2)
			}
		} else if (configure_value == ConfigureWordParameter.SunroofControlModeParameter.ELECTRIC_CONTROL_SUNROOF) {
			if (signal == CarSettingConstant.SUNSHADE_CLOSING || signal == CarSettingConstant.SUNSHADE_OPENING || signal2 == CarSettingConstant.SUNSHADE_CLOSING || signal2 == CarSettingConstant.SUNSHADE_OPENING) {
				mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_STOP
				mQuickManager.sunshadeRear = CarSettingConstant.SET_SUNSHADE_STOP
				sendResultCode(VDVRRespondID.pause_sunshade_slide_1)
			} else if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
				sendResultCode(VDVRRespondID.pause_sunshade_slide_3_1)
			} else {
				sendResultCode(VDVRRespondID.pause_sunshade_slide_2)
			}
		}
	}


	/**
	 * 倒车外后视镜自动调节设置为xxx
	 * @param value 2:仅右侧 3:仅左侧 4:两侧同时
	 */
	fun setRearviewMirrorAutomaticallyAdjusted(value: String) {
		val signal = mQuickManager.backRearAdjust
//		val configure_value = mConfigManager?.mirrorTurnDownConfig
		val configure_value = 1
		if (configure_value == CarSettingConstant.AUTO_MIRROR_NO) {
			sendResultCode(VDVRRespondID.close_rearview_mirror_mode_10_3)
		} else if (configure_value == CarSettingConstant.AUTO_MIRROR_YES) {
			when (value) {
				VehicleConstant.SET_MIRROR_RIGHT -> {
					if (signal == CarSettingConstant.Reversing_RIGHT) {
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_10_2, "仅右侧")
					} else {
						mQuickManager.setBackRearAdjust(CarSettingConstant.SET_Reversing_RIGHT)
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_10_1, "仅右侧")
					}

				}

				VehicleConstant.SET_MIRROR_LEFT -> {
					if (signal == CarSettingConstant.Reversing_LEFT) {
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_10_2, "仅左侧")
					} else {
						mQuickManager.setBackRearAdjust(CarSettingConstant.SET_Reversing_LEFT)
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_10_1, "仅左侧")
					}

				}

				VehicleConstant.SET_MIRROR_BOTH -> {
					if (signal == CarSettingConstant.Reversing_BOTH) {
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_10_2, "两侧同时")
					} else {
						mQuickManager.setBackRearAdjust(CarSettingConstant.SET_Reversing_BOTH)
						sendResultCode(VDVRRespondID.close_rearview_mirror_mode_10_1, "两侧同时")
					}

				}
			}
		}
	}


	/**
	 * 遮阳帘开关
	 * @param flag true:打开 false:关闭
	 *@param position "ALL":全车；"F":前车；"B":后车;默认全车；
	 */
	fun setSunshadeSwitch(flag: Boolean, position: String) {
//		val configure_value = mConfigManager?.getSunshadeCtrlMode()
		val configure_value = 3
		val signal = mQuickManager.sunshadeFront
		val signal2 = mQuickManager.sunshadeRear
		//配置不存在
		if (configure_value == ConfigureWordParameter.SunroofControlModeParameter.NO_SUNROOF || configure_value == ConfigureWordParameter.SunroofControlModeParameter.MANUAL_CONTROL_SUNROOF) {
			sendResultCode(VDVRRespondID.pause_sunshade_slide_3)
		}
		//带前排不带后排
		else if (configure_value == ConfigureWordParameter.SunroofControlModeParameter.FRONT_ELECTRIC_CONTROL_SUNROOF_REAR_MANUAL_CONTROL_SUNROOF) {
			if (flag) {
				//全车
				if (position == "ALL" || position == "") {
					if (signal == CarSettingConstant.Sunshade_OPEN) {
						sendResultCode(VDVRRespondID.open_sunshade_new_3, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_CLOSE) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_1, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_2, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4)
					}
				}
				//前排
				if (position == "F") {
					if (signal == CarSettingConstant.Sunshade_OPEN) {
						sendResultCode(VDVRRespondID.open_sunshade_new_3, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_CLOSE) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_1, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_2, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4)
					}
				}
				//后排
				if (position == "B") {
					sendResultCode(VDVRRespondID.pause_sunshade_slide_3)
				}
			} else {
				//全车
				if (position == "ALL" || position == "") {
					if (signal == CarSettingConstant.Sunshade_OPEN || signal == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_CLOSE
						sendResultCode(VDVRRespondID.close_sunshade_new_1, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_CLOSE) {
						sendResultCode(VDVRRespondID.close_sunshade_new_2, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4)
					}
				}
				//前排
				if (position == "F") {
					if (signal == CarSettingConstant.Sunshade_OPEN || signal == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_CLOSE
						sendResultCode(VDVRRespondID.close_sunshade_new_1, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_CLOSE) {
						sendResultCode(VDVRRespondID.close_sunshade_new_2, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4, "前排遮阳帘")
					}

				}
				//后排
				if (position == "B") {
					sendResultCode(VDVRRespondID.pause_sunshade_slide_3)
				}
			}
		}
		//带前排和后排
		else if (configure_value == ConfigureWordParameter.SunroofControlModeParameter.ELECTRIC_CONTROL_SUNROOF) {
			if (flag) {
				//全车
				if (position == "ALL" || position == "") {
					if (signal == CarSettingConstant.Sunshade_OPEN && signal2 == CarSettingConstant.Sunshade_OPEN) {
						sendResultCode(VDVRRespondID.open_sunshade_new_3, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_CLOSE || signal2 == CarSettingConstant.Sunshade_CLOSE) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_OPEN
						mQuickManager.sunshadeRear = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_1, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_OTHER || signal2 == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_OPEN
						mQuickManager.sunshadeRear = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_2, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4, "全车遮阳帘")
					}
				}
				//前排
				if (position == "F") {
					if (signal == CarSettingConstant.Sunshade_OPEN) {
						sendResultCode(VDVRRespondID.open_sunshade_new_3, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_CLOSE) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_1, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_2, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4, "前排遮阳帘")
					}
				}
				//后排
				if (position == "B") {
					if (signal2 == CarSettingConstant.Sunshade_OPEN) {
						sendResultCode(VDVRRespondID.open_sunshade_new_3, "后排遮阳帘")
					}
					if (signal2 == CarSettingConstant.Sunshade_CLOSE) {
						mQuickManager.sunshadeRear = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_1, "后排遮阳帘")
					}
					if (signal2 == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeRear = CarSettingConstant.SET_SUNSHADE_OPEN
						sendResultCode(VDVRRespondID.open_sunshade_new_2, "后排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4, "后排遮阳帘")
					}
				}
			} else {
				//全车
				if (position == "ALL" || position == "") {
					if (signal == CarSettingConstant.Sunshade_OPEN || signal2 == CarSettingConstant.Sunshade_OPEN || signal == CarSettingConstant.Sunshade_OTHER || signal2 == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_CLOSE
						mQuickManager.sunshadeRear = CarSettingConstant.SET_SUNSHADE_CLOSE
						sendResultCode(VDVRRespondID.close_sunshade_new_1, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_CLOSE || signal2 == CarSettingConstant.Sunshade_CLOSE) {
						sendResultCode(VDVRRespondID.close_sunshade_new_2, "全车遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4, "全车遮阳帘")
					}
				}
				//前排
				if (position == "F") {
					if (signal == CarSettingConstant.Sunshade_OPEN || signal == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeFront = CarSettingConstant.SET_SUNSHADE_CLOSE
						sendResultCode(VDVRRespondID.close_sunshade_new_1, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_CLOSE) {
						sendResultCode(VDVRRespondID.close_sunshade_new_2, "前排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4, "前排遮阳帘")
					}

				}
				//后排
				if (position == "B") {
					if (signal2 == CarSettingConstant.Sunshade_OPEN || signal2 == CarSettingConstant.Sunshade_OTHER) {
						mQuickManager.sunshadeRear = CarSettingConstant.SET_SUNSHADE_CLOSE
						sendResultCode(VDVRRespondID.close_sunshade_new_1, "后排遮阳帘")
					}
					if (signal2 == CarSettingConstant.Sunshade_CLOSE) {
						sendResultCode(VDVRRespondID.close_sunshade_new_2, "后排遮阳帘")
					}
					if (signal == CarSettingConstant.Sunshade_NO || signal2 == CarSettingConstant.Sunshade_NO) {
						sendResultCode(VDVRRespondID.open_sunshade_new_4, "后排遮阳帘")
					}
				}
			}
		}
	}

	/**
	 * 	遮阳帘打开到具体数值
	 */
	fun setSunshadeSwitchValue(value: String) {
		//获取遮阳帘异常状态
		if (getSunshadeAbnormal()) {
			sunshadeAbnormal()
			return
		}
		val state = mQuickManager.sunShadeOpenDegree
		if (value.toInt() > CarSettingConstant.SUNSHADE_MAX || value.toInt() < CarSettingConstant.SUNSHADE_MIN) {
			sendResultCode(VDVRRespondID.pause_sunshade_slide_4_1)
		} else {
			if (state != value.toInt()) {
				mQuickManager.sunShadeOpenDegree = value.toInt()
				sendResultCode(VDVRRespondID.open_sunshade_to_special_number_2_1)
			} else {
				sendResultCode(VDVRRespondID.pause_sunshade_slide_5_1)
			}
		}
	}

	/**
	 * 遮阳帘关闭到具体数值
	 */
	fun setSunshadeCloseValue(value: String) {
		//获取遮阳帘异常状态
		if (getSunshadeAbnormal()) {
			sunshadeAbnormal()
			return
		}
		val state = mQuickManager.sunShadeOpenDegree
		if (value.toInt() > CarSettingConstant.SUNSHADE_MAX || value.toInt() < CarSettingConstant.SUNSHADE_MIN) {
			sendResultCode(VDVRRespondID.close_sunshade_slide_4_1)
		} else {
			if (state != value.toInt()) {
				mQuickManager.sunShadeOpenDegree = value.toInt()
				sendResultCode(VDVRRespondID.close_sunshade_to_special_number_2_1)
			} else {
				sendResultCode(VDVRRespondID.close_sunshade_slide_4_2)
			}
		}
	}

	/**
	 * 遮阳帘打开关闭一点
	 * @param value "0":打开一点，"1":关闭一点
	 */
	fun setSunshadeSwitchLittle(value: String) {
		//获取遮阳帘异常状态
		val state = mQuickManager.sunShadeOpenDegree
		if (getSunshadeAbnormal()) {
			sunshadeAbnormal()
			return
		}
		if (value == VehicleConstant.SET_SHADE_OPEN) {
			if (state == CarSettingConstant.SUNSHADE_MAX) {
				sendResultCode(VDVRRespondID.open_sunshade_little_6)
			} else {
				if (state > CarSettingConstant.SUNSHADE_90) {
					mQuickManager.sunShadeOpenDegree = CarSettingConstant.SUNSHADE_MAX
					sendResultCode(VDVRRespondID.open_sunshade_little_5)
				}
				if (state < CarSettingConstant.SUNSHADE_90) {
					mQuickManager.sunShadeOpenDegree = state + CarSettingConstant.SUNSHADE_ADD
					sendResultCode(VDVRRespondID.open_sunshade_little_4)
				}
			}
		}
		if (value == VehicleConstant.SET_SHADE_CLOSE) {
			if (state == CarSettingConstant.SUNSHADE_MIN) {
				sendResultCode(VDVRRespondID.close_sunshade_little_3)
			} else {
				if (state < CarSettingConstant.SUNSHADE_10) {
					mQuickManager.sunShadeOpenDegree = CarSettingConstant.SUNSHADE_MIN
					sendResultCode(VDVRRespondID.close_sunshade_little_2)

				}
				if (state > CarSettingConstant.SUNSHADE_10) {
					mQuickManager.sunShadeOpenDegree = state - CarSettingConstant.SUNSHADE_ADD
					sendResultCode(VDVRRespondID.close_sunshade_little_1)
				}
			}
		}
	}

	/**
	 * 判断遮阳帘异常状态
	 */
	fun sunshadeAbnormal() {
		val signal1 = mQuickManager.sunShadeNormalizedStatus//初始化状态
		val signal2 = mQuickManager.sunShadeTeachRunStatus//学习状态
		val signal3 = mQuickManager.sunShadeSensorStatus//传感器状态
		val signal4 = mQuickManager.sunShadeAntiPinchStatus//电机防夹状态
		val signal5 = mQuickManager.sunShadeTProtectionStatus//热防护状态
		val signal6 = mQuickManager.sunShadeOverVolStatus//过压状态
		val signal7 = mQuickManager.sunShadeUnderVolStatus//欠压状态
		if (signal1 == CarSettingConstant.Normal_Lized || signal2 == CarSettingConstant.Teach_Run || signal3 == CarSettingConstant.Sensor_Status) {
			TtsUtil.speak(mContext, R.string.sunshade_failure_123)
		}
		if (signal4 == CarSettingConstant.Anti_Pinch) {
			TtsUtil.speak(mContext, R.string.sunshade_failure_4)
		}
		if (signal5 == CarSettingConstant.TProtection) {
			TtsUtil.speak(mContext, R.string.sunshade_failure_5)
		}
		if (signal6 == CarSettingConstant.Over_Vol) {
			TtsUtil.speak(mContext, R.string.sunshade_failure_6)
		}
		if (signal7 == CarSettingConstant.Under_Vol) {
			TtsUtil.speak(mContext, R.string.sunshade_failure_7)
		}
	}

	/**
	 * 返回遮阳帘异常情况
	 */
	private fun getSunshadeAbnormal(): Boolean {
		val signal1 = mQuickManager.sunShadeNormalizedStatus
		val signal2 = mQuickManager.sunShadeTeachRunStatus
		val signal3 = mQuickManager.sunShadeSensorStatus
		val signal4 = mQuickManager.sunShadeAntiPinchStatus
		val signal5 = mQuickManager.sunShadeTProtectionStatus
		val signal6 = mQuickManager.sunShadeOverVolStatus
		val signal7 = mQuickManager.sunShadeUnderVolStatus

		return (signal1 == CarSettingConstant.Normal_Lized || signal2 == CarSettingConstant.Teach_Run || signal3 == CarSettingConstant.Sensor_Status ||
				signal4 == CarSettingConstant.Anti_Pinch || signal5 == CarSettingConstant.TProtection ||
				signal6 == CarSettingConstant.Over_Vol || signal7 == CarSettingConstant.Under_Vol)
	}

	/**
	 * 锁车收起遮阳帘开关
	 * @param flag true:开, false:关
	 */
	fun lockCarSunRoofShadeClose(flag: Boolean) {
		val status = mQuickManager.lockCarSunRoofShadeClose
		if (flag) {
			if (status == CarSettingConstant.Lock_SunRoofShade_OPEN) {
				sendResultCode(VDVRRespondID.Open_car_lock_and_retract_sunshade_2)
			}
			if (status == CarSettingConstant.Lock_SunRoofShade_CLOSE) {
				mQuickManager.lockCarSunRoofShadeClose = CarSettingConstant.Lock_SunRoofShade_OPEN
				sendResultCode(VDVRRespondID.Open_car_lock_and_retract_sunshade_1)
			}
		} else {
			if (status == CarSettingConstant.Lock_SunRoofShade_OPEN) {
				mQuickManager.lockCarSunRoofShadeClose = CarSettingConstant.Lock_SunRoofShade_CLOSE
				sendResultCode(VDVRRespondID.close_car_lock_and_retract_sunshade_1)
			}
			if (status == CarSettingConstant.Lock_SunRoofShade_CLOSE) {
				sendResultCode(VDVRRespondID.close_car_lock_and_retract_sunshade_2)
			}
		}
	}

	/**
	 * 洗车模式界面开关
	 * @param flag true:开, false:关
	 */
	fun carWashMode(flag: Boolean) {
		val status = mDrivingManager.flzcU_CleanModeStatus//0x1:OFF 0x2:ON
		if (status == CarSettingConstant.WASH_CAR_MODE_OFF) {
			if (flag) {
				if (WashCarModeUIAlert.isShow == true) {
					sendResultCode(VDVRRespondID.Open_car_wash_mode_face_3)
				} else {
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.DRIVE,
						DrivingFragment.DRIVE_WASH_MODE_DIALOG,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(VDVRRespondID.Open_car_wash_mode_face_2)
				}
			} else {
				if (WashCarModeUIAlert.isShow == true) {
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.DRIVE,
						DrivingFragment.DRIVE_WASH_MODE_DIALOG,
						CommonConst.DIALOG_CLOSE
					)
					sendResultCode(VDVRRespondID.close_car_wash_mode_face_3)
				} else {
					sendResultCode(VDVRRespondID.close_car_wash_mode_face_2)
				}
			}
		} else if (status == CarSettingConstant.WASH_CAR_MODE_ON) {
			if (flag) {
				if (RegularWashUIAlert.isShow == true) {
					sendResultCode(VDVRRespondID.Open_car_wash_mode_face_3)
				} else {
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.DRIVE,
						DrivingFragment.DRIVE_WASH_MODE_START_DIALOG,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(VDVRRespondID.Open_car_wash_mode_face_2)
				}
			} else {
				if (RegularWashUIAlert.isShow == true) {
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.DRIVE,
						DrivingFragment.DRIVE_WASH_MODE_START_DIALOG,
						CommonConst.DIALOG_CLOSE
					)
					sendResultCode(VDVRRespondID.close_car_wash_mode_face_3)
				} else {
					sendResultCode(VDVRRespondID.close_car_wash_mode_face_2)
				}
			}
		}
	}

	/**
	 * 洗车模式设置指定值
	 * @param value 0:常规洗车, 1:传送带洗车
	 */
	fun setCarWashMode(value: String) {
		val washMode = Prefs.get(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.REGULAR_WASH)
		//常规洗车
		if (value == VehicleConstant.WASH_MODE_REGULAR) {
			//判断记忆是否为常规洗车
			if (washMode != CarDriving.Prefs_WASH_MODE.REGULAR_WASH) {
				Prefs.put(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.REGULAR_WASH)
				if (WashCarModeUIAlert.isShow == false) {
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.DRIVE,
						DrivingFragment.DRIVE_WASH_MODE_DIALOG,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(VDVRRespondID.Open_car_wash_mode_2)
				} else {
					sendResultCode(VDVRRespondID.Open_car_wash_mode_2)
				}
			} else {
				Prefs.put(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.REGULAR_WASH)
				if (WashCarModeUIAlert.isShow == false) {
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.DRIVE,
						DrivingFragment.DRIVE_WASH_MODE_DIALOG,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(VDVRRespondID.Open_car_wash_mode_2)
					return
				}
				sendResultCode(VDVRRespondID.Open_car_wash_mode_3, "常规洗车")
			}
		}
		//传送带洗车
		if (value == VehicleConstant.WASH_MODE_TRANSPORT) {
			//判断是否为传送带洗车
			if (washMode == CarDriving.Prefs_WASH_MODE.CONVEYOR_WASH) {
				if (WashCarModeUIAlert.isShow == false) {
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.DRIVE,
						DrivingFragment.DRIVE_WASH_MODE_DIALOG,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(VDVRRespondID.Open_car_wash_mode_2)
					return
				} else {
					sendResultCode(VDVRRespondID.Open_car_wash_mode_3, "传送带洗车")
				}
			} else {
				Prefs.put(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.CONVEYOR_WASH)
				if (WashCarModeUIAlert.isShow == false) {
					DialogNavigationUtils.launchMainActivity(
						context,
						MainActivity.MainTabIndex.DRIVE,
						DrivingFragment.DRIVE_WASH_MODE_DIALOG,
						CommonConst.DIALOG_OPEN
					)
					sendResultCode(VDVRRespondID.Open_car_wash_mode_2)
				} else {
					sendResultCode(VDVRRespondID.Open_car_wash_mode_2)
				}
			}
		}
	}

	/**
	 * 洗车模式开关
	 * @param value 0:开, 1:关
	 */
	fun washCarMode(value: String) {
		val status = mDrivingManager.flzcU_CleanModeStatus//0x1:OFF 0x2:ON
		val power = mDrivingManager.flzcU_9_PowerMode//0x2:ON
		val speed = mDrivingManager.carSpeed
		val gearPosition = mDrivingManager.gearPosition//0x1:P,0x3:N
		//四门两盖
		val chrg = mQuickManager.getChrgHoodSts(0)
		val oil = mQuickManager.oilLidSwitch
		val lhf = mQuickManager.lhFdoorSts
		val rhf = mQuickManager.rhfDoorSts
		val lhr = mQuickManager.lhRdoorSts
		val rhr = mQuickManager.rhrDoorSts
		val washMode = Prefs.get(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.REGULAR_WASH)
		if (value == VehicleConstant.WASH_MODE_ON) {
			if (status == CarSettingConstant.WASH_CAR_MODE_OFF) {
				if (power == CarSettingConstant.GEAR_ON && speed < CarSettingConstant.SPEED_4 && chrg == CarSettingConstant.FOUR_DOOR_CONFIG_OFF && oil == CarSettingConstant.FOUR_DOOR_CONFIG_OFF && lhf == CarSettingConstant.FOUR_DOOR_CONFIG_OFF && rhf == CarSettingConstant.FOUR_DOOR_CONFIG_OFF && lhr == CarSettingConstant.FOUR_DOOR_CONFIG_OFF && rhr == CarSettingConstant.FOUR_DOOR_CONFIG_OFF) {
					if (gearPosition == CarSettingConstant.GEAR_POSITION_P) {
						if (washMode == CarDriving.Prefs_WASH_MODE.REGULAR_WASH) {
							Prefs.put(PrefsConst.WASH_MODE, CarDriving.Prefs_WASH_MODE.REGULAR_WASH)
							if (WashCarModeUIAlert.isShow == false) {
								DialogNavigationUtils.launchMainActivity(
									context,
									MainActivity.MainTabIndex.DRIVE,
									DrivingFragment.DRIVE_WASH_MODE_DIALOG,
									CommonConst.DIALOG_OPEN
								)
								Handler(Looper.getMainLooper()).postDelayed({
									mDrivingManager.setICC_CleanMode(CarSettingConstant.WASH_CAR_MODE_ON)
								}, 3000)
								sendResultCode(VDVRRespondID.Start_washing_car_2)
							} else {
								mDrivingManager.setICC_CleanMode(CarSettingConstant.WASH_CAR_MODE_ON)
								sendResultCode(VDVRRespondID.Start_washing_car_2)
							}
						} else {
							EToast.showToast(
								context,
								"请将电源切换⾄ON档，车辆关闭四门两盖，处于N档静⽌状态",
								2000,
								false
							)
							sendResultCode(VDVRRespondID.Start_washing_car_1)
						}
					} else if (gearPosition == CarSettingConstant.GEAR_POSITION_N) {
						if (washMode == CarDriving.Prefs_WASH_MODE.CONVEYOR_WASH) {
							if (WashCarModeUIAlert.isShow == false) {
								DialogNavigationUtils.launchMainActivity(
									context,
									MainActivity.MainTabIndex.DRIVE,
									DrivingFragment.DRIVE_WASH_MODE_DIALOG,
									CommonConst.DIALOG_OPEN
								)
								Handler(Looper.getMainLooper()).postDelayed({
									mDrivingManager.setICC_CleanMode(CarSettingConstant.WASH_CAR_MODE_ON)
								}, 3000)
								sendResultCode(VDVRRespondID.Start_washing_car_2)
								return
							}
							mDrivingManager.setICC_CleanMode(CarSettingConstant.WASH_CAR_MODE_ON)
							sendResultCode(VDVRRespondID.Start_washing_car_2)
						} else {
							EToast.showToast(
								context,
								"请将电源切换⾄ON档，车辆关闭四门两盖，处于P档静⽌状态",
								2000,
								false
							)
							sendResultCode(VDVRRespondID.Start_washing_car_1)
						}
					}
				} else {
					if (washMode == CarDriving.Prefs_WASH_MODE.CONVEYOR_WASH) {
						EToast.showToast(
							context,
							"请将电源切换⾄ON档，车辆关闭四门两盖，处于N档静⽌状态",
							2000,
							false
						)
						sendResultCode(VDVRRespondID.Start_washing_car_1)
					} else {
						EToast.showToast(
							context,
							"请将电源切换⾄ON档，车辆关闭四门两盖，处于P档静⽌状态",
							2000,
							false
						)
						sendResultCode(VDVRRespondID.Start_washing_car_1)
					}
				}
			} else {
				sendResultCode(VDVRRespondID.Start_washing_car_3)
			}
		} else if (value == VehicleConstant.WASH_MODE_OFF) {
			if (status == CarSettingConstant.WASH_CAR_MODE_OFF) {
				sendResultCode(VDVRRespondID.End_washing_car_1)
			} else if (status == CarSettingConstant.WASH_CAR_MODE_ON) {
				mDrivingManager.setICC_CleanMode(CarSettingConstant.WASH_CAR_MODE_OFF)
				sendResultCode(VDVRRespondID.End_washing_car_2)
			}
		}
	}

	/**
	 * 保存后视镜位置
	 */
	fun saveMirrorPosition() {
		sendResultCode(VDVRRespondID.save_rearview_mirror_direction_1)
	}

	/**
	 * 打开后视镜位置
	 */
	fun openMirrorPosition(flag: Boolean) {
		sendResultCode(VDVRRespondID.open_rearview_mirror_mode_9)
	}



	fun videoLimitControl(flag: Boolean) {
		val videoLimit = Prefs.getGlobalValue(
			PrefsConst.GlobalValue.DISPLAY_VIDEO_LIMIT,
			PrefsConst.TRUE
		)
		if (flag) {
			if (videoLimit == 1)
				sendResultCode(VDVRRespondID.open_video_limit_2)
			else if (videoLimit == 0) {
				Prefs.setGlobalValue(PrefsConst.GlobalValue.DISPLAY_VIDEO_LIMIT, 1)
				sendResultCode(VDVRRespondID.open_video_limit_1)
			}
		} else {
			if (videoLimit == 1) {
//				TODO拉起页面
				Prefs.setGlobalValue(PrefsConst.GlobalValue.DISPLAY_VIDEO_LIMIT, 0)
				sendResultCode(VDVRRespondID.close_video_limit_1)
			} else if (videoLimit == 0)
				sendResultCode(VDVRRespondID.close_video_limit_2)
		}
	}

	/**
	 * 无线充电开关
	 * @param flag true:开, false:关
	 * @param position
	 */
	fun wirelessChargeControl(flag: Boolean, position: String) {
		val wirelessCharge = connectManager.wirelessCharge
		if (flag) {
			when (position) {
				"LF" -> {
					if (wirelessCharge == CarSettingConstant.PHONE_CHARGE)
						sendResultCode(VDVRRespondID.open_wireless_charging_for_phone_1_2)
					else if (wirelessCharge == CarSettingConstant.PHONE_CHARGE_CLOSE) {
						connectManager.wirelessCharge = CarSettingConstant.PHONE_CHARGE_OPEN
						sendResultCode(VDVRRespondID.open_wireless_charging_for_phone_1_1)
					}
				}

				"RF" -> {
					sendResultCode(VDVRRespondID.open_wireless_charging_for_phone_2_3)
				}

				"" -> {
					if (wirelessCharge == CarSettingConstant.PHONE_CHARGE)
						sendResultCode(VDVRRespondID.open_wireless_charging_for_phone_2)
					else if (wirelessCharge == CarSettingConstant.PHONE_CHARGE_CLOSE) {
						connectManager.wirelessCharge = CarSettingConstant.PHONE_CHARGE_OPEN
						sendResultCode(VDVRRespondID.open_wireless_charging_for_phone_1)
					}
				}
			}
		} else {
			when (position) {
				"LF" -> {
					if (wirelessCharge == CarSettingConstant.PHONE_CHARGE) {
						mConnectPresenter.frontChargingState = CarSettingConstant.PHONE_CHARGE
						sendResultCode(VDVRRespondID.close_wireless_charging_for_phone_1_2)
					} else if (wirelessCharge == CarSettingConstant.PHONE_CHARGE_CLOSE)
						sendResultCode(VDVRRespondID.close_wireless_charging_for_phone_1_1)
				}

				"RF" -> {
					sendResultCode(VDVRRespondID.close_wireless_charging_for_phone_2_3)
				}

				"" -> {
					if (wirelessCharge == CarSettingConstant.PHONE_CHARGE) {
						mConnectPresenter.frontChargingState = CarSettingConstant.PHONE_CHARGE
						sendResultCode(VDVRRespondID.close_wireless_charging_for_phone_2)
					} else if (wirelessCharge == CarSettingConstant.PHONE_CHARGE_CLOSE)
						sendResultCode(VDVRRespondID.close_wireless_charging_for_phone_1)
				}
			}
		}
	}

	fun trailerModeControl(flag: Boolean) {
		val towingMode = carNewEnergyManager.getTowingModeSwitch()

		val enableTractionMode = mDrivingPresenter.enableTractionMode()
		if (flag) {
			if (towingMode == CarDriving.VCU_TowingMode.ACTIVE)
				sendResultCode(VDVRRespondID.open_trailer_mode_switch_2)
			else if (towingMode == CarDriving.VCU_TowingMode.NOT_ACTIVE) {
				if (enableTractionMode == 1) {
					carNewEnergyManager.setTowingModeSwitch(CarDriving.ICC_TowingMode.ON)
					sendResultCode(VDVRRespondID.open_trailer_mode_switch_1)
				} else if (enableTractionMode == 0) {
					sendResultCode(VDVRRespondID.open_trailer_mode_switch_3)
				}
			}
		} else {
			if (towingMode == CarDriving.VCU_TowingMode.ACTIVE) {
				carNewEnergyManager.setTowingModeSwitch(CarDriving.ICC_TowingMode.OFF)
				sendResultCode(VDVRRespondID.close_trailer_mode_switch_2)
			} else if (towingMode == CarDriving.VCU_TowingMode.NOT_ACTIVE)
				sendResultCode(VDVRRespondID.close_trailer_mode_switch_1)
		}
	}

	fun hillDescentSystemControl(flag: Boolean) {
		val powerMode = mDrivingPresenter.powerMode
		val steepSlopeDescent = mDrivingManager.getHillDescentControl()

		if (flag) {
			if (powerMode == 1) {

				if (steepSlopeDescent == CarDriving.HDCCtrlSts.NOT_ACTIVE || steepSlopeDescent == CarDriving.HDCCtrlSts.ON_ACTIVE_BRAKING)
					sendResultCode(VDVRRespondID.steep_descent_system_2)
				else if (steepSlopeDescent == CarDriving.HDCCtrlSts.OFF) {
					mDrivingManager.setHillDescentControlSet(CarDriving.TIHU_SetHDCOnOff.ON)
					sendResultCode(VDVRRespondID.steep_descent_system_1)
				}
			} else {
				sendResultCode(VDVRRespondID.steep_descent_system_5)
			}
		} else {
			if (steepSlopeDescent == CarDriving.HDCCtrlSts.NOT_ACTIVE || steepSlopeDescent == CarDriving.HDCCtrlSts.ON_ACTIVE_BRAKING) {
				mDrivingManager.setHillDescentControlSet(CarDriving.TIHU_SetHDCOnOff.OFF)
				sendResultCode(VDVRRespondID.steep_descent_system_4)
			} else if (steepSlopeDescent == CarDriving.HDCCtrlSts.OFF)
				sendResultCode(VDVRRespondID.steep_descent_system_3)

		}
	}


	fun phoneForgetReminderControl(flag: Boolean) {
//		TODO判断是否支持 open_phone_forget_reminder_4 close_phone_forget_reminder_4
		val wirelessCharge = connectManager.wirelessCharge
		val forgetReminder = mCarSettingManager.phoneLeaveAlert
		if (flag) {
			if (forgetReminder == 0x1)
				sendResultCode(VDVRRespondID.open_phone_forget_reminder_2)
			else if (forgetReminder == 0x0) {
				if (wirelessCharge == 0x0)
					sendResultCode(VDVRRespondID.open_phone_forget_reminder_3)
				else if (wirelessCharge == 0x1) {
					mConnectPresenter.forgetReminder = 0x2
					sendResultCode(VDVRRespondID.open_phone_forget_reminder_1)
				}
			}
		} else {
			if (forgetReminder == 0x1) {
				mConnectPresenter.forgetReminder = 0x1
				sendResultCode(VDVRRespondID.close_phone_forget_reminder_2)
			} else if (forgetReminder == 0x0)
				sendResultCode(VDVRRespondID.close_phone_forget_reminder_1)
		}
	}

	fun setAlarmModeControl() {
		sendResultCode(VDVRRespondID.set_defense_method_1)
	}

	fun autoParkingControl(flag: Boolean) {
//		TODO 拉起页面
		if (flag)
			sendResultCode(VDVRRespondID.open_auto_hold_1)
		else
			sendResultCode(VDVRRespondID.close_auto_hold_1)
	}

	fun maintenanceTimeReminderControl(flag: Boolean) {
//		TODO判斷是否支持 open_maintenance_time_reminder_1  close_maintenance_time_reminder_1


		if (flag) {
			conditionManager.setMaintainTipSet(0x1)
			sendResultCode(VDVRRespondID.open_maintenance_time_reminder_2)
		} else {
			conditionManager.setMaintainTipSet(0x0)
			sendResultCode(VDVRRespondID.close_maintenance_time_reminder_2)
		}
	}

	fun holdMileageResetDialogControl(flag: Boolean) {
//		if (flag) {
//			if (MaintenanceConfirmUIAlert.isShow) {
//				sendResultCode(VDVRRespondID.Open_maintenance_mileage_reset_2)
//			} else {
//				DialogNavigationUtils.launchMainActivity(
//					context,
//					MainActivity.MainTabIndex.CONDITION,
//					ConditionFragment.DIALOG_ALERT_CONDITION_MAINTENCE_RESET_CONFIRM,
//					CommonConst.DIALOG_OPEN
//				)
//				sendResultCode(VDVRRespondID.Open_maintenance_mileage_reset_1)
//			}
//		} else {
//			if (MaintenanceConfirmUIAlert.isShow) {
//				DialogNavigationUtils.launchMainActivity(
//					context,
//					MainActivity.MainTabIndex.CONDITION,
//					ConditionFragment.DIALOG_ALERT_CONDITION_MAINTENCE_RESET_CONFIRM,
//					CommonConst.DIALOG_CLOSE
//				)
//				sendResultCode(VDVRRespondID.close_maintenance_mileage_reset_1)
//			} else {
//				sendResultCode(VDVRRespondID.close_maintenance_mileage_reset_2)
//			}
//		}
	}

	fun adjustEnergyControl(value: String) {
		val energyRecoveryMode = carNewEnergyManager.energyRecoveryMode
		if (value == Voice.LevelConstants.HIGHER) {//0-高一点
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL) {
				sendResultCode(VDVRRespondID.raise_energy_recuperation_level_little_2)
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL
				sendResultCode(VDVRRespondID.raise_energy_recuperation_level_little_3, "高")
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.LOW_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL
				sendResultCode(VDVRRespondID.raise_energy_recuperation_level_little_3, "中")
			}
		} else if (value == Voice.LevelConstants.LOWER) {//1-低一点
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.LOW_LEVEL) {
				sendResultCode(VDVRRespondID.lower_energy_recuperation_level_little_2)
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.LOW_LEVEL
				sendResultCode(VDVRRespondID.lower_energy_recuperation_level_little_3, "低")
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL
				sendResultCode(VDVRRespondID.lower_energy_recuperation_level_little_3, "中")
			}
		} else if (value == Voice.LevelConstants.LOW) {//4-低档
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.LOW_LEVEL) {
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_gear_1_1, "低")
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL || energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.LOW_LEVEL
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_gear_2_1, "低")
			}
		} else if (value == Voice.LevelConstants.MEDIUM) {//3-中档
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL) {
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_gear_1_1, "中")
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.LOW_LEVEL || energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_gear_2_1, "中")
			}
		} else if (value == Voice.LevelConstants.HIGH) {//2-高档
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL) {
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_gear_1_1, "高")
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.LOW_LEVEL || energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_gear_2_1, "高")
			}
		} else if (value == Voice.LevelConstants.HIGHEST) {//5-最高
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL) {
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_max_2, "高")
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL || energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.LOW_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_max_3, "高")
			}
		} else if (value == Voice.LevelConstants.LOWEST) {//6-最低
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.LOW_LEVEL) {
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_min_2, "低")
			} else if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL || energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL) {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.LOW_LEVEL
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_min_3, "低")
			}
		}
	}

	fun autoParkingUnlockControl(flag: Boolean) {
		val automaticParkingUnlock = mQuickManager.automaticParkingUnlock
		if (flag) {
			if (automaticParkingUnlock == CarQuickControl.GetAutomaticParkUnlockSts.ENABLE)
				sendResultCode(VDVRRespondID.open_parking_automatically_unlocks_1)
			else if (automaticParkingUnlock == CarQuickControl.GetAutomaticParkUnlockSts.DISABLE) {
				mQuickManager.automaticParkingUnlock =
					CarQuickControl.SetAutomaticParkUnlockSts.ENABLE
				sendResultCode(VDVRRespondID.open_parking_automatically_unlocks_2)
			}
		} else {
			if (automaticParkingUnlock == CarQuickControl.GetAutomaticParkUnlockSts.ENABLE) {
				mQuickManager.automaticParkingUnlock =
					CarQuickControl.SetAutomaticParkUnlockSts.DISABLE
				sendResultCode(VDVRRespondID.close_parking_automatically_unlocks_1)
			} else if (automaticParkingUnlock == CarQuickControl.GetAutomaticParkUnlockSts.DISABLE)
				sendResultCode(VDVRRespondID.close_parking_automatically_unlocks_2)
		}
	}

	fun hoodBrakeControl(flag: Boolean) {
//		TODO 拉起页面
		if (flag)
			sendResultCode(VDVRRespondID.open_auto_hold_1)
		else
			sendResultCode(VDVRRespondID.Turn_off_parking_brake_4)
	}

	fun lockCloseDayShadeControl(flag: Boolean) {
//		TODO open_closing_canopy_sunshades_when_locking_3 close_closing_canopy_sunshades_when_locking_3
		val lockCarSunRoofShadeClose = mQuickManager.lockCarSunRoofShadeClose

		if (flag) {
			if (lockCarSunRoofShadeClose == CarQuickControl.GetLockCarSunRoofShadeCloseSts.ENABLE)
				sendResultCode(VDVRRespondID.open_closing_canopy_sunshades_when_locking_1)
			else if (lockCarSunRoofShadeClose == CarQuickControl.GetLockCarSunRoofShadeCloseSts.DISABLE) {
				mQuickManager.lockCarSunRoofShadeClose =
					CarQuickControl.SetLockCarSunRoofShadeCloseSts.ON
				sendResultCode(VDVRRespondID.open_closing_canopy_sunshades_when_locking_2)
			}
		} else {
			if (lockCarSunRoofShadeClose == CarQuickControl.GetLockCarSunRoofShadeCloseSts.ENABLE) {
				mQuickManager.lockCarSunRoofShadeClose =
					CarQuickControl.SetLockCarSunRoofShadeCloseSts.OFF
				sendResultCode(VDVRRespondID.close_closing_canopy_sunshades_when_locking_1)
			} else if (lockCarSunRoofShadeClose == CarQuickControl.GetLockCarSunRoofShadeCloseSts.DISABLE) {
				sendResultCode(VDVRRespondID.close_closing_canopy_sunshades_when_locking_2)
			}
		}
	}

	/**
	 * 低 = 1
	 * 中 = 2
	 * 高 = 3
	 */
	fun setAdjustModeEnergy(value: String) {
		//TODO 判断是否支持 adjust_energy_recuperation_level_to_number_6
		val energyRecoveryMode = carNewEnergyManager.energyRecoveryMode
		if (value == "1") {
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.LOW_LEVEL) {
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_number_4, "1挡")
			} else {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.LOW_LEVEL
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_number_5, "1挡")
			}
		} else if (value == "2") {
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL) {
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_number_4, "2挡")
			} else {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.MIDDLE_LEVEL
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_number_5, "2挡")
			}
		} else if (value == "3") {
			if (energyRecoveryMode == CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL) {
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_number_4, "3挡")
			} else {
				carNewEnergyManager.energyRecoveryMode = CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL
				sendResultCode(VDVRRespondID.adjust_energy_recuperation_level_to_number_5, "3挡")
			}
		}
	}

	/**
	 * 极致纯电开关
	 * @param flag true 打开
	 */
	fun ultimateElectricControl(flag: Boolean) {
		val carMode = mDrivingManager.driveMode
		val extremePureSignal = mDrivingManager.evMode
		if (flag) {
			if (carMode != CarDriving.VCC_1_DriveMode.ECO) {
				sendResultCode(VDVRRespondID.Open_Extreme_Pure_Electric_1)
			} else {
				if (extremePureSignal == CarDriving.ForcedEVMode.ACTIVE) {
					sendResultCode(VDVRRespondID.Open_Extreme_Pure_Electric_2)
				} else if (extremePureSignal == CarDriving.ForcedEVMode.NOT_ACTIVE) {
					mDrivingManager.setEVModeSet(CarDriving.ICC_ForcedEVMode.ON)
					sendResultCode(VDVRRespondID.Open_Extreme_Pure_Electric_3)
				}
			}
		} else {
			if (carMode != CarDriving.VCC_1_DriveMode.ECO) {
				sendResultCode(VDVRRespondID.close_Extreme_Pure_Electric_1)
			} else {
				if (extremePureSignal == CarDriving.ForcedEVMode.ACTIVE) {
					mDrivingManager.setEVModeSet(CarDriving.ICC_ForcedEVMode.OFF)
					sendResultCode(VDVRRespondID.close_Extreme_Pure_Electric_2)
				} else if (extremePureSignal == CarDriving.ForcedEVMode.NOT_ACTIVE) {
					sendResultCode(VDVRRespondID.close_Extreme_Pure_Electric_3)
				}
			}
		}
	}

	/**
	 * 驾驶模式设置为xxx
	 * @param value  0=经济，1=舒适，2=运动，
	 */
	fun setDrivingMode(value: String) {
		val drivingMode = mDrivingManager.propulsionMode
		val carSpeed = mDrivingManager.carSpeed
		var TTS_str = mContext.getString(R.string.str_driving_entered)
		if (carSpeed == 0) {
			if (value == Voice.CarDrivingConstants.ECONOMY) {
				if (drivingMode == CarDriving.FLZCU_PropulsionMode.COMFORTABLE)
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "经济")
				else {
					mDrivingManager.setPropulsionModeSet(CarDriving.ICC_PropulsionMode.COMFORTABLE)
					TTS_str += mContext.getString(R.string.str_driving_mode_jj) + mContext.getString(
						R.string.mode
					)
					TtsUtil.speak(mContext, TTS_str)
				}
			} else if (value == Voice.CarDrivingConstants.STANDARD) {
				if (drivingMode == CarDriving.FLZCU_PropulsionMode.NORMAL)
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "舒适")
				else {
					mDrivingManager.setPropulsionModeSet(CarDriving.ICC_PropulsionMode.NORMAL)
					TTS_str += mContext.getString(R.string.str_driving_mode_ss) + mContext.getString(
						R.string.mode
					)
					TtsUtil.speak(mContext, TTS_str)
				}
			} else if (value == Voice.CarDrivingConstants.SPORT) {
				if (drivingMode == CarDriving.FLZCU_PropulsionMode.SPORT)
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "运动")
				else {
					mDrivingManager.setPropulsionModeSet(CarDriving.ICC_PropulsionMode.SPORT)
					TTS_str += mContext.getString(R.string.str_driving_mode_yd) + mContext.getString(
						R.string.mode
					)
					TtsUtil.speak(mContext, TTS_str)
				}
			}
		} else if (carSpeed > 0) {
			sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_3)
		}
	}

	/**
	 * 切换驾驶模式,无指定值
	 */
	fun changeDrivingMode() {
		var TTS_str = mContext.getString(R.string.str_driving_entered)
		val enableChangeDrivingMode = mDrivingPresenter.enableChangeDrivingMode()
		val currentDrivingMode = mDrivingPresenter.drivingMode
		if (enableChangeDrivingMode == 1) {
			val newMode = listOf(1, 2, 3).filter { it != currentDrivingMode }.random()
			mDrivingPresenter.drivingMode = newMode
			// 根据新驾驶模式播放对应的TTS语音提示
			when (newMode) {
				CarDriving.FLZCU_PropulsionMode.COMFORTABLE -> {
					TTS_str += mContext.getString(R.string.str_driving_mode_jj) + mContext.getString(
						R.string.mode
					)
					TtsUtil.speak(mContext, TTS_str)
				}

				CarDriving.FLZCU_PropulsionMode.NORMAL -> {
					TTS_str += mContext.getString(R.string.str_driving_mode_ss) + mContext.getString(
						R.string.mode
					)
					TtsUtil.speak(mContext, TTS_str)
				}

				CarDriving.FLZCU_PropulsionMode.SPORT -> {
					TTS_str += mContext.getString(R.string.str_driving_mode_yd) + mContext.getString(
						R.string.mode
					)
					TtsUtil.speak(mContext, TTS_str)
				}
			}
		} else {
			sendResultCode(VDVRRespondID.switch_driving_mode_2)
		}
	}

	/**
	 * 封装TTS播报
	 */
	object TtsUtil {

		/**
		 * 播放TTS语音
		 * @param context 上下文对象
		 * @param stringResId 字符串资源ID (R.string.xxx)
		 */
		fun speak(context: Context?, @StringRes stringResId: Int) {
			context?.let { safeContext ->
				val ttsText = safeContext.getString(stringResId)
				speakInternal(safeContext, ttsText)
			} ?: Log.e("TtsUtil", "Context is null, cannot play TTS")
		}

		/**
		 * 播放TTS语音 (直接传入字符串)
		 * @param context 上下文对象
		 * @param text 要播报的文本内容
		 */
		fun speak(context: Context?, text: String) {
			if (context == null) {
				Log.e("TtsUtil", "Context is null, cannot play TTS")
				return
			}
			speakInternal(context, text)
		}

		private fun speakInternal(context: Context, text: String) {
			TtsHelper.getInstance().init(context, object : TtsInitListener {
				override fun onInitSuccess() {
					Log.d("TtsUtil", "TTS initialized")
					TtsHelper.getInstance().speak(text, object : TtsPlayListener {
						override fun onPlayBegin() {
							Log.d("TtsUtil", "Playback started")
						}

						override fun onPlayCompleted() {
							Log.d("TtsUtil", "Playback completed")
							TtsHelper.getInstance().release()
						}

						override fun onPlayInterrupted() {
							Log.w("TtsUtil", "Playback interrupted")
							TtsHelper.getInstance().release()
						}
					})
				}

				override fun onInitFailed(errorCode: Int) {
					Log.e("TtsUtil", "Initialization failed, error code: $errorCode")
				}
			})
		}

		/**
		 * 主动释放TTS资源
		 */
		fun release() {
			TtsHelper.getInstance().release()
		}
	}

	/**
	 * 车辆模式设置为xxx
	 * @param value 0=纯电/节能，1=舒适混动，2=舒适，3=节能混动，4=风云GT/运动，5=个性化/自定义，6=雨雪
	 */
	fun setCarMode(value: String) {
		val carMode = mDrivingManager.driveMode
		val carSpeed = mDrivingManager.carSpeed
		if (carSpeed == 0) {
			if (value == Voice.CarModeConstants.ECO) {
				if (carMode == CarDriving.VCC_1_DriveMode.ECO) {
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "纯电优先")
				} else {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.ECO)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_1_tts)
				}
			} else if (value == Voice.CarModeConstants.ECO_MIX) {
				if (carMode == CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID) {
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "节能混动")
				} else {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.ENERGY_SAVING_HYBRID)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_2_tts)
				}
			} else if (value == Voice.CarModeConstants.COMFORT_MIX) {
				if (carMode == CarDriving.VCC_1_DriveMode.NORMAL) {
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "舒适混动")
				} else {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.NORMAL)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_3_tts)
				}
			} else if (value == Voice.CarModeConstants.SPORTS) {
				if (carMode == CarDriving.VCC_1_DriveMode.SPORT) {
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "风云GT")

				} else {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.SPORT)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_4_tts)
				}
			} else if (value == Voice.CarModeConstants.RAIN_SNOW) {
				if (carMode == CarDriving.VCC_1_DriveMode.SNOW) {
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "雨雪模式")
				} else {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.RAIN_SNOW)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_5_tts)
				}
			} else if (value == Voice.CarModeConstants.PERSONALIZED) {
				if (carMode == CarDriving.VCC_1_DriveMode.INDIVIDUAL) {
					sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_4, "个性化")
				} else {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.INDIVIDUAL)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_6_tts)
				}
			}
		} else if (carSpeed > 0) {
			sendResultCode(VDVRRespondID.set_driving_mode_to_xxx_3)
		}


	}

	/**
	 * 切换车辆模式，无指定模式
	 */
	fun switchRandomCarMode() {
		val carMode = mDrivingManager.driveMode
		val carSpeed = mDrivingManager.carSpeed
		if (carSpeed == 0) {
			val newMode = listOf(
				CarDriving.VCC_1_DriveMode.ECO,
				CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID,
				CarDriving.VCC_1_DriveMode.NORMAL,
				CarDriving.VCC_1_DriveMode.SPORT,
				CarDriving.VCC_1_DriveMode.SNOW,
				CarDriving.VCC_1_DriveMode.INDIVIDUAL,
			).filter { it != carMode }.random()
			when (newMode) {
				CarDriving.VCC_1_DriveMode.ECO -> {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.ECO)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_1_tts)
				}

				CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID -> {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.ENERGY_SAVING_HYBRID)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_2_tts)
				}

				CarDriving.VCC_1_DriveMode.NORMAL -> {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.NORMAL)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_3_tts)
				}

				CarDriving.VCC_1_DriveMode.SPORT -> {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.SPORT)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_4_tts)
				}

				CarDriving.VCC_1_DriveMode.SNOW -> {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.RAIN_SNOW)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_5_tts)
				}

				CarDriving.VCC_1_DriveMode.INDIVIDUAL -> {
					mDrivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.INDIVIDUAL)
					TtsUtil.speak(mContext, R.string.str_driving_car_mode_6_tts)
				}
			}

		} else {
			sendResultCode(VDVRRespondID.switch_vehicle_mode_2)
		}
	}

	fun holdBrakeControl(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_auto_hold_1)
		} else {
			sendResultCode(VDVRRespondID.Turn_off_parking_brake_4)
		}
	}

	fun dmsControl(flag: Boolean) {
//		TODO 配置是否存在open_dms_camera_5
		val privacyPolicyStatus = mDrivingPresenter.privacyPolicyStatus
		val swCamera = mRecognitionPresenter.swCamera
		if (flag) {
			if (privacyPolicyStatus == 0) {
//				TODO 拉起页面
				sendResultCode(VDVRRespondID.open_dms_camera_1)
			} else if (privacyPolicyStatus == 1) {
				if (swCamera == 1) {
					sendResultCode(VDVRRespondID.open_dms_camera_2)
				} else if (swCamera == 0) {
//					TODO 摄像头权限 拉起页面
					sendResultCode(VDVRRespondID.open_dms_camera_3)
				}
			}
		} else {
			if (swCamera == 1) {
				mRecognitionPresenter.swCamera = 0
				sendResultCode(VDVRRespondID.close_dms_camera_1)
			} else
				sendResultCode(VDVRRespondID.close_dms_camera_2)
		}
	}

	/**
	 * 遥控车窗
	 * @parameter flag true 打开 false 关闭
	 * @return null
	 */
	fun setRemoteSwitch(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_remote_control_window_1)
		} else {
			sendResultCode(VDVRRespondID.close_remote_control_window_1)
		}
	}

	/**
	 * 雨天自动关窗
	 * @parameter flag true 打开 false 关闭
	 * @return null
	 */
	fun setRainAutoLiftWindow(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.automatic_closing_of_windows_when_it_rains_1)
		} else {
			sendResultCode(VDVRRespondID.automatic_closing_of_windows_when_it_rains_2)
		}
	}

	/**
	 * 特殊情况自动关窗
	 * @parameter flag true 打开 false 关闭
	 * @return null
	 */
	fun setSpecConditionLiftWindow(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_automatic_window_closing_1)
		} else {
			sendResultCode(VDVRRespondID.close_automatic_window_closing_1)
		}
	}

	/**
	 * 车窗帘
	 * @parameter flag true 打开 false 关闭
	 * @return null
	 */
	fun setWindowCurtain(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_window_curtain_1)
		} else {
			sendResultCode(VDVRRespondID.close_window_curtain_1)
		}
	}

	/**
	 * 天窗通风
	 * @parameter flag true 打开 false 关闭
	 * @return null
	 */
	fun setSunroofVentilate(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_skylight_ventilate_1)
		} else {
			sendResultCode(VDVRRespondID.close_skylight_ventilate_1)
		}
	}

	/**
	 * 天窗透气
	 * @parameter flag true 打开 false 关闭
	 * @return null
	 */
	fun setSunroofRiser(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_sunroof_breathe_mode_1)
		} else {
			sendResultCode(VDVRRespondID.close_sunroof_breathe_mode_1)
		}
	}

	/**
	 * 天窗翘起
	 * @parameter flag true 打开 false 关闭
	 * @return null
	 */
	fun setSunroofRaise(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_skylight_tilting_1)
		} else {
			sendResultCode(VDVRRespondID.close_skylight_tilting_1)
		}
	}

	/**
	 * 定时通风
	 * @parameter flag true 启动 false 停止
	 * @return null
	 */
	fun timedVentilate(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_timed_ventilation_1)
		} else {
			sendResultCode(VDVRRespondID.close_timed_ventilation_1)
		}
	}

	/**
	 * 定时通风间隔
	 * @parameter value: String, pos: String
	 * @return null
	 */
	fun timedVentilateInterval(value: String, pos: String) {
		sendResultCode(VDVRRespondID.set_timed_ventilation_interval_1)
	}

	/**
	 * 星动吧台
	 * @parameter value: String
	 * @return null
	 */
	fun setAuxiliaryInstrument(value: String) {
		sendResultCode(VDVRRespondID.move_star_bar_forward_2)
	}

	/**
	 * 控制车门
	 * @parameter flag: Boolean, pos: Strin
	 * @return null
	 */
	fun controlDoor(flag: Boolean, pos: String) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_car_door_7)
		} else {
			sendResultCode(VDVRRespondID.close_car_door_7)
		}
	}

	/**
	 * 暂停打开/关闭车门
	 * @parameter isEnable: Boolean, pos: String
	 * @return null
	 */
	fun temporaryControlDoor(isEnable: Boolean, pos: String) {
		sendResultCode(VDVRRespondID.pause_car_door_1)
	}

	/**
	 * 车门微调
	 * @parameter value 0 调大一点 1 调小一点
	 * @return
	 */
	fun adjustLittleDoor(value: String, pos: String) {
		if (value == VehicleConstant.VR_TEAILGATE_RAISE_LITTLE) {
			sendResultCode(VDVRRespondID.open_door_little_1)
		} else {
			sendResultCode(VDVRRespondID.close_door_little_1)
		}
	}

	/**
	 * 车门调整到具体数值
	 * @parameter value: String, pos: String
	 * @return null
	 */
	fun openDegreeDoor(value: String, pos: String) {
		sendResultCode(VDVRRespondID.adjust_door_to_number_1)
	}

	/**
	 * 车门开大到相对数值
	 * @parameter value: String, pos: String
	 * @return null
	 */
	fun reOpenDegreeDoor(value: String, pos: String) {
		sendResultCode(VDVRRespondID.open_door_by_number_1)
	}

	/**
	 * 车门关小到相对数值
	 * @parameter value: String, pos: String
	 * @return null
	 */
	fun reCloseDegreeDoor(value: String, pos: String) {
		sendResultCode(VDVRRespondID.close_door_by_number_1)
	}

	/**
	 * 设置车门最大开度为指定值
	 * @parameter value: String, pos: String
	 * @return null
	 */
	fun openDegreeValueDoor(value: String, pos: String) {
		sendResultCode(VDVRRespondID.set_maximum_of_door_opening_to_number_1)
	}

	/**
	 *  暂停打开和关闭车窗
	 * @parameter isEnable: Boolean, pos: String
	 * @return null
	 */
	fun temporaryControlWindow(isEnable: Boolean, pos: String) {
		sendResultCode(VDVRRespondID.Pause_opening_or_closing_the_car_window_1)
	}


	//兜底回复
	//                        设置仪表屏显示内容  关闭仪表屏显示内容
	fun setDashboardDisplay(value: String) {
		when (value.toInt()) {
			0 -> sendResultCode(VDVRRespondID.open_instrument_show_content_1)
			1 -> sendResultCode(VDVRRespondID.open_instrument_show_content_1)
			2 -> sendResultCode(VDVRRespondID.open_instrument_show_content_1)
			3 -> sendResultCode(VDVRRespondID.open_instrument_show_content_1)
			4 -> sendResultCode(VDVRRespondID.open_instrument_show_content_1)
			5 -> sendResultCode(VDVRRespondID.open_instrument_show_content_1)
			6 -> sendResultCode(VDVRRespondID.close_instrument_show_content_1)
		}
	}

	//                        切换仪表显示内容，无指定值
	fun switchDashboardDisplay() {
		sendResultCode(VDVRRespondID.switch_instrument_show_content_1)
	}

	//                                打开仪表背光/关闭仪表背光
	fun openDashboardBacklight(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_instrument_backlight_1)
		} else {
			sendResultCode(VDVRRespondID.close_instrument_backlight_1)
		}
	}

	//	解闭锁声光设置 (静音模式)
	fun setSunVoice(value: String) {
		sendResultCode(VDVRRespondID.set_lock_or_unlock_sound_and_lamp_2_1)
	}

	//                        打开悬架指定模式/关闭悬架指定模式
	fun setSuspensionMode(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_suspension_mode_1)
		} else {
			sendResultCode(VDVRRespondID.close_suspension_mode_1)
		}
	}

	//                                悬架模式设置为xxx
	fun setSuspensionMode(value: String) {
		when (value.toInt()) {
			0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11 -> sendResultCode(VDVRRespondID.open_suspension_mode_2)
		}
	}

	//                        切换悬架模式，无指定值
	fun switchSuspensionMode() {
		sendResultCode(VDVRRespondID.switch_suspension_mode_1)
	}

	//                                打开悬架维修
//                        关闭悬架维修
	fun setSuspensionRepair(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_suspension_repair_2)
		} else {
			sendResultCode(VDVRRespondID.close_suspension_repair_2)
		}
	}

	//                                打开高速悬架智控
//                        关闭高速悬架智控
	fun setHighwaySuspensionControl(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_high_speed_suspension_intelligent_contro_2)
		} else {
			sendResultCode(VDVRRespondID.close_high_speed_suspension_intelligent_contro_2)
		}
	}

	//                                打开悬架智能预瞄
//                        关闭悬架智能预瞄
	fun setSuspensionAiming(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_suspension_intelligent_preaiming_3)
		} else {
			sendResultCode(VDVRRespondID.close_suspension_intelligent_preaiming_3)
		}
	}

	//                                悬架高度设置
	fun setSuspensionHeight(value: String) {
		when (value.toInt()) {
			0, 1, 2, 3, 4 -> sendResultCode(VDVRRespondID.set_suspension_height_2)
		}
	}

	//                        悬架高度调高一点
//                                悬架高度调低一点
//                        悬架高度调到顶
//                                悬架高度调到底
	fun setSuspensionHeightMax_Min(value: String) {
		// 悬架高度调XX：XX=value=【0=高一点，1=低一点，2=高档，3=中档，4=低档，5=最高，6=最低】
		when (value.toInt()) {
			0 -> sendResultCode(VDVRRespondID.raise_suspension_little_2)
			1 -> sendResultCode(VDVRRespondID.lower_suspension_little_2)
			5 -> sendResultCode(VDVRRespondID.adjust_suspension_height_to_top_2)
			6 -> sendResultCode(VDVRRespondID.adjust_suspension_height_to_bottom_2)
		}
	}

	//                        悬架高度调到指定挡位
	fun setSuspensionHeightToGears(value: String) {
		when (value.toInt()) {
			2 -> sendResultCode(VDVRRespondID.adjust_suspension_height_to_gear_2)
			3 -> sendResultCode(VDVRRespondID.adjust_suspension_height_to_gear_2)
			4 -> sendResultCode(VDVRRespondID.adjust_suspension_height_to_gear_2)
		}
	}

	//                                悬架高度调到指定数值
	fun setSuspensionHeightToNumber(value: String) {
		sendResultCode(VDVRRespondID.adjust_suspension_height_to_number_2)
	}

	//                        悬架舒适度设置
	fun setSuspensionComfort(value: String) {
		sendResultCode(VDVRRespondID.set_suspension_comfort_2)
	}

	//                                悬架调软一点
//                        悬架调硬一点
//                                悬架调最软
//                        悬架调最硬
	fun setSuspensionSoft_Hard(value: String) {
		when (value.toInt()) {
			0 -> sendResultCode(VDVRRespondID.soft_suspension_little_2)
			1 -> sendResultCode(VDVRRespondID.hard_suspension_little_2)
			5 -> sendResultCode(VDVRRespondID.adjust_suspension_to_softest_2)
			6 -> sendResultCode(VDVRRespondID.adjust_suspension_to_hardest_2)
		}
	}

	/**
	 * 车门开关速度设置为指定值
	 * @parameter value: String, pos: String
	 * @return null
	 */
	fun openSpeedDoor(value: String, pos: String) {
		sendResultCode(VDVRRespondID.set_door_speed_to_gear_1)
	}

	/**
	 * 暂停打开关闭后尾门
	 * @parameter isEnable: Boolean
	 * @return null
	 */
	fun temporaryControlTrunk(isEnable: Boolean) {
		sendResultCode(VDVRRespondID.pause_tailgate_1)
	}

	/**
	 * 打开电动尾门开关
	 * @parameter flag: Boolean
	 * @return null
	 */
	fun tailGateElectric(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_electric_tailgate_switch_1)
		} else {
			sendResultCode(VDVRRespondID.open_electric_tailgate_switch_4)
		}
	}

	/**
	 * 设置尾门开启方式
	 * @parameter value: String
	 * @return null
	 */
	fun openMethodTailGate(value: String) {
		sendResultCode(VDVRRespondID.set_tailgate_opening_method_1)
	}

	/**
	 * 尾门调高到具体数值
	 * @parameter value: String
	 * @return null
	 */
	fun adjustUpHeightTailGate(value: String) {
		sendResultCode(VDVRRespondID.raise_tailgate_height_by_number_5)
	}

	/**
	 * 尾门调低到具体数值
	 * @parameter value: String
	 * @return null
	 */
	fun adjustDownLittleHeightTailGate(value: String) {
		sendResultCode(VDVRRespondID.lower_tailgate_height_by_number_5)
	}

	// 尾门高度微调
	//0:调高一点 1:调低一点 5： 最高 6： 最低
	fun adjustModeHeightTailGate(value: String) {
		when (value) {
			VehicleConstant.VR_TEAILGATE_RAISE_LITTLE -> sendResultCode(VDVRRespondID.lower_tailgate_low_little_4)
			VehicleConstant.VR_TEAILGATE_LOW_LITTLE -> sendResultCode(VDVRRespondID.lower_tailgate_height_little_5)
			VehicleConstant.VR_TEAILGATE_TO_HIGHEST -> sendResultCode(VDVRRespondID.adjust_tailgate_height_to_highest_5)
			VehicleConstant.VR_TEAILGATE_TO_LOWEST -> sendResultCode(VDVRRespondID.adjust_tailgate_height_to_lowest_5)
		}
	}

	/**
	 * 尾门高度调到具体数值
	 * @parameter value: String
	 * @return null
	 */
	fun adjustHeightTailGate(value: String) {
		sendResultCode(VDVRRespondID.adjust_tailgate_height_to_number_8)
	}

	/**
	 * 尾门感应
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun tailGateInduction(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_smart_key_tailgate_sensing_3)
		} else {
			sendResultCode(VDVRRespondID.close_smart_key_tailgate_sensing_3)
		}
	}

	/**
	 * 打开智能钥匙感应尾门
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun smartKeyTailGate(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_smart_key_tailgate_sensing_3)
		} else {
			sendResultCode(VDVRRespondID.close_smart_key_tailgate_sensing_3)
		}
	}

	/**
	 * 电动尾翼
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun controlTailWing(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_electric_tail_wing_3)
		} else {
			sendResultCode(VDVRRespondID.close_electric_tail_wing_3)
		}
	}

	/**
	 * 电动尾翼自动模式
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun autoModeTailWing(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.electric_tail_wing_automode_3)
		}
	}

	/**
	 * 电动尾翼高度调节
	 * @parameter value: String
	 * @return
	 */
	fun adjustHeightTailWing(value: String) {
		sendResultCode(VDVRRespondID.adjust_electric_tail_wing_height_4)
	}

	/**
	 * 电动尾翼角度调节为指定角度
	 * @parameter value: String
	 * @return null
	 */
	fun adjustAngleTailWing(value: String) {
		sendResultCode(VDVRRespondID.adjust_electric_tail_wing_angle_to_number_1)
	}

	/**
	 * 尾翼角度微调
	 * @parameter value 0: 调高一点 1:调低一点
	 * @return null
	 */
	fun adjustModeAngleTailWing(value: String) {
		when (value) {
			VehicleConstant.VR_TEAILGATE_RAISE_LITTLE -> sendResultCode(VDVRRespondID.raise_electric_tail_wing_height_little_4)
			VehicleConstant.VR_TEAILGATE_LOW_LITTLE -> sendResultCode(VDVRRespondID.lower_electric_tail_wing_height_little_4)
		}
	}

	/**
	 * 电动尾翼高度调高调低
	 * @parameter value 0: 调高一点 1:调低一点
	 * @return null
	 */
	fun adjustModeHeightTailWing(value: String) {
		when (value) {
			VehicleConstant.VR_TEAILGATE_RAISE_LITTLE -> sendResultCode(VDVRRespondID.raise_electric_tail_wing_height_little_4)
			VehicleConstant.VR_TEAILGATE_LOW_LITTLE -> sendResultCode(VDVRRespondID.lower_electric_tail_wing_height_little_4)
		}
	}

	/**
	 * 打开电动尾翼迎宾
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun welcomeTailWing(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_electric_tail_wing_welcome_ceremony_1)
		} else {
			sendResultCode(VDVRRespondID.close_electric_tail_wing_welcome_ceremony_1)
		}
	}

	/**
	 * 发光行李架
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun luminousBaggage(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_illuminated_luggage_rack_1)
		} else {
			sendResultCode(VDVRRespondID.close_illuminated_luggage_rack_1)
		}
	}

	/**
	 * 发光行李架颜色设置
	 * @parameter value: String
	 * @return null
	 */
	fun colorLuminousBaggage(value: String) {
		sendResultCode(VDVRRespondID.set_luminous_luggage_rack_color_1)
	}

	/**
	 * 切换发光行李架颜色，无指定值
	 * @parameter value: String
	 * @return null
	 */
	fun changeColorLuminousBaggage(value: String) {
		sendResultCode(VDVRRespondID.switch_luminous_luggage_rack_color_1)
	}

	/**
	 * 发光行李架灯光调节
	 * @parameter value: String
	 * @return null
	 */
	fun lightLuminousBaggage(value: String) {
		when (value) {
			VehicleConstant.VR_TEAILGATE_RAISE_LITTLE -> sendResultCode(VDVRRespondID.raise_luminous_luggage_rack_brightness_little_1)
			VehicleConstant.VR_TEAILGATE_LOW_LITTLE -> sendResultCode(VDVRRespondID.lower_luminous_luggage_rack_brightness_little_1)
		}
	}

	/**
	 * 引擎盖
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun hood(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_engine_hood_1)
		} else {
			sendResultCode(VDVRRespondID.close_engine_hood_1)
		}
	}

	/**
	 * 充电口盖
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun chargingCover(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_charging_port_4)
		} else {
			sendResultCode(VDVRRespondID.close_charging_port_5)
		}
	}

	/**
	 * 尾门开启上限高度
	 * @parameter value: String
	 * @return null
	 */
	fun setHeightTailGate(value: String) {
		sendResultCode(VDVRRespondID.set_tailgate_opening_upper_limit_cm_2)
	}

	/**
	 * hud
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun hud(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_hud_3)
		} else {
			sendResultCode(VDVRRespondID.close_hud_3)
		}
	}

	/**
	 * hud显示模式
	 * @parameter flag true ：打开 ； false ：关闭
	 * @return null
	 */
	fun hudDisplayMode(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_hud_display_mode_1)
		} else {
			sendResultCode(VDVRRespondID.close_hud_display_mode_1)
		}
	}

	/**
	 * HUD显示
	 */
	fun setHUDDisplay(value: String) {
		sendResultCode(VDVRRespondID.set_hud_specific_mode_4)
	}

	/**
	 * HUD恢复默认值
	 */
	fun restoreHUDDefault() {
		sendResultCode(VDVRRespondID.recover_hud_default_4)
	}

	/**
	 * 左HUD图片旋转
	 */
	fun setLeftHUDImageRotate(value: String) {
		sendResultCode(VDVRRespondID.adjust_rotation_of_hud_image_1)
	}

	/**
	 * 右HUD图片旋转
	 */
	fun setRightHUDImageRotate(value: String) {
		sendResultCode(VDVRRespondID.adjust_rotation_of_hud_image_1)
	}

	/**
	 * 关闭胎压监测
	 */
	fun setTirePressureMonitoring(voiceCommandState: Boolean) {
		sendResultCode(VDVRRespondID.close_tpms_1)
	}

	/**
	 * 打开/胎压校准功能
	 * @param voiceCommandState true:打开 false:关闭
	 */
	fun setTirePressureCalibration(voiceCommandState: Boolean) {
		when (voiceCommandState) {
			true -> sendResultCode(VDVRRespondID.open_tire_pressure_calibration_1)
			false -> sendResultCode(VDVRRespondID.close_tire_pressure_calibration_1)
		}
	}

	//	调节hud高度
//	【0=高一点，1=低一点，2=高档，3=中档，4=低档，5=最高，6=最低】
	fun adjustHeightHud(value: String) {
		when (value.toInt()) {
			0 -> sendResultCode(VDVRRespondID.raise_hud_height_little_6)
			5 -> sendResultCode(VDVRRespondID.raise_hud_height_top_4)
			1 -> sendResultCode(VDVRRespondID.lower_hud_height_little_6)
			6 -> sendResultCode(VDVRRespondID.lower_hud_height_bottom_4)
		}
	}
	//hud高度调低具体数值
	fun adjustDownLittleHeightHud(value: String) {
		sendResultCode(VDVRRespondID.lower_hud_brightness_by_number_6)
	}
	//hud高度调高具体数值
	fun adjustUpHeightHud(value: String) {
		sendResultCode(VDVRRespondID.adjust_hud_height_to_number_5)
	}
	//hud高度自动调节
	fun autoAdjustHeightHud(flag: Boolean) {
		sendResultCode(VDVRRespondID.hud_height_automatic_adjustment_3)
	}
	//hud倾斜度调到具体数值
	fun adjustAngleHud(value: String) {
		sendResultCode(VDVRRespondID.adjust_hud_inclination_to_number_1)
	}
	//hud倾斜度向指定方向调节一点
	fun adjustModeAngleHud(value: String) {
		sendResultCode(VDVRRespondID.adjust_hud_inclination_little_1)
	}
	//hud倾斜度往指定方向调到极值
	fun adjustModeAngleHudToExtreme(value: String) {
		sendResultCode(VDVRRespondID.adjust_hud_inclination_to_extremum_1)
	}
	//hud位置横向调节
	fun adjustPositionHud(value: String) {
		sendResultCode(VDVRRespondID.adjust_hud_position_transversely_1)
	}
	//hud位置向指定方向调节到极值
	fun adjustPositionHudToExtreme(value: String) {
		sendResultCode(VDVRRespondID.adjust_hud_position_transversely_to_extremum_1)
	}
	//设置触摸开关延迟时间
	fun setTouchSwitchDelayTime(value: String) {
		sendResultCode(VDVRRespondID.set_touch_switch_delayed_time_1)
	}
	//按键亮度调到高中低挡
	fun adjustHudBrightnessLevel(value: String) {
		sendResultCode(VDVRRespondID.adjust_button_brightness_to_gear_1)
	}

	/**
	 * hud亮度调高一点,低一点,最高,最低
	 * @param value 0:调高 1:调低 5:最高 6:最低
	 */
	fun adjustHudBrightnessValue(value: String) {
		if (value != "") {
			when (value.toInt()) {
				VehicleConstant.AR_PARAM_High -> sendResultCode(VDVRRespondID.raise_hud_brightness_little_6)
				VehicleConstant.AR_PARAM_Low -> sendResultCode(VDVRRespondID.lower_hud_brightness_little_6)
				VehicleConstant.AR_PARAM_Max -> sendResultCode(VDVRRespondID.adjust_hud_brightness_to_max_4)
				VehicleConstant.AR_PARAM_Min -> sendResultCode(VDVRRespondID.adjust_hud_brightness_to_min_4)
				else -> sendResultCode(VDVRRespondID.adjust_hud_brightness_to_number_5)
			}
		}

	}

	/**
	 * hud亮度调高具体数值
	 */
	fun adjustUpHudBrightness(value: String) {
		if (value != "") {
			sendResultCode(VDVRRespondID.raise_hud_brightness_by_number_6)
		}
	}

	/**
	 * hud亮度调低具体数值
	 */
	fun adjustDownHudBrightness(value: String) {
		if (value != "") {
			sendResultCode(VDVRRespondID.lower_hud_brightness_to_number_6)
		}

	}

	/**
	 * hud亮度调到具体数值
	 */
	fun adjustHudBrightness(value: String) {
		if (value != "") {
			sendResultCode(VDVRRespondID.adjust_hud_brightness_to_number_5)
		}

	}

	/**
	 * 打开/关闭差速锁
	 * @param voiceCommandState true:打开 false:关闭
	 */
	fun setDifferentialLock(voiceCommandState: Boolean) {
		when (voiceCommandState) {
			true -> sendResultCode(VDVRRespondID.open_differential_lock_1)
			false -> sendResultCode(VDVRRespondID.close_differential_lock_1)
		}
	}

	/**
	 * 打开/关闭能量回收
	 * @param voiceCommandState true:打开 false:关闭
	 */
	fun setEngineRecovery(voiceCommandState: Boolean) {
		when (voiceCommandState) {
			true -> sendResultCode(VDVRRespondID.open_energy_recuperation_3)
			false -> sendResultCode(VDVRRespondID.close_energy_recuperation_3)
		}
	}

	/**
	 * 能源模式设置为xxx
	 * @param value 能量模式
	 */
	fun setEnergyMode(value: String) {
		when (value) {
			"1", "2" -> sendResultCode(VDVRRespondID.set_energy_mode_to_number_3)
			"0" -> {
				setCarMode(value)
			}
		}
	}

	/**
	 * 切换能源模式
	 */
	fun switchEnergyMode(voiceCommandState: Boolean) {
		sendResultCode(VDVRRespondID.set_energy_mode_to_number_3)
	}

	/**
	 * 打开/关闭强制EV模式
	 * @param voiceCommandState true:打开 false:关闭
	 */
	fun setForceEVMode(voiceCommandState: Boolean) {
		when (voiceCommandState) {
			true -> sendResultCode(VDVRRespondID.open_forced_EV_mode_3)
			false -> sendResultCode(VDVRRespondID.close_forced_EV_mode_3)
		}
	}

	/**打开、关闭保存驾驶偏好功能
	 * @param flag true:打开 false:关闭
	 */
	fun setSaveDrivingPreference(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_save_driving_preference_1)
		} else {
			sendResultCode(VDVRRespondID.close_save_driving_preference_1)
		}
	}

	/**打开、关闭便捷装载
	 * @param flag true:打开 false:关闭
	 */
	fun setConvenientLoad(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_convenient_load_4)
		} else {
			sendResultCode(VDVRRespondID.close_convenient_load_4)
		}
	}

	/**便捷下车
	 * @param flag true:打开 false:关闭
	 */
	fun setConvenientGetOff(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_convenient_disembarkation_4)
		} else {
			sendResultCode(VDVRRespondID.close_convenient_disembarkation_4)
		}
	}

	/**调低、抬高车身高度
	 * @param value 0:调低 1:抬高
	 */
	fun setAdjustCarHeight(value: String) {
		when (value.toInt()) {
			CarSettingConstant.ADJUST_CAR_HEIGHT_DOWN -> sendResultCode(VDVRRespondID.adjust_car_body_height_4)
			CarSettingConstant.ADJUST_CAR_HEIGHT_UP -> sendResultCode(VDVRRespondID.raise_car_body_height_4)
		}
	}

	/**打开、关闭自动便捷上车
	 * @param flag true:打开 false:关闭
	 */
	fun setAutoConvenientGetOn(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_automatic_convenient_boarding_4)
		} else {
			sendResultCode(VDVRRespondID.close_automatic_convenient_boarding_4)
		}
	}

	/**打开、关闭电动侧踏板
	 * @param flag true:打开 false:关闭
	 */
	fun setElectricSideBrake(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_electric_side_pedal_1)
		} else {
			sendResultCode(VDVRRespondID.close_electric_side_pedal_1)
		}
	}

	/**打开、关闭N挡防误触
	 * @param flag true:打开 false:关闭
	 */
	fun setNgearPrevention(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_N_gear_to_prevent_false_touch_1)
		} else {
			sendResultCode(VDVRRespondID.close_N_gear_to_prevent_false_touch_1)
		}
	}

	/**打开、关闭防油门误踩
	 * @param flag true:打开 false:关闭
	 */
	fun setPreventionOfOilPistonMistake(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_accelerator_anti_false_step_1)
		} else {
			sendResultCode(VDVRRespondID.close_accelerator_anti_false_step_1)
		}
	}

	/**关闭指定驾驶模式、车辆模式
	 * @param value 0:纯电 1:舒适混动 2:舒适 3:风云GT 4:雨雪 5:自定义 6:个性化
	 */
	fun setCloseSpecifiedDrivingMode(value: String) {
		if (value.toInt() == CarSettingConstant.CAR_MODE_PURE_ELECTRIC) sendResultCode(
			VDVRRespondID.close_driving_mode_1,
			"纯电"
		)
		if (value.toInt() == CarSettingConstant.CAR_MODE_COMFORT_MIX) sendResultCode(
			VDVRRespondID.close_driving_mode_1,
			"舒适混动"
		)
		if (value.toInt() == CarSettingConstant.CAR_MODE_COMFORT) sendResultCode(
			VDVRRespondID.close_driving_mode_1,
			"舒适"
		)
		if (value.toInt() == CarSettingConstant.CAR_MODE_FUTURE_GT) sendResultCode(
			VDVRRespondID.close_driving_mode_1,
			"风云GT"
		)
		if (value.toInt() == CarSettingConstant.CAR_MODE_SNOW) sendResultCode(
			VDVRRespondID.close_driving_mode_1,
			"雨雪"
		)
		if (value.toInt() == CarSettingConstant.CAR_MODE_CUSTOM) sendResultCode(
			VDVRRespondID.close_driving_mode_1,
			"自定义"
		)
		if (value.toInt() == CarSettingConstant.CAR_MODE_PERSONALIZATION) sendResultCode(
			VDVRRespondID.close_driving_mode_1,
			"个性化"
		)
	}

	/**关闭车辆模式无指定值
	 * 无指定值
	 */
	fun setCloseSpecifiedDrivingModeNoValue() {
		sendResultCode(VDVRRespondID.close_driving_mode_2)
	}

	/**打开、关闭驾驶模式记忆
	 * @param flag true:打开 false:关闭
	 */
	fun setDrivingModeMemory(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_driving_mode_memory_1)
		} else {
			sendResultCode(VDVRRespondID.close_driving_mode_memory_1)
		}
	}

	/**打开、关闭转向助力模式
	 * @param flag true:打开 false:关闭
	 */
	fun setTurnAssistMode(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.switch_eps_mode_1)
		} else {
			sendResultCode(VDVRRespondID.close_eps_mode_1)
		}
	}

	/**切换转向助力模式，无指定值
	 * 无指定值
	 */
	fun setTurnAssistModeNoValue() {
		sendResultCode(VDVRRespondID.switch_eps_mode_1)
	}

	/**打开、关闭转向助力关联驾驶模式
	 * @param flag true:打开 false:关闭
	 */
	fun setTurnAssistRelatedDrivingMode(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_eps_associated_with_driving_mode_1)
		} else {
			sendResultCode(VDVRRespondID.close_eps_associated_with_driving_mode_1)
		}
	}

	/**预测型碰撞报警系统等级设置
	 * @param value 0:调高 1:调低 2:最高 3:最低 4:高档 5:中档 6:低档
	 */
	fun setPredictionWarningLevel(value: String) {
		when (value.toInt()) {
			CarSettingConstant.LEVEL_HIGH -> sendResultCode(VDVRRespondID.raise_predictive_collision_warning_level_little_1)
			CarSettingConstant.LEVEL_LOW -> sendResultCode(VDVRRespondID.lower_predictive_collision_warning_level_little_1)
			CarSettingConstant.LEVEL_MAX -> sendResultCode(VDVRRespondID.set_predictive_collision_warning_level_to_gear_1)
			CarSettingConstant.LEVEL_MIN -> sendResultCode(VDVRRespondID.set_predictive_collision_warning_level_to_gear_1)
			CarSettingConstant.LEVEL_HIGH_D -> sendResultCode(VDVRRespondID.set_predictive_collision_warning_level_to_gear_1)
			CarSettingConstant.LEVEL_MID_D -> sendResultCode(VDVRRespondID.adjust_predictive_collision_warning_level_to_max_1)
			CarSettingConstant.LEVEL_LOW_D -> sendResultCode(VDVRRespondID.adjust_predictive_collision_warning_level_to_min_1)
		}
	}

	/**打开、关闭交通标示识别TSR
	 * @param flag
	 */
	fun setTrafficSignRecognition(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_tsr_1)
		} else {
			sendResultCode(VDVRRespondID.close_tsr_1)
		}
	}

	/**打开、关闭动态交通场景显示
	 * @param flag
	 */
	fun setDynamicTrafficSceneDisplay(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_dynamic_traffic_scene_display_1)
		} else {
			sendResultCode(VDVRRespondID.close_dynamic_traffic_scene_display_1)
		}
	}

	//	行人低速提示音设置成指定话术
	fun setPedestrianLowSpeedTips(value: String) {
		sendResultCode(VDVRRespondID.set_pedestrian_low_speed_sound_content_1)
	}

	/**打开、关闭行人安全辅助
	 * @param flag
	 */
	fun setPedestrianSafetyAssistance(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_pedestrian_safety_assistance_1)
		} else {
			sendResultCode(VDVRRespondID.close_pedestrian_safety_assistance_1)
		}
	}

	/**行人安全辅助模式设置
	 * @param value
	 */
	fun setPedestrianSafetyAssistanceMode(value: String) {
		sendResultCode(VDVRRespondID.set_pedestrian_safety_assistance_mode_1)
	}

	/**打开、关闭起步提醒
	 * @param flag
	 */
	fun setStartUpReminder(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_departure_reminder_system_1)
		} else {
			sendResultCode(VDVRRespondID.close_departure_reminder_system_1)
		}
	}

	/**设置Ibooster模式为指定值
	 * @param value
	 */
	fun setIboosterMode(value: String) {
		sendResultCode(VDVRRespondID.set_Ibooster_mode_1)
	}

	/**打开、关闭穿行制动
	 * @param flag
	 */
	fun setTreadBrake(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_cross_traffic_brake_1)
		} else {
			sendResultCode(VDVRRespondID.close_cross_traffic_brake_1)
		}
	}

	/**打开、关闭盲区影像画质增强
	 * @param flag
	 */
	fun setBlindAreaImageQualityEnhancement(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_blind_spot_image_quality_enhancement_1)
		} else {
			sendResultCode(VDVRRespondID.close_blind_spot_image_quality_enhancement_1)
		}
	}

	/**打开、关闭主动车速限制
	 * @param flag
	 */
	fun setActiveSpeedLimit(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_speed_limit_1)
		} else {
			sendResultCode(VDVRRespondID.close_speed_limit_1)
		}
	}

	/**主动车速限制调到具体数值
	 * @param value
	 */
	fun setActiveSpeed(value: String) {
		sendResultCode(VDVRRespondID.adjus_limit_speed_to_number_1)
	}

	/**车速限制调大、调小一点
	 * @param value
	 */
	fun setActiveSpeedLimit(value: String) {
		when (value.toInt()) {
			0 -> sendResultCode(VDVRRespondID.raise_limit_speed_little_1)
			1 -> sendResultCode(VDVRRespondID.lower_limit_speed_little_1)
		}
	}

	/**车身电子稳定控制系统模式设置
	 * @param value
	 */
	fun setElectronicStabilityControlSystemMode(value: String) {
		DialogNavigationUtils.launchMainActivity(
			context,
			MainActivity.MainTabIndex.DRIVE,
			CommonConst.INVALID_DIALOG,
			CommonConst.DIALOG_OPEN
		)
		sendResultCode(VDVRRespondID.set_esc_mode_1)
	}

	/**切换电子车身稳定系统模式，无指定值
	 * @return
	 */
	fun setElectronicStabilityControlSystemModeNoValue() {
		DialogNavigationUtils.launchMainActivity(
			context,
			MainActivity.MainTabIndex.DRIVE,
			CommonConst.INVALID_DIALOG,
			CommonConst.DIALOG_OPEN
		)
		sendResultCode(VDVRRespondID.switch_esc_mode_1)
	}

	/**打开、关闭动态方向稳定辅助
	 * @param flag
	 */
	fun setDynamicDirectionStabilityAssistance(flag: Boolean) {
		if (flag) {
			DialogNavigationUtils.launchMainActivity(
				context,
				MainActivity.MainTabIndex.DRIVE,
				CommonConst.INVALID_DIALOG,
				CommonConst.DIALOG_OPEN
			)
			sendResultCode(VDVRRespondID.open_dst_1)
		} else {
			DialogNavigationUtils.launchMainActivity(
				context,
				MainActivity.MainTabIndex.DRIVE,
				CommonConst.INVALID_DIALOG,
				CommonConst.DIALOG_OPEN
			)
			sendResultCode(VDVRRespondID.close_dst_1)
		}
	}

	/**
	 * 安全驾驶模式开关
	 */
	fun setSafetyDrivingMode(flag: Boolean) {
		// TODO: 拉起功能界面
		if (flag) {
			sendResultCode(VDVRRespondID.open_safe_driving_mode_1)
		} else {
			sendResultCode(VDVRRespondID.close_safe_driving_mode_1)
		}
	}

	/**
	 * 安全驾驶提醒开关
	 */
	fun setSafetyDrivingReminder(flag: Boolean) {
		// TODO: 拉起功能界面
		if (flag) {
			sendResultCode(VDVRRespondID.open_safe_drive_warning_1)
		} else {
			sendResultCode(VDVRRespondID.close_safe_drive_warning_1)
		}
	}

	/**
	 * 行车锁屏开关
	 */
	fun setDrivingLockScreen(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_driving_lock_screen_1)
		} else {
			sendResultCode(VDVRRespondID.close_driving_lock_screen_1)
		}
	}

	/**
	 * READY开关
	 */
	fun setReady(flag: Boolean) {
		// TODO:拉起功能界面
		if (flag) {
			sendResultCode(VDVRRespondID.open_ready_1)
		} else {
			sendResultCode(VDVRRespondID.close_ready_1)
		}
	}


	/**
	 *  越野热管理开关
	 */
	fun setThermalMode(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_off_road_thermal_management_1)
		} else {
			sendResultCode(VDVRRespondID.close_off_road_thermal_management_1)
		}
	}

	/**
	 * 行车语音播报开关
	 */
	fun setDrivingBroadcast(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_driving_voice_announcement_1)
		} else {
			sendResultCode(VDVRRespondID.close_driving_voice_announcement_1)
		}
	}

	/**
	 * NFC开关
	 */
	fun setNFC(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.open_nfc_1)
		} else {
			sendResultCode(VDVRRespondID.close_nfc_1)
		}
	}

	/**
	 *  极致节能模式开关
	 */
	fun setUltimateSavingMode(isOpen: Boolean) {
//        if (isOpen) {
//            if (是否在对应界面) {
//                sendResultCode(VDVRRespondID.open_extreme_energy_saving_mode_3)
//            } else {
//                // TODO: 打开界面
//                sendResultCode(VDVRRespondID.open_extreme_energy_saving_mode_2)
//            }
//        } else {
//            if (是否在对应界面) {
//                // TODO: 关闭界面
//                sendResultCode(VDVRRespondID.close_extreme_energy_saving_mode_1)
//            } else {
//                sendResultCode(VDVRRespondID.close_extreme_energy_saving_mode_3)
//            }
//        }
	}

	/**
	 * 设置车模颜色
	 */
	fun setCarColor(value: String) {
		sendResultCode(VDVRRespondID.set_car_body_color_4)
	}

	/**
	 * 后排检测开关
	 */
	fun setRearSeatDetection(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_back_row_detection_1)
		} else {
			sendResultCode(VDVRRespondID.close_back_row_detection_1)
		}
	}

	/**
	 * 调光玻璃顶开关
	 */
	fun setDimmingGlassTop(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_dimming_glass_top_1)
		} else {
			sendResultCode(VDVRRespondID.close_dimming_glass_top_1)
		}
	}

	/**
	 * 设置调光玻璃顶为指定模式
	 */
	fun setGlassTopMode(value: String) {
		sendResultCode(VDVRRespondID.set_dimming_glass_top_mode_1)
	}

	/**
	 * 切换调光玻璃顶模式，无指定值
	 */
	fun changeGlassTopMode() {
		sendResultCode(VDVRRespondID.switch_dimming_glass_top_mode_1)
	}

	/**
	 * 设置调光玻璃顶为具体颜色
	 */
	fun setGlassTopColor(value: String) {
		sendResultCode(VDVRRespondID.set_dimming_glass_top_color_1)
	}

	/**
	 * 切换调光玻璃顶颜色，无指定值
	 */
	fun changeGlassTopColor() {
		sendResultCode(VDVRRespondID.switch_dimming_glass_top_color_1)
	}

	/**
	 * 调光玻璃顶透明度调节
	 */
	fun adjustModeGlassTopAlpha(value: String) {
		when (value.toInt()) {
			0 -> sendResultCode(VDVRRespondID.raise_dimming_glass_top_transparency_little_1)
			1 -> sendResultCode(VDVRRespondID.lower_dimming_glass_top_transparency_little_1)
			2 -> sendResultCode(VDVRRespondID.adjust_dimming_glass_top_transparency_to_max_1)
			3 -> sendResultCode(VDVRRespondID.adjust_dimming_glass_top_transparency_to_min_1)
		}
	}

	/**
	 * 调光玻璃顶透明度调到指定数值
	 */
	fun adjustGlassTopAlpha(value: String) {
		sendResultCode(VDVRRespondID.adjust_dimming_glass_top_transparency_to_number_1)
	}

	/**
	 *调光玻璃顶透明度调高相对数值
	 */
	fun adjustUpGlassTopAlpha(value: String) {
		sendResultCode(VDVRRespondID.raise_dimming_glass_top_transparency_by_number_1)
	}

	/**
	 * 调光玻璃顶透明度调低相对数值
	 */
	fun adjustDownGlassTopAlpha(value: String) {
		sendResultCode(VDVRRespondID.lower_dimming_glass_top_transparency_by_number_1)
	}

	/**
	 * 调光玻璃顶透明度调到指定挡位
	 */
	fun adjustGlassTopBrightness(value: String) {
		sendResultCode(VDVRRespondID.adjust_dimming_glass_top_transparency_to_gear_1)
	}

	/**
	 * 调光玻璃顶亮度调高具体数值
	 */
	fun adjustUpGlassTopBrightness(value: String) {
		sendResultCode(VDVRRespondID.raise_dimming_glass_top_brightness_by_number_1)
	}

	/**
	 * 调光玻璃顶亮度调高一点
	 */
	fun adjustModeGlassTopBrightness(value: String) {
		sendResultCode(VDVRRespondID.raise_dimming_glass_top_brightness_little_1)
	}

	/**
	 * 调光玻璃顶亮度调低具体数值
	 */
	fun adjustDownGlassTopBrightness(value: String) {
		sendResultCode(VDVRRespondID.lower_dimming_glass_top_brightness_by_number_1)
	}

	/**
	 * 控制顶棚开关
	 */
	fun setCeiling(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_headliner_1)
		} else {
			sendResultCode(VDVRRespondID.close_headliner_1)
		}
	}

	/**
	 * 顶棚调到具体数值
	 */
	fun adjustPercentageGlassTop(value: String) {
		sendResultCode(VDVRRespondID.adjust_headliner_to_number_1)
	}

	/**
	 * 顶棚开大/小一点
	 */
	fun adjustModeGlassTop(value: String) {
		when (value.toInt()) {
			0 -> sendResultCode(VDVRRespondID.raise_headliner_little_1)
			1 -> sendResultCode(VDVRRespondID.lower_headliner_little_1)
		}
	}

	/**
	 * 顶棚挡位调到具体挡位
	 */
	fun adjustValueGlassTop(value: String) {
		sendResultCode(VDVRRespondID.adjust_headliner_to_gear_1)
	}

	/**
	 * 内饰加热开关
	 */
	fun setInteriorHeating(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_interior_heat_1)
		} else {
			sendResultCode(VDVRRespondID.close_interior_heat_1)
		}
	}

	/**
	 * 内饰加热调高具体数值
	 */
	fun adjustUpIntHeating(value: String) {
		sendResultCode(VDVRRespondID.raise_interior_heat_by_number_1)
	}

	/**
	 * 内饰加热调高一点
	 */
	fun adjustModeIntHeating(value: String) {
		sendResultCode(VDVRRespondID.raise_interior_heat_little_1)
	}

	/**
	 * 内饰加热调低具体数值
	 */
	fun adjustDownIntHeating(value: String) {
		sendResultCode(VDVRRespondID.lower_interior_heat_by_number_1)
	}

	/**
	 * 内饰加热调到具体数值
	 */
	fun adjustIntHeating(value: String) {
		sendResultCode(VDVRRespondID.adjust_interior_heat_to_number_1)
	}

	/**
	 * 设防提示开关
	 */
	fun setDefenseReminder(isOpen: Boolean) {
		if (isOpen) {
			// TODO: 拉起界面
			sendResultCode(VDVRRespondID.open_fortify_reminder_1)
		} else {
			sendResultCode(VDVRRespondID.close_fortify_reminder_1)
		}
	}

	/**
	 * 光线传感器开关
	 */
	fun setLightSensor(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_light_sensor_1)
		} else {
			sendResultCode(VDVRRespondID.close_light_sensor_1)
		}
	}

	/**
	 * 背光关联调节开关
	 */
	fun setBackLightAssociationAdjustment(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_backlight_associate_adjustment_1)
		} else {
			sendResultCode(VDVRRespondID.close_backlight_associate_adjustment_1)
		}
	}

	/**
	 * 背光自动调节开关
	 */
	fun setBackLightAutoAdjustment(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_backlight_automatic_adjustment_1)
		} else {
			sendResultCode(VDVRRespondID.close_backlight_automatic_adjustment_1)
		}
	}

	/**
	 * 鼓风机延迟关闭开关
	 */
	fun setDelayedCloseFan(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_blower_delay_shutdown_1)
		} else {
			sendResultCode(VDVRRespondID.close_blower_delay_shutdown_1)
		}
	}

	/**
	 * 夜视开关
	 */
	fun setNightVision(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_night_vision_1)
		} else {
			sendResultCode(VDVRRespondID.close_night_vision_1)
		}
	}

	/**
	 * 倒车雷达开关
	 */
	fun setPdc(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_park_sensor_1)
		} else {
			sendResultCode(VDVRRespondID.close_park_sensor_1)
		}
	}

	/**
	 * 智能启停开关
	 */
	fun setIntelligentStartStop(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_intelligent_start_stop_1)
		} else {
			sendResultCode(VDVRRespondID.close_intelligent_start_stop_1)
		}
	}

	/**
	 * 车辆启动/熄火
	 */
	fun controlVehicle(isStart: Boolean) {
		if (isStart) {
			sendResultCode(VDVRRespondID.start_car_1)
		} else {
			sendResultCode(VDVRRespondID.stall_car_1)
		}
	}

	/**
	 * 电子手刹开关
	 */
	fun electronicBrake(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_electronic_brake_1)
		} else {
			sendResultCode(VDVRRespondID.close_electronic_brake_1)
		}

	}

	/**
	 * 切换灯光秀，无指定值
	 */
	fun changeLightShow() {
		sendResultCode(VDVRRespondID.switch_light_show_1)
	}

	/**
	 * 灯光表达开关
	 */
	fun lightExpression(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_light_expression_1)
		} else {
			sendResultCode(VDVRRespondID.close_light_expression_1)
		}
	}

	/**
	 * 切换灯光表达，无指定值
	 */
	fun changeLightExpression() {
		sendResultCode(VDVRRespondID.switch_light_expression_1)
	}

	/**
	 * 灯光主题开关
	 */
	fun lightTheme(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_light_theme_1)
		} else {
			sendResultCode(VDVRRespondID.close_light_theme_1)
		}

	}

	/**
	 * 切换灯光主题，无指定值
	 */
	fun changeLightTheme() {
		sendResultCode(VDVRRespondID.switch_light_theme_1)
	}

	/**
	 * 灯光游戏开关
	 */
	fun lightGame(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_light_game_1)
		} else {
			sendResultCode(VDVRRespondID.close_light_game_1)
		}
	}

	/**
	 * 保养里程提醒距离设置
	 */
	fun setMaintainDistance(value: String) {
		sendResultCode(VDVRRespondID.set_maintenance_reminder_distance_1)
	}

	/**
	 * 车辆检测开关
	 */
	fun vehicleDetection(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_vehicle_check_1)
		} else {
			sendResultCode(VDVRRespondID.close_vehicle_check_1)
		}
	}

	/**
	 * 车况异常提醒开关
	 */
	fun abnormalReminder(isOpen: Boolean) {
		if (isOpen) {
			sendResultCode(VDVRRespondID.open_vehicleinfo_reminder_1)
		} else {
			sendResultCode(VDVRRespondID.close_vehicleinfo_reminder_1)
		}
	}

    /**
     * 打开/关闭车载冰箱
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setCarRefrigerator(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> sendResultCode(VDVRRespondID.open_car_fridge_3)
            false -> sendResultCode(VDVRRespondID.close_car_fridge_3)
        }
    }

    /**
     * 充电口盖解锁
     */
    fun setChargingCoverLock(voiceCommandState: Boolean) {
        sendResultCode(VDVRRespondID.Unlocking_charging_port_cover_1);
    }

    /**
     * 打开/关闭冰箱门
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setRefrigeratorDoor(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> sendResultCode(VDVRRespondID.Open_refrigerator_door_3)
            false -> sendResultCode(VDVRRespondID.close_refrigerator_door_3)
        }
    }

    /**
     * 打开/关闭冰箱电源
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setRefrigeratorPower(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> sendResultCode(VDVRRespondID.Turn_refrigerator_power_3)
            false -> sendResultCode(VDVRRespondID.Close_refrigerator_power_3)
        }
    }

    /**
     * 打开冰箱XX功能
     */
    fun setRefrigeratorMode(value: String) {
        sendResultCode(VDVRRespondID.Open_XX_function_refrigerator_1)
    }

    /**
     * 冰箱温度设置为XX
     */
    fun setRefrigeratorTemperature(value: String) {
        sendResultCode(VDVRRespondID.The_refrigerator_temperature_set_XX_1)
    }

    /**
     * 冰箱温度设置为最高/最低
     * 5=最高，6=最低
     */
    fun setRefrigeratorTemperatureUp(value: String) {
        when (value) {
            "5" -> sendResultCode(VDVRRespondID.Set_refrigerator_temperature_highest_1)
            "6" -> sendResultCode(VDVRRespondID.Set_refrigerator_temperature_lowest_1)
        }
    }

    /**
     * 打开/关闭离车冰箱持续工作
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setRefrigeratorContinuesWork(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> sendResultCode(VDVRRespondID.Open_car_refrigerator_continue_working_3)
            false -> sendResultCode(VDVRRespondID.close_car_refrigerator_continue_working_3)
        }
    }

    /**
     * 离车冰箱持续工作设置为XX
     */
    fun setRefrigeratorContinuesWorkTime(value: String) {
        sendResultCode(VDVRRespondID.close_car_refrigerator_continue_working_3)
    }

    /**
     * 打开/关闭冰箱儿童锁
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setRefrigeratorChildLock(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> sendResultCode(VDVRRespondID.Open_refrigerator_child_lock_3)
            false -> sendResultCode(VDVRRespondID.close_refrigerator_child_lock_3)
        }
    }

	/**
	 * 后排屏锁
	 * @param flag true:打开 false:关闭
	 */
	fun setScreenLock(flag: Boolean) {
		if (flag) {
			if (mQuickManager.rearScreenControl == CarSettingConstant.Lock_REAR_SCREEN) {
				sendResultCode(VDVRRespondID.lock_rear_screen_2)
			} else {
				mQuickManager.rearScreenControl = CarSettingConstant.Lock_REAR_SCREEN
				sendResultCode(VDVRRespondID.lock_rear_screen_1)
			}
		} else {
			if (mQuickManager.rearScreenControl == CarSettingConstant.Unlock_REAR_SCREEN) {
				sendResultCode(VDVRRespondID.unlock_rear_screen_1)
			} else {
				mQuickManager.rearScreenControl = CarSettingConstant.Unlock_REAR_SCREEN
				sendResultCode(VDVRRespondID.unlock_rear_screen_2)
			}
		}
	}

    /**
     * 打开/关闭限速辅助
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setSpeedLimitReminder(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> sendResultCode(VDVRRespondID.Turn_speed_limit_assist_2)
            false -> sendResultCode(VDVRRespondID.close_speed_limit_assist_2)
        }
    }

	/**
	 * 打开or关闭减速缓行
	 * @param flag true:打开 false:关闭
	 */
	fun setDecelerationSlowly(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.Open_Slow_down_2)
		} else {
			sendResultCode(VDVRRespondID.Close_Slow_down_1)
		}
	}

	/**
	 * 打开or关闭数字后视镜影像
	 * @param flag true:打开 false:关闭
	 */
	fun setDigitalMirror(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.Open_Digital_rearview_mirror_image_4)
		} else {
			sendResultCode(VDVRRespondID.Close_Digital_rearview_mirror_image_4)
		}
	}

	/**沉浸视频
	 * @param flag true:打开 false:关闭
	 */
	fun setImmersiveVideo(flag: Boolean) {
		if (flag) {
			sendResultCode(VDVRRespondID.Enter_Immersive_Video_4)
		} else {
			sendResultCode(VDVRRespondID.exit_Immersive_Video_3)
		}
	}

	/**
	 * 初始化遮阳帘
	 */
	fun initShade() {
		DialogNavigationUtils.launchMainActivity(
			context,
			MainActivity.MainTabIndex.CONDITION,
			ConditionFragment.DIALOG_ALERT_CONDITION_SUN_SHADE_CURTAIN_MODE,
			CommonConst.DIALOG_OPEN
		)
		sendResultCode(VDVRRespondID.initialize_the_sunshade_curtain_1)
	}

	/**
	 * 打开/关闭保养里程提醒
	 */
	fun setMaintenanceMileageReminder(voiceCommandState: Boolean) {
		if (voiceCommandState) {
			sendResultCode(VDVRRespondID.open_maintenance_mileage_reminder_1)
		} else {
			sendResultCode(VDVRRespondID.close_maintenance_mileage_reminder_1)
		}
	}

	object VehicleConstant {
		const val AR_PARAM_High: Int = 0
		const val AR_PARAM_Low: Int = 1
		const val AR_PARAM_Max: Int = 5
		const val AR_PARAM_Min: Int = 6
		const val UP_LITTLE: Int = 0x0
		const val LOW_LITTLE: Int = 0x1
		const val HIGH_LEVEL: Int = 0x2
		const val MID_LEVEL: Int = 0x3
		const val LOW_LEVEL: Int = 0x4
		const val HIGHEST_LEVEL: Int = 0x5
		const val MINIMUM_LEVEL: Int = 0x6
		const val DASH_CAM: Int = 0x4
		const val AVM: Int = 0x3
		const val HUD: Int = 0x1
		const val MIRROR_ADJUSTMENT: Int = 0x0
		const val SWITCHING_OF_AUDIO_SOURCES: Int = 0x6
		const val STEERING_WHEEL_ADJUSTMENT: Int = 0x2
		const val PANORAMIC_IMAGERY: Int = 0x5
		const val DASH_CAM_LONG_PRESS: Int = 0x0
		const val MIRROR_ADJUSTMENT_LONG_PRESS: Int = 0x1
		const val AVM_LONG_PRESS: Int = 0x2
		const val ALL_UP: String = "0-0"
		const val ALL_LOW: String = "0-1"
		const val ALL_LEFT: String = "0-2"
		const val ALL_RIGHT: String = "0-3"
		const val ALL_FRONT: String = "0-4"
		const val ALL_BACK: String = "0-5"
		const val LEFT_UP: String = "1-0"
		const val LEFT_LOW: String = "1-1"
		const val LEFT_LEFT: String = "1-2"
		const val LEFT_RIGHT: String = "1-3"
		const val LEFT_FRONT: String = "1-4"
		const val LEFT_BACK: String = "1-5"
		const val RIGHT_UP: String = "2-0"
		const val RIGHT_LOW: String = "2-1"
		const val RIGHT_LEFT: String = "2-2"
		const val RIGHT_RIGHT: String = "2-3"
		const val RIGHT_FRONT: String = "2-4"
		const val RIGHT_BACK: String = "2-5"
		const val INVALID_POSITION: String = "99"

		//set倒车后视镜 2:仅右侧 3:仅左侧 4:两侧同时
		const val SET_MIRROR_RIGHT = "2"
		const val SET_MIRROR_LEFT = "3"
		const val SET_MIRROR_BOTH = "4"

		//遮阳帘打开关闭一点 0:打开一点 1:关闭一点
		const val SET_SHADE_OPEN = "0"
		const val SET_SHADE_CLOSE = "1"

		//洗车模式设置指定值 0:常规 1:传送带
		const val WASH_MODE_REGULAR = "0"
		const val WASH_MODE_TRANSPORT = "1"

		//洗车模式开关 0:开启 1:关闭
		const val WASH_MODE_ON = "0"
		const val WASH_MODE_OFF = "1"

		// 尾门高度微调
		const val VR_TEAILGATE_RAISE_LITTLE = "0"
		const val VR_TEAILGATE_LOW_LITTLE = "1"
		const val VR_TEAILGATE_TO_HIGHEST = "5"
		const val VR_TEAILGATE_TO_LOWEST = "6"


	}

}
