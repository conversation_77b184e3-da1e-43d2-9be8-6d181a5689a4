package com.bitech.vehiclesettings.service.sound;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleService;

public class VoiceService extends LifecycleService {
    private static final String TAG = VoiceService.class.getName();

    VoiceLifecycle voiceLifecycle;
    private static VoiceService instance;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "VoiceService启动");
        instance = this;
        setForeground();
        voiceLifecycle = new VoiceLifecycle(this);
        getLifecycle().addObserver(voiceLifecycle);
    }

    public static VoiceService getInstance() {
        return instance;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        instance = null;
        getLifecycle().removeObserver(voiceLifecycle);
    }

    @Override
    public int onStartCommand(@Nullable Intent intent, int flags, int startId) {
        return super.onStartCommand(intent, flags, startId);
    }

    @SuppressLint("ForegroundServiceType")
    private void setForeground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            final String CHANNEL_ID = "2";
            final CharSequence CHANNEL_NAME = "VehicleService";
            NotificationManager notificationManager = (NotificationManager)
                    this.getSystemService(Context.NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_MIN);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
            Notification notification = new Notification.Builder(
                    this, CHANNEL_ID).build();
            startForeground(2, notification);
        } else {
            Notification notification = new Notification();
            startForeground(2, notification);
        }
    }
}
