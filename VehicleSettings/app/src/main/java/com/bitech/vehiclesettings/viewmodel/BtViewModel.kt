package com.bitech.vehiclesettings.viewmodel

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.base.kt.BaseViewModel
import com.bitech.vehiclesettings.bean.BtDeviceBean
import com.bitech.vehiclesettings.manager.CarBtManager
import com.bitech.vehiclesettings.manager.CountDownTimeManager
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.EToast
import com.bitech.vehiclesettings.utils.LogUtil
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.base.VDKey
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink
import com.chery.ivi.vdb.event.id.phonelink.bean.VDLinkDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Timer
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @ClassName: BtViewModel
 *
 * @Date:  2024/1/18 14:28
 * @Description: this is a BtViewModel class.
 **/
class BtViewModel : BaseViewModel(), CarBtManager.BtInfoListenerCallback {

    // 蓝牙管理对象
    private var carBtManager = CarBtManager.instance

    // ContentResolver对象
    private var contentResolver = MyApplication.getContext().contentResolver

    // 自动连接次数计数
    private var isAutoConnectedCount = 0

    // 是否自动扫描蓝牙设备
    private var isAutoScanBtDevice = false

    // 设备扫描倒计时管理对象
    private var scanTimerTask: CountDownTimeManager? = null

    // 设备自动连接讯息倒计时管理对象
    private var autoConnectTimerTask: CountDownTimeManager? = null

    // 蓝牙状态LiveData
    var bluetoothStatusLiveData = MutableLiveData<Boolean>()

    // 蓝牙可被发现LiveData
    val btCanFoundStatusLiveData = MutableLiveData<Boolean>()

    // 蓝牙名称LiveData
    val btNameLiveData = MutableLiveData<String?>()

    // 蓝牙可被发现倒计时LiveData
    val btFoundTimerLiveData = MutableLiveData<String>()

    // 蓝牙搜索动画订阅LiveData
    val btScanAnimationLiveData = MutableLiveData<Boolean>()

    // 蓝牙可用设备列表LiveData
    val btDevicesLiveData = MutableLiveData<CopyOnWriteArrayList<BtDeviceBean>>()

    // 蓝牙配对列表LiveData
    val btPairedDevLiveData = MutableLiveData<CopyOnWriteArrayList<BtDeviceBean>>()

    // 搜索关键词LiveData
    var searchKeywordLiveData = MutableLiveData<String>()

    // handler对象
    var handler: Handler? = Handler(Looper.getMainLooper())

    // ContentObserver订阅对象
    private val contentObserver: ContentObserver by lazy {
        object : ContentObserver(handler) {
            override fun onChange(selfChange: Boolean, uri: Uri?) {
                super.onChange(selfChange, uri)
                // 更新相关订阅信息
                updateObserver(uri)
            }
        }
    }

    init {
        viewModelScope.launch(Dispatchers.Default) {
            LogUtil.d(TAG, "init : ")
            // 注册蓝牙信息回调监听
            carBtManager.registerBtInfoListener(
                <EMAIL><EMAIL>(),
                this@BtViewModel
            )
            // 注册ContentObserver订阅
            contentResolver.registerContentObserver(
                Settings.System.getUriFor(Contacts.SETTINGS_SEARCH_KEYWORD_KEY),
                true,
                contentObserver
            )
        }
    }

    /**
     * contentResolver内容设置订阅.
     *
     * @param uri
     */
    private fun updateObserver(uri: Uri?) {
        when (uri) {
            Settings.System.getUriFor(Contacts.SETTINGS_SEARCH_KEYWORD_KEY) -> {
                // 搜索关键词发生改变时,获取搜索关键词
                getSearchKeyword()
            }
        }
    }

    /**
     * 获取搜索关键词.
     *
     */
    fun getSearchKeyword() {
        if(contentResolver == null){
            LogUtil.e(TAG, "getSearchKeyword : contentResolver == null")
            return
        }
        viewModelScope.launch(Dispatchers.Default) {
            val keyword =
                Settings.System.getString(contentResolver, Contacts.SETTINGS_SEARCH_KEYWORD_KEY)
            // 判断搜索关键词是否在蓝牙页面内,只需在二级菜单中搜索该项
            val btMenuBean =
                Contacts.btMenuBean.secondMenuList.find {
                    TextUtils.equals(
                        it.secondMenuName,
                        keyword
                    )
                }
            if (btMenuBean != null) {
                LogUtil.i(TAG, "getSearchKeyword : keyword = $keyword")
                // 在蓝牙页面内，则定位到页面内控件关键词位置
                searchKeywordLiveData.postValue(keyword)
                // 重置搜索关键词
                Settings.System.putString(
                    contentResolver,
                    Contacts.SETTINGS_SEARCH_KEYWORD_KEY,
                    Contacts.SETTINGS_SEARCH_KEYWORD_DEFAULT
                )
            }
        }
    }

    /**
     * 蓝牙开启后，自动尝试连接已配对设备，一个上电周期内循环三次.
     *
     */
    fun autoConnectDevice() {
        if (carBtManager.getBtPairedListNoSort().size != 0 && !Contacts.isBtLashBack) {
            LogUtil.d(TAG, "autoConnectDevice : start!")
            // 蓝牙配对列表不为空,且当前不处于设备回连状态，则开启自动连接倒计时
            startAutoConnectedTimerTask()
        } else {
            LogUtil.i(
                TAG, "autoConnectDevice : no start , " +
                        "size = ${carBtManager.getBtPairedListNoSort().size} , " +
                        "isBtLashBack = ${Contacts.isBtLashBack}"
            )
        }
    }

    /**
     * 停止自动连接.
     *
     */
    fun stopConnectDevice() {
        LogUtil.d(TAG, "stopConnectDevice : ")
        stopAutoConnectTimerTask()
    }

    /**
     * 连接蓝牙.
     *
     * @param btDeviceBean 蓝牙对象
     * @param connectType 连接类型
     */
    fun connectDevice(btDeviceBean: BtDeviceBean, connectType: Int = Contacts.BT_CONNECT_ALL) {
        LogUtil.d(
            TAG, "connectDevice : " +
                    "btDeviceBean = ${btDeviceBean.device.name} , " +
                    "connectType = $connectType"
        )
        // 停止扫描
        stopScanBtDevice()
        // 开始对连接设备进行双蓝牙模式仲裁
        carBtManager.connectDeviceArbitration(btDeviceBean, connectType)
    }

    /**
     * 断开蓝牙设备.
     *
     * @param btDeviceBean 蓝牙对象
     * @param disconnectType 断开连接类型
     * @param isAutoConnectBt AA或CP断开后是否自动连接蓝牙
     */
    fun disconnectDevice(
        btDeviceBean: BtDeviceBean,
        disconnectType: Int = Contacts.BT_CONNECT_ALL,
        isAutoConnectBt: Boolean = false
    ) {
        LogUtil.i(
            TAG, "disconnectDevice : " +
                    "disconnect name = ${btDeviceBean.device.name} , " +
                    "disconnectType = $disconnectType , " +
                    "isAutoConnectBt = $isAutoConnectBt"
        )
        // 停止扫描
        stopScanBtDevice()
        // 断开对应设备连接
        carBtManager.disconnectDevice(btDeviceBean, disconnectType, isAutoConnectBt)
    }

    fun connectBtMusic(btDeviceBean: BtDeviceBean){
        carBtManager.connectBtMusic(btDeviceBean.device)
    }

    /**
     * 关闭蓝牙开关时，断开当前连接的蓝牙设备的状态，更新列表状态.
     *
     */
    fun disconnectDevice() {
        // 断开当前已连接的设备,更新列表信息
        carBtManager.disconnectDeviceStatus()
    }

    fun getBtFoundSwitch(): Boolean {
        return carBtManager.getBtFoundSwitch()
    }


    /**
     * 打开carPlay.
     *
     * @param beDevice 蓝牙设备.
     */
    fun openCarPlay(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "openCarPlay : beDevice = ${beDevice.device.name}")
        carBtManager.openCarPlay(beDevice)
    }

    /**
     * 打开AndroidAuto.
     *
     * @param beDevice 蓝牙设备
     */
    fun openAndroidAuto(beDevice: BtDeviceBean) {
        LogUtil.d(TAG, "openAndroidAuto : beDevice = ${beDevice.device.name}")
        carBtManager.openAndroidAuto(beDevice)
    }

    /**
     * 获取当前连接的carPlay设备.
     *
     * @return BtDeviceBean
     */
    fun getCarPlayBean(): BtDeviceBean? {
        LogUtil.d(TAG, "getCarPlayBean : ")
        return carBtManager.getCarPlayBean()
    }

    /**
     * 获取当前连接的无线AA设备.
     *
     * @return BtDeviceBean
     */
    fun getAndroidAutoBean(): BtDeviceBean? {
        LogUtil.d(TAG, "getAndroidAutoBean : ")
        return carBtManager.getAndroidAutoBean()
    }

    /**
     * 获取已连接列表.
     *
     * @return 已连接列表
     */
    fun getConnectedDevices(): CopyOnWriteArrayList<BtDeviceBean> {
        LogUtil.d(TAG, "getConnectedDevices : ")
        return carBtManager.getConnectedDevices()
    }

    /**
     * 判断目标设备是否在已连接列表设备中存在.
     *
     * @param device 目标设备
     * @return Boolean
     */
    fun hasExistConnectedDevice(device: BtDeviceBean): Boolean {
        val isExist = carBtManager.hasExistConnectedDevice(device)
        LogUtil.i(TAG, "hasExistConnectedDevice : isExist = $isExist")
        return isExist
    }

    /**
     * 当前是否存在Cp连接.
     *
     * @return 状态
     */
    fun hasConnectDeviceCp(): Boolean {
        val hasConnectDeviceCp = carBtManager.hasConnectDeviceCp()
        LogUtil.i(TAG, "hasConnectDeviceCp : hasConnectDeviceCp = $hasConnectDeviceCp")
        return hasConnectDeviceCp
    }

    /**
     * 当前是否存在AA连接.
     *
     * @return 状态
     */
    fun hasConnectDeviceAa(): Boolean {
        val hasConnectDeviceAa = carBtManager.hasConnectDeviceAa()
        LogUtil.i(TAG, "hasConnectDeviceAa : hasConnectDeviceAa = $hasConnectDeviceAa")
        return hasConnectDeviceAa
    }

    /**
     * 开始蓝牙配对.
     *
     */
    fun startPairedBt(btDeviceBean: BtDeviceBean) {
        LogUtil.d(TAG, "startPairedBt : ")
        if (!carBtManager.getBtPairing()) {
            // 停止扫描
            stopScanBtDevice()
            Contacts.isStopScan = true
            // 开始请求绑定
            CarBtManager.instance.isUserCreateBond = true
            btDeviceBean.device.createBond()
        } else {
            LogUtil.d(TAG, "startPairedBt : is isPairing")
        }
    }

    /**
     * 解除蓝牙配对.
     *
     * @param btDeviceBean
     */
    fun removeBond(btDeviceBean: BtDeviceBean) {
        LogUtil.d(TAG, "removeBond : ")
        carBtManager.removeBond(btDeviceBean)
    }

    /**
     * 获取蓝牙扫描列表.
     *
     * @return btScanDevicesList
     */
    fun getBtScanList(): CopyOnWriteArrayList<BtDeviceBean> {
        val btScanList = carBtManager.getBtScanList()
        val tem = CopyOnWriteArrayList(btScanList)
        LogUtil.d(TAG, "getBtScanList : tem = $tem")
        btDevicesLiveData.postValue(tem)
        return tem
    }

    /**
     * 获取蓝牙扫描列表.
     *
     * @return
     */
    fun getBtPairedList(): CopyOnWriteArrayList<BtDeviceBean> {
        val btPairedList = carBtManager.getBtPairedList()
        LogUtil.i(TAG, "getBtPairedList : btPairedList = ${btPairedList.size}")
        return btPairedList
    }

    /**
     * 获取当前蓝牙状态.
     *
     * @return Boolean
     */
    fun getBluetoothStatus(): Boolean {
        val status = carBtManager.getBluetoothStatus()
        LogUtil.d(TAG, "getBluetoothStatus : status = $status")
        return status
    }

    /**
     * 设置蓝牙状态.
     *
     * @param btStatus 蓝牙状态.
     */
    fun setBluetoothStatus(btStatus: Boolean) {
        LogUtil.d(TAG, "setBluetoothStatus : btStatus = $btStatus")
        carBtManager.setBluetoothStatus(btStatus)
        if (btStatus){
            carBtManager.setBtFoundStatus(btStatus)
        }
    }

    /**
     * 获取蓝牙可被发现状态.
     *
     * @return 状态
     */
    fun getBtFoundStatus(): Boolean {
        val btFoundStatus = carBtManager.getBtFoundStatus()
        LogUtil.d(TAG, "getBtFoundStatus : btFoundStatus = $btFoundStatus")
        return when (btFoundStatus) {
            BluetoothAdapter.SCAN_MODE_CONNECTABLE_DISCOVERABLE -> {
                true
            }

            BluetoothAdapter.SCAN_MODE_CONNECTABLE -> {
                false
            }

            BluetoothAdapter.SCAN_MODE_NONE -> {
                false
            }

            else -> false
        }
    }

    /**
     * 设置蓝牙可被发现.
     *
     * @param btFoundStatus 蓝牙可被发现开关状态
     */
    fun setBtFoundStatus(btFoundStatus: Boolean) {
        LogUtil.d(TAG, "setBtFoundStatus : btFoundStatus = $btFoundStatus")
        carBtManager.setBtFoundStatus(btFoundStatus)
    }

    /**
     * 设置蓝牙名称.
     *
     * @param oldBtName 之前蓝牙名称
     * @param newBtName 现在蓝牙名称
     */
    fun setBtName(oldBtName: String, newBtName: String) {
        val nameStatus = carBtManager.setBtName(newBtName)
        if (!nameStatus) {
            // 设置失败,显示之前的蓝牙名称
            btNameLiveData.postValue(oldBtName)
        }
    }

    /**
     * 获取本机蓝牙名称.
     *
     * @return btName
     */
    fun getBtName(): String {
        val btName: String? = carBtManager.getBtName()
        LogUtil.d(TAG, "getBtName : btName = $btName")
        return if (btName == null) {
            // 未设置蓝牙名称，先设置为默认蓝牙名称
            carBtManager.setBtName(Contacts.BT_DEFAULT_NAME)
            Contacts.BT_DEFAULT_NAME
        } else {
            btName
        }
    }

    /**
     *开始扫描蓝牙设备.
     *
     * @param isAutoScan 是否是自动扫描(自动扫描需要开启计时器轮询扫描).
     * @param isHand 是否手动点击刷新按钮.
     */
    fun startScanBtDevice(isAutoScan: Boolean, isHand: Boolean) {
        if (carBtManager.getBtPairing() ) {
            // 当前在配对中，则不进行扫描
            LogUtil.d(TAG, "startScanBtDevice : is pairing ")
            return
        }
        LogUtil.d(TAG, "startScanBtDevice : isAutoScan = $isAutoScan")
        if (!carBtManager.isDiscovering()) {
            isAutoScanBtDevice = isAutoScan
            carBtManager.startScanBtDevice()
            if (isHand) {
                LogUtil.d(TAG, "发送一次扫描蓝牙设备")
                EToast.showToast(
                    MyApplication.getContext(),
                    MyApplication.getContext().getString(R.string.bt_start_scan_bt),
                    Toast.LENGTH_SHORT,
                    false
                );
            }
            if (isAutoScan) {
                // 开始计时
                startScanTimerTask()
            }
        } else {
            LogUtil.d(TAG, "startScanBtDevice : isDiscovering!")
        }
    }

    /**
     * 停止扫描蓝牙设备.
     *
     */
    fun stopScanBtDevice() {
        LogUtil.d(TAG, "stopScanBtDevice : ")
        isAutoScanBtDevice = false
        carBtManager.stopScanBtDevice()
        // 停止计时
        stopScanTimerTask()
    }

    /**
     * 隐藏软键盘.
     *
     * @param context 环境上下文
     * @param view 焦点视图
     */
    fun hideSoftInputFromWindow(context: Context, view: View) {
        val immInputManager =
            context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        immInputManager.hideSoftInputFromWindow(view.windowToken, 0)
    }

    /**
     * 显示软键盘.
     *
     * @param context 环境上下文
     * @param editText 编辑控件
     */
    fun showSoftInputFromWindow(context: Context, editText: EditText) {
        val immInputManager =
            context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        immInputManager.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT)
    }

    /**
     * 开始蓝牙扫描倒计时.
     *
     */
    private fun startScanTimerTask() {
        LogUtil.d(TAG, "startScanTimerTask : ")
        scanTimerTask = CountDownTimeManager(SCAN_DOWN_TIME) { count ->
            // 倒计时
            if (count == 0) {
                // 停止蓝牙设备搜索
                stopScanBtDevice()
            }
        }
        // 间隔1秒，开始计时
        Timer().schedule(scanTimerTask, 0, PERIOD)
    }

    /**
     * 停止扫描倒计时.
     *
     */
    private fun stopScanTimerTask() {
        LogUtil.d(TAG, "stopScanTimerTask : ")
        scanTimerTask?.cancel()
        scanTimerTask = null
    }

    /**
     * 开始自动连接循环倒计时.
     *
     */
    private fun startAutoConnectedTimerTask() {
        LogUtil.d(TAG, "startAutoConnectedTimerTask : ")
        if (autoConnectTimerTask == null) {
            // 一个上电周期循环三次配对列表，如果当前没有连接或连接失败,间隔10秒后执行下一次连接，连接成功后关闭计时器
            autoConnectTimerTask = CountDownTimeManager(
                (carBtManager.getBtPairedListNoSort().size) * 3
            ) { count ->
                LogUtil.d(TAG, "startAutoConnectedTimerTask : count =$count")
                // 倒计时
                if (count == 0) {
                    // 上电循环周期结束，关闭自动连接
                    stopAutoConnectTimerTask()
                } else {
                    // 开始自动连接
                    startAutoConnected()
                }
            }
            // 间隔10秒，开始计时
            Timer().schedule(autoConnectTimerTask, 0, PERIOD_AUTO_CONNECT)
        } else {
            LogUtil.d(TAG, "startAutoConnectedTimerTask : auto connect Task is running!")
        }
    }

    /**
     * 开始自动连接.
     *
     */
    private fun startAutoConnected() {
        // 判断当前是否存在已连接或连接中的设备，若存在，则停止自动重连，不存在，则开始自动重连
        if (!carBtManager.hasBtConnected() && !carBtManager.hasConnectDeviceCp() && !carBtManager.hasConnectDeviceAa()
        ) {
            LogUtil.i(TAG, "startAutoConnected : isAutoConnectedCount = $isAutoConnectedCount")
            if (isAutoConnectedCount <= carBtManager.getBtPairedListNoSort().size - 1) {
                LogUtil.d(TAG, "startAutoConnected : start auto connected!")
                // 当前不存在已连接的设备，则开始进行自动连接,从蓝牙配对列表首个开始连接
                carBtManager.autoConnectA2dpAndHfp(
                    carBtManager.getBtPairedListNoSort()[isAutoConnectedCount].device
                )
                isAutoConnectedCount++
                if (isAutoConnectedCount == carBtManager.getBtPairedListNoSort().size) {
                    // 循环周期，取决于蓝牙配对列表中的数量
                    isAutoConnectedCount = 0
                }
            } else {
                LogUtil.d(TAG, "startAutoConnected : stop auto connected!")
                stopAutoConnectTimerTask()
            }
        } else if (carBtManager.hasBtConnected()) {
            LogUtil.d(TAG, "startAutoConnected : exist connected!")
            // 如果存在已连接设备，则关闭自动连接倒计时
            stopAutoConnectTimerTask()
        } else {
            LogUtil.d(TAG, "startAutoConnected : other!")
            stopAutoConnectTimerTask()
        }
    }

    /**
     * 停止自动连接循环倒计时.
     *
     */
    fun stopAutoConnectTimerTask() {
        LogUtil.d(TAG, "stopAutoConnectTimerTask : ")
        isAutoConnectedCount = 0
        autoConnectTimerTask?.cancel()
        autoConnectTimerTask = null
    }

    fun getPairedBtDeviceName(): String? {
        return carBtManager.getCurrentConnectedBluetoothDeviceName()
    }

    fun getConnectTime(): String {
        return carBtManager.getConnectTime()
    }

    fun getPhoneLinkStatus(): Boolean {
        return carBtManager.getPhoneLinkStatus()
    }

    /*--------------------------------蓝牙广播事件相关状态监听回调-------------------------------------*/
    /**
     * 蓝牙名称发生改变时监听.
     *
     * @param btName 修改之后的蓝牙名称.
     */
    override fun onBtNameChanged(btName: String) {
        LogUtil.d(TAG, "onBtNameChanged : bt name = $btName")
        btNameLiveData.postValue(btName)
    }

    /**
     * 蓝牙状态监听.
     *
     * @param state
     */
    override fun onBluetoothStateChanged(state: Int) {
        LogUtil.d(TAG, "onBluetoothStateChanged : bt state = $state")
        when (state) {
            BluetoothAdapter.STATE_OFF -> {
                // 蓝牙关闭
                bluetoothStatusLiveData.postValue(false)
            }

            BluetoothAdapter.STATE_ON -> {
                // 蓝牙开启
                bluetoothStatusLiveData.postValue(true)
            }
        }
    }

    /**
     * 可被发现监听.
     *
     * @param foundState
     */
    override fun onBluetoothFoundStateChanged(foundState: Int) {
        LogUtil.d(TAG, "onBluetoothFoundStateChanged : bt found state = $foundState")
        when (foundState) {
            BluetoothAdapter.SCAN_MODE_CONNECTABLE_DISCOVERABLE -> {
                // 蓝牙可被发现开关开启
                btCanFoundStatusLiveData.postValue(true)
            }

            BluetoothAdapter.SCAN_MODE_CONNECTABLE -> {
                // 蓝牙可被发现开关关闭
                btCanFoundStatusLiveData.postValue(false)
            }

            BluetoothAdapter.SCAN_MODE_NONE -> {
                // 蓝牙可被发现开关关闭
                btCanFoundStatusLiveData.postValue(false)
            }
        }
    }

    /**
     * 蓝牙可被发现倒计时.
     *
     * @param countdown 倒计时
     */
    override fun onBtFoundCountdownChanged(countdown: String) {
        LogUtil.i(TAG, "onBtFoundCountdownChanged : countdown = $countdown")
        // 更新蓝牙可被发现倒计时
        btFoundTimerLiveData.postValue(countdown)
    }

    /**
     * 蓝牙开始扫描时回调.
     *
     */
    override fun onStartScan() {
        LogUtil.d(TAG, "onStartScan : ")
        // 清空列表,自动扫描触发和存在设备回连时，不清空列表
        if (!isAutoScanBtDevice && !Contacts.isBtLashBack) {
            carBtManager.clearBtScanList()
        }
        // 开始扫描,开始动画
        btScanAnimationLiveData.postValue(true)
    }

    /**
     * 蓝牙停止扫描时回调.
     *
     */
    override fun onStopScan() {
        LogUtil.d(TAG, "onStopScan : ")
        if (isAutoScanBtDevice) {
            // 如果是自动触发扫描，结束后继续开始扫描，轮询
            carBtManager.startScanBtDevice()
        } else {
            // 如果是主动触发扫描，结束后，图标结束
            btScanAnimationLiveData.postValue(false)
            // 停止扫描
            stopScanBtDevice()
        }
    }

    /**
     * 蓝牙扫描列表更新时回调.
     *
     * @param btScanDevicesList 蓝牙扫描列表
     */
    override fun onUpdateBtScanList(btScanDevicesList: CopyOnWriteArrayList<BtDeviceBean>) {
        LogUtil.i(TAG, "onUpdateBtScanList : btScanDevicesList = ${btScanDevicesList.size}")
        // 更新蓝牙扫描列表
        val tem = CopyOnWriteArrayList(btScanDevicesList)
        btDevicesLiveData.postValue(tem)
    }

    /**
     * 蓝牙配对列表更新时回调.
     *
     * @param btPairedDeviceList 蓝牙配对列表
     */
    override fun onUpdateBtPairedList(btPairedDeviceList: CopyOnWriteArrayList<BtDeviceBean>) {
        LogUtil.i(TAG, "onUpdateBtPairedList : btPairedList = ${btPairedDeviceList.size}")
        // 更新蓝牙配对列表
        val tem = CopyOnWriteArrayList(btPairedDeviceList)
        //蓝牙关闭情况下，有线cp连接，只显示有线cp设备，过滤其他设备
        val event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
        if (event != null) {
            val bundle = event.payload
            val status = bundle.getInt(VDKey.STATUS)
            val devicePhoneLink = bundle.getParcelable<VDLinkDevice>(VDKey.INFO)
            if (status > 0) {
                val macAddress = devicePhoneLink?.btAddress
                // 过滤掉 macAddress 对应的设备
                val filteredList = if (!macAddress.isNullOrEmpty()) {
                    tem.filterNot { it.device.address == macAddress }
                } else {
                    tem
                }
                btPairedDevLiveData.postValue(CopyOnWriteArrayList(filteredList))
                return
            }
        }
        btPairedDevLiveData.postValue(tem)
    }

    /**
     * 停止自动连接到计时.
     *
     */
    override fun onStopAutoConnectTimerTask() {
        LogUtil.d(TAG, "onStopAutoConnectTimerTask : ")
        // 停止自动连接
        stopAutoConnectTimerTask()
    }

    override fun onPairedStateChange(pairedState: Int, device: BluetoothDevice) {
        LogUtil.d(TAG, "onPairedStateChange : pairedState = $pairedState , device = ${device.name}(${device.address})")
        //T13JSUPPLY-697 正在配对时，停止扫描
        if(pairedState == BluetoothDevice.BOND_BONDING){
            // 停止扫描
            stopScanBtDevice()
        }
    }

    /*--------------------------------蓝牙广播事件相关状态监听回调-------------------------------------*/

    override fun onCleared() {
        LogUtil.d(TAG, "onCleared : ")
        // 移除蓝牙信息监听
        carBtManager.unregisterBtInfoListener(<EMAIL>())
        contentResolver.unregisterContentObserver(contentObserver)
        contentResolver = null
        handler = null
        super.onCleared()
    }

    companion object {
        // 日志标志位
        private const val TAG = "BtViewModel"

        // 计时器间隔
        private const val PERIOD = 1000L

        // 自动连接计时器间隔
        private const val PERIOD_AUTO_CONNECT = 7000L

        // 蓝牙扫描倒计时
        private const val SCAN_DOWN_TIME = 60
    }
}
