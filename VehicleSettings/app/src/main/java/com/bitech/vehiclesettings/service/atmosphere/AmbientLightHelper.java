package com.bitech.vehiclesettings.service.atmosphere;

import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.atmosphere.AmbLigBean;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.ambientlightsdk.core.AmbientlightAPI;
import com.bitech.vehiclesettings.utils.Bit64Utils;

public class AmbientLightHelper implements AmbientlightAPI {
    private static final String TAG = AmbientLightHelper.class.getSimpleName();
    private static volatile AmbientLightHelper instance;
    private LightManager manager = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);

    public static AmbientLightHelper getInstance() {
        if (instance == null) {
            synchronized (AmbientLightHelper.class) {
                if (instance == null) {
                    instance = new AmbientLightHelper();
                }
            }
        }
        return instance;
    }

    /**
     * @param colorIndex
     * @param brightness
     */
    @Override
    public void setAmbientLightMusicColor(int colorIndex, int brightness) {
        Log.d(TAG, "Setting ambient light color to index colorIndex: " + colorIndex + ",bright:" + brightness);
        sendRhythm(colorIndex, brightness);
    }

    private void sendRhythm(int colorIndex, int lightLevel) {
        AmbLigBean ambLigBean = new AmbLigBean().setAll();
        ambLigBean.setAmbLigBriAdj(lightLevel);
        ambLigBean.setAmbLigColorAdj(colorIndex);
        ambLigBean.setAmbLigFadeINorOUTStepTi(0);
        ambLigBean.setAmbLigFlngModSel(3);
        ambLigBean.setDymAmbLigLevelSig(0);
        ambLigBean.setDymAmbLigDirSig(0);
        ambLigBean.setAmbModLocSig(0);
        ambLigBean.setAmbLigModRepOrdIdeSig(0);
        ambLigBean.setAmbLigModTimCabSig(0);
        // 发送音律律动
        Log.d(TAG, "sendRhythm val: " + Bit64Utils.toHexString(ambLigBean.getValue(), true, 4));
        manager.setLightAmbLightCan(ambLigBean);
    }
}
