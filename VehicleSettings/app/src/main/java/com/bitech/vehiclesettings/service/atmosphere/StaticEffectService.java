package com.bitech.vehiclesettings.service.atmosphere;

import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.bean.atmosphere.AmbLigBean;
import com.bitech.vehiclesettings.utils.CommonConst;

public class StaticEffectService {
    private static final String TAG = StaticEffectService.class.getSimpleName();
    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
    private static volatile StaticEffectService instance;
    private LightInBean lInBean;
    private GradientEffectService gradualChangeService;
    private BreathingEffectService breathingEffectService;

    public static StaticEffectService getInstance() {
        if (instance == null) {
            synchronized (StaticEffectService.class) {
                if (instance == null) {
                    instance = new StaticEffectService();
                }
            }
        }
        return instance;
    }

    public void setOnOff(int open) {
        lInBean = carServer.getLightInData();
        if (lInBean != null) {
            lInBean.setLightSw(open);
            lInBean.getFront().setFront(open);
            lInBean.setLightFontSw(open);
            lInBean.getRear().setRear(open);
            lInBean.setLightRearSw(open);
            lInBean.save(MyApplication.getContext());
        }
    }

    public void setStatic(int open) {
        lInBean = carServer.getLightInData();
        int brightnessIdx = 0;
        int colorIdx = 0;
        if (open == CommonConst.OPEN) {
            if (lInBean != null) {
                if (lInBean.getThemeMode() == CommonConst.TAB_0) {
                    if (lInBean.getColorPos() >= CommonConst.COLOR_POS_MAX) {
                        brightnessIdx = lInBean.getBrightPos();
                        colorIdx = lInBean.getSingleColorLin();
                        Log.d(TAG, "[静态氛围灯] brightnessIdx: " + brightnessIdx + ",colorIdx:" + colorIdx);
                        sendAmbLigAll(brightnessIdx, colorIdx);
                    } else {
                        brightnessIdx = lInBean.getBrightPos();
                        colorIdx = lInBean.getMutiFrontColorLin();
                        sendAmbLigFront(brightnessIdx, colorIdx);
                        colorIdx = lInBean.getMutiRearColorLin();
                        Log.d(TAG, "[静态氛围灯] brightnessIdx: " + brightnessIdx + ",colorIdx front:" + lInBean.getMutiFrontColorLin() + ",colorIdx rear:" + colorIdx);
                        sendAmbLigRear(brightnessIdx, colorIdx);
                    }
                } else {
                    brightnessIdx = lInBean.getFront().getFrontBrightness();
                    colorIdx = lInBean.getFront().getFrontColorLin();
                    sendAmbLigFront(brightnessIdx, colorIdx);
                    brightnessIdx = lInBean.getRear().getRearBrightness();
                    colorIdx = lInBean.getRear().getRearColorLin();
                    Log.d(TAG, "[静态氛围灯] brightnessIdx: " + brightnessIdx + ",colorIdx:" + colorIdx);
                    sendAmbLigRear(brightnessIdx, colorIdx);
                }
            }
        } else {
            sendAmbLigAll(CarLight.AmbLigBriAdj.LEVEL_0, CarLight.AmbLigColorAdj.Invalid);
        }
    }


    private void sendAmbLigAll(int bright, int colorLin) {
        AmbLigBean ambLigBean = new AmbLigBean();
        ambLigBean.setAmbLig02Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig04Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig05Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig07Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig08Authn(CarLight.AmbLigXXAuthn.Active);
        ambLigBean.setAmbLig09Authn(CarLight.AmbLigXXAuthn.Active);
        ambLigBean.setAmbLigBriAdj(bright).setAmbLigColorAdj(colorLin);
        carServer.setLightAmbLightCan(ambLigBean);
        carServer.saveAtmosphereLight(ambLigBean.getValue(), ambLigBean.getValue());
    }


    private void sendAmbLigFront(int bright, int colorLin) {
        AmbLigBean ambLigBean = new AmbLigBean();
        ambLigBean.setAmbLig02Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig04Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig05Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig07Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig08Authn(CarLight.AmbLigXXAuthn.Active);
        ambLigBean.setAmbLigBriAdj(bright).setAmbLigColorAdj(colorLin);
        carServer.setLightAmbLightCan(ambLigBean);
        carServer.saveAtmosphereLight(ambLigBean.getValue(), ambLigBean.getValue());
    }

    private void sendAmbLigRear(int bright, int colorLin) {
        AmbLigBean ambLigBean = new AmbLigBean();
        ambLigBean.setAmbLig09Authn(CarLight.AmbLigXXAuthn.Active);
        ambLigBean.setAmbLigBriAdj(bright).setAmbLigColorAdj(colorLin);
        carServer.setLightAmbLightCan(ambLigBean);
        carServer.saveAtmosphereLight(ambLigBean.getValue(), ambLigBean.getValue());
    }
}
