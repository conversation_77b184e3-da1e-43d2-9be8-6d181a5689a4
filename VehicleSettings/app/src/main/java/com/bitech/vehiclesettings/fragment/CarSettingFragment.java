package com.bitech.vehiclesettings.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.interfaces.carSetting.ICarSettingManagerListener;
import com.bitech.platformlib.manager.CarSettingManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertCDriveAirbagBinding;
import com.bitech.vehiclesettings.databinding.FragmentCarSettingBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.carsetting.CarSettingPresenter;
import com.bitech.vehiclesettings.presenter.carsetting.CarSettingPresenterListener;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.carsetting.ChildLockUIAlert;
import com.bitech.vehiclesettings.view.carsetting.DriveAirbagUIAlert;
import com.bitech.vehiclesettings.view.carsetting.LockTipsUIAlert;
import com.bitech.vehiclesettings.view.carsetting.MaintainResetUIAlert;
import com.bitech.vehiclesettings.view.carsetting.MoreSettingUIAlert;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.viewmodel.CarSettingModel;

public class CarSettingFragment extends BaseFragment<FragmentCarSettingBinding> implements View.OnClickListener, SafeHandlerCallback {
    private static final String TAG = CarSettingFragment.class.getSimpleName();
    private CarSettingPresenterListener presenter;
    private SafeHandler carHandler;

    private LockTipsUIAlert.Builder lockTipsUIAlert;
    private ChildLockUIAlert.Builder childLockUIAlert;
    private DetailsUIAlert.Builder detailUIAlert;
    private MaintainResetUIAlert.Builder maintainResetUIAlert;
    private DriveAirbagUIAlert.Builder driveAirbagUIAlert;
    private MoreSettingUIAlert.Builder overSpeedUIAlert, fatDriveUIAlert;

    private CarSettingModel viewModel;
    private volatile boolean isActive;
    private int lockTipsStatus;
    private int swWiperSens;
    private int driveBagFlag;
    // 判断设置安全气囊是否成功
    private boolean switchDriveBagFlag = true;

    private int[] stepsOverSpeed = new int[]{
            30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140, 145, 150, 155, 160, 165, 170
    };

    private float[] stepsFatigueDrive = new float[]{
            1, 1.5f, 2, 2.5f, 3, 3.5f, 4
    };
    private CarSettingManager manager = (CarSettingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_SETTING_MANAGER);

    private final String[] carPropertyIds = new String[]{
            Topics.CarSetting.WIPER_SENSITIVITY,
    };

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null) return;
        loadPageAnim(binding.scrollView, currentPosition, position);
    }

    public void setPresenter(CarSettingPresenterListener carPresenter) {
        this.presenter = carPresenter;
    }

    private void registerVoiceListen() {
        if (manager == null) {
            manager = (CarSettingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_SETTING_MANAGER);
        }
        manager.addCallback(TAG, new ICarSettingManagerListener() {

            @Override
            public void wiperSensitivityCallback(int status) {
                if (viewModel.getWiperSensitivity().getValue() != null && viewModel.getWiperSensitivity().getValue() != status) {
                    switch (status) {
                        case 0:
                            status = -1;
                            break;
                        case 0x1:
                            status = 0;
                            break;
                        case 0x2:
                            status = 1;
                            break;
                        case 0x3:
                            status = 2;
                            break;
                        case 0x4:
                            status = 3;
                            break;
                        default:
                            status = -1;
                            break;
                    }
                    Log.d(TAG, "wiperSensitivityCallback Callback status: " + status);
                    int finalStatus = status;
                    mContext.runOnUiThread(() -> {
                        if (finalStatus == -1) return;
                        binding.segmentedPickerView.setSelectedIndex(finalStatus, true);
                        Prefs.put(PrefsConst.C_WIPER_SENS, finalStatus);
                    });
                }
            }
        });
        manager.registerListener(carPropertyIds);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isActive = true;
        setPresenter(new CarSettingPresenter(getContext()));
        carHandler = new SafeHandler(this);
        registerVoiceListen();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = getLayoutResId(inflater, container).getRoot();
        initObserver();
        initView();
        setListener();
//        presenter.getOverSpeed();
        return rootView;
    }

    @Override
    protected FragmentCarSettingBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentCarSettingBinding.inflate(getLayoutInflater());
        return binding;
    }

    @Override
    protected void initView() {
        initData();
        initPicker();
    }

    protected void initData() {
//        presenter.setCarSpeed(120);
        viewModel.setHudRoate(Prefs.get(PrefsConst.C_REAR_TAILGATE_ROATE, 90));

        viewModel.setWiperSensitivity(Prefs.get(PrefsConst.C_WIPER_SENS, PrefsConst.DefaultValue.C_WIPER_SENS));
        swWiperSens = Prefs.get(PrefsConst.C_WIPER_SENS, PrefsConst.DefaultValue.C_WIPER_SENS);

        viewModel.setLockAutoRaiseWindow(Prefs.get(PrefsConst.C_LOCK_AUTO_RAISE_WINDOW, 0));

        viewModel.setDefenseReminder(Prefs.get(PrefsConst.C_DEFENSE_REMINDER, 0));
        lockTipsStatus = Prefs.get(PrefsConst.C_DEFENSE_REMINDER, PrefsConst.DefaultValue.C_DEFENSE_REMINDER);

        viewModel.setLeftChildLock(Prefs.get(PrefsConst.C_LEFT_CHILD_LOCK, 0));
        viewModel.setRightChildLock(Prefs.get(PrefsConst.C_RIGHT_CHILD_LOCK, 0));

        viewModel.setAutomaticLocking(Prefs.get(PrefsConst.C_AUTOMATIC_LOCKING, 0));
        viewModel.setAutomaticParkingUnlock(Prefs.get(PrefsConst.C_AUTOMATIC_PARKING_UNLOCK, 0));

        viewModel.setWiperRepairMode(Prefs.get(PrefsConst.C_WIPER_REPAIR_MODE, 0));
        viewModel.setOverSpeed(Prefs.get(PrefsConst.C_OVER_SPEED, 0));
        viewModel.setFatigueDrivingReminder(Prefs.get(PrefsConst.C_FATIGUE_DRIVING_REMINDER, 0));

        viewModel.setMaintainTips(Prefs.get(PrefsConst.C_MAINTAIN_TIPS, 0));

        viewModel.setDriveAirBag(Prefs.get(PrefsConst.C_DRIVE_AIR_BAG, PrefsConst.DefaultValue.C_DRIVE_AIR_BAG));
        driveBagFlag = Prefs.get(PrefsConst.C_DRIVE_AIR_BAG, PrefsConst.DefaultValue.C_DRIVE_AIR_BAG);

        viewModel.setOverSpeedSeekbar(Prefs.get(PrefsConst.C_OVER_SPEED_SEEKBAR, PrefsConst.DefaultValue.C_OVER_SPEED_SEEKBAR));
        viewModel.setFatigueDrivingSeekbar(Prefs.get(PrefsConst.C_FATIGUE_DRIVING_SEEKBAR, PrefsConst.DefaultValue.C_FATIGUE_DRIVING_SEEKBAR));
    }

    private void initObserver() {
        viewModel = new ViewModelProvider(this).get(CarSettingModel.class);
        viewModel.getWiperSensitivity().observe(getViewLifecycleOwner(), status -> {
            // 更新UI雨刮器
            selWiperSensTranslate();
        });
        viewModel.getHudRoate().observe(getViewLifecycleOwner(), status -> {
            // 更新UI后尾门
            presenter.updateHudRoateUI(status, binding.sbIvHudRoate, binding.ivHudRoateTop);
        });
        viewModel.getLockAutoRaiseWindow().observe(getViewLifecycleOwner(), status -> {
            // 锁车自动升窗
            updateLockAutoRaiseWindowUI(status);
        });
        viewModel.getLeftChildLock().observe(getViewLifecycleOwner(), status -> {
            // 左儿童锁
            updateLeftChildLockUI(status);
        });
        viewModel.getRightChildLock().observe(getViewLifecycleOwner(), status -> {
            // 右儿童锁
            updateRightChildLockUI(status);
        });
        viewModel.getAutomaticLocking().observe(getViewLifecycleOwner(), status -> {
            // 自动落锁
            updateAutomaticLockingUI(status);
        });
        viewModel.getAutomaticParkingUnlock().observe(getViewLifecycleOwner(), status -> {
            // 驻车自动解锁
            updateAutomaticParkingUnlockUI(status);
        });
        viewModel.getWiperRepairMode().observe(getViewLifecycleOwner(), status -> {
            // 雨刮维修模式
            updateWiperRepairModeUI(status);
        });
        viewModel.getOverSpeed().observe(getViewLifecycleOwner(), status -> {
            // 超速报警
            updateOverSpeedUI(status);
        });
        viewModel.getFatigueDrivingReminder().observe(getViewLifecycleOwner(), status -> {
            // 疲劳驾驶提醒
            updateFatigueDrivingReminderUI(status);
        });
        // 保养提示
        viewModel.getMaintainTips().observe(getViewLifecycleOwner(), this::updateMaintainTipsUI);
        // 驾驶安全气囊
        viewModel.getDriveAirBag().observe(getViewLifecycleOwner(), status -> {
            updateDriveAirBagUI();
        });
        // 设防提示
        viewModel.getDefenseReminder().observe(getViewLifecycleOwner(), status -> {
            updateLockTipsUI();
        });
    }

    private void updateLockAutoRaiseWindowUI(Integer status) {
        Log.d(TAG, "锁车自动升窗: " + status);
        binding.swAutoWindow.setChecked(CommonUtils.IntToBool(status));
        Log.d(TAG, "锁车自动升窗: " + binding.swAutoLock.isChecked());
    }

    /**
     * 设防提示
     */
    private void updateLockTipsUI() {
        if (lockTipsUIAlert != null) {
            lockTipsUIAlert.updateLockTipsUI(lockTipsStatus);
        }
    }

    /**
     * 儿童锁-左
     *
     * @param status
     */

    private void updateLeftChildLockUI(Integer status) {
        if (childLockUIAlert != null) {
            childLockUIAlert.updateLeftChildLockUI(status);
        }
    }

    /**
     * 儿童锁-右
     *
     * @param status
     */
    private void updateRightChildLockUI(Integer status) {
        if (childLockUIAlert != null) {
            childLockUIAlert.updateRightChildLockUI(status);
        }
    }

    /**
     * 自动落锁UI
     *
     * @param status
     */
    private void updateAutomaticLockingUI(Integer status) {
        binding.swAutoLock.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 驻车自动解锁
     *
     * @param status
     */
    private void updateAutomaticParkingUnlockUI(Integer status) {
        binding.swAutoUnlock.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 雨刮器维修模式
     *
     * @param status
     */
    private void updateWiperRepairModeUI(Integer status) {
        binding.swWiperRepair.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 超速报警
     *
     * @param status
     */
    private void updateOverSpeedUI(Integer status) {
        binding.swOverspeed.setChecked(CommonUtils.IntToBool(status));
        if (overSpeedUIAlert != null) {
            overSpeedUIAlert.updateSwitch(status);
        }
    }

    /**
     * 疲劳驾驶提醒
     *
     * @param status
     */
    private void updateFatigueDrivingReminderUI(Integer status) {
        binding.swFatigueDrive.setChecked(CommonUtils.IntToBool(status));
        if (fatDriveUIAlert != null) {
            fatDriveUIAlert.updateSwitch(status);
        }
    }

    /**
     * 保养提示
     *
     * @param status
     */
    private void updateMaintainTipsUI(Integer status) {
        binding.swMaintainTips.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 副驾安全气囊
     */
    private void updateDriveAirBagUI() {
        if (driveAirbagUIAlert != null) {
            driveAirbagUIAlert.updateDriveAirBagUI(driveBagFlag);
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    protected void setListener() {
        // 后尾门高度
        binding.sbIvHudRoate.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                Log.d(TAG, "onProgressChanged: " + progress);
                binding.ivHudRoateTop.setText(progress + "%");
                // 发送数据
                presenter.setHudRoate(progress);
                viewModel.setHudRoate(progress);
                carHandler.sendMessageDelayed(MessageConst.CAR_HUD_ROATE);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(false);
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) seekBar.getLayoutParams();
                layoutParams.topMargin = 139;
                seekBar.setLayoutParams(layoutParams);
                seekBar.setThumb(getResources().getDrawable(R.mipmap.ic_sound_slider_s));
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                binding.scrollView.setEnableScroll(true);
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) seekBar.getLayoutParams();
                layoutParams.topMargin = 142;
                seekBar.setLayoutParams(layoutParams);
                seekBar.setThumb(getResources().getDrawable(R.mipmap.ic_sound_slider));
            }
        });
        BindingUtil.bindClicks(this, binding.rlLockTips, binding.rlChildLock, binding.rlMaintainTips, binding.rlMaintainReset, binding.rlSafe, binding.llOverspeed, binding.llFatugueDrive);
        BindingUtil.bindClicks(this, binding.ivSafeTips);
        // 门锁，车锁相关事件
        BindingUtil.bindClicks(this, binding.swAutoLock, binding.swAutoUnlock, binding.swAutoWindow, binding.swFatigueDrive, binding.swMaintainTips, binding.swOverspeed, binding.swWiperRepair);
//        BindingUtil.bindClicks(this, binding.swAutoWindow, binding.swAutoLock, binding.swAutoUnlock, binding.swWiperRepair, binding.swOverspeed, binding.swFatigueDrive);
        // 设防提示接口
        LockTipsUIAlert.setOnProgressChangedListener(new LockTipsUIAlert.onProgressChangedListener() {
            @Override
            public void onSwitch(int index) {
                lockTipsStatus = index;
                viewModel.setDefenseReminder(index);
                presenter.setDefenseReminder(index);
                carHandler.sendMessageDelayed(MessageConst.CAR_DEFENSE_REMINDER);
                Log.d(TAG, "设防提示: " + index);
            }

            @Override
            public int getInitStatus() {
                return lockTipsStatus;
            }
        });
        // 雨刮器灵敏度picker
        binding.segmentedPickerView.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index, String text) {
                swWiperSens = index;
                viewModel.setWiperSensitivity(index);
                presenter.setWiperSens(index);
                carHandler.sendMessageDelayed(MessageConst.CAR_WIPERSENS);
                Log.d(TAG, "雨刮器灵敏度: " + text);
            }

            @Override
            public void onItemClicked(int index, String text) {

            }
        });
        // 保养里程复位Alert
        MaintainResetUIAlert.setOnProgressChangedListener(() -> {
            presenter.maintainReset();
            int maintainKm = presenter.getMaintainKm();
            int maintainDays = presenter.getMaintainDays();
            Log.d(TAG, "保养里程复位");
            binding.tvMaintainContent.setText(
                    "剩余保养里程：" + maintainKm + "km 剩余保养天数：" + maintainDays + "天"
            );
            carHandler.sendMessageDelayed(MessageConst.CAR_MAINTAIN_RESET);
        });
        // 副驾安全气囊
        DriveAirbagUIAlert.setOnProgressChangedListener(new DriveAirbagUIAlert.onProgressChangedListener() {
            @Override
            public void onSwitch(DialogAlertCDriveAirbagBinding binding) {
                boolean enabled = CommonUtils.IntToBool(presenter.getCarSpeed() <= 120 ? 1 : 0);
                binding.spvDriveAirbag.setEnabled(enabled);
            }

            @Override
            public void switchSuccess(int index) {
                driveBagFlag = index;
                viewModel.setDriveAirBag(index);
                presenter.setDriveAirBag(index);
                carHandler.sendMessageDelayed(MessageConst.CAR_DRIVE_AIR_BAG);
                Log.d(TAG, "副驾安全气囊: " + index);
            }

            @Override
            public int getInitStatus() {
                return driveBagFlag;
            }
        });
        // 超速报警&疲劳驾驶提醒
        MoreSettingUIAlert.setOnProgressChangedListener(new MoreSettingUIAlert.onProgressChangedListener() {
            @Override
            public void initSwitch(@SuppressLint("UseSwitchCompatOrMaterialCode") Switch switchView, SeekBar seekBar, int type) {
                if (type == 0) {
                    switchView.setChecked(CommonUtils.IntToBool(viewModel.getOverSpeed().getValue()));
                } else if (type == 1) {
                    switchView.setChecked(CommonUtils.IntToBool(viewModel.getFatigueDrivingReminder().getValue()));
                }
            }

            @Override
            public void initSeekbar(SeekBar seekBar, TextView textView, int type) {
                Log.d(TAG, "initSeekbar: " + type);
                if (type == 0) {
                    viewModel.getOverSpeedSeekbar().observe(getViewLifecycleOwner(), status -> {
                        seekBar.setProgress(status);
                        textView.setText(stepsOverSpeed[status] + "km/h");
                    });
                    seekBar.setProgress(viewModel.getOverSpeedSeekbar().getValue());
                } else if (type == 1) {
                    viewModel.getFatigueDrivingSeekbar().observe(getViewLifecycleOwner(), status -> {
                        seekBar.setProgress(status);
                        textView.setText(stepsFatigueDrive[status] + "");
                    });
                    seekBar.setProgress(viewModel.getFatigueDrivingSeekbar().getValue());
                }
            }

            @Override
            public void updateSeekBarUI(int type, SeekBar seekBar, TextView textView) {
                if (type == 0) {
                    viewModel.getOverSpeedSeekbar().observe(getViewLifecycleOwner(), status -> {
                        seekBar.setProgress(status);
                        textView.setText(stepsOverSpeed[status] + "km/h");
                    });
                    seekBar.setMax(stepsOverSpeed.length - 1);
                } else if (type == 1) {
                    viewModel.getFatigueDrivingSeekbar().observe(getViewLifecycleOwner(), status -> {
                        seekBar.setProgress(status);
                        textView.setText(stepsFatigueDrive[status] + "");
                    });
                    seekBar.setMax(stepsFatigueDrive.length - 1);
                }
            }

            @Override
            public void setOnSeekBarListener(int type, SeekBar seekBar, int progress, boolean fromUser) {
                if (type == 0) {
                    // 超速报警seekbar
                    overSpeedUIAlert.getBinding().tvSeekbarProgress.setText(stepsOverSpeed[progress] + "km/h");
                    // 发送数据
                    presenter.setOverSpeedSeekbar(progress);
                    viewModel.setOverSpeedSeekbar(progress);
                    carHandler.sendMessageDelayed(MessageConst.CAR_OVER_SPEED_SEEKBAR);
                } else if (type == 1) {
                    // 疲劳驾驶seekbar
                    fatDriveUIAlert.getBinding().tvSeekbarProgress.setText(stepsFatigueDrive[progress] + "");
                    // 发送数据
                    presenter.setFatigueDriveSeekbar(progress);
                    viewModel.setFatigueDrivingSeekbar(progress);
                    carHandler.sendMessageDelayed(MessageConst.CAR_FATIGUE_DRIVE_SEEKBAR);
                }
            }

            @Override
            public void setOnSwitchListener(int type, boolean isChecked) {
                if (type == 0) {
                    // 超速报警
                    if (viewModel.getOverSpeed().getValue() == 1) {
                        viewModel.setOverSpeed(0);
                        presenter.setOverSpeed(0);
                    } else {
                        viewModel.setOverSpeed(1);
                        presenter.setOverSpeed(1);
                    }
                    carHandler.sendMessageDelayed(MessageConst.CAR_OVER_SPEED);
                } else if (type == 1) {
                    if (viewModel.getFatigueDrivingReminder().getValue() == 1) {
                        viewModel.setFatigueDrivingReminder(0);
                        presenter.setFatigueDrivingReminder(0);
                    } else {
                        viewModel.setFatigueDrivingReminder(1);
                        presenter.setFatigueDrivingReminder(1);
                    }
                    carHandler.sendMessageDelayed(MessageConst.CAR_FATIGUE_DRIVING_REMINDER);
                }
            }
        });
    }

    @Override
    protected void initObserve() {

    }


    private void openLockTips() {
        if (lockTipsUIAlert != null && lockTipsUIAlert.isShowing()) {
            return;
        }
        if (lockTipsUIAlert == null) {
            lockTipsUIAlert = new LockTipsUIAlert.Builder(mContext);
        }
        lockTipsUIAlert.create().show();
    }

    private void openChildLock() {
        if (childLockUIAlert != null && childLockUIAlert.isShowing()) {
            return;
        }
        if (childLockUIAlert == null) {
            childLockUIAlert = new ChildLockUIAlert.Builder(mContext);
        }
        childLockUIAlert.create().show();
    }

    private void openMaintainReset() {
        if (maintainResetUIAlert != null && maintainResetUIAlert.isShowing()) {
            return;
        }
        if (maintainResetUIAlert == null) {
            maintainResetUIAlert = new MaintainResetUIAlert.Builder(mContext);
        }
        maintainResetUIAlert.create().show();
    }

    private void openDriveAirbag() {
        if (driveAirbagUIAlert != null && driveAirbagUIAlert.isShowing()) {
            return;
        }
        if (driveAirbagUIAlert == null) {
            driveAirbagUIAlert = new DriveAirbagUIAlert.Builder(mContext);
        }
        driveAirbagUIAlert.create().show();
    }

    private void openOverSpeed() {
        if (overSpeedUIAlert != null && overSpeedUIAlert.isShowing()) {
            return;
        }
        if (overSpeedUIAlert == null) {
            overSpeedUIAlert = new MoreSettingUIAlert.Builder(mContext,
                    getString(R.string.str_carsetting_more_2),
                    getString(R.string.str_carsetting_more_speed),
                    R.mipmap.ic_speed, 0);
        }
        overSpeedUIAlert.create().show();
    }

    private void openFatigueDrive() {
        if (fatDriveUIAlert != null && fatDriveUIAlert.isShowing()) {
            return;
        }
        if (fatDriveUIAlert == null) {
            fatDriveUIAlert = new MoreSettingUIAlert.Builder(mContext,
                    getString(R.string.str_carsetting_more_3),
                    getString(R.string.str_carsetting_more_hour),
                    R.mipmap.ic_hour, 1);
        }
        fatDriveUIAlert.create().show();
    }

    // 打开自定义提示窗口
    @SuppressLint("RtlHardcoded")
    private void openTipsDialog(String title, String content, int width, int height) {
        if (detailUIAlert != null && detailUIAlert.isShowing()) {
            return;
        }
        if (detailUIAlert == null) {
            detailUIAlert = new DetailsUIAlert.Builder(mContext);
        }
        detailUIAlert.create(title, content, width, height).show();
    }


    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.rl_lock_tips:
                // 设防提示
                openLockTips();
                break;
            case R.id.rl_child_lock:
                openChildLock();
                break;
            case R.id.sw_maintain_tips:
                // 保养里程复位
                if (viewModel.getMaintainTips().getValue() == 1) {
                    viewModel.setMaintainTips(0);
                    presenter.setMaintainTips(0);
                } else {
                    viewModel.setMaintainTips(1);
                    presenter.setMaintainTips(1);
                }
                carHandler.sendMessageDelayed(MessageConst.CAR_MAINTAIN_TIPS);
                break;
            case R.id.rl_maintain_reset:
                openMaintainReset();
                break;
            case R.id.rl_safe:
                openDriveAirbag();
                break;
            case R.id.ll_overspeed:
                openOverSpeed();
                break;
            case R.id.ll_fatugue_drive:
                openFatigueDrive();
                break;
            case R.id.sw_auto_window:
                // 自动锁车升窗
                if (viewModel.getLockAutoRaiseWindow().getValue() == 1) {
                    viewModel.setLockAutoRaiseWindow(0);
                    presenter.setLockAutoRaiseWindow(0);
                } else {
                    viewModel.setLockAutoRaiseWindow(1);
                    presenter.setLockAutoRaiseWindow(1);
                }
                carHandler.sendMessageDelayed(MessageConst.CAR_LOCK_AUTO_RAISE_WINDOW);
                break;
            case R.id.sw_auto_lock:
                // 自动落锁
                if (viewModel.getAutomaticLocking().getValue() == 1) {
                    viewModel.setAutomaticLocking(0);
                    presenter.setAutomaticLocking(0);
                } else {
                    viewModel.setAutomaticLocking(1);
                    presenter.setAutomaticLocking(1);
                }
                carHandler.sendMessageDelayed(MessageConst.CAR_AUTOMATIC_LOCKING);
                break;
            case R.id.sw_auto_unlock:
                // 驻车自动解锁
                if (viewModel.getAutomaticParkingUnlock().getValue() == 1) {
                    viewModel.setAutomaticParkingUnlock(0);
                    presenter.setAutomaticParkingUnlock(0);
                } else {
                    viewModel.setAutomaticParkingUnlock(1);
                    presenter.setAutomaticParkingUnlock(1);
                }
                carHandler.sendMessageDelayed(MessageConst.CAR_AUTOMATIC_PARKING_UNLOCK);
                break;
            case R.id.sw_wiper_repair:
                // 雨刮维修模式
                if (viewModel.getWiperRepairMode().getValue() == 1) {
                    viewModel.setWiperRepairMode(0);
                    presenter.setWiperRepairMode(0);
                } else {
                    viewModel.setWiperRepairMode(1);
                    presenter.setWiperRepairMode(1);
                }
                carHandler.sendMessageDelayed(MessageConst.CAR_WIPER_REPAIR_MOD);
                break;
            case R.id.sw_overspeed:
                // 超速报警
                if (viewModel.getOverSpeed().getValue() == 1) {
                    viewModel.setOverSpeed(0);
                    presenter.setOverSpeed(0);
                } else {
                    viewModel.setOverSpeed(1);
                    presenter.setOverSpeed(1);
                }
                carHandler.sendMessageDelayed(MessageConst.CAR_OVER_SPEED);
                break;
            case R.id.sw_fatigue_drive:
                // 疲劳驾驶提醒
                if (viewModel.getFatigueDrivingReminder().getValue() == 1) {
                    viewModel.setFatigueDrivingReminder(0);
                    presenter.setFatigueDrivingReminder(0);
                } else {
                    viewModel.setFatigueDrivingReminder(1);
                    presenter.setFatigueDrivingReminder(1);
                }
                carHandler.sendMessageDelayed(MessageConst.CAR_FATIGUE_DRIVING_REMINDER);
                break;
            case R.id.iv_safe_tips:
                openTipsDialog(getString(R.string.str_carsetting_safe_1), getString(R.string.str_carsetting_safe_7), 1128, 478);
                break;
            default:
                break;
        }
    }

    private void selWiperSensTranslate() {
        binding.segmentedPickerView.setSelectedIndex(swWiperSens, true);
    }

    private void initPicker() {
        binding.segmentedPickerView.setItems(R.string.str_carsetting_wiper_sens_1, R.string.str_carsetting_wiper_sens_2,
                R.string.str_carsetting_wiper_sens_3, R.string.str_carsetting_wiper_sens_4);
    }

    /**
     * @param msg
     */
    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.CAR_HUD_ROATE:
                // 后尾门高度
                int roateStatus = presenter.getHudRoate();
                Log.d(TAG, "roateStatus:" + roateStatus);
                if (roateStatus != viewModel.getHudRoate().getValue()) {
                    viewModel.setHudRoate(roateStatus);
                }
                break;
            case MessageConst.CAR_WIPERSENS:
                // 雨刮器灵敏度
                int wiperSensStatus = presenter.getWiperSens();
                if (wiperSensStatus != viewModel.getWiperSensitivity().getValue()) {
                    swWiperSens = wiperSensStatus;
                    viewModel.setWiperSensitivity(wiperSensStatus);
                }
                break;
            case MessageConst.CAR_LOCK_AUTO_RAISE_WINDOW:
                // 锁车自动升窗
                int raiseStatus = presenter.getLockAutoRaiseWindow();
                if (raiseStatus != viewModel.getLockAutoRaiseWindow().getValue()) {
                    viewModel.setLockAutoRaiseWindow(raiseStatus);
                }
                break;
            case MessageConst.CAR_DEFENSE_REMINDER:
                // 设防提示
                int defenseStatus = presenter.getDefenseReminder();
                if (defenseStatus != viewModel.getDefenseReminder().getValue()) {
                    lockTipsStatus = defenseStatus;
                    viewModel.setDefenseReminder(defenseStatus);
                }
                break;
            case MessageConst.CAR_LEFT_CHILD_LOCK:
                // 儿童锁-左
                int leftCLockStatus = presenter.getLeftChildLock();
                if (leftCLockStatus != viewModel.getLeftChildLock().getValue()) {
                    viewModel.setLeftChildLock(leftCLockStatus);
                }
                break;
            case MessageConst.CAR_RIGHT_CHILD_LOCK:
                // 儿童锁-右
                int rCLockStatus = presenter.getRightChildLock();
                if (rCLockStatus != viewModel.getRightChildLock().getValue()) {
                    viewModel.setRightChildLock(rCLockStatus);
                }
                break;
            case MessageConst.CAR_AUTOMATIC_LOCKING:
                // 自动落锁
                int auLockStatus = presenter.getAutomaticLocking();
                if (auLockStatus != viewModel.getAutomaticLocking().getValue()) {
                    viewModel.setAutomaticLocking(auLockStatus);
                }
                break;
            case MessageConst.CAR_AUTOMATIC_PARKING_UNLOCK:
                // 驻车自动解锁
                int pUnlockStatus = presenter.getAutomaticParkingUnlock();
                if (pUnlockStatus != viewModel.getAutomaticParkingUnlock().getValue()) {
                    viewModel.setAutomaticParkingUnlock(pUnlockStatus);
                }
                break;
            case MessageConst.CAR_WIPER_REPAIR_MOD:
                // 雨刮维修模式
                int wRepairStatus = presenter.getWiperRepairMode();
                if (wRepairStatus != viewModel.getWiperRepairMode().getValue()) {
                    viewModel.setWiperRepairMode(wRepairStatus);
                }
                break;
            case MessageConst.CAR_OVER_SPEED:
                // 超速报警
                int oSpeedStatus = presenter.getOverSpeed();
                if (oSpeedStatus != viewModel.getOverSpeed().getValue()) {
                    viewModel.setOverSpeed(oSpeedStatus);
                }
                break;
            case MessageConst.CAR_FATIGUE_DRIVING_REMINDER:
                // 疲劳驾驶提醒
                int fDrivingStatus = presenter.getFatigueDrivingReminder();
                if (fDrivingStatus != viewModel.getFatigueDrivingReminder().getValue()) {
                    viewModel.setFatigueDrivingReminder(fDrivingStatus);
                }
                break;
            case MessageConst.CAR_MAINTAIN_TIPS:
                // 保养提示
                int mTipsStatus = presenter.getMaintainTips();
                if (mTipsStatus != viewModel.getMaintainTips().getValue()) {
                    viewModel.setMaintainTips(mTipsStatus);
                }
                break;
            case MessageConst.CAR_DRIVE_AIR_BAG:
                // 副驾安全气囊
                int driveBagStatus = presenter.getDriveAirBag();
                if (driveBagStatus != viewModel.getDriveAirBag().getValue()) {
                    switchDriveBagFlag = false;
                    driveBagFlag = driveBagStatus;
                    viewModel.setDriveAirBag(driveBagStatus);
                    EToast.showToast(mContext, "系统设置副驾安全⽓囊失败", 0, false);
                } else {
                    switchDriveBagFlag = true;
                }
                driveAirbagUIAlert.setSwitchDriveBagFlag(switchDriveBagFlag);
                break;
            default:
                break;
        }
    }

    /**
     * @return
     */
    @Override
    public boolean isActive() {
        return isActive;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    /**
     * Fragment 销毁时，做进一步清理，比如关闭对话框
     */
    @Override
    public void onDestroy() {
        super.onDestroy();
        // 如果对话框仍然显示，关闭它
        if (lockTipsUIAlert != null && lockTipsUIAlert.create().isShowing()) {
            lockTipsUIAlert.create().dismiss();
        }
        if (childLockUIAlert != null && childLockUIAlert.create().isShowing()) {
            childLockUIAlert.create().dismiss();
        }
        // 释放 Presenter 的引用，若有相应的释放方法
        presenter = null;
        isActive = false;
        if (binding != null) {
            binding = null;
        }
        if (manager != null) {
            manager.removeCallback(TAG);
            manager.unregisterListener(carPropertyIds);
            manager = null;
        }
    }
}
