package com.bitech.vehiclesettings.bean;

public class SystemDatas extends BaseData {
    private String tag;
    /**
     * 分析与改进
     */
    private boolean analysisFlag = true;

    @Override
    public String getTag() {
        return tag;
    }

    @Override
    public void setTag(String tag) {
        this.tag = tag;
    }

    public boolean isAnalysisFlag() {
        return analysisFlag;
    }

    public void setAnalysisFlag(boolean analysisFlag) {
        this.analysisFlag = analysisFlag;
    }

    private int autoCalibration;
    public int getAutoCalibration() {
        return autoCalibration;
    }
    public void setAutoCalibration(int autoCalibration) {
        this.autoCalibration = autoCalibration;
    }
}
