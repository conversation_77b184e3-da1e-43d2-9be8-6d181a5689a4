package com.bitech.vehiclesettings.utils;

import android.content.res.AssetFileDescriptor;
import android.media.AudioAttributes;
import android.media.MediaPlayer;

public class MusicPlayer {
    private MediaPlayer mediaPlayer;

    // 初始化方法
    public void initMediaPlayer(AssetFileDescriptor afd) {
        try {
            mediaPlayer = new MediaPlayer();
            // 配置AudioAttributes
            AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)          // 设置使用场景为媒体播放
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC) // 内容类型为音乐
                    .build();
            // 应用音频属性
            mediaPlayer.setAudioAttributes(audioAttributes);
            mediaPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
            // 异步准备
            mediaPlayer.prepareAsync();

            // 准备完成监听
            mediaPlayer.setOnPreparedListener(mp -> {
                // 此处可添加播放启动逻辑
                mp.start();
            });

            // 错误监听
            mediaPlayer.setOnErrorListener((mp, what, extra) -> {
                releaseMediaPlayer();
                return false;
            });

        } catch (Exception e) {
            e.printStackTrace();
            releaseMediaPlayer();
        }
    }

    // 释放资源方法
    public void releaseMediaPlayer() {
        if (mediaPlayer != null) {
            try {
                mediaPlayer.stop();
                mediaPlayer.release();
            } finally {
                mediaPlayer = null;
            }
        }
    }
}
