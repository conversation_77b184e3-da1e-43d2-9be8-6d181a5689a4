package com.bitech.vehiclesettings.utils;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class Bit64Utils {
    /**
     * 设置单个bit的值
     *
     * @param original 原始值
     * @param position 位位置(0-63)
     * @param value    要设置的值(true=1, false=0)
     * @return 修改后的值
     */
    public static long setBit(long original, int position, int value) {
        if (position < 0 || position > 63) return original;
        if (value == 1) {
            return original | (1L << position);  // 置1操作
        } else {
            return original & ~(1L << position);  // 置0操作
        }

    }

    /**
     * 设置指定位为1
     *
     * @param original 原始值
     * @param position 位位置(0-63)
     * @return 设置后的值
     */
    public static long setBit(long original, int position) {
        if (position < 0 || position > 63) {
            throw new IllegalArgumentException("Position must be 0-63");
        }
        return original | (1L << position);
    }

    /**
     * 设置指定位为0
     *
     * @param original 原始值
     * @param position 位位置(0-63)
     * @return 设置后的值
     */
    public static long clearBit(long original, int position) {
        if (position < 0 || position > 63) {
            throw new IllegalArgumentException("Position must be 0-63");
        }
        return original & ~(1L << position);
    }

    /**
     * 切换指定位状态
     *
     * @param original 原始值
     * @param position 位位置(0-63)
     * @return 切换后的值
     */
    public static long toggleBit(long original, int position) {
        if (position < 0 || position > 63) {
            throw new IllegalArgumentException("Position must be 0-63");
        }
        return original ^ (1L << position);
    }

    /**
     * 检查指定位是否为1
     *
     * @param value    要检查的值
     * @param position 位位置(0-63)
     * @return 该位是否为1
     */
    public static boolean isBitSet(long value, int position) {
        if (position < 0 || position > 63) {
            throw new IllegalArgumentException("Position must be 0-63");
        }
        return (value & (1L << position)) != 0;
    }

    /**
     * 检查指定位是否为1
     *
     * @param value    要检查的值
     * @param position 位位置(0-63)
     * @return 该位是否为1
     */
    public static long getBitSet(long value, int position) {
        if (position < 0 || position > 63) {
            throw new IllegalArgumentException("Position must be 0-63");
        }
        return (value & (1L << position));
    }

    /**
     * 获取从start到end位的组合值(包含两端)
     *
     * @param value 原始值
     * @param start 起始位(0-63)
     * @param end   结束位(≥start且≤63)
     * @return 提取的位组合值(右对齐)
     */
    public static long getBitsRange(long value, int start, int end) {
        if (start < 0 || end > 63 || start > end) {
            throw new IllegalArgumentException("Invalid bit range");
        }
        long mask = (1L << (end - start + 1)) - 1;
        return (value >>> start) & mask;
    }

    /**
     * 获取指定位数组的组合值
     *
     * @param value     原始值
     * @param positions 位位置数组(0-63)
     * @return 组合值(按输入顺序从低位到高位排列)
     */
    public static long getBitsCombination(long value, int... positions) {
        long result = 0L;
        for (int i = 0; i < positions.length; i++) {
            int pos = positions[i];
            if (pos < 0 || pos > 63) continue;
            if ((value & (1L << pos)) != 0) {
                result |= (1L << i);
            }
        }
        return result;
    }

    /**
     * 批量设置位
     *
     * @param original  原始值
     * @param positions 要设置的位数组
     * @return 设置后的值
     */
    public static long setBits(long original, int... positions) {
        long mask = 0L;
        for (int pos : positions) {
            if (pos < 0 || pos > 63) continue;
            mask |= (1L << pos);
        }
        return original | mask;
    }

    /**
     * 设置连续位范围的值
     *
     * @param original  原始值
     * @param start     起始位(0-63)
     * @param end       结束位(≥start且≤63)
     * @param bitsValue 要设置的值(右对齐)
     * @return 设置后的值
     */
    public static long setBitsRange(long original, int start, int end, long bitsValue) {
        if (start < 0 || end > 63 || start > end) {
            throw new IllegalArgumentException("Invalid bit range");
        }
        long mask = ((1L << (end - start + 1)) - 1) << start;
        return (original & ~mask) | ((bitsValue << start) & mask);
    }

    /**
     * 批量设置指定位为1
     *
     * @param original  原始值
     * @param positions 位位置数组(0-63)
     * @return 设置后的值
     */
    public static long setMultipleBits(long original, int... positions) {
        long result = original;
        for (int pos : positions) {
            if (pos >= 0 && pos <= 63) {
                result |= (1L << pos);
            }
        }
        return result;
    }

    /**
     * 批量清除指定位为0
     *
     * @param original  原始值
     * @param positions 位位置数组(0-63)
     * @return 清除后的值
     */
    public static long clearMultipleBits(long original, int... positions) {
        long result = original;
        for (int pos : positions) {
            if (pos >= 0 && pos <= 63) {
                result &= ~(1L << pos);
            }
        }
        return result;
    }

    /**
     * 将十进制long值转为十六进制字符串
     * @param decimal 十进制数值
     * @param withPrefix 是否包含0x前缀
     * @param minLength 最小输出长度(不足补前导0)
     * @return 十六进制字符串(大写)
     */
    public static String toHexString(long decimal, boolean withPrefix, int minLength) {
        String hex = Long.toHexString(decimal).toUpperCase();
        if(hex.length() < minLength) {
            hex = "0".repeat(minLength - hex.length()) + hex;
        }
        return withPrefix ? "0x" + hex : hex;
    }

    // 保留原有位操作方法...
    public static long setBit(long original, int position, boolean value) {
        if(position < 0 || position > 63) return original;
        return value ? original | (1L << position) : original & ~(1L << position);
    }
}
