package com.bitech.vehiclesettings.view.common;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.SeekBar;

import androidx.core.content.ContextCompat;

import com.bitech.vehiclesettings.R;

public class ImageSeekBarView extends FrameLayout {

    private SeekBar seekBar;
    private ImageView icon;

    public ImageSeekBarView(Context context) {
        super(context);
        init(context, null);
    }

    public ImageSeekBarView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public ImageSeekBarView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    public SeekBar getSeekBar() {
        return seekBar;
    }

    private void init(Context context, AttributeSet attrs) {
        LayoutInflater.from(context).inflate(R.layout.view_image_seek_bar, this, true);
        seekBar = findViewById(R.id.sb);
        icon = findViewById(R.id.iv);

        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.ImageSeekBarView);

            // 图标属性
            if (a.hasValue(R.styleable.ImageSeekBarView_iconSrc)) {
                icon.setImageResource(a.getResourceId(R.styleable.ImageSeekBarView_iconSrc, 0));
            }

            int iconWidth = a.getDimensionPixelSize(R.styleable.ImageSeekBarView_iconWidth, dpToPx(context, 21.33f));
            int iconHeight = a.getDimensionPixelSize(R.styleable.ImageSeekBarView_iconHeight, dpToPx(context, 21.33f));
            int marginStart = a.getDimensionPixelSize(R.styleable.ImageSeekBarView_iconMarginStart, dpToPx(context, 13.33f));

            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) icon.getLayoutParams();
            lp.width = iconWidth;
            lp.height = iconHeight;
            lp.setMarginStart(marginStart);
            icon.setLayoutParams(lp);

            // 解析 max 属性
            if (a.hasValue(R.styleable.ImageSeekBarView_max)) {
                int max = a.getInt(R.styleable.ImageSeekBarView_max, 10);
                seekBar.setMax(max);
            }
            if (a.hasValue(R.styleable.ImageSeekBarView_min)) {
                int min = a.getInt(R.styleable.ImageSeekBarView_min, 0);
                seekBar.setMin(min);
            }
            if (a.hasValue(R.styleable.ImageSeekBarView_progress)) {
                int progress = a.getInt(R.styleable.ImageSeekBarView_progress, 0);
                seekBar.setProgress(progress);
            }
            if (a.hasValue(R.styleable.ImageSeekBarView_seekBarWidth)) {
                int seekBarWidth = a.getDimensionPixelSize(R.styleable.ImageSeekBarView_seekBarWidth, LayoutParams.MATCH_PARENT);
                ViewGroup.LayoutParams seekBarParams = seekBar.getLayoutParams();
                seekBarParams.width = seekBarWidth;
                seekBar.setLayoutParams(seekBarParams);
            }
            if (a.hasValue(R.styleable.ImageSeekBarView_seekBarHeight)) {
                int seekBarHeight = a.getDimensionPixelSize(R.styleable.ImageSeekBarView_seekBarHeight, dpToPx(context, 4.33f));
                ViewGroup.LayoutParams seekBarParams = seekBar.getLayoutParams();
                seekBarParams.height = seekBarHeight;
                seekBar.setLayoutParams(seekBarParams);
            }
            if (a.hasValue(R.styleable.ImageSeekBarView_radius)) {
                int cornerRadius = a.getDimensionPixelSize(R.styleable.ImageSeekBarView_radius, dpToPx(context, 4.33f));

                GradientDrawable bg = new GradientDrawable();
                bg.setCornerRadius(cornerRadius);
                setBackground(bg);
                setClipToOutline(true);
            }

            a.recycle();
        }
    }

    private int dpToPx(Context context, float dp) {
        return (int) (dp * context.getResources().getDisplayMetrics().density + 0.5f);
    }

    public void setProgress(int progress) {
        seekBar.setProgress(progress);
    }

    public int getProgress() {
        return seekBar.getProgress();
    }

    public void setMax(int max) {
        seekBar.setMax(max);
    }

    public int getMax() {
        return seekBar != null ? seekBar.getMax() : 0;
    }

    public void setOnSeekBarChangeListener(SeekBar.OnSeekBarChangeListener listener) {
        seekBar.setOnSeekBarChangeListener(listener);
    }

    public void setSeekBarStyle(Context context, SeekBar seekBar) {
        if (seekBar.getProgress() == seekBar.getMax()) {
            seekBar.setProgressDrawable(context.getDrawable(R.drawable.progress_blue_white_max));
        } else {
            seekBar.setProgressDrawable(context.getDrawable(R.drawable.progress_blue_white));
        }
    }
    public void setSeekBarStyle(Context context, SeekBar seekBar,int maxStyleColor,int styleColor) {
        if (seekBar.getProgress() == seekBar.getMax()) {
            seekBar.setProgressDrawable(context.getDrawable(maxStyleColor));
        } else {
            seekBar.setProgressDrawable(context.getDrawable(styleColor));
        }
    }

    public void setIcon(int resId) {
        icon.setImageResource(resId);
    }

}