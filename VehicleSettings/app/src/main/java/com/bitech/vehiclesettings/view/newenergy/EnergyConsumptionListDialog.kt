package com.bitech.vehiclesettings.view.newenergy

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableString
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.interfaces.newenergy.INewEnergyListener
import com.bitech.platformlib.manager.SystemManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogEnergyConsumptionListBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * @Description: 能耗清单
 **/
class EnergyConsumptionListDialog(context: Context) : BaseDialog(context), LifecycleOwner,
    View.OnClickListener {

    private val lifecycleRegistry = LifecycleRegistry(this)

    override val lifecycle: Lifecycle
        get() = lifecycleRegistry

    private val mainHandler = Handler(Looper.getMainLooper())
    private var globalJob: Job? = null
    private var carNewEnergyManager = NewEnergyViewModel.newEnergyManager
    private val systemManager: SystemManager by lazy(LazyThreadSafetyMode.PUBLICATION) {
        BitechCar.getInstance().getServiceManager(BitechCar.CAR_SYSTEM_MANAGER) as SystemManager
    }

    // 能耗清单详情弹窗
    private lateinit var binding: DialogEnergyConsumptionListBinding

    private val colorSpan = ForegroundColorSpan(context.getColor(R.color.text_color_2))
    private val sizeSpan = AbsoluteSizeSpan(28)

    private var powerUnitText = POWER_UNIT_WH
    private var powerUnitLength = if (powerUnitText == POWER_UNIT_KWH) 10 else 5

    private val carPropertyCallback: INewEnergyListener by lazy {
        object : INewEnergyListener {

            override fun onTotalMileageStartup(value: Float) {
                val totalMileageStartup = getFinalValueGreaterThan0(value)
                setStyledText(binding.tvTotalMileageStartup, "$totalMileageStartup km", 2)
            }

            override fun onTotalMileageClearZero(value: Float) {
                val totalMileageClearZero = getFinalValueGreaterThan0(value)
                setStyledText(binding.tvTotalMileageResetting, "$totalMileageClearZero km", 2)
            }

            override fun onTotalMileageCharging(value: Float) {
                val totalMileageCharging = getFinalValueGreaterThan0(value)
                setStyledText(binding.tvTotalMileage, "$totalMileageCharging km", 2)
            }

            override fun onTotalMileageRefueling(value: Float) {
                val totalMileageRefueling = getFinalValueGreaterThan0(value)
                setStyledText(binding.tvTotalMileageRefueling, "$totalMileageRefueling km", 2)
            }

            override fun onAvgPowerConsumptionStartup(value: Float) {
                val avgPowerConsumptionStartup = getFinalValue(value)
                setStyledText(
                    binding.tvAvgPowerConsumptionStartup,
                    "$avgPowerConsumptionStartup $powerUnitText",
                    powerUnitLength
                )
            }

            override fun onAvgPowerConsumptionClearZero(value: Float) {
                val avgPowerConsumptionClearZero = getFinalValue(value)
                setStyledText(
                    binding.tvAvgPowerConsumptionResetting,
                    "$avgPowerConsumptionClearZero $powerUnitText",
                    powerUnitLength
                )
            }

            override fun onAvgPowerConsumptionCharging(value: Float) {
                val avgPowerConsumptionCharging = getFinalValue(value)
                setStyledText(
                    binding.tvAvgPowerConsumption,
                    "$avgPowerConsumptionCharging $powerUnitText",
                    powerUnitLength
                )
            }

//            override fun onAvgPowerConsumptionRefueling(value: Float) {
//                super.onAvgPowerConsumptionRefueling(value)
//            }

            override fun onAvgSpeedStartup(value: Float) {
                val avgSpeedStartup = getFinalValueGreaterThan0(value)
                setStyledText(binding.tvAverageSpeedStartup, "$avgSpeedStartup km/h", 4)
            }

            override fun onAvgSpeedClearZero(value: Float) {
                val avgSpeedClearZero = getFinalValueGreaterThan0(value)
                setStyledText(binding.tvAverageSpeedResetting, "$avgSpeedClearZero km/h", 4)
            }

            override fun onAvgSpeedCharging(value: Float) {
                val avgSpeedCharging = getFinalValueGreaterThan0(value)
                setStyledText(binding.tvAverageSpeed, "$avgSpeedCharging km/h", 4)
            }

            override fun onAvgSpeedRefueling(value: Float) {
                val avgSpeedRefueling = getFinalValueGreaterThan0(value)
                setStyledText(binding.tvAverageSpeedRefueling, "$avgSpeedRefueling km/h", 4)
            }
        }
    }

    private fun setStyledText(textView: TextView, text: String, index: Int) {
        val spannableString = SpannableString(text)
        spannableString.setSpan(
            sizeSpan, text.length - index, text.length, Spannable.SPAN_EXCLUSIVE_INCLUSIVE
        )
        spannableString.setSpan(
            colorSpan, text.length - index, text.length, Spannable.SPAN_EXCLUSIVE_INCLUSIVE
        )
        mainHandler.post {
            textView.text = spannableString
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleRegistry.currentState = Lifecycle.State.CREATED
        LogUtil.d(TAG, "onCreate : ")
        binding = DialogEnergyConsumptionListBinding.inflate(layoutInflater)
        // 绑定自定义dialog视图
        setContentView(binding.root)
        // 初始化页面视图
        initView()
        // 初始化页面监听
        intiListener()
    }

    override fun onStart() {
        super.onStart()
        carNewEnergyManager.addCallback(TAG, carPropertyCallback)
        lifecycleRegistry.currentState = Lifecycle.State.STARTED
        isShow = true
        // 初始化页面数据
        initData()
    }

    override fun onStop() {
        lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
        carNewEnergyManager.removeCallback(TAG)
        super.onStop()
        isShow = false
    }

    /**
     * 初始化页面视图.
     *
     */
    private fun initView() {
        LogUtil.d(TAG, "initView : ")
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG
        attributes?.width = 1992
        attributes?.height = 1128
        window?.attributes = attributes
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        globalJob?.cancel()
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss : ")
        globalJob?.cancel()
        super.dismiss()
    }

    /**
     * 初始化页面监听.
     *
     */
    private fun intiListener() {
        LogUtil.d(TAG, "intiListener : ")

        // 页面相关事件监听设置
        binding.apply {
            neResetting.setOnClickListener(this@EnergyConsumptionListDialog)
        }
    }

    /**
     * 初始化页面数据.
     *
     */
    private fun initData() {
        LogUtil.d(TAG, "initData: ")
        globalJob = GlobalScope.launch(Dispatchers.Default) {
            //获取油耗单位和电耗单位
            val fuelUnit = systemManager.averageFuelUnit
            val powerUnit = systemManager.averagePowerUnit
            powerUnitText = if (powerUnit == 1) POWER_UNIT_KWH else POWER_UNIT_WH
            powerUnitLength = if (powerUnitText == POWER_UNIT_KWH) 10 else 5
            //总里程
            val totalDriveDistance =
                carNewEnergyManager.totalDriveDistance.takeIf { it >= 0 && it != Float.MIN_VALUE }
                    ?: ERROR_VALUE_TEXT
            //获取当前模式的续航里程
            val enduranceCurrentMode =
                (carNewEnergyManager.enduranceElectricCurrentMode + carNewEnergyManager.enduranceFuelCurrentMode)
                    .takeIf { it >= 0 } ?: ERROR_VALUE_TEXT

            LogUtil.d(
                TAG, "initData: fuelUnit=$fuelUnit, powerUnit=$powerUnit, " +
                        "totalDriveDistance=$totalDriveDistance, enduranceCurrentMode=$enduranceCurrentMode"
            )

            //总计行驶里程-自启动后A
            val totalMileageStartup =
                getFinalValueGreaterThan0(carNewEnergyManager.totalMileageStartup)
            //总计行驶里程-自清零后B
            val totalMileageClearZero =
                getFinalValueGreaterThan0(carNewEnergyManager.totalMileageClearZero)
            //总计行驶里程-自充电后
            val totalMileageCharging =
                getFinalValueGreaterThan0(carNewEnergyManager.totalMileageCharging)
            //总计行驶里程-自加油后C
            val totalMileageRefueling =
                getFinalValueGreaterThan0(carNewEnergyManager.totalMileageRefueling)

            LogUtil.d(
                TAG, "initData: totalMileageStartup=$totalMileageStartup, " +
                        "totalMileageClearZero=$totalMileageClearZero, " +
                        "totalMileageCharging=$totalMileageCharging, " +
                        "totalMileageRefueling=$totalMileageRefueling"
            )

            // 平均电耗-自启动后A
            val avgPowerConsumptionStartup =
                getFinalValue(carNewEnergyManager.avgPowerConsumptionStartup)
            // 平均电耗-自清零后B
            val avgPowerConsumptionClearZero =
                getFinalValue(carNewEnergyManager.avgPowerConsumptionClearZero)
            // 平均电耗-自充电后
            val avgPowerConsumptionCharging =
                getFinalValue(carNewEnergyManager.avgPowerConsumptionCharging)
            // 平均电耗-自加油后C
//            val avgPowerConsumptionRefueling =
//                carNewEnergyManager.avgPowerConsumptionRefueling.takeIf { it != Float.MIN_VALUE }
//                    ?.let { String.format("%.1f", it).toFloat() }
//                    ?: ERROR_VALUE_TEXT
            LogUtil.d(
                TAG, "initData: avgPowerConsumptionStartup=$avgPowerConsumptionStartup, " +
                        "avgPowerConsumptionClearZero=$avgPowerConsumptionClearZero, " +
                        "avgPowerConsumptionCharging=$avgPowerConsumptionCharging"
            )

            // 平均速度-自启动后A
            val avgSpeedStartup = getFinalValueGreaterThan0(carNewEnergyManager.avgSpeedStartup)
            //平均速度-自清零后B
            val avgSpeedClearZero = getFinalValueGreaterThan0(carNewEnergyManager.avgSpeedClearZero)
            // 平均速度-自充电后
            val avgSpeedCharging = getFinalValueGreaterThan0(carNewEnergyManager.avgSpeedCharging)
            // 平均速度-自加油后C
            val avgSpeedRefueling = getFinalValueGreaterThan0(carNewEnergyManager.avgSpeedRefueling)
            LogUtil.d(
                TAG, "initData: avgSpeedStartup=$avgSpeedStartup, " +
                        "avgSpeedClearZero=$avgSpeedClearZero, " +
                        "avgSpeedCharging=$avgSpeedCharging, " +
                        "avgSpeedRefueling=$avgSpeedRefueling"
            )

            withContext(Dispatchers.Main) {

                binding.apply {
                    setStyledText(tvTotalMileage, "$totalMileageCharging km", 2)
                    setStyledText(
                        tvAvgPowerConsumption,
                        "$avgPowerConsumptionCharging $powerUnitText",
                        powerUnitLength
                    )
                    setStyledText(tvAverageSpeed, "$avgSpeedCharging km/h", 4)

                    setStyledText(tvTotalMileageRefueling, "$totalMileageRefueling km", 2)
                    setStyledText(tvAverageSpeedRefueling, "$avgSpeedRefueling km/h", 4)

                    setStyledText(tvTotalMileageStartup, "$totalMileageStartup km", 2)
                    setStyledText(
                        tvAvgPowerConsumptionStartup,
                        "$avgPowerConsumptionStartup $powerUnitText",
                        powerUnitLength
                    )
                    setStyledText(tvAverageSpeedStartup, "$avgSpeedStartup km/h", 4)

                    setStyledText(tvTotalMileageResetting, "$totalMileageClearZero km", 2)
                    setStyledText(
                        tvAvgPowerConsumptionResetting,
                        "$avgPowerConsumptionClearZero $powerUnitText",
                        powerUnitLength
                    )
                    setStyledText(tvAverageSpeedResetting, "$avgSpeedClearZero km/h", 4)

                }
            }
        }
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.neResetting -> {
                LogUtil.d(TAG, "onClick neZeroClearing")
                //“ICC_SelfClearing=0x1:Enable”
                carNewEnergyManager.setSelfClearing(1)

                setStyledText(binding.tvTotalMileageResetting, "$ERROR_VALUE_TEXT km", 2)
                setStyledText(
                    binding.tvAvgPowerConsumptionResetting,
                    "$ERROR_VALUE_TEXT $powerUnitText",
                    powerUnitLength
                )
                setStyledText(binding.tvAverageSpeedResetting, "$ERROR_VALUE_TEXT km/h", 4)
            }
        }
    }

    fun getFinalValueGreaterThan0(value: Float): String {
        return value.takeIf { it >= 0 && it != Float.MIN_VALUE }
            ?.let { String.format("%.1f", it).toFloat().toString() }
            ?: ERROR_VALUE_TEXT
    }

    fun getFinalValue(value: Float): String {
        return value.takeIf { it != Float.MIN_VALUE }
            ?.let { String.format("%.1f", it).toFloat().toString() }
            ?: ERROR_VALUE_TEXT
    }

    companion object {
        // 日志标志位
        private const val TAG = "EnergyConsumptionListDialog"
        private const val ERROR_VALUE_TEXT = "---"
        const val POWER_UNIT_WH = "Wh/km"
        const val POWER_UNIT_KWH = "kW·h/100km"
        var isShow = false
    }
}