package com.bitech.vehiclesettings.view.system;

import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.AppPermissionBean;
import com.bitech.vehiclesettings.bean.AppPermissionLevel;
import com.bitech.vehiclesettings.bean.PermissionAppBean;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionAppAuthoirzedItemBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionAppAuthoirzedMainBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class PermissionAppAuthorizedItemUIAlert extends BaseDialog {
    private static final String TAG = PermissionAppAuthorizedItemUIAlert.class.getSimpleName();
    private static PermissionAppAuthorizedItemUIAlert.onProgressChangedListener onProgressChangedListener;


    public PermissionAppAuthorizedItemUIAlert(@NonNull Context context) {
        super(context);
    }

    public PermissionAppAuthorizedItemUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PermissionAppAuthorizedItemUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static PermissionAppAuthorizedItemUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(PermissionAppAuthorizedItemUIAlert.onProgressChangedListener onProgressChangedListener) {
        PermissionAppAuthorizedItemUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private PermissionAppAuthorizedItemUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSPermissionAppAuthoirzedItemBinding binding;

        int position, index, status, lastStatus;
        AppPermissionBean bean;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private PermissionAppAuthorizedItemUIAlert dialog = null;

        public Builder(Context context, int position, int index, AppPermissionBean bean) {
            this.context = context;
            this.position = position;
            this.index = index;
            this.bean = bean;
        }


        public PermissionAppAuthorizedItemUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public PermissionAppAuthorizedItemUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PermissionAppAuthorizedItemUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSPermissionAppAuthoirzedItemBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128;
            layoutParams.height = 600;
            window.setAttributes(layoutParams);

            // 设置文本
            setText();
            // 初始化按钮
            initRadio();
            // 初始化保存
            initSave();

            return dialog;
        }

        private void initRadio() {
            // 获取当前授权的app权限
            status = onProgressChangedListener.getPermission(position, index);
            lastStatus = status;
            Log.d(TAG, "initPermission: " + status);

            // 直接为 RadioButton 设置选中状态变化监听器
            binding.rbEachUse.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked) {
                    status = 0;
                    Log.d(TAG, "onRadioButtonChecked: " + status);
                    bean.setLevel(AppPermissionLevel.ONE_TIME);
                    binding.rbEachUse.setChecked(true);
                    binding.rbUsePeriodOnly.setChecked(false);
                    binding.rbDisabled.setChecked(false);
                }
            });
            binding.rbUsePeriodOnly.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked) {
                    status = 1;
                    Log.d(TAG, "onRadioButtonChecked: " + status);
                    bean.setLevel(AppPermissionLevel.WHILE_USING);
                    binding.rbEachUse.setChecked(false);
                    binding.rbUsePeriodOnly.setChecked(true);
                    binding.rbDisabled.setChecked(false);
                }
            });
            binding.rbDisabled.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked) {
                    status = 2;
                    Log.d(TAG, "onRadioButtonChecked: " + status);
                    bean.setLevel(AppPermissionLevel.DENIED);
                    binding.rbEachUse.setChecked(false);
                    binding.rbUsePeriodOnly.setChecked(false);
                    binding.rbDisabled.setChecked(true);
                }
            });

            // 设置初始选中状态
            if (status == 0) {
                binding.rbEachUse.setChecked(true);
                binding.rbUsePeriodOnly.setChecked(false);
                binding.rbDisabled.setChecked(false);
            } else if (status == 1) {
                binding.rbEachUse.setChecked(false);
                binding.rbUsePeriodOnly.setChecked(true);
                binding.rbDisabled.setChecked(false);
            } else if (status == 2) {
                binding.rbEachUse.setChecked(false);
                binding.rbUsePeriodOnly.setChecked(false);
                binding.rbDisabled.setChecked(true);
            }
        }



        private void initSave() {
            dialog.setOnDismissListener(d -> {
                if (onProgressChangedListener != null) {
                    Log.d(TAG, "initSave: " + status);
                    int status = onProgressChangedListener.updateText(position, index, bean);
                    if (status == 1) {
                        EToast.showToast(context, "app授权修改成功", 1000, false);
                        lastStatus = status;
                    } else if (status == 0) {
                        EToast.showToast(context, "app授权修改失败", 1000, false);
                        if (lastStatus == 0) {
                            binding.rbEachUse.setChecked(true);
                        } else if (lastStatus == 1) {
                            binding.rbUsePeriodOnly.setChecked(true);
                        } else if (lastStatus == 2) {
                            binding.rbDisabled.setChecked(true);
                        }
                    }
                }
            });
        }

        private void setText() {
            binding.tvTitle.setText(context.getString(R.string.str_system_permission_app_authorized_item));
            if (position == 0) {
                binding.tvSubTitle.setText(context.getString(R.string.str_system_permission_camera_app_authorized_item));
            } else if (position == 1) {
                binding.tvSubTitle.setText(context.getString(R.string.str_system_permission_microphone_app_authorized_item));
            } else if (position == 2) {
                binding.tvSubTitle.setText(context.getString(R.string.str_system_permission_location_app_authorized_item));
            }
        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {

        void setPermissionTime(int position, int status);

        int getPermission(int position, int index);

        int updateText(int position, int index, AppPermissionBean bean);
    }
}
