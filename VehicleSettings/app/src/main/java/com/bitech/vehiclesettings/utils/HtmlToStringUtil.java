package com.bitech.vehiclesettings.utils;

import android.content.Context;
import android.os.Build;
import android.text.Html;
import android.text.Spanned;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

public class HtmlToStringUtil {

    /**
     * 将assets目录下的HTML文件转换为Spanned类型，适合显示在TextView中
     *
     * @param context   上下文
     * @param assetPath assets目录下的HTML文件路径
     * @return 转换后的Spanned类型内容
     */
    public static Spanned convertHtmlToSpanned(Context context, String assetPath) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            // 打开HTML文件的输入流
            InputStream inputStream = context.getAssets().open(assetPath);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            // 逐行读取HTML文件内容并拼接
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line).append("\n");
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
            return Html.fromHtml("文件读取失败，请检查HTML文件路径或文件内容。");
        }

        // 使用Html.fromHtml将HTML内容转换为Spanned类型
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return Html.fromHtml(stringBuilder.toString(), Html.FROM_HTML_MODE_LEGACY);
        } else {
            return Html.fromHtml(stringBuilder.toString());
        }
    }
}
