package com.bitech.vehiclesettings.view.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.BtDeviceBean
import com.bitech.vehiclesettings.databinding.DialogBtConnectSelectedBinding
import com.bitech.vehiclesettings.manager.CarConfigInfoManager
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: BtConnectSelectedDialog
 * 
 * @Date:  2024/6/6 13:23
 * @Description: 蓝牙连接选择弹窗.
 **/
class BtConnectSelectedDialog(
    context: Context,
    private val deviceOne: BtDeviceBean,
    private val deviceTwo: BtDeviceBean
) : Dialog(context, R.style.dialog), View.OnClickListener {

    // 蓝牙连接选择弹窗
    private lateinit var binding: DialogBtConnectSelectedBinding

    // 页面弹窗设备选择点击callback
    private var deviceDialogClickCallback: OnDeviceDialogClickCallback? = null

    /**
     * 生命周期-创建.
     *
     * @param savedInstanceState 状态保存对象
     */
    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        binding = DialogBtConnectSelectedBinding
                .bind(layoutInflater.inflate(R.layout.dialog_bt_connect_selected, null))
        setContentView(binding.root)
        // 初始化视图
        initView()
        // 初始化数据
        initData()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        LogUtil.d(TAG, "initView : ")
        // 设置对话框窗口属性
        val attributes = window?.attributes
        // 设置弹窗类型-系统弹窗
        attributes?.type = WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG
        attributes?.windowAnimations = 0
        window?.attributes = attributes
        // 外部点击可关闭
        setCanceledOnTouchOutside(true)
        // 页面监听设置
        binding.apply {
            // 对话框按钮监听
            dialogDeviceOneBtn.setOnClickListener(this@BtConnectSelectedDialog)
            dialogDeviceTwoBtn.setOnClickListener(this@BtConnectSelectedDialog)

        }
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    /**
     * 初始化页面数据.
     *
     */
    private fun initData() {
        LogUtil.d(TAG, "initData : ")
        // 设置设备按钮名称
        binding.dialogDeviceOneBtn.text = deviceOne.device.name ?: deviceOne.wiredName
        binding.dialogDeviceTwoBtn.text = deviceTwo.device.name ?: deviceTwo.wiredName
    }

    /**
     * dialog 按钮点击事件.
     *
     * @param view View
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.dialog_device_one_btn -> {
                LogUtil.d(TAG, "onClick : device one is click!")
                deviceDialogClickCallback?.onBtDeviceOne(deviceOne)
                dismiss()
            }

            R.id.dialog_device_two_btn -> {
                LogUtil.d(TAG, "onClick : device two is click!")
                deviceDialogClickCallback?.onBtDeviceTwo(deviceTwo)
                dismiss()
            }
        }
    }

    /**
     * 设备按钮选择事件监听.
     *
     * @param callback OnDeviceDialogClickCallback
     */
    fun setDialogClickCallback(callback: OnDeviceDialogClickCallback) {
        deviceDialogClickCallback = callback
    }

    /**
     * @ClassName: OnDeviceDialogClickCallback
     * 
     * @Date:  2024/6/6 13:27
     * @Description: 设备选择回调callback.
     **/
    interface OnDeviceDialogClickCallback {
        /**
         * 蓝牙设备1.
         *
         * @param device 蓝牙设备1
         */
        fun onBtDeviceOne(device: BtDeviceBean)

        /**
         * 蓝牙设备2.
         *
         * @param device 蓝牙设备2
         */
        fun onBtDeviceTwo(device: BtDeviceBean)
    }

    companion object {
        // 日志标志位
        private const val TAG = "BtConnectSelectedDialog"
    }
}