package com.bitech.vehiclesettings.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.lifecycle.ViewModelProvider;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.interfaces.light.ILightManagerListener;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.SegmentItemBean;
import com.bitech.vehiclesettings.bean.report.Content;
import com.bitech.vehiclesettings.databinding.FragmentLightOutBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.light.LightOutPresenter;
import com.bitech.vehiclesettings.presenter.light.LightOutPresenterListener;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.viewmodel.LightOutViewModel;

import java.util.ArrayList;

/**
 * 灯光
 *
 * <AUTHOR>
 */
public class LightOutFragment extends BaseFragment<FragmentLightOutBinding> implements View.OnClickListener, SafeHandlerCallback {
    private static final String TAG = LightOutFragment.class.getSimpleName();
    private int swLightHigh = 3;
    private int swLampDelay = 3;
    private volatile boolean isActive;
    private ArrayList<Content> dataList;
    private LightOutPresenterListener lightOutPresenter;
    private SafeHandler lightHandler;
    private LightOutViewModel viewModel;

    private LightManager manager = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);


    private void regLightListen() {
        manager.addCallback(TAG, new ILightManagerListener() {
            /**
             * 车控调节
             * @param status
             */
            @Override
            public void lightMainSwitchStsCallback(int status) {
                if (CommonUtils.isFastDoubleClick()) {
                    Log.d(TAG, "updateLampHighUI: isFastDoubleClick");
                    return;
                }
                int pos = lightOutPresenter.lampControlChange(status);
                Log.d(TAG, "lightMainSwitchStsCallback status: " + status);
                if (viewModel.getLampControl().getValue() != null && viewModel.getLampControl().getValue() != pos) {
                    viewModel.setLampControl(pos);
                }
                Prefs.put(PrefsConst.L_LAMP_CONTROL, pos);
            }


            /**
             * @param status
             */
            @Override
            public void getLightHeightStsCallback(int status) {

                Log.d(TAG, "getLightHeightStsCallback status: " + status);
                if (CommonUtils.isFastDoubleClick()) {
                    Log.d(TAG, "updateLampHighUI: isFastDoubleClick");
                    return;
                }
                // 大灯高度调节
                int pos = lightOutPresenter.hightChange(status);
                Prefs.put(PrefsConst.L_LAMP_HIGH, pos);
                updateLampHighUI(pos);
            }


            /**
             * @param status
             */
            @Override
            public void beamDelayStsCallback(int status) {
                Log.d(TAG, "beamDelayStsCallback status: " + status);
                if (CommonUtils.isFastDoubleClick()) {
                    Log.d(TAG, "updateLampHighUI: isFastDoubleClick");
                    return;
                }
                // 大灯延迟关闭
                int pos = delayChange(status);
                Prefs.put(PrefsConst.L_LAMP_DELAY, pos);
                updateLampDelayUI(pos);
            }

            private int delayChange(int status) {
                int vswLampDelay = 0;
                switch (status) {
                    case CarLight.BeamDelaySts.NOT_ACTIVE:
                        break;
                    case CarLight.BeamDelaySts.DELAY_0S:
                        vswLampDelay = 0;
                        break;
                    case CarLight.BeamDelaySts.DELAY_10S:
                        vswLampDelay = 1;
                        break;
                    case CarLight.BeamDelaySts.DELAY_20S:
                        vswLampDelay = 2;
                        break;
                    case CarLight.BeamDelaySts.DELAY_30S:
                        vswLampDelay = 3;
                        break;
                    case CarLight.BeamDelaySts.DELAY_60S:
                        vswLampDelay = 4;
                        break;
                }
                return vswLampDelay;
            }

            /**
             * @param status
             */
            @Override
            public void welcomeOpenStasCallback(int status) {
                Log.d(TAG, "welcomeOpenStasCallback status: " + status);
                if (CommonUtils.isFastDoubleClick()) {
                    Log.d(TAG, "updateLampHighUI: isFastDoubleClick");
                    return;
                }
                // 靠近迎宾
                if (viewModel.getApproachingWelcome().getValue() != null && viewModel.getApproachingWelcome().getValue() != status) {
                    viewModel.setApproachingWelcome(status);
                }
            }

            /**
             * @param status
             */
            @Override
            public void autoReadLightStsCallback(int status) {
                Log.d(TAG, " autoReadLightStsCallback status: " + status);
            }


            @Override
            public void powerDownLightOff(int status) {
                Log.d(TAG, "powerDownLightOff status: " + status);
            }

            /**
             * 后雾灯
             * @param status
             */
            @Override
            public void rearFogLightStsCallback(int status) {
                Log.d(TAG, "rearFogLightStsCallback status: " + status);
                if (CommonUtils.isFastDoubleClick()) {
                    Log.d(TAG, "updateLampHighUI: isFastDoubleClick");
                    return;
                }
                if (viewModel.getRearFogLamp().getValue() != null && viewModel.getRearFogLamp().getValue() != status) {
                    viewModel.setRearFogLamp(status);
                }
            }

            /**
             * @param success
             */
            @Override
            public void onConfigChanged(boolean success) {
                Log.d(TAG, "onConfigChanged status: " + success);
            }

        });
        manager.registerListener();
    }

    private void initObserver() {
        // 车灯控制
        viewModel.getLampControl().observe(getViewLifecycleOwner(), status -> {
            updateLampControlUI(status);
        });
        // 后雾灯
        viewModel.getRearFogLamp().observe(getViewLifecycleOwner(), status -> {
            updateRearFogLamp(status);
        });
        // 大灯高度调节

        // 大灯延时关闭

        viewModel.getApproachingWelcome().observe(getViewLifecycleOwner(), status -> {
            // 车灯设置 靠近迎宾
            updateApproachingWelcomeUI(status);
        });

        viewModel.getHighLowSwitch().observe(getViewLifecycleOwner(), status -> {
            // 智能远近光切换
            updateHighLowSwitchUI(status);
        });

        viewModel.getIntelligentWelcome().observe(getViewLifecycleOwner(), status -> {
            // 智能迎宾灯开关
            updateIntelligentWelcomeUI(status);
        });


    }

    private void updateLampControlUI(int status) {
        if (status == 3) {
            binding.tvLampControlTip.setVisibility(View.VISIBLE);
        } else {
            binding.tvLampControlTip.setVisibility(View.INVISIBLE);
        }
        binding.spvLightControl.setSelectedIndex(status, false);
    }

    private void updateRearFogLamp(int status) {
        binding.tvLightRearFog.setSelected(CommonUtils.IntToBool(status));
    }

    // 靠近迎宾
    private void updateApproachingWelcomeUI(int status) {
        binding.swApproachingWelcome.setChecked(CommonUtils.IntToBool(status));
    }

    // 智能远近光切换
    private void updateHighLowSwitchUI(int status) {
        binding.swHighLowSwitch.setChecked(CommonUtils.IntToBool(status));
    }

    // 智能迎宾灯开关
    private void updateIntelligentWelcomeUI(int status) {
        binding.swIntelligentWelcome.setChecked(CommonUtils.IntToBool(status));
        // 同步更新状态

    }

    // 智能迎宾灯状态
    private void updateIntelligentWelcomeStUI(int status) {

        if (status == 0) {
            binding.swIntelligentWelcome.setChecked(CommonUtils.IntToBool(status));
        }
    }


    // 大灯高度调节
    private void updateLampHighUI(int status) {
        if (status != swLightHigh) {
            swLightHigh = status;
            if (mContext != null) {
                mContext.runOnUiThread(() -> {
                    if (binding != null && binding.spvLightHigh != null) {
                        binding.spvLightHigh.setSelectedIndex(swLightHigh, false);
                    }
                });
            }


        }

    }

    // 大灯延时关闭
    // * @param status "0x0:Not Active
    //     *               0x1:0s
    //     *               0x2:10s
    //     *               0x3:20s
    //     *               0x4:30s
    //     *               0x5:60s
    //     *               0x6~0x7:Not Used
    private void updateLampDelayUI(int status) {
        if (status != swLampDelay) {
            swLampDelay = status;
            if (mContext != null) {
                mContext.runOnUiThread(() -> {
                    if (binding != null && binding.spvLampDelay != null) {
                        binding.spvLampDelay.setSelectedIndex(swLampDelay, false);
                    }
                });
            }
        }
    }

    /**
     * 车灯控制
     */
    private void initLightControlItem() {
        SegmentItemBean ItemBean1 = new SegmentItemBean(getResources().getString(R.string.str_control_close));
        SegmentItemBean ItemBean2 = new SegmentItemBean();
        ItemBean2.setImageResId(R.mipmap.ic_light_position);
        ItemBean2.setImageSelectedResId(R.mipmap.ic_light_position_sel);
        SegmentItemBean ItemBean3 = new SegmentItemBean();
        ItemBean3.setImageResId(R.mipmap.ic_light_nearbeam);
        ItemBean3.setImageSelectedResId(R.mipmap.ic_light_nearbeam_sel);
        SegmentItemBean ItemBean4 = new SegmentItemBean(getResources().getString(R.string.str_control_auot));
        binding.spvLightControl.setItems(ItemBean1, ItemBean2, ItemBean3, ItemBean4);
    }

    /**
     * 数据初始化
     */
    @Override
    protected void initData() {
        initLightControlItem();
        binding.spvLightHigh.setItems(R.string.str_light_low, R.string.str_light_lower, R.string.str_light_higher, R.string.str_light_high);
        binding.spvLampDelay.setItems(R.string.str_lamp_delay_0, R.string.str_lamp_delay_10, R.string.str_lamp_delay_20, R.string.str_lamp_delay_30, R.string.str_lamp_delay_60);
        swLightHigh = PrefsConst.DefaultValue.L_LAMP_HIGH;

        if (binding != null && binding.spvLightHigh != null) {
            binding.spvLightHigh.setSelectedIndex(swLightHigh, false);
        }
        if (binding != null && binding.spvLampDelay != null) {
            binding.spvLampDelay.setSelectedIndex(swLampDelay, false);
        }
        // 智能迎宾
        viewModel.setIntelligentWelcome(Prefs.get(PrefsConst.L_INTELLIGENT_WELCOME, PrefsConst.DefaultValue.L_INTELLIGENT_WELCOME));
    }


    /**
     * @param inflater
     * @param container
     * @return
     */
    @Override
    protected FragmentLightOutBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentLightOutBinding.inflate(getLayoutInflater());
        return binding;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isActive = true;
        lightOutPresenter = new LightOutPresenter(mContext);
        lightHandler = new SafeHandler(this);

    }

    @Override
    protected void initView() {
        if (viewModel == null) {
            viewModel = new ViewModelProvider(this).get(LightOutViewModel.class);
        }
        initData();
        initObserver();
        regLightListen();
        initDeviceSignal();
        // 滚动事件监听
        scrollListener();
    }

    /**
     * 滚动监听
     */
    private void scrollListener() {
        binding.scrollView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            Log.d(TAG, "light onScroll: " + scrollY);
            Prefs.put(PrefsConst.LIGHT_SCROLL_Y, scrollY);
            MainActivity activity = (MainActivity) getActivity();
            if (activity != null) {
                activity.getBinding().ivModel.handleScroll(scrollY);
            }
        });
    }

    /**
     * 初始化信号值
     */
    private void initDeviceSignal() {
        lightHandler.sendMessage(MessageConst.LIGHT_INIT_DATA);
    }


    @Override
    protected void setListener() {
        BindingUtil.bindClicks(this, binding.tvLightRearFog);
        BindingUtil.bindClicks(this, binding.swApproachingWelcome, binding.swHighLowSwitch, binding.swIntelligentWelcome);
        // 车灯控制
        binding.spvLightControl.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index, String text) {

            }

            @Override
            public void onItemClicked(int index, String text) {
                Log.d(TAG, "setListener: " + lightOutPresenter.getLampControl() + ",index:" + index);
                if (lightOutPresenter.getLampControl() != index) {
                    viewModel.setLampControl(index);

                    lightOutPresenter.setLampControl(index);
                    lightHandler.sendMessageDelayed(MessageConst.LIGHT_OUT_LAMP_CONTROL);
                }
            }
        });

        // 大灯高度调节
        binding.spvLightHigh.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index, String text) {

            }

            @Override
            public void onItemClicked(int index, String text) {
                Log.d(TAG, "binding.spvLightHigh setListener: " + binding.spvLightHigh.isSelected());
                if (lightOutPresenter.getLampHigh() != index) {
                    swLightHigh = index;

                    lightOutPresenter.setLampHigh(index);
                    lightHandler.sendMessageDelayed(MessageConst.LIGHT_LAMP_HIGH);
                }
            }
        });

        // 大灯延迟
        binding.spvLampDelay.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index, String text) {

            }

            @Override
            public void onItemClicked(int index, String text) {
                if (lightOutPresenter.getLampDelay() != index) {
                    swLampDelay = index;
                    lightOutPresenter.setLampDelay(index);
                    lightHandler.sendMessageDelayed(MessageConst.LIGHT_LAMP_DELAY);
                }
            }
        });

        // 滚动监听
        binding.scrollView.setScrollViewListener((x, y, oldX, oldY) -> {
            // 判断滚动方向
            boolean isScrollingUp = y > oldY;
            // 获取滚动距离
            int scrollY = binding.scrollView.getScrollY();
            int height = binding.scrollView.getHeight();
            float ratio = 0f;
            if (scrollY >= height) {
                ratio = 1f;
            } else {
                ratio = Math.abs((float) scrollY / height);
            }
            Log.d(TAG, "onScrollChanged: " + scrollY + ",height:" + binding.scrollView.getHeight() + ",ratio:" + ratio);
            // 根据比例更新UI

        });
    }

    @Override
    protected void initObserve() {

    }

    /**
     * @param view The view that was clicked.
     */
    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_light_rear_fog:
                int rearFogStatus = 0;
                if (!binding.tvLightRearFog.isSelected()) {
                    rearFogStatus = 1;
                }
                viewModel.setRearFogLamp(rearFogStatus);
                lightOutPresenter.setRearFogLamp(rearFogStatus);
                lightHandler.sendMessageDelayed(MessageConst.LIGHT_OUT_REAR_FOG_LAMP);
                break;
            case R.id.sw_approaching_welcome:
                // 靠近迎宾
                if (binding.swApproachingWelcome.isChecked()) {
                    viewModel.setApproachingWelcome(1);
                    lightOutPresenter.setApproachingWelcome(CarLight.WelcomeOpenSetCmd.ON);
                } else {
                    viewModel.setApproachingWelcome(0);
                    lightOutPresenter.setApproachingWelcome(CarLight.WelcomeOpenSetCmd.OFF);
                }
                lightHandler.sendMessageDelayed(MessageConst.LIGHT_APPROACHING_WELCOME);
                break;

            case R.id.sw_high_low_switch:
                // 远近光切换
                if (binding.swHighLowSwitch.isChecked()) {
                    viewModel.setHighLowSwitch(1);
                    lightOutPresenter.setHighLowSwitch(1);
                } else {
                    viewModel.setHighLowSwitch(0);
                    lightOutPresenter.setHighLowSwitch(0);
                }
                lightHandler.sendMessageDelayed(MessageConst.LIGHT_HIGH_LOW_SWITCH);
                break;
            case R.id.sw_intelligent_welcome:
                // 智能迎宾灯
                if (binding.swIntelligentWelcome.isChecked()) {
                    viewModel.setIntelligentWelcome(1);
                    lightOutPresenter.setIntelligentWelcome(1);
                } else {
                    viewModel.setIntelligentWelcome(0);
                    lightOutPresenter.setIntelligentWelcome(0);
                }
                lightHandler.sendMessageDelayed(MessageConst.LIGHT_INTELLIGENT_WELCOME);
                break;

            default:
                break;
        }
    }

    /**
     * @param msg
     */

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.LIGHT_INIT_DATA:
                handRearFogLamp();
                handLampControl();
                handLampHigh();
                handLampDelay();
                handApproachingWelcome();
                handHighLowSwitch();
                handIntelligentWelcome();
                break;
            case MessageConst.LIGHT_OUT_REAR_FOG_LAMP:
                handRearFogLamp();
                break;
            case MessageConst.LIGHT_OUT_LAMP_CONTROL:
                handLampControl();
                break;
            case MessageConst.LIGHT_LAMP_HIGH:
                handLampHigh();
                break;
            case MessageConst.LIGHT_LAMP_DELAY:
                handLampDelay();
                break;
            case MessageConst.LIGHT_APPROACHING_WELCOME:
                handApproachingWelcome();
                break;
            case MessageConst.LIGHT_HIGH_LOW_SWITCH:
                handHighLowSwitch();
                break;
            case MessageConst.LIGHT_INTELLIGENT_WELCOME:
                handIntelligentWelcome();
                break;

        }
    }

    private void handRearFogLamp() {
        int fogStatus = lightOutPresenter.getRearFogLamp();
        if (fogStatus != viewModel.getRearFogLamp().getValue()) {
            viewModel.setRearFogLamp(fogStatus);
        }
    }

    private void handLampControl() {
        int lampControlStatus = lightOutPresenter.getLampControl();
        if (lampControlStatus != viewModel.getLampControl().getValue()) {
            viewModel.setLampControl(lampControlStatus);
        }
    }

    private void handLampHigh() {
        int status = lightOutPresenter.getLampHigh();
        updateLampHighUI(status);
    }

    private void handLampDelay() {
        int delayStatus = lightOutPresenter.getLampDelay();
        // 更新视图状态
        updateLampDelayUI(delayStatus);
    }

    private void handApproachingWelcome() {
        int wStatus = lightOutPresenter.getApproachingWelcome();
        if (viewModel.getApproachingWelcome().getValue() != wStatus) {
            viewModel.setApproachingWelcome(wStatus);
        }
    }

    private void handHighLowSwitch() {
        // todo 识别此处为智驾信号接入,待需求释放
//        int highLowSwitchStatus = lightOutPresenter.getHighLowSwitch();
//        if (highLowSwitchStatus != viewModel.getHighLowSwitch().getValue()) {
//            viewModel.setHighLowSwitch(highLowSwitchStatus);
//        }
    }

    private void handIntelligentWelcome() {
        int intelligentWelcomeStatus = lightOutPresenter.getIntelligentWelcome();
        if (intelligentWelcomeStatus != viewModel.getIntelligentWelcome().getValue()) {
            viewModel.setIntelligentWelcome(intelligentWelcomeStatus);
        }
    }

    /**
     * @return
     */
    @Override
    public boolean isActive() {
        return isActive;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (manager != null) {
            manager.removeCallback(TAG);
            manager.unregisterListener();
            manager = null;
        }

        if (binding != null) {
            binding = null;
        }

    }


    public ArrayList<Content> getData() {
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
//        氛围灯开关	AtmosphereLampSw
//        亮度调节	LightSet
//        前排	FrontRowSw
//        后排	BackRowSw
//        远近光自动切换	ASHLBSw
        if (viewModel != null && viewModel.getHighLowSwitch() != null) {
            dataList.add(reportData(CommonConst.CodeId.ZB141805, CommonConst.Att.ASHLBSw, String.valueOf(viewModel.getHighLowSwitch().getValue())));
        }

//        智能迎宾	AutowelampSw
        if (viewModel != null && viewModel.getIntelligentWelcome() != null) {
            dataList.add(reportData(CommonConst.CodeId.ZB141808, CommonConst.Att.AutowelampSw, String.valueOf(viewModel.getIntelligentWelcome().getValue())));
        }

//        靠近迎宾	CourtesyLightSw
        if (viewModel != null && viewModel.getApproachingWelcome() != null) {
            dataList.add(reportData(CommonConst.CodeId.ZB141812, CommonConst.Att.CourtesyLightSw, String.valueOf(viewModel.getApproachingWelcome().getValue())));
        }

//        动态氛围灯	TrendSw
//        灯光效果	LightingEffect
//        大灯延时关闭	HLdelaySet
        int hLdelaySet = hLdelaySetChange(swLampDelay);
        dataList.add(reportData(CommonConst.CodeId.ZB141817, CommonConst.Att.HLdelaySet, String.valueOf(hLdelaySet)));
//        自动顶灯	AutoinLampSw

        return dataList;
    }

    private Content reportData(String attributeId, String locationId, String attributeValue) {
        Content content = new Content();
        content.setAttributeId(attributeId);
        content.setLocationId(locationId);
        content.setAttributeValue(attributeValue);
        return content;
    }

    private int hLdelaySetChange(int swLampDelay) {
        int vswLampDelay = 0;
        switch (swLampDelay) {
            case 0:
                vswLampDelay = 1;
                break;
            case 1:
                vswLampDelay = 2;
                break;
            case 2:
                vswLampDelay = 3;
                break;
            case 3:
                vswLampDelay = 4;
                break;
            case 4:
                vswLampDelay = 5;
                break;
        }
        return vswLampDelay;
    }

}
