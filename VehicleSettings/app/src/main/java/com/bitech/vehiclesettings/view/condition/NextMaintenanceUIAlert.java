package com.bitech.vehiclesettings.view.condition;

import android.content.Context;
import android.content.DialogInterface;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarCondition;
import com.bitech.vehiclesettings.databinding.DialogAlertConditionNextMaintenanceBinding;
import com.bitech.vehiclesettings.utils.GrayEffectHelper;
import com.bitech.vehiclesettings.utils.SingleSwitchListener;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class NextMaintenanceUIAlert extends BaseDialog {
    private static final String TAG = NextMaintenanceUIAlert.class.getSimpleName();
    private static NextMaintenanceUIAlert.onProgressChangedListener onProgressChangedListener;
    public static boolean isShow = false;

    public NextMaintenanceUIAlert(@NonNull Context context) {
        super(context);
    }

    public NextMaintenanceUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected NextMaintenanceUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static NextMaintenanceUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(NextMaintenanceUIAlert.onProgressChangedListener onProgressChangedListener) {
        NextMaintenanceUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private NextMaintenanceUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConditionNextMaintenanceBinding binding;
        private MaintenanceConfirmUIAlert.Builder maintenanceConfirmUIAlertBuilder;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private NextMaintenanceUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public NextMaintenanceUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public NextMaintenanceUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new NextMaintenanceUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertConditionNextMaintenanceBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 698;
            window.setAttributes(layoutParams);
            initData();
            binding.swMaintenanceRemind.setOnCheckedChangeListener(new SingleSwitchListener() {
                @Override
                public void onSingleCheckedChanged(@NonNull CompoundButton v, boolean isChecked) {
                    onProgressChangedListener.setMaintenanceRemind(isChecked);
                }
            });
            binding.rlMaintenanceReset.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    maintenanceConfirmUIAlertBuilder = new MaintenanceConfirmUIAlert.Builder(context);
                    maintenanceConfirmUIAlertBuilder.create().show();
                }
            });
            return dialog;
        }

        private void initData() {
            int maintenanceRemindStatus = onProgressChangedListener.getMaintenanceRemind();
            updateMaintainTips(maintenanceRemindStatus);
            int powerMode = onProgressChangedListener.getPowerMode();
            updatePowerMode(powerMode);
            int maintainKm = onProgressChangedListener.getMaintainKm();
            if (maintainKm == Integer.MIN_VALUE) {
                updateMaintainKm(context.getString(R.string.str_condition_next_maintenance_until) + "--" + context.getString(R.string.str_unit_setting_km));
            } else {
                updateMaintainKm(context.getString(R.string.str_condition_next_maintenance_until) + maintainKm + context.getString(R.string.str_unit_setting_km));
            }
            int maintainTime = onProgressChangedListener.getMaintainTime();
            if (maintainTime == Integer.MIN_VALUE) {
                updateMaintainTime("--" + context.getString(R.string.str_condition_next_maintenance_content_day));
            } else {
                updateMaintainTime(maintainTime + context.getString(R.string.str_condition_next_maintenance_content_day));
            }
        }

        public void updateMaintainKm(String maintainKm) {
            binding.tvNextMaintenanceKm.setText(maintainKm);
        }

        public void updateMaintainTime(String maintainTime) {
            binding.tvNextMaintenanceTime.setText(maintainTime);
        }

        public void updateMaintainTips(Integer signalVal) {
            Log.d(TAG, "updateMaintainTips: " + signalVal);
            binding.swMaintenanceRemind.setChecked(signalVal == CarCondition.MaintainRemind.ACTIVE);
        }

        public void updatePowerMode(int status) {
            if (status == 0) {
                GrayEffectHelper.setEnableExcludeViews(binding.rlMaintenanceReset, false, null);
                GrayEffectHelper.setGrayScale(binding.rlMaintenanceReset, 0.5f);

                GrayEffectHelper.setEnableExcludeViews(binding.llMaintenanceRemind, false, null);
                GrayEffectHelper.setGrayScale(binding.llMaintenanceRemind, 0.5f);
            } else if (status == 1) {
                GrayEffectHelper.setEnableExcludeViews(binding.rlMaintenanceReset, false, null);
                GrayEffectHelper.setGrayScale(binding.rlMaintenanceReset, 0.5f);

                GrayEffectHelper.setEnableExcludeViews(binding.llMaintenanceRemind, false, null);
                GrayEffectHelper.setGrayScale(binding.llMaintenanceRemind, 0.5f);
            } else if (status == 2) {
                GrayEffectHelper.setEnableExcludeViews(binding.rlMaintenanceReset, true, null);
                GrayEffectHelper.setEnableExcludeViews(binding.llMaintenanceRemind, true, null);

                GrayEffectHelper.clearGrayScaleExcludeViews(binding.rlMaintenanceReset, null);
                GrayEffectHelper.clearGrayScaleExcludeViews(binding.llMaintenanceRemind, null);
            } else if (status == 3) {
                GrayEffectHelper.setEnableExcludeViews(binding.rlMaintenanceReset, false, null);
                GrayEffectHelper.setGrayScale(binding.rlMaintenanceReset, 0.5f);

                GrayEffectHelper.setEnableExcludeViews(binding.llMaintenanceRemind, false, null);
                GrayEffectHelper.setGrayScale(binding.llMaintenanceRemind, 0.5f);
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public void dismiss() {
            dialog.dismiss();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    @Override
    protected void onStart() {
        isShow = true;
        super.onStart();
    }

    @Override
    protected void onStop() {
        isShow = false;
        super.onStop();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void setMaintenanceRemind(boolean isChecked);

        int getMaintenanceRemind();

        int getPowerMode();

        int getMaintainKm();

        int getMaintainTime();
    }
}
