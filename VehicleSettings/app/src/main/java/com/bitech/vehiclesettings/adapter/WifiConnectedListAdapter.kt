package com.bitech.vehiclesettings.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.WifiDeviceBean
import com.bitech.vehiclesettings.databinding.ItemWifiConnectedBinding
import com.bitech.vehiclesettings.utils.LogUtil
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @ClassName: WifiConnectedListAdapter
 * 
 * @Date:  2024/2/7 9:53
 * @Description: WIFI已连接列表适配器.
 **/
class WifiConnectedListAdapter(
    private val context: Context,
    private var wifiConnectedList: CopyOnWriteArrayList<WifiDeviceBean>
) :
    RecyclerView.Adapter<WifiConnectedListAdapter.WifiConnectedHolder>() {

    // 蓝牙配对列表点击事件监听对象.
    private var wifiItemClickIconCallback: OnWifiItemClickIconCallback? = null

    /**
     * list item视图绑定.
     *
     * @param parent 父布局
     * @param viewType 视图类型
     * @return
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WifiConnectedHolder {
        val binding = ItemWifiConnectedBinding.bind(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_wifi_connected, parent, false)
            )
        return WifiConnectedHolder(binding)
    }

    /**
     * 列表内容大小.
     *
     * @return Int
     */
    override fun getItemCount(): Int {
        return wifiConnectedList.size
    }

    /**
     * ViewHolder绑定.
     *
     * @param holder  item
     * @param position 位置
     */
    override fun onBindViewHolder(holder: WifiConnectedHolder, position: Int) {
        wifiConnectedList.getOrNull(position)?.let {
            val wifiDeviceBean = wifiConnectedList[position]
            // 设置WIFI名称
            holder.wifiNameTv.text = wifiDeviceBean.wifiSSID.ifBlank { wifiDeviceBean.wifiBSSID }
            // 设置是否显示WIFI连接中动画
            if (wifiDeviceBean.wifiConnectedState == NetworkInfo.State.CONNECTING) {
                // 显示WIFI连接中动画
                holder.showWifiConnectingAnimation(true)
            } else {
                // 隐藏WIFI连接中动画
                holder.showWifiConnectingAnimation(false)
            }
            if (wifiDeviceBean.wifiConnectedState == NetworkInfo.State.CONNECTED) {
                // 显示已连接提示语
                holder.showWifiConnectedTips(true)
            } else {
                // 隐藏已连接提示语
                holder.showWifiConnectedTips(false)
            }
            holder.itemView.setOnClickListener {
                when (wifiDeviceBean.wifiConnectedState) {
                    NetworkInfo.State.CONNECTED -> {
                        // 当前状态已连接，点击后断开连接
                        wifiItemClickIconCallback?.onWifiDisconnected(wifiDeviceBean)
                    }

                    NetworkInfo.State.DISCONNECTED -> {
                        // 当前状态已断开，点击后进行连接
                        wifiItemClickIconCallback?.onWifiConnected(wifiDeviceBean)
                    }

                    else -> {
                        // 其他情况不做处理
                    }
                }
            }
        }
    }

    /**
     * 设置WIFI已连接列表数据.
     *
     * @param wifiList WIFI可用扫描列表.
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setWifiConnectedList(wifiList: CopyOnWriteArrayList<WifiDeviceBean>) {
        LogUtil.d(TAG, "setWifiConnectedList : wifiList size = ${wifiList.size}")
//        // 获取当前连接的WiFi信息
//        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
//        val currentWifiInfo = wifiManager.connectionInfo
//        val currentSsid = currentWifiInfo.ssid.replace("\"", "")
//
//        // 过滤列表，只保留当前连接的WiFi
//        val connectedWifiList = wifiList.filter { it.wifiSSID == currentSsid }
//        wifiConnectedList = CopyOnWriteArrayList(connectedWifiList)
        wifiConnectedList = wifiList
        notifyDataSetChanged()
    }

    /**
     * 设置配对列表监听.
     *
     * @param itemClickIconCallback OnWifiItemClickIconCallback
     */
    fun setOnBtItemClickIconCallback(itemClickIconCallback: OnWifiItemClickIconCallback?) {
        LogUtil.d(
            TAG,
            "setOnBtItemClickIconCallback : itemClickIconCallback = $itemClickIconCallback"
        )
        if (wifiItemClickIconCallback == null) {
            wifiItemClickIconCallback = itemClickIconCallback
        }
    }

    /**
     * @ClassName: OnWifiItemClickIconCallback
     * 
     * @Date:  2024/2/7 10:20
     * @Description: 列表图标点击事件回调.
     **/
    interface OnWifiItemClickIconCallback {

        /**
         * 点击删除WIFI已连接设备图标回调.
         *
         * @param wifiDevice wifi对象
         */
        fun onWifiDeleteDevice(wifiDevice: WifiDeviceBean)

        /**
         * 连接WIFI时回调.
         *
         * @param wifiDevice
         */
        fun onWifiConnected(wifiDevice: WifiDeviceBean)

        /**
         * WIFI断开时回调.
         *
         * @param wifiDevice
         */
        fun onWifiDisconnected(wifiDevice: WifiDeviceBean)
    }

    /**
     * @ClassName: WifiConnectedHolder
     * 
     * @Date:  2024/2/7 9:57
     * @Description: WIFI已连接列表适配器对应的ViewHolder.
     **/
    inner class WifiConnectedHolder(binding: ItemWifiConnectedBinding) :
        RecyclerView.ViewHolder(binding.root), View.OnClickListener {

        private var wifiDeleteIv = binding.wifiDeleteIv
        private var wifiStateTv = binding.wifiConnectedStateTv
        private var wifiLoadPb = binding.wifiLoadingPb
        var wifiNameTv = binding.wifiNameTv

        init {
            // 设置监听
            wifiDeleteIv.setOnClickListener(this)
        }

        /**
         * 显示网络连接动画.
         *
         * @param isShow
         */
        fun showWifiConnectingAnimation(isShow: Boolean) {
            if (isShow) {
                // 显示动画图标
                wifiLoadPb.visibility = View.VISIBLE
                // 隐藏删除图标
                wifiDeleteIv.visibility = View.INVISIBLE
            } else {
                // 隐藏动画图标
                wifiLoadPb.visibility = View.GONE
                // 显示删除图标
                wifiDeleteIv.visibility = View.VISIBLE
            }
        }

        /**
         * 显示WIFI已连接状态提示语及高亮.
         *
         * @param isShow 是否显示及高亮
         */
        fun showWifiConnectedTips(isShow: Boolean) {
            if (isShow) {
                // 显示连接状态提示语
                wifiStateTv.visibility = View.VISIBLE
            } else {
                // 隐藏连接状态提示语
                wifiStateTv.visibility = View.GONE
            }
        }

        /**
         * item图标绑定.
         *
         * @param view
         */
        override fun onClick(view: View?) {
            when (view?.id) {
                R.id.wifi_delete_iv -> {
                    LogUtil.d(TAG, "onClick : wifi delete icon is click!")
                    // WIFI删除图标被点击.
                    wifiItemClickIconCallback?.onWifiDeleteDevice(wifiConnectedList[adapterPosition])
                }
            }
        }
    }

    companion object {
        // 日志标志位
        private const val TAG = "WifiConnectedListAdapter"
    }
}
