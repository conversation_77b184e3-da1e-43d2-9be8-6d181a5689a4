package com.bitech.vehiclesettings.viewmodel;

import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.platformlib.bean.LightInBean;

public class LightInViewModel extends ViewModel {

    public LightInViewModel(LightInBean initialBean) {
        lightSw.setValue(initialBean.getLightSw());
        themeSw.setValue(initialBean.getThemeMode());
        colorPos.setValue(initialBean.getColorPos());
        colorBrightPos.setValue(initialBean.getBrightPos());

        lightSel.setValue(initialBean.getLightSel());
        lightFront.setValue(initialBean.getFront().getFront());
        lightRear.setValue(initialBean.getRear().getRear());
        lightSync.setValue(initialBean.getLightSync());

//        lampEffect.setValue(initialBean.getLampEffect());
        automaticCeiling.setValue(initialBean.getAutomaticCeiling());

    }


    // 氛围灯开关
    private MutableLiveData<Integer> lightSw = new MediatorLiveData<>();
    // 主题 自定义
    private MutableLiveData<Integer> themeSw = new MediatorLiveData<>();

    private MutableLiveData<Integer> lightSel = new MediatorLiveData<>();
    private MutableLiveData<Integer> lightFront = new MediatorLiveData<>();
    private MutableLiveData<Integer> lightRear = new MediatorLiveData<>();
    private MutableLiveData<Integer> lightSync = new MediatorLiveData<>();
    // 色块选中
    private MutableLiveData<Integer> colorPos = new MediatorLiveData<>();
    // 亮度
    private MutableLiveData<Integer> colorBrightPos = new MediatorLiveData<>();
    // 车灯效果
//    private MutableLiveData<Integer> lampEffect = new MediatorLiveData<>();
    // 自动顶灯
    private MutableLiveData<Integer> automaticCeiling = new MediatorLiveData<>();
    // 自动亮度
    private MutableLiveData<Integer> autoBright = new MediatorLiveData<>(0);

    private MutableLiveData<Integer> xpos = new MediatorLiveData<>(0);

    public MutableLiveData<Integer> getXpos() {
        return xpos;
    }

    public void setXpos(Integer status) {
        xpos.postValue(status);
    }

    public MutableLiveData<Integer> getAutoBright() {
        return autoBright;
    }

    public void setAutoBright(Integer status) {
        autoBright.postValue(status);
    }

    public MutableLiveData<Integer> getLightSel() {
        return lightSel;
    }

    public void setLightSel(Integer status) {
        lightSel.postValue(status);
    }

    public MutableLiveData<Integer> getLightFront() {
        return lightFront;
    }

    public void setLightFront(Integer status) {
        lightFront.postValue(status);
    }


    public MutableLiveData<Integer> getLightRear() {
        return lightRear;
    }

    public void setLightRear(Integer status) {
        lightRear.postValue(status);
    }


    public MutableLiveData<Integer> getLightSync() {
        return lightSync;
    }

    public void setLightSync(Integer status) {
        lightSync.postValue(status);
    }


    public MutableLiveData<Integer> getLightSw() {
        return lightSw;
    }

    public void setLightSw(Integer status) {
        lightSw.postValue(status);
    }

    public MutableLiveData<Integer> getThemeSw() {
        return themeSw;
    }

    public void setThemeSw(Integer status) {
        themeSw.postValue(status);
    }

    public MutableLiveData<Integer> getColorPos() {
        return colorPos;
    }

    public void setColorPos(Integer status) {
        colorPos.postValue(status);
    }

    public MutableLiveData<Integer> getColorBrightsPos() {
        return colorBrightPos;
    }

    public void setColorBrightsPos(Integer status) {
        colorBrightPos.postValue(status);
    }

//    public MutableLiveData<Integer> getLampEffect() {
//        return lampEffect;
//    }
//
//    public void setLampEffect(Integer status) {
//        lampEffect.postValue(status);
//    }

    public MutableLiveData<Integer> getAutomaticCeiling() {
        return automaticCeiling;
    }

    public void setAutomaticCeiling(Integer status) {
        automaticCeiling.postValue(status);
    }

}
