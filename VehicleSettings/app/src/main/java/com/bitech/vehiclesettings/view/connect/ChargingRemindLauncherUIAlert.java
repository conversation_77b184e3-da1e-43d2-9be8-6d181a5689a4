package com.bitech.vehiclesettings.view.connect;

import android.content.Context;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectChargingRemindBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectWirelessChargingLauncherBinding;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class ChargingRemindLauncherUIAlert extends BaseDialog {
    private static final String TAG = ChargingRemindLauncherUIAlert.class.getSimpleName();
    private static ChargingRemindLauncherUIAlert.onProgressChangedListener onProgressChangedListener;


    public ChargingRemindLauncherUIAlert(@NonNull Context context) {
        super(context);
    }

    public ChargingRemindLauncherUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ChargingRemindLauncherUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static ChargingRemindLauncherUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(ChargingRemindLauncherUIAlert.onProgressChangedListener onProgressChangedListener) {
        ChargingRemindLauncherUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private ChargingRemindLauncherUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConnectChargingRemindBinding binding;
        DetailsUIAlert.Builder detailUIAlert;
        boolean globalAlert = false;
        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private ChargingRemindLauncherUIAlert dialog = null;
        private View layout;
        public Builder(Context context) {
            this.context = context;
        }


        public ChargingRemindLauncherUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }
        public void setGlobalAlert(boolean b){
            globalAlert = b;
        }
        /**
         * Create the custom dialog
         */
        public ChargingRemindLauncherUIAlert create() {
            // instantiate the dialog with the custom Theme
            // instantiate the dialog with the custom Theme
            int themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue);
            Context themedContext = new ContextThemeWrapper(context, themeId);
            if (dialog == null)
                dialog = new ChargingRemindLauncherUIAlert(themedContext, R.style.Dialog);
            // 设置dialog的bind
            binding = DialogAlertConnectChargingRemindBinding.inflate(LayoutInflater.from(themedContext));
            context.setTheme(Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            // 不让背景变暗
            window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            layoutParams.dimAmount = 0f;
            layoutParams.type = globalAlert ?WindowManager.LayoutParams.TYPE_SYSTEM_ALERT: WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            layoutParams.width = 1176; // 或者使用具体的像素值
            layoutParams.height = 688;
            window.setAttributes(layoutParams);

            binding.tvConfirm.setOnClickListener(v -> {
                Prefs.put(PrefsConst.CONNECT_CHARGING_TIPS_LAUNCHER, binding.rbNoReminder.isChecked() ? 1 : 0);
                dialog.dismiss();
            });

            return dialog;
        }

        public ChargingRemindLauncherUIAlert getDialog() {
            return dialog;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(int state);
    }
}

