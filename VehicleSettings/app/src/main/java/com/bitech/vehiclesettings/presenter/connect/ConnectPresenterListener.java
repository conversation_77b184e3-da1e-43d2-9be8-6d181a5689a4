package com.bitech.vehiclesettings.presenter.connect;

public interface ConnectPresenterListener {
    int getMobileNetworkState();
    void setMobileNetworkState(int state);
    int getFrontChargingState();
    // 前排无线充电
    void setFrontChargingState(int state);
    int getBackChargingState();
    void setBackChargingState(int state);
    // 遗忘提醒状态
    int getForgetReminder();
    void setForgetReminder(int state);
    // 记录前排关闭时遗忘提醒的状态
    int getForgetReminderMemory();
    void setForgetReminderMemory(int state);
    // 充电温馨提醒
    int getChargingRemindState();
    void setChargingRemindState(int state);
    // 获得高温提醒状态
    int getHighTemperatureRemindState();
}
