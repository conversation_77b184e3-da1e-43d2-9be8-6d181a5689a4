package com.bitech.vehiclesettings.view.condition;

import static androidx.databinding.adapters.ViewGroupBindingAdapter.setListener;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertConditionSunShadeCurtainModeBinding;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class SunShadeCurtainModeUIAlert extends BaseDialog {
    private static final String TAG = SunShadeCurtainModeUIAlert.class.getSimpleName();
    private static SunShadeCurtainModeUIAlert.onProgressChangedListener onProgressChangedListener;

    public SunShadeCurtainModeUIAlert(@NonNull Context context) {
        super(context);
    }

    public SunShadeCurtainModeUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected SunShadeCurtainModeUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static SunShadeCurtainModeUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(SunShadeCurtainModeUIAlert.onProgressChangedListener onProgressChangedListener) {
        SunShadeCurtainModeUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private SunShadeCurtainModeUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConditionSunShadeCurtainModeBinding binding;
        public boolean isBlueOpen() {
            return isBlueOpen;
        }
        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private SunShadeCurtainModeUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public SunShadeCurtainModeUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public SunShadeCurtainModeUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new SunShadeCurtainModeUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertConditionSunShadeCurtainModeBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 640;
            window.setAttributes(layoutParams);
            initData();

            binding.tvConfirm.setOnClickListener(v -> {
                dialog.setCancelable(false);
                updateConfirmStatus(1);
                onProgressChangedListener.onStartRepair();
            });
            binding.tvCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });

            return dialog;
        }

        private void initData() {
            int status = onProgressChangedListener.getRepair();
            updateConfirmStatus( status);
        }

        public void updateConfirmStatus(int status) {
            if (status == 1) {
                GrayEffectUtils.applyGrayEffect(binding.tvConfirm);
                binding.tvConfirm.setText(context.getString(R.string.str_condition_repairs_in_progress));
            } else if (status == 0) {
                GrayEffectUtils.removeGrayEffect(binding.tvConfirm);
                binding.tvConfirm.setText(context.getString(R.string.str_condition_start_repair));
            }
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onStartRepair();
        int getRepair();
    }
}
