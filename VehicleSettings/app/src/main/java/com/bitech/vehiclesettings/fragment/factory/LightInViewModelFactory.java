package com.bitech.vehiclesettings.fragment.factory;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.bitech.platformlib.bean.LightInBean;
import com.bitech.vehiclesettings.viewmodel.LightInViewModel;

public class LightInViewModelFactory implements ViewModelProvider.Factory {
    private LightInBean lightInBean;

    public LightInViewModelFactory(LightInBean lightInBean) {
        this.lightInBean = lightInBean;
    }

    @NonNull
    @Override
    public <T extends ViewModel> T create(@NonNull Class<T> modelClass) {
        if (modelClass.isAssignableFrom(LightInViewModel.class)) {
            return (T) new LightInViewModel(lightInBean);
        }
        throw new IllegalArgumentException("Unknown ViewModel class");
    }
}
