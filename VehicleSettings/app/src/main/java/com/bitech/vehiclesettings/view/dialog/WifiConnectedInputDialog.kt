package com.bitech.vehiclesettings.view.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogWifiConnectedInputBinding
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: wifiConnectedInputDialog
 * 
 * @Date:  2024/2/12 14:08
 * @Description: WIFI连接密码输入框.
 **/
class WifiConnectedInputDialog(context: Context) : Dialog(context, R.style.dialog),
    View.OnClickListener {

    // WIFI密码输入框视图对象
    private lateinit var binding: DialogWifiConnectedInputBinding

    // WIFI连接回调监听对象
    private lateinit var wifiConnectedClickCallback: OnWifiConnectedClickCallback


    // 当前密码是否可视化
    private var isVisual = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        binding = DialogWifiConnectedInputBinding.inflate(layoutInflater)
        // 绑定自定义dialog视图
        setContentView(binding.root)
        // 添加以下两行代码，设置软键盘输入模式
        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        // 初始化页面视图
        initView()
    }

    /**
     * 初始化页面视图.
     *
     */
    private fun initView() {
        val attributes = window?.attributes
        //修改弹出层级
        attributes?.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        attributes?.windowAnimations = 0
        attributes?.x = 0
        attributes?.y = 0
        window?.attributes = attributes

        // 外部点击不可关闭
        setCanceledOnTouchOutside(false)
        // 对话框按钮监听
        binding.dialogConnectedBtn.setOnClickListener(this)
        binding.dialogCancelBtn.setOnClickListener(this)
        binding.dialogWifiIconIv.setOnClickListener(this)
        // 密码输入框焦点请求
        binding.dialogPasswordInputEt.requestFocus()
        binding.dialogPasswordInputEt.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                
            }

            override fun onTextChanged(
                textString: CharSequence?,
                start: Int,
                before: Int,
                count: Int
            ) {
                // 输入位数≥8时,“连接”按钮才高亮可点击,否则置灰，
                binding.dialogConnectedBtn.isEnabled = (textString?.length ?: 0) >= 8
            }

            override fun afterTextChanged(s: Editable?) {
                
            }
        })
    }

    /**
     * 更新密码显示格式.
     *
     */
    private fun updatePasswordShowUi() {
        if (isVisual) {
            // 当前密码可见，点击后，密码不可见
            isVisual = false
            // 设置输入框内容不可见
            binding.dialogPasswordInputEt.transformationMethod =
                PasswordTransformationMethod.getInstance()
            // 设置图标为不可见图标
            binding.dialogWifiIconIv.setImageResource(R.mipmap.img_closeeyes)
        } else {
            // 当前密码不可见，点击后，密码可见
            isVisual = true
            // 设置输入框内容可见
            binding.dialogPasswordInputEt.transformationMethod =
                HideReturnsTransformationMethod.getInstance()
            // 设置图标为可见图标
            binding.dialogWifiIconIv.setImageResource(R.mipmap.img_eyes)
        }
        // 将光标放在密码最后面
        binding.dialogPasswordInputEt.setSelection(binding.dialogPasswordInputEt.text.toString().length)
    }

    /**
     * 对话框页面，点击事件监听.
     *
     * @param view 视图对象
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.dialog_connected_btn -> {
                // 连接按钮被点击
                LogUtil.d(TAG, "onClick : wifi connected is click!")
                wifiConnectedClickCallback
                    .onWifiConnectedClick(binding.dialogPasswordInputEt.text.toString())
                dismiss()
            }

            R.id.dialog_cancel_btn -> {
                // 取消按钮被点击
                LogUtil.d(TAG, "onClick : cancel is click!")
                wifiConnectedClickCallback.onCancelClick()
                dismiss()
            }

            R.id.dialog_wifi_icon_iv -> {
                // 密码可视化图标被点击
                LogUtil.d(TAG, "onClick : password visual is click!")
                // 更新密码显示格式
                updatePasswordShowUi()
            }
        }
    }

    /**
     * 设置连接按钮点击事件监听.
     *
     * @param callback
     */
    fun setWifiConnectedClickCallback(callback: OnWifiConnectedClickCallback) {
        wifiConnectedClickCallback = callback
    }

    /**
     * @ClassName: OnConfirmDialogClickCallback
     * 
     * @Date:  2024/2/12 15:11
     * @Description: 连接按钮点击事件回调.
     **/
    interface OnWifiConnectedClickCallback {
        /**
         * WIFI连接按钮被点击.
         *
         * @param password wifi密码
         */
        fun onWifiConnectedClick(password: String)

        /**
         * 取消按钮被点击.
         *
         */
        fun onCancelClick()
    }


    companion object {
        // 日志标志位
        private const val TAG = "WifiConnectedInputDialog"
    }
}
