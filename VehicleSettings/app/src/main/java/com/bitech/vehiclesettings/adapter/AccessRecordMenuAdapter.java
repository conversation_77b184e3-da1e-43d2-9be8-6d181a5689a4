package com.bitech.vehiclesettings.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.RecordMenuBean;

import java.util.List;

public class AccessRecordMenuAdapter extends RecyclerView.Adapter<AccessRecordMenuAdapter.ViewHolder> {
    private final List<RecordMenuBean> menuItems;
    private final OnItemClickListener listener;
    private int selectedPosition = 0; // 默认选中第一个菜单项

    public interface OnItemClickListener {
        void onItemClick(int position);
    }

    public AccessRecordMenuAdapter(List<RecordMenuBean> menuItems, OnItemClickListener listener) {
        this.menuItems = menuItems;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_access_record_menu, parent, false);
        return new ViewHolder(view);
    }

    @SuppressLint({"SetTextI18n", "ResourceAsColor"})
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        Context context = holder.itemView.getContext();
        holder.icon.setImageResource(menuItems.get(position).getIcon());
        holder.menuTitle.setText(menuItems.get(position).getTitle());
        holder.accessTimes.setText(context.getString(R.string.str_system_permission_5) + menuItems.get(position).getTimes());

        // 设置选中状态的样式
        holder.menu.setBackgroundResource(selectedPosition == position ?
                R.drawable.selector_bg_open : R.color.transparent);
        if (selectedPosition == position || Util.isNight(context)) {
            holder.icon.setImageResource(menuItems.get(position).getIconSelected());
            holder.menuTitle.setTextColor(context.getColor(R.color.white));
            holder.accessTimes.setTextColor(context.getColor(R.color.color_white_transparent_50));
        } else {
            holder.icon.setImageResource(menuItems.get(position).getIcon());
            holder.menuTitle.setTextColor(context.getColor(R.color.black));
            holder.accessTimes.setTextColor(context.getColor(R.color.color_transparent_40));
        }

        holder.itemView.setOnClickListener(v -> {
            selectedPosition = position;
            notifyDataSetChanged(); // 刷新 UI
            listener.onItemClick(position);
        });
    }

    @Override
    public int getItemCount() {
        return menuItems.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        RelativeLayout menu;
        ImageView icon;
        TextView menuTitle, accessTimes;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            menu = itemView.findViewById(R.id.rl_menu);
            icon = itemView.findViewById(R.id.iv_menu);
            menuTitle = itemView.findViewById(R.id.tv_menu_title);
            accessTimes = itemView.findViewById(R.id.tv_access_times);
        }
    }
}
