package com.bitech.vehiclesettings.view.light;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bitech.vehiclesettings.R;

/**
 * 对话框
 */
public class TipsUIAlert extends Dialog {
    private static final String TAG = TipsUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public TipsUIAlert(Context context) {
        super(context);
    }

    public TipsUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected TipsUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static TipsUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(TipsUIAlert.onProgressChangedListener onProgressChangedListener) {
        TipsUIAlert.onProgressChangedListener = onProgressChangedListener;
    }


    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private View rlNfcItem1;
        private String tips;
        private String tipsText;
        private TextView tvTips;
        private TextView tvTipsText;
        private int width = 1128;
        private int height = 508;

        public int getWidth(int width) {
            return this.width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight(int height) {
            return this.height;
        }

        public void setWidthAndHeight(int width, int height) {
            this.width = width;
            this.height = height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private TipsUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }

        public String getTips() {
            return tips;
        }

        public void setTips(String tips) {
            this.tips = tips;
        }

        public String getTipsText() {
            return tipsText;
        }

        public void setTipsText(String tipsText) {
            this.tipsText = tipsText;
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public TipsUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new TipsUIAlert(context,
                        R.style.Dialog);
            layout = View.inflate(context, R.layout.dialog_alert_tips, null);
            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            tvTips = layout.findViewById(R.id.tv_tips_title);
            tvTipsText = layout.findViewById(R.id.tv_tips_text);
            tvTips.setText(tips);
            tvTipsText.setText(tipsText);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = width; // 或者使用具体的像素值
            layoutParams.height = height;
            window.setAttributes(layoutParams);

            return dialog;
        }

        public void setTitleAndTxt(String tips, String tipsText) {
            setTips(tips);
            setTipsText(tipsText);
        }

    }


    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }

}
