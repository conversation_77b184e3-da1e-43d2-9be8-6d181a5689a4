package com.bitech.vehiclesettings.presenter.light;

public interface LightOutPresenterListener {


    void setLampControl(int state);

    int lampControlChange(int status);

    int getLampControl();

    int hightChange(int status);

    void setLampHigh(int state);

    int getLampHigh();

    void setLampDelay(int state);


    int getLampDelay();

    int delayChange(int status);

    void setRearFogLamp(int state);

    int getRearFogLamp();

    void setApproachingWelcome(int state);

    int getApproachingWelcome();


    void setHighLowSwitch(int state);

    int getHighLowSwitch();

    void setIntelligentWelcome(int state);

    int getIntelligentWelcome();


}
