package com.bitech.vehiclesettings.service.earlywarning;

import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.bean.atmosphere.AmbLigBean;


/**
 * 前碰撞预警
 */
public class FcwService {
    private static final String TAG = FcwService.class.getSimpleName();
    private static volatile FcwService instance;
    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
    private static final long TARGET_INTERVAL_MS = 250;
    private Handler mHandler;
    private Runnable mTaskRunnable;
    private volatile boolean isRunning = false;
    private long mLastExecutionTime;
    int count = 0;

    public FcwService() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public static FcwService getInstance() {
        if (instance == null) {
            synchronized (FcwService.class) {
                if (instance == null) {
                    instance = new FcwService();
                }
            }
        }
        return instance;
    }

    /**
     * ADAS_enum_FcwAcitveSt=0x2/0x3
     */
    public void start(int sigal) {
        if (isRunning) {
            return;
        }
        isRunning = true;
        mLastExecutionTime = SystemClock.elapsedRealtime();
        mTaskRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isRunning) {
                    return;
                }
                // 1. 执行任务
                Log.d(TAG, "[warn] 前碰撞预警 start ");
                doTask(sigal);
                // 2. 计算下一次执行时间（动态补偿误差）
                long currentTime = SystemClock.elapsedRealtime();
                long elapsed = currentTime - mLastExecutionTime;
                long nextDelay = Math.max(0, TARGET_INTERVAL_MS - elapsed);
                // 3. 递归调度下一次任务
                if ((count / 2) == 0) {
                    mHandler.postDelayed(this, nextDelay - 200);
                } else {
                    mHandler.postDelayed(this, nextDelay);
                }
                mLastExecutionTime = currentTime + nextDelay;
            }
        };
        mHandler.post(mTaskRunnable);
    }

    public void stop() {
        // 完成一个周期后退出
        if (count == 0) {
            if (mTaskRunnable != null) {
                Log.d(TAG, "[warn] 前碰撞预警 stop ");
                mHandler.removeCallbacks(mTaskRunnable);
            }
            isRunning = false;
        }
    }

    private void doTask(int signal) {
        Log.d(TAG, "[warn] 前碰撞预警 doTask: " + signal + ",count:" + count);
        if (count == 0) {
            count++;
            // 熄灭
            AmbLigBean ambLigBean = getAmbLigDown(CarLight.AmbLigColorAdj.lin_1);
            carServer.setLightAmbLightCan(ambLigBean);
        } else {
            // 点亮
            AmbLigBean ambLigBean = getAmbLigUp(CarLight.AmbLigColorAdj.lin_1);
            carServer.setLightAmbLightCan(ambLigBean);
            if (count >= 4) {
                count = 0;
            }
        }
    }

    /**
     * 渐亮
     */
    private AmbLigBean getAmbLigUp(int colorIdx) {
        AmbLigBean up1 = new AmbLigBean().setAll();
        up1.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_5).setAmbLigColorAdj(colorIdx).setAmbModLocSig(CarLight.AmbModLocSig.NOT_ACTIVE).setAmbLigFlngModSel(CarLight.AmbLigFlngModSel.MODE_2);
        return up1;
    }

    /**
     * 渐灭
     */
    private AmbLigBean getAmbLigDown(int colorIdx) {
        AmbLigBean down1 = new AmbLigBean().setAll();
        down1.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0).setAmbLigColorAdj(colorIdx).setAmbModLocSig(CarLight.AmbModLocSig.NOT_ACTIVE).setAmbLigFlngModSel(CarLight.AmbLigFlngModSel.MODE_2);
        return down1;
    }
}
