package com.bitech.vehiclesettings.presenter.system;

import static com.bitech.vehiclesettings.presenter.display.DisplayPresenter.DEFAULT_SYSTEM_COLOR;
import static com.bitech.vehiclesettings.presenter.display.DisplayPresenter.getDisplayMode;
import static com.bitech.vehiclesettings.view.system.GotoEngineModeUIAlert.Builder.generatePassword;

import android.app.Activity;
import android.app.ActivityManagerNative;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.IPackageDataObserver;
import android.content.pm.IPackageManager;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.os.ServiceManager;
import android.os.StatFs;
import android.provider.Settings;
import android.text.format.DateFormat;
import android.util.Log;

import com.android.internal.app.LocalePicker;
import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.SystemManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.bean.AppPermissionBean;
import com.bitech.vehiclesettings.bean.AppPermissionLevel;
import com.bitech.vehiclesettings.bean.GlobalVar;
import com.bitech.vehiclesettings.bean.PermissionType;
import com.bitech.vehiclesettings.bean.PrivacyStatementBean;
import com.bitech.vehiclesettings.bean.RecordItemBean;
import com.bitech.vehiclesettings.bean.SystemDataBean;
import com.bitech.vehiclesettings.bean.SystemPermission;
import com.bitech.vehiclesettings.bean.SystemScope;
import com.bitech.vehiclesettings.broadcast.TimeChangeReceiver;
import com.bitech.vehiclesettings.carapi.constants.CarSystem;
import com.bitech.vehiclesettings.manager.CarWifiManager;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.presenter.driving.DrivingPresenter;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.utils.PermissionManager;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.SendICUTopicsUtil;
import com.bitech.vehiclesettings.viewmodel.WifiViewModel;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.id.vehicle.VDEventVehicle;
import com.chery.ivi.vdb.event.id.vehicle.VDKeyVehicle;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

public class SystemPresenter<T> implements SystemPresenterListener {
    private final CompositeDisposable disposables = new CompositeDisposable();
    private static final String TAG = SystemPresenter.class.getSimpleName();
    private Context mContext;
    private int mLastRequestType = -1; // 记录最后一次请求的类型
    private int mReceivedUnitValue = -1; // 存储接收到的单位值

    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mCountdownRunnable;

    SystemManager systemManager = (SystemManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_SYSTEM_MANAGER);

    private T data;
    private static final String enginePKG = "com.bitech.engmode";
    private static final String engineCLS = "com.bitech.engmode.activity.MainActivity";
    private ClearUserDataObserver clearUserDataObserver = null;
    // 显示
    private DisplayPresenter displayPresenter;
    // 声音
    private VoicePresenter voicePresenter;
    // 驾驶模块
    private DrivingPresenter drivingPresenter;
    //热点控制
    private CarWifiManager carWifiManager;
    // WIFI
    private WifiManager wifiManager = null;

    static SystemPresenter instance;

    public static SystemPresenter getInstance() {
        if (instance == null) {
            synchronized (SystemPresenter.class) {
                if (instance == null) {
                    instance = new SystemPresenter(MyApplication.getContext());
                }
            }
        }
        return instance;
    }

    public static class SystemConstant {
        public static final int DATE_DISPLAY_YMD = 0;
        public static final int DATE_DISPLAY_MDY = 1;
        public static final int DATE_DISPLAY_DMY = 2;

        public static final int TIME_DISPLAY_12 = 0;
        public static final int TIME_DISPLAY_24 = 1;

        public static final int AUTO_CALIBRATION = 1;
        public static final int AUTO_CALIBRATION_FALSE = 0;

        public static final int INSTRUMENT_FUEL_UNIT_INDEX = 0x3B;
        public static final int INSTRUMENT_FUEL_UNIT_INDEX_SET = 0x48;
        public static final int INSTRUMENT_FUEL_UNIT_L_100KM = 0x00;
        public static final int INSTRUMENT_FUEL_UNIT_KM_L = 0x01;
        public static final int[] INSTRUMENT_FUEL_UNIT_STATUS_LIST = {
                INSTRUMENT_FUEL_UNIT_L_100KM, INSTRUMENT_FUEL_UNIT_KM_L
        };
        public static final int TIRE_PRESSURE_UNIT_INDEX = 0x4C;
        public static final int TIRE_PRESSURE_UNIT_INDEX_SET = 0x5A;
        public static final int TIRE_PRESSURE_UNIT_BAR = 0x00;
        public static final int TIRE_PRESSURE_UNIT_KPA = 0x01;
        public static final int TIRE_PRESSURE_UNIT_PSI = 0x02;
        public static final int[] TIRE_PRESSURE_UNIT_STATUS_LIST = {
                TIRE_PRESSURE_UNIT_BAR, TIRE_PRESSURE_UNIT_KPA, TIRE_PRESSURE_UNIT_PSI
        };
        public static final int POWER_CONSUMPTION_UNIT_INDEX = 0x51;
        public static final int POWER_CONSUMPTION_UNIT_INDEX_SET = 0x62;
        public static final int POWER_CONSUMPTION_UNIT_KWH = 0x00;
        public static final int POWER_CONSUMPTION_UNIT_WH = 0x01;
        public static final int[] POWER_CONSUMPTION_STATUS_LIST = {
                POWER_CONSUMPTION_UNIT_KWH, POWER_CONSUMPTION_UNIT_WH
        };

    }

    public static boolean isThisTimePermissionCamera = false;
    public static boolean isThisTimePermissionMicrophone = false;
    public static boolean isThisTimePermissionLocation = false;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }


    // 初始化时注册监听器
    public SystemPresenter(Context context) {
        mContext = context;
    }

    private Context mCtx = null;
    private Activity mActivity = null;
    private List<AppPermissionBean> cameraList = null;
    private List<AppPermissionBean> microphoneList = null;
    private List<AppPermissionBean> locationList = null;
    private static final int REQ_APP_PERMISSION = 100;

    public SystemPresenter(Activity activity,
                           List<AppPermissionBean> cameraList,
                           List<AppPermissionBean> microphoneList,
                           List<AppPermissionBean> locationList) {
        this.mActivity = activity;
        this.mCtx = activity.getApplicationContext();
        this.mContext = mCtx;
        this.cameraList = cameraList;
        this.microphoneList = microphoneList;
        this.locationList = locationList;
    }

/*    private List<AppPermissionBean> cameraList = Arrays.asList(
            new PermissionAppBean("相机App1", 0),
            new PermissionAppBean("相机App2", 1),
            new PermissionAppBean("相机App3", 2),
            new PermissionAppBean("相机App4", 0),
            new PermissionAppBean("相机App5", 0)
    );
    private List<AppPermissionBean> microphoneList = Arrays.asList(
            new PermissionAppBean("录音App1", 0),
            new PermissionAppBean("会议App", 0)
    );
    private List<AppPermissionBean> locationList = Arrays.asList(
            new PermissionAppBean("地图App1", 0),
            new PermissionAppBean("导航App", 0)
    );*/

    @Override
    public void gotoEngineMode() {
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        ComponentName cn = new ComponentName(enginePKG, engineCLS);
        intent.setComponent(cn);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(intent);
    }

    @Override
    public void setSwAnalysis(int status) {
        Log.d(TAG, "setSwAnalysis: 开关分析与改进:" + status);
        // 1.保存状态值
        Prefs.put(PrefsConst.SYSTEM_ANALYSIS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.SYSTEM_ANALYSIS, status);
        // 2.调用接口
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwAnalysis() {
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.SYSTEM_ANALYSIS, PrefsConst.DefaultValue.SYSTEM_ANALYSIS);
        Log.d(TAG, "getSwAnalysis: 获取分析与改进:" + status);
        return status;
    }

/*    @Override
    public List<RecordItemBean> getRecordsForPermission(int position) {
        Log.d(TAG, "getRecordsForPermission: 获取访问记录数据：" + position);
        return switch (position) {
            case 0 -> // 摄像头权限
                    Arrays.asList(
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "相机App1", 5, "2025年03月30日 10:30"),
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "相机App2", 2, "2025年03月29日 14:20"),
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "相机App3", 2, "2025年03月29日 14:20"),
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "相机App4", 2, "2025年03月29日 14:20"),
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "相机App5", 2, "2025年03月29日 14:20")
                    );
            case 1 -> // 麦克风权限
                    Arrays.asList(
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "录音App1", 8, "2025年03月28日 09:15"),
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "会议App", 3, "2025年03月27日 19:40")
                    );
            case 2 -> // 位置权限
                    Arrays.asList(
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "地图App1", 12, "2025年03月26日 18:10"),
                            new RecordItemBean(R.mipmap.ic_new_equipment_color_1, "导航App", 6, "2025年03月25日 12:50")
                    );
            default -> new ArrayList<>(); // 防止越界
        };
    }

    @Override
    public int getRecordTimes(int position) {
        Log.d(TAG, "getRecordTimes: 设置访问次数：" + position);
        return switch (position) {
            case 0 -> // 摄像头权限
                    1000;
            case 1 -> // 麦克风权限
                    1500;
            case 2 -> // 位置权限
                    2000;
            default -> 0; // 防止越界
        };
    }*/

    @Override
    public List<RecordItemBean> getRecordsForPermission(int position) {
        Log.d(TAG, "getRecordsForPermission: 获取访问记录数据：" + position);

        String packageName = mContext.getPackageName();
        long now = System.currentTimeMillis();
        long sevenDaysAgo = now - 7L * 24 * 60 * 60 * 1000; // 过去7天

        PermissionType type = switch (position) {
            case 0 -> PermissionType.CAMERA;
            case 1 -> PermissionType.MICROPHONE;
            case 2 -> PermissionType.LOCATION;
            default -> null;
        };
        if (type == null) return new ArrayList<>();

        return PermissionManager.getHistoricalPermissionRecordsReflection(mContext, packageName, type, sevenDaysAgo, now);
    }

    @Override
    public int getRecordTimes(int position) {
        Log.d(TAG, "getRecordTimes: 设置访问次数：" + position);

        String packageName = mContext.getPackageName();
        long now = System.currentTimeMillis();
        long sevenDaysAgo = now - 7L * 24 * 60 * 60 * 1000;

        PermissionType type = switch (position) {
            case 0 -> PermissionType.CAMERA;
            case 1 -> PermissionType.MICROPHONE;
            case 2 -> PermissionType.LOCATION;
            default -> null;
        };
        if (type == null) return 0;
        List<RecordItemBean> records =
                PermissionManager.getHistoricalPermissionRecordsReflection(mContext, packageName, type, sevenDaysAgo, now);

        int total = 0;
        for (RecordItemBean item : records) {
            total += item.getVisitCount();
        }
        return total;
    }

    @Override
    public List<AppPermissionBean> getPermissionAppList(int position) {
        Log.d(TAG, "getPermissionAppList: 获取授权app列表：" + position);
        return switch (position) {
            case 0 -> // 摄像头授权
                    cameraList;
            case 1 -> // 麦克风授权
                    microphoneList;
            case 2 -> // 位置授权
                    locationList;
            default -> new ArrayList<>(); // 防止越界
        };
    }

    @Override
    public void setDateDisplay(int dateDisplay, Context context) {
        Log.d(TAG, "setDateDisplay:" + dateDisplay);
        Prefs.put(PrefsConst.SYSTEM_DATE_DISPLAY, dateDisplay);
    }

    @Override
    public int getDateDisplay() {
        int display = Prefs.get(PrefsConst.SYSTEM_DATE_DISPLAY, SystemConstant.DATE_DISPLAY_YMD);
        Log.d(TAG, "getDateDisplay:" + display);
        return display;
    }

    @Override
    public void setTimeDisplay(int timeDisplay, Context context) {
        Settings.System.putString(
                context.getContentResolver(),
                Settings.System.TIME_12_24,
                timeDisplay == SystemConstant.TIME_DISPLAY_12 ? "12" : "24"
        );
        // 发送Topic至ICU

        SendICUTopicsUtil.sendTopics(SendICUTopicsUtil.Topics.Vehiclesettings_AMPM_SET, timeDisplay == 0 ? 0 : 1);

        Intent intent = new Intent(TimeChangeReceiver.BITECH_FORMAT_CHANGE_URI);
        intent.putExtra("is24Hour", timeDisplay == SystemConstant.TIME_DISPLAY_24);
        Log.d(TAG, "发送时间格式设置广播 setTimeDisplay: " + timeDisplay);
        context.sendBroadcast(intent);

    }

    @Override
    public boolean getTimeDisplay(Context context) {
        return DateFormat.is24HourFormat(context);
    }

    @Override
    public void setAutoCalibration(int autoCalibration) {
        Log.d(TAG, "自动校准时间:" + autoCalibration);
        Prefs.put(PrefsConst.SYSTEM_AUTO_CALIBRATION, autoCalibration);
        Prefs.setGlobalValue(Settings.Global.AUTO_TIME, autoCalibration);

        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);  // 24小时制
        int minute = calendar.get(Calendar.MINUTE);
        Map<String, Object> map = new HashMap<>();
        map.put("extension", null);
        map.put("relative", false);
        map.put("valid", true);
        map.put("hour", getTimeDisplay(mContext) ? hour % 12 : hour);
        map.put("minute", minute);
        // 发送Topic至ICU
        SendICUTopicsUtil.sendObjectTopics(SendICUTopicsUtil.Topics.SystemSetting_TIMECHANGE_SET, map);
        SendICUTopicsUtil.sendTopics(SendICUTopicsUtil.Topics.SystemSetting_TIMECHANGE_SET, autoCalibration);
    }

    @Override
    public int getAutoCalibration() {
        int status = Prefs.get(PrefsConst.SYSTEM_AUTO_CALIBRATION, SystemConstant.AUTO_CALIBRATION);
        Log.d(TAG, "自动校准时间:" + status);
        return Prefs.getGlobalValue(Settings.Global.AUTO_TIME, PrefsConst.DefaultValue.AUTO_TIME);
    }

/*    private void setUnit(int type, int state) {
        // 设置单位
        Bundle payload = new Bundle();
        payload.putIntArray(VDKeyVehicle.INT_VECTOR, new int[]{type, state});

        VDEvent event = new VDEvent(VDEventVehicle.SV_DISPATCH_RADIO_REQUEST_INFO, payload);
        VDBus.getDefault().set(event);
        Log.d(TAG, "setUnit: " + event);
    }

    private int getUnit(int type) {
        Bundle payload = new Bundle();
        payload.putIntArray(VDKeyVehicle.INT_VECTOR, new int[]{type});
        VDEvent event = new VDEvent(VDEventVehicle.SV_DISPATCH_RADIO_REQUEST_INFO, payload);
        VDBus.getDefault().set(event);

        double[] data = event.getPayload().getDoubleArray(VDKeyVehicle.DOUBLE_VECTOR);

        int status = -1;
        if (data != null) {

            status = 0;
        }
        Log.d(TAG, "getUnit: " + status);
        return status;
    }*/

    //    Bundle payload = new Bundle();
    //    payload.putIntArray(VDKeyVehicleHal.INT_VECTOR, new int[]{0x5A, 1});
    //    VDEvent event = new VDEvent(VDEventVehicleHal.SV_DISPATCH_RADIO_REQUEST_INFO, payload);
    //    VDBus.getDefault().set(event);

    /**
     * //胎压单位
     * data[0]
     * 0x5A
     * data[1]
     * Bar    0x00
     * Kpa   0x01
     * Psi    0x02
     * //油耗单位
     * data[0]
     * 0x48
     * data[1]
     * L/100KM    0x00
     * KM/L   0x01
     * //电耗单位
     * data[0]
     * 0x62
     * data[1]
     * kWh/100km    0x00
     * Wh/km   0x01
     **/

    private void setUnit(int type, int state) {
        Bundle payload = new Bundle();
        payload.putIntArray(VDKeyVehicle.INT_VECTOR, new int[]{type, state});

        VDEvent event = new VDEvent(VDEventVehicle.SV_DISPATCH_RADIO_REQUEST_INFO, payload);
        VDBus.getDefault().set(event);
        Log.d(TAG, "setUnit: type=" + type + ", state=" + state);
    }

    private void requestUnit(int type) {
        Bundle payload = new Bundle();
        payload.putIntArray(VDKeyVehicle.INT_VECTOR, new int[]{type});
        VDEvent event = new VDEvent(VDEventVehicle.SV_DISPATCH_RADIO_REQUEST_INFO, payload);
        VDBus.getDefault().set(event);

//        event.getPayload().getParcelableArrayList(VDEventWallpaper.WALLPAPER_LIST_KEY);
    }

    @Override
    public void setInstrumentFuelUnit(int state) {
        Log.d(TAG, "setInstrumentFuelUnit 设置油耗单位:" + state);
        if (MsgUtil.getInstance().supportPowerMode()) {
//            int typeState = SystemConstant.INSTRUMENT_FUEL_UNIT_STATUS_LIST[state];
//            setUnit(SystemConstant.INSTRUMENT_FUEL_UNIT_INDEX_SET, typeState);
            setUnit(SystemConstant.INSTRUMENT_FUEL_UNIT_INDEX_SET, state);
            Prefs.put(PrefsConst.SYSTEM_INSTRUMENT_FUEL_UNIT, state);
            // 发送topic
            systemManager.setAverageFuelUnit(state);
//            Log.d(TAG, "setInstrumentFuelUnit 设置油耗单位成功:" + typeState);
            Log.d(TAG, "setInstrumentFuelUnit 设置油耗单位成功:" + state);
        } else {
            Log.d(TAG, "setInstrumentFuelUnit 设置油耗单位失败");
        }
    }

    @Override
    public int getInstrumentFuelUnit() {
        int value = systemManager.getAverageFuelUnit();
        Log.d(TAG, "getInstrumentFuelUnit 获取油耗单位:" + value);
        return value;
    }

    @Override
    public void requestInstrumentFuelUnit() {
        Log.d(TAG, "getInstrumentFuelUnit 获取油耗单位:");
        if (MsgUtil.getInstance().supportPowerMode()) {
            try {
                requestUnit(SystemConstant.INSTRUMENT_FUEL_UNIT_INDEX);
                Log.d(TAG, "getInstrumentFuelUnit 通知获取油耗单位成功");
            } catch (Exception e) {
                Log.e(TAG, "getInstrumentFuelUnit 通知获取油耗单位异常:", e);
            }
        }
    }

    @Override
    public void setTirePressureUnit(int state) {
        Log.d(TAG, "setTirePressureUnit 设置胎压单位:" + state);
        if (MsgUtil.getInstance().supportPowerMode()) {
//            int typeState = SystemConstant.TIRE_PRESSURE_UNIT_STATUS_LIST[state];
//            setUnit(SystemConstant.TIRE_PRESSURE_UNIT_INDEX_SET, typeState);
            setUnit(SystemConstant.TIRE_PRESSURE_UNIT_INDEX_SET, state);
            if (state == CarSystem.TirePressureDisplayUnit.kPa) {
                Prefs.put(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, 0);
            } else if (state == CarSystem.TirePressureDisplayUnit.psi) {
                Prefs.put(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, 1);
            } else if (state == CarSystem.TirePressureDisplayUnit.bar) {
                Prefs.put(PrefsConst.SYSTEM_TIRE_PRESSURE_UNIT, 2);
            }
            // 发送topic
            systemManager.setTirePressureUnit(state);
            Log.d(TAG, "setTirePressureUnit 设置胎压单位成功:" + state);
        } else {
            Log.d(TAG, "setTirePressureUnit 设置胎压单位失败");
        }
    }

    @Override
    public int getTirePressureUnit() {
        int value = systemManager.getTirePressureUnit();
        Log.d(TAG, "getInstrumentFuelUnit 获取胎压单位:" + value);
        return value;
    }

    /**
     * 恢复出厂设置，需要系统权限，以及系统签名
     */
    public void resetSystem() {
        PackageManager pm = mContext.getPackageManager();
        List<ApplicationInfo> apps = pm.getInstalledApplications(PackageManager.MATCH_UNINSTALLED_PACKAGES);

        IPackageManager ipm = IPackageManager.Stub.asInterface(ServiceManager.getService("package"));

        for (ApplicationInfo app : apps) {
            try {
                Log.d(TAG, "resetSystem: " + app.packageName);
                // 排除系统应用（只处理非系统应用）
                // 1. 清除应用数据
                if ((app.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                    Log.d(TAG, "clearApplicationUserData: " + app.packageName);
                    ipm.clearApplicationUserData(app.packageName, null, 0);
                    // 2. 卸载非系统应用
                    ipm.deletePackageAsUser(app.packageName, 0, null, 0, 0);
                }
            } catch (Exception e) {
                Log.e(TAG, "处理失败: " + app.packageName, e);
            }
        }
    }


    @Override
    public void requestTirePressureUnit() {
        Log.d(TAG, "getTirePressureUnit 获取胎压单位:");
        if (MsgUtil.getInstance().supportPowerMode()) {
            try {
                requestUnit(SystemConstant.TIRE_PRESSURE_UNIT_INDEX);
                Log.d(TAG, "getTirePressureUnit 通知获取胎压单位成功");
            } catch (Exception e) {
                Log.e(TAG, "getTirePressureUnit 通知获取胎压单位异常:", e);
            }
        }
    }

    @Override
    public void setPowerConsumptionUnit(int state) {
        Log.d(TAG, "setPowerConsumptionUnit:" + state);
        if (MsgUtil.getInstance().supportPowerMode()) {
//            int typeState = SystemConstant.POWER_CONSUMPTION_STATUS_LIST[state];
//            setUnit(SystemConstant.POWER_CONSUMPTION_UNIT_INDEX_SET, typeState);
            setUnit(SystemConstant.POWER_CONSUMPTION_UNIT_INDEX_SET, state);
            Prefs.put(PrefsConst.SYSTEM_POWER_CONSUMPTION_UNIT, state);
            // 发送topic
            systemManager.setAveragePowerUnit(state);
//            Log.d(TAG, "setPowerConsumptionUnit 设置电耗单位成功:" + typeState);
            Log.d(TAG, "setPowerConsumptionUnit 设置电耗单位成功:" + state);
        } else {
            Log.d(TAG, "setPowerConsumptionUnit 设置电耗单位失败");
        }
    }

    @Override
    public int getPowerConsumptionUnit() {
        int value = systemManager.getAveragePowerUnit();
        Log.d(TAG, "getInstrumentFuelUnit 获取电耗单位:" + value);
        return value;
    }

    @Override
    public void requestPowerConsumptionUnit() {
        Log.d(TAG, "getPowerConsumptionUnit 获取电耗单位:");
        if (MsgUtil.getInstance().supportPowerMode()) {
            try {
                requestUnit(SystemConstant.POWER_CONSUMPTION_UNIT_INDEX);
                Log.d(TAG, "getPowerConsumptionUnit 通知获取电耗单位成功");
            } catch (Exception e) {
                Log.e(TAG, "getPowerConsumptionUnit 通知获取电耗单位异常:", e);
            }
        }
    }

    @Override
    public void setUnitSetting(int state) {
        Log.d(TAG, "setUnitSetting:" + state);
        Prefs.put(PrefsConst.SYSTEM_UNIT_SETTING, state);
    }

    @Override
    public int getUnitSetting() {
        int status = Prefs.get(PrefsConst.SYSTEM_UNIT_SETTING, 0);
        Log.d(TAG, "getUnitSetting:" + status);
        return status;
    }

    @Override
    public void setTemperatureUnit(int state) {
        Log.d(TAG, "setTemperatureUnit:" + state);
        Prefs.put(PrefsConst.SYSTEM_TEMPERATURE_UNIT, state);
    }

    @Override
    public int getTemperatureUnit() {
        int status = Prefs.get(PrefsConst.SYSTEM_TEMPERATURE_UNIT, 0);
        Log.d(TAG, "getTemperatureUnit:" + status);
        return status;
    }

    private PermissionType mapPosition(int pos) {
        switch (pos) {
            case 0:
                return PermissionType.CAMERA;
            case 1:
                return PermissionType.MICROPHONE;
            case 2:
                return PermissionType.LOCATION;
            default:
                throw new IllegalArgumentException("Invalid position");
        }
    }

    private List<AppPermissionBean> getListByPosition(int position) {
        if (position == 0) return cameraList;
        if (position == 1) return microphoneList;
        if (position == 2) return locationList;
        throw new IllegalArgumentException("Invalid position");
    }

    @Override
    public void setSwitchPermission(int position, int status) {
        PermissionType type = mapPosition(position);
        SystemPermission sys = PermissionManager.getSystemPermission(mCtx, type);
        sys.setEnabled(status == 1);
        PermissionManager.setSystemPermission(mCtx, sys);

        if (status == 1) {
            if (position == 0) {
                // 摄像头授权开启状态
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA, status);
            } else if (position == 1) {
                // 麦克风授权开启状态
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE, status);
            } else if (position == 2) {
                // 位置授权开启状态
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION, status);
            }
        } else {
            if (position == 0) {
                // 摄像头授权开启状态
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA, status);
            } else if (position == 1) {
                // 麦克风授权开启状态
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE, status);
            } else if (position == 2) {
                // 位置授权开启状态
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION, status);
            }
        }
    }

    @Override
    public int getSwitchPermission(int position) {
        PermissionType type = mapPosition(position);
        boolean enabled = PermissionManager.getSystemPermission(mCtx, type).isEnabled();
        return enabled ? 1 : 0;
    }

    @Override
    public void setPermissionDuring(int position, int status) {
        PermissionType type = mapPosition(position);
        SystemPermission sys = PermissionManager.getSystemPermission(mCtx, type);
        SystemScope scope = (status == 0)
                ? SystemScope.ONE_YEAR
                : SystemScope.CURRENT_SESSION;
        sys.setScope(scope);
        PermissionManager.setSystemPermission(mCtx, sys);

        if (status == 0) {
            if (position == 0) {
                // 设置检测标志
                isThisTimePermissionCamera = false;
                // 摄像头授权时长
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA_DURATION, LocalDateTime.now().plusMonths(12));
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA_DURATION_TYPE, 0);
            } else if (position == 1) {
                // 设置检测标志
                isThisTimePermissionMicrophone = false;
                // 麦克风授权时长
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE_DURATION, LocalDateTime.now().plusMonths(12));
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE_DURATION_TYPE, 0);
            } else if (position == 2) {
                // 设置检测标志
                isThisTimePermissionLocation = false;
                // 位置授权时长
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION_DURATION, LocalDateTime.now().plusMonths(12));
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION_DURATION_TYPE, 0);
            }
        } else if (status == 1) {
            if (position == 0) {
                // 设置检测标志
                isThisTimePermissionCamera = true;
                // 摄像头授权时长
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA_DURATION, LocalDateTime.now());
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA_DURATION_TYPE, 1);
            } else if (position == 1) {
                // 设置检测标志
                isThisTimePermissionMicrophone = true;
                // 麦克风授权时长
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE_DURATION, LocalDateTime.now());
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE_DURATION_TYPE, 1);
            } else if (position == 2) {
                // 设置检测标志
                isThisTimePermissionLocation = true;
                // 位置授权时长
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION_DURATION, LocalDateTime.now());
                Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION_DURATION_TYPE, 1);
            }
        }
    }

    @Override
    public LocalDateTime getPermissionDuring(int position) {
        if (position == 0) {
            return Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA_DURATION, LocalDateTime.now());
        } else if (position == 1) {
            return Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE_DURATION, LocalDateTime.now());
        } else if (position == 2) {
            return Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION_DURATION, LocalDateTime.now());
        }
        return LocalDateTime.now();
    }

    @Override
    public int getPermissionDuringStatus(int position) {
        if (position == 0) {
            return Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA_DURATION_TYPE, 0);
        } else if (position == 1) {
            return Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE_DURATION_TYPE, 0);
        } else if (position == 2) {
            return Prefs.get(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION_DURATION_TYPE, 0);
        }
        return 0;
    }

    @Override
    public int setPermissionApp(int position, int index, AppPermissionBean bean) {
        // 1. 更新内存列表
        List<AppPermissionBean> list = getListByPosition(position);
        list.set(index, bean);

        // 2. 取出要操作的包名、权限类型
        String pkg = bean.getPackageName();
        PermissionType type = bean.getType();

        // 3. 判断要授予还是撤销
        boolean toGrant = (bean.getLevel() != AppPermissionLevel.DENIED);

        // 4. 日志打印，方便排查
        Log.d("PermissionApp", "setPermissionApp: 修改应用权限，pkg=" + pkg
                + ", type=" + type
                + ", level=" + bean.getLevel()
                + ", toGrant=" + toGrant);

        // 5. 通过 DevicePolicyManager 进行静默设置
        return PermissionManager.setAppPermissionState(
                mCtx,
                new ComponentName(mCtx, SystemPresenter.class),
                pkg,
                type,
                toGrant
        );
    }


    @Override
    public AppPermissionBean getPermissionApp(int position, int index) {
        return getListByPosition(position).get(index);
    }

    @Override
    public List<PrivacyStatementBean> getPrivacyStatementList() {
        String[] privacyStatementPaths;
        if (getDisplayMode()) {
            privacyStatementPaths = new String[]{
                    "night/chery.pdf",
                    "night/photo.pdf",
                    "night/calendar.pdf",
                    "night/dashboardcamera.pdf",
                    "night/Themeshopping.pdf",
                    "night/Personalprivacy.pdf",
                    "night/Sing.pdf",
                    "night/weather.pdf",
                    "night/Applicationstore.pdf",
                    "night/Vehicleupgrade.pdf",
                    "night/Auxiliarydriving.pdf",
                    "night/IntellectualLife.pdf",
                    "night/Sentinelmode.pdf",
                    "night/sceneMode.pdf",
                    "night/Healthexamination.pdf"
            };
        } else {
            privacyStatementPaths = new String[]{
                    "day/chery.pdf",
                    "day/photo.pdf",
                    "day/calendar.pdf",
                    "day/dashboardcamera.pdf",
                    "day/Themeshopping.pdf",
                    "day/Personalprivacy.pdf",
                    "day/Sing.pdf",
                    "day/weather.pdf",
                    "day/Applicationstore.pdf",
                    "day/Vehicleupgrade.pdf",
                    "day/Auxiliarydriving.pdf",
                    "day/IntellectualLife.pdf",
                    "day/Sentinelmode.pdf",
                    "day/sceneMode.pdf",
                    "day/Healthexamination.pdf"
            };
        }
        String[] privacyStatementNames = new String[]{
                "奇瑞汽车隐私政策",
                "图库",
                "日历",
                "行车记录仪",
                "主题商城",
                "个人中心",
                "唱吧",
                "雄狮天气",
                "应用商店",
                "车辆升级",
                "智能驾驶",
                "智行生活",
                "哨兵模式",
                "情景模式",
                "健康助手"

        };
        List<PrivacyStatementBean> list = new ArrayList<>();
        for (int i = 0; i < privacyStatementPaths.length; i++) {
            String privacyStatementPath = privacyStatementPaths[i];
            String privacyStatementName = privacyStatementNames[i];
            PrivacyStatementBean privacyStatementBean = new PrivacyStatementBean(privacyStatementName, privacyStatementName, privacyStatementPath);
            list.add(privacyStatementBean);
        }
        return list;
    }

    /**
     * 获取系统设备名称
     *
     * @return
     */
    @Override
    public String getDeviceInfo() {
        String deviceName = Prefs.get(PrefsConst.SYSTEM_DEVICE_INFO_NAME, PrefsConst.DefaultValue.SYSTEM_DEVICE_INFO_NAME);
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        deviceName = bluetoothAdapter.getName() == null ? deviceName : bluetoothAdapter.getName();
        Log.d("getDeviceName", "getDeviceInfo: " + deviceName);
//        bluetoothAdapter.setName(deviceName);
        return deviceName;
    }

    /**
     * 修改系统设备名称
     *
     * @param info
     */
    @Override
    public void setDeviceInfo(String info) {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (bluetoothAdapter == null) {
            return;
        }

        boolean wasBluetoothEnabled = bluetoothAdapter.isEnabled();
        try {
            // 如果蓝牙未开启，先开启它
            if (!wasBluetoothEnabled) {
                boolean enableSuccess = bluetoothAdapter.enable();
                if (!enableSuccess) {
                    return;
                }

                if (!bluetoothAdapter.isEnabled()) {
                    return;
                }
            }

            // 修改蓝牙名称
            boolean isFinish = bluetoothAdapter.setName(info);
            Log.d(TAG, "setDeviceInfo: " + isFinish);

        } catch (Exception e) {
            Log.e(TAG, "Thread interrupted while waiting for Bluetooth to enable", e);
        } finally {
            // 如果原本蓝牙是关闭状态，操作完成后关闭它
            Log.d(TAG, "Restoring Bluetooth to original state (disabled)...");
            bluetoothAdapter.disable();
        }
    }

    @Override
    public void setServicePrivacyAgreement(int status) {
        boolean b = new Random().nextInt(10) < 7;
        if (b) {
            Prefs.put(PrefsConst.SYSTEM_SERVICE_PRIVACY_AGREEMENT, status);
        } else {
            // 模拟网络错误
            Prefs.put(PrefsConst.SYSTEM_SERVICE_PRIVACY_AGREEMENT, 0);
        }
        Log.d(TAG, "setServicePrivacyAgreement: " + status);
    }

    @Override
    public int getServicePrivacyAgreement() {
        return Prefs.get(PrefsConst.SYSTEM_SERVICE_PRIVACY_AGREEMENT, 0);
    }

    List<SystemDataBean> realTimeSystemData = Arrays.asList(
            new SystemDataBean("实时数据显示日期", "yyyy-MM-dd", true),
            new SystemDataBean("实时数据显示时间", "HH:mm:ss", true),
            new SystemDataBean("实时数据显示星期", "星期X", true),
            new SystemDataBean("实时数据显示秒针", "显示", true),
            new SystemDataBean("实时数据显示日期", "yyyy-MM-dd", true),
            new SystemDataBean("实时数据显示时间", "HH:mm:ss", true)
    );
    List<SystemDataBean> diagnosticSystemData = Arrays.asList(
            new SystemDataBean("诊断数据显示日期", "yyyy-MM-dd", true),
            new SystemDataBean("诊断数据显示时间", "HH:mm:ss", true),
            new SystemDataBean("诊断数据显示星期", "星期X", true),
            new SystemDataBean("诊断数据显示秒针", "显示", true),
            new SystemDataBean("诊断数据显示日期", "yyyy-MM-dd", true)
    );


    @Override
    public List<SystemDataBean> getSystemDatatList(int type) {
        boolean b = new Random().nextInt(10) < 8;
        // 模拟网络错误
        if (!b) return Collections.emptyList();
        return switch (type) {
            case 0 -> realTimeSystemData;
            case 1 -> diagnosticSystemData;
            default -> Collections.emptyList();
        };
    }

    @Override
    public int setSystemData(int type, int position, boolean isChecked) {
        boolean b = new Random().nextInt(10) < 8;
        if (!b) {
            return 0;
        }
        if (type == 0) {
            realTimeSystemData.get(position).setChecked(isChecked);
        } else if (type == 1) {
            diagnosticSystemData.get(position).setChecked(isChecked);
        }
        return 1;
    }

    @Override
    public int getSystemDataAcquisitionStatus(int index) {
        if (index == 0) {
            return Prefs.get(PrefsConst.SYSTEM_PRIVACY_STATEMENT_ACTIVITY_DATA, 1);
        } else if (index == 1) {
            return Prefs.get(PrefsConst.SYSTEM_PRIVACY_STATEMENT_SETTING_DATA, 1);
        } else if (index == 2) {
            return Prefs.get(PrefsConst.SYSTEM_PRIVACY_STATEMENT_BROWSE_DATA, 1);
        }
        return 1;
    }

    @Override
    public void setSystemDataAcquisitionStatus(int index, int status) {
        if (index == 0) {
            Prefs.put(PrefsConst.SYSTEM_PRIVACY_STATEMENT_ACTIVITY_DATA, status);
        } else if (index == 1) {
            Prefs.put(PrefsConst.SYSTEM_PRIVACY_STATEMENT_SETTING_DATA, status);
        } else if (index == 2) {
            Prefs.put(PrefsConst.SYSTEM_PRIVACY_STATEMENT_BROWSE_DATA, status);
        }
    }

    @Override
    public boolean isPGear() {
        return new Random().nextInt(10) < 8;
    }

    @Override
    public boolean isFirstStart() {
        int key = Prefs.get(PrefsConst.KEY_FIRST_LAUNCH, 1);
        Prefs.put(PrefsConst.KEY_FIRST_LAUNCH, key);
        return key == 1;
    }

    @Override
    public String getStorageMsg() {
        StatFs stat = new StatFs("/");
        // 获取存储的块大小
        long blockSize = stat.getBlockSizeLong();

        // 获取总块数
        long totalBlocks = stat.getBlockCountLong();

        // 获取可用块数
        long availableBlocks = stat.getAvailableBlocksLong();

        // 计算总存储空间（单位：字节）
        long totalSpace = totalBlocks * blockSize;

        // 计算可用存储空间（单位：字节）
        long availableSpace = availableBlocks * blockSize;

        // 计算已用存储空间
        long usedSpace = totalSpace - availableSpace;

        // 将字节转换为GB
        double usedGB = (double) usedSpace / (1024 * 1024 * 1024);
        double totalGB = (double) totalSpace / (1024 * 1024 * 1024);
// 返回已用存储空间（保留一位小数）和总存储空间（无小数）
        return String.format("%.1fGB / %.0fGB", usedGB, totalGB);

    }

    @Override
    public String getSystemHardwareVersionInfo() {
        // TODO 获取系统硬件版本信息
        String systemHardwareVersion = "0.1.0";
//        systemHardwareVersion = SystemProperties.get("persist.vendor.cdc.hw_version", "--.--.--");
        Log.d("systemSoftwareVersion", systemHardwareVersion);
        Prefs.put(PrefsConst.SYSTEM_SYSTEM_HARDWARE_VERSION, systemHardwareVersion);
        return systemHardwareVersion;
    }

    @Override
    public String getSystemSoftwareVersionInfo() {
        // TODO 获取系统软件版本信息
        String systemSoftwareVersion = "00.01.00";
//        systemSoftwareVersion = SystemProperties.get("ro.build.version.incremental", "00.01.00");
        Log.d("systemSoftwareVersion", systemSoftwareVersion);
        Prefs.put(PrefsConst.SYSTEM_SYSTEM_SOFTWARE_VERSION, systemSoftwareVersion);
        return systemSoftwareVersion;
    }

    @Override
    public String getTBoxSoftwareVersionInfo() {
        // TODO 获取TBOX软件版本信息
        return "";
    }

    @Override
    public String getTBoxHardwareVersionInfo() {
        // TODO 获取TBOX硬件版本信息
        return "";
    }

    @Override
    public Map<String, Boolean> systemReset() {
        // 定义初始化结果字典
        Map<String, Boolean> result = new HashMap<>();
        // 语言设置 初始语言为中文
        result.put("language", true);
        try {
            Configuration config = ActivityManagerNative.getDefault().getConfiguration();
            config.locale = Locale.SIMPLIFIED_CHINESE;
            LocalePicker.updateLocale(config.locale);
            GlobalVar.setIsSetSystem(true);
        } catch (RemoteException exception) {
            Log.e(TAG, "setSystemLanguage : exception = " + exception.getMessage());
            exception.printStackTrace();
            result.put("language", false);
        }

        // 自动校准日期和时间的设置
        setTimeDisplay(PrefsConst.DefaultValue.SYSTEM_DATE_TIME_FORMAT, mContext);
        result.put("datetime", true);

        // 仪表油耗单位
        setInstrumentFuelUnit(PrefsConst.DefaultValue.SYSTEM_INSTRUMENT_FUEL_UNIT);
        result.put("instrumentFuelUnit", true);

        // 按键自定义

        return result;
    }

    public void factoryReset() {
        disposables.add(
                Completable.fromAction(this::factoryResetSystemTBox)
                        .andThen(Completable.fromAction(() -> {
                            // 播放开机动画
                            systemManager.setBootanimation(2, 0);
                        }))
                        .andThen(Completable.fromAction(this::factoryResetSystemICU))
                        .andThen(Completable.fromAction(() -> {
                            Log.d("蓝牙恢复出厂设置", "蓝牙恢复出厂设置");
                            factoryResetBt();
                        }))
                        .andThen(Completable.fromAction(() -> {
                            Log.d("声音恢复出厂设置", "声音恢复出厂设置");
                            voicePresenter = new VoicePresenter();
                            voicePresenter.reset();
                        }))
                        .andThen(Completable.fromAction(() -> {
                            Log.d("系统恢复出厂设置", "系统恢复出厂设置");
                            systemReset();
                        }))
                        .andThen(Completable.fromAction(() -> {
                            Log.d("WIFI恢复出厂设置", "WIFI恢复出厂设置");
                            factoryResetWifi();
                        }))
                        .andThen(Completable.fromAction(this::factoryResetSystemAvm))
                        .andThen(Completable.fromAction(this::factoryResetSystemDvr))
                        .andThen(Completable.fromAction(() -> {
                            Log.d("Data恢复出厂设置", "Data恢复出厂设置");
                            resetSystem();
                        }))
                        .andThen(Completable.fromAction(() -> {
                            Log.d("显示恢复出厂设置", "显示恢复出厂设置");
                            new Handler(Looper.getMainLooper()).post(() -> {
                                displayPresenter = DisplayPresenter.getInstance();
                                displayPresenter.reset(); // 必须在主线程执行
                                displayPresenter.setSystemColor(DEFAULT_SYSTEM_COLOR); // 系统色恢复
                            });
                        }))
                        .andThen(Completable.fromAction(() -> {
                            Settings.Global.putInt(
                                    mContext.getContentResolver(),
                                    "has_factory_reset",
                                    1
                            );
                            Settings.Global.putLong(
                                    mContext.getContentResolver(),
                                    "last_reset_timestamp",
                                    System.currentTimeMillis()
                            );
                            Log.d("factoryReset", "已保存恢复出厂设置状态到系统Settings");
                        }))
                        .subscribeOn(Schedulers.io()) // 在 IO 线程执行所有操作
                        .observeOn(AndroidSchedulers.mainThread()) // 最后回到主线程（如需要 UI 操作）
                        .subscribe(
                                () -> {
                                    Log.d("factoryReset", "所有恢复操作完成");
                                    //         过 7 秒后，发送通知给MCU
                                    startCountdown();
                                },
                                throwable -> Log.e("factoryReset", "恢复失败", throwable)
                        )
        );
    }

    // 开始计时
    private void startCountdown() {
        mCountdownRunnable = new Runnable() {
            @Override
            public void run() {
                // 发送信号给MCU
                Log.d(TAG, "发送通知给MCU");
                byte[] bytes = new byte[3];
                bytes[0] = 0x23;
                bytes[1] = 0x15;
                systemManager.sendSignalToMCU(bytes);
            }
        };

        // 延迟7秒执行
        mHandler.postDelayed(mCountdownRunnable, 7000); // 7000毫秒 = 7秒
    }

    private void factoryResetSystemAvm() {
        // AVM 只有一个按钮，无需恢复
    }

    private void factoryResetSystemDvr() {
        // 发送广播
        Intent intent = new Intent("com.bitech.vehicleSetting.FACTORY_RESET");
        intent.putExtra("factory_reset", true);
        mContext.sendBroadcast(intent);
    }

    /**
     * 恢复出厂设置TBox.
     */
    private void factoryResetSystemTBox() {
        Log.d(TAG, "factoryResetSystemTBox : ");
        // 暂未接入TBox
    }


    private void factoryResetSystemICU() {
        Log.d(TAG, "factoryResetSystemTBox : ");
        // 暂未接入ICU
    }

    // 恢复Wifi
    private void factoryResetWifi() {
        WifiViewModel wifiViewModel = new WifiViewModel();
        carWifiManager = CarWifiManager.Companion.getInstance();
        carWifiManager.closeFirstWifiHotspot();
        wifiManager = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
        wifiManager.setWifiEnabled(false);
        //初始化热点密码
        wifiViewModel.updateHotspotPassword(generatePassword());
        clearAllSavedWifi(mContext);
    }

    /**
     * 恢复出厂设置，清空Data分区
     */
    private void factoryResetClearAppData() {
        // 日志：开始执行恢复出厂设置（清除应用数据）

        // 获取 ActivityManager 系统服务
//        ActivityManager activityManager = (ActivityManager) mContext.getSystemService(Context.ACTIVITY_SERVICE);
//
//        // 如果数据清除观察者未初始化，则创建
//        if (clearUserDataObserver == null) {
//            clearUserDataObserver = new ClearUserDataObserver();
//        }
//
//        for (String packageName : Contacts.packageNames) {
//            try {
//                // 调用系统 API 清除应用数据
//                boolean res = activityManager.clearApplicationUserData(packageName, clearUserDataObserver);
//            } catch (Exception exception) {
//                // 打印异常日志
//                exception.printStackTrace();
//            }
//        }
    }

    // 恢复WIFI
    public void clearAllSavedWifi(Context context) {
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        // 获取所有已保存的 Wi-Fi 配置
        List<WifiConfiguration> configs = wifiManager.getConfiguredNetworks();
        if (configs == null) {
            return;
        }
        // 遍历并移除所有配置的网络
        for (WifiConfiguration config : configs) {
            wifiManager.removeNetwork(config.networkId);
            // 要使更改生效，可能需要保存配置
            wifiManager.saveConfiguration();
        }
    }

    // 恢复蓝牙
    private void factoryResetBt() {
        Log.d(TAG, "factoryResetBt : ");
        BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        mBluetoothAdapter.disable();

        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        // 获取已配对设备列表
        Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
        if (pairedDevices.isEmpty()) {
            // 没有已配对设备
            return;
        }
        for (BluetoothDevice device : pairedDevices) {
            try {
                // 取消配对
                Method method = device.getClass().getMethod("removeBond", (Class<?>[]) null);
                method.invoke(device, (Object[]) null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @ClassName: ClearUserDataObserver
     * @Date: 2025/5/20 15:38
     * @Description: 清除应用用户数据定义类.
     */
    class ClearUserDataObserver extends IPackageDataObserver.Stub {

        private static final String TAG = "YourTag"; // 替换成你实际使用的 TAG

        /**
         * 当应用数据移除完成时.
         *
         * @param packageName 应用包名
         * @param isSucceeded 是否成功
         */
        @Override
        public void onRemoveCompleted(String packageName, boolean isSucceeded) {
            Log.d(TAG, "onRemoveCompleted : " +
                    "packageName = " + packageName + ", " +
                    "isSucceeded = " + isSucceeded);
        }
    }


    public void destroy() {
        // RxJava 取消订阅
        disposables.clear(); // 取消所有订阅
    }
}
