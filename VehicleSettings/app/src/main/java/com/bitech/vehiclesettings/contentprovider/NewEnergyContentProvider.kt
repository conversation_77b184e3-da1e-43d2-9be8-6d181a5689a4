package com.bitech.vehiclesettings.contentprovider

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.net.toUri
import com.bitech.vehiclesettings.service.DrivingLifeCycle
import com.bitech.vehiclesettings.service.NegativeScreenLifecycle
import com.bitech.vehiclesettings.service.NewEnergyLifeCycle

class NewEnergyContentProvider : ContentProvider() {

    companion object {
        val NEW_ENERGY_URI = "content://com.bitech.vehiclesettings.contentprovider.new_energy".toUri()
        const val TAG = "NewEnergyProvider"
        const val SHOW_CHARGING = "show_charging"
        const val HIDE_CHARGING = "hide_charging"
        const val QUERY_BATTERY_STATUS = "query_battery_status"
        //状态栏灵动岛电池状态 1 充电 2 放电
        const val BATTERY_CHARGING = 1
        const val BATTERY_DISCHARGING = 2
        //展车模式状态 0x0:No Active  0x1:Active
        const val QUERY_EXHIBITION_MOD = "query_exhibition_mod"
        //牵引模式状态
        //0x0:NotActive
        //0x1:Active
        //0x2:Reserved
        const val QUREY_TOWING_MOD = "query_towing_mod"
        //驻车保电状态
        //0x1:ON
        //0x0:OFF
        const val PARK_POWER = "query_park_power"
    }

    private val mainHandler = Handler(Looper.getMainLooper())

    override fun onCreate(): Boolean {
        return true
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        return 0
    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        return when (method) {
            QUERY_BATTERY_STATUS -> handleQueryBattery(arg, extras)
            SHOW_CHARGING -> handleShowCharging(arg, extras)
            HIDE_CHARGING -> handleHideCharging(arg, extras)
            QUERY_EXHIBITION_MOD -> handleQueryExhibitionMod(arg, extras)
            QUREY_TOWING_MOD -> handleQueryTowingMod(arg, extras)
            PARK_POWER -> handleQueryParkPower(arg, extras)
            else -> null
        }
    }

    private fun handleQueryParkPower(arg: String?, extras: Bundle?): Bundle? {
        Log.d(TAG, "handleQueryParkPower: arg=$arg, extras=$extras")
        val result = Bundle()
        result.putInt("parkPower", NegativeScreenLifecycle.parkPower.get())
        return result
    }

    private fun handleQueryTowingMod(arg: String?, extras: Bundle?): Bundle? {
        Log.d(TAG, "handleQueryTowingMod: arg=$arg, extras=$extras")
        val result = Bundle()
        result.putInt("towingMod", DrivingLifeCycle.mCurrentTowingMode.get())
        return result
    }

    private fun handleQueryBattery(arg: String?, extras: Bundle?): Bundle {
        Log.d(TAG, "handleQueryBattery: arg=$arg, extras=$extras")
        val result = Bundle()
        val batteryStatus = if (NewEnergyLifeCycle.chargingStatus.get()) BATTERY_CHARGING else
            if (NewEnergyLifeCycle.dischargingStatus.get()) BATTERY_DISCHARGING else 0
        result.putInt("state", batteryStatus)
        result.putInt("batteryLevel", NewEnergyLifeCycle.mCurrentSOC.get())
        return result
    }

    private fun handleShowCharging(arg: String?, extras: Bundle?): Bundle? {
        Log.d(TAG, "handleShowCharging: arg=$arg, extras=$extras")
        mainHandler.post {
            NewEnergyLifeCycle.showChargingView(true)
        }
        return null
    }

    private fun handleHideCharging(arg: String?, extras: Bundle?): Bundle? {
        Log.d(TAG, "handleHideCharging: arg=$arg, extras=$extras")
        mainHandler.post {
            NewEnergyLifeCycle.showChargingView(false)
        }
        return null
    }

    private fun handleQueryExhibitionMod(arg: String?, extras: Bundle?): Bundle {
        Log.d(TAG, "handleQueryExhibitionMod: arg=$arg, extras=$extras")
        val result = Bundle()
        result.putInt("exhibitionMod", NewEnergyLifeCycle.exhibitionMod.get())
        return result
    }

}