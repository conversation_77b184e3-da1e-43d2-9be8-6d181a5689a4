package com.bitech.vehiclesettings.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class BeanToStringUtil {
    public static String beanToString(Object bean) {
        if (bean == null) {
            return "null";
        }

        StringBuilder sb = new StringBuilder();
        sb.append(bean.getClass().getSimpleName()).append("{");

        try {
            Field[] fields = bean.getClass().getDeclaredFields();
            boolean firstField = true;

            for (Field field : fields) {
                if (!firstField) {
                    sb.append(", ");
                }
                firstField = false;

                field.setAccessible(true);
                String fieldName = field.getName();
                Object value = null;

                try {
                    String getterName = "get" +
                            fieldName.substring(0, 1).toUpperCase() +
                            fieldName.substring(1);
                    Method getter = bean.getClass().getMethod(getterName);
                    value = getter.invoke(bean);
                } catch (Exception e) {
                    value = field.get(bean);
                }

                sb.append(fieldName).append("=")
                        .append(value != null ? value.toString() : "null");
            }
        } catch (Exception e) {
            return "Error converting bean: " + e.getMessage();
        }

        sb.append("}");
        return sb.toString();
    }
}
