package com.bitech.vehiclesettings.bean.atmosphere;

import com.bitech.platformlib.bean.Extension;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.vehiclesettings.utils.Bit64Utils;

public class AmbLigBean {

    Extension extension;
    Boolean relative = false;
    Long time;
    Boolean valid = true;
    private long value = 0L;

    public long getValue() {
        return value;
    }

    public AmbLigBean() {
        if (extension == null) {
            extension = new Extension();
        }
        time = System.currentTimeMillis();
    }

    public AmbLigBean setFront() {
        // 2,4,5,7,8
        setAmbLig02Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig04Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig05Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig07Authn(CarLight.AmbLigXXAuthn.Active).setAmbLig08Authn(CarLight.AmbLigXXAuthn.Active);
        return this;
    }

    public AmbLigBean setRear() {
        // 9
        setAmbLig09Authn(CarLight.AmbLigXXAuthn.Active);
        return this;
    }

    public AmbLigBean setAll() {
        setFront().setRear();
        return this;
    }

    public int getAmbLig01Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig01Authn));
    }

    public AmbLigBean setAmbLig01Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig01Authn, val);
        return this;
    }

    public int getAmbLig02Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig02Authn));
    }

    public AmbLigBean setAmbLig02Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig02Authn, val);
        return this;
    }

    public int getAmbLig03Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig03Authn));
    }

    public AmbLigBean setAmbLig03Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig03Authn, val);
        return this;
    }

    public int getAmbLig04Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig04Authn));
    }

    public AmbLigBean setAmbLig04Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig04Authn, val);
        return this;
    }


    public int getAmbLig05Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig05Authn));
    }

    public AmbLigBean setAmbLig05Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig05Authn, val);
        return this;
    }

    public int getAmbLig06Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig06Authn));
    }

    public AmbLigBean setAmbLig06Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig06Authn, val);
        return this;
    }

    public int getAmbLig07Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig07Authn));
    }

    public AmbLigBean setAmbLig07Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig07Authn, val);
        return this;
    }

    public int getAmbLig08Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig08Authn));
    }

    public AmbLigBean setAmbLig08Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig08Authn, val);
        return this;
    }

    public int getAmbLig09Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig09Authn));
    }

    public AmbLigBean setAmbLig09Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig09Authn, val);
        return this;
    }

    public int getAmbLig10Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig10Authn));
    }

    public AmbLigBean setAmbLig10Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig10Authn, val);
        return this;
    }

    public int getAmbLig11Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig11Authn));
    }

    public AmbLigBean setAmbLig11Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig11Authn, val);
        return this;
    }

    public int getAmbLig12Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig12Authn));
    }

    public AmbLigBean setAmbLig12Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig12Authn, val);
        return this;
    }

    public int getAmbLig13Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig13Authn));
    }

    public AmbLigBean setAmbLig13Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig13Authn, val);
        return this;
    }

    public int getAmbLig14Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig14Authn));
    }

    public AmbLigBean setAmbLig14Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig14Authn, val);
        return this;
    }

    public int getAmbLig15Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig15Authn));
    }

    public AmbLigBean setAmbLig15Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig15Authn, val);
        return this;
    }

    public int getAmbLig16Authn() {
        return (int) (Bit64Utils.getBitSet(value, AmbLigBeanConst.ambLig16Authn));
    }

    public AmbLigBean setAmbLig16Authn(int val) {
        this.value = Bit64Utils.setBit(value, AmbLigBeanConst.ambLig16Authn, val);
        return this;
    }


    public int getAmbLigBriAdj() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.ambLigBriAdj, AmbLigBeanConst.ambLigBriAdj + 7));
    }

    public AmbLigBean setAmbLigBriAdj(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.ambLigBriAdj, AmbLigBeanConst.ambLigBriAdj + 7, val);
        return this;
    }

    public int getAmbLigColorAdj() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.ambLigColorAdj, AmbLigBeanConst.ambLigColorAdj + 8));
    }

    public AmbLigBean setAmbLigColorAdj(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.ambLigColorAdj, AmbLigBeanConst.ambLigColorAdj + 8, val);
        return this;
    }

    public int getAmbLigFadeINorOUTStepTi() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.ambLigFadeINorOUTStepTi, AmbLigBeanConst.ambLigFadeINorOUTStepTi + 7));
    }

    public AmbLigBean setAmbLigFadeINorOUTStepTi(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.ambLigFadeINorOUTStepTi, AmbLigBeanConst.ambLigFadeINorOUTStepTi + 7, val);
        return this;
    }

    public int getAmbLigFlngModSel() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.ambLigFlngModSel, AmbLigBeanConst.ambLigFlngModSel + 6));
    }

    public AmbLigBean setAmbLigFlngModSel(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.ambLigFlngModSel, AmbLigBeanConst.ambLigFlngModSel + 6, val);
        return this;
    }

    public int getDymAmbLigLevelSig() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.dymAmbLigLevelSig, AmbLigBeanConst.dymAmbLigLevelSig + 7));
    }

    public AmbLigBean setDymAmbLigLevelSig(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.dymAmbLigLevelSig, AmbLigBeanConst.dymAmbLigLevelSig + 7, val);
        return this;
    }

    public int getDymAmbLigDirSig() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.dymAmbLigDirSig, AmbLigBeanConst.dymAmbLigDirSig + 3));
    }

    public AmbLigBean setDymAmbLigDirSig(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.dymAmbLigDirSig, AmbLigBeanConst.dymAmbLigDirSig + 3, val);
        return this;
    }

    public int getAmbModLocSig() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.ambModLocSig, AmbLigBeanConst.ambModLocSig + 3));
    }

    public AmbLigBean setAmbModLocSig(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.ambModLocSig, AmbLigBeanConst.ambModLocSig + 3, val);
        return this;
    }

    public int getAmbLigModRepOrdIdeSig() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.ambLigModRepOrdIdeSig, AmbLigBeanConst.ambLigModRepOrdIdeSig + 1));
    }

    public AmbLigBean setAmbLigModRepOrdIdeSig(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.ambLigModRepOrdIdeSig, AmbLigBeanConst.ambLigModRepOrdIdeSig + 1, val);
        return this;
    }

    public int getAmbLigModTimCabSig() {
        return (int) (Bit64Utils.getBitsRange(value, AmbLigBeanConst.ambLigModTimCabSig, AmbLigBeanConst.ambLigModTimCabSig + 1));
    }

    public AmbLigBean setAmbLigModTimCabSig(int val) {
        this.value = Bit64Utils.setBitsRange(value, AmbLigBeanConst.ambLigModTimCabSig, AmbLigBeanConst.ambLigModTimCabSig + 1, val);
        return this;
    }
}
