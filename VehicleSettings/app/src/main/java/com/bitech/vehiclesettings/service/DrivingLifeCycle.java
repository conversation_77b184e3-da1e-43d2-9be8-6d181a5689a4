package com.bitech.vehiclesettings.service;

import static com.bitech.vehiclesettings.MyApplication.getContext;
import static com.bitech.vehiclesettings.MyApplication.getInstance;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.driving.IDrivingManagerListener;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.contentprovider.NewEnergyContentProvider;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.DialogNavigationUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.TtsHelper;

import java.lang.ref.WeakReference;
import java.util.concurrent.atomic.AtomicInteger;


public class DrivingLifeCycle implements LifecycleEventObserver {
    private static final String TAG = DrivingLifeCycle.class.getName();

    private DrivingManager drivingManager;
    private NewEnergyManager newEnergyManager;
    private final Handler handler = new DrivingLifeCycleServiceHandler(this, Looper.getMainLooper());

    // 方控按键
    private long pressStartTime = 0; // 记录按键按下时刻的时间戳
    private static final int SHORT_PRESS_THRESHOLD = 200;
    private static final int LONG_PRESS_THRESHOLD = 1200;
    private boolean isProcessingPress = false; // 防止重复处理标志
    private boolean hasLongPressTriggered = false; // 是否已经执行过长按


    // TTS语音是否播报
    private boolean drivingTTS = false;

    // 车辆模式
    public static AtomicInteger carMode = new AtomicInteger(CarDriving.VCC_1_DriveMode.ECO);
    public static AtomicInteger mCurrentTowingMode = new AtomicInteger(); // 牵引模式
    public static AtomicInteger gear = new AtomicInteger(CarDriving.VCU_PRNDGearAct.INIT); // 挡位


    @Override
    public void onStateChanged(@NonNull LifecycleOwner lifecycleOwner, @NonNull Lifecycle.Event event) {
        switch (event) {
            case ON_CREATE:
                // 初始化TTS语音播报
                Log.i(TAG, "onCreate: DrivingLifeCycle" + lifecycleOwner);
                drivingManager = (DrivingManager) BitechCar.getInstance()
                        .getServiceManager(BitechCar.CAR_DRIVING_MANAGER);
                newEnergyManager = (NewEnergyManager) BitechCar.getInstance()
                        .getServiceManager(BitechCar.CAR_ENERGY_MANAGER);
                if (drivingManager != null) {
                    drivingManager.addCallback(TAG, msgCallback);
                    drivingManager.registerListener();
                }
                // 车辆模式
                Integer carModeTemp = drivingManager.getDriveMode();
                if (carModeTemp != Integer.MIN_VALUE) {
                    carMode.set(carModeTemp);
                }
                // 挡位
                int gearSignal = drivingManager.getGearPosition();
                if (gearSignal != Integer.MIN_VALUE) {
                    gear.set(gearSignal);
                }
                // 获取牵引模式
                int towingModeSwitch = newEnergyManager.getTowingModeSwitch();
                mCurrentTowingMode.set(towingModeSwitch == Integer.MIN_VALUE ? 0 : towingModeSwitch);
                break;
            case ON_DESTROY:
                Log.i(TAG, "onDestroy: " + lifecycleOwner);
                if (drivingManager != null) {
                    drivingManager.removeCallback(TAG);
                }
                // TTS播报销毁
                TtsHelper.getInstance().release();
                break;
            default:
                break;
        }
    }

    private IDrivingManagerListener msgCallback = new IDrivingManagerListener() {
        @Override
        public void drivingKeyInputEventCallback(int signalVal) {
            Log.d(TAG, "方控键切换驾驶模式 drivingKeyInputEventCallback: " + signalVal);
            switch (signalVal) {
                case 0x1: // 按下
                    drivingTTS = true;
                    if (!isProcessingPress) {
                        pressStartTime = System.currentTimeMillis();
                        isProcessingPress = true;
                        hasLongPressTriggered = false;

                        // 延时触发长按
                        handler.sendEmptyMessageDelayed(
                                MessageConst.DRIVING_MODE_KEY_EVENT_SERVICE,
                                LONG_PRESS_THRESHOLD
                        );
                    }
                    break;

                case 0x0: // 松手
                    if (isProcessingPress) {
                        long pressDuration = System.currentTimeMillis() - pressStartTime;
                        isProcessingPress = false;

                        // 如果已经触发长按，不做额外操作
                        if (hasLongPressTriggered) {
                            Log.d(TAG, "长按已触发，松手无操作");
                        } else {
                            // 取消长按延时
                            handler.removeMessages(MessageConst.DRIVING_MODE_KEY_EVENT_SERVICE);

                            if (pressDuration >= SHORT_PRESS_THRESHOLD && pressDuration < LONG_PRESS_THRESHOLD) {
                                Log.d(TAG, "短按触发，持续时间: " + pressDuration + "ms");
                                updateCarModeUIByInputEventShortPress();
                                openMainActivityDriving();
                                handler.sendEmptyMessageDelayed(MessageConst.DRIVING_MODE_LIFE_CYCLE, 2000);
                            } else {
                                Log.d(TAG, "忽略按键，持续时间: " + pressDuration + "ms");
                            }
                        }
                    }
                    break;
            }
        }


        @Override
        public void driveModeCallback(int signalVal) {
            Log.d(TAG, "车辆模式 carModeCallback signalVal: " + signalVal);
            if (signalVal == CarDriving.VCC_1_DriveMode.ECO
                    || signalVal == CarDriving.VCC_1_DriveMode.NORMAL
                    || signalVal == CarDriving.VCC_1_DriveMode.SPORT
                    || signalVal == CarDriving.VCC_1_DriveMode.SNOW
                    || signalVal == CarDriving.VCC_1_DriveMode.INDIVIDUAL
                    || signalVal == CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID
                    || signalVal == CarDriving.VCC_1_DriveMode.AI
                    || signalVal == CarDriving.VCC_1_DriveMode.MUD
                    || signalVal == CarDriving.VCC_1_DriveMode.OFF_ROAD
            ) {
                carMode.set(signalVal);
                if (drivingTTS) {
                    drivingTTS = false;
                    carModeSpeakTTS();
                }
            }
        }

        @Override
        public void towModeCallback(int signalVal) {
            Log.d(TAG, "牵引模式 towModeCallback: " + signalVal);
            if (signalVal == CarDriving.VCU_TowingMode.NOT_ACTIVE || signalVal == CarDriving.VCU_TowingMode.ACTIVE) {
                mCurrentTowingMode.set(signalVal);
                notifyNewEnergyUri();
            }
        }

        @Override
        public void onGearPositionChanged(int gearPosition) {
            IDrivingManagerListener.super.onGearPositionChanged(gearPosition);
            if (gearPosition != Integer.MIN_VALUE) {
                gear.set(gearPosition);
            }
        }
    };

    private void openMainActivityDriving() {
        DialogNavigationUtils.launchMainActivity(getContext(), MainActivity.MainTabIndex.DRIVE, -1, CommonConst.DIALOG_OPEN);
    }

    private void carModeSpeakTTS() {
        String speakText = "";
        try {
            Context context = MyApplication.getContext();

            switch (carMode.get()) {
                // 纯电优先
                case CarDriving.VCC_1_DriveMode.ECO:
                    speakText = context.getString(R.string.str_driving_car_mode_1_tts);
                    break;
                // 节能混动
                case CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID:
                    speakText = context.getString(R.string.str_driving_car_mode_2_tts);
                    break;
                // 舒适混动
                case CarDriving.VCC_1_DriveMode.NORMAL:
                    speakText = context.getString(R.string.str_driving_car_mode_3_tts);
                    break;
                // 风云GT
                case CarDriving.VCC_1_DriveMode.SPORT:
                    speakText = context.getString(R.string.str_driving_car_mode_4_tts);
                    break;
                // 雨雪模式
                case CarDriving.VCC_1_DriveMode.SNOW:
                    speakText = context.getString(R.string.str_driving_car_mode_5_tts);
                    break;
                // 个性化
                case CarDriving.VCC_1_DriveMode.INDIVIDUAL:
                    speakText = context.getString(R.string.str_driving_car_mode_6_tts);
                    break;
                // 越野模式
                case CarDriving.VCC_1_DriveMode.OFF_ROAD:
                    speakText = context.getString(R.string.str_driving_car_mode_7_tts);
                    break;
                // AI模式
                case CarDriving.VCC_1_DriveMode.AI:
                    speakText = context.getString(R.string.str_driving_car_mode_8_tts);
                    break;
                // 沙地模式
                case CarDriving.VCC_1_DriveMode.MUD:
                    speakText = context.getString(R.string.str_driving_car_mode_9_tts);
                    break;
                default:
                    speakText = "";
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "SpeakTTSException: " + e.getMessage());
            speakText = "";
        }
        if (!speakText.isEmpty() && !speakText.equals("")) {
            speakText(speakText);
        }
    }

    private void notifyNewEnergyUri() {
        Log.d(TAG, "notifyNewEnergyUri: ");
        MyApplication.getContext().getContentResolver().notifyChange(NewEnergyContentProvider.Companion.getNEW_ENERGY_URI(), null);
    }

    private void getCarMode() {
        int driveMode = drivingManager.getDriveMode();
        if (driveMode != Integer.MIN_VALUE) {
            carMode.set(driveMode);
        }
    }

    /**
     * 方控键切换车辆模式 - 短按
     */
    private void updateCarModeUIByInputEventShortPress() {
        switch (carMode.get()) {
            case CarDriving.VCC_1_DriveMode.ECO:
                drivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.ENERGY_SAVING_HYBRID);
                break;
            case CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID:
                drivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.NORMAL);
                break;
            case CarDriving.VCC_1_DriveMode.NORMAL:
                drivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.SPORT);
                break;
            case CarDriving.VCC_1_DriveMode.SPORT:
                drivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.RAIN_SNOW);
                break;
            case CarDriving.VCC_1_DriveMode.SNOW:
                drivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.INDIVIDUAL);
                break;
            case CarDriving.VCC_1_DriveMode.INDIVIDUAL:
                drivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.ECO);
                break;
            default:
                break;
        }
    }

    /**
     * 方控键切换车辆模式 - 长按
     */
    private void updateCarModeUIByInputEventLongPress() {
        int carMode = drivingManager.getDriveMode();
        if (carMode == CarDriving.VCC_1_DriveMode.SPORT) return;
        drivingManager.setDrivingModeSet(CarDriving.ICC_DriveModeSet_Req.SPORT);
        handler.sendEmptyMessageDelayed(MessageConst.DRIVING_MODE_LIFE_CYCLE, 2000);
    }

    /**
     * 播报TTS
     *
     * @param ttsStr
     */
    public void speakText(String ttsStr) {
// TTS播报
        Context context = MyApplication.getContext();
        if (context == null) {
            Log.e(TAG, "Context is null, cannot play TTS");
            return;
        }

        TtsHelper.getInstance().init(context, new TtsHelper.TtsInitListener() {
            @Override
            public void onInitSuccess() {
                Log.d(TAG, "初始化成功");

                // 初始化成功后开始播报
                TtsHelper.getInstance().speak(ttsStr, new TtsHelper.TtsPlayListener() {
                    @Override
                    public void onPlayBegin() {
                        Log.d(TAG, "开始播报");
                    }

                    @Override
                    public void onPlayCompleted() {
                        Log.d(TAG, "播报完成");
                        TtsHelper.getInstance().release();
                    }

                    @Override
                    public void onPlayInterrupted() {
                        TtsHelper.getInstance().release();
                    }
                });
            }

            @Override
            public void onInitFailed(int errorCode) {
                Log.e("TTS", "初始化失败，错误码: " + errorCode);
            }
        });
    }

    public static class DrivingLifeCycleServiceHandler extends Handler {
        private static final String TAG = DrivingLifeCycleServiceHandler.class.getName();
        private final WeakReference<DrivingLifeCycle> mDrivingLifeCycleRef;

        public DrivingLifeCycleServiceHandler(DrivingLifeCycle drivingLifeCycle, Looper looper) {
            super(looper);
            mDrivingLifeCycleRef = new WeakReference<>(drivingLifeCycle);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            DrivingLifeCycle drivingLifeCycle = mDrivingLifeCycleRef.get();
            if (drivingLifeCycle == null) {
                Log.w(TAG, "DrivingLifeCycle reference is null, ignoring message");
                return;
            }

            switch (msg.what) {
                case MessageConst.DRIVING_MODE_LIFE_CYCLE:
                    drivingLifeCycle.getCarMode();
                    break;
                // 方控键切换车辆模式
                case MessageConst.DRIVING_MODE_KEY_EVENT_SERVICE:
                    if (!drivingLifeCycle.hasLongPressTriggered && drivingLifeCycle.isProcessingPress) {
                        drivingLifeCycle.hasLongPressTriggered = true;
                        drivingLifeCycle.updateCarModeUIByInputEventLongPress();
                        drivingLifeCycle.openMainActivityDriving();
                    }
                    break;
                default:
                    Log.w(TAG, "Unknown message type: " + msg.what);
                    break;
            }
        }

    }
}
