package com.bitech.vehiclesettings.service;

import android.annotation.SystemApi;
import android.car.media.CarAudioManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;

import androidx.annotation.NonNull;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

public class VolumeChangeObserver {
    private static final String VOLUME_CHANGED_ACTION = "android.media.VOLUME_CHANGED_ACTION";
    private static final String EXTRA_VOLUME_STREAM_TYPE = "android.media.EXTRA_VOLUME_STREAM_TYPE";

    public interface VolumeChangeListener {
        /**
         * 系统音量变化
         *
         * @param volume
         */
        void onVolumeChanged(int streamType, int volume);
    }

    private VolumeChangeListener mVolumeChangeListener;
    private VolumeBroadcastReceiver mVolumeBroadcastReceiver;
    private Context mContext;
    private AudioManager mAudioManager;
    private boolean mRegistered = false;
    private int lastVolume = -1;
    private Map<Integer, Integer> lastVolumeMap = new HashMap<>();

    public VolumeChangeObserver(Context context) {
        mContext = context;
        mAudioManager = (AudioManager) context.getApplicationContext()
                .getSystemService(Context.AUDIO_SERVICE);
    }

    /**
     * 获取当前媒体音量
     *
     * @return
     */
    public int getCurrentMusicVolume() {
        return mAudioManager != null ? mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC) : -1;
    }

    /**
     * 获取系统最大媒体音量
     *
     * @return
     */
    public int getMaxMusicVolume() {
        return mAudioManager != null ? mAudioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC) : 15;
    }

    public VolumeChangeListener getVolumeChangeListener() {
        return mVolumeChangeListener;
    }

    public void setVolumeChangeListener(VolumeChangeListener volumeChangeListener) {
        this.mVolumeChangeListener = volumeChangeListener;
    }

    /**
     * 注册音量广播接收器
     *
     * @return
     */
    public void registerReceiver() {
        if (mRegistered) return;

        mVolumeBroadcastReceiver = new VolumeBroadcastReceiver(this);
        IntentFilter filter = new IntentFilter();
        filter.addAction(VOLUME_CHANGED_ACTION);
        mContext.registerReceiver(mVolumeBroadcastReceiver, filter);
        mRegistered = true;
    }

    public int getCurrentStreamVolume(int streamType) {
        return mAudioManager != null ? mAudioManager.getStreamVolume(streamType) : -1;
    }

    public int getMaxStreamVolume(int streamType) {
        return mAudioManager != null ? mAudioManager.getStreamMaxVolume(streamType) : -1;
    }


    /**
     * 解注册音量广播监听器，需要与 registerReceiver 成对使用
     */
    public void unregisterReceiver() {
        if (mRegistered) {
            try {
                mContext.unregisterReceiver(mVolumeBroadcastReceiver);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                mVolumeBroadcastReceiver = null;
                mVolumeChangeListener = null;
                mRegistered = false;
                lastVolume = -1;
            }
        }
    }

    private static class VolumeBroadcastReceiver extends BroadcastReceiver {
        private WeakReference<VolumeChangeObserver> mObserverWeakReference;

        public VolumeBroadcastReceiver(VolumeChangeObserver volumeChangeObserver) {
            mObserverWeakReference = new WeakReference<>(volumeChangeObserver);
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            if (VOLUME_CHANGED_ACTION.equals(intent.getAction())) {
                VolumeChangeObserver observer = mObserverWeakReference.get();
                if (observer != null) {
                    int streamType = intent.getIntExtra(EXTRA_VOLUME_STREAM_TYPE, -1);
                    int currentVolume = observer.getCurrentStreamVolume(streamType);

                    // 检查是否变化
                    int lastVolume = observer.lastVolumeMap.getOrDefault(streamType, -1);
                    if (currentVolume < 0 || currentVolume == lastVolume) return;


                    observer.lastVolumeMap.put(streamType, currentVolume);
                    VolumeChangeListener listener = observer.getVolumeChangeListener();
                    if (listener != null) {
                        listener.onVolumeChanged(streamType, currentVolume);
                    }
                }
            }
        }
    }

}
