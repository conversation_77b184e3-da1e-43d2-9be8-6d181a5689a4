package com.bitech.vehiclesettings.presenter.recognition;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.bitech.base.utils.PreferenceSettings;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenter;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;

public class RecognitionPresenter<T> implements RecognitionPresenterListener {

    private static final String TAG = RecognitionPresenter.class.getSimpleName();
    private Context mContext;
    private T data;
//    private CameraDevice mCameraDevice = null; // 添加这行声明

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public RecognitionPresenter(Context context) {
        this.mContext = context;
    }

    @Override
    public void setSwCamera(int status) {
        /*Log.d(TAG, "setSwCamera: " + status);

        try {
            Log.d(TAG, "setSwCamera: 开启摄像头");
            CameraManager manager = (CameraManager) mContext.getSystemService(Context.CAMERA_SERVICE);
            Log.d(TAG, "setSwCamera: manager: " + manager);
            String[] cameraIds = manager.getCameraIdList();

            if (status == 1 && mCameraDevice == null) { // 避免重复打开
                Log.d(TAG, "setSwCamera: mCameraDevice == null");
                if (cameraIds.length > 0 && checkPermission()) {
                    Log.d(TAG, "setSwCamera: cameraIds.length > 0");
                    manager.openCamera(cameraIds[0], new CameraDevice.StateCallback() {
                        @Override
                        public void onOpened(@NonNull CameraDevice camera) {
                            mCameraDevice = camera; // 正确保存实例
                            saveStatus(1);
                            Log.d(TAG, "摄像头已打开 | 设备ID: " + camera.getId());
                        }

                        @Override
                        public void onDisconnected(@NonNull CameraDevice cameraDevice) {

                        }

                        @Override
                        public void onError(@NonNull CameraDevice camera, int error) {
                            Log.e(TAG, "打开失败 ERROR_CODE: " + error);
                            mCameraDevice = null; // 失败时重置
                        }
                    }, null);
                }
            } else if (status == 0 && mCameraDevice != null) { // 避免重复关闭
                Log.d(TAG, "setSwCamera: 关闭摄像头 mCameraDevice != null");
                mCameraDevice.close();
                mCameraDevice = null; // 显式释放
                saveStatus(0);
                Log.d(TAG, "摄像头已关闭");
            }
        } catch (Exception e) {
            Log.e(TAG, "操作异常", e);
            mCameraDevice = null; // 异常时强制重置
        }*/
        // TODO DMS 摄像头开关
        Log.d(TAG, "setSwhCamera: 开关DMS摄像头:" + status);
        Prefs.put(PrefsConst.R_DMS_CAMERA_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_DMS_CAMERA_STATUS, status);
    }

    @Override
    public int getSwCamera() {
        // TODO DMS 摄像头开关
        int status = Prefs.getGlobalValue( PrefsConst.GlobalValue.R_DMS_CAMERA_STATUS, PrefsConst.DefaultValue.R_DMS_CAMERA_STATUS);

        Log.d(TAG, "getSwhCamera: 获取DMS摄像头:" + status);
        return status;
//        return 0;
    }
    private boolean checkPermission() {
        return ContextCompat.checkSelfPermission(mContext,
                Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
    }

    private void saveStatus(int status) {
        Prefs.put(PrefsConst.R_DMS_CAMERA_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_DMS_CAMERA_STATUS, status);
    }

    @Override
    public void setSwFatigue(int status) {
        // TODO 疲劳检测开关
        Log.d(TAG, "setSwFatigue: 开关疲劳检测:" + status);
        Prefs.put(PrefsConst.R_FATIGUE_DETECTION_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_FATIGUE_DETECTION_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwFatigue() {
        // TODO 疲劳检测开关
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.R_FATIGUE_DETECTION_STATUS, PrefsConst.DefaultValue.R_FATIGUE_DETECTION_STATUS);
        Log.d(TAG, "getSwFatigue: 获取疲劳检测:" + status);
        return status;
    }

    @Override
    public void setSwDistraction(int status) {
        // TODO 视线分心提醒开关
        Log.d(TAG, "setSwDistraction: 开关视线分心提醒:" + status);
        Prefs.put(PrefsConst.R_DISTRACTION_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_DISTRACTION_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwDistraction() {
        // TODO 视线分心提醒开关
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.R_DISTRACTION_STATUS, PrefsConst.DefaultValue.R_DISTRACTION_STATUS);
        Log.d(TAG, "getSwDistraction: 获取视线分心提醒:" + status);
        return status;
    }

    @Override
    public void setSwCall(int status) {
        // TODO 打电话提醒开关
        Log.d(TAG, "setSwCall: 开关打电话提醒:" + status);
        Prefs.put(PrefsConst.R_CALL_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_CALL_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwCall() {
        // TODO 打电话提醒开关
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.R_CALL_STATUS, PrefsConst.DefaultValue.R_CALL_STATUS);
        Log.d(TAG, "getSwhCamera: 获取打电话提醒:" + status);
        return status;
    }

    @Override
    public void setSwDrink(int status) {
        // TODO 喝水提醒开关
        Log.d(TAG, "setSwDrink: 开关喝水提醒:" + status);
        Prefs.put(PrefsConst.R_DRINK_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_DRINK_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwDrink() {
        // TODO 喝水提醒开关
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.R_DRINK_STATUS, PrefsConst.DefaultValue.R_DRINK_STATUS);
        Log.d(TAG, "getSwDrink: 获取喝水提醒:" + status);
        return status;
    }

    @Override
    public void setSwSeatHeat(int status) {
        // TODO 座椅加热开关
        Log.d(TAG, "setSwSeatHeat: 开关座椅加热:" + status);
        Prefs.put(PrefsConst.R_SEAT_HEAT_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_SEAT_HEAT_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwSeatHeat() {
        // TODO 座椅加热开关
        int status = Prefs.getGlobalValue( PrefsConst.GlobalValue.R_SEAT_HEAT_STATUS, PrefsConst.DefaultValue.R_SEAT_HEAT_STATUS);
        Log.d(TAG, "getSwSeatHeat: 获取座椅加热:" + status);
        return status;
    }

    @Override
    public void setSwSeatVentilation(int status) {
        // TODO 座椅通风开关
        Log.d(TAG, "setSwSeatVentilation: 开关座椅通风:" + status);
        Prefs.put(PrefsConst.R_SEAT_VENTILATION_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_SEAT_VENTILATION_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwSeatVentilation() {
        // TODO 座椅通风开关
        int status = Prefs.getGlobalValue( PrefsConst.GlobalValue.R_SEAT_VENTILATION_STATUS, PrefsConst.DefaultValue.R_SEAT_VENTILATION_STATUS);
        Log.d(TAG, "getSwSeatVentilation: 获取座椅通风:" + status);
        return status;
    }

    @Override
    public void setSwSightUnlock(int status) {
        // TODO 视线解锁屏保开关
        Log.d(TAG, "setSwSightUnlock: 开关视线解锁屏保:" + status);
        Prefs.put(PrefsConst.R_SIGHT_UNLOCK_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_SIGHT_UNLOCK_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwSightUnlock() {
        // TODO 视线解锁屏保开关
        int status = Prefs.getGlobalValue( PrefsConst.GlobalValue.R_SIGHT_UNLOCK_STATUS, PrefsConst.DefaultValue.R_SIGHT_UNLOCK_STATUS);
        Log.d(TAG, "getSwSightUnlock: 获取视线解锁屏保:" + status);
        return status;
    }

    @Override
    public void setSwGreet(int status) {
        // TODO 个性化问候开关
        Log.d(TAG, "setSwGreet: 开关个性化问候:" + status);
        Prefs.put(PrefsConst.R_GREET_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_GREET_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwGreet() {
        // TODO 个性化问候开关
        int status = Prefs.getGlobalValue( PrefsConst.GlobalValue.R_GREET_STATUS, PrefsConst.DefaultValue.R_GREET_STATUS);
        Log.d(TAG, "getSwGreet: 获取个性化问候:" + status);
        return status;
    }

    @Override
    public void setSwSmoke(int status) {
        // TODO 抽烟关怀开关
        Log.d(TAG, "setSwSmoke: 开关抽烟关怀:" + status);
        Prefs.put(PrefsConst.R_SMOKE_STATUS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.R_SMOKE_STATUS, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getSwSmoke() {
        // TODO 抽烟关怀开关
        int status = Prefs.getGlobalValue( PrefsConst.GlobalValue.R_SMOKE_STATUS, PrefsConst.DefaultValue.R_SMOKE_STATUS);
        Log.d(TAG, "getSwSmoke: 获取抽烟关怀:" + status);
        return status;
    }

    /**
     * 根据配置字来获得车型
     *
     * @return
     */
    public int getVisualService() {
        // TODO 获得视觉服务
/*        int status = Prefs.getGlobalValue( PrefsConst.GlobalValue.R_VISUAL_SERVICE);
        Log.d(TAG, "getVisualService: 获取视觉服务:" + status);
        return status;*/
        return 1;
    }

    public void setVisualService(int state) {
        // TODO 设置视觉服务
/*        Prefs.put(PrefsConst.R_VISUAL_SERVICE, state);
        Prefs.setGlobalValue(mContext, PrefsConst.GlobalValue.R_VISUAL_SERVICE, state);
        Log.d(TAG, "setVisualService: 获取视觉服务:" + state);*/
    }
}
