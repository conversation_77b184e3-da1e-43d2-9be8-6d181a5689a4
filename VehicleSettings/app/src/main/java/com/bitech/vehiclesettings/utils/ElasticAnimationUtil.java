package com.bitech.vehiclesettings.utils;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.os.Handler;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;

import androidx.dynamicanimation.animation.SpringAnimation;
import androidx.dynamicanimation.animation.SpringForce;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.WeakHashMap;

public class ElasticAnimationUtil {
    /**
     * 动画参数
     */
    // 动效缩放幅度
    private static final float PRESSED_SCALE = 0.9f;
    // 动效持续时间
    private static final int DEFAULT_DURATION = 200;
    // 动效触发延时
    private static final int PRESS_DELAY_MS = 0;

    // 动画跟踪系统
    private static final Map<View, List<Object>> activeAnimations = new WeakHashMap<>();
    // 延时任务跟踪系统
    private static final Map<View, Runnable> pendingPressRunnables = new WeakHashMap<>();
    private static final Handler handler = new Handler(Looper.getMainLooper());
    // 组件状态跟踪
    private static final Map<View, Boolean> viewPressedStates = new WeakHashMap<>();

    /**
     * 取消指定View的所有动画和延时任务
     */
    public static void cancelAnimations(View view) {
        if (view == null) return;

        cancelPendingPress(view);

        List<Object> animations = activeAnimations.get(view);
        if (animations != null) {
            for (Object anim : new ArrayList<>(animations)) {
                if (anim instanceof Animator) {
                    ((Animator) anim).end(); // 改为end()确保完成动画
                } else if (anim instanceof SpringAnimation) {
                    ((SpringAnimation) anim).cancel();
                }
            }
            animations.clear();
        }
        view.setScaleX(1f);
        view.setScaleY(1f);
        viewPressedStates.put(view, false); // 重置状态
    }

    /**
     * 为View添加弹性触摸效果
     */
    @SuppressLint("ClickableViewAccessibility")
    public static void applyElasticTouchEffect(View view) {
        if (view == null) return;

        view.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    // 如果当前已经是按下状态，先强制恢复
                    if (Boolean.TRUE.equals(viewPressedStates.get(v))) {
                        cancelAnimations(v);
                    }

                    cancelAnimations(v);
                    viewPressedStates.put(v, true);

                    Runnable pressRunnable = () -> {
                        if (Boolean.TRUE.equals(viewPressedStates.get(v))) {
                            startPressAnimation(v);
                        }
                    };
                    pendingPressRunnables.put(v, pressRunnable);
                    handler.postDelayed(pressRunnable, PRESS_DELAY_MS);
                    break;
                case MotionEvent.ACTION_UP:
                    viewPressedStates.put(v, false);
                    cancelPendingPress(v);
                    startReleaseAnimation(v);
                    break;
                case MotionEvent.ACTION_CANCEL:
                    viewPressedStates.put(v, false);
                    cancelPendingPress(v);
                    startReleaseAnimation(v);
                    break;
            }
            return false;
        });


    }

    // 修改释放动画逻辑
    private static void startReleaseAnimation(View view) {
        // 如果已经处于非按下状态才执行恢复
        if (!Boolean.TRUE.equals(viewPressedStates.get(view))) {
            SpringAnimation scaleX = new SpringAnimation(view, SpringAnimation.SCALE_X, 1f);
            SpringAnimation scaleY = new SpringAnimation(view, SpringAnimation.SCALE_Y, 1f);

            SpringForce spring = new SpringForce(1f)
                    .setStiffness(SpringForce.STIFFNESS_MEDIUM)
                    .setDampingRatio(SpringForce.DAMPING_RATIO_NO_BOUNCY);

            scaleX.setSpring(spring);
            scaleY.setSpring(spring);

            // 添加动画状态验证
            scaleX.addUpdateListener((animation, value, velocity) -> {
                if (!Boolean.TRUE.equals(viewPressedStates.get(view))) {
                    view.setScaleX(value);
                }
            });

            scaleY.addUpdateListener((animation, value, velocity) -> {
                if (!Boolean.TRUE.equals(viewPressedStates.get(view))) {
                    view.setScaleY(value);
                }
            });

            scaleX.addEndListener((animation, canceled, value, velocity) -> {
                removeAnimation(view, scaleX);
                if (!canceled) {
                    view.setScaleX(1f);
                }
            });

            scaleY.addEndListener((animation, canceled, value, velocity) -> {
                removeAnimation(view, scaleY);
                if (!canceled) {
                    view.setScaleY(1f);
                }
            });

            trackAnimation(view, scaleX);
            trackAnimation(view, scaleY);
            scaleX.start();
            scaleY.start();
        }
    }

    /**
     * 取消指定View的延时按下任务
     */
    private static void cancelPendingPress(View view) {
        Runnable pendingRunnable = pendingPressRunnables.get(view);
        if (pendingRunnable != null) {
            handler.removeCallbacks(pendingRunnable);
            pendingPressRunnables.remove(view);
        }
    }

    /**
     * 联动效果（点击左侧触发右侧动画）
     */
    public static void applyLinkedElasticEffect(View leftView, View rightView) {
        applyLinkedElasticEffect(leftView, rightView, true);
    }

    @SuppressLint("ClickableViewAccessibility")
    public static void applyLinkedElasticEffect(View leftView, View rightView, boolean animateBoth) {
        if (leftView == null || rightView == null) return;

        leftView.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    if (animateBoth) cancelAnimations(leftView);
                    cancelAnimations(rightView);

                    // 设置延时任务
                    Runnable pressRunnable = () -> {
                        if (animateBoth) startPressAnimation(leftView);
                        startPressAnimation(rightView);
                    };
                    pendingPressRunnables.put(v, pressRunnable);
                    handler.postDelayed(pressRunnable, PRESS_DELAY_MS);
                    break;

                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    cancelPendingPress(v);
                    if (animateBoth) reversePressAnimation(leftView);
                    reversePressAnimation(rightView);
                    break;
            }
            return false;
        });
    }

    /**
     * 缩小动效
     *
     * @param view
     */
    private static void startPressAnimation(View view) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", PRESSED_SCALE);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", PRESSED_SCALE);

        AnimatorSet set = new AnimatorSet();
        set.playTogether(scaleX, scaleY);

        set.setDuration(DEFAULT_DURATION / 2);
        set.addListener(new AnimatorTracker(view, set));
        trackAnimation(view, set);
        set.start();
    }

    /**
     * 恢复动效
     *
     * @param view
     */
    private static void reversePressAnimation(View view) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", PRESSED_SCALE, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", PRESSED_SCALE, 1f);
        AnimatorSet set = new AnimatorSet();
        set.playTogether(scaleX, scaleY);
        set.setDuration(DEFAULT_DURATION / 2);
        set.addListener(new AnimatorTracker(view, set));
        trackAnimation(view, set);
        set.start();
    }

    private static void trackAnimation(View view, Object animation) {
        List<Object> animations = activeAnimations.computeIfAbsent(view, k -> new ArrayList<>());
        animations.add(animation);
    }

    private static void removeAnimation(View view, Object animation) {
        List<Object> animations = activeAnimations.get(view);
        if (animations != null) {
            animations.remove(animation);
        }
    }

    private static class AnimatorTracker extends AnimatorListenerAdapter {
        private final View mView;
        private final Object mAnimation;

        AnimatorTracker(View view, Object animation) {
            this.mView = view;
            this.mAnimation = animation;
        }

        @Override
        public void onAnimationEnd(Animator animation) {
            removeAnimation(mView, mAnimation);
        }
    }
}