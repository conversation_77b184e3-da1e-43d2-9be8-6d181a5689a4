package com.bitech.vehiclesettings.fragment;

import static com.bitech.vehiclesettings.service.DataPointReportLifeCycle.KEY_DATA_POINT;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.bean.report.Content;
import com.bitech.vehiclesettings.bean.report.DataPoint;
import com.bitech.vehiclesettings.databinding.FragmentLightBaseBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.GlideBackgroundUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;
import com.bumptech.glide.Glide;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.lion.datapoint.log.LogDataUtil;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 灯光
 *
 * <AUTHOR>
 */
public class LightBaseFragment extends BaseFragment<FragmentLightBaseBinding> implements View.OnClickListener {
    private static final String TAG = LightBaseFragment.class.getSimpleName();
    private WeakReference<LightInFragment> lightInFragmentRef;
    private WeakReference<LightOutFragment> lightOutFragmentRef;

    private WeakReference<MainActivity> activityRef;
    private int inOrOut = Prefs.get(PrefsConst.SELECT_TAB, 0);
    private MainActViewModel mainActViewModel;
    public static final int LIGHT_SETECT_IN = 10;
    public static final int LIGHT_SETECT_OUT = 11;

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null || binding.rlLightTab == null) {
            // binding未初始化或视图已销毁，直接返回或打印日志
            Log.w(TAG, "loadPageAnim called but binding or scrollView is null");
            return;
        }
        loadPageAnim(binding.rlLightTab, currentPosition, position);
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        activityRef = new WeakReference<>((MainActivity) context);
    }

    /**
     * @param inflater
     * @param container
     * @return
     */
    @Override
    protected FragmentLightBaseBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentLightBaseBinding.inflate(getLayoutInflater());
        return binding;
    }

    /**
     *
     */
    @Override
    protected void initView() {
        ViewPagerFragment adapter = new ViewPagerFragment(getChildFragmentManager());
        binding.pager.setOffscreenPageLimit(1);
        binding.pager.setAdapter(adapter);
        inOrOut = Prefs.get(PrefsConst.SELECT_TAB, 0);

        selectTab(inOrOut);
        BindingUtil.bindClicks(this, binding.tvLightIn, binding.ivLightIn, binding.ivLightOut, binding.tvLightOut);
    }

    /**
     *
     */
    @Override
    protected void setListener() {
        binding.pager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                Log.d(TAG, "onPageScrolled: " + position + ",positionOffset:" + positionOffset + ",positionOffsetPixels:" + positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                if (position == 0) {
                    if (lightInFragmentRef == null || (lightInFragmentRef.get() == null)) {
                        lightInFragmentRef = new WeakReference<>(new LightInFragment());
                    }
                }
                if (position == 1) {
                    if (lightOutFragmentRef == null || (lightOutFragmentRef.get() == null)) {
                        lightOutFragmentRef = new WeakReference<>(new LightOutFragment());
                    }
                }
                Prefs.put(PrefsConst.SELECT_TAB, position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    @Override
    protected void initObserve() {
        //MainActivity的ViewModel
        mainActViewModel = new ViewModelProvider(requireActivity()).get(MainActViewModel.class);
        processTargetDialogEvent(mainActViewModel.getTargetDialogLiveEvent().getValue());
        mainActViewModel.getTargetDialogLiveEvent().observe(getViewLifecycleOwner(), this::processTargetDialogEvent);
    }

    @Override
    protected void initData() {

    }

    /**
     * @param v The view that was clicked.
     */
    @Override
    public void onClick(View v) {
//        Log.d(TAG, "onClick: " + v.getId());
        switch (v.getId()) {
            case R.id.tv_light_in:
            case R.id.iv_light_in:
                selectTab(0);
                break;
            case R.id.tv_light_out:
            case R.id.iv_light_out:
                selectTab(1);
                break;

        }
    }

    public void selectTab(int position) {
        // 判断是否位置
        Prefs.put(PrefsConst.SELECT_TAB, position);
        //EventBus.getDefault().post(CommonConst.ACTION_UI_UPDATE);
        synToMainLightBackPic(position);
        if (position == 0) {

            binding.ivLightIn.setVisibility(View.VISIBLE);
            binding.ivLightOut.setVisibility(View.INVISIBLE);
            binding.pager.setCurrentItem(0, false);

        }
        if (position == 1) {
            binding.ivLightIn.setVisibility(View.INVISIBLE);
            binding.ivLightOut.setVisibility(View.VISIBLE);
            binding.pager.setCurrentItem(1, false);
        }
    }

    private void synToMainLightBackPic(int tab) {
        MainActivity activity = (MainActivity) getActivity();
        int theme = Prefs.get(PrefsConst.SELECT_THEME, CommonConst.TAB_0);
        int front = Prefs.get(PrefsConst.SELECT_FRONT_REAR, CommonConst.TAB_0);
        if (activity != null) {
            if (activity.getBinding().tvLight.isSelected()) {
                if (tab == CommonConst.TAB_0) {
                    activity.getBinding().clBg.setBackgroundResource(R.color.main_bg_color);
                    if (theme == CommonConst.TAB_0) {
                        GlideBackgroundUtils.setBackgroundSafely(Glide.with(this), activity.getBinding().ivModel, R.mipmap.ic_light_lighting_inside);
                    } else {
                        if (front == CommonConst.TAB_0) {
                            GlideBackgroundUtils.setBackgroundSafely(Glide.with(this), activity.getBinding().ivModel, R.mipmap.ic_light_lighting_inside);
                        } else {
                            GlideBackgroundUtils.setBackgroundSafely(Glide.with(this), activity.getBinding().ivModel, R.mipmap.ic_light_car_back);
                        }
                    }

                    activity.getBinding().ivModel.setVisibility(View.VISIBLE);
                    activity.getBinding().ivModel.handleScroll(0);
                } else {
                    activity.getBinding().clBg.setBackgroundResource(R.color.transparent);
                    activity.getBinding().ivModel.setVisibility(View.INVISIBLE);
                    activity.getBinding().ivModel.setBackgroundResource(R.mipmap.ic_light_lighting_outside);
                    activity.getBinding().ivModel.handleScroll(Prefs.get(PrefsConst.LIGHT_SCROLL_Y, 0));

                }
            }

        }
    }

    private class ViewPagerFragment extends FragmentPagerAdapter {

        public ViewPagerFragment(FragmentManager childFragmentManager) {
            super(childFragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        }


        /**
         * @param position
         * @return
         */
        @NonNull
        @Override
        public Fragment getItem(int position) {
            switch (position) {
                case 1:
                    if (lightOutFragmentRef == null || lightOutFragmentRef.get() == null) {
                        lightOutFragmentRef = new WeakReference<>(new LightOutFragment());
                    }
                    return lightOutFragmentRef.get();
                default:
                    if (lightInFragmentRef == null || lightInFragmentRef.get() == null) {
                        lightInFragmentRef = new WeakReference<>(new LightInFragment());
                    }
                    return lightInFragmentRef.get();
            }
        }

        /**
         * @return
         */
        @Override
        public int getCount() {
            return 2;
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: ");
        // 埋点上报数据
        DataPoint dataPoint = new DataPoint();
        dataPoint.setId(CommonConst.DataPoint.id);
        dataPoint.setEventId(CommonConst.EventId.Lighting_Set);
        dataPoint.setTimestamp(System.currentTimeMillis());
        dataPoint.setSupplierCode(CommonConst.DataPoint.supplierCode);
        dataPoint.setPlatformCode(CommonConst.DataPoint.platformCode);
        dataPoint.setNodeType(LogDataUtil.NODE_TYPE_ADD_OPERATION);
        // 上报数据氛围灯/车外灯光
        ArrayList<Content> list = new ArrayList<>();
        ReentrantLock lock = new ReentrantLock();
        if (lightInFragmentRef != null && (lightInFragmentRef.get() != null)) {
            ArrayList<Content> insist = lightInFragmentRef.get().getData();
            if (insist != null) {
                try {
                    lock.lock();
                    list.addAll(insist);
                } finally {
                    lock.unlock();
                }

            }
        }
        if (lightOutFragmentRef != null && (lightOutFragmentRef.get() != null)) {
            ArrayList<Content> outlast = lightOutFragmentRef.get().getData();
            if (outlast != null) {
                try {
                    lock.lock();
                    list.addAll(outlast);
                } finally {
                    lock.unlock();
                }
            }

        }
        if (!list.isEmpty()) {
            dataPoint.setContent(list);
        }

        LiveEventBus
                .get(KEY_DATA_POINT)
                .post(dataPoint);
    }

    private void processTargetDialogEvent(TargetDialogInfo targetDialog) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= " + targetDialog);
        if (targetDialog == null) {
            return;
        }
        //具体Tab索引
        if (targetDialog.getTargetTab() == MainActivity.MainTabIndex.LIGHT) {
            switch (targetDialog.getTargetDialog()) {
                case LIGHT_SETECT_IN:
                    if (targetDialog.getOperation() == 1) {
                        selectTab(0);
                        safeAccessActivity();

                    } else {
                        Prefs.put(PrefsConst.SELECT_TAB, -1);
                    }
                    break;
                case LIGHT_SETECT_OUT:
                    if (targetDialog.getOperation() == 1) {
                        selectTab(1);
                        safeAccessActivity();
                    } else {
                        Prefs.put(PrefsConst.SELECT_TAB, -1);
                    }
                    break;
            }
        }
    }

    private void safeAccessActivity() {
        MainActivity activity = activityRef.get();
        if (activity != null && isAdded()) {
            activity.lightBackPic();
        }
    }

}
