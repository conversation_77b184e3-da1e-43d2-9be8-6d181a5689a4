package com.bitech.vehiclesettings.view.newenergy

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.utils.LogUtil

abstract class BaseDialog(context: Context) : Dialog(context, R.style.dialog) {

    companion object {
        private const val TAG = "BaseDialog"
    }

//    init {
//        if (context is AppCompatActivity) {
//            context.lifecycle.addObserver(object : androidx.lifecycle.DefaultLifecycleObserver {
//                override fun onStop(owner: LifecycleOwner) {
//                    super.onStop(owner)
//                    LogUtil.d(getChildSimpleName(), "Activity onStop")
//                    if (isShowing && autoDismiss()) {
//                        dismiss()
//                    }
//                }
//            })
//        }
//    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate: ${getChildSimpleName()}")
    }

    override fun onStart() {
        super.onStart()
        LogUtil.d(TAG, "onStart: ${getChildSimpleName()}")
    }

    override fun onStop() {
        super.onStop()
        LogUtil.d(TAG, "onStop: ${getChildSimpleName()}")
    }

    open fun autoDismiss(): Boolean {
        return true
    }

    open fun getChildSimpleName(): String {
        return javaClass.simpleName
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        LogUtil.d(getChildSimpleName(), "onWindowFocusChanged : hasFocus = $hasFocus")
        if (!hasFocus && autoDismiss()) {
            dismiss()
        }
    }

}