package com.bitech.vehiclesettings.utils

import android.net.MacAddress
import com.chery.ivi.vdb.event.id.phonelink.VDValuePhoneLink

data class PhoneLink(
    var status: Int = -1,
    var type: Int = -1,
    var typeName: String = "",
    var isWireless: Boolean = false,
    var macAddress: MacAddress = MacAddress.fromString("00:00:00:00:00:00")
) {
    companion object {
        const val TYPE_CARLINK: Int = VDValuePhoneLink.DeviceType.CARLINK
        const val TYPE_CARPLAY: Int = VDValuePhoneLink.DeviceType.CARPLAY
        const val TYPE_HICAR: Int = VDValuePhoneLink.DeviceType.HICAR
        const val TYPE_CARLINK_NAME = "CarLink"
        const val TYPE_CARPLAY_NAME = "CarPlay"
        const val TYPE_HICAR_NAME = "HiCar"
        const val TYPE_SERVERID_CARPLAY = VDValuePhoneLink.ServerId.CARPLAY
        const val TYPE_SERVERID_CARLINK = VDValuePhoneLink.ServerId.CARLINK
        const val TYPE_SERVERID_HICAR = VDValuePhoneLink.ServerId.HICAR
    }
}