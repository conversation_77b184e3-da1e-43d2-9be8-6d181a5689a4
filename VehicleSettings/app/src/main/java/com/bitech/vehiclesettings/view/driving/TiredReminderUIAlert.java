package com.bitech.vehiclesettings.view.driving;

import android.app.Dialog;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.net.Uri;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDMSConstant;
import com.bitech.vehiclesettings.databinding.DialogAlertCTiredReminderBinding;
import com.bitech.vehiclesettings.presenter.driving.DrivingAnime;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.view.recognition.PrivacyPolicyUIAlert;

public class TiredReminderUIAlert extends BaseDialog {
    private static final String TAG = TiredReminderUIAlert.class.getSimpleName();
    private static TiredReminderUIAlert.onProgressChangedListener onProgressChangedListener;
    private DrivingAnime drivingAnime;


    public TiredReminderUIAlert(@NonNull Context context) {
        super(context);
    }

    public TiredReminderUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected TiredReminderUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static TiredReminderUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(TiredReminderUIAlert.onProgressChangedListener onProgressChangedListener) {
        TiredReminderUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private TiredReminderUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertCTiredReminderBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private TiredReminderUIAlert dialog = null;
        private View layout;
        ContentResolver cr;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder(Context context, ContentResolver cr) {
            this.context = context;
            this.cr = cr;
        }


        public TiredReminderUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public TiredReminderUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new TiredReminderUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertCTiredReminderBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1584;
            layoutParams.height = 918;
            window.setAttributes(layoutParams);

            // 初始化操作
            operateClickable(binding.swDriverState.isChecked());
            // 初始化开关
            initSwitch();
            // 初始化提示
            initTips();
            // 设置文字link效果
            clickTextLink();
            return dialog;
        }

        private void initTips() {
            binding.ivFatigueDetectionTips.setOnClickListener(v -> {
                openTipsDialog(context.getString(R.string.str_recognition_fatigue_detection), context.getString(R.string.str_recognition_tips_content), 1128, 378);
            });
            binding.ivDistractionTips.setOnClickListener(v -> {
                openTipsDialog(context.getString(R.string.str_recognition_eye_distraction_alerts), context.getString(R.string.str_recognition_tips_content_1), 1128, 278);
            });
            binding.ivCallTips.setOnClickListener(v -> {
                openTipsDialog(context.getString(R.string.str_recognition_safe_3), context.getString(R.string.str_recognition_tips_content_2), 1128, 278);
            });
        }

        private DetailsUIAlert.Builder detailUIAlert;

        // 打开自定义提示窗口
        private void openTipsDialog(String title, String content, int width, int height) {
            if (detailUIAlert != null && detailUIAlert.isShowing()) {
                return;
            }
            if (detailUIAlert == null) {
                detailUIAlert = new DetailsUIAlert.Builder(context);
            }
            // 注册子弹窗
            if (dialog != null) {
                dialog.registerChildDialog();
            }
            Dialog subDialog = detailUIAlert.create(title, content, width, height);
            subDialog.show();
            detailUIAlert.setTextSize(com.bitech.base.R.dimen.font_24px);
            detailUIAlert.setTitleSize(com.bitech.base.R.dimen.font_24px);

            // 设置关闭监听器以注销子弹窗
            subDialog.setOnDismissListener(dialog -> {
                if (this.dialog != null) {
                    this.dialog.unregisterChildDialog();
                }
            });
        }

        private PrivacyPolicyUIAlert.Builder privacyPolicyUIAlert;

        // 设置文字链接点击效果
        private void clickTextLink() {
            binding.tvAgreeLink.setOnClickListener(v -> {
                openTipsDialog(context.getString(R.string.str_recognition_privacy), context.getString(R.string.str_recognition_privacy_1), 1584, 1000);
                detailUIAlert.setGravity(Gravity.LEFT);
                detailUIAlert.setTextSize(com.bitech.base.R.dimen.font_24px);
                detailUIAlert.setPadding(120);
                detailUIAlert.setScrollable(true);
            });
        }

        private void initSwitch() {
            int dmsInitStatus = Prefs.getInt(PrefsConst.D_DMS_INIT_STATUS);
            if (dmsInitStatus == 0) {
                EToast.showToast(context, context.getString(R.string.str_dms_init_failed), 2000, false);
                GrayEffectUtils.applyGrayEffect(binding.swDriverState);
            } else {
                GrayEffectUtils.removeGrayEffect(binding.swDriverState);
            }
            binding.swDriverState.setChecked(onProgressChangedListener.initSwitch(0) == 1);
            binding.swDriverState.setOnClickListener(buttonView -> {
                onProgressChangedListener.setDMSStatus(binding.swDriverState.isChecked() ? 0 : 1);
                if (!binding.swDriverState.isChecked()) {
                    if (dialog != null) {
                        dialog.registerChildDialog();
                    }
                    onProgressChangedListener.openCameraOpenDialog(dialog);
                } else {
                    if (dialog != null) {
                        dialog.registerChildDialog();
                    }
                    onProgressChangedListener.openCameraCloseDialog(dialog);
                }
//                dialog.setShouldAutoDismiss(true);
                Log.d(TAG, "fatigueDetectionSwitch: " + binding.swDriverState.isChecked());
                operateClickable(binding.swDriverState.isChecked());
            });
            operateClickable(binding.swDriverState.isChecked());

            binding.swFatigueDetection.setChecked(onProgressChangedListener.initSwitch(1) == 1);
            binding.swFatigueDetection.setOnCheckedChangeListener((buttonView, isChecked) -> {
                setDMSStatus(CarDMSConstant.KEY_SAFETY_FATIGUE_STATUS, isChecked ? 1 : 0);
            });
            binding.swDistraction.setChecked(onProgressChangedListener.initSwitch(2) == 1);
            binding.swDistraction.setOnCheckedChangeListener((buttonView, isChecked) -> {
                setDMSStatus(CarDMSConstant.KEY_SAFETY_DISTRACTION_DRIVER_STATUS, isChecked ? 1 : 0);
            });
            binding.swCall.setChecked(onProgressChangedListener.initSwitch(3) == 1);
            binding.swCall.setOnCheckedChangeListener((buttonView, isChecked) -> {
                setDMSStatus(CarDMSConstant.KEY_SAFETY_DISTRACTION_CALLS_STATUS, isChecked ? 1 : 0);
            });
        }

        public void setDMSStatus(String name, int status) {
            Uri uri = Uri.parse("content://com.lion.smartscenes/settings/" + name + "/" + status);
            cr.update(uri, new ContentValues(), null, null);
        }

        public void operateClickable(boolean state) {
            if (state) {
                GrayEffectUtils.removeGrayEffect(binding.llFatigueDetection);
                GrayEffectUtils.removeGrayEffect(binding.llDistraction);
                GrayEffectUtils.removeGrayEffect(binding.llCallReminder);
            } else {
                GrayEffectUtils.applyGrayEffect(binding.llFatigueDetection);
                GrayEffectUtils.applyGrayEffect(binding.llDistraction);
                GrayEffectUtils.applyGrayEffect(binding.llCallReminder);
            }
        }

        public DialogAlertCTiredReminderBinding getBinding() {
            return binding;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public void updateFatigueUI(long status) {
            binding.swFatigueDetection.setChecked(status == 1);
        }

        public void updateDistractionDriverUI(long status) {
            binding.swDistraction.setChecked(status == 1);
        }

        public void updateDistractionCallsUI(long status) {
            binding.swCall.setChecked(status == 1);
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void openCameraOpenDialog(TiredReminderUIAlert dialog);

        void openCameraCloseDialog(TiredReminderUIAlert dialog);

        long initSwitch(int index);
        void setDMSStatus(int status);
    }
}
