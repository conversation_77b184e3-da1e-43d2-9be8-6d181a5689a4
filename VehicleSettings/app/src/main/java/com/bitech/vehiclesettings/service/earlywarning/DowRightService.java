package com.bitech.vehiclesettings.service.earlywarning;

import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;


/**
 * DOW 提示
 */
public class DowRightService extends DowBaseService {
    private static final String TAG = DowRightService.class.getSimpleName();
    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
    private static volatile DowRightService instance;
    private static final long TARGET_INTERVAL_MS = 250;
    private Handler mHandler;
    private Runnable mTaskRunnable;
    private volatile boolean isRunning = false;
    private long mLastExecutionTime;
    int count = 0;

    public static DowRightService getInstance() {
        if (instance == null) {
            synchronized (DowRightService.class) {
                if (instance == null) {
                    instance = new DowRightService();
                }
            }
        }
        return instance;
    }

    public DowRightService() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public void start(int type, int sign) {
        if (isRunning) {
            return;
        }
        isRunning = true;
        mLastExecutionTime = SystemClock.elapsedRealtime();
        mTaskRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isRunning) {
                    return;
                }
                // 1. 执行任务
                Log.d(TAG, "[warn] DOW提示right start ");
                doTask(type, sign);
                // 2. 计算下一次执行时间（动态补偿误差）
                long currentTime = SystemClock.elapsedRealtime();
                long elapsed = currentTime - mLastExecutionTime;
                long nextDelay = Math.max(0, TARGET_INTERVAL_MS - elapsed);
                // 3. 递归调度下一次任务
                if (count == 1) {
                    mHandler.postDelayed(this, nextDelay - 200);
                } else {
                    mHandler.postDelayed(this, nextDelay);
                }
                mLastExecutionTime = currentTime + nextDelay;
            }
        };
        mHandler.post(mTaskRunnable);
    }

    public void stop() {
        if (mTaskRunnable != null) {
            Log.d(TAG, "[warn] DOW提示right stop ");
            mHandler.removeCallbacks(mTaskRunnable);
        }
        isRunning = false;
    }


}
