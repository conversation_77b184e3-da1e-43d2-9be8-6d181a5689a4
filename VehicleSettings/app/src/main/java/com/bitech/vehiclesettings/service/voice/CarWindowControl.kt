package com.bitech.vehiclesettings.service.voice

import android.util.Log
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.interfaces.quick.handler.QuickManagerHandler
import com.bitech.platformlib.manager.NewEnergyManager
import com.bitech.platformlib.manager.QuickManager
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P


class CarWindowControl {
    private val quickManager: QuickManager =
        BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER) as QuickManager
    var newEnergyManager: NewEnergyManager =
        BitechCar.getInstance().getServiceManager(BitechCar.CAR_ENERGY_MANAGER) as NewEnergyManager
    private var manager: QuickManagerHandler? = QuickManagerHandler()
    //单回复提示语id
    private fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    private fun getFLWindow(): Int {
        return quickManager.flWindow
    }

    private fun getFRWindow(): Int {
        return quickManager.frWindow
    }
    private fun getRLWindow(): Int {
        return quickManager.rlWindow
    }

    private fun getRRWindow(): Int {
        return quickManager.rrWindow
    }
    private fun setFLWindow(signal: Int) {
        var state = signal
        if (signal < 0){
            state = 0
        }else if (signal > 100){
            state = 100
        }
        quickManager.flWindow = state
    }

    private fun setFRWindow(signal: Int) {
        var state = signal
        if (signal < 0){
            state = 0
        }else if (signal > 100){
            state = 100
        }
        quickManager.frWindow = state
    }

    private fun setRLWindow(signal: Int) {
        var state = signal
        if (signal < 0){
            state = 0
        }else if (signal > 100){
            state = 100
        }
        quickManager.rlWindow = state
    }

    private fun setRRWindow(signal: Int) {
        var state = signal
        if (signal < 0){
            state = 0
        }else if (signal > 100){
            state = 100
        }
        quickManager.rrWindow = state
    }

    /**
     * 车窗锁
     * @param flag true:打开，false:关闭
     */
    fun setWindowLock(flag: Boolean) {

        //0为解除，1为启用
        if (flag) {
            if (manager?.windowLock == 0) {
                manager!!.setWindowLock(1)
                sendResultCode("Lock_the_car_window_2")
            } else {
                sendResultCode("Lock_the_car_window_1")
            }
        } else {
            if (manager?.windowLock == 1) {
                manager!!.setWindowLock(0)
                sendResultCode("Unlock_the_car_window_1")
            } else {
                sendResultCode("Unlock_the_car_window_2")
            }
        }
    }

    /**
     * 锁车自动升窗
     * @param flag true:打开，false:关闭
     */
    fun setLockAutoRaiseWindow(flag: Boolean) {
        val state = quickManager.lockAutoRaiseWindow
        Log.d("zhc6whu", "setLockAutoRaiseWindow:设置锁车自动升窗 ")
        if (state == 0) {
            //当前已打开
            if (flag) {
                sendResultCode("open_the_lock_and_raise_the_window_2")
            } else {
                quickManager.lockAutoRaiseWindow = 0
                sendResultCode("close_the_lock_and_raise_the_window_2")
            }
        } else if (state == 1) {
            //当前已关闭
            if (flag) {
                quickManager.lockAutoRaiseWindow = 1
                sendResultCode("open_the_lock_and_raise_the_window_1")
            } else {
                sendResultCode("close_the_lock_and_raise_the_window_1")
            }
        }
    }

    /**
     * 设置车窗
     * @param flag true:打开，false:关闭
     */
    fun setWindow(flag: Boolean, pos: String, user: String) {
        manager = QuickManagerHandler()
        val washMode = (manager as QuickManagerHandler).washMode
        Log.d("zhc6whu", "setWindow: "+washMode+pos)
        if (flag){

            //判断是否P档并且中控锁落锁
            val signalVal: Int = newEnergyManager.drivingInfoGear
            // 获取中控锁状态
            val centralLockingState: Int = quickManager.centerLock
            if(signalVal==1&&centralLockingState==2){
                sendResultCode("Open_the_car_window_5")
            }
            //判断洗车模式是否开启
            else if(washMode==2){
                sendResultCode("Open_the_car_window_8")
            }
            //判断车窗锁是否启用
            else if (manager?.windowLock == 0){
                //车窗锁未启用，可以打开车窗
                when (pos) {
                    "LF" -> {
                        val state = getFLWindow()
                        if (state == 0){
                            setFLWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }else if(state == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setFLWindow(100)
                            sendResultCode("Close_the_car_window_10")
                        }
                    }
                    "RF" -> {
                        val state = getFRWindow()
                        if (state == 0){
                            setFRWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }else if(state == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setFRWindow(100)
                            sendResultCode("Close_the_car_window_10")
                        }
                    }
                    "LB" -> {
                        val state = getRLWindow()
                        if (state == 0){
                            setRLWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }else if(state == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setRLWindow(100)
                            sendResultCode("Close_the_car_window_10")
                        }
                    }
                    "RB" -> {
                        val state = getRRWindow()
                        if (state == 0){
                            setRRWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }else if(state == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setRRWindow(100)
                            sendResultCode("Close_the_car_window_10")
                        }
                    }
                    "F" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        if (state1== 0 && state2 == 0){
                            setFLWindow(100)
                            setFRWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }else if(state1== 100 && state2 == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setFLWindow(100)
                            setFRWindow(100)
                            sendResultCode("Close_the_car_window_10")
                        }
                    }
                    "B" -> {
                        val state1 = getRLWindow()
                        val state2 = getRRWindow()
                        if (state1== 0 && state2 == 0){
                            setRLWindow(100)
                            setRRWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }else if(state1== 100 && state2 == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setRLWindow(100)
                            setRRWindow(100)
                            sendResultCode("Close_the_car_window_10")
                        }
                    }
                    "L" -> {
                        val state1 = getFLWindow()
                        val state2 = getRLWindow()
                        if (state1== 0 && state2 == 0){
                            setFLWindow(100)
                            setRLWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }else if(state1== 100 && state2 == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setFLWindow(100)
                            setRLWindow(100)
                            sendResultCode("Close_the_car_window_10")
                        }
                    }
                    "R" -> {
                        val state1 = getFRWindow()
                        val state2 = getRRWindow()
                        if (state1== 0 && state2 == 0){
                            setFRWindow(100)
                            setRRWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }else if(state1== 100 && state2 == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setFRWindow(100)
                            setRRWindow(100)
                            sendResultCode("Close_the_car_window_10")
                        }
                    }
                    "ALL" ->{
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        val state3 = getRLWindow()
                        val state4 = getRRWindow()
                        if (state1 == 100&&state2 == 100&&state3 == 100&&state4 == 100){
                            sendResultCode("Open_the_car_window_2")
                        }else{
                            setFLWindow(100)
                            setFRWindow(100)
                            setRLWindow(100)
                            setRRWindow(100)
                            sendResultCode("Open_the_car_window_1")
                        }
                    }
                    //无该座位
                    else -> {
                        // 获取左前、右前、左后、右后车窗状态
                        val state_FL = getFLWindow()
                        val state_FR = getFRWindow()
                        val state_RL = getRLWindow()
                        val state_RR = getRRWindow()
                        // 声源位置
                        when (user) {
                            // 左前方
                            "LF" -> {
                                // 判断左前方开度是否为0
                                if (state_FL == 0) {
                                    // 车窗开度开到100
                                    setFLWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else if (state_FL == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    // 车窗开度开到100
                                    setFLWindow(100)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_10)
                                }
                            }
                            // 右前方
                            "RF" -> {
                                // 判断右前方开度是否为0
                                if (state_FR == 0) {
                                    // 车窗开度开到100
                                    setFRWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else if (state_FR == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    // 车窗开度开到100
                                    setFRWindow(100)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_10)
                                }
                            }
                            // 左后方
                            "LB" -> {
                                // 判断左后方开度是否为0
                                if (state_RL == 0) {
                                    // 车窗开度开到100
                                    setRLWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else if (state_RL == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    // 车窗开度开到100
                                    setRLWindow(100)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_10)
                                }
                            }
                            // 右后方
                            "RB" -> {
                                // 判断右后方开度是否为0
                                if (state_RR == 0) {
                                    // 车窗开度开到100
                                    setRRWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else if (state_RR == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    // 车窗开度开到100
                                    setRRWindow(100)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_10)
                                }
                            }
                            // 前排车窗
                            "F" -> {
                                // 判断前排车窗开度是否为0
                                if (state_FL == 0 && state_FR == 0) {
                                    // 车窗开度开到100
                                    setFLWindow(100)
                                    setFRWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else if (state_FL == 100 && state_FR == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    // 车窗开度开到100
                                    setFLWindow(100)
                                    setFRWindow(100)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_10)
                                }
                            }
                            // 后排车窗
                            "B" -> {
                                // 判断后排车窗开度是否为0
                                if (state_RL == 0 && state_RR == 0) {
                                    // 车窗开度开到100
                                    setRLWindow(100)
                                    setRRWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else if (state_RL == 100 && state_RR == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    // 车窗开度开到100
                                    setRLWindow(100)
                                    setRRWindow(100)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_10)
                                }
                            }
                            // 左侧车窗
                            "L" -> {
                                // 判断左侧车窗开度是否为0
                                if (state_FL == 0 && state_RL == 0) {
                                    // 车窗开度开到100
                                    setFLWindow(100)
                                    setRLWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else if (state_FL == 100 && state_RL == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    setFLWindow(100)
                                    setRLWindow(100)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_10)
                                }
                            }
                            // 右侧车窗
                            "R" -> {
                                // 判断右侧车窗开度是否为0
                                if (state_FR == 0 && state_RR == 0) {
                                    // 车窗开度开到100
                                    setFRWindow(100)
                                    setRRWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else if (state_FR == 100 && state_RR == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    // 车窗开度开到100
                                    setFRWindow(100)
                                    setRRWindow(100)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_10)
                                }
                            }
                            // 所有车窗
                            "ALL" -> {
                                // 判断所有车窗开度是否为0
                                if (state_FL == 100 && state_FR == 100 && state_RL == 100 && state_RR == 100) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else {
                                    // 车窗开度开到100
                                    setFLWindow(100)
                                    setFRWindow(100)
                                    setRLWindow(100)
                                    setRRWindow(100)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                }
                            }
                            // 未找到车窗位置
                            else -> {
                                Log.d("zhc6whu", "setWindow: 未找到" + pos)
                                sendResultCode(VDVRRespondID.Open_the_car_window_7)
                            }
                        }
                    }
                }
            }else if (manager?.windowLock == 1){
                sendResultCode(VDVRRespondID.Open_the_car_window_4)
            }
        }else{
            //关闭车窗
            //判断车窗锁是否启用
            if (manager?.windowLock == 0){
                //车窗锁未启用，可以关闭车窗
                when (pos) {
                    "LF" -> {
                        val state = getFLWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_2")
                        }else if(state == 100){
                            setFLWindow(0)
                            sendResultCode("Close_the_car_window_1")
                        }else{
                            setFLWindow(0)
                            sendResultCode("Close_the_car_window_7")
                        }
                    }
                    "RF" -> {
                        val state = getFRWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_2")
                        }else if(state == 100){
                            setFRWindow(0)
                            sendResultCode("Close_the_car_window_1")
                        }else{
                            setFRWindow(0)
                            sendResultCode("Close_the_car_window_7")
                        }
                    }
                    "LB" -> {
                        val state = getRLWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_2")
                        }else if(state == 100){
                            setRLWindow(0)
                            sendResultCode("Close_the_car_window_1")
                        }else{
                            setRLWindow(0)
                            sendResultCode("Close_the_car_window_7")
                        }
                    }
                    "RB" -> {
                        val state = getRRWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_2")
                        }else if(state == 100){
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_1")
                        }else{
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_7")
                        }
                    }
                    "F" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        if (state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_2")
                        }else if(state1== 100 && state2 == 100){
                            setFLWindow(0)
                            setFRWindow(0)
                            sendResultCode("Close_the_car_window_1")
                        }else{
                            setFLWindow(0)
                            setFRWindow(0)
                            sendResultCode("Close_the_car_window_7")
                        }
                    }
                    "B" -> {
                        val state1 = getRLWindow()
                        val state2 = getRRWindow()
                        if (state1== 0 && state2 == 0){
                            sendResultCode("Open_the_car_window_2")
                        }else if(state1== 100 && state2 == 100){
                            setRLWindow(0)
                            setRRWindow(0)
                            sendResultCode("Open_the_car_window_1")
                        }else{
                            setRLWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_7")
                        }
                    }
                    "L" -> {
                        val state1 = getFLWindow()
                        val state2 = getRLWindow()
                        if (state1== 0 && state2 == 0){
                            sendResultCode("Open_the_car_window_2")
                        }else if(state1== 100 && state2 == 100){
                            setFLWindow(0)
                            setRLWindow(0)
                            sendResultCode("Open_the_car_window_1")
                        }else{
                            setFLWindow(0)
                            setRLWindow(0)
                            sendResultCode("Close_the_car_window_7")
                        }
                    }
                    "R" -> {
                        val state1 = getFRWindow()
                        val state2 = getRRWindow()
                        if (state1== 0 && state2 == 0){
                            sendResultCode("Open_the_car_window_2")
                        }else if(state1== 100 && state2 == 100){
                            setFRWindow(0)
                            setRRWindow(0)
                            sendResultCode("Open_the_car_window_1")
                        }else{
                            setFRWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_7")
                        }
                    }
                    "ALL" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        val state3 = getRLWindow()
                        val state4 = getRRWindow()
                        if (state1 == 0 && state2 == 0 && state3 == 0 && state4 == 0) {
                            sendResultCode("Close_the_car_window_1")
                        } else {
                            setFLWindow(0)
                            setFRWindow(0)
                            setRLWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_2")
                        }
                    }
                    //无该座位
                    else -> {
                        // 获取左前、右前、左后、右后车窗状态
                        val state_FL = getFLWindow()
                        val state_FR = getFRWindow()
                        val state_RL = getRLWindow()
                        val state_RR = getRRWindow()
                        // 判断声源位置
                        when (user) {
                            // 左前
                            "LF" -> {
                                // 判断左前方车窗状态
                                if (state_FL == 0) {
                                    sendResultCode(VDVRRespondID.Close_the_car_window_2)
                                } else if (state_FL == 100) {
                                    // 车窗开度设置为0
                                    setFLWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setFLWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_7)
                                }
                            }
                            // 右前
                            "RF" -> {
                                // 获取右前方车窗状态
                                if (state_FR == 0) {
                                    sendResultCode(VDVRRespondID.Close_the_car_window_2)
                                } else if (state_FR == 100) {
                                    // 车窗开度设置为0
                                    setFRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setFRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_7)
                                }
                            }
                            // 左后
                            "LB" -> {
                                // 获取左后方车窗状态
                                if (state_RL == 0) {
                                    sendResultCode(VDVRRespondID.Close_the_car_window_2)
                                } else if (state_RL == 100) {
                                    // 车窗开度设置为0
                                    setRLWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setRLWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_7)
                                }
                            }
                            // 右后
                            "RB" -> {
                                // 获取右后方车窗状态
                                if (state_RR == 0) {
                                    sendResultCode(VDVRRespondID.Close_the_car_window_2)
                                } else if (state_RR == 100) {
                                    // 车窗开度设置为0
                                    setRRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setRRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_7)
                                }
                            }
                            // 前排车窗
                            "F" -> {
                                // 获取前排车窗状态
                                if (state_FL == 0 && state_FR == 0) {
                                    sendResultCode(VDVRRespondID.Close_the_car_window_2)
                                } else if (state_FL == 100 && state_FR == 100) {
                                    // 车窗开度设置为0
                                    setFLWindow(0)
                                    setFRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setFLWindow(0)
                                    setFRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_7)
                                }
                            }
                            // 后排车窗
                            "B" -> {
                                // 获取后排车窗状态
                                if (state_RL == 0 && state_RR == 0) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else if (state_RL == 100 && state_RR == 100) {
                                    // 车窗开度设置为0
                                    setRLWindow(0)
                                    setRRWindow(0)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setRLWindow(0)
                                    setRRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_7)
                                }
                            }
                            // 左侧车窗
                            "L" -> {
                                // 获取左侧车窗状态
                                if (state_FL == 0 && state_RL == 0) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else if (state_FL == 100 && state_RL == 100) {
                                    // 车窗开度设置为0
                                    setFLWindow(0)
                                    setRLWindow(0)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setFLWindow(0)
                                    setRLWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_7)
                                }
                            }
                            // 右侧车窗
                            "R" -> {
                                // 获取右侧车窗状态
                                if (state_FR == 0 && state_RR == 0) {
                                    sendResultCode(VDVRRespondID.Open_the_car_window_2)
                                } else if (state_FR == 100 && state_RR == 100) {
                                    // 车窗开度设置为0
                                    setFRWindow(0)
                                    setRRWindow(0)
                                    sendResultCode(VDVRRespondID.Open_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setFRWindow(0)
                                    setRRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_7)
                                }
                            }
                            // 全部车窗
                            "ALL" -> {
                                // 获取全部车窗状态
                                if (state_FR == 0 && state_FL == 0 && state_RR == 0 && state_RL == 0) {
                                    sendResultCode(VDVRRespondID.Close_the_car_window_1)
                                } else {
                                    // 车窗开度设置为0
                                    setFLWindow(0)
                                    setFRWindow(0)
                                    setRLWindow(0)
                                    setRRWindow(0)
                                    sendResultCode(VDVRRespondID.Close_the_car_window_2)
                                }
                            }
                            // 车窗位置不存在
                            else -> {
                                sendResultCode(VDVRRespondID.Close_the_car_window_6)
                            }
                        }
                    }
                }
            }else if (manager?.windowLock == 1){
                sendResultCode(VDVRRespondID.Close_the_car_window_4)
            }
        }
    }

    /**
     * 车窗开大点
     */
    fun setAdjustLittleWindow(value: String,pos: String){
        manager = QuickManagerHandler()
        val washMode = (manager as QuickManagerHandler).washMode
        Log.d("zhc6whu", "setWindow: "+washMode+"位置"+pos)
        if (value=="0"){//开大点
            //判断是否P档并且中控锁落锁
            val signalVal: Int = newEnergyManager.drivingInfoGear
            // 获取中控锁状态
            val centralLockingState: Int = quickManager.centerLock
            if(signalVal==1&&centralLockingState==2){
                sendResultCode("Open_the_car_window_wider_5")
            }
            //判断洗车模式是否开启
            else if(washMode==2){
                sendResultCode("Open_the_car_window_wider_10")
            }
            //判断车窗锁是否启用
            else if (manager?.windowLock == 0){
                //车窗锁未启用，可以打开车窗
                when (pos) {
                    "LF" -> {
                        val state = getFLWindow()
                        if (state <= 99){
                            setFLWindow(state+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else if(state == 100){
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    "RF" -> {
                        val state = getFRWindow()
                        if (state <= 99){
                            setFRWindow(state+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else if(state == 100){
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    "LB" -> {
                        val state = getRLWindow()
                        if (state <= 99){
                            setRLWindow(state+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else if(state == 100){
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    "RB" -> {
                        val state = getRRWindow()
                        if (state <= 99){
                            setRRWindow(state+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else if(state == 100){
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    "F" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        if (state1 <100 || state2 < 100){
                            setFLWindow(state1+5)
                            setFRWindow(state2+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else if(state1== 100 && state2 == 100){
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    "B" -> {
                        val state1 = getRLWindow()
                        val state2 = getRRWindow()
                        if (state1 <100 || state2 < 100){
                            setRLWindow(state1+5)
                            setRRWindow(state2+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else if(state1== 100 && state2 == 100){
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    "L" -> {
                        val state1 = getFLWindow()
                        val state2 = getRLWindow()
                        if (state1 <100 || state2 < 100){
                            setFLWindow(state1+5)
                            setRLWindow(state2+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else if(state1== 100 && state2 == 100){
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    "R" -> {
                        val state1 = getFRWindow()
                        val state2 = getRRWindow()
                        if (state1 <100 || state2 < 100){
                            setFRWindow(state1+5)
                            setRRWindow(state2+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else if(state1== 100 && state2 == 100){
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    "ALL" ->{
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        val state3 = getRLWindow()
                        val state4 = getRRWindow()
                        if (state1 < 100||state2 < 100||state3 < 100||state4 < 100){
                            setFLWindow(state1+5)
                            setFRWindow(state2+5)
                            setRLWindow(state3+5)
                            setRRWindow(state4+5)
                            sendResultCode("Open_the_car_window_wider_1")
                        }else{
                            sendResultCode("Open_the_car_window_wider_2")
                        }
                    }
                    //无该座位
                    else -> {
                        sendResultCode("Open_the_car_window_wider_7")
                    }
                }
            }else if (manager?.windowLock == 1){
                sendResultCode("Open_the_car_window_wider_4")
            }
        }else{
            //开小点
            //判断车窗锁是否启用
            if (manager?.windowLock == 0){
                //车窗锁未启用，可以关闭车窗
                when (pos) {
                    "LF" -> {
                        val state = getFLWindow()
                        if (state >0){
                            setFLWindow(state-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    "RF" -> {
                        val state = getFRWindow()
                        if (state >0){
                            setFRWindow(state-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    "LB" -> {
                        val state = getRLWindow()
                        if (state >0){
                            setRLWindow(state-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    "RB" -> {
                        val state = getRRWindow()
                        if (state >0){
                            setRRWindow(state-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    "F" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        if (state1 >0 || state2 >0){
                            setFLWindow(state1-5)
                            setFRWindow(state2-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    "B" -> {
                        val state1 = getRLWindow()
                        val state2 = getRRWindow()
                        if(state1 >0 || state2 >0){
                            setRLWindow(state1-5)
                            setRRWindow(state2-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    "L" -> {
                        val state1 = getFLWindow()
                        val state2 = getRLWindow()
                        if (state1 >0 || state2 >0){
                            setFLWindow(state1-5)
                            setRLWindow(state2-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    "R" -> {
                        val state1 = getFRWindow()
                        val state2 = getRRWindow()
                        if (state1 >0 || state2 >0){
                            setFRWindow(state1-5)
                            setRRWindow(state2-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    "ALL" ->{
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        val state3 = getRLWindow()
                        val state4 = getRRWindow()
                        if (state1 >0||state2 >0||state3 >0||state4 >0){
                            setFLWindow(state1-5)
                            setFRWindow(state2-5)
                            setRLWindow(state3-5)
                            setRRWindow(state4-5)
                            sendResultCode("Close_the_car_window_a_little_2")
                        }else if(state1== 0 && state2 == 0&& state3 == 0&& state4 == 0){
                            sendResultCode("Close_the_car_window_a_little_1")
                        }
                    }
                    //无该座位
                    else -> {
                        sendResultCode("Close_the_car_window_a_little_6")
                    }
                }
            }else if (manager?.windowLock == 1){
                sendResultCode("Close_the_car_window_a_little_4")
            }
        }
    }

    /**
     * 打开车窗通风
     */
    fun setWindowVentilate(flag: Boolean,pos: String) {
        manager = QuickManagerHandler()
        val washMode = (manager as QuickManagerHandler).washMode
        Log.d("zhc6whu", "setWindow: "+washMode+pos)
        if (flag){
            //判断是否P档并且中控锁落锁
            val signalVal: Int = newEnergyManager.drivingInfoGear
            // 获取中控锁状态
            val centralLockingState: Int = quickManager.centerLock
            if(signalVal==1&&centralLockingState==2){
                sendResultCode("Open_the_car_window_5")
            }
            //判断洗车模式是否开启
            else if(washMode==2){
                sendResultCode("Open_the_car_window_for_ventilation_8")
            }
            //判断车窗锁是否启用
            else if (manager?.windowLock == 0){
                //车窗锁未启用，可以打开车窗
                when (pos) {
                    "LF" -> {
                        val state = getFLWindow()
                        if (state == 20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else {
                            setFLWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "RF" -> {
                        val state = getFRWindow()
                        if (state == 20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else {
                            setFRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "LB" -> {
                        val state = getRLWindow()
                        if (state == 20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else {
                            setRLWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "RB" -> {
                        val state = getRRWindow()
                        if (state == 20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else {
                            setRRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "F" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        if (state1 ==20 && state2 ==20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else{
                            setFLWindow(20)
                            setFRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "B" -> {
                        val state1 = getRLWindow()
                        val state2 = getRRWindow()
                        if(state1 ==20 && state2 ==20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else{
                            setRLWindow(20)
                            setRRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "L" -> {
                        val state1 = getFLWindow()
                        val state2 = getRLWindow()
                        if(state1 ==20 && state2 ==20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else{
                            setFLWindow(20)
                            setRLWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "R" -> {
                        val state1 = getFRWindow()
                        val state2 = getRRWindow()
                        if(state1 ==20 && state2 ==20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else{
                            setFRWindow(20)
                            setRRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "ALL" ->{
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        val state3 = getRLWindow()
                        val state4 = getRRWindow()
                        if (state1 ==20&&state2 ==20&&state3 ==20&&state4 ==20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else{
                            setFLWindow(20)
                            setFRWindow(20)
                            setRLWindow(20)
                            setRRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    //无该座位
                    else -> {
                        sendResultCode("Open_the_car_window_for_ventilation_6")
                    }
                }
            }else if (manager?.windowLock == 1){
                sendResultCode("Open_the_car_window_for_ventilation_4")
            }
        }else{
            //关闭车窗
            //判断车窗锁是否启用
            if (manager?.windowLock == 0){
                //车窗锁未启用，可以关闭车窗
                when (pos) {
                    "LF" -> {
                        val state = getFLWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else{
                            setFLWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "RF" -> {
                        val state = getFRWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else{
                            setFRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "LB" -> {
                        val state = getRLWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else{
                            setRLWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "RB" -> {
                        val state = getRRWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else{
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "F" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        if (state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else{
                            setFLWindow(0)
                            setFRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "B" -> {
                        val state1 = getRLWindow()
                        val state2 = getRRWindow()
                        if (state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else{
                            setRLWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "L" -> {
                        val state1 = getFLWindow()
                        val state2 = getRLWindow()
                        if (state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else{
                            setFLWindow(0)
                            setRLWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "R" -> {
                        val state1 = getFRWindow()
                        val state2 = getRRWindow()
                        if (state1== 0 && state2 == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else{
                            setFRWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "ALL" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        val state3 = getRLWindow()
                        val state4 = getRRWindow()
                        if (state1 == 0 && state2 == 0 && state3 == 0 && state4 == 0) {
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        } else {
                            setFLWindow(0)
                            setFRWindow(0)
                            setRLWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    //无该座位
                    else -> {
                        sendResultCode("Close_the_car_window_6")
                    }
                }
            }else if (manager?.windowLock == 1){
                sendResultCode("Close_the_car_window_4")
            }
        }
    }

    /**
     * 车窗透气
     */
    fun setWindowRiser(flag: Boolean,pos: String){
        manager = QuickManagerHandler()
        val washMode = (manager as QuickManagerHandler).washMode
        Log.d("zhc6whu", "setWindow: $washMode$pos")
        if (flag){

            //判断是否P档并且中控锁落锁
            val signalVal: Int = newEnergyManager.drivingInfoGear
            // 获取中控锁状态
            val centralLockingState: Int = quickManager.centerLock
            if(signalVal==1&&centralLockingState==2){
                sendResultCode("Open_the_car_window_5")
            }
            //判断洗车模式是否开启
            else if(washMode==2){
                sendResultCode("Open_the_car_window_for_ventilation_8")
            }
            //判断车窗锁是否启用
            else if (manager?.windowLock == 0){
                //车窗锁未启用，可以打开车窗
                when (pos) {
                    "LF" -> {
                        val state = getFLWindow()
                        if (state == 20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else {
                            setFLWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "RF" -> {
                        val state = getFRWindow()
                        if (state == 20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else {
                            setFRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "LB" -> {
                        val state = getRLWindow()
                        if (state == 20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else {
                            setRLWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "RB" -> {
                        val state = getRRWindow()
                        if (state == 20){
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        }else {
                            setRRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    "F" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        if (state1 == 20 && state2 == 20) {
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        } else {
                            setFLWindow(20)
                            setFRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }

                    "B" -> {
                        val state1 = getRLWindow()
                        val state2 = getRRWindow()
                        if (state1 == 20 && state2 == 20) {
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        } else {
                            setRLWindow(20)
                            setRRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }

                    "L" -> {
                        val state1 = getFLWindow()
                        val state2 = getRLWindow()
                        if (state1 == 20 && state2 == 20) {
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        } else {
                            setFLWindow(20)
                            setRLWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }

                    "R" -> {
                        val state1 = getFRWindow()
                        val state2 = getRRWindow()
                        if (state1 == 20 && state2 == 20) {
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        } else {
                            setFRWindow(20)
                            setRRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }

                    "ALL" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        val state3 = getRLWindow()
                        val state4 = getRRWindow()
                        if (state1 == 20 && state2 == 20 && state3 == 20 && state4 == 20) {
                            sendResultCode("Open_the_car_window_for_ventilation_2")
                        } else {
                            setFLWindow(20)
                            setFRWindow(20)
                            setRLWindow(20)
                            setRRWindow(20)
                            sendResultCode("Open_the_car_window_for_ventilation_1")
                        }
                    }
                    //无该座位
                    else -> {
                        sendResultCode("Open_the_car_window_for_ventilation_6")
                    }
                }
            }else if (manager?.windowLock == 1){
                sendResultCode("Open_the_car_window_for_ventilation_4")
            }
        }else{
            //关闭车窗
            //判断车窗锁是否启用
            if (manager?.windowLock == 0){
                //车窗锁未启用，可以关闭车窗
                when (pos) {
                    "LF" -> {
                        val state = getFLWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else {
                            setFLWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "RF" -> {
                        val state = getFRWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else {
                            setFRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "LB" -> {
                        val state = getRLWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else {
                            setRLWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "RB" -> {
                        val state = getRRWindow()
                        if (state == 0){
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        }else {
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }

                    "F" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        if (state1 == 0 && state2 == 0) {
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        } else {
                            setFLWindow(0)
                            setFRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }

                    "B" -> {
                        val state1 = getRLWindow()
                        val state2 = getRRWindow()
                        if (state1 == 0 && state2 == 0) {
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        } else {
                            setRLWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }

                    "L" -> {
                        val state1 = getFLWindow()
                        val state2 = getRLWindow()
                        if (state1 == 0 && state2 == 0) {
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        } else {
                            setFLWindow(0)
                            setRLWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }

                    "R" -> {
                        val state1 = getFRWindow()
                        val state2 = getRRWindow()
                        if (state1 == 0 && state2 == 0) {
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        } else {
                            setFRWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    "ALL" -> {
                        val state1 = getFLWindow()
                        val state2 = getFRWindow()
                        val state3 = getRLWindow()
                        val state4 = getRRWindow()
                        if (state1 == 0 && state2 == 0 && state3 == 0 && state4 == 0) {
                            sendResultCode("Close_the_car_window_for_ventilation_2")
                        } else {
                            setFLWindow(0)
                            setFRWindow(0)
                            setRLWindow(0)
                            setRRWindow(0)
                            sendResultCode("Close_the_car_window_for_ventilation_1")
                        }
                    }
                    //无该座位
                    else -> {
                        sendResultCode("Close_the_car_window_6")
                    }
                }
            }else if (manager?.windowLock == 1){
                sendResultCode("Close_the_car_window_4")
            }
        }
    }

    /**
     * 透气模式
     * @param flag ture:打开,false:关闭
     */
    fun setRiserSystem(flag: Boolean){
        manager = QuickManagerHandler()
        val washMode = (manager as QuickManagerHandler).washMode
        if (flag){

            //判断是否P档并且中控锁落锁
            val signalVal: Int = newEnergyManager.drivingInfoGear
            // 获取中控锁状态
            val centralLockingState: Int = quickManager.centerLock
            if(signalVal==1&&centralLockingState==2){
                sendResultCode("Open_the_car_window_5")
            }
            //判断洗车模式是否开启
            else if(washMode==2){
                sendResultCode("Open_the_car_window_for_ventilation_8")
            }
            //判断车窗锁是否启用
            else if (manager?.windowLock == 0){
                //车窗锁未启用，可以打开车窗
                val state1 = getFLWindow()
                val state2 = getFRWindow()
                val state3 = getRLWindow()
                val state4 = getRRWindow()
                if (state1 == 20&&state2 == 20&&state3 == 20&&state4 == 20){
                    sendResultCode("Open_the_car_window_for_ventilation_2")
                }else{
                    setFLWindow(20)
                    setFRWindow(20)
                    setRLWindow(20)
                    setRRWindow(20)
                    sendResultCode("Open_the_car_window_for_ventilation_1")
                }
            }else if (manager?.windowLock == 1){
                sendResultCode("Open_the_car_window_for_ventilation_4")
            }
        }else{
            //关闭车窗
            //判断车窗锁是否启用
            if (manager?.windowLock == 0){
                //车窗锁未启用，可以关闭车窗
                val state1 = getFLWindow()
                val state2 = getFRWindow()
                val state3 = getRLWindow()
                val state4 = getRRWindow()
                if (state1 == 0 && state2 == 0 && state3 == 0 && state4 == 0) {
                    sendResultCode("Close_the_car_window_for_ventilation_2")
                } else {
                    setFLWindow(0)
                    setFRWindow(0)
                    setRLWindow(0)
                    setRRWindow(0)
                    sendResultCode("Close_the_car_window_for_ventilation_1")
                }
            }else if (manager?.windowLock == 1){
                sendResultCode("Close_the_car_window_4")
            }
        }
    }

    /**
     * 车窗开度调节
     * @param value 窗开度
     * @param pos 窗位置
     */
    fun setOpenDegreeWindow(value:String,pos: String){
        manager = QuickManagerHandler()
        val target = value.toInt()
        val washMode = (manager as QuickManagerHandler).washMode
        Log.d("zhc6whu", "setWindow: "+washMode+pos)

        //判断是否P档并且中控锁落锁
        val signalVal: Int = newEnergyManager.drivingInfoGear
        // 获取中控锁状态
        val centralLockingState: Int = quickManager.centerLock
        if(signalVal==1&&centralLockingState==2){
            sendResultCode("Open_the_car_window_to_X_5")
        }
        //判断洗车模式是否开启
        else if(washMode==2){
            sendResultCode("Open_the_car_window_to_X_10")
        }
        //判断车窗锁是否启用
        else if (manager?.windowLock == 0){
            //车窗锁未启用，可以打开车窗
            when (pos) {
                "LF" -> {
                    val state = getFLWindow()
                    if (state == target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFLWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setFLWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                "RF" -> {
                    val state = getFRWindow()
                    if (state == target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFRWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setFRWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                "LB" -> {
                    val state = getRLWindow()
                    if (state == target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setRLWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setRLWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                "RB" -> {
                    val state = getRRWindow()
                    if (state == target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setRRWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setRRWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                "F" -> {
                    val state1 = getFLWindow()
                    val state2 = getFRWindow()
                    if (state1 ==target || state2 ==target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFLWindow(target)
                        setFRWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setFLWindow(100)
                        setFRWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                "B" -> {
                    val state1 = getRLWindow()
                    val state2 = getRRWindow()
                    if (state1 ==target || state2 ==target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setRLWindow(target)
                        setRRWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setRLWindow(100)
                        setRRWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                "L" -> {
                    val state1 = getFLWindow()
                    val state2 = getRLWindow()
                    if (state1 ==target || state2 ==target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFLWindow(target)
                        setRLWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setFLWindow(100)
                        setRLWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                "R" -> {
                    val state1 = getFRWindow()
                    val state2 = getRRWindow()
                    if (state1 ==target || state2 ==target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFRWindow(target)
                        setRRWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setFRWindow(100)
                        setRRWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                "ALL" ->{
                    val state1 = getFLWindow()
                    val state2 = getFRWindow()
                    val state3 = getRLWindow()
                    val state4 = getRRWindow()
                    if (state1 ==target||state2 ==target||state3 ==target||state4 ==target){
                        sendResultCode("Open_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFLWindow(target)
                        setRLWindow(target)
                        setFRWindow(target)
                        setRRWindow(target)
                        sendResultCode("Open_the_car_window_to_X_1")
                    }else{
                        setFLWindow(100)
                        setRLWindow(100)
                        setFRWindow(100)
                        setRRWindow(100)
                        sendResultCode("Open_the_car_window_to_X_11")
                    }
                }
                //无该座位
                else -> {
                    sendResultCode("Open_the_car_window_to_X_7")
                }
            }
        }else if ( manager?.windowLock == 1){
            sendResultCode("Open_the_car_window_to_X_4")
        }
    }

    /**
     * 关闭到指定程度
     */
    fun setCloseDegreeWindow(value:String,pos: String){
        manager = QuickManagerHandler()
        val target = value.toInt()
        val washMode = (manager as QuickManagerHandler).washMode
        Log.d("zhc6whu", "setWindow: "+washMode+pos)
        //判断是否P档并且中控锁落锁
        val signalVal: Int = newEnergyManager.drivingInfoGear
        // 获取中控锁状态
        val centralLockingState: Int = quickManager.centerLock
        if(signalVal==1&&centralLockingState==2){
            sendResultCode("Open_the_car_window_to_X_5")
        }
        //判断洗车模式是否开启
        else if(washMode==2){
            sendResultCode("Open_the_car_window_to_X_10")
        }
        //判断车窗锁是否启用
        else if ( manager?.windowLock == 0){
            //车窗锁未启用，可以关闭车窗
            when (pos) {
                "LF" -> {
                    val state = getFLWindow()
                    if (state == target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFLWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setFLWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setFLWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                "RF" -> {
                    val state = getFRWindow()
                    if (state == target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFRWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setFRWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setFRWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                "LB" -> {
                    val state = getRLWindow()
                    if (state == target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setRLWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setRLWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setRLWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                "RB" -> {
                    val state = getRRWindow()
                    if (state == target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setRRWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setRRWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setRRWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                "F" -> {
                    val state1 = getFLWindow()
                    val state2 = getFRWindow()
                    if (state1 ==target || state2 ==target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFLWindow(target)
                        setFRWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setFLWindow(0)
                        setFRWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setFLWindow(100)
                        setFRWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                "B" -> {
                    val state1 = getRLWindow()
                    val state2 = getRRWindow()
                    if (state1 ==target || state2 ==target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setRLWindow(target)
                        setRRWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setRLWindow(0)
                        setRRWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setRLWindow(100)
                        setRRWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                "L" -> {
                    val state1 = getFLWindow()
                    val state2 = getRLWindow()
                    if (state1 ==target || state2 ==target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFLWindow(target)
                        setRLWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setFLWindow(0)
                        setRLWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setFLWindow(100)
                        setRLWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                "R" -> {
                    val state1 = getFRWindow()
                    val state2 = getRRWindow()
                    if (state1 ==target || state2 ==target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFRWindow(target)
                        setRRWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setFRWindow(0)
                        setRRWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setFRWindow(100)
                        setRRWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                "ALL" ->{
                    val state1 = getFLWindow()
                    val state2 = getFRWindow()
                    val state3 = getRLWindow()
                    val state4 = getRRWindow()
                    if (state1 ==target||state2 ==target||state3 ==target||state4 ==target){
                        sendResultCode("Close_the_car_window_to_X_2")
                    }else if(target in 0..100){
                        setFLWindow(target)
                        setFRWindow(target)
                        setRLWindow(target)
                        setRRWindow(target)
                        sendResultCode("Close_the_car_window_to_X_1")
                    }else if(target<0){
                        setFLWindow(0)
                        setFRWindow(0)
                        setRLWindow(0)
                        setRRWindow(0)
                        sendResultCode("Close_the_car_window_to_X_9")
                    }else{
                        setFLWindow(100)
                        setFRWindow(100)
                        setRLWindow(100)
                        setRRWindow(100)
                        sendResultCode("Close_the_car_window_to_X_10")
                    }
                }
                //无该座位
                else -> {
                    sendResultCode("Close_the_car_window_to_X_6")
                }
            }
        }else if ( manager?.windowLock == 1){
            sendResultCode("Close_the_car_window_to_X_4")
        }
    }
}