/**
 * Copyright (C), 2015-2024, XXX有限公司
 * FileName: VehicleConfigUtil
 * Author: WUY1WHU
 * Date: 2024/6/18 15:15
 * Description:
 * History:
 * <author> <time> <version> <desc>
 * 作者姓名 修改时间 版本号 描述
 */
package com.bitech.vehiclesettings.utils;


import android.graphics.Color;
import android.util.Log;

import com.blankj.utilcode.util.StringUtils;

/**
 * 功能: VehicleConfigUtil
 * 红	0.680	0.310	255	7	0
 * 橙	0.543	0.401	255	90	3
 * 黄	0.429	0.478	255	235	5
 * 绿	0.200	0.630	0	255	5
 * 青	0.167	0.277	6	251	255
 * 蓝	0.150	0.100	5	36	255
 * 紫	0.276	0.150	173	41	255
 * RED(1, "红"),
 * ORANGE(2, "橙"),
 * YELLOW(3, "黄"),
 * GREEN(4, "绿"),
 * YOUNG(5, "青"),
 * BLUE(6, "蓝"),
 * PURPLE(7, "紫"),
 * OTHERS(8,"未知颜色");
 * Author: WUY1WHU
 * Date: 2024/6/18 15:15
 * Description:
 * History:
 */
public class ColorUtils {
    private static final String TAG = ColorUtils.class.getSimpleName();

    public static AppEnum.Color findNearColor(String colorHex) {
        int rgb[] = hexToRgb(colorHex);
        if (rgb.length == 3) {
            return findNearColor(rgb[0], rgb[1], rgb[2]);
        }
        return AppEnum.Color.OTHERS;
    }

    public static float[] RGBToHSL(int r, int g, int b) {
        float[] hsl = new float[3];
        float fr = r / 255f;
        float fg = g / 255f;
        float fb = b / 255f;

        float max = Math.max(fr, Math.max(fg, fb));
        float min = Math.min(fr, Math.min(fg, fb));
        float delta = max - min;

        // Lightness
        hsl[2] = (max + min) / 2;

        if (delta == 0) {
            hsl[0] = 0; // Hue
            hsl[1] = 0; // Saturation
        } else {
            // Saturation
            if (hsl[2] < 0.5) {
                hsl[1] = delta / (max + min);
            } else {
                hsl[1] = delta / (2 - max - min);
            }

            // Hue
            if (r == (int) (max * 255)) {
                hsl[0] = (g - b) / delta + (g < b ? 6 : 0);
            } else if (g == (int) (max * 255)) {
                hsl[0] = (b - r) / delta + 2;
            } else {
                hsl[0] = (r - g) / delta + 4;
            }
            hsl[0] /= 6;
        }

        return hsl;
    }

    public static AppEnum.Color findNearColor(int r, int g, int b) {

        double disRed = calculateColorSimilarityLab(255, 7, 0, r, g, b);
        double disOrange = calculateColorSimilarityLab(255, 90, 3, r, g, b);
        double disYellow = calculateColorSimilarityLab(255, 235, 5, r, g, b);
        double disGreen = calculateColorSimilarityLab(0, 255, 5, r, g, b);
        double disYoung = calculateColorSimilarityLab(6, 251, 255, r, g, b);
        double disBlue = calculateColorSimilarityLab(5, 36, 255, r, g, b);
        double disPurple = calculateColorSimilarityLab(173, 41, 255, r, g, b);
        double maxValue = Math.max(disRed, Math.max(disOrange, Math.max(disYellow, Math.max(disGreen, Math.max(disYoung, Math.max(disBlue, disPurple))))));
        System.out.println("disRed:" + disRed);
        System.out.println("disOrange:" + disOrange);
        System.out.println("disYellow:" + disYellow);
        System.out.println("disGreen:" + disGreen);
        System.out.println("disYoung:" + disYoung);
        System.out.println("disBlue:" + disBlue);
        System.out.println("disPurple:" + disPurple);
        System.out.println("maxValue:" + maxValue);
        if (disRed == maxValue) {
            return AppEnum.Color.RED;
        } else if (disOrange == maxValue) {
            return AppEnum.Color.ORANGE;
        } else if (disYellow == maxValue) {
            return AppEnum.Color.YELLOW;
        } else if (disGreen == maxValue) {
            return AppEnum.Color.GREEN;
        } else if (disYoung == maxValue) {
            return AppEnum.Color.YOUNG;
        } else if (disBlue == maxValue) {
            return AppEnum.Color.BLUE;
        } else if (disPurple == maxValue) {
            return AppEnum.Color.PURPLE;
        } else {
            return AppEnum.Color.OTHERS;
        }
    }

    public static String defaultRgbToHex(int id) {

        switch (id) {
            case CommonConst.RED:
                return String.format("#%02X%02X%02X", 255, 0, 0);
            case CommonConst.ORANGE:
                return String.format("#%02X%02X%02X", 255, 127, 24);

            case CommonConst.YELLOW:
                return String.format("#%02X%02X%02X", 255, 231, 58);

            case CommonConst.GREEN:
                return String.format("#%02X%02X%02X", 0, 255, 72);

            case CommonConst.YOUNG:
                return String.format("#%02X%02X%02X", 0, 219, 255);

            case CommonConst.BLUE:
                return String.format("#%02X%02X%02X", 54, 82, 255);

            case CommonConst.PURPLE:
                return String.format("#%02X%02X%02X", 210, 76, 255);
            case CommonConst.MULTI_COLOR_1:
                return AppEnum.ambientLightColor.getHexByLin(22);
            case CommonConst.MULTI_COLOR_2:
                return AppEnum.ambientLightColor.getHexByLin(48);
            case CommonConst.MULTI_COLOR_3:
                return AppEnum.ambientLightColor.getHexByLin(1);
            case CommonConst.MULTI_COLOR_4:
                return AppEnum.ambientLightColor.getHexByLin(36);
            case CommonConst.MULTI_COLOR_5:
                return AppEnum.ambientLightColor.getHexByLin(43);
            default:
                return String.format("#%02X%02X%02X", 0, 0, 0);

        }


    }

    public static String rgbToHex(int r, int g, int b) {
        return String.format("#%02X%02X%02X", r, g, b);
    }

    public static String rgbToHex(String rgbStr) {
        if (!StringUtils.isEmpty(rgbStr)) {
            String[] rgb = rgbStr.split(" ");
            if (rgb.length == 3) {
                int r = Integer.parseInt(rgb[0].trim());
                int g = Integer.parseInt(rgb[1].trim());
                int b = Integer.parseInt(rgb[2].trim());
                return rgbToHex(r, g, b);
            }
        }
        return rgbToHex(0, 0, 0);
    }

    public static String hexToRgbStr(String hexColor) {
        int[] rgb = hexToRgb(hexColor);
        if (rgb.length == 3) {
            return rgb[0] + " " + rgb[1] + " " + rgb[2];
        } else {
            return "0 0 0";
        }
    }

    public static int[] hexToRgb(String hexColor) {
        // 去除开头的 '#' 符号
        if (hexColor.startsWith("#")) {
            hexColor = hexColor.substring(1);
        }

        // 将十六进制字符串转换为整数
        int r = Integer.parseInt(hexColor.substring(0, 2), 16);
        int g = Integer.parseInt(hexColor.substring(2, 4), 16);
        int b = Integer.parseInt(hexColor.substring(4, 6), 16);

        return new int[]{r, g, b};
    }

    /**
     * 计算两个颜色的相似度
     *
     * @return 相似度，范围为0到1，1表示完全相同，0表示完全不同
     */
    private static double calculateColorSimilarity(int red1, int green1, int blue1, int red2, int green2, int blue2) {

        double distance = Math.sqrt(Math.pow(red1 - red2, 2) + Math.pow(green1 - green2, 2) + Math.pow(blue1 - blue2, 2));
        // 最大距离是RGB各分量的最大差值的平方和的平方根
        double maxDistance = Math.sqrt(Math.pow(255, 2) * 3);
        return 1 - (distance / maxDistance);
    }

    private static double calculateColorSimilarityLab(int red1, int green1, int blue1, int red2, int green2, int blue2) {
        double[] lab1 = rgbToLab(red1, green1, blue1);
        double[] lab2 = rgbToLab(red2, green2, blue2);
        double distance = Math.sqrt(Math.pow(lab2[0] - lab1[0], 2) +
                Math.pow(lab2[1] - lab1[1], 2) +
                Math.pow(lab2[2] - lab1[2], 2));
        double similarity = 1 - (distance / Math.sqrt(Math.pow(100, 2) + Math.pow(100, 2) + Math.pow(100, 2)));
        return similarity;
    }

    public static double[] rgbToLab(int r, int g, int b) {
        double[] lab = new double[3];
        // Normalize RGB values to [0, 1]
        double x = r / 255.0;
        double y = g / 255.0;
        double z = b / 255.0;
        // Apply transformation
        x = (x > 0.04045) ? Math.pow((x + 0.055) / 1.055, 2.4) : x / 12.92;
        y = (y > 0.04045) ? Math.pow((y + 0.055) / 1.055, 2.4) : y / 12.92;
        z = (z > 0.04045) ? Math.pow((z + 0.055) / 1.055, 2.4) : z / 12.92;
        // Calculate Lab values
        // L*
        lab[0] = (116 * y) - 16;
        // a*
        lab[1] = 500 * (x - y);
        // b*
        lab[2] = 200 * (y - z);
        return lab;
    }

    public static int hexColorToInt(String colorStr) throws IllegalArgumentException {
        // 去除可能包含的#号
        String formatted = colorStr.replaceFirst("#", "");
        int length = formatted.length();

        // 处理短格式颜色代码
        if (length == 3 || length == 4) {
            StringBuilder extended = new StringBuilder();
            for (char c : formatted.toCharArray()) {
                extended.append(c).append(c);
            }
            formatted = extended.toString();
            length = formatted.length();
        }

        // 添加默认Alpha通道（当只有RGB时）
        if (length == 6) {
            formatted = "FF" + formatted; // 添加完全不透明的Alpha通道
        } else if (length != 8) {
            throw new IllegalArgumentException("Invalid color format: " + colorStr);
        }

        try {
            // 转换为32位整数（包含ARGB四个通道）
            long longValue = Long.parseLong(formatted, 16);
            return (int) longValue;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid hex value: " + colorStr, e);
        }
    }

    public static int adjustBrightness(int color, float factor) {
        int red = Color.red(color);
        int green = Color.green(color);
        int blue = Color.blue(color);
        Log.d(TAG, "adjustBrightness: red=" + red + " ;green=" + green + " ;blue=" + blue);
        red = (int) Math.min(255, red * factor);
        green = (int) Math.min(255, green * factor);
        blue = (int) Math.min(255, blue * factor);
        return Color.argb(Color.alpha(color), red, green, blue);
    }

    // 3. 自定义S曲线调整
    public static Float sCurve(float x) {
        // 曲线强度
        float k = 0.6f;
        return (float) (1 / (1 + Math.pow(Math.E, -k * (x - 0.5))));
    }

    public static void main(String[] args) {
        System.out.println(ColorUtils.findNearColor(255, 7, 0));
        System.out.println(ColorUtils.findNearColor(255, 90, 3));
        System.out.println(ColorUtils.findNearColor(255, 235, 5));
        System.out.println(ColorUtils.findNearColor(0, 255, 5));
        System.out.println(ColorUtils.findNearColor(6, 251, 255));
        System.out.println(ColorUtils.findNearColor(5, 36, 255));
        System.out.println(ColorUtils.findNearColor(173, 41, 155));
    }

}