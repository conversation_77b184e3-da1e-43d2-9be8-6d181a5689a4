package com.bitech.vehiclesettings.utils;

import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.view.View;
import android.view.ViewGroup;

import com.bitech.vehiclesettings.R;

/**
 * 灰度处理.
 *
 * <AUTHOR>
 */
public class GrayEffectUtils {
    private static final String TAG = GrayEffectUtils.class.getSimpleName();

    public static void applyGrayEffect(ViewGroup container, int excludeId) {
        disableChildren(container, true, excludeId);
    }

    // 应用灰度效果
    public static void applyGrayEffect(ViewGroup container, boolean disabled) {
        disableChildren(container, disabled);
    }

    // 应用灰度效果
    public static void applyGrayEffect(ViewGroup container) {
        disableChildren(container, true);
    }

    public static void applyGrayEffect(View view, boolean disabled) {
        setGrayEffect(view, disabled);
    }

    public static void applyGrayEffect(View view) {
        setGrayEffect(view, true);
    }

    public static void applyGrayEffect(View view, float alphaScale) {
        setGrayEffect(view, true, alphaScale);
    }

    // 移除灰度效果
    public static void removeGrayEffect(ViewGroup container, int excludeId) {
        removeChildren(container, false, excludeId);
    }

    // 移除灰度效果
    public static void removeGrayEffect(ViewGroup container) {
        removeChildren(container, false);
    }

    public static void removeGrayEffect(View view) {
        removeGrayEffect(view, false);
    }

    private static void removeChildren(ViewGroup viewGroup, boolean disabled, int excludeId) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View child = viewGroup.getChildAt(i);
            int vId = child.getId();
            if (excludeId != vId) {
                if (child instanceof ViewGroup) {
                    // 递归处理子 ViewGroup
                    removeChildren((ViewGroup) child, disabled, excludeId);
                } else {
                    removeGrayEffect(child, disabled);
                }
            }

        }
        // 处理容器本身
        removeGrayEffect(viewGroup, disabled);
    }

    private static void disableChildren(ViewGroup viewGroup, boolean disabled, int excludeId) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View child = viewGroup.getChildAt(i);
            int vId = child.getId();
            if (excludeId != vId) {
                if (child instanceof ViewGroup) {
                    // 递归处理子 ViewGroup
                    disableChildren((ViewGroup) child, disabled, excludeId);
                } else {
                    setGrayEffect(child, disabled);
                }
            }

        }
        // 处理容器本身
        setGrayEffect(viewGroup, disabled);
    }

    private static void disableChildren(ViewGroup viewGroup, boolean disabled) {
        disableChildren(viewGroup, disabled, 0);
    }

    private static void setGrayEffect(View view, boolean disabled) {
        setGrayEffect(view, disabled, 0.75f);
    }
    private static void removeChildren(ViewGroup viewGroup, boolean disabled) {
        removeChildren(viewGroup, disabled, 0);
    }
    private static void removeGrayEffect(View view, boolean disabled) {
        // 恢复原始状态
        Object original = view.getTag(R.id.original_paint);
        if (original instanceof Integer) {
            view.setLayerType((Integer) original, null);
        }
        view.setTag(R.id.original_paint, null);

        // 设置点击状态
        view.setEnabled(!disabled);
        //view.setClickable(!disabled);
        //view.setFocusable(!disabled);
    }

    private static void setGrayEffect(View view, boolean disabled, float temp_alphaScale) {

        ColorMatrix matrix = new ColorMatrix();
        // 设置饱和度为0实现灰度 matrix.setSaturation(0);
        // 提高亮度
        float scale = 1.0f;
        // 降低Alpha
        float alphaScale = temp_alphaScale;
        matrix.setScale(scale, scale, scale, alphaScale);
        // 保存原始 Paint
        if (view.getTag(R.id.original_paint) == null) {
            view.setTag(R.id.original_paint, view.getLayerType());
        }

        // 应用滤镜
        Paint paint = new Paint();
        paint.setColorFilter(new ColorMatrixColorFilter(matrix));
        view.setLayerType(View.LAYER_TYPE_HARDWARE, paint);


        // 设置点击状态
        view.setEnabled(!disabled);

        //view.setClickable(!disabled);
        //view.setFocusable(!disabled);
    }
}
