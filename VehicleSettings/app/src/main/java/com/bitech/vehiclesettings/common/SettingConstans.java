package com.bitech.vehiclesettings.common;

public class SettingConstans {
    /**
     * 疲劳驾驶时间
     */
    public static final String URL_DRIVING_TIRED_TIME = "driving_tired_time";

    /**
     * 疲劳驾驶广播
     */
    public final static String SETTING_DRIVING_TIRED_ACTION
            = "com.chery.settings.DRIVING_TIRED_ACTION";

    /**
     * 疲劳驾驶广播key
     */
    public final static String SETTING_DRIVING_TIRED_KEY
            = "com.chery.settings.DRIVING_TIRED_KEY";

    public static final String VR_SPEAKER = "cerence_speaker";

    public static final int VR_SPEAKER_MAN = 1;
    public static final int VR_SPEAKER_WOMAN = 2;
    public static final int VR_SPEAKER_DEFAULT = VR_SPEAKER_WOMAN;

    public static final String VR_FREE_WAKE_UP_TIME = "wakeup_duration";
    public static final int VR_FREE_WAKE_UP_TIME_CLOSE = 0;
    public static final int VR_FREE_WAKE_UP_TIME_10 = 10;
    public static final int VR_FREE_WAKE_UP_TIME_15 = 15;
    public static final int VR_FREE_WAKE_UP_TIME_20 = 20;
    public static final int VR_FREE_WAKE_UP_TIME_DEFAULT = VR_FREE_WAKE_UP_TIME_10;

    public static final String VR_CUSTOM_WALKUP_WORD = "vr_custom_walkup_word";

    public static final String VR_OIL_QUANTITY_NOTIFY = "vr_oil_quantity_notify";

    public static final String VR_WELCOME = "vr_welcome";
    public static final String SOUND_ARKAYMYS = "sound_arkaymys";
    public static final String LINK_THEME_METER = "link_theme_meter";

    public static final int ENABLE = 1;
    public static final int DISABLE = 0;
    public static final int DEFAULT = ENABLE;

    public static final String CITOS_TIME = "citosTime";

    /**
     * 声源定位设置项 url
     */
    public static final String VR_URL_SOUND_LOCATION = "com.iflytek.autofly.IFLY_WOKE_MODE";

    /**
     * 语音唤醒开关
     */
    public static final String URL_VR_WAKEUP = "com.iflytek.autofly.IFLY_SPEECH_WAKE";

    /**
     * 语音播报显示开关
     */
    public static final String URL_SETTING_IFLY_SPEECH_TTS_ENABLE = "com.iflytek.autofly.SETTING_IFLY_SPEECH_TTS_ENABLE";

    /**
     * 语音唤醒提示语开关
     */
    public static final String URL_VR_WAKEUP_HINT = "cerence_vr_wakeup_hint";

    /**
     * 来电播报开关
     */
    public static final String URL_VR_INCOMING_CALL
            = "com.iflytek.autofly.SETTING_IFLY_PHONE_COMMING_TTS_ENABLE";

    /**
     * 语音设置项开关值
     */
    public static final int VR_SETTING_CLOSE = 0;
    public static final int VR_SETTING_OPEN = 1;
    public static final int VR_SETTING_DEFAULT = VR_SETTING_OPEN; // 默认打开


    /**
     * 声源定位设置项的值：
     */
    public static final int SOUND_LOCATION_OFF = 1; // 声源定位关闭
    public static final int SOUND_LOCATION_DRIVER = 2; // 声源定位仅响应主驾驶
    public static final int SOUND_LOCATION_AUTO = 0; // 声源定位自动模式
    public static final int SOUND_LOCATION_DEFAULT = SOUND_LOCATION_AUTO; //声源定位默认自动

    /**
     * 收音机区域
     */
    public final static int CHINA = 0x0;
    public final static int EUROPE = 0x1;//欧盟
    public final static int AMERICA = 0x2;//美国
    public final static int LATIN = 0x3;//拉丁
    public final static int BRAZIL = 0x4;//巴西

    // 车辆中心热点开关记忆KEY
    public final static String HOT_SPOT_STATUS_KEY = "com.chery.settings.WIFI_HOT_SPOT_STATUS_KEY";
    
    /**
     * 主题设置
     * 用于区分恢复出厂设置后，开机是否需要systemui切换主题为默认主题
     * 0:默认值，
     * 1:setting手动改过主题后设为该值
     * 2:systemui切换一次主题后设为该值
     */
    public static final String THEME_MODE_RESET = "com.android.systemui.theme.mode.reset";
    public final static int THEME_NOT_ACTIVE = 0x0;
    public final static int THEME_CUSTOM = 0x1;
    public final static int THEME_RESET = 0x2;
    public final static int CURRENT_LANGUAGE_DEFAULT = -1;
    //断开蓝牙连接
    public final static String BT_DISCONNECT_ACTION = "com.chery.settings.BT_DISCONNECT_ACTION";

    // 设置页面弹窗广播开启事件
    public final static String SETTING_DIALOG_SHOW_ACTION = "com.chery.settings.SHOW_DIALOG_ACTION";
    // 设置页面弹窗广播参数KEY
    public final static String SETTING_DIALOG_SHOW_KEY = "com.chery.settings.SHOW_DIALOG_KEY";
    // 设置页面弹窗广播关闭事件
    public final static String SETTING_DIALOG_DISMISS_ACTION
            = "com.chery.settings.DISMISS_DIALOG_ACTION";
    // 关闭设置页面弹窗广播参数KEY
    public final static String SETTING_DIALOG_DISMISS_KEY = "com.chery.settings.DISMISS_DIALOG_KEY";
    // 设置页面弹窗广播参数KEY对应的VALUE-打开或关闭息屏
    public final static int SETTING_DIALOG_STANDBY = 0;
    // 设置页面弹窗广播参数KEY对应的VALUE-打开或关闭屏幕清洁
    public final static int SETTING_DIALOG_CLEAN_SCREEN = 1;
    // 设置页面弹窗广播参数KEY对应的VALUE-打开音效设置弹窗
    public final static int SETTING_DIALOG_EQ_SETTING = 2;
    // 设置页面弹窗广播参数KEY对应的VALUE-打开cp&aa切换弹窗
    public final static int SETTING_DIALOG_CP_AA_SETTING = 3;
    // 设置页面弹窗广播参数KEY对应的VALUE-打开方向盘按键自定义弹窗
    public final static int SETTING_DIALOG_WHEEL_CUSTOM_SETTING = 4;
}
