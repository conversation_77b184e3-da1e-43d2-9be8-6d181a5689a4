package com.bitech.vehiclesettings.view.common;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Parcelable;
import android.os.SystemClock;
import android.util.AttributeSet;
import android.util.Log;
import android.view.animation.Interpolator;
import android.view.animation.LinearInterpolator;

import androidx.appcompat.widget.AppCompatImageView;

import com.bitech.vehiclesettings.R;

public class FadeBackgroundImageView extends AppCompatImageView {
    private boolean mFadeEnabled = false;
    private int mFadeThreshold = 300;
    private float mInitialAlpha = 1.0f;
    private boolean mReverseFade = true;
    private float mCurrentAlpha = 1.0f;
    private Interpolator mInterpolator = new LinearInterpolator();

    // 构造方法
    public FadeBackgroundImageView(Context context) {
        super(context);
        init(context, null);
    }

    public FadeBackgroundImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public FadeBackgroundImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.FadeBackgroundImageView);

            mFadeEnabled = a.getBoolean(R.styleable.FadeBackgroundImageView_fadeEnabled, true);
            mFadeThreshold = a.getDimensionPixelSize(R.styleable.FadeBackgroundImageView_fadeThreshold, 300);
            mInitialAlpha = a.getFloat(R.styleable.FadeBackgroundImageView_initialAlpha, 1.0f);
            mReverseFade = a.getBoolean(R.styleable.FadeBackgroundImageView_reverseFade, false);

            a.recycle();
        }

        mCurrentAlpha = mInitialAlpha;
        updateAlpha();
    }

    @Override
    public Parcelable onSaveInstanceState() {
        Bundle bundle = new Bundle();
        bundle.putParcelable("superState", super.onSaveInstanceState());
        bundle.putFloat("currentAlpha", mCurrentAlpha);
        return bundle;
    }

    @Override
    public void onRestoreInstanceState(Parcelable state) {
        if (state instanceof Bundle) {
            Bundle bundle = (Bundle) state;
            mCurrentAlpha = bundle.getFloat("currentAlpha", mInitialAlpha);
            super.onRestoreInstanceState(bundle.getParcelable("superState"));
            updateAlpha(); // 恢复保存的透明度
        } else {
            super.onRestoreInstanceState(state);
        }
    }

    /**
     * 处理滚动变化
     *
     * @param scrollY 当前垂直滚动位置
     */
    private long mLastUpdateTime = 0;
    private static final long THROTTLE_MS = 50;

    public void handleScroll(int scrollY) {
        // todo 待后续添加相关页面滚动隐藏车模逻辑
    }

    private void updateAlpha() {
//        Drawable drawable = getDrawable();
        Drawable background = getBackground();
        if (background != null) {
            background.setAlpha((int) (mCurrentAlpha * 255));
            invalidate();
        }
    }

    // ========== 公开API ==========

    public void setFadeEnabled(boolean enabled) {
        this.mFadeEnabled = enabled;
        if (!enabled) resetAlpha();
    }

    public void setFadeThreshold(int threshold) {
        this.mFadeThreshold = threshold;
    }

    public void setInitialAlpha(float alpha) {
        this.mInitialAlpha = alpha;
        resetAlpha();
    }

    public void setReverseFade(boolean reverse) {
        this.mReverseFade = reverse;
    }

    public void setFadeInterpolator(Interpolator interpolator) {
        this.mInterpolator = interpolator != null ? interpolator : new LinearInterpolator();
    }

    public void resetAlpha() {
        mCurrentAlpha = mInitialAlpha;
        updateAlpha();
    }

    public float getCurrentAlpha() {
        return mCurrentAlpha;
    }
}