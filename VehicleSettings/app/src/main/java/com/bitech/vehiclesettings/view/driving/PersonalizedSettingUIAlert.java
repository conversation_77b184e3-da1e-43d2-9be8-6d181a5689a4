package com.bitech.vehiclesettings.view.driving;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CompoundButton;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.databinding.DialogAlertDPersonalizedSetingBinding;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class PersonalizedSettingUIAlert extends BaseDialog {
    private static final String TAG = PersonalizedSettingUIAlert.class.getSimpleName();
    private static PersonalizedSettingUIAlert.onProgressChangedListener onProgressChangedListener;


    public PersonalizedSettingUIAlert(@NonNull Context context) {
        super(context);
    }

    public PersonalizedSettingUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PersonalizedSettingUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static PersonalizedSettingUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(PersonalizedSettingUIAlert.onProgressChangedListener onProgressChangedListener) {
        PersonalizedSettingUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private PersonalizedSettingUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertDPersonalizedSetingBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private PersonalizedSettingUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public PersonalizedSettingUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        // 驾驶模式
        private Integer drivingMode = 0;
        // 转向模式
        private Integer swerveMode = 0;
        // 悬架模式
        private Integer suspensionMode = 0;
        // 制动模式
        private Integer brakingMode = 0;
        // 保电电量
        private Integer powerProtection = 20;

        /**
         * Create the custom dialog
         */
        public PersonalizedSettingUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PersonalizedSettingUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertDPersonalizedSetingBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1992;
            layoutParams.height = 1170;
            window.setAttributes(layoutParams);

            // 驾驶模式
//            binding.spvDrivingPersonalizedMode.setItems(R.string.str_driving_mode_jj, R.string.str_driving_mode_bz, R.string.str_driving_car_mode_6_setting_movement, R.string.str_driving_mode_yx, R.string.str_driving_mode_yy, R.string.str_driving_mode_sd);
            binding.spvDrivingPersonalizedMode.setItems(R.string.str_driving_mode_jj, R.string.str_driving_mode_ss, R.string.str_driving_car_mode_6_setting_movement);

            // 驾驶模式监听
            binding.spvDrivingPersonalizedMode.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    drivingMode = index;
                }

                public void onItemClicked(int index, String text) {

                }
            });

            // 转向模式监听
            binding.llSwerveModeComfort.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    swerveMode = CarDriving.ICC_SteeringMode.NORMAL;
                    updateSwerveModeUI(CarDriving.FLZCU_SteeringMode.NORMAL);
                }
            });
            binding.llSwerveModeSport.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    swerveMode = CarDriving.ICC_SteeringMode.SPORT;
                    updateSwerveModeUI(CarDriving.FLZCU_SteeringMode.SPORT);
                }
            });
            binding.rbSwerveModeComfort.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    swerveMode = CarDriving.ICC_SteeringMode.NORMAL;
                    updateSwerveModeUI(CarDriving.FLZCU_SteeringMode.NORMAL);
                }
            });
            binding.rbSwerveModeSport.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    swerveMode = CarDriving.ICC_SteeringMode.SPORT;
                    updateSwerveModeUI(CarDriving.FLZCU_SteeringMode.SPORT);
                }
            });

            // 悬挂模式监听
            binding.llSuspensionComfort.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    suspensionMode = CarDriving.ICC_SuspensionMode.SOFT;
                    updateSuspensionModeUI(CarDriving.FLZCU_SuspensionDamping.SOFT);
                }
            });
            binding.llSuspensionSport.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    suspensionMode = CarDriving.ICC_SuspensionMode.HARD;
                    updateSuspensionModeUI(CarDriving.FLZCU_SuspensionDamping.HARD);
                }
            });
            binding.rbSuspensionComfort.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    suspensionMode = CarDriving.ICC_SuspensionMode.SOFT;
                    updateSuspensionModeUI(CarDriving.FLZCU_SuspensionDamping.SOFT);
                }
            });
            binding.rbSuspensionSport.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    suspensionMode = CarDriving.ICC_SuspensionMode.HARD;
                    updateSuspensionModeUI(CarDriving.FLZCU_SuspensionDamping.HARD);
                }
            });

            // 制动模式监听
            binding.llImmobilizeModeComfort.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    brakingMode = CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE;
                    updateBrakingModeUI(CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE);
                }
            });
            binding.llImmobilizeModeSport.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    brakingMode = CarDriving.FLZCU_BrakePedalFeelMode.SPORT;
                    updateBrakingModeUI(CarDriving.FLZCU_BrakePedalFeelMode.SPORT);
                }
            });
            binding.rbImmobilizeModeComfort.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    brakingMode = CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE;
                    updateBrakingModeUI(CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE);
                }
            });
            binding.rbImmobilizeModeSport.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    brakingMode = CarDriving.FLZCU_BrakePedalFeelMode.SPORT;
                    updateBrakingModeUI(CarDriving.FLZCU_BrakePedalFeelMode.SPORT);
                }
            });


            initData();

            // 保电电量监听
            binding.isbvIvDrivingRoate.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    powerProtection = progress + 20;
                    binding.tvDrivingPowerProtection.setText(progress + 20 + "%");
                }

                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {

                }

                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {

                }
            });

            // 取消按钮监听
            binding.btnCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });
            // 确认按钮监听
            binding.btnConfirm.setOnClickListener(v -> {
                // 驾驶模式
                onProgressChangedListener.onDrivingMode(drivingMode);
                // 转向模式
                onProgressChangedListener.setSwerveMode(swerveMode);
                // 悬架模式
                onProgressChangedListener.setSuspensionMode(suspensionMode);
                // 制动模式
                onProgressChangedListener.setBrakingMode(brakingMode);
                // 保电电量
                onProgressChangedListener.setPowerProtection(powerProtection);
                dialog.dismiss();
            });
            return dialog;
        }

        private void initData() {
            updateDrivingModeUI(onProgressChangedListener.getCarMode(), false);
            updateSuspensionModeUI(onProgressChangedListener.getSuspensionMode());
            updateSwerveModeUI(onProgressChangedListener.getSwerveMode());
            updateBrakingModeUI(onProgressChangedListener.getBrakingMode());
            updatePowerProtectionUI(onProgressChangedListener.getPowerProtection());
        }

        public void updateDrivingModeUI(Integer signalVal, boolean flag) {
            int state = 0;
            if (signalVal == CarDriving.FLZCU_PropulsionMode.COMFORTABLE) {
                state = 0;
            } else if (signalVal == CarDriving.FLZCU_PropulsionMode.NORMAL) {
                state = 1;
            } else if (signalVal == CarDriving.FLZCU_PropulsionMode.SPORT) {
                state = 2;
            } else if (signalVal == CarDriving.FLZCU_PropulsionMode.SNOW) {
                state = 3;
            }
            binding.spvDrivingPersonalizedMode.setSelectedIndex(state, flag);
        }

        public void updateSuspensionModeUI(Integer signalVal) {
            // 悬架模式
            binding.rbSuspensionComfort.setChecked(signalVal == CarDriving.FLZCU_SuspensionDamping.SOFT);
            binding.rbSuspensionSport.setChecked(signalVal == CarDriving.FLZCU_SuspensionDamping.HARD);
        }

        public void updateSwerveModeUI(Integer signalVal) {
            // 转向模式
            binding.rbSwerveModeComfort.setChecked(signalVal == CarDriving.FLZCU_SteeringMode.NORMAL);
            binding.rbSwerveModeSport.setChecked(signalVal == CarDriving.FLZCU_SteeringMode.SPORT);
        }

        public void updateBrakingModeUI(Integer signalVal) {
            // 制动模式
            binding.rbImmobilizeModeComfort.setChecked(signalVal == CarDriving.FLZCU_BrakePedalFeelMode.COMFORTABLE);
            binding.rbImmobilizeModeSport.setChecked(signalVal == CarDriving.FLZCU_BrakePedalFeelMode.SPORT);
        }

        public void updatePowerProtectionUI(Integer signalVal) {
            // 保电电量
            if (signalVal < 20 || signalVal > 80) {
                binding.isbvIvDrivingRoate.setProgress(0);
                binding.tvDrivingPowerProtection.setText("--%");
            } else {
                binding.isbvIvDrivingRoate.setProgress(signalVal - 20);
                binding.tvDrivingPowerProtection.setText(signalVal + "%");
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public void dismiss() {
            dialog.dismiss();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onDrivingMode(int status);

        void setPowerProtection(int status);

        // 转向模式
        void setSwerveMode(int status);

        // 悬挂模式
        void setSuspensionMode(int status);

        // 制动模式
        void setBrakingMode(int status);

        int getCarMode();

        Integer getSuspensionMode();

        Integer getSwerveMode();

        Integer getBrakingMode();

        Integer getPowerProtection();
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;
}
