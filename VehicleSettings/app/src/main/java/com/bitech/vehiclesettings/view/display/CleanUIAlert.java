package com.bitech.vehiclesettings.view.display;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.WindowManager;

import com.bitech.platformlib.interfaces.driving.IDrivingManagerListener;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.databinding.DialogAlertDisplayCleanBinding;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.quickcontrol.RearMirrorUIAlert;

public class CleanUIAlert {

    private static final String TAG = "CleanUIAlert";
    private final Context context;
    public final WindowManager windowManager;
    private View overlayView;
    private DialogAlertDisplayCleanBinding binding;
    DrivingManager drivingManager = DrivingManager.getInstance();
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final Runnable countdownRunnable = new Runnable() {
        @Override
        public void run() {
            count--;
            if (binding != null) {
                binding.tvCountdown.setText(String.valueOf(count));
            }
            if (count > 0) {
                handler.postDelayed(this, 1000);
            } else {
                Log.d(TAG, "完成倒计时");
                dismiss();
            }
        }
    };
    private int count = 3;
    private float downX, downY;
    private final int touchSlop;

    private static CleanUIAlert instance;

    public static CleanUIAlert getInstance(Context context) {
        if (instance == null) {
            instance = new CleanUIAlert(context.getApplicationContext());
        }
        return instance;
    }

    private OnBackGestureListener backGestureListener;

    public void setOnBackGestureListener(OnBackGestureListener listener) {
        this.backGestureListener = listener;
    }

    private void notifyInterceptBackGesture(boolean intercept) {
        if (backGestureListener != null) {
            backGestureListener.onInterceptBackGesture(intercept);
        }
    }


    public CleanUIAlert(Context context) {
        this.context = context.getApplicationContext();
        this.windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        this.touchSlop = ViewConfiguration.get(context).getScaledTouchSlop() * 4;
        drivingManager.addCallback("CleanUIAlert", new IDrivingManagerListener() {
            @Override
            public void onGearPositionChanged(int gearPosition) {
                if(gearPosition == Integer.MIN_VALUE){
                    return;
                }
                if(gearPosition != CarDriving.VCU_PRNDGearAct.P){
                    dismiss();
                }
            }
        });
        drivingManager.registerListener();
    }

    @SuppressLint("ClickableViewAccessibility")
    public void show() {
        if (overlayView != null) {
            return;
        }
        disableGesture();

        binding = DialogAlertDisplayCleanBinding.inflate(LayoutInflater.from(context));
        overlayView = binding.getRoot();

        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                2016,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                        | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                        | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                PixelFormat.TRANSLUCENT
        );

        binding.llHotArea.setOnTouchListener((v, event) -> {
            switch (event.getActionMasked()) {
                case MotionEvent.ACTION_DOWN:
                    downX = event.getX();
                    downY = event.getY();
                    Log.d(TAG, "触发触摸事件，开始倒计时");
                    startCountdown();
                    return true;

                case MotionEvent.ACTION_MOVE:
                    float dx = Math.abs(event.getX() - downX);
                    float dy = Math.abs(event.getY() - downY);
                    // 移动超过阈值，取消倒计时
                    if (dx > touchSlop || dy > touchSlop) {
                        Log.d(TAG, "移动事件，取消倒计时");
                        cancelCountdown();
                    }
                    // 屏蔽移动事件，返回true
                    return true;

                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    Log.d(TAG, "触摸事件丢失，取消倒计时");
                    cancelCountdown();
                    return true;
            }
            return false;
        });

        resetCountdown();
        notifyInterceptBackGesture(true);
        windowManager.addView(overlayView, params);
    }

    public void dismiss() {
        if (overlayView != null) {
            handler.removeCallbacks(countdownRunnable);
            if(overlayView.isAttachedToWindow()){
                windowManager.removeView(overlayView);
            }
            overlayView = null;
        }
        enableGesture();
        notifyInterceptBackGesture(false);
//        drivingManager.unregisterListener();
    }

    private void startCountdown() {
        resetCountdown();
        if (binding != null) {
            binding.progressBar.setProgress(100);
        }
        handler.postDelayed(countdownRunnable, 1000);
    }

    private void cancelCountdown() {
        handler.removeCallbacks(countdownRunnable);
        resetCountdown();
    }

    private void resetCountdown() {
        count = 3;
        if (binding != null) {
            binding.progressBar.setProgressClean();
            binding.tvCountdown.setText(String.valueOf(count));
        }
    }

    public interface OnBackGestureListener {
        void onInterceptBackGesture(boolean intercept);
    }

    public boolean isShowing() {
        return overlayView != null && overlayView.isAttachedToWindow();
    }


    private void disableGesture() {
        // 禁用手势
        Prefs.setSystemValue(PrefsConst.GlobalValue.HVAC_GESTURE,1);
    }
    private void enableGesture() {
        // 启用手势
        Prefs.setSystemValue(PrefsConst.GlobalValue.HVAC_GESTURE,0);
    }
}
