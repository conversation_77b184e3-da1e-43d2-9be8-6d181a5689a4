package com.bitech.vehiclesettings.ambientlightsdk.core;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Keep;

import com.bitech.vehiclesettings.ambientlightsdk.source.VisualizerManager;

@Keep
public class Ambientlightsdk implements VisualizerManager.MusicDataListener {

    private static final String TAG = "SDK TEST";

    private double currentFrequency;
    private int musicDB;
    private static final long FLASH_FREQ = 60;
    VisualizerManager mVisualizerManager = VisualizerManager.Companion.getInstance();
    Handler myHandler = new Handler(Looper.getMainLooper());
    final Runnable runnable;

    public Ambientlightsdk() {
        this.runnable = new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "myHandler, postDelayed, freq=  " + currentFrequency + ",musicDB = " + musicDB);
                MusicUtils.INSTANCE.sendMusicColor(currentFrequency, musicDB);
                myHandler.postDelayed(this, 60);
            }
        };
    }

    public void initSDK() {
        initVisualizer();
    }


    public void startSDK() {
        mVisualizerManager.setVisualizerEnable(true);
        MusicUtils.INSTANCE.sendMusicColor(currentFrequency, musicDB);
        myHandler.postDelayed(runnable, FLASH_FREQ);
    }

    public void closeSDK() {
        mVisualizerManager.setVisualizerEnable(false);
        if (runnable != null && myHandler != null) {
            myHandler.removeCallbacks(runnable);
        }
    }

    private void initVisualizer() {
        mVisualizerManager.initVisualizer();
        mVisualizerManager.registerMusicDataListener(this);
    }


    @Override
    public void onWaveChange(double freq) {
        currentFrequency = freq;
        Log.v(TAG, "onWaveFormDataCapture(), currentFrequency=" + currentFrequency);
    }

    @Override
    public void onFftChange(int db) {
        musicDB = db;
        Log.v(TAG, "onFftChange(), musicDB=" + db);
    }

    @Override
    public void forceDestroy() {
        Log.i(TAG, "forceDestroy(), ==============================");
        myHandler.removeCallbacksAndMessages(null);
    }


}
