package com.bitech.vehiclesettings.bean;
public class LauncherWallpaperInfo {
    private int mId;
    private String mWallpaperId;
    private String mName;
    private String mFrom;
    private int mHide; // 1 隐藏，非1为未隐藏
    private int mFlag; // 1 默认，非1自定义
    private int mRank;
    private String mUri = null;
    public LauncherWallpaperInfo(
            int id, String wallpaperId, String name, String from, int hide, int flag, int rank, String uri) {
        mId = id;
        mWallpaperId = wallpaperId;
        mName = name;
        mFrom = from;
        mHide = hide;
        mFlag = flag;
        mRank = rank;
        mUri = uri;
    }
    public int getId() {
        return mId;
    }
    public String getWallpaperId() {
        return mWallpaperId;
    }
    public String getName() {
        return mName;
    }
    public String getFrom() {
        return mFrom;
    }
    public int getHide() {
        return mHide;
    }
    public void setHide(int hide) {
        mHide = hide;
    }
    public int getFlag() {
        return mFlag;
    }
    public int getRank() {
        return mRank;
    }
    public boolean isDefault() {
        return mFlag == 1;
    }
    public boolean isHide() {
        return mHide == 1;
    }
    public void setUri(String uri){
        this.mUri = uri;
    }
    public String getUri() {
        return mUri;
    }
    @Override
    public String toString() {
        return "LauncherWallpaperInfo{"
                + "mId=" + mId
                + ", mWallpaperId='" + mWallpaperId + '\''
                + ", mName='" + mName + '\''
                + ", mFrom='" + mFrom + '\''
                + ", mHide=" + mHide
                + ", mFlag=" + mFlag
                + ", mRank=" + mRank
                + '}';
    }
}
