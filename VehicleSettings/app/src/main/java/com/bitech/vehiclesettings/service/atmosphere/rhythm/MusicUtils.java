package com.bitech.vehiclesettings.service.atmosphere.rhythm;

import android.content.Intent;

import com.blankj.utilcode.util.Utils;

/**
 * 律动算法-颜色索引匹配
 */
public class MusicUtils {

    // 单例实现
    private static volatile MusicUtils instance;

    // 常量定义
    public static final String ACTION_MUSIC_MODEL = "ACTION_MUSIC_MODEL";
    public static final String ACTION_MUSIC_MODEL_STATUS = "ACTION_MUSIC_MODEL_STATUS";

    private MusicUtils() {
    }

    public static MusicUtils getInstance() {
        if (instance == null) {
            synchronized (MusicUtils.class) {
                if (instance == null) {
                    instance = new MusicUtils();
                }
            }
        }
        return instance;
    }

    public void sendMusicModelStatus(boolean isOpen) {
        Intent intent = new Intent();
        intent.setAction(ACTION_MUSIC_MODEL);
        intent.putExtra(ACTION_MUSIC_MODEL_STATUS, isOpen);
        Utils.getContext().sendBroadcast(intent);
    }

    public synchronized void sendMusicColor(double currentFrequency, int currentDb) {
        // 计算光照等级
        int lightLevel = currentDb - 20;
        if (lightLevel < 5) {
            lightLevel = 5;
        } else if (lightLevel > 100) {
            lightLevel = 100;
        }

        // 颜色索引匹配
        int colorIndex = 0;
        if (currentFrequency >= 0.0 && currentFrequency <= 23.2) {
            colorIndex = 137;
        } else if (currentFrequency >= 23.3 && currentFrequency <= 26.0) {
            colorIndex = 117;
        } else if (currentFrequency >= 26.1 && currentFrequency <= 29.2) {
            colorIndex = 159;
        } else if (currentFrequency >= 29.3 && currentFrequency <= 31.8) {
            colorIndex = 179;
        } else if (currentFrequency >= 31.9 && currentFrequency <= 34.7) {
            colorIndex = 75;
        } else if (currentFrequency >= 34.8 && currentFrequency <= 39.0) {
            colorIndex = 85;
        } else if (currentFrequency >= 39.1 && currentFrequency <= 42.4) {
            colorIndex = 95;
        } else if (currentFrequency >= 42.5 && currentFrequency <= 46.3) {
            colorIndex = 136;
        } else if (currentFrequency >= 46.4 && currentFrequency <= 52.0) {
            colorIndex = 116;
        } else if (currentFrequency >= 52.1 && currentFrequency <= 58.4) {
            colorIndex = 158;
        } else if (currentFrequency >= 58.5 && currentFrequency <= 63.6) {
            colorIndex = 178;
        } else if (currentFrequency >= 63.7 && currentFrequency <= 69.4) {
            colorIndex = 74;
        } else if (currentFrequency >= 69.5 && currentFrequency <= 77.9) {
            colorIndex = 84;
        } else if (currentFrequency >= 78.0 && currentFrequency <= 84.9) {
            colorIndex = 94;
        } else if (currentFrequency >= 85.0 && currentFrequency <= 92.7) {
            colorIndex = 135;
        } else if (currentFrequency >= 92.8 && currentFrequency <= 104.0) {
            colorIndex = 115;
        } else if (currentFrequency >= 104.1 && currentFrequency <= 116.7) {
            colorIndex = 157;
        } else if (currentFrequency >= 116.8 && currentFrequency <= 127.1) {
            colorIndex = 177;
        } else if (currentFrequency >= 127.2 && currentFrequency <= 138.8) {
            colorIndex = 73;
        } else if (currentFrequency >= 138.9 && currentFrequency <= 155.8) {
            colorIndex = 83;
        } else if (currentFrequency >= 155.9 && currentFrequency <= 169.7) {
            colorIndex = 93;
        } else if (currentFrequency >= 169.8 && currentFrequency <= 185.3) {
            colorIndex = 134;
        } else if (currentFrequency >= 185.4 && currentFrequency <= 208.0) {
            colorIndex = 114;
        } else if (currentFrequency >= 208.1 && currentFrequency <= 233.5) {
            colorIndex = 156;
        } else if (currentFrequency >= 233.6 && currentFrequency <= 254.3) {
            colorIndex = 176;
        } else if (currentFrequency >= 254.4 && currentFrequency <= 277.6) {
            colorIndex = 72;
        } else if (currentFrequency >= 277.7 && currentFrequency <= 311.6) {
            colorIndex = 82;
        } else if (currentFrequency >= 311.7 && currentFrequency <= 339.4) {
            colorIndex = 92;
        } else if (currentFrequency >= 339.5 && currentFrequency <= 370.6) {
            colorIndex = 133;
        } else if (currentFrequency >= 370.7 && currentFrequency <= 416.0) {
            colorIndex = 113;
        } else if (currentFrequency >= 416.1 && currentFrequency <= 466.9) {
            colorIndex = 155;
        } else if (currentFrequency >= 467.0 && currentFrequency <= 508.6) {
            colorIndex = 175;
        } else if (currentFrequency >= 508.7 && currentFrequency <= 555.3) {
            colorIndex = 71;
        } else if (currentFrequency >= 555.4 && currentFrequency <= 623.3) {
            colorIndex = 81;
        } else if (currentFrequency >= 623.4 && currentFrequency <= 678.8) {
            colorIndex = 91;
        } else if (currentFrequency >= 678.9 && currentFrequency <= 741.2) {
            colorIndex = 132;
        } else if (currentFrequency >= 741.3 && currentFrequency <= 832.0) {
            colorIndex = 112;
        } else if (currentFrequency >= 832.1 && currentFrequency <= 933.9) {
            colorIndex = 154;
        } else if (currentFrequency >= 934.0 && currentFrequency <= 1017.1) {
            colorIndex = 174;
        } else if (currentFrequency >= 1017.2 && currentFrequency <= 1110.6) {
            colorIndex = 70;
        } else if (currentFrequency >= 1110.7 && currentFrequency <= 1246.6) {
            colorIndex = 80;
        } else if (currentFrequency >= 1246.7 && currentFrequency <= 1357.7) {
            colorIndex = 90;
        } else if (currentFrequency >= 1357.8 && currentFrequency <= 1482.4) {
            colorIndex = 131;
        } else if (currentFrequency >= 1482.5 && currentFrequency <= 1664.0) {
            colorIndex = 111;
        } else if (currentFrequency >= 1664.1 && currentFrequency <= 1867.8) {
            colorIndex = 153;
        } else if (currentFrequency >= 1867.9 && currentFrequency <= 2034.3) {
            colorIndex = 173;
        } else if (currentFrequency >= 2034.4 && currentFrequency <= 2221.1) {
            colorIndex = 69;
        } else if (currentFrequency >= 2221.2 && currentFrequency <= 2493.1) {
            colorIndex = 79;
        } else if (currentFrequency >= 2493.2 && currentFrequency <= 2715.4) {
            colorIndex = 89;
        } else if (currentFrequency >= 2715.5 && currentFrequency <= 2964.8) {
            colorIndex = 130;
        } else if (currentFrequency >= 2964.9 && currentFrequency <= 3328.0) {
            colorIndex = 110;
        } else if (currentFrequency >= 3328.1 && currentFrequency <= 3735.5) {
            colorIndex = 152;
        } else if (currentFrequency >= 3735.6 && currentFrequency <= 4068.5) {
            colorIndex = 172;
        } else if (currentFrequency >= 4068.6 && currentFrequency <= 4442.3) {
            colorIndex = 68;
        } else if (currentFrequency >= 4442.4 && currentFrequency <= 4986.3) {
            colorIndex = 78;
        } else {
            // 处理未匹配的情况（根据需求设置默认值）
            colorIndex = 0;
        }

        // 这里应该添加发送颜色的逻辑
        // 例如：发送广播或调用硬件接口
        // 示例代码可能需要补充具体实现
    }
}