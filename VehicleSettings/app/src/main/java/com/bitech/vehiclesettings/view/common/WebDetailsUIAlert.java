package com.bitech.vehiclesettings.view.common;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebSettings;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertRDetailsBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertWebDetailsBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class WebDetailsUIAlert extends BaseDialog {
    private static final String TAG = WebDetailsUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public WebDetailsUIAlert(@NonNull Context context) {
        super(context);
    }

    public WebDetailsUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected WebDetailsUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        WebDetailsUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertWebDetailsBinding binding;
        private boolean isBlueOpen = false;
        private WebDetailsUIAlert dialog = null;
        private View layout;
        WindowManager.LayoutParams layoutParams;
        private final String webRoot = "file:///android_asset/";

        public Builder(Context context) {
            this.context = context;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public WebDetailsUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }

        public WebDetailsUIAlert create() {
            return create("标题", "", 1128, 428);
        }

        public WebDetailsUIAlert create(String title, String contentPath, int width, int height) {
            if (dialog == null)
                dialog = new WebDetailsUIAlert(context, R.style.Dialog);
            dialog.setCancelable(isCan);

            binding = DialogAlertWebDetailsBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            layoutParams = window.getAttributes();
            setSize(width, height);
            window.setAttributes(layoutParams);
            // 设置显示文本
            setText(title, webRoot + contentPath);
            // 默认正文不可滚动
            setScrollable(false);
            return dialog;
        }

        public void setPadding(int size) {
            binding.scrollView.setPadding(size, 0, size, 0);
        }

        public void setScrollable(boolean flag) {
            binding.scrollView.setScrollable(flag);
        }

        public void setSize(int width, int height) {
            layoutParams.width = width;
            layoutParams.height = height;
        }

        public void setText(String title, String contentPath) {
            binding.tvTitle.setText(title);

            // 启用 JavaScript（如果 HTML 文件中有需要执行的 JavaScript）
            WebSettings webSettings = binding.tvContent.getSettings();
            webSettings.setJavaScriptEnabled(true);

            // 设置 WebViewClient，以便打开链接时不跳转到外部浏览器
            binding.tvContent.setWebViewClient(new WebViewClient());

            // 加载 assets 文件夹中的 HTML 文件
            binding.tvContent.loadUrl(contentPath);
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }
}
