package com.bitech.vehiclesettings.service.earlywarning;

import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

/**
 * 倒车辅助预警
 */
public class ReverseRadarService {
    private static volatile ReverseRadarService instance;
    private static final String TAG = ReverseRadarService.class.getSimpleName();
    private static final long TARGET_INTERVAL_MS = 250;
    private Handler mHandler;
    private Runnable mTaskRunnable;
    private volatile boolean isRunning = false;
    private long mLastExecutionTime;
    int count = 0;

    public static ReverseRadarService getInstance() {
        if (instance == null) {
            synchronized (ReverseRadarService.class) {
                if (instance == null) {
                    instance = new ReverseRadarService();
                }
            }
        }
        return instance;
    }

    public ReverseRadarService() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public void start() {
        if (isRunning) {
            return;
        }
        isRunning = true;
        mLastExecutionTime = SystemClock.elapsedRealtime();
        mTaskRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isRunning) {
                    return;
                }
                // 1. 执行任务
                doTask();
                // 2. 计算下一次执行时间（动态补偿误差）
                long currentTime = SystemClock.elapsedRealtime();
                long elapsed = currentTime - mLastExecutionTime;
                long nextDelay = Math.max(0, TARGET_INTERVAL_MS - elapsed);
                // 3. 递归调度下一次任务
                if (count == 1) {
                    mHandler.postDelayed(this, nextDelay - 200);
                } else {
                    mHandler.postDelayed(this, nextDelay);
                }
                mLastExecutionTime = currentTime + nextDelay;
            }
        };
        mHandler.post(mTaskRunnable);
    }

    public void stop() {
        if (mTaskRunnable != null) {
            mHandler.removeCallbacks(mTaskRunnable);
        }
        isRunning = false;
    }

    private void doTask() {
        Log.d(TAG, "[倒车辅助预警] doTask: ");

    }
}
