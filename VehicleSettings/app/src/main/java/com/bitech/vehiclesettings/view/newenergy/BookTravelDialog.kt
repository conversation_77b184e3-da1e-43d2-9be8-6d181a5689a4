package com.bitech.vehiclesettings.view.newenergy

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.adapter.WeekTextAdapter
import com.bitech.vehiclesettings.databinding.DialogBookTravelBinding
import com.bitech.vehiclesettings.utils.BitUtils
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel.Companion.formatWithZero
import com.shawnlin.numberpicker.NumberPicker
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * @Description: 预约出行
 **/
class BookTravelDialog(context: Context, val week: Int, val hour: Int, val minute: Int) :
    BaseDialog(context) {

    private lateinit var binding: DialogBookTravelBinding
    private var bookTravelCallback: OnBookTravelCallback? = null
    private var titleText: String = context.getString(R.string.app_name)

    private var gravity = Gravity.CENTER
    private var y = 0
    private var width = 1584
    private var height = 1123

    private var prevAmPmValue = 0
    private val is24Hour = is24HourFormat(context)

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : week = $week, hour = $hour, minute = $minute")
        binding = DialogBookTravelBinding.bind(
            layoutInflater.inflate(R.layout.dialog_book_travel, null)
        )
        setContentView(binding.root)
        // 初始化视图
        initView()
        initData()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        // 设置对话框窗口属性
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG
        attributes?.width = width
        attributes?.height = height
        attributes?.gravity = gravity
        if (y != 0) {
            attributes?.y = y
        }
        window?.attributes = attributes

        binding.rvRepeatWeek.layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        val weekTextAdapter = WeekTextAdapter()
        weekTextAdapter.setOnItemClickListener { adapter, _, position ->
            val item = adapter.getItem(position)
            LogUtil.d(TAG, "weekTextAdapter.setOnItemClickListener : position = $position, item = $item")
            if (item != null) {
                item.isChecked = !item.isChecked
                adapter.notifyItemChanged(position)
            }
        }
        binding.rvRepeatWeek.adapter = weekTextAdapter
        weekTextAdapter.submitList(
            listOf(
                WeekTextAdapter.WeekText("周一", BitUtils.getBit(week, 0) == 1),
                WeekTextAdapter.WeekText("周二", BitUtils.getBit(week, 1) == 1),
                WeekTextAdapter.WeekText("周三", BitUtils.getBit(week, 2) == 1),
                WeekTextAdapter.WeekText("周四", BitUtils.getBit(week, 3) == 1),
                WeekTextAdapter.WeekText("周五", BitUtils.getBit(week, 4) == 1),
                WeekTextAdapter.WeekText("周六", BitUtils.getBit(week, 5) == 1),
                WeekTextAdapter.WeekText("周日", BitUtils.getBit(week, 6) == 1)
            )
        )
    }

    private fun initData() {
        var hour = this.hour
        val minute = this.minute
        // 预约升级的时间区间
        val startUpdateTime = LocalTime.of(20, 30)
        val endUpdateTime = LocalTime.of(22, 30)
        // 时间格式化为 HH:mm
        val formatter = DateTimeFormatter.ofPattern("HH:mm")

        binding.apply {
            tvTitle.text = titleText
            tvTips.text = context.getString(
                R.string.ne_book_trip_error,
                "${startUpdateTime.format(formatter)}-${endUpdateTime.format(formatter)}"
            )
            tvTips.visibility = View.GONE

            //上午/下午
            npAmPm.visibility = if (is24Hour) View.GONE else View.VISIBLE
            npAmPm.value = if (!is24Hour) 1 else 0 // 初始AM/PM值
            prevAmPmValue = npAmPm.value // 初始化：记录初始AM/PM值

            npAmPm.minValue = 0
            npAmPm.maxValue = 1
            npAmPm.displayedValues = arrayOf(
                context.getString(R.string.ne_am),
                context.getString(R.string.ne_pm)
            )
            npAmPm.value = if (!is24Hour) 1 else 0
            npHour.minValue = 0
            npHour.maxValue = 23
            npHour.displayedValues = if (is24Hour) {
                // 24小时制 对应00
                (0..23).map { it.formatWithZero() }.toTypedArray()
            } else {
                //用24个显示数字的数组 AM 0-11  PM 12-23
                val amHours =
                    listOf("12") + (1..11).map { it.formatWithZero() } // AM: 12,01-11（索引0-11）
                val pmHours =
                    listOf("12") + (1..11).map { it.formatWithZero() } // PM: 12,01-11（索引12-23）
                (amHours + pmHours).toTypedArray()
            }
            npHour.value = hour
            //分钟
            npMinute.minValue = 0
            npMinute.maxValue = 59
            npMinute.value = minute
            //确定按钮
//            binding.enableConfirm = !isCurrentTimeInRange(startUpdateTime, endUpdateTime)
            binding.enableConfirm = true

            //监控AM PM切换 调整对应小时为AM/PM索引 防止切换后 滑动小时 am pm回位
            npAmPm.setOnScrollListener { _, scrollState ->
                if (scrollState == NumberPicker.OnScrollListener.SCROLL_STATE_IDLE && !is24Hour) {
                    val currentAmPm = npAmPm.value
                    if (currentAmPm != prevAmPmValue) { // AM/PM发生切换
                        npHour.value = when (currentAmPm) {
                            1 -> npHour.value + 12 // AM→PM：索引+12
                            0 -> npHour.value - 12 // PM→AM：索引-12
                            else -> npHour.value
                        }
                        prevAmPmValue = currentAmPm // 更新记录值
                    }
                }
            }

            npHour.setOnScrollListener { _, scrollState ->
                if (scrollState == NumberPicker.OnScrollListener.SCROLL_STATE_IDLE) {
                    val currentIndex = npHour.value
                    if (!is24Hour) {
                        npAmPm.value =
                            if (currentIndex < 12) 0 else 1 // 0=AM（0-11 display显示数组中AM下标范围） 1=PM（12-23 display显示数组中PM下标范围）
                    }
                }
            }
            npMinute.setOnScrollListener { _, scrollState ->
                if (scrollState == NumberPicker.OnScrollListener.SCROLL_STATE_IDLE) {
                    LogUtil.d(TAG, "npMinute value = ${npMinute.value}")
//                    binding.enableConfirm = !isCurrentTimeInRange(startUpdateTime, endUpdateTime)
                }
            }


            //取消/确定
            btnCancel.setOnClickListener { dismiss() }
            btnConfirm.setOnClickListener {
                var npHourValue = binding.npHour.value
                val npMinuteValue = binding.npMinute.value
                val npAmPmValue = binding.npAmPm.value

                //最⾼位（最左侧）预留发默认值0，第二⾼位到最后⼀位依次表⽰周⽇到周⼀
                val weekString = StringBuilder("00000000")
                binding.rvRepeatWeek.adapter?.let { adapter ->
                    val adapter1 = adapter as WeekTextAdapter
                    adapter1.items.asReversed().forEachIndexed { index, item ->
                        //index从1开始
                        weekString.setCharAt(index + 1, if (item.isChecked) '1' else '0')
                        LogUtil.d(TAG, "weekString asReversed Index $index = ${item.isChecked}")
                    }
                }
                val weekValue = weekString.toString().toInt(radix = 2)
                LogUtil.d(
                    TAG,
                    "btnConfirm click weekStr=$weekString weekValue=$weekValue hour=$npHourValue minute=$npMinuteValue"
                )
                bookTravelCallback?.onConfirmBookTravel(weekValue, npHourValue, npMinuteValue)
                dismiss()
            }

        }
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    fun setTitle(title: String) {
        titleText = title
    }

    fun setDialogGravity(gravity: Int) {
        this.gravity = gravity
    }

    fun setDialogWidthHeight(width: Int, height: Int, y: Int) {
        this.width = width
        this.height = height
        this.y = y
    }

    /**
     * 判断当前时间是否处于指定的时间区间内
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 如果当前时间在区间内返回 true，否则返回 false
     */
    fun isCurrentTimeInRange(startTime: LocalTime, endTime: LocalTime): Boolean {
        var npHourValue = binding.npHour.value
        var npMinuteValue = binding.npMinute.value
        if (!is24Hour) {
            val npAmPmValue = binding.npAmPm.value
            if (npHourValue == 12) {
                npHourValue = if (npAmPmValue == 1) 12 else 0
            } else {
                if (npAmPmValue == 1) {
                    npHourValue += 12
                }
            }
        }
        val selectedTime = LocalTime.of(npHourValue, npMinuteValue)
        val inRange = if (startTime.isBefore(endTime)) {
            selectedTime.isAfter(startTime) && selectedTime.isBefore(endTime)
        } else {
            // 处理跨天的情况
            selectedTime.isAfter(startTime) || selectedTime.isBefore(endTime)
        }
        LogUtil.d(TAG, "isCurrentTimeInRange : selectedTime = $selectedTime isInRange = $inRange")
        return inRange
    }

    fun setDialogClickCallback(callback: OnBookTravelCallback) {
        bookTravelCallback = callback
    }

    interface OnBookTravelCallback {
        fun onConfirmBookTravel(week: Int, hour: Int, minute: Int)
    }

    companion object {
        // 日志标志位
        private const val TAG = "BookTravelDialog"

        fun is24HourFormat(context: Context?): Boolean {
            return android.text.format.DateFormat.is24HourFormat(context)
        }

    }
}
