package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertRDetailsBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSBasicPravicyAgreementBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class BasicPrivacyAgreementUIAlert extends BaseDialog {
    private static final String TAG = BasicPrivacyAgreementUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public BasicPrivacyAgreementUIAlert(@NonNull Context context) {
        super(context);
    }

    public BasicPrivacyAgreementUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected BasicPrivacyAgreementUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        BasicPrivacyAgreementUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSBasicPravicyAgreementBinding binding;
        private boolean isBlueOpen = false;
        public BasicPrivacyAgreementUIAlert dialog = null;
        private View layout;
        WindowManager.LayoutParams layoutParams;

        public Builder(Context context) {
            this.context = context;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public BasicPrivacyAgreementUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }

        public BasicPrivacyAgreementUIAlert create() {
            if (dialog == null)
                dialog = new BasicPrivacyAgreementUIAlert(context, R.style.Dialog);
            dialog.setCancelable(isCan);

            binding = DialogAlertSBasicPravicyAgreementBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1912;
            layoutParams.height = 1080;
            window.setAttributes(layoutParams);

            // 初始化标题
            initTitle();
            // 初始化网页
//            initWeb();

            return dialog;
        }

        private void initTitle() {
            binding.tvTitle.setText(context.getResources().getString(
                    R.string.str_system_permission_basic_privacy_statement_title
            ));
        }

        @SuppressLint("SetJavaScriptEnabled")
        private void initWeb() {
            // 启用 JS
            WebSettings webSettings = binding.webView.getSettings();
            webSettings.setJavaScriptEnabled(true);

            // 加载网页（远程或本地）
            binding.webView.loadUrl("https://www.example.com"); // 远程网页
            binding.webView.setWebViewClient(new WebViewClient() {
                @Override
                public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                    super.onReceivedError(view, request, error);
                    // 隐藏WebView，显示错误文本
                    binding.webView.setVisibility(View.GONE);
                    binding.errorText.setVisibility(View.VISIBLE);
                }

                @Override
                public void onPageStarted(WebView view, String url, Bitmap favicon) {
                    super.onPageStarted(view, url, favicon);
                    // 开始加载时隐藏错误文本
                    binding.errorText.setVisibility(View.GONE);
                    binding.webView.setVisibility(View.VISIBLE);
                }
            });
//             binding.webView.loadUrl("file:///android_asset/your_page.html"); // 本地网页

            webSettings.setJavaScriptEnabled(true);
            webSettings.setBuiltInZoomControls(true);      // 支持缩放
            webSettings.setDisplayZoomControls(false);     // 不显示缩放按钮
            webSettings.setDomStorageEnabled(true);        // 支持 HTML5 本地存储

            binding.webView.setWebViewClient(new WebViewClient()); // 内部跳转不跳系统浏览器
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }
}
