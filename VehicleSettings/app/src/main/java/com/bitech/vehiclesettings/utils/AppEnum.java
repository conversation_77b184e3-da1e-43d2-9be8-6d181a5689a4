package com.bitech.vehiclesettings.utils; /**
 * Copyright (C), 2015-2024, XXX有限公司
 * FileName: AppLogEnum
 * Author: WUY1WHU
 * Date: 2024/6/18 17:18
 * Description:
 * History:
 * <author> <time> <version> <desc>
 * 作者姓名 修改时间 版本号 描述
 */

import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.blankj.utilcode.util.StringUtils;

/**
 * FileName: AppLogEnum
 * Author: WUY1WHU
 * Date: 2024/6/18 17:18
 * Description:
 * History:
 */
public class AppEnum {


    /**
     * 单色.
     */
    public enum Color {
        // 单色
        RED(0, MyApplication.getContext().getString(R.string.str_red)),
        ORANGE(1, MyApplication.getContext().getString(R.string.str_orange)),
        YELLOW(2, MyApplication.getContext().getString(R.string.str_yellow)),
        GREEN(3, MyApplication.getContext().getString(R.string.str_green)),
        YOUNG(4, MyApplication.getContext().getString(R.string.str_cyan)),
        BLUE(5, MyApplication.getContext().getString(R.string.str_blue)),
        PURPLE(6, MyApplication.getContext().getString(R.string.str_purple)),
        OTHERS(7, "未知颜色");
        private Integer value;
        private String name;

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        Color(Integer value, String label) {
            this.value = value;
            this.name = label;

        }

        public static String getByValue(int value) {
            for (Color e : values()) {
                if (e.getValue() == value) {
                    return e.name;
                }
            }
            return "";
        }

        public static Integer getByName(String name) {
            for (Color e : values()) {
                if (!StringUtils.isEmpty(e.getName()) && e.getName().equalsIgnoreCase(name)) {
                    return e.value;
                }
            }
            return null;
        }
    }

    /**
     * 单色.
     */
    public enum Scene {
        // 单色
        STATIC_SINGLE(0, MyApplication.getContext().getString(R.string.str_scene_static_single)),
        STATCIC_MUTI(1, MyApplication.getContext().getString(R.string.str_scene_static_muti)),
        STATCIC_CUST(2, MyApplication.getContext().getString(R.string.str_scene_static_customize)),
        BREATHE(3, MyApplication.getContext().getString(R.string.str_scene_breathe)),
        GRADIENT(4, MyApplication.getContext().getString(R.string.str_scene_gradient)),
        RHYTHM(5, MyApplication.getContext().getString(R.string.str_scene_rhythm));
        private Integer value;
        private String name;

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        Scene(Integer value, String label) {
            this.value = value;
            this.name = label;

        }

        public static String getByValue(int value) {
            for (Scene e : values()) {
                if (e.getValue() == value) {
                    return e.name;
                }
            }
            return "";
        }

        public static Integer getByName(String name) {
            for (Scene e : values()) {
                if (!StringUtils.isEmpty(e.getName()) && e.getName().equalsIgnoreCase(name)) {
                    return e.value;
                }
            }
            return null;
        }
    }

    public enum ambientLightColor {
        COLOR0(0x0, "0 0 0"),
        COLOR1(0X01, "255 0 0"),
        COLOR2(0x02, "255 0 0"),
        COLOR3(0x03, "255 40 0"),
        COLOR4(0x04, "255 71 0"),
        COLOR5(0x05, "255 91 0"),
        COLOR6(0x06, "255 110 14"),
        COLOR7(0x07, "255 127 24"),
        COLOR8(0x08, "255 145 30"),
        COLOR9(0x09, "255 164 37"),
        COLOR10(0x0A, "255 163 44"),
        COLOR11(0x0B, "255 206 52"),
        COLOR12(0x0C, "255 231 58"),
        COLOR13(0x0D, "249 255 66"),
        COLOR14(0x0E, "216 255 67"),
        COLOR15(0x0F, "184 255 69"),
        COLOR16(0x10, "147 255 69"),
        COLOR17(0x11, "104 255 70"),
        COLOR18(0x12, "0 255 71"),
        COLOR19(0x13, "0 255 71"),
        COLOR20(0x14, "0 255 71"),
        COLOR21(0x15, "0 255 72"),
        COLOR22(0x16, "0 255 72"),
        COLOR23(0x17, "0 255 91"),
        COLOR24(0x18, "0 255 107"),
        COLOR25(0x19, "0 255 122"),
        COLOR26(0x1A, "0 255 136"),
        COLOR27(0x1B, "0 255 150"),
        COLOR28(0x1C, "0 255 163"),
        COLOR29(0x1D, "0 255 177"),
        COLOR30(0x1E, "0 255 192"),
        COLOR31(0x1F, "0 255 206"),
        COLOR32(0x20, "0 255 222"),
        COLOR33(0x21, "0 255 239"),
        COLOR34(0x22, "0 254 255"),
        COLOR35(0x23, "0 235 255"),
        COLOR36(0x24, "0 219 255"),
        COLOR37(0x25, "0 201 255"),
        COLOR38(0x26, "0 183 255"),
        COLOR39(0x27, "0 166 255"),
        COLOR40(0x28, "0 149 255"),
        COLOR41(0x29, "0 129 255"),
        COLOR42(0x2A, "0 108 255"),
        COLOR43(0x2B, "54 82 255"),
        COLOR44(0x2C, "72 89 255"),
        COLOR45(0x2D, "121 87 255"),
        COLOR46(0x2E, "155 83 255"),
        COLOR47(0x2F, "183 80 255"),
        COLOR48(0x30, "210 76 255"),
        COLOR49(0x31, "236 71 255"),
        COLOR50(0x32, "255 62 249"),
        COLOR51(0x33, "255 48 226"),
        COLOR52(0x34, "255 34 207"),
        COLOR53(0x35, "255 14 190"),
        COLOR54(0x36, "255 0 174"),
        COLOR55(0x37, "255 0 159"),
        COLOR56(0x38, "255 0 145"),
        COLOR57(0x39, "255 0 133"),
        COLOR58(0x3A, "255 0 119"),
        COLOR59(0x3B, "255 0 106"),
        COLOR60(0x3C, "255 0 93"),
        COLOR61(0x3D, "255 0 78"),
        COLOR62(0x3E, "255 0 78"),
        COLOR63(0x3F, "255 0 37"),
        COLOR64(0x40, "255 163 166"),
        COLOR65(0x41, "255 146 151"),
        COLOR66(0x42, "255 130 136"),
        COLOR67(0x43, "255 117 125"),
        COLOR68(0x44, "255 104 113"),
        COLOR69(0x45, "255 90 102"),
        COLOR70(0x46, "255 77 91"),
        COLOR71(0x47, "255 63 81"),
        COLOR72(0x48, "255 43 69"),
        COLOR73(0x49, "255 5 57"),
        COLOR74(0x4A, "255 0 42"),
        COLOR75(0x4B, "255 0 20"),
        COLOR76(0x4C, "255 173 151"),
        COLOR77(0x4D, "255 166 139"),
        COLOR78(0x4E, "255 159 127"),
        COLOR79(0x4F, "255 153 116"),
        COLOR80(0x50, "255 146 104"),
        COLOR81(0x51, "255 141 104"),
        COLOR82(0x52, "255 135 80"),
        COLOR83(0x53, "255 130 66"),
        COLOR84(0x54, "255 125 48"),
        COLOR85(0x55, "255 121 25"),
        COLOR86(0x56, "255 230 181"),
        COLOR87(0x57, "255 231 171"),
        COLOR88(0x58, "255 230 161"),
        COLOR89(0x59, "255 230 152"),
        COLOR90(0x5A, "255 230 141"),
        COLOR91(0x5B, "255 229 129"),
        COLOR92(0x5C, "255 231 120"),
        COLOR93(0x5D, "255 230 106"),
        COLOR94(0x5E, "255 229 93"),
        COLOR95(0x5F, "255 230 78"),
        COLOR96(0x60, "224 255 188"),
        COLOR97(0x61, "215 255 176"),
        COLOR98(0x62, "205 255 164"),
        COLOR99(0x63, "199 255 155"),
        COLOR100(0x64, "190 255 144"),
        COLOR101(0x65, "183 255 133"),
        COLOR102(0x66, "175 255 121"),
        COLOR103(0x67, "168 255 121"),
        COLOR104(0x68, "161 255 98"),
        COLOR105(0x69, "154 255 84"),
        COLOR106(0x6A, "190 255 200"),
        COLOR107(0x6B, "162 255 186"),
        COLOR108(0x6C, "134 255 174"),
        COLOR109(0x6D, "100 255 162"),
        COLOR110(0x6E, "50 255 152"),
        COLOR111(0x6F, "0 255 141"),
        COLOR112(0x70, "0 255 131"),
        COLOR113(0x71, "0 255 121"),
        COLOR114(0x72, "0 255 111"),
        COLOR115(0x73, "0 255 102"),
        COLOR116(0x74, "0 255 92"),
        COLOR117(0x75, "0 255 83"),
        COLOR118(0x76, "163 255 213"),
        COLOR119(0x77, "135 255 206"),
        COLOR120(0x78, "103 255 200"),
        COLOR121(0x79, "61 255 195"),
        COLOR122(0x7A, "0 255 190"),
        COLOR123(0x7B, "0 255 185"),
        COLOR124(0x7C, "0 255 181"),
        COLOR125(0x7D, "0 255 177"),
        COLOR126(0x7E, "0 255 173"),
        COLOR127(0x7F, "0 255 169"),
        COLOR128(0x80, "177 255 242"),
        COLOR129(0x81, "154 255 242"),
        COLOR130(0x82, "125 255 239"),
        COLOR131(0x83, "85 255 237"),
        COLOR132(0x84, "0 255 236"),
        COLOR133(0x85, "0 255 235"),
        COLOR134(0x86, "0 255 232"),
        COLOR135(0x87, "0 255 232"),
        COLOR136(0x88, "0 255 230"),
        COLOR137(0x89, "0 255 230"),
        COLOR138(0x8A, "188 225 255"),
        COLOR139(0x8B, "168 222 255"),
        COLOR140(0x8C, "147 217 250"),
        COLOR141(0x8D, "123 213 255"),
        COLOR142(0x8E, "95 210 255"),
        COLOR143(0x8F, "50 206 255"),
        COLOR144(0x90, "0 202 255"),
        COLOR145(0x91, "0 200 255"),
        COLOR146(0x92, "0 196 255"),
        COLOR147(0x93, "0 193 255"),
        COLOR148(0x94, "226 212 255"),
        COLOR149(0x95, "209 200 255"),
        COLOR150(0x96, "193 188 255"),
        COLOR151(0x97, "178 177 255"),
        COLOR152(0x98, "163 167 255"),
        COLOR153(0x99, "148 157 255"),
        COLOR154(0x9A, "132 147 255"),
        COLOR155(0x9B, "116 139 255"),
        COLOR156(0x9C, "98 129 255"),
        COLOR157(0x9D, "77 120 255"),
        COLOR158(0x9E, "47 111 255"),
        COLOR159(0x9F, "29 102 255"),
        COLOR160(0xA0, "255 192 253"),
        COLOR161(0xA1, "253 182 255"),
        COLOR162(0xA2, "248 170 255"),
        COLOR163(0xA3, "246 159 255"),
        COLOR164(0xA4, "241 147 255"),
        COLOR165(0xA5, "238 135 255"),
        COLOR166(0xA6, "236 122 255"),
        COLOR167(0xA7, "233 109 255"),
        COLOR168(0xA8, "229 94 255"),
        COLOR169(0xA9, "228 75 255"),
        COLOR170(0xAA, "255 171 211"),
        COLOR171(0xAB, "255 159 208"),
        COLOR172(0xAC, "255 148 205"),
        COLOR173(0xAD, "255 139 204"),
        COLOR174(0xAE, "255 126 202"),
        COLOR175(0xAF, "255 114 199"),
        COLOR176(0xB0, "255 103 198"),
        COLOR177(0xB1, "255 89 196"),
        COLOR178(0xB2, "255 73 194"),
        COLOR179(0xB3, "255 56 193"),
        COLOR180(0xB4, "255 150 175"),
        COLOR181(0xB5, "255 137 167"),
        COLOR182(0xB6, "255 124 160"),
        COLOR183(0xB7, "255 113 154"),
        COLOR184(0xB8, "255 100 148"),
        COLOR185(0xB9, "255 88 144"),
        COLOR186(0xBA, "255 74 138"),
        COLOR187(0xBB, "255 57 134"),
        COLOR188(0xBC, "255 35 129"),
        COLOR189(0xBD, "255 0 125"),
        COLOR190(0xBE, "255 69 61"),
        COLOR191(0xBF, "255 90 61"),
        COLOR192(0xC0, "255 109 66"),
        COLOR193(0xC1, "255 109 87"),
        COLOR194(0xC2, "255 130 109"),
        COLOR195(0xC3, "255 154 80"),
        COLOR196(0xC4, "255 175 87"),
        COLOR197(0xC5, "255 196 93"),
        COLOR198(0xC6, "255 187 118"),
        COLOR199(0xC7, "255 197 118"),
        COLOR200(0xC8, "255 253 111"),
        COLOR201(0xC9, "226 255 107"),
        COLOR202(0xCA, "195 255 104"),
        COLOR203(0xCB, "229 255 136"),
        COLOR204(0xCC, "241 255 167"),
        COLOR205(0xCD, "117 255 103"),
        COLOR206(0xCE, "0 255 97"),
        COLOR207(0xCF, "0 255 100"),
        COLOR208(0xD0, "110 255 130"),
        COLOR209(0xD1, "165 255 162"),
        COLOR210(0xD2, "0 255 145"),
        COLOR211(0xD3, "0 255 125"),
        COLOR212(0xD4, "0 255 162"),
        COLOR213(0xD5, "0 255 163"),
        COLOR214(0xD6, "95 255 181"),
        COLOR215(0xD7, "0 255 189"),
        COLOR216(0xD8, "0 255 204"),
        COLOR217(0xD9, "0 255 221"),
        COLOR218(0xDA, "0 255 212"),
        COLOR219(0xDB, "134 255 220"),
        COLOR220(0xDC, "0 255 247"),
        COLOR221(0xDD, "0 241 255"),
        COLOR222(0xDE, "0 218 255"),
        COLOR223(0xDF, "80 243 255"),
        COLOR224(0xE0, "172 246 255"),
        COLOR225(0xE1, "0 180 255"),
        COLOR226(0xE2, "42 161 255"),
        COLOR227(0xE3, "65 143 255"),
        COLOR228(0xE4, "123 185 255"),
        COLOR229(0xE5, "160 198 255"),
        COLOR230(0xE6, "141 115 255"),
        COLOR231(0xE7, "173 115 255"),
        COLOR232(0xE8, "202 114 255"),
        COLOR233(0xE9, "215 173 255"),
        COLOR234(0xEA, "193 147 255"),
        COLOR235(0xEB, "255 111 254"),
        COLOR236(0xEC, "255 102 230"),
        COLOR237(0xED, "255 94 212"),
        COLOR238(0xEE, "255 137 230"),
        COLOR239(0xEF, "255 163 228"),
        COLOR240(0xF0, "255 79 183"),
        COLOR241(0xF1, "255 75 166"),
        COLOR242(0xF2, "255 69 151"),
        COLOR243(0xF3, "255 144 186"),
        COLOR244(0xF4, "255 109 172"),
        COLOR245(0xF5, "255 55 122"),
        COLOR246(0xF6, "255 52 107"),
        COLOR247(0xF7, "255 46 90"),
        COLOR248(0xF8, "255 87 123"),
        COLOR249(0xF9, "255 117 144"),
        COLOR250(0xFA, "234 227 255"),
        COLOR251(0xFB, "255 231 230"),
        COLOR252(0xFC, "255 213 193"),
        COLOR253(0xFD, "215 255 235"),
        COLOR254(0xFE, "247 249 218"),
        COLOR255(0xFF, "255 187 200");


        private Integer value;
        private String name;

        public Integer getValue() {
            return value;
        }


        public void setValue(Integer value) {
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        ambientLightColor(Integer value, String label) {
            this.value = value;
            this.name = label;

        }

        public static String getByValue(int value) {
            for (ambientLightColor e : values()) {
                if (e.getValue() == value) {
                    return e.name;
                }
            }
            return "";
        }

        public static String getHexByLin(int value) {
            for (ambientLightColor e : values()) {
                if (e.getValue() == value) {
                    return ColorUtils.rgbToHex(e.name);
                }
            }
            return "";
        }

        public static int getLinByHex(String colorHex) {
            for (ambientLightColor e : values()) {
                if (!StringUtils.isEmpty(e.getName()) && e.getName().equalsIgnoreCase(ColorUtils.hexToRgbStr(colorHex))) {
                    return e.value;
                }
            }
            return 0X00;
        }

        public static int getLinByRgb(int r, int g, int b) {
            for (ambientLightColor e : values()) {
                if (!StringUtils.isEmpty(e.getName()) && e.getName().equalsIgnoreCase(r + " " + g + " " + b)) {
                    return e.value;
                }
            }
            return 0X00;
        }

        public static int getLinByRgb(String rgbStr) {
            for (ambientLightColor e : values()) {
                if (!StringUtils.isEmpty(e.getName()) && e.getName().equalsIgnoreCase(rgbStr)) {
                    return e.value;
                }
            }
            return 0X00;
        }

        public static Integer getByName(String name) {
            for (ambientLightColor e : values()) {
                if (!StringUtils.isEmpty(e.getName()) && e.getName().equalsIgnoreCase(name)) {
                    return e.value;
                }
            }
            return 0X00;
        }
    }
}