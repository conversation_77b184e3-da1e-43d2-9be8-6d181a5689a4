package com.bitech.vehiclesettings.presenter.carsetting;

import android.content.Context;
import android.util.Log;
import android.widget.SeekBar;
import android.widget.TextView;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.CarSettingManager;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.common.SiganlConstans;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.event.VDEvent;
import com.chery.ivi.vdb.event.id.vehicle.VDEventVehicle;

public class CarSettingPresenter implements CarSettingPresenterListener {
    private static final String TAG = CarSettingPresenter.class.getSimpleName();
    private Context mContext;

    CarSettingManager carSettingManager = (CarSettingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_SETTING_MANAGER);

    public CarSettingPresenter(Context context) {
        this.mContext = context;
    }

    /**
     * 设置雨刮器灵敏度
     *
     * @param status
     */
    @Override
    public void setWiperSens(int status) {
        //显⽰名称：⾬刮灵敏度 开关设置： 低、标准、⾼、最⾼ 开关默认值：⾼
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2||3||4）
        //1. 在⼤屏上点击⾬刮灵敏调节设置项为 低；
        //2. 在⼤屏上点击⾬刮灵敏调节设置项为 标准；
        //3. 在⼤屏上点击⾬刮灵敏调节设置项为 ⾼；
        //4. 在⼤屏上点击⾬刮灵敏调节设置项为 最⾼；
        //执⾏输出（1||2||3||4）
        //1. 若触发条件 1，ICC 连续发送三帧 ICC_WiprSnvty = 0x1:Level 1，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 低，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x1:Level 1，则⾬刮灵敏调节设置 项保持 level1，否则开关根据收
        //到的信号显⽰对应状态；
        //2. 若触发条件 2，ICC 连续发送三帧 ICC_WiprSnvty = 0x2:Level 2 ，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 标准，计时2s若检测到状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x2:Level 2，则⾬刮灵敏调节设置 项保持 level2，否则开关根据收
        //到的信号显⽰对应状态；
        //3. 若触发条件 3，ICC 连续发送三帧 ICC_WiprSnvty = 0x3:Level 3，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 ⾼，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x3:Level 3，则⾬刮灵敏调节设置 项保持 level3，否则开关根据收
        //到的信号显⽰对应状态；
        //4. 若触发条件 4，ICC 连续发送三帧 ICC_WiprSnvty = 0x4:Level 4，然后发送 0x0:Not Active 给
        //FLZCU，FLZCU 控制将⾬刮灵敏调节为 最⾼，计时 2s 若检测到 状态反馈信号
        //FLZCU_WipeSensitivitySts = 0x4:Level4，则⾬刮灵敏调节设置 项保持 level4，否则开关根据收到
        //的信号显⽰对应状态；
        //5. 若未进⾏开关设置，FLZCU_WipeSensitivitySts对应等级信号发⽣变化，⾬刮灵敏调节设置为对应
        //等级。

        // ICC -> FLZCU 信号名：ICC_WiprSnvty
        // 0x0:Not Active
        // 0x1:Level 1
        // 0x2:Level 2
        // 0x3:Level 3
        // 0x4:Level 4
        // 0x5:Reserved
        // 0x6:Reserved

        // FLZCU -> ICC 信号名：FLZCU_WipeSensitivitySts
        // 0x0:Not Active
        // 0x1:Level 1
        // 0x2:Level 2
        // 0x3:Level 3
        // 0x4:Level 4
        // 0x5~0x7:Reserved
        if (MsgUtil.getInstance().supportPowerMode()) {
            Log.d(TAG, "setCentralLocking: 发送雨刮器灵敏度:" + status);
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY_SET, 0x1);
                    carSettingManager.setWiperSensitivity(0x1);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY_SET, 0x2);
                    carSettingManager.setWiperSensitivity(0x2);
                    break;
                case 2:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY_SET, 0x3);
                    carSettingManager.setWiperSensitivity(0x3);
                    break;
                case 3:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY_SET, 0x4);
                    carSettingManager.setWiperSensitivity(0x4);
                    break;
            }
        }
    }

    @Override
    public int getWiperSens() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_WIPERSENSITIVITY);
        int signlVal = carSettingManager.getWiperSensitivity();

        LightManager lightManager = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
        int leftDOWWarn = lightManager.getLeftDOWWarn();
        Log.d(TAG, "leftDOWWarn:" + leftDOWWarn);


        Log.d(TAG, "setCentralLocking: 获取雨刮器灵敏度:" + signlVal);
        int status = 3;
        if (signlVal == 0x1) {
            status = 0;
        } else if (signlVal == 0x2) {
            status = 1;
        } else if (signlVal == 0x3) {
            status = 2;
        } else if (signlVal == 0x4) {
            status = 3;
        }
        Prefs.put(PrefsConst.C_WIPER_SENS, status);
//        return Prefs.rtnStatus(PrefsConst.C_WIPER_SENS, status, PrefsConst.DefaultValue.C_WIPER_SENS);
        return status;
    }

    @Override
    public void setHudRoate(int status) {
        //显⽰名称：后尾⻔开启⾼度设置 开关设置：50%~95% 开关默认值：95%
        //配置：PLG 配置
        //前置条件：
        //1. 电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件
        //1. 在中控屏设置后尾⻔开启⾼度（50%~95%），ICC 发送 ICC_Set_PLGOperateSts 给 PLG，⾄少发
        //送三帧之后发送 0x0:Not Active 给 FLZCU；
        //执⾏输出（1||2||3）
        //1. ICC 收到 PLG_Set_PLGOperateStsFb≤50，后尾⻔开启⾼度显⽰ 50%；
        //2. ICC 收到 PLG_Set_PLGOperateStsFb= 51-95，后尾⻔开启⾼度显⽰对应信号值 51%~95% ；
        //3. ICC 收到 PLG_Set_PLGOperateStsFb≥95，后尾⻔开启⾼度显⽰ 95%；
        //备注：
        //1. ICC显⽰界⾯设置为⽤⼾选择的状态，并在2秒后同步总线反馈的状态，否则返回上⼀设置值。

        // ICC -> PLG 信号名：ICC_Set_PLGOperateSts
        // 0x0:Not Active
        // 0x~0x65:0%~100%
        Log.d(TAG, "setHudRoate: 设置后尾门高度:" + status);
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status <= 50) {
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_TRUNKDOOROPENDEGREE_SET, 1);
            } else if (status < 95) {
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_TRUNKDOOROPENDEGREE_SET, 2);
            } else {
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_TRUNKDOOROPENDEGREE_SET, 3);
            }
        }
    }

    @Override
    public int getHudRoate() {
//        int status = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEDOOR_TRUNKDOOROPENDEGREE);
        int status = 0;
        Log.d(TAG, "getHudRoate: 获取后尾门高度:" + status);

        return Prefs.rtnStatus(PrefsConst.C_REAR_TAILGATE_ROATE, status, PrefsConst.DefaultValue.C_REAR_TAILGATE_ROATE);
    }

    @Override
    public void updateHudRoateUI(Integer vstate, SeekBar seekBar, TextView textView) {
        seekBar.setProgress(vstate);
        textView.setText(vstate + "%");
    }

    @Override
    public void setLockAutoRaiseWindow(int status) {
        //显⽰名称：锁⻋⾃动升窗 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-点击开
        //2. 在中控屏⻋辆设置-点击关
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC 发送 ICC_LockCarWinCloseSw = 0x1：ON； ，然后发送
        //0x0:Not Active 给 FLZCU，⼤屏锁⻋升窗显⽰为开，计时 2s 若检测到状态反馈信号
        //FLZCU_LockCarWinCloseFb = 0x0:Enable，则⼤屏锁⻋升窗显⽰保持为开状态， 否则弹回关状
        //态；
        //2. 若触发条件为 2，ICC 连续发送三帧ICC_LockCarWinCloseSw= 0x2：OFF，然后发送 0x0:Not
        //Active 给 FLZCU，⼤屏锁⻋升窗显⽰为关，计时 2s 若检测到状态反馈信号
        //FLZCU_LockCarWinCloseFb = 0x1:Disable，则⼤屏锁⻋升窗显⽰保持为关状态，否则弹回开状
        //态；

        // ICC -> FLZCU 信号名：ICC_LockCarWinCloseSw
        // 0x0:Not Active
        // 0x1:ON
        // 0x2:OFF
        // 0x3:Reserved

        // FLZCU -> ICC 信号名：FLZCU_LockCarWinCloseFb
        // 0x0:Enable
        // 0x1:Disable
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_LOCKCARWINDOWCLOSE_SET, 0x2);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_LOCKCARWINDOWCLOSE_SET, 0x1);
                    break;
            }
        }
    }

    @Override
    public int getLockAutoRaiseWindow() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_LOCKCARWINDOWCLOSE);
        int signlVal = 0;
        int status = 0;
        if (signlVal == 0x1) {
            status = 0;
        } else if (signlVal == 0x0) {
            status = 1;
        }
        Prefs.put(PrefsConst.C_LOCK_AUTO_RAISE_WINDOW, status);
//        return Prefs.rtnStatus(PrefsConst.C_LOCK_AUTO_RAISE_WINDOW, status, PrefsConst.DefaultValue.C_LOCK_AUTO_RAISE_WINDOW);
        return status;
    }

    @Override
    public void setDefenseReminder(int status) {
        //显⽰名称：设防提⽰ 开关设置：灯光、灯光和喇叭 开关默认值：灯光和喇叭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. ⽤⼾在⼤屏上设置设防提⽰为灯光提⽰；
        //2. ⽤⼾在⼤屏上设置设防提⽰为灯光和喇叭；
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧，ICC_lockSetSwitchSts = 0x2:Only Lighting，然后发送
        //0x0:Not Active 给 FLZCU，⼤屏设防提⽰显⽰为灯光提⽰，计时 2s 若检测到状态反馈信号
        //FLZCU_AlarmWarnSetSW = 0x2:Only Lighting，则⼤屏设防提⽰显⽰保持为灯光提⽰状态， 否则
        //回弹；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_lockSetSwitchSts = 0x3：Sound and Lighting，然后发
        //送 0x0:Not Active 给 FLZCU，⼤屏设防提⽰显⽰为灯光和喇叭，计时 2s 若检测到状态反馈信号
        //FLZCU_AlarmWarnSetSW = 0x3：Sound and Lighting，则⼤屏设防提⽰显⽰保持为灯光和喇叭
        //状态，否则回弹；

        // ICC -> FLZCU 信号名：ICC_lockSetSwitchSts
        // 0x0:Not Active
        // 0x1:OFF
        // 0x2:Only Lighting
        // 0x3:Sound and Lighting
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 0) {
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_UNLOCKALARMWARN_SET, 0x2);
            } else if (status == 1) {
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_UNLOCKALARMWARN_SET, 0x3);
            }
        }
    }

    @Override
    public int getDefenseReminder() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_UNLOCKALARMWARN);
        int signlVal = 0;
        Log.d(TAG, "getDefenseReminder: 获取设防提⽰:" + signlVal);
        int status = 0;
        if (signlVal == 0x2) {
            status = 0;
        } else if (signlVal == 0x3) {
            status = 1;
        }
        Prefs.put(PrefsConst.C_DEFENSE_REMINDER, status);
//        return Prefs.rtnStatus(PrefsConst.C_DEFENSE_REMINDER, status, PrefsConst.DefaultValue.C_DEFENSE_REMINDER);
        return status;
    }

    @Override
    public void setLeftChildLock(int status) {
        //显⽰名称：左⼉童锁 开关设置：解锁/闭锁 开关默认值：解锁
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-⻋辆控制界⾯提供左⼉童锁选项：⽤⼾点击左⼉童锁开关按键闭锁；
        //2. 在中控屏⻋辆设置-⻋辆控制界⾯提供左⼉童锁选项：⽤⼾点击左⼉童锁开关按键解锁；
        //执⾏输出（1||2）
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_ChildLockSW = 0x2:ChildLock_RL 给FLZCU，接着发送
        //0x0:Not Active，⼤屏左⼉童锁显⽰为闭锁，FLZCU 控制左后⼉童锁闭锁，计时 2s 若接收到状态反
        //馈信号 RL_ChildrenProtectSwitch =0x0:Locked，则左⼉童锁开关保持闭锁（开关⾼亮），否则
        //开关弹回⾮⾼亮；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ChildLockSW = 0x2:ChildLock_RL 给 FLZCU，接着发送
        //0x0:Not Active，⼤屏左⼉童锁显⽰为解锁，FZCU控制左后⼉童锁解锁，计时2s 若接收到状态反馈
        //信号 RL_ChildrenProtectSwitch=0x1:Unlocked，则左后⼉童锁开关保持解锁（开关⾮⾼亮），否
        //则开关弹回⾼亮；

        // ICC -> FLZCU/FRZCU 信号名：ICC_ChildLockSW
        // 0x0:Not Active
        // 0x1:Window
        // 0x2:ChildLock_RL
        // 0x3:ChildLock_RR

        // FLZCU -> ICC 信号名：RL_ChildrenProtectSwitch
        // 0x0:Locked
        // 0x1:Unlocked
        // 0x2:Superlocked

        // FRZCU -> ICC 信号名：RR_ChildrenProtectSwitch
        // 0x0:Locked
        // 0x1:Unlocked
        // 0x2:Superlocked
        if (MsgUtil.getInstance().supportPowerMode()) {
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_CHILDLOCKSWITCH_SET, 0x2);
        }
    }

    @Override
    public int getLeftChildLock() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEDOOR_CHILDLOCK_REARLEFT);
        int signlVal = 0;
        Log.d(TAG, "getLeftChildLock: 获取左⼉童锁:" + signlVal);
        int status = 0;
        if (signlVal == 0x0) {
            status = 1;
        } else if (signlVal == 0x1) {
            status = 0;
        }
        Prefs.put(PrefsConst.C_LEFT_CHILD_LOCK, status);
//        return Prefs.rtnStatus(PrefsConst.C_LEFT_CHILD_LOCK, status, PrefsConst.DefaultValue.C_LEFT_CHILD_LOCK);
        return status;
    }

    @Override
    public void setRightChildLock(int status) {
        //显⽰名称：右⼉童锁 开关设置：解锁/闭锁 开关默认值：解锁
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击右⼉童锁开关按键闭锁；
        //2. 在中控屏⻋辆设置-⻋辆控制界⾯提供中控锁选项：⽤⼾点击右⼉童锁开关按键解锁；
        //执⾏输出（1||2）
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_ChildLockSW = 0x3:ChildLock_RL 给 FRZCU，接着发送
        //0x0:Not Active，⼤屏右⼉童锁显⽰为闭锁，FRZCU 控制右后⼉童锁闭锁，计时 2s 若接收到状态
        //反馈信号 RR_ChildrenProtectSwitch =0x0:Locked，则右⼉童锁开关保持闭锁（开关⾼亮），否
        //则开关弹回⾮⾼亮；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ChildLockSW = 0x3:ChildLock_RL 给 FRZCU，接着发送
        //0x0:Not Active，⼤屏右⼉童锁显⽰为解锁，FRZCU控制右后⼉童锁解锁，计时2s 若接收到状态反
        //馈信号 RR_ChildrenProtectSwitch=0x1:Unlocked，则右⼉童锁开关保持解锁（开关⾮⾼亮），
        //否则开关弹回⾼亮；


        if (MsgUtil.getInstance().supportPowerMode()) {
//            MsgUtil.getInstance().setSignlVal(SiganlConstans.VEHICLEDOOR_CHILDLOCKSWITCH_SET, 0x3);
        }
    }

    @Override
    public int getRightChildLock() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.VEHICLEDOOR_CHILDLOCK_REARRIGHT);
        int signlVal = 0;
        int status = 0;
        if (signlVal == 0x0) {
            status = 1;
        } else if (signlVal == 0x1) {
            status = 0;
        }
        Prefs.put(PrefsConst.C_RIGHT_CHILD_LOCK, status);
//        return Prefs.rtnStatus(PrefsConst.C_RIGHT_CHILD_LOCK, status, PrefsConst.DefaultValue.C_RIGHT_CHILD_LOCK);
        return status;
    }

    @Override
    public void setAutomaticLocking(int status) {
        //显⽰名称：⾃动落锁 开关设置：开启/关闭 开关默认值：开启
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在⼤屏上打开⾃动落锁开关
        //2. 在⼤屏上关闭⾃动落锁开关
        //执⾏输出（1||2）
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_AutolockSts =0x1:Autolock mode 给 FLZCU，接着发 送
        //0x0:Not active ，FLZCU 控制⾃动落锁功能开启，计时 2s 若接收到状态反馈 信号
        //FLZCU_AutolockSts=0x1:Autolock mode，则⾃动落锁开关保持开启， 否则开关弹回关闭；
        //2. 若触发条件为 2，ICC 发送 ICC_AutolockSts=0x2:Not autolock mode 给 FLZCU，FLZCU 控制⾃
        //动落锁功能关闭, 计时 2s 若接收到状态反馈信号 FLZCU_AutolockSts=0x0:Not autolock mode，
        //则⾃动落锁开关保持关闭，否则开关弹回开启；
        //3. 若未进⾏开关设置，⾃动落锁开关接收FLZCU_AutolockSts对应信号显⽰对应开关状态，。

        // ICC -> FLZCU 信号名：ICC_AutolockSts
        // 0x0:Not Active
        // 0x1:Autolock mode
        // 0x2:Not autolock mode
        // 0x3:Not Used

        // FLZCU -> ICC 信号名：FLZCU_AutolockSts
        // 0x0:Not autolock mode
        // 0x1:Autolock mode
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AUTOMATICLOCKED_SET, 0x2);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AUTOMATICLOCKED_SET, 0x1);
                    break;
            }
        }
    }

    @Override
    public int getAutomaticLocking() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_AUTOMATICLOCKED);
        int signlVal = 0;
        Log.d(TAG, "getAutomaticLocking: 获取⾃动落锁:" + signlVal);
        int status = 0;
        if (signlVal == 0x0) {
            status = 0;
        } else if (signlVal == 0x1) {
            status = 1;
        }
        Prefs.put(PrefsConst.C_AUTOMATIC_LOCKING, status);
//        return Prefs.rtnStatus(PrefsConst.C_AUTOMATIC_LOCKING, status, PrefsConst.DefaultValue.C_AUTOMATIC_LOCKING);
        return status;
    }

    @Override
    public void setAutomaticParkingUnlock(int status) {
        //显⽰名称：驻⻋⾃动解锁 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件： （1||2）
        //1. ⽤⼾在⼤屏上设置驻⻋⾃动解锁功能开关开启；
        //2. ⽤⼾在⼤屏上设置驻⻋⾃动解锁功能开关关闭；
        //执⾏输出：
        //1. 若触发条件为 1，ICC 连续发送三帧 ICC_ParkUnlockEnable=0x2:Enable 给 FLZCU，之后发送
        //0x0:Not Active，计时 2s 若检测到状态反馈信号 FLZCU_ParkUnlockEnableFb=0x1:Enable，则开
        //关保持开启，否则开关弹回关闭；
        //2. 若触发条件为 2，ICC 连续发送三帧 ICC_ParkUnlockEnable=0x1:Disable 给 FLZCU，之后发送
        //0x0:Not Active，计时 2s 若检测到状态反馈信号 FLZCU_ParkUnlockEnableFb= 0x0:Disable，则
        //开关保持关闭，否则开关弹回开启；

        // ICC -> FLZCU 信号名：ICC_ParkUnlockEnable
        // 0x0:Not Active
        // 0x1:Disable
        // 0x2:Enable
        // 0x3:Reserved

        // FLZCU -> ICC 信号名：FLZCU_ParkUnlockEnableFb
        // 0x0:Disable
        // 0x1:Enable
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AUTOMATICUNLOCKED_SET, 0x1);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_AUTOMATICUNLOCKED_SET, 0x2);
                    break;
            }
        }
    }

    @Override
    public int getAutomaticParkingUnlock() {
//        int signlVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.BODYINFO_AUTOMATICUNLOCKED);
        int signlVal = 0;
        int status = 0;
        if (signlVal == 0x0) {
            status = 0;
        } else if (signlVal == 0x1) {
            status = 1;
        }
        Prefs.put(PrefsConst.C_AUTOMATIC_PARKING_UNLOCK, status);
//        return Prefs.rtnStatus(PrefsConst.C_AUTOMATIC_PARKING_UNLOCK, status, PrefsConst.DefaultValue.C_AUTOMATIC_PARKING_UNLOCK);
        return status;
    }

    @Override
    public void setWiperRepairMode(int status) {
        //显⽰名称：⾬刮维修模式 开关设置：开启/关闭 开关默认值：关闭
        //前置条件：
        //电源模式:Comfort/ON档，(信号：FLZCU_9_PowerMode=ON||Comfort)；
        //触发条件（1||2）
        //1. 在⼤屏上点击⾬刮维修模式开关按键使打开
        //2. 在⼤屏上点击⾬刮维修模式开关按键使其关闭
        //执⾏输出（1||2）MaintenanceMode/WiperMode/Set
        //1. 若触发条件为 1， ICC 连续发送三帧 ICC_MaintenanceMode =0x2:Entrance & ICC_WiperID
        //=0x3:Both Wiper，然后发送 0x0:Not Active 给 FLZCU，FLZCU 控制进⼊⾬刮 维修模式，计时 2s
        //若检测到状态反馈信号 FLZCU_WipeMaintenanceSWSts=0x3:Both Wiper，则⾬刮维修模式开关
        //保 持开启，否则开关弹回关闭；
        //2. 若触发条件为 1，ICC 连续发送三帧 ICC_MaintenanceMode =0x1:Exit & ICC_WiperID =0x3:Both
        //Wiper，然后发送 0x0:Not Active 给 FLZCU，FLZCU 控制退出⾬刮 维修模式，计时 2s 若检测到状
        //态反馈信号 FLZCU_WipeMaintenanceSWSts≠0x3:Both Wiper，则⾬刮维修模式开关保 持关闭，
        //否则开关弹回开启；；
        //3. ICC 收到 FLZCU_WipeMaintenanceSWSts = 0x3:Both Wiper，⼤屏显⽰⾬刮维修模式开启
        //ICC 收到 FLZCU_WipeMaintenanceSWSts =0x2:Rear Wiper||0x1:Front Wiper||0x0:Not
        //Active，⼤屏显⽰⾬刮维修模式关闭

        // ICC -> FLZCU 信号名：ICC_MaintenanceMode
        // 0x0:Not Active
        // 0x1:Exit
        // 0x2:Entrance

        // FLZCU -> ICC 信号名：FLZCU_WipeMaintenanceSWSts
        // 0x0:Not Active
        // 0x1:Front Wiper
        // 0x2:Rear Wiper
        // 0x3:Both Wiper
        if (MsgUtil.getInstance().supportPowerMode()) {
            switch (status) {
                case 0:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.MAINTENANCEMODE_WIPERMODE_SET, 0x2);
                    break;
                case 1:
//                    MsgUtil.getInstance().setSignlVal(SiganlConstans.MAINTENANCEMODE_WIPERMODE_SET, 0x1);
                    break;
            }
        }
    }

    @Override
    public int getWiperRepairMode() {
//        int signalVal = MsgUtil.getInstance().getSignlVal(SiganlConstans.MAINTENANCEMODE_WIPERMODE);
        int signalVal = 0;
        int status = 0;
        if (signalVal == 0x0) {
            status = 0;
        } else if (signalVal == 0x1) {
            status = 1;
        }
        Prefs.put(PrefsConst.C_WIPER_REPAIR_MODE, status);
//        return Prefs.rtnStatus(PrefsConst.C_WIPER_REPAIR_MODE, status, PrefsConst.DefaultValue.C_WIPER_REPAIR_MODE);
        return status;
    }

    @Override
    public void setOverSpeed(int status) {
        //显⽰名称：超速报警
        //设置和显⽰状态：30~170，精度5
        //默认值：开启，若检测到限速提醒功能且限速提醒打开，则超速报警关闭。超速值同步仪表
        //座舱域控的内部信号交互。
        //功能交互逻辑：
        //1）当整⻋电源模式处于ON档(FLZCU_9_PowerMode =ON)，⽤⼾可以设置超速报警功能，并将限速
        //值传给仪表。
        //2）当整⻋电源模式处于OFF/Comfort 档时(FLZCU_9_PowerMode =OFF||Comfort)，设置为不可操作
        //状态。
        //3）当限速辅助打开时，超速报警功能置灰并将开关关闭。当限速辅助关闭时，超速报警功能可设置。
        //3）当限速提醒打开时，超速报警开关关闭（此时超速报警开关需可操作，不做置灰）；当超速报警打
        //开时，限速提醒开关关闭；即两个开关不能同时打开；
        //每次下电或休眠，ICC需记忆超速报警的设置状态。

        // todo 超速报警
        // TODO 在线配置 仪表
        Log.d(TAG, "setSwOverspeed: 开关超速预警:" + status);
        Prefs.put(PrefsConst.C_OVER_SPEED, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_OVER_SPEED, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getOverSpeed() {
        // todo 超速报警
        // TODO 在线配置 仪表
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.C_OVER_SPEED, PrefsConst.DefaultValue.C_OVER_SPEED);
        Log.d(TAG, "getSwOverspeed: 获取超速预警:" + status);
        return status;
    }

    @Override
    public void setFatigueDrivingReminder(int status) {
        // todo 疲劳驾驶提醒
        // TODO 在线配置 仪表
        Log.d(TAG, "setFatigueDrive: 开关疲劳驾驶提醒:" + status);
        Prefs.put(PrefsConst.C_FATIGUE_DRIVING_REMINDER, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_FATIGUE_DRIVING_REMINDER, status);
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getFatigueDrivingReminder() {
        // todo 疲劳驾驶提醒
        // TODO 在线配置 仪表
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.C_FATIGUE_DRIVING_REMINDER, PrefsConst.DefaultValue.C_FATIGUE_DRIVING_REMINDER);
        Log.d(TAG, "getFatigueDrive: 获取疲劳驾驶提醒:" + status);
        return status;
    }

    @Override
    public void setMaintainTips(int status) {
        // TODO 在线配置
        Log.d(TAG, "setSwMaintainTips: 开关保养提示:" + status);
        // 1.保存状态值
        Prefs.put(PrefsConst.C_MAINTAIN_TIPS, status);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_TIPS, status);
        // 2.调用接口
        Log.d("点击成功：", status + "");
    }

    @Override
    public int getMaintainTips() {
        // TODO 在线配置 仪表
        int status = Prefs.getGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_TIPS, PrefsConst.DefaultValue.C_MAINTAIN_TIPS);
        Log.d(TAG, "getSwMaintainTips: 获取养提示:" + status);
        return status;
    }

    @Override
    public void maintainReset() {
        // TODO 在线配置
        Log.d(TAG, "setSwMaintainReset: 保养里程复位");
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_KM, 999);
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_DAY, 120);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_KM, 999);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_DAY, 120);
    }

    @Override
    public int getMaintainKm() {
        // TODO 在线配置
        int km = Prefs.getGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_KM, PrefsConst.DefaultValue.C_MAINTAIN_RESET_KM);
        Log.d(TAG, "getSwMaintainKm: 获取保养里程:" + km);
        return km;
    }

    @Override
    public void setMaintainKm(int km) {
        // TODO 在线配置
        Log.d(TAG, "setSwMaintainKm: 设置保养里程:" + km);
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_KM, km);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_KM, km);
        Log.d(TAG, "点击成功：" + km);
    }

    @Override
    public int getMaintainDays() {
        // TODO 在线配置
        int day = Prefs.getGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_DAY, PrefsConst.DefaultValue.C_MAINTAIN_RESET_DAY);
        Log.d(TAG, "getSwMaintainDays: 获取保养天数:" + day);
        return day;
    }

    @Override
    public void setMaintainDays(int day) {
        // TODO 在线配置
        Log.d(TAG, "setSwMaintainDays: 设置保养天数:" + day);
        Prefs.put(PrefsConst.C_MAINTAIN_RESET_DAY, day);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_MAINTAIN_RESET_DAY, day);
        Log.d(TAG, "点击成功：" + day);
    }

    @Override
    public void setDriveAirBag(int status) {
        // 显示名称：副驾安全气囊 开关设置：开启/关闭 开关默认值：开启
        // 开关设置：
        // 前置条件：
        // 电源模式:ON档，(信号：FLZCU_9_PowerMode=ON)；
        // 触发条件：（1||2）
        // 1. 在中控屏车辆设置-：副驾安全气囊，点击打开
        // 2. 在中控屏车辆设置-：副驾安全气囊，点击关闭
        // 执行输出：（1||2）
        // 1. 若触发条件为 1， ICC 连续发送三帧 ICC_PABSetCmd = 0x2: PAB ON Cmd 给 ACU，ACU 控制副
        // 驾气囊 PAB 开启，接着发送 0x0:Not Active ，计时 2s 若收到状态反馈
        // ABM_1_PABSetSts=0x1:PAB ON，则副驾气囊屏蔽开关保持开启；
        // 2. 若触发条件为 2， 弹框提醒“您是否确定要停用前排乘客安全气囊？”，点击确认后ICC发送
        //         ICC_PABSetCmd = 0x1: PAB OFF Cmd 给 ACU，ACU 控制副驾气囊 PAB 关闭，接着发送 0x0:NotActive ，
        // 计时 2s 若收到状态反馈ABM_1_PABSetSts=0x0:PAB OFF，则副驾气囊屏蔽开关保持关闭
        // 副驾气囊 PAB 状态栏图标显示
        // 前置条件：
        // 电源状态 ON 档(信号：FLZCU_9_PowerMode=0x2:ON)；
        // 触发条件：（1||2||3||4）
        // 1. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x0: 'PAB ON' lamp on”；
        // 2. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x1: 'PAB OFF' lamp on”；
        // 3. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x2: no lamp on”；
        // 4. 接收到副驾安全气囊状态服务 ACU_1_PsngrBagSts =0x3: Both lamp on”；
        // 执行输出：（1||2||3||4）
        // 1. 若触发条件 1，点亮副驾安全气囊开启指示灯，并在指示灯上方显示 “Passenger Airbag”字样；
        // 2. 若触发条件 2，点亮副驾安全气囊关闭指示灯 ，并在指示灯上方显示 “Passenger Airbag”字样 ；
        // 3. 若触发条件 3，熄灭副驾安全气囊指示灯，同时不再显示“Passenger Airbag”字样。
        // 4. 若触发条件 4，同时点亮副驾安全气囊开启和关闭指示灯，同时不再显示“Passenger Airbag”字样；
        // 异常处理：失去通讯，保持当前状态。
        // 信号描述：
        // ICC -> ACU 信号名：ICC_PABSetCmd
        // 0x0:NotActive
        // 0x1:PAB OFF
        // 0x2:PAB ON
        //
        // ACU -> ICC 信号名：ABM_1_PABSetSts
        // 0x0:PAB OFF
        // 0x1:PAB ON
        //
        // ACU -> ICC 信号名：ACU_1_PsngrBagSts
        // 0x0:'PAB ON' lamp on
        // 0x1:'PAB OFF' lamp on
        // 0x2:no pamp on
        // 0x3:Both lamp on
        // todo 驾驶安全气囊
        // TODO 在线配置 仪表
        Log.d(TAG, "setSwDriveAirBag: 开关驾驶安全气囊:" + status);
        // 电源模式:ON档，(信号：FLZCU_9_PowerMode=ON)；
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (status == 1) {
                // ICC 连续发送三帧 ICC_PABSetCmd = 0x2
                Log.d(TAG, "setSwDriveAirBag: 开启");
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_PASSENGERAIRBAG_SET, 0x2);
                carSettingManager.setPassengerAirbag(0x2);
            } else if (status == 0) {
                // ICC发送 ICC_PABSetCmd = 0x1:
                Log.d(TAG, "setSwDriveAirBag: 关闭");
//                MsgUtil.getInstance().setSignlVal(SiganlConstans.BODYINFO_PASSENGERAIRBAG_SET, 0x1);
                carSettingManager.setPassengerAirbag(0x1);
            }
        }
    }

    @Override
    public int getDriveAirBag() {
        // todo 驾驶安全气囊
        int signalVal = carSettingManager.getPassengerAirbag();
        int status = 0;
        if (signalVal == 0x1) {
            status = 0;
        } else if (signalVal == 0x2) {
            status = 1;
        }
        Prefs.put(PrefsConst.C_DRIVE_AIR_BAG, status);
        return status;
    }


    @Override
    public int getCarSpeed() {
        VDEvent event = VDBus.getDefault().getOnce(VDEventVehicle.PERF_VEHICLE_SPEED);
//        double[] data = event.getPayload().getDoubleArray(VDKeyVehicle.DOUBLE_VECTOR);
        double[] data = new double[]{-1};
        double value = data[0];
        setCarSpeed((int) value);
        int d = Prefs.getGlobalValue(PrefsConst.GlobalValue.C_CAR_SPEED, PrefsConst.DefaultValue.C_CAR_SPEED);
        Log.d(TAG, "getCarSpeed: 获取车速:" + value);

        return d;
    }

    @Override
    public void setCarSpeed(int v) {
        // TODO 在线配置
        Log.d(TAG, "setCarSpeed: 设置车速:" + v);
        if (v == -1) {
            Log.d(TAG, "setCarSpeed: 车速设置失败");
            return;
        }
        Prefs.put(PrefsConst.C_CAR_SPEED, v);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_CAR_SPEED, v);
        Log.d(TAG, "setCarSpeed: 车速设置成功" + v);
    }

    @Override
    public void setOverSpeedSeekbar(int value) {
        // TODO 在线配置
        Log.d(TAG, "setOverSpeedSeekbar: 设置超速报警值:" + value);
        Prefs.put(PrefsConst.C_OVER_SPEED_SEEKBAR, value);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_OVER_SPEED_VALUE, value);
        Log.d(TAG, "点击成功：" + value);
    }

    @Override
    public int getOverSpeedSeekbar() {
        // TODO 在线配置
        int value = Prefs.getGlobalValue(PrefsConst.GlobalValue.C_OVER_SPEED_VALUE, PrefsConst.DefaultValue.C_OVER_SPEED_SEEKBAR);
        Log.d(TAG, "getOverSpeedSeekbar: 获取超速报警值:" + value);
        return value;
    }

    @Override
    public void setFatigueDriveSeekbar(int value) {
        // TODO 在线配置
        Log.d(TAG, "setFatigueDriveSeekbar: 设置疲劳驾驶提醒:" + value);
        Prefs.put(PrefsConst.C_FATIGUE_DRIVING_SEEKBAR, value);
        Prefs.setGlobalValue(PrefsConst.GlobalValue.C_FATIGUE_DRIVING_REMINDER_VALUE, value);
        Log.d(TAG, "点击成功：" + value);
    }

    @Override
    public int getFatigueDriveSeekbar() {
        // TODO 在线配置
        int value = Prefs.getGlobalValue(PrefsConst.GlobalValue.C_FATIGUE_DRIVING_REMINDER_VALUE, PrefsConst.DefaultValue.C_FATIGUE_DRIVING_SEEKBAR);
        Log.d(TAG, "getFatigueDriveSeekbar: 获取疲劳驾驶提醒:" + value);
        return value;
    }
}
