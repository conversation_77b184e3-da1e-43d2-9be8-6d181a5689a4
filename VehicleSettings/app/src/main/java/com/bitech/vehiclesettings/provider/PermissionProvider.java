package com.bitech.vehiclesettings.provider;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;

import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.utils.GsonUtils;
import com.bitech.vehiclesettings.utils.PermissionManager;

import java.util.List;

public class PermissionProvider extends ContentProvider {

    public static final String ACTION = "com.chery.carsettings.provider.permission";
    public static final Uri DRIVING_MODE_URI = Uri.parse("content://" + ACTION);
    public static final String TAG = PermissionProvider.class.getSimpleName();
    public static final String GET_PERMISSION_STATUS = "getPermissionStatus";
    public static final String GET_PERMISSION_LIST = "getPermissionList";
    public static final String KEY_PERMISSION_STATUS = "permissionStatus";
    public static final String KEY_PERMISSION_LIST = "permissionList";
    public static final int CAMERA_PERMISSION = 0;
    public static final int LOCATION_PERMISSION = 1;
    public static final int MICROPHONE_PERMISSION = 2;

    @Override
    public boolean onCreate() {
        return true;
    }

    @Override
    public String getType(Uri uri) {
        return null;
    }

    @Override
    public Uri insert(Uri uri, ContentValues values) {
        return null;
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection,
                        String[] selectionArgs, String sortOrder) {
        return null;
    }

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        return 0;
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection,
                      String[] selectionArgs) {
        return 0;
    }

    @Override
    public Bundle call(String method, String arg, Bundle extras) {
        switch (method) {
            case GET_PERMISSION_STATUS:
                int statusKey = extras.getInt(KEY_PERMISSION_STATUS);
                return getPermissionStatus(statusKey);
            case GET_PERMISSION_LIST:
                int statusList = extras.getInt(KEY_PERMISSION_LIST);
                return getPermissionList(statusList);
            default:
                return null;
        }
    }

    private Bundle getPermissionStatus(int index) {
        Bundle result = new Bundle();
        MyApplication instance = MyApplication.getInstance();
        switch (index) {
            case CAMERA_PERMISSION:
                boolean cameraPermissionEnabled = PermissionManager.isCameraPermissionEnabled(instance);
                result.putBoolean(KEY_PERMISSION_STATUS, cameraPermissionEnabled);
                break;
            case LOCATION_PERMISSION:
                boolean locationPermissionEnabled = PermissionManager.isLocationPermissionEnabled(instance);
                result.putBoolean(KEY_PERMISSION_STATUS, locationPermissionEnabled);
                break;
            case MICROPHONE_PERMISSION:
                boolean microphonePermissionEnabled = PermissionManager.isMicrophonePermissionEnabled(instance);
                result.putBoolean(KEY_PERMISSION_STATUS, microphonePermissionEnabled);
                break;
            default:
                break;

        }
        return result;
    }

    private Bundle getPermissionList(int index) {
        Bundle result = new Bundle();
        Context context = MyApplication.getContext();
        switch (index) {
            case CAMERA_PERMISSION:
                List<String> appsWithCameraPermission = PermissionManager.getAppsWithCameraPermission(context);
                result.putString(KEY_PERMISSION_LIST, GsonUtils.toJson(appsWithCameraPermission));
                break;
            case LOCATION_PERMISSION:
                List<String> appsWithLocationPermission = PermissionManager.getAppsWithLocationPermission(context);
                result.putString(KEY_PERMISSION_LIST, GsonUtils.toJson(appsWithLocationPermission));
                break;
            case MICROPHONE_PERMISSION:
                List<String> appsWithMicrophonePermission = PermissionManager.getAppsWithMicrophonePermission(context);
                result.putString(KEY_PERMISSION_LIST, GsonUtils.toJson(appsWithMicrophonePermission));
                break;
            default:
                break;
        }
        return result;
    }
}