package com.bitech.vehiclesettings.bean;

public class CarSettingDatas extends BaseData {
    private String tag;

    /**
     * 雨刮器灵敏度 - 低
     */
    private boolean wiperSensLowFlag = true;
    /**
     * 雨刮器灵敏度 - 标准
     */
    private boolean wiperSensStandardFlag = false;
    /**
     * 雨刮器灵敏度 - 高
     */
    private boolean wiperSensHighFlag = false;
    /**
     * 雨刮器灵敏度 - 最高
     */
    private boolean wiperSensHighestFlag = false;
    /**
     * 锁车自动升窗
     */
    private boolean swAutoWindow = false;
    /**
     * 自动落锁
     */
    private boolean swAutoLock = true;
    /**
     * 驻车自动解锁
     */
    private boolean swAutoUnlock = true;
    /**
     * 保养提示
     */
    private boolean swMaintainTips = true;
    /**
     * 雨刮维修模式
     */
    private boolean swWiperRepair = true;
    /**
     * 超速报警
     */
    private boolean swOverspeed = true;
    /**
     * 疲劳驾驶提醒
     */
    private boolean swFatigueDrive = true;

    @Override
    public String getTag() {
        return tag;
    }

    @Override
    public void setTag(String tag) {
        this.tag = tag;
    }

    public boolean isWiperSensLowFlag() {
        return wiperSensLowFlag;
    }

    public void setWiperSensLowFlag(boolean wiperSensLowFlag) {
        this.wiperSensLowFlag = wiperSensLowFlag;
    }

    public boolean isWiperSensStandardFlag() {
        return wiperSensStandardFlag;
    }

    public void setWiperSensStandardFlag(boolean wiperSensStandardFlag) {
        this.wiperSensStandardFlag = wiperSensStandardFlag;
    }

    public boolean isWiperSensHighFlag() {
        return wiperSensHighFlag;
    }

    public void setWiperSensHighFlag(boolean wiperSensHighFlag) {
        this.wiperSensHighFlag = wiperSensHighFlag;
    }

    public boolean isWiperSensHighestFlag() {
        return wiperSensHighestFlag;
    }

    public void setWiperSensHighestFlag(boolean wiperSensHighestFlag) {
        this.wiperSensHighestFlag = wiperSensHighestFlag;
    }

    public boolean isSwAutoWindow() {
        return swAutoWindow;
    }

    public void setSwAutoWindow(boolean swAutoWindow) {
        this.swAutoWindow = swAutoWindow;
    }

    public boolean isSwAutoLock() {
        return swAutoLock;
    }

    public void setSwAutoLock(boolean swAutoLock) {
        this.swAutoLock = swAutoLock;
    }

    public boolean isSwAutoUnlock() {
        return swAutoUnlock;
    }

    public void setSwAutoUnlock(boolean swAutoUnlock) {
        this.swAutoUnlock = swAutoUnlock;
    }

    public boolean isSwMaintainTips() {
        return swMaintainTips;
    }

    public void setSwMaintainTips(boolean swMaintainTips) {
        this.swMaintainTips = swMaintainTips;
    }

    public boolean isSwWiperRepair() {
        return swWiperRepair;
    }

    public void setSwWiperRepair(boolean swWiperRepair) {
        this.swWiperRepair = swWiperRepair;
    }

    public boolean isSwOverspeed() {
        return swOverspeed;
    }

    public void setSwOverspeed(boolean swOverspeed) {
        this.swOverspeed = swOverspeed;
    }

    public boolean isSwFatigueDrive() {
        return swFatigueDrive;
    }

    public void setSwFatigueDrive(boolean swFatigueDrive) {
        this.swFatigueDrive = swFatigueDrive;
    }
}
