package com.bitech.vehiclesettings.carapi.constants;

public class CarSettingConstant {
    /*
    * 车设自己设置的参数
     */
    // 电量
    public static final double LOW_BATTERY = 12.5;
    // 里程
    public static final double LOW_MILEAGE = 50;
    // 壁纸
    public static final int WALLPAPER_TYPE_2D = 0;
    public static final int WALLPAPER_TYPE_3D = 1;
    // 随速补偿
    public static final int SPEED_COMPENSATION_NOTACTIVE = 0;
    // 关
    public static final int SPEED_COMPENSATION_OFF= 1;
    // 低
    public static final int SPEED_COMPENSATION_LOW = 2;
    // 中
    public static final int SPEED_COMPENSATION_MID = 3;
    // 高
    public static final int SPEED_COMPENSATION_HIGH = 4;

    //环绕音
    // 0 是关闭 1 是打开
    public static final int SURROUND_SOUND_MODE_OFF = 2;
    public static final int SURROUND_SOUND_MODE_ON = 1;

    // 头枕音响
    // 共享模式
    public static final int HEADREST_GX = 1;
    // 驾享模式
    public static final int HEADREST_JX = 2;
    //私享模式
    public static final int HEADREST_SX = 3;
    // 车外音效外放模式
    public static final int EXTERNAL_SOUND_MODE_ON = 1;
    public static final int EXTERNAL_SOUND_MODE_OFF = 0;
    // 虚拟现场模式
    //  0:风云HIFI, 1: 音乐厅, 2:演唱会，3: 关闭, 4:体育馆
    public static final int VIRTUAL_SCENE_MODE_OFF = 3;
    public static final int VIRTUAL_SCENE_MODE_MUSICHALL = 1;
    public static final int VIRTUAL_SCENE_MODE_CONCERT = 2;
    public static final int VIRTUAL_SCENE_MODE_FYHIFI = 0;
    public static final int VIRTUAL_SCENE_MODE_STADIUM = 4;

    // 报警音类型
    // 2: 国风 1: 科技 3: 新潮
    public static final int ALARM_NATIONAL = 0;
    public static final int ALARM_TECHNOLOGY = 1;
    public static final int ALARM_SMART = 2;

    // 摄像机功能
    // 0: 关闭, 1: 开启
    public static final int CAMERA_OFF = 0;
    public static final int CAMERA_ON = 1;

    // 声音模块
    // 0: 关闭, 1: 开启
    public static final int SOUND_OFF = 0;
    public static final int SOUND_ON = 1;

    // 声音类型 0=导航，1=语音，2=电话，3=媒体，4=报警，5=标准，6=蓝牙音乐，7=开机背景，98=未指定音源，99=无效值
    public static final String SOUND_TYPE_NAVI = "0";
    public static final String SOUND_TYPE_VOICE = "1";
    public static final String SOUND_TYPE_PHONE = "2";
    public static final String SOUND_TYPE_MEDIA = "3";
    public static final String SOUND_TYPE_ALARM = "4";
    public static final String SOUND_TYPE_STANDARD = "5";
    public static final String SOUND_TYPE_BLUETOOTH = "6";
    public static final String SOUND_TYPE_BOOT = "7";
    public static final String SOUND_TYPE_UNKNOWN = "98";
    public static final String SOUND_TYPE_INVALID = "99";
    // 音量调节
    public static final int VOLUME_0 = 0;
    public static final int VOLUME_1 = 1;
    public static final int VOLUME_5 = 5;
    public static final int VOLUME_10 = 10;
    public static final int VOLUME_12 = 12;
    public static final int VOLUME_15 = 15;
    public static final int VOLUME_25 = 25;
    public static final int VOLUME_31 = 31;
    // 音量调节百分比分母
    public static final int VOLUME_DIVISOR = 100;
    // 音量加一
    public static final int VOLUME_PLUS_ONE = 1;
    // 音量加二
    public static final int VOLUME_PLUS_TWO = 2;
    // 音量调高一点，低一点，最高，最低，高档，中档，低档
    public static final String VOLUME_ADJUST_HIGH_Little = "0";
    public static final String VOLUME_ADJUST_LOW_Little = "1";
    public static final String VOLUME_ADJUST_MAX = "2";
    public static final String VOLUME_ADJUST_MIN = "3";
    public static final String VOLUME_ADJUST_UPSCALE = "4";
    public static final String VOLUME_ADJUST_MID = "5";
    public static final String VOLUME_ADJUST_LOW = "6";
    // 摄像机状态
    // 0: 关闭, 1: 开启
    public static final int CAMERA_STATUS_OFF = 0;
    public static final int CAMERA_STATUS_ON = 1;

   // 自定义
    public static final int EQ_CUSTOMIZED = 1;
    // 全车模式
    public static final int EQ_ALL = 2;
    // 主驾模式
    public static final int EQ_DRIVER = 3;
    // 前排均衡
    public static final int EQ_SLEEP = 4;
    // 后排均衡
    public static final int EQ_VIP = 5;

    //遮阳帘异常信号
    //初始化状态
    public static final int Normal_Lized = 0X0;
    //学习状态
    public static final int Teach_Run = 0X0;
    //传感器状态
    public static final int Sensor_Status = 0X0;
    //电机防夹状态
    public static final int Anti_Pinch = 0X1;
    //热防护状态
    public static final int TProtection = 0X1;
    //过压状态
    public static final int Over_Vol = 0X1;
    //欠压状态
    public static final int Under_Vol = 0X1;
    //氛围灯开关 ON=1 OFF=0
    public static final int InLight_ON = 1;
    public static final int InLight_OFF = 0;
    // 氛围灯灯光效果 静态=0 呼吸=1 渐变=2 音乐律动=3
    public static final int InLight_Static = 0;
    public static final int InLight_Breathe = 1;
    public static final int InLight_Gradient = 2;
    public static final int InLight_MusicRhythm = 3;
    //  负一屏uri
    public static final String Negative_Creen = "content://com.chery.systemui.api";
    //  氛围灯配置字 0=不存在 1=存在
    public static final int InLight_Config_Exist = 1;
    public static final int InLight_Config_NotExist = 0;
    //  氛围灯分区控制配置字 0=不存在 1=存在
    public static final int InLight_Partition_Control_Exist = 1;
    public static final int InLight_Partition_Control_NotExist = 0;
    //  氛围灯颜色 0=红色 1=橙色 2=黄色 3=绿色 4=青色 5=蓝色 6=紫色
    public static final int InLight_Red = 0;
    public static final int InLight_Orange = 1;
    public static final int InLight_Yellow = 2;
    public static final int InLight_Green = 3;
    public static final int InLight_Cyan = 4;
    public static final int InLight_Blue = 5;
    public static final int InLight_Purple = 6;
    //  氛围灯双色模式 7=双色 8=双色 9=双色 10=双色 11=双色
    public static final int InLight_Double_Color_Mode_7 = 7;
    public static final int InLight_Double_Color_Mode_8 = 8;
    public static final int InLight_Double_Color_Mode_9 = 9;
    public static final int InLight_Double_Color_Mode_10 = 10;
    public static final int InLight_Double_Color_Mode_11 = 11;
    //  氛围灯亮度调到最高
    public static final int InLight_Brightness_Max = 5;
    //  氛围灯亮度调到最低
    public static final int InLight_Brightness_Min = 6;
    //  氛围灯亮度100
    public static final int InLight_Brightness_100 = 100;
    //  氛围灯亮度0
    public static final int InLight_Brightness_0 = 0;
    //  氛围灯自动亮度开关 0=关闭 1=开启
    public static final int InLight_Auto_Brightness_ON = 1;
    public static final int InLight_Auto_Brightness_OFF = 0;
    //    自动大灯调高一点、低一点、最高、最低 0=调高 1=调低 2=低档 3=中档 4=高档 5=调到最高 6=调到最低
    public static final int Auto_High_Light_Adjust_Up = 0;
    public static final int Auto_High_Light_Adjust_Down = 1;
    public static final int Auto_High_Light_Adjust_Low = 2;
    public static final int Auto_High_Light_Adjust_Mid = 3;
    public static final int Auto_High_Light_Adjust_High = 4;
    public static final int Auto_High_Light_Adjust_Max = 5;
    public static final int Auto_High_Light_Adjust_Min = 6;
    //  极致省电模式状态 0=不存在 1=存在
    public static final int Ultra_Power_Saving_Mode_Exist = 1;
    public static final int Ultra_Power_Saving_Mode_NotExist = 0;
    //  展车模式状态 0=关闭 1=开启
    public static final int Display_Car_Mode_Close = 0;
    public static final int Display_Car_Mode_Open = 1;
    //  车灯类型 0=车内 1=车外
    public static final int Car_Light_Type_Inside = 0;
    public static final int Car_Light_Type_Outside = 1;
    //  主题状态 0=主题模式 1=自定义模式
    public static final int Theme_Mode_Theme = 0;
    public static final int Theme_Mode_Customize = 1;
    //  头枕音响模式 0=私享 1=驾享 2=共享
    public static final int Headrest_Sound_Mode_Private = 0;
    public static final int Headrest_Sound_Mode_Drive = 1;
    public static final int Headrest_Sound_Mode_Shared = 2;
    //set遮阳帘状态
    //1：开启,2：关闭,3：停止
    public static final int SET_SUNSHADE_OPEN = 0X1;
    public static final int SET_SUNSHADE_CLOSE = 0X2;
    public static final int SET_SUNSHADE_STOP = 0X3;

    //get遮阳帘状态
    //0:Not Active,1:CLOSE,2:OPEN,3:CLOSING,4:OPENING,5:OTHER
    public static final int Sunshade_NO = 0X0;
    public static final int Sunshade_CLOSE = 0X1;
    public static final int Sunshade_OPEN = 0X2;
    public static final int SUNSHADE_CLOSING = 0X3;
    public static final int SUNSHADE_OPENING = 0X4;
    public static final int Sunshade_OTHER = 0X5;
    //遮阳帘开度
    public static final int SUNSHADE_MAX = 100;
    public static final int SUNSHADE_90 = 90;
    public static final int SUNSHADE_10 = 10;
    public static final int SUNSHADE_ADD = 10;
    public static final int SUNSHADE_MIN = 0;

    public static final int Reversing_RIGHT = 0X1;
    public static final int Reversing_LEFT = 0X2;
    public static final int Reversing_BOTH = 0X3;
    public static final int SET_Reversing_RIGHT = 0X2;
    public static final int SET_Reversing_LEFT = 0X3;
    public static final int SET_Reversing_BOTH = 0X4;

    //洗车模式
    // 1：关闭 ，2：开启
    public static final int WASH_CAR_MODE_OFF = 0X1;
    public static final int WASH_CAR_MODE_ON = 0X2;

    //后视镜加热
    // 0：关闭,1：开启
    public static final int MIRROR_HOT_CLOSE = 0X0;
    public static final int MIRROR_HOT_OPEN = 0X1;

    //外后视镜自动折叠开关
    // 0：关闭,1：开启
    public static final int MIRROR_FOLD_CLOSE = 0X0;
    public static final int MIRROR_FOLD_OPEN = 0X1;

    //获取后视镜状态
    // 0：关闭,1：仅右侧,2：仅左侧,3：两侧同时
    public static final int MIRROR_OFF = 0X0;
    public static final int MIRROR_RIGHT = 0X1;
    public static final int MIRROR_LEFT = 0X2;
    public static final int MIRROR_BOTH = 0X3;

    //set倒车后视镜自动调节开关
    // 0：关闭,4：两侧同时
    public static final int SET_MIRROR_OFF = 0X1;
    public static final int SET_MIRROR_BOTH = 0X4;

    //后视镜折叠状态
    // 1：折叠,2：展开
    public static final int MIRROR_FOLD = 0X1;
    public static final int MIRROR_UNFOLD = 0X2;

    //获取后尾门状态
    // 0：开启
    public static final int TailGate_OPEN = 0X0;

    //set后尾门状态
    // 1：开启,2：关闭
    public static final int SET_TailGate_ON = 0X1;
    public static final int SET_TailGate_OFF = 0X2;
    //锁车收起遮阳帘开关
    //1：开启，2：关闭
    public static final int Lock_SunRoofShade_OPEN = 0X1;
    public static final int Lock_SunRoofShade_CLOSE = 0X2;

    //车速
    public static final int SPEED_0 = 0;
    public static final int SPEED_4 = 4;
    //电源
    public static final int GEAR_ON = 0x2;
    //挡位
    //1：P档,3：N档
    public static final int GEAR_POSITION_P = 1;
    public static final int GEAR_POSITION_N = 3;
    //四门两盖
    //0：关闭
    public static final int FOUR_DOOR_CONFIG_OFF = 0;
    //分屏开关
    //0：关闭,1：开启
    public static final int SPLIT_SCREEN_OFF = 0;
    public static final int SPLIT_SCREEN_ON = 1;
    //屏幕亮度
    //最大亮度10，最小亮度1

    public static final int SCREEN_BRIGHTNESS_MAX = 10;
    public static final int SCREEN_BRIGHTNESS_MIN = 1;
    //屏幕亮度自动开关
    //0：关闭,1：开启
    public static final int SCREEN_AUTO_OFF = 0;
    public static final int SCREEN_AUTO_ON = 1;
    /**
     * 统色
     * 0x0: blue
     * 0x1: purple
     * 0x2: cyan
     * 0x3: green
     * 0x4: yellow
     * 0x5: red
     */
    public static final int COLOR_BLUE = 0x0;
    public static final int COLOR_PURPLE = 0x1;
    public static final int COLOR_CYAN = 0x2;
    public static final int COLOR_GREEN = 0x3;
    public static final int COLOR_ORANGE = 0x4;
    public static final int COLOR_RED = 0x5;
    //负一屏url
    public static final String NEGATIVE_CREEN = "content://com.chery.systemui.api";
    //打开负一屏接口
    public static final String OPEN_NEGATIVE_SCREEN = "showControlCenter";
    //关闭负一屏接口
    public static final String CLOSE_NEGATIVE_SCREEN = "hideControlCenter";
    //负一屏状态获取
    public static final String NEGATIVE_SCREEN_STATUS = "getControlCenterStatus";
    //负一屏状态
    public static final String NEGATIVE_SCREEN_STATUS_OPEN = "param_split_status";
    //分屏url
    public static final String SPLIT_SCREEN_URL = "content://com.android.systemui.splitscreen";
    //获取分屏状态
    public static final String SPLIT_SCREEN_STATUS = "query_split_status";
    //返回分屏状态
    public static final String SPLIT_SCREEN_STATUS_RESULT = "param_split_status";

    //后尾门配置字
    //0：不存在，1：存在
    public static final int TAIL_GATE_CONFIG_N = 0;

    //后视镜加热配置字
    //0：不存在，1：存在
    public static final int MIRROR_HEAT_CONFIG_N = 0;
    //尾门上限值
    public static final int TAIL_DOOR_MAX = 95;
    //尾门下限值
    public static final int TAIL_DOOR_MIN = 50;
    //小号字体
    public static final float FONT_SIZE_LITTLE = 1.0f;
    //标准字体
    public static final float FONT_SIZE_STANDARD = 1.15f;
    //大号字体
    public static final float FONT_SIZE_LARGE = 1.3f;
    //设置动态参数
    public static final int SIGNAL_DYNAMICS = 1;
    //设置的CLTC参数
    public static final int SIGNAL_CLTC = 2;
    //设置的WLTC参数
    public static final int SIGNAL_WLTC = 3;
    //12小时制
    public static final int TIME_DISPLAY_12 = 0;
    //24小时制
    public static final int TIME_DISPLAY_24 = 1;
    //打开自动校准日期时间
    public static final int AUTO_CALIBRATION_FALSE = 1;
    //关闭自动校准日期时间
    public static final int AUTO_CALIBRATION_TRUE = 0;
    //油耗单位千米每升
    public static final int FUEL_UNIT_KML = 1;
    //油耗单位升每百公里
    public static final int FUEL_UNIT_L100KM = 0;
    //打开导航时压低媒体音
    public static final int NAV_LOWER_MEDIA_ON = 1;
    //关闭导航时压低媒体音
    public static final int NAV_LOWER_MEDIA_OFF = 0;
    //打开后排屏锁
    public static final int Lock_REAR_SCREEN = 1;
    //关闭后排屏锁
    public static final int Unlock_REAR_SCREEN = 0;
    //手机连接
    public static final int CARPLAY = 1;
    public static final int HICAR = 7;
    public static final int CARLINK = 8;

    /**
     * 雨刮传感器模式
     * WIPER_SENS_MODE_1 :雨刮灵敏度1档
     * WIPER_SENS_MODE_2 :雨刮灵敏度2档
     * WIPER_SENS_MODE_3 :雨刮灵敏度3档
     * WIPER_SENS_MODE_4 :雨刮灵敏度4档
     * UP_LITTLE :高一点
     * low_LITTLE :低一点
     * HIGH_LEVEL :高档
     * MID_LEVEL :中档
     * LOW_LEVEL :低档
     * HIGHEST_LEVEL :最高
     * MINIMUM_LEVEL :最低
     */
    public static final int WIPER_SENS_MODE_1 = 0x1;
    public static final int WIPER_SENS_MODE_2 = 0x2;
    public static final int WIPER_SENS_MODE_3 = 0x3;
    public static final int WIPER_SENS_MODE_4 = 0x4;

    /**
     * 方向盘自定义按钮
     * DASH_CAM : 行车记录仪抓拍
     * MIRROR_ADJUSTMENT : 后视镜调节
     * SWITCHING_OF_AUDIO_SOURCES : 音频切换
     * STEERING_WHEEL_ADJUSTMENT : 方向盘调节
     * PANORAMIC_IMAGERY : 全景影像
     */
    public static final int DASH_CAM = 0x0;
    public static final int AVM = 0x1;
    public static final int HUD = 0x2;
    public static final int MIRROR_ADJUSTMENT = 0x3;
    public static final int SWITCHING_OF_AUDIO_SOURCES = 0x4;
    public static final int STEERING_WHEEL_ADJUSTMENT = 0x5;
    public static final int PANORAMIC_IMAGERY = 0x6;

    /**
     * 设置后视镜方向调到指定方向极值
     * MAX : 最大
     * MIN : 最小
     */
    public static final int MAX = 100;
    public static final int MIN = 1;

    /**
     * 油箱盖锁
     * LOCKED : 上锁
     * UNLOCK : 解锁
     */
    public static final int LOCKED = 0;
    public static final int UNLOCK = 1;

    /**
     * 收到雨刮维修模式开关置灰请求
     * NO_FAIL_CAUSE :未置灰
     * FAIL_CAUSE_1 : FLZCU_WipeMntnModeFailCause=0x1
     * FAIL_CAUSE_2 : FLZCU_WipeMntnModeFailCause=0x2
     * FAIL_CAUSE_3 : FLZCU_WipeMntnModeFailCause=0x3
     * WIPER_REPAIR_MODE_OPEN :雨刮维修模式开启
     * WIPER_REPAIR_MODE_CLOSE :雨刮维修模式关闭
     */
    public static final int FAIL_CAUSE_1 = 0x1;
    public static final int FAIL_CAUSE_2 = 0x2;
    public static final int FAIL_CAUSE_3 = 0x3;
    public static final int WIPER_CLOSE_SIGNAL_0 = 0x0;
    public static final int WIPER_CLOSE_SIGNAL_1 = 0x1;
    public static final int WIPER_CLOSE_SIGNAL_2 = 0x2;
    public static final int WIPER_OPEN_SIGNAL = 0x3;
    public static final int WIPER_REPAIR_MODE_OPEN = 0x2;
    public static final int WIPER_REPAIR_MODE_CLOSE = 0x1;
    public static final int WASH_MODE_OPEN = 0x2;
    //手机无线充电
    public static final int PHONE_CHARGE_CLOSE = 0x0;
    public static final int PHONE_CHARGE = 0x1;
    public static final int PHONE_CHARGE_OPEN = 0x2;
    //碰撞报警系统等级设置
    public static final int LEVEL_HIGH = 0;
    public static final int LEVEL_LOW = 1;
    public static final int LEVEL_MAX = 2;
    public static final int LEVEL_MIN = 3;
    public static final int LEVEL_HIGH_D = 4;
    public static final int LEVEL_MID_D = 5;
    public static final int LEVEL_LOW_D = 6;
    //调低车身高度
    public static final int ADJUST_CAR_HEIGHT_DOWN = 0;
    //调高车身高度
    public static final int ADJUST_CAR_HEIGHT_UP = 1;
    //车辆模式 0:纯电 1:舒适混动 2:舒适 3:风云GT 4:雨雪 5:自定义 6:个性化
    public static final int CAR_MODE_PURE_ELECTRIC = 0;
    public static final int CAR_MODE_COMFORT_MIX = 1;
    public static final int CAR_MODE_COMFORT = 2;
    public static final int CAR_MODE_FUTURE_GT = 3;
    public static final int CAR_MODE_SNOW = 4;
    public static final int CAR_MODE_CUSTOM = 5;
    public static final int CAR_MODE_PERSONALIZATION = 6;
    //关闭界面
    public static final int CLOSE_SCREEN = -1;
    //胎压异常
    public static final int TIRE_PRESSURE_ABNORMAL = 1;
    //胎压正常
    public static final int TIRE_PRESSURE_NORMAL = 0;
    //胎压信号值
    public static final int TIRE_PRESSURE_SIGNAL_0 = 0;
    public static final int TIRE_PRESSURE_SIGNAL_7 = 7;
    //打开屏保
    public static final int OPEN_SCREEN_SAVER = 1;
    //关闭屏保
    public static final int CLOSE_SCREEN_SAVER = 0;
    //屏保时间
    public static final long SCREEN_SAVER_TIME = 300;
    //屏保topic
    public static final String SCREEN_SAVER_TOPIC = "DrivingInfo/PowerKey";
    //倒车后视镜自动调节配置字
    //0 : 不存在 1 : 存在
    public static final int AUTO_MIRROR_NO = 0;
    public static final int AUTO_MIRROR_YES = 1;

    /*
     * 讯飞传入参数
     */

    // 按键音
    public static final int XF_SOUND_ON = 0;
    public static final int XF_SOUND_OFF = 1;


}
