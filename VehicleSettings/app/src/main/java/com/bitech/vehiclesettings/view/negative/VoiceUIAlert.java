package com.bitech.vehiclesettings.view.negative;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertNVoiceControlBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSDatetimeBinding;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class VoiceUIAlert extends BaseDialog {

    private final Context context;
    private boolean isCan = true;
    protected DialogAlertNVoiceControlBinding binding;
    private Handler handler;
    private Runnable autoDismissTask = () -> {
        if (this.isShowing()) {
            this.dismiss();
        }
    };
    VoicePresenter presenter = VoicePresenter.getInstance();

    public VoiceUIAlert(@NonNull Context context) {
        super(context, R.style.Dialog);
        this.context = context;
    }

    @Override
    protected void onStart() {
        super.onStart();
        this.setCancelable(isCan);
        handler.postDelayed(autoDismissTask, 3000);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置dialog的bind
        binding = DialogAlertNVoiceControlBinding.inflate(LayoutInflater.from(context));
        this.setContentView(binding.getRoot());
        Window window = this.getWindow();
        WindowManager.LayoutParams layoutParams = window.getAttributes();
        layoutParams.width = 816;
        layoutParams.height = 680;
        layoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        window.setAttributes(layoutParams);

        handler = new Handler(Looper.getMainLooper());
        init();
    }

    @Override
    protected void onStop() {
        super.onStop();
        handler.removeCallbacks(autoDismissTask);
    }

    public void init() {
        initSeekbar();
        initListener();
    }

    void initSeekbar() {
        int voiceNavi = presenter.getVoiceNavi();
        int voiceVR = presenter.getVoiceVR();
        int voiceMedia = presenter.getVoiceMedia();
        int voicePhone = presenter.getVoicePhone();
        Log.d("VoiceUIAlert", "导航:" + voiceNavi + " 语音:" + voiceVR + " 媒体:" + voiceMedia + " 电话:" + voicePhone);
        binding.sbNavi.setProgress(voiceNavi);
        binding.sbVR.setProgress(voiceVR);
        binding.sbMedia.setProgress(voiceMedia);
        binding.sbPhone.setProgress(voicePhone);

        binding.getRoot().post(() -> {
            if (voiceNavi == 0) {
                binding.sbNavi.setIcon(R.mipmap.ic_sound_navigation_dock_off);
            } else {
                binding.sbNavi.setIcon(R.mipmap.ic_sound_navigation_dock);
            }
            if (voiceVR == 0) {
                binding.sbVR.setIcon(R.mipmap.ic_sound_voice_dock_off);
            } else {
                binding.sbVR.setIcon(R.mipmap.ic_sound_voice_dock);
            }
            if (voiceMedia == 0) {
                binding.sbMedia.setIcon(R.mipmap.ic_sound_media_dock_off);
            } else {
                binding.sbMedia.setIcon(R.mipmap.ic_sound_media_dock);
            }
            if (voicePhone == 0) {
                binding.sbPhone.setIcon(R.mipmap.ic_sound_phone_dock_off);
            } else {
                binding.sbPhone.setIcon(R.mipmap.ic_sound_phone_dock);
            }
            binding.sbNavi.setSeekBarStyle(MyApplication.getContext(), binding.sbNavi.getSeekBar(), R.drawable.progress_white_max, R.drawable.progress_white);
            binding.sbPhone.setSeekBarStyle(MyApplication.getContext(), binding.sbPhone.getSeekBar(), R.drawable.progress_white_max, R.drawable.progress_white);
            binding.sbMedia.setSeekBarStyle(MyApplication.getContext(), binding.sbMedia.getSeekBar(), R.drawable.progress_white_max, R.drawable.progress_white);
            binding.sbVR.setSeekBarStyle(MyApplication.getContext(), binding.sbVR.getSeekBar(), R.drawable.progress_white_max, R.drawable.progress_white);
        });
    }

    void initListener() {
        binding.sbMedia.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            private int lastUserProgress = 0;

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                resetAutoDismissTimer();

                if (fromUser) {
                    lastUserProgress = progress;
                    int limited = presenter.getMediaBrightnessLimit(lastUserProgress);
                    seekBar.setProgress(limited);
                }
                if (progress == 0) {
                    binding.sbMedia.setIcon(R.mipmap.ic_sound_media_dock_off);
                } else {
                    binding.sbMedia.setIcon(R.mipmap.ic_sound_media_dock);
                }
                binding.sbMedia.setSeekBarStyle(MyApplication.getContext(), seekBar, R.drawable.progress_white_max, R.drawable.progress_white);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                resetAutoDismissTimer();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                resetAutoDismissTimer();
                presenter.setVoiceMedia(lastUserProgress);
                binding.sbMedia.setSeekBarStyle(MyApplication.getContext(), seekBar, R.drawable.progress_white_max, R.drawable.progress_white);
                presenter.playMediaAudio();
            }
        });

        binding.sbPhone.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                resetAutoDismissTimer();

                binding.sbPhone.setSeekBarStyle(MyApplication.getContext(), seekBar, R.drawable.progress_white_max, R.drawable.progress_white);
                if (progress == 0) {
                    binding.sbPhone.setIcon(R.mipmap.ic_sound_phone_dock_off);
                } else {
                    binding.sbPhone.setIcon(R.mipmap.ic_sound_phone_dock);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                resetAutoDismissTimer();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                resetAutoDismissTimer();
                presenter.setVoicePhone(seekBar.getProgress());
                binding.sbPhone.setSeekBarStyle(MyApplication.getContext(), seekBar, R.drawable.progress_white_max, R.drawable.progress_white);
                presenter.playPhoneAudio();
            }
        });

        binding.sbVR.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                resetAutoDismissTimer();

                binding.sbVR.setSeekBarStyle(MyApplication.getContext(), seekBar, R.drawable.progress_white_max, R.drawable.progress_white);
                if (progress == 0) {
                    binding.sbVR.setIcon(R.mipmap.ic_sound_voice_dock_off);
                } else {
                    binding.sbVR.setIcon(R.mipmap.ic_sound_voice_dock);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                resetAutoDismissTimer();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                resetAutoDismissTimer();
                presenter.setVoiceVR(seekBar.getProgress());
                binding.sbVR.setSeekBarStyle(MyApplication.getContext(), seekBar, R.drawable.progress_white_max, R.drawable.progress_white);
                presenter.playVRAudio();
            }
        });

        binding.sbNavi.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                resetAutoDismissTimer();

                binding.sbNavi.setSeekBarStyle(MyApplication.getContext(), seekBar, R.drawable.progress_white_max, R.drawable.progress_white);
                if (progress == 0) {
                    binding.sbNavi.setIcon(R.mipmap.ic_sound_navigation_dock_off);
                } else {
                    binding.sbNavi.setIcon(R.mipmap.ic_sound_navigation_dock);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                resetAutoDismissTimer();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                resetAutoDismissTimer();
                presenter.setVoiceNavi(seekBar.getProgress());
                binding.sbNavi.setSeekBarStyle(MyApplication.getContext(), seekBar, R.drawable.progress_white_max, R.drawable.progress_white);
                presenter.playNaviAudio();
            }
        });
    }

    /**
     * 重置自动关闭定时器
     */
    private void resetAutoDismissTimer() {
        if (handler != null && autoDismissTask != null) {
            handler.removeCallbacks(autoDismissTask);
            handler.postDelayed(autoDismissTask, 3000);
        }
    }
}