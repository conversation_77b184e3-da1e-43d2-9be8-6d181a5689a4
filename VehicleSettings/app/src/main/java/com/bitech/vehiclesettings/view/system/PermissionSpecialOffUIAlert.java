package com.bitech.vehiclesettings.view.system;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertRCameraSwitchOffBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionSpecialOffBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.recognition.CameraSwitchOnUIAlert;
import com.bitech.vehiclesettings.view.recognition.PrivacyPolicyUIAlert;

public class PermissionSpecialOffUIAlert extends Dialog {
    private static final String TAG = PermissionSpecialOffUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public PermissionSpecialOffUIAlert(@NonNull Context context) {
        super(context);
    }

    public PermissionSpecialOffUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PermissionSpecialOffUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        PermissionSpecialOffUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private boolean cameraConfirmFlag = false;
        private boolean cameraCancelFlag = false;
        protected DialogAlertSPermissionSpecialOffBinding binding;

        private PrivacyPolicyUIAlert.Builder privacyPolicyUIAlert;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private PermissionSpecialOffUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }


        /**
         * Create the custom dialog
         */
        public PermissionSpecialOffUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PermissionSpecialOffUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSPermissionSpecialOffBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 548;
            window.setAttributes(layoutParams);

            // 设置确定按钮效果
            clickConfirmCamera();
            // 设置取消按钮效果
            clickCancelCamera();

            return dialog;
        }

        // 设置确定按钮点击逻辑
        private void clickConfirmCamera() {
            BindingUtil.bindClick(binding.tvSmokeConfirm, v -> {
                Prefs.setGlobalValue(PrefsConst.SYSTEM_ANALYSIS, 0);
                dialog.dismiss();
            });
        }

        // 设置取消按钮点击逻辑
        private void clickCancelCamera() {
            binding.tvSmokeCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
        PermissionSpecialUIAlert.Builder permissionSpecialUIAlert = new PermissionSpecialUIAlert.Builder(this.getContext());
        permissionSpecialUIAlert.create().show();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void confirm();
    }
}
