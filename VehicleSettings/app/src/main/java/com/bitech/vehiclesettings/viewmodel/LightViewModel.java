package com.bitech.vehiclesettings.viewmodel;

import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

public class LightViewModel extends ViewModel {
    // 氛围灯开关
    private MutableLiveData<Integer> lightSw = new MediatorLiveData<>();
    // 主题 自定义
    private MutableLiveData<Integer> themeSw = new MediatorLiveData<>();
    // 色块选中
    private MutableLiveData<Integer> colorPos = new MediatorLiveData<>();
    // 亮度
    private MutableLiveData<Integer> colorBrightPos = new MediatorLiveData<>();
    // 车灯效果
    private MutableLiveData<Integer> lampEffect = new MediatorLiveData<>();
    // 大灯高度调节

    // 大灯延时关闭

    // 车灯设置 - 靠近迎宾
    private MutableLiveData<Integer> approachingWelcome = new MediatorLiveData<>();
    // 自动顶灯
    private MutableLiveData<Integer> automaticCeiling = new MediatorLiveData<>();
    // 大灯音量律动 sw_headlight_music_rhythm
    private MutableLiveData<Integer> lampMusicRhythm = new MediatorLiveData<>();
    // 智能远近光切换 sw_high_low_switching
    private MutableLiveData<Integer> highLowSwitch = new MediatorLiveData<>();
    // 智能迎宾灯开关 intelligent_welcome
    private MutableLiveData<Integer> intelligentWelcome = new MediatorLiveData<>();
    // 智能迎宾灯

    public MutableLiveData<Integer> getLightSw() {
        return lightSw;
    }

    public void setLightSw(Integer status) {
        lightSw.postValue(status);
    }

    public MutableLiveData<Integer> getThemeSw() {
        return themeSw;
    }

    public void setThemeSw(Integer status) {
        themeSw.postValue(status);
    }

    public MutableLiveData<Integer> getColorPos() {
        return colorPos;
    }

    public void setColorPos(Integer status) {
        colorPos.postValue(status);
    }

    public MutableLiveData<Integer> getColorBrightsPos() {
        return colorBrightPos;
    }

    public void setColorBrightsPos(Integer status) {
        colorBrightPos.postValue(status);
    }

    public MutableLiveData<Integer> getLampEffect() {
        return lampEffect;
    }

    public void setLampEffect(Integer status) {
        lampEffect.postValue(status);
    }


    public MutableLiveData<Integer> getApproachingWelcome() {
        return approachingWelcome;
    }

    public void setApproachingWelcome(Integer status) {
        approachingWelcome.postValue(status);
    }

    public MutableLiveData<Integer> getAutomaticCeiling() {
        return automaticCeiling;
    }

    public void setAutomaticCeiling(Integer status) {
        automaticCeiling.postValue(status);
    }

    public MutableLiveData<Integer> getLampMusicRhythm() {
        return lampMusicRhythm;
    }

    public void setLampMusicRhythm(Integer status) {
        lampMusicRhythm.postValue(status);
    }

    public MutableLiveData<Integer> getHighLowSwitch() {
        return highLowSwitch;
    }

    public void setHighLowSwitch(Integer status) {
        highLowSwitch.postValue(status);
    }

    public MutableLiveData<Integer> getIntelligentWelcome() {
        return intelligentWelcome;
    }

    public void setIntelligentWelcome(Integer status) {
        intelligentWelcome.postValue(status);
    }


}
