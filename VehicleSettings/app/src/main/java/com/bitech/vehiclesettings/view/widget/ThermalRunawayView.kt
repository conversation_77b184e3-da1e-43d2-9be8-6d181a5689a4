package com.bitech.vehiclesettings.view.widget

import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @Description: 电池热失控报警.
 **/
class ThermalRunawayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var warningOutLayerView: ImageView
    private var backgroundLayerView: View
    private var carImageView: ImageView
    private var warningImageView: ImageView

    private var alphaAnimator: ObjectAnimator? = null
    private var scaleAnimator: ObjectAnimator? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.dialog_thermal_runaway, this, true)
        warningOutLayerView = findViewById(R.id.warning_outlay)
        backgroundLayerView = findViewById(R.id.background_layer)
        carImageView = findViewById(R.id.car_image)
        warningImageView = findViewById(R.id.warning_image)

    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()

        //启动动画 边框红色闪烁 警告标志缩放效果
        startAlphaAnimation(warningOutLayerView, ALPHA_TO)
        startScaleAnimation(warningImageView)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()

        //窗口销毁 取消动画
        alphaAnimator?.cancel()
        scaleAnimator?.cancel()
        LogUtil.d(TAG, "onDetachedFromWindow Animation Cancel")

    }

    /**
     * 启动淡入淡出动画
     * @param view 动画视图
     * @param alpha 透明度
     */
    private fun startAlphaAnimation(view: View, alpha: Float) {
        LogUtil.d(TAG, "startAlphaAnimation")
        alphaAnimator = ObjectAnimator.ofFloat(
            view,
            "alpha",
            ALPHA_FROM, alpha
        ).apply {
            duration = DEFAULT_DURATION
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.REVERSE
            start()
        }
    }

    /**
     * 启动缩放动画
     * @param view 动画视图
     *
     */
    private fun startScaleAnimation(view: View) {
        LogUtil.d(TAG, "startScaleAnimation")
        scaleAnimator = ObjectAnimator.ofPropertyValuesHolder(
            view,
            PropertyValuesHolder.ofFloat("scaleX", SCALE_X_TO, SCALE_X_FROM),
            PropertyValuesHolder.ofFloat("scaleY", SCALE_Y_TO, SCALE_Y_FROM)
        ).apply {
            duration = DEFAULT_DURATION
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.REVERSE
            start()
        }
    }

    companion object {
        private const val TAG = "ThermalRunawayView"

        //动画时长
        private const val DEFAULT_DURATION = 250L

        //缩放比例结束值
        private const val SCALE_X_TO = 0.8f
        private const val SCALE_Y_TO = 0.8f

        //缩放比例起始值
        private const val SCALE_X_FROM = 1.0f
        private const val SCALE_Y_FROM = 1.0f

        //透明度结束值
        private const val ALPHA_TO = 0.6f

        //透明度起始值
        private const val ALPHA_FROM = 1.0f
    }
}