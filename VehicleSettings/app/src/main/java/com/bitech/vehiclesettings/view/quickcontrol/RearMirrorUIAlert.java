package com.bitech.vehiclesettings.view.quickcontrol;

import android.content.Context;
import android.os.Message;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.quick.IQuickManagerListener;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl;
import com.bitech.vehiclesettings.databinding.DialogAlertQRearMirrorBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenter;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenterListener;
import com.bitech.vehiclesettings.repository.QuickRepository;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.viewmodel.QuickViewModel;

/**
 * FileName: NoTitleUIAlert
 * Author: WUY1WHU
 * Date: 2024/6/19 10:29
 * Description:通用对话框
 */
public class RearMirrorUIAlert extends BaseDialog {
    private static final String TAG = RearMirrorUIAlert.class.getSimpleName();

    private static onProgressChangedListener onProgressChangedListener;


    private int backRearAdjustStatus;

    public RearMirrorUIAlert(Context context) {
        super(context);
    }

    public RearMirrorUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected RearMirrorUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static RearMirrorUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(RearMirrorUIAlert.onProgressChangedListener onProgressChangedListener) {
        RearMirrorUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder implements SafeHandlerCallback {

        private final Context context;
        private boolean isCan = true;
        private String promptText;
        public RearMirrorUIAlert dialog = null;
        protected DialogAlertQRearMirrorBinding binding;
        private boolean globalAlert = false;
        private QuickPresenterListener quickPresenter;
        private QuickViewModel viewModel;
        private SafeHandler quickHandler;
        private static QuickManager quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);

        private void quickControllerRegLightListen() {
            if (quickManager == null) {
                quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);
            }
            quickManager.addCallback(TAG, new IQuickManagerListener() {
                @Override
                public void autoStsCallback(int status) {
                    Log.d(TAG, "autoStsCallback: 后视镜自动折叠: " + status);
                    if (status == CarQuickControl.GetAutoRearMirrorFoldSts.NOT_AUTO_FOLD) {
                        status = CarQuickControl.ButtonSts.OFF;
                    } else if (status == CarQuickControl.GetAutoRearMirrorFoldSts.AUTO_FOLD) {
                        status = CarQuickControl.ButtonSts.ON;
                    } else {
                        status = Integer.MIN_VALUE;
                    }
                    if (viewModel.getAutoRearMirrorFold().getValue() != null && viewModel.getAutoRearMirrorFold().getValue() != status) {
                        if (status != Integer.MIN_VALUE) {
                            viewModel.setAutoRearMirrorFold(status);
                        }
                    }
                }

                @Override
                public void autoHeatingFbCallback(int status) {
                    Log.d(TAG, "autoHeatingFbCallback: 雨天自动加热外后视镜: " + status);
                    if (status == CarQuickControl.GetAutoHeatingRearMirrorSts.CLOSE) {
                        status = CarQuickControl.ButtonSts.OFF;
                    } else if (status == CarQuickControl.GetAutoHeatingRearMirrorSts.OPEN) {
                        status = CarQuickControl.ButtonSts.ON;
                    } else {
                        status = Integer.MIN_VALUE;
                    }
                    if (viewModel.getAutoHotRearMirror().getValue() != null && viewModel.getAutoHotRearMirror().getValue() != status) {
                        if (status != Integer.MIN_VALUE) {
                            viewModel.setAutoHotRearMirror(status);
                        }
                    }
                }

                @Override
                public void backAutoRearMirrorAdjustCallback(int status) {
                    Log.d(TAG, "backAutoRearMirrorAdjustCallback: 倒车时后视镜自动调节: " + status);
                    // 0 == OFF
                    // 1 == Right
                    // 2 == Left
                    // 3 == Both
                    if (status == CarQuickControl.GetBackAutoRearMirrorAdjustSts.OFF) {
                        status = 0;
                    } else if (status == CarQuickControl.GetBackAutoRearMirrorAdjustSts.BOTH_SIDES) {
                        status = 1;
                    } else if (status == CarQuickControl.GetBackAutoRearMirrorAdjustSts.ONLY_LEFT_SIDE) {
                        status = 2;
                    } else if (status == CarQuickControl.GetBackAutoRearMirrorAdjustSts.ONLY_RIGHT_SIDE) {
                        status = 3;
                    } else {
                        status = Integer.MIN_VALUE;
                    }
                    if (viewModel.getBackRearAdjust().getValue() != null && viewModel.getBackRearAdjust().getValue() != status) {
                        if (status != Integer.MIN_VALUE) {
                            dialog.backRearAdjustStatus = status;
                            viewModel.setBackRearAdjust(status);
                        }
                    }
                }
            });
            quickManager.registerListener();
        }

        public void setGlobalAlert(boolean flag) {
            this.globalAlert = flag;
        }

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public Builder setContent(String text) {
            this.promptText = text;
            return this;
        }


        public Builder setConfirmAndCancel(boolean flag) {

            return this;
        }

        /**
         * Create the custom dialog
         */
        public RearMirrorUIAlert create() {
            int themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue);
            Context themedContext = new ContextThemeWrapper(context, themeId);
            if (dialog == null) {
                dialog = new RearMirrorUIAlert(themedContext, R.style.Dialog);
            }
            binding = DialogAlertQRearMirrorBinding.inflate(LayoutInflater.from(themedContext));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());

            context.setTheme(Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue));
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1992;
            layoutParams.height = 922;
            layoutParams.type = globalAlert ? WindowManager.LayoutParams.TYPE_SYSTEM_ALERT :
                    WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            window.setAttributes(layoutParams);

            quickPresenter = new QuickPresenter<QuickRepository>(context);
            viewModel = new QuickViewModel();
            quickHandler = new SafeHandler(this);
            dialog.backRearAdjustStatus = quickPresenter.getBackAutoRearMirrorAdjust();

            // 发送调节指令
            quickPresenter.sendMirrorAdjuse();
            initObserver();
            initPicker();
            initSwitch();
            initButton();
            quickControllerRegLightListen();

            updateAutoArearMirrorFoldUI(quickPresenter.getAutoRearMirrorFold());
            updateHotMirrorUI(quickPresenter.getAutoHotRearMirror());
            updateBackRearAdjustUI(quickPresenter.getBackAutoRearMirrorAdjust());

            return dialog;
        }

        private void initObserver() {
            viewModel.getAutoRearMirrorFold().observeForever(status -> {
                // 外后视镜自动折叠
                updateAutoArearMirrorFoldUI(status);
            });
            viewModel.getAutoHotRearMirror().observeForever(status -> {
                // 雨天自动加热外后视镜
                updateHotMirrorUI(status);
            });
            viewModel.getBackRearAdjust().observeForever(status -> {
                // 倒车时后视镜自动调节
                updateBackRearAdjustUI(status);
            });
        }

        private void initButton() {
            binding.tvSimplyConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                    int status = onProgress(0);
                    if (status == 0) {

                    } else if (status == 1) {

                    }
                }
            });
            binding.tvSimplyCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                    onProgress(1);
                }
            });
        }

        private int onProgress(int progress) {
            return 0;
        }

        private void initPicker() {
            Log.d(TAG, "initPicker: ");
            binding.spvRearMirrorAdjust.setItems(
                    R.string.str_rear_mirror_ad_1, R.string.str_rear_mirror_ad_2, R.string.str_rear_mirror_ad_3, R.string.str_rear_mirror_ad_4
            );
            binding.spvRearMirrorAdjust.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    setSwitch(index, text);
                }

                @Override
                public void onItemClicked(int index, String text) {

                }
            });
            binding.spvRearMirrorAdjust.setSelectedIndex(dialog.backRearAdjustStatus, false);
//            binding.spvRearMirrorAdjust.setSelectedIndex(3, false);
        }

        private void setSwitch(int index, String text) {
            dialog.backRearAdjustStatus = index;
            viewModel.setBackRearAdjust(index);
            if (quickPresenter.getBackAutoRearMirrorAdjust() != index) {
                quickPresenter.setBackAutoRearMirrorAdjust(index);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_BACK_AUTO_REAR_MIRROR_ADJUSE);
            }
            Log.d(TAG, "倒车时后视镜自动调节: " + text);
        }

        private void initSwitch() {
            Log.d(TAG, "initSwitch: ");
            binding.swConditionRearMirror.setChecked(CommonUtils.IntToBool(quickPresenter.getAutoRearMirrorFold()));
            binding.swConditionRearMirror.setOnCheckedChangeListener((buttonView, b) -> {
                if (b) {
                    onConditionRearMirror(1);
                    Log.d(TAG, "外后视镜自动折叠：：" + buttonView.getId());
                } else {
                    onConditionRearMirror(0);
                    Log.d(TAG, "外后视镜自动折叠：" + buttonView.getId());
                }
            });
            binding.swConditionHeatingRearMirror.setChecked(CommonUtils.IntToBool(quickPresenter.getAutoHotRearMirror()));
            binding.swConditionHeatingRearMirror.setOnCheckedChangeListener((buttonView, b) -> {
                if (b) {
                    onConditionHeatingRearMirror(1);
                    Log.d(TAG, "后镜加热开启事件：" + buttonView.getId());
                } else {
                    onConditionHeatingRearMirror(0);
                    Log.d(TAG, "后镜加热关闭事件：" + buttonView.getId());
                }
            });
        }

        private void onConditionHeatingRearMirror(int status) {
            viewModel.setAutoHotRearMirror(status);
            if (quickPresenter.getAutoHotRearMirror() != status) {
                quickPresenter.setAutoHotRearMirror(status);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTO_HOT_REAR_MIRROR);
            }
            Log.d(TAG, "雨天自动加热外后视镜setListener: " + status);
        }

        private void onConditionRearMirror(int status) {
            viewModel.setAutoRearMirrorFold(status);
            if (quickPresenter.getAutoRearMirrorFold() != status) {
                quickPresenter.setAutoRearMirrorFold(status);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTO_REAR_MIRROR_FOLD);
            }
            Log.d(TAG, "外后视镜自动折叠setListener: " + status);
        }

        public void updateAutoArearMirrorFoldUI(int status) {
            binding.swConditionRearMirror.setChecked(CommonUtils.IntToBool(status));
        }

        public void updateHotMirrorUI(int status) {
            binding.swConditionHeatingRearMirror.setChecked(CommonUtils.IntToBool(status));
        }

        public void updateBackRearAdjustUI(int status) {
            if (dialog != null) {
                binding.spvRearMirrorAdjust.setSelectedIndex(status, true);
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        @Override
        public void handleSafeMessage(Message msg) {
            switch (msg.what) {
                case MessageConst.QUICK_AUTO_REAR_MIRROR_FOLD:
                    autoRearMirrorFoldHandle();
                    break;
                case MessageConst.QUICK_AUTO_HOT_REAR_MIRROR:
                    autoHotRearMirrorHandle();
                    break;
                case MessageConst.QUICK_BACK_AUTO_REAR_MIRROR_ADJUSE:
                    backAutoRearMirrorAdjustHandle();
                    break;
                default:
                    break;
            }
        }

        @Override
        public boolean isActive() {
            return false;
        }

        private void autoRearMirrorFoldHandle() {
            // 外后视镜自动折叠
            int arearMirrorFoldState = quickPresenter.getAutoRearMirrorFold();
            if (arearMirrorFoldState != viewModel.getAutoRearMirrorFold().getValue()) {
                viewModel.setAutoRearMirrorFold(arearMirrorFoldState);
            }
        }

        private void autoHotRearMirrorHandle() {
            // 外后视镜自动加热
            int hotMirrorState = quickPresenter.getAutoHotRearMirror();
            if (hotMirrorState != viewModel.getAutoHotRearMirror().getValue()) {
                viewModel.setAutoHotRearMirror(hotMirrorState);
            }
        }

        private void backAutoRearMirrorAdjustHandle() {
            // 倒车后视镜自动调节
            int backAutoRearMirrorAdjust = quickPresenter.getBackAutoRearMirrorAdjust();
            if (backAutoRearMirrorAdjust != viewModel.getBackRearAdjust().getValue()) {
                dialog.backRearAdjustStatus = backAutoRearMirrorAdjust;
                viewModel.setBackRearAdjust(backAutoRearMirrorAdjust);
            }
        }
    }

    public static boolean isShow = false;

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public interface onProgressChangedListener {
    }
}
