package com.bitech.vehiclesettings.adapter

import android.annotation.SuppressLint
import android.bluetooth.BluetoothProfile
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.TouchDelegate
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.BtDeviceBean
import com.bitech.vehiclesettings.databinding.ItemBtPairedBinding
import com.bitech.vehiclesettings.manager.CarBtManager
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.EToast
import com.bitech.vehiclesettings.utils.LogUtil
import com.chery.adapter.androidauto.connection.devicelist.ConnectState
import com.chery.adapter.androidauto.connection.devicelist.ConnectType
import com.chery.adapter.common.config.IConstant
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.VDEvent
import com.chery.ivi.vdb.event.base.VDKey
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink
import com.chery.ivi.vdb.event.id.phonelink.VDValuePhoneLink
import com.lion.media.api.LionMediaApiSystem.init
import com.lion.media.api.LionMediaApiSystem.playNavigationMedia
import com.lion.media.api.system.bean.LionLaunchBean
import com.lion.media.api.system.bean.MediaReason.REASON_NAVIGATION_NO_PLAY_START
import com.lion.media.api.system.bean.MediaType
import java.util.concurrent.CopyOnWriteArrayList


/**
 * @ClassName: BtPairedListAdapter
 * 
 * @Date:  2024/1/23 9:59
 * @Description: 蓝牙配对列表适配器.
 **/
class BtPairedListAdapter(
    private val context: Context,
    private var pairedList: CopyOnWriteArrayList<BtDeviceBean>
) :
    RecyclerView.Adapter<BtPairedListAdapter.PairedViewHolder>() {
    private var carBtManager = CarBtManager.instance
    // 蓝牙配对列表点击事件监听对象.
    private var btItemClickIconCallback: OnBtItemClickIconCallback? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PairedViewHolder {
        val binding =
            ItemBtPairedBinding.bind(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_bt_paired, parent, false)
            )
        return PairedViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return pairedList.size
    }

    override fun onBindViewHolder(holder: PairedViewHolder, position: Int) {
        LogUtil.d(TAG, "onBindViewHolder position = $position")
        pairedList.getOrNull(position)?.let { btDeviceBean ->
            // 蓝牙名称设置
            holder.setBtName(btDeviceBean.device.name ?: btDeviceBean.wiredName)
            // 支持CarPlay,显示CarPlay图标
            holder.showPhoneLinkIcon(btDeviceBean)
            // 根据有线AA或CP，判断是否显示删除图标
            holder.showDeleteIcon(btDeviceBean)
            // 名称和各类图标高亮与否
            holder.showListIconHighLight(btDeviceBean)
            // 处理蓝牙、CP、AA连接逻辑
            if (btDeviceBean.hfpState == BluetoothProfile.STATE_DISCONNECTED
                && btDeviceBean.a2dpState == BluetoothProfile.STATE_DISCONNECTED
                && (btDeviceBean.cPConnectedState == IConstant.ConnectState.IDLE || btDeviceBean.cPConnectedState == IConstant.ConnectState.DISCONNECTING)
                && (btDeviceBean.aAConnectedState == ConnectState.DISCONNECTED.state || btDeviceBean.aAConnectedState == ConnectState.DISCONNECTING.state)
            ) {
                // 蓝牙、AA、CP均未连接，则更新未连接UI
                holder.showDisconnectedUi(btDeviceBean)
                holder.showPhoneLinkIcon(btDeviceBean)
            } else {
                if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTING
                    || btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTING
                    || btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTING
                    || btDeviceBean.aAConnectedState == ConnectState.CONNECTING.state
                ) {
                    // 蓝牙、AA、CP在连接中，则更新连接中UI
                    holder.showConnectingUi(btDeviceBean)
                } else {
                    if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED
                        || btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED
                        || btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED
                        || btDeviceBean.aAConnectedState == ConnectState.CONNECTED.state
                    ) {
                        // 蓝牙、AA、CP连接成功，则更新连接成功UI
                        holder.showConnectedUi(btDeviceBean)
                        //设置toast提示
//                        EToast.showToast(context, context.getString(R.string.bt_device_success_tips), Toast.LENGTH_SHORT,false)
                    }
                }
            }
            // 列表item点击监听
            holder.itemView.setOnClickListener {
                if (btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED) {
                    if (holder.adapterPosition in 0 until pairedList.size) {
                        // 点击弹框断开carPlay
                        btItemClickIconCallback?.onCarplayAaToBtDevice(pairedList[holder.adapterPosition])
                    }
                } else if (btDeviceBean.aAConnectedState == ConnectState.CONNECTED.state) {
                    if (holder.adapterPosition in 0 until pairedList.size) {
                        // 点击弹框断开android auto
                        btItemClickIconCallback?.onCarplayAaToBtDevice(pairedList[holder.adapterPosition])
                    }
                } else {
                    // CarPlay不再连接中或已连接状态，则可点击进行连接
                    if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED
                        || btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED
                    ) {
                        if (btDeviceBean.cPConnectedState != IConstant.ConnectState.CONNECTING
                            && btDeviceBean.aAConnectedState != ConnectState.CONNECTING.state
                        ) {
                            if (holder.adapterPosition in 0 until pairedList.size) {
                                // 当前设备CP或AA不在连接中，则可点击断开蓝牙连接
                                btItemClickIconCallback
                                    ?.onBtDisconnected(pairedList[holder.adapterPosition])
                            }
                        }
                    } else {
                        if (holder.adapterPosition in 0 until pairedList.size) {
                            // 点击开始蓝牙连接
                            btItemClickIconCallback?.onBtConnect(pairedList[holder.adapterPosition])
                            val connectedDevices = carBtManager.getConnectedDevices()

                            // 断开除新设备外的所有已连接设备
                            connectedDevices.forEach { btDevice ->
                                if (btDevice.device.address != btDeviceBean.device.address) {
                                    carBtManager.disconnectedDevice(btDevice)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置蓝牙配对列表.
     *
     * @param btPairedList 蓝牙配对列表
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setPairedList(btPairedList: CopyOnWriteArrayList<BtDeviceBean>) {
        pairedList = btPairedList
        pairedList.sort()
        notifyDataSetChanged()
    }

    /**
     * 设置配对列表监听.
     *
     * @param itemClickIconCallback OnBtItemClickIconCallback
     */
    fun setOnBtItemClickIconCallback(itemClickIconCallback: OnBtItemClickIconCallback) {
        if (btItemClickIconCallback == null) {
            btItemClickIconCallback = itemClickIconCallback
        }
    }

    /**
     * @ClassName: OnBtItemClickIconCallback
     * 
     * @Date:  2024/1/23 10:38
     * @Description: 列表图标点击事件回调.
     **/
    interface OnBtItemClickIconCallback {

        /**
         * 蓝牙断开连接图标被点击回调.
         *
         * @param beDevice 蓝牙对象
         */
        fun onBtDisconnected(beDevice: BtDeviceBean)

        /**
         * 点击item蓝牙开始连接回调.
         *
         * @param beDevice 蓝牙对象
         */
        fun onBtConnect(beDevice: BtDeviceBean)

        /**
         * 点击删除蓝牙设备图标回调.
         *
         * @param beDevice 蓝牙对象
         */
        fun onBtDeleteDevice(beDevice: BtDeviceBean)

        /**
         * 点击从carPlay或AA切换蓝牙.
         *
         * @param beDevice 蓝牙对象
         */
        fun onCarplayAaToBtDevice(beDevice: BtDeviceBean)

        /**
         * 点击CarPlay图标连接cp或打开cp页面
         *
         * @param beDevice 蓝牙对象
         */
        fun onBtPhoneLink(beDevice: BtDeviceBean,type:Int)

        /**
         * 点击AA图标连接aa或打开aa页面
         *
         * @param beDevice 蓝牙对象
         */
        fun onBtAndroidAuto(beDevice: BtDeviceBean)

        /**
         * 点击未高亮的蓝牙音乐图标，进行蓝牙音乐连接.
         *
         * @param beDevice 蓝牙对象
         */
        fun onBtConnectMusic(beDevice: BtDeviceBean)

        /**
         * 点击未高亮的蓝牙音乐图标，断开蓝牙音乐
         *
         * @param beDevice
         */
        fun onBtDisconnectMusic(beDevice: BtDeviceBean)

        /**
         * 点击高亮的蓝牙电话图标，进行蓝牙电话的连接
         *
         * @param beDevice 蓝牙对象
         */
        fun onBtConnectPhone(beDevice: BtDeviceBean)

        /**
         * 点击未高亮的蓝牙电话图标，断开蓝牙电话的连接
         *
         * @param beDevice
         */
        fun onBtDisconnectPhone(beDevice: BtDeviceBean)
    }

    /**
     * @ClassName: PairedViewHolder
     * 
     * @Date:  2024/1/23 10:22
     * @Description: 蓝牙配对列表ViewHolder.
     **/
    inner class PairedViewHolder(binding: ItemBtPairedBinding) :
        RecyclerView.ViewHolder(binding.root), View.OnClickListener {

        // 实例化对象
        private var btNameTv = binding.btNameTv
        private var btConnectStateTv = binding.btConnectedStateTv
        private var btIconIv = binding.btIconIv
        private var btCarPlayIv = binding.btCarplayIv
        private var btCarLinkIv = binding.btCarlinkIv
        private var btHiCarIv = binding.btHicarIv
        private var btMusicIv = binding.btMusicIv
        private var btPhoneIv = binding.btPhoneIv
        private var btDeleteIv = binding.btDeleteIv
        private var btLoadingPb = binding.btLoadingPb
        private var btClPaired = binding.btClPaired
        private var btPreferences = binding.borderBgPreferences

        init {
            // 初始化item点击事件监听
            btCarPlayIv.setOnClickListener(this)
            btCarLinkIv.setOnClickListener(this)
            btHiCarIv.setOnClickListener(this)
            btMusicIv.setOnClickListener(this)
            btPhoneIv.setOnClickListener(this)
            btDeleteIv.setOnClickListener(this)

            // 扩大图标点击区域
            expendSettingIcon(btMusicIv)
            expendSettingIcon(btPhoneIv)
            expendSettingIcon(btDeleteIv)
        }

        /**
         * 设置蓝牙名称.
         *
         * @param btName
         */
        fun setBtName(btName: String) {
            btNameTv.text = btName
        }

        /**
         * 显示列表icon高亮与否.
         *
         * @param btDeviceBean 蓝牙对象
         */
        @SuppressLint("ResourceAsColor")
        fun showListIconHighLight(btDeviceBean: BtDeviceBean) {
            LogUtil.d(
                TAG, "showListIconHighLight : ${btDeviceBean.device.name}(${btDeviceBean.device.address}) , " +
                        "hfp = ${btDeviceBean.hfpState} , " +
                        "a2dp = ${btDeviceBean.a2dpState} , " +
                        "cPConnectedState = ${btDeviceBean.cPConnectedState} , " +
                        "aAConnectedState = ${btDeviceBean.aAConnectedState}"
            )
            if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTING
                || btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTING
                || btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTING
                || btDeviceBean.aAConnectedState == ConnectState.CONNECTING.state
            ) {
                // 设置连接中提示语,连接中
                btConnectStateTv.visibility = View.VISIBLE
                btConnectStateTv.text = context.getString(R.string.bt_device_connecting)
                btConnectStateTv.setTextAppearance(R.style.settings_text_19sp_regular_AAABAF_style)
                // 名称不高亮
                btNameTv.setTextAppearance(R.style.settings_text_36_regular_17191e_style)
                btClPaired.setBackgroundResource(R.drawable.shape_bg_white)
                btDeleteIv.setImageResource(R.mipmap.icon_list_del_s_48)
                // 蓝牙图标不高亮
                btIconIv.isSelected = false
            } else if (btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED
                || btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED
                || btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED
                || btDeviceBean.aAConnectedState == ConnectState.CONNECTED.state
            ) {
                // 名称高亮
                btClPaired.setBackgroundResource(R.drawable.shape_bg_blue)
                btNameTv.setTextAppearance(R.style.settings_text_36_regular_3292F5_style)
                btDeleteIv.setImageResource(R.mipmap.icon_list_del_n_48)
                // 蓝牙图标高亮
                btIconIv.isSelected = true
                // 设置连接中提示语,已连接
                btConnectStateTv.visibility = View.VISIBLE
                btConnectStateTv.text = context.getString(R.string.bt_device_connected)
                //设置该文本颜色为白色
                btConnectStateTv.setTextAppearance(R.style.settings_text_19sp_regular_3292F5_style)
                //设置文本为白色
                btPreferences.setTextColor(Color.WHITE)
                btPreferences.setBackgroundResource(R.drawable.border_bg_preferences_checked)
            } else {
                // 连接提示语隐藏
                btConnectStateTv.visibility = View.GONE
                // 名称不高亮
                btClPaired.setBackgroundResource(R.drawable.shape_bg_white)
                btNameTv.setTextAppearance(R.style.settings_text_36_regular_17191e_style)
                btDeleteIv.setImageResource(R.mipmap.icon_list_del_s_48)
                // 蓝牙图标不高亮
                btIconIv.isSelected = false
            }
            //btNameTv长度修改
            //TODO 等待互联判断，如果有互联就把名称长度减少
            // 蓝牙电话图标是否高亮
            btPhoneIv.isSelected = btDeviceBean.hfpState == BluetoothProfile.STATE_CONNECTED
            // 蓝牙音乐图标是否高亮
            btMusicIv.isSelected = btDeviceBean.a2dpState == BluetoothProfile.STATE_CONNECTED
            btPreferences.isVisible = btDeviceBean.isPreferencesDevice
            Log.d(TAG, "showListIconHighLight: zhc6whu:btPreferences的显示状态:"+btDeviceBean.isPreferencesDevice)
        }

        /**
         * 显示CarPlay图标.
         *
         * @param btDeviceBean carPlay对应的蓝牙设备.
         */
        fun showPhoneLinkIcon(btDeviceBean: BtDeviceBean) {
            val isCarplay = checkCarplay(btDeviceBean)
            val isCarLink = checkCarLink(btDeviceBean)
            val isHiCar = checkHiCar(btDeviceBean)
            Log.d(TAG, "showPhoneLinkIcon: isCarplay"+isCarplay+" isCarLink "+isCarLink+" isHiCar "+isHiCar)
            if (isCarplay) {
                btCarPlayIv.visibility = View.VISIBLE
                btCarLinkIv.visibility = View.GONE
                btHiCarIv.visibility = View.GONE
            } else if (isCarLink) {
                btCarPlayIv.visibility = View.GONE
                btCarLinkIv.visibility = View.VISIBLE
                btHiCarIv.visibility = View.GONE
            } else if (isHiCar) {
                btCarPlayIv.visibility = View.GONE
                btCarLinkIv.visibility = View.GONE
                btHiCarIv.visibility = View.VISIBLE
            } else {
                btCarPlayIv.visibility = View.GONE
                btCarLinkIv.visibility = View.GONE
                btHiCarIv.visibility = View.GONE
            }
        }

        private fun checkCarplay(btDeviceBean: BtDeviceBean): Boolean {
            val bundle = Bundle()
            bundle.putInt(VDKey.TYPE, VDValuePhoneLink.ServerId.CARPLAY)
            bundle.putString(VDKey.DATA, btDeviceBean.device.address)
            val vdEvent = VDEvent(VDEventPhoneLink.SUPPORT_WIRELESS)
            vdEvent.payload = bundle
            val event = VDBus.getDefault().getOnce(vdEvent) // 失败了会返回null
            if (event != null) {
                val payload = event.payload ?: return false
                val isSupportWireless = payload.getBoolean(VDKey.ENABLE)
                Log.d(TAG, "showPhoneLinkIcon"+btDeviceBean.device.name + " 判断显示支持carplay互联:"+isSupportWireless)
                if (isSupportWireless){
                    return true
                }
            }
            return false
        }

        private fun checkCarLink(btDeviceBean: BtDeviceBean): Boolean {
            val bundle = Bundle()
            bundle.putInt(VDKey.TYPE, VDValuePhoneLink.ServerId.CARLINK)
            bundle.putString(VDKey.DATA, btDeviceBean.device.address)
            val vdEvent = VDEvent(VDEventPhoneLink.SUPPORT_WIRELESS)
            vdEvent.payload = bundle
            val event = VDBus.getDefault().getOnce(vdEvent) // 失败了会返回null
            if (event != null) {
                val payload = event.payload ?: return false
                val isSupportWireless = payload.getBoolean(VDKey.ENABLE)
                Log.d(TAG, "showPhoneLinkIcon "+btDeviceBean.device.name + "判断显示支持carlink互联:"+isSupportWireless)
                if (isSupportWireless){
                    return true
                }
            }
            return false
        }

        private fun checkHiCar(btDeviceBean: BtDeviceBean): Boolean {
            val bundle = Bundle()
            bundle.putInt(VDKey.TYPE, VDValuePhoneLink.ServerId.HICAR)
            bundle.putString(VDKey.DATA, btDeviceBean.device.address)
            val vdEvent = VDEvent(VDEventPhoneLink.SUPPORT_WIRELESS)
            vdEvent.payload = bundle
            val event = VDBus.getDefault().getOnce(vdEvent) // 失败了会返回null
            if (event != null) {
                val payload = event.payload ?: return false
                val isSupportWireless = payload.getBoolean(VDKey.ENABLE)
                Log.d(TAG, "showPhoneLinkIcon"+btDeviceBean.device.name + "判断显示支持hicar互联:"+isSupportWireless)
                if (isSupportWireless){
                    return true
                }
            }
            return false
        }

        /**
         * 显示删除图标.
         *
         * @param btDeviceBean 有线AA或有线CP对应的蓝牙设备
         */
        fun showDeleteIcon(btDeviceBean: BtDeviceBean) {
            LogUtil.i(
                TAG,
                "showDeleteIcon : carPlayLinkType = ${btDeviceBean.carPlayLinkType} , androidAutoLinkType = ${btDeviceBean.androidAutoLinkType} "
            )
            if (btDeviceBean.carPlayLinkType == Contacts.CP_CONNECT_WIRED
                || btDeviceBean.androidAutoLinkType == ConnectType.CONNECT_WIRED.type
            ) {
                if (btDeviceBean.aAConnectedState == ConnectState.CONNECTED.state
                    || btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED
                ) {
                    // 有线AA或者有线CP已连接，则隐藏删除图标
                    btDeleteIv.visibility = View.GONE
                } else {
                    btDeleteIv.visibility = View.VISIBLE
                }
            } else {
                // 其他情况，则显示删除图标
                btDeleteIv.visibility = View.VISIBLE
            }
        }

        /**
         * 蓝牙、CP、AA均未连接UI.
         *
         */
        fun showDisconnectedUi(btDeviceBean: BtDeviceBean) {
            LogUtil.i(TAG, "showDisconnectedUi : btDeviceBean = ${btDeviceBean.device.name}(${btDeviceBean.device.address})")
            // 电话图标显示
            btPhoneIv.visibility = View.GONE
            // 音乐图标显示
            btMusicIv.visibility = View.GONE
            // 连接动画隐藏
            btLoadingPb.visibility = View.GONE
            // 连接动画隐藏
            btLoadingPb.visibility = View.GONE
        }

        /**
         * 蓝牙、CP、AA均处于连接中UI.
         *
         */
        fun showConnectingUi(btDeviceBean: BtDeviceBean) {
            LogUtil.i(TAG, "showConnectingUi : btDeviceBean = ${btDeviceBean.device.name}(${btDeviceBean.device.address})")
            // CP图标隐藏
            btCarPlayIv.visibility = View.GONE
            // 电话图标隐藏
            btPhoneIv.visibility = View.GONE
            // 音乐图标隐藏
            btMusicIv.visibility = View.GONE
            // 删除图标隐藏
            btDeleteIv.visibility = View.GONE
            // 连接动画显示
            btLoadingPb.visibility = View.VISIBLE
        }

        /**
         * 显示连接成功UI.
         *
         * @param btDeviceBean 连接对象对应的蓝牙设备.
         */
        fun showConnectedUi(btDeviceBean: BtDeviceBean) {
            LogUtil.i(TAG, "showConnectedUi : btDeviceBean = ${btDeviceBean.device.name}(${btDeviceBean.device.address})")
            // CP图标显示
            showPhoneLinkIcon(btDeviceBean)
            // 删除图标是否显示
            showDeleteIcon(btDeviceBean)
            // 电话图标显示
            btPhoneIv.visibility = View.VISIBLE
            // 音乐图标显示
            btMusicIv.visibility = View.VISIBLE
            //显示A2dp连接状态
            Log.d(TAG, "showSetBluetoothDialog: zhc6whu:"+btDeviceBean.device.name+"是否连接A2DP为"+btDeviceBean.a2dpState)
            // 有线AA和CP对电话和音乐图标逻辑显示
            if (btDeviceBean.cPConnectedState == IConstant.ConnectState.CONNECTED
                && btDeviceBean.carPlayLinkType == Contacts.CP_CONNECT_WIRED
            ) {
                // 有线CP，蓝牙音乐音乐和电话隐藏
                btPhoneIv.visibility = View.GONE
                btMusicIv.visibility = View.GONE
            }
            if (btDeviceBean.aAConnectedState == ConnectState.CONNECTED.state
                && btDeviceBean.androidAutoLinkType == ConnectType.CONNECT_WIRED.type
            ) {
                // 有线AA，蓝牙电话展示
                btPhoneIv.visibility = View.VISIBLE
                // 有线AA，蓝牙音乐隐藏
                btMusicIv.visibility = View.GONE
            }
            // 连接动画隐藏
            btLoadingPb.visibility = View.GONE
            Handler(Looper.getMainLooper()).postDelayed({
                showPhoneLinkIcon(btDeviceBean)
            }, 2000L)   // 2000 ms = 2 s
        }

        /**
         * 扩大图标点击区域.
         */
        fun expendSettingIcon( btn: ImageView){
            val parent = btn.parent as View
            val rect = Rect()
            btn.getHitRect(rect)
            // 扩大点击区域10dp
            rect.left -= 10
            rect.top -= 10
            rect.right += 10
            rect.bottom += 10
            (parent as ViewGroup).touchDelegate = TouchDelegate(rect, btn)
        }

        /**
         * item中图标点击事件.
         *
         * @param view 视图
         */
        override fun onClick(view: View?) {
            when (view?.id) {
                R.id.bt_carplay_iv -> {
                    LogUtil.d(TAG, "onClick : car play icon is clicked!")
                    if (adapterPosition in 0 until pairedList.size) {
                        btItemClickIconCallback?.onBtPhoneLink(pairedList[adapterPosition],1)
                    }
                }

                R.id.bt_carlink_iv -> {
                    LogUtil.d(TAG, "onClick : carlink icon is clicked!")
                    if (adapterPosition in 0 until pairedList.size) {
                        btItemClickIconCallback?.onBtPhoneLink(pairedList[adapterPosition],2)
                    }
                }

                R.id.bt_hicar_iv -> {
                    LogUtil.d(TAG, "onClick : hicar play icon is clicked!")
                    if (adapterPosition in 0 until pairedList.size) {
                        btItemClickIconCallback?.onBtPhoneLink(pairedList[adapterPosition],3)
                    }
                }

                R.id.bt_music_iv -> {
                    LogUtil.d(TAG, "onClick : bt music icon is clicked!")
                    if (adapterPosition in 0 until pairedList.size) {
                        if (btMusicIv.isSelected) {
                            // 蓝牙音乐高亮，点击跳转到蓝牙音乐
                            init(context)
                            playNavigationMedia(LionLaunchBean().apply {
                                mediaType = MediaType.MUSIC_BT
                                reason = REASON_NAVIGATION_NO_PLAY_START
                                uiType = 1    // 全屏
                                isShowUi = true
                            })
                            //点击关闭a2dp链接
//                            btItemClickIconCallback?.onBtDisconnectMusic(pairedList[adapterPosition])
                        } else {
                            // 蓝牙音乐不高亮，点击连接蓝牙音乐
                            EToast.showToast(context, "蓝⽛音乐未连接，请连接设备或检查手机设置", Toast.LENGTH_SHORT, false)
                            //点击打开a2dp链接
//                            btItemClickIconCallback?.onBtConnectMusic(pairedList[adapterPosition])
                        }
                    }
                }

                R.id.bt_phone_iv -> {
                    LogUtil.d(TAG, "onClick : bt phone icon is clicked!")
                    if (adapterPosition in 0 until pairedList.size) {
                        if (btPhoneIv.isSelected) {
                            // 蓝牙电话高亮，点击断开蓝牙电话
                            //点击打开蓝牙电话应用
                            if (MyApplication.getContext().packageManager.getLaunchIntentForPackage("com.bitech.dialer") != null) {
                                val packageName = "com.bitech.dialer"
                                val launchIntent: Intent? =
                                    MyApplication.getContext().packageManager.getLaunchIntentForPackage(
                                        packageName
                                    )?.apply {
                                        flags = Intent.FLAG_ACTIVITY_NEW_TASK // 强制覆盖 Flags
                                    }
                                MyApplication.getContext().startActivity(launchIntent)
                            } else {
                                EToast.showToast(context, "未安装蓝牙电话", Toast.LENGTH_SHORT, false)
                            }
//                            btItemClickIconCallback?.onBtDisconnectPhone(pairedList[adapterPosition])
                        } else {
                            // 蓝牙电话不高亮，点击连接蓝牙电话
                            EToast.showToast(context, "蓝⽛电话未连接，请连接设备或检查手机设置", Toast.LENGTH_SHORT, false)
//                            btItemClickIconCallback?.onBtConnectPhone(pairedList[adapterPosition])
                        }
                    }
                }

                R.id.bt_delete_iv -> {
                    LogUtil.d(TAG, "onClick : bt delete icon is clicked!")
                    if (adapterPosition in 0 until pairedList.size) {
                        btItemClickIconCallback?.onBtDeleteDevice(pairedList[adapterPosition])
                    }
                }
            }
        }
    }

    companion object {
        // 日志标志位
        private const val TAG = "BtPairedListAdapter"
    }
}
