package com.bitech.vehiclesettings.fragment;

import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.carapi.constants.CarCondition;
import com.bitech.vehiclesettings.databinding.FragmentConditionBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.view.condition.MaintenanceConfirmUIAlert;
import com.bitech.vehiclesettings.view.condition.NextMaintenanceUIAlert;
import com.bitech.vehiclesettings.view.condition.RepairCheckUIAlert;
import com.bitech.vehiclesettings.view.condition.RoadRescueCallUIAlert;
import com.bitech.vehiclesettings.view.condition.SunShadeCurtainModeUIAlert;
import com.bitech.vehiclesettings.view.newenergy.EnergyConsumptionListDialog;
import com.bitech.vehiclesettings.viewmodel.ConditionViewModel;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;

import java.lang.ref.WeakReference;

public class ConditionFragment extends BaseFragment<FragmentConditionBinding> implements View.OnClickListener, SafeHandlerCallback {
    private static final String TAG = ConditionFragment.class.getSimpleName();

    private volatile boolean isActive;
    private SafeHandler conditionHandler;

    private final Handler handler = new Handler(Looper.getMainLooper());
    private EnergyConsumptionListDialog energyConsumptionListDialog;
    private RoadRescueCallUIAlert.Builder roadRescueCallUIAlert;
    private RepairCheckUIAlert.Builder repairCheckUIAlert;
    private SunShadeCurtainModeUIAlert.Builder sunShadeCurtainModeUIAlert;

    private ConditionViewModel viewModel;
    private MainActViewModel mainActViewModel;
    // dialogs / builders (kept as nullable, cleared on lifecycle end)
    private NextMaintenanceUIAlert.Builder nextMaintenanceUIAlert;
    private Runnable timeoutRunnable;
    private boolean isRepairInProgress = false;

    public static final int ENERGY_CONSUMPTION_LIST_DIALOG = 10;
    public static final int DIALOG_ALERT_CONDITION_SUN_SHADE_CURTAIN_MODE = 11;
    public static final int DIALOG_ALERT_CONDITION_NEXT_MAINTENANCE = 12;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate");
        isActive = true;
        conditionHandler = new SafeHandler(this);
    }

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null) return;
        loadPageAnim(binding.scrollView, currentPosition, position);
    }

    @Override
    protected FragmentConditionBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentConditionBinding.inflate(inflater, container, false);
        return binding;
    }

    @Override
    protected void initObserve() {
        initObserver();
    }

    @Override
    protected void initData() {
        viewModel.initData();
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void setListener() {
        scrollListener();

        binding.rlMaintainReset.setOnClickListener(v -> {
            if (nextMaintenanceUIAlert == null) {
                nextMaintenanceUIAlert = new NextMaintenanceUIAlert.Builder(requireContext());
            }
            nextMaintenanceUIAlert.create().show();
        });

        binding.rlRepairCheck.setOnClickListener(v -> {
            if (repairCheckUIAlert == null) {
                repairCheckUIAlert = new RepairCheckUIAlert.Builder(requireContext());
            }
            repairCheckUIAlert.create().show();
        });

        binding.rlEnergyConsumptionList.setOnClickListener(v -> showEnergyConsumptionListDialog());

        binding.rlRoadRescue.setOnClickListener(v -> {
            if (roadRescueCallUIAlert == null) {
                roadRescueCallUIAlert = new RoadRescueCallUIAlert.Builder(requireContext());
            }
            roadRescueCallUIAlert.create().show();
        });

        // RepairCheck listeners
        RepairCheckUIAlert.setOnProgressChangedListener(new RepairCheckUIAlert.onProgressChangedListener() {
            @Override
            public void setWiperRepair(boolean isChecked) {
                viewModel.setWiperRepairMode(isChecked);
                conditionHandler.sendMessageDelayed(MessageConst.CAR_WIPER_REPAIR_MOD);
            }

            @Override
            public int getWiperRepairMode() {
                return viewModel.wiperRepairModeLiveData.getValue();
            }

            @Override
            public int getWiperRepairState() {
                return viewModel.wiperRepairDisableLiveData.getValue();
            }

            @Override
            public void setSunShadeCurtainMode() {
                sunShadeCurtainModeUIAlert = new SunShadeCurtainModeUIAlert.Builder(requireContext());
                sunShadeCurtainModeUIAlert.create().show();
            }

            @Override
            public int getSunShadeCurtainMode() {
                return viewModel.sunShadeRepairModeLiveData.getValue();
            }
        });

        // 遮阳帘维修检测弹窗确认
        SunShadeCurtainModeUIAlert.setOnProgressChangedListener(new SunShadeCurtainModeUIAlert.onProgressChangedListener() {
            @Override
            public void onStartRepair() {
                int currentSunShadeMode = viewModel.SunShadeCurtainStatusLiveData.getValue();
                if (currentSunShadeMode == CarCondition.SRFR_Movement.STOPPED
                        || currentSunShadeMode == CarCondition.SRFR_Movement.FULL_CLOSE
                        || currentSunShadeMode == CarCondition.SRFR_Movement.FULL_OPEN) {
                    viewModel.startSunShadeRepairMode();
                    viewModel.setSunShadeRepairMode(1);
                    isRepairInProgress = true;

                    // 启动90秒计时器
                    timeoutRunnable = new Runnable() {
                        @Override
                        public void run() {
                            viewModel.setSunShadeRepairMode(0);
                            EToast.showToast(mContext, getString(R.string.str_sun_shade_curtain_repair_fail), 0, false);
                            isRepairInProgress = false;
                        }
                    };
                    handler.postDelayed(timeoutRunnable, 90 * 1000); // 90秒
                }
            }

            @Override
            public int getRepair() {
                return viewModel.sunShadeRepairModeLiveData.getValue();
            }
        });

        // 保养提醒弹窗
        NextMaintenanceUIAlert.setOnProgressChangedListener(new NextMaintenanceUIAlert.onProgressChangedListener() {
            @Override
            public void setMaintenanceRemind(boolean isChecked) {
                viewModel.setMaintainRemind(isChecked);
                conditionHandler.sendMessageDelayed(MessageConst.CONDITION_MAINTAIN_TIP);
            }

            @Override
            public int getMaintenanceRemind() {
                // 保养提醒
                return viewModel.maintainTipsLiveData.getValue();
            }

            @Override
            public int getPowerMode() {
                return viewModel.powerMode.getValue();
            }

            @Override
            public int getMaintainKm() {
                return viewModel.maintainKmLiveData.getValue();
            }

            @Override
            public int getMaintainTime() {
                return viewModel.maintainDaysLiveData.getValue();
            }
        });

        // 保养复位弹窗
        MaintenanceConfirmUIAlert.setOnProgressChangedListener(new MaintenanceConfirmUIAlert.onProgressChangedListener() {
            @Override
            public void onConfirm() {
                Log.d(TAG, "保养里程复位");
                viewModel.setMaintainReset();
            }
        });

        // 道路救援弹窗
        RoadRescueCallUIAlert.setOnProgressChangedListener(new RoadRescueCallUIAlert.onProgressChangedListener() {
            @Override
            public void intentToRoadRescue() {
                // 跳转至道路救援Apps
                Intent intent = new Intent();
                intent.setComponent(new ComponentName("com.bitech.roadassistance", "com.bitech.roadassistance.activity.MainActivity"));
                try {
                    intent.putExtra("is_external_call", true);
                    startActivity(intent);
                } catch (ActivityNotFoundException e) {
                    // 处理目标App未安装的情况
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void onClick(View v) {
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (binding != null) {
            binding = null;
        }
    }

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.CAR_WIPER_REPAIR_MOD:
                viewModel.getWiperRepairMode();
                break;
            case MessageConst.CONDITION_MAINTAIN_TIP:
                viewModel.getMaintainRemind();
                break;
        }
    }

    private void initObserver() {
        Log.d(TAG, "initObserver");
        mainActViewModel = new ViewModelProvider(requireActivity()).get(MainActViewModel.class);
        // 先注册 observer，再处理当前值，避免一次性事件丢失
        mainActViewModel.getTargetDialogLiveEvent().observe(getViewLifecycleOwner(), this::processTargetDialogEvent);
        processTargetDialogEvent(mainActViewModel.getTargetDialogLiveEvent().getValue());

        viewModel = new ViewModelProvider(this).get(ConditionViewModel.class);

        // 以下绑定 LiveData -> UI 更新
        viewModel.wiperRepairModeLiveData.observe(getViewLifecycleOwner(), this::updateWiperRepairModeUI);
        viewModel.wiperRepairDisableLiveData.observe(getViewLifecycleOwner(), this::updateWiperRepairDisableUI);
        viewModel.maintainKmLiveData.observe(getViewLifecycleOwner(), this::updateMaintainKmUI);
        viewModel.maintainDaysLiveData.observe(getViewLifecycleOwner(), this::updateMaintainDaysUI);

        viewModel.leftFrontTyrePressureLiveData.observe(getViewLifecycleOwner(), val -> updateLeftFrontTyrePressureUI(val == null ? Integer.MIN_VALUE : val));
        viewModel.rightFrontTyrePressureLiveData.observe(getViewLifecycleOwner(), val -> updateRightFrontTyrePressureUI(val == null ? Integer.MIN_VALUE : val));
        viewModel.leftRearTyrePressureLiveData.observe(getViewLifecycleOwner(), val -> updateLeftRearTyrePressureUI(val == null ? Integer.MIN_VALUE : val));
        viewModel.rightRearTyrePressureLiveData.observe(getViewLifecycleOwner(), val -> updateRightRearTyrePressureUI(val == null ? Integer.MIN_VALUE : val));

        viewModel.leftFrontTyreTemperatureLiveData.observe(getViewLifecycleOwner(), val -> updateLeftFrontTyreTemperatureUI(val == null ? Integer.MIN_VALUE : val));
        viewModel.rightFrontTyreTemperatureLiveData.observe(getViewLifecycleOwner(), val -> updateRightFrontTyreTemperatureUI(val == null ? Integer.MIN_VALUE : val));
        viewModel.leftRearTyreTemperatureLiveData.observe(getViewLifecycleOwner(), val -> updateLeftRearTyreTemperatureUI(val == null ? Integer.MIN_VALUE : val));
        viewModel.rightRearTyreTemperatureLiveData.observe(getViewLifecycleOwner(), val -> updateRightRearTyreTemperatureUI(val == null ? Integer.MIN_VALUE : val));

        viewModel.maintainTipsLiveData.observe(getViewLifecycleOwner(), this::updateMaintainTipsUI);
        viewModel.powerMode.observe(getViewLifecycleOwner(), this::updatePowerMode);
        viewModel.sunShadeRepairModeLiveData.observe(getViewLifecycleOwner(), this::updateSunShadeRepairModeUI);
        viewModel.sunShadeTeachRunStatusLiveData.observe(getViewLifecycleOwner(), this::updateSunShadeTeachRunStatusUI);

        viewModel.leftFrontTyrePressureStatusLiveData.observe(getViewLifecycleOwner(), val -> updateTyreStatusForPosition(val, TyrePosition.LHFTire));
        viewModel.rightFrontTyrePressureStatusLiveData.observe(getViewLifecycleOwner(), val -> updateTyreStatusForPosition(val, TyrePosition.RHFTire));
        viewModel.leftRearTyrePressureStatusLiveData.observe(getViewLifecycleOwner(), val -> updateTyreStatusForPosition(val, TyrePosition.LHRTire));
        viewModel.rightRearTyrePressureStatusLiveData.observe(getViewLifecycleOwner(), val -> updateTyreStatusForPosition(val, TyrePosition.RHRTire));

        viewModel.tyrePressureUnitLiveData.observe(getViewLifecycleOwner(), this::updateTyrePressureUnitUI);
    }

    // 统一处理轮胎状态 -> 调用具体 UI 更新
    private void updateTyreStatusForPosition(Integer status, TyrePosition pos) {
        switch (pos) {
            case LHFTire:
                updateTyreStatusUI(status,
                        binding.tvLeftFrontTyrePressure, binding.tvLeftFrontTyreTemperature, binding.ivImageLeftFront,
                        CarCondition.TirePositionWarning_LHFTire.ERROR_MODE,
                        viewModel.leftFrontTyrePressureLiveData.getValue(), viewModel.leftFrontTyrePressureStatusLiveData.getValue(), viewModel.leftFrontTyreTemperatureLiveData.getValue());
                break;
            case RHFTire:
                updateTyreStatusUI(status,
                        binding.tvRightFrontTyrePressure, binding.tvRightFrontTyreTemperature, binding.ivImageRightFront,
                        CarCondition.TirePositionWarning_RHFTire.ERROR_MODE,
                        viewModel.rightFrontTyrePressureLiveData.getValue(), viewModel.rightFrontTyrePressureStatusLiveData.getValue(), viewModel.rightFrontTyreTemperatureLiveData.getValue());
                break;
            case LHRTire:
                updateTyreStatusUI(status,
                        binding.tvLeftRearTyrePressure, binding.tvLeftRearTyreTemperature, binding.ivImageLeftRear,
                        CarCondition.TirePositionWarning_LHRTire.ERROR_MODE,
                        viewModel.leftRearTyrePressureLiveData.getValue(), viewModel.leftRearTyrePressureStatusLiveData.getValue(), viewModel.leftRearTyreTemperatureLiveData.getValue());
                break;
            case RHRTire:
                updateTyreStatusUI(status,
                        binding.tvRightRearTyrePressure, binding.tvRightRearTyreTemperature, binding.ivImageRightRear,
                        CarCondition.TirePositionWarning_RHRTire.ERROR_MODE,
                        viewModel.rightRearTyrePressureLiveData.getValue(), viewModel.rightRearTyrePressureStatusLiveData.getValue(), viewModel.rightRearTyreTemperatureLiveData.getValue());
                break;
        }
    }

    // 抽取出来的通用轮胎状态更新逻辑
    private void updateTyreStatusUI(Integer status, TextView tvPressure, TextView tvTemp, ImageView ivWarning,
                                    int errorModeConst, Integer pressureValueLive, Integer statusLive, Integer tempValueLive) {
        if (status == null) return;

        if (status == CarCondition.TirePositionWarning_LHFTire.NORMAL || status == CarCondition.TirePositionWarning_LHFTire.NOT_USED) {
            ivWarning.setVisibility(View.GONE);
            tvTemp.setTextColor(ContextCompat.getColor(mContext, R.color.black));
            tvPressure.setTextColor(ContextCompat.getColor(mContext, R.color.black));
        } else {
            ivWarning.setVisibility(View.VISIBLE);
            // 判断属于哪种告警
            boolean pressureAlert = (status == CarCondition.TirePositionWarning_LHFTire.RAPID_AIR_LOSS
                    || status == CarCondition.TirePositionWarning_LHFTire.LOW_PRESSURE
                    || status == CarCondition.TirePositionWarning_LHFTire.LOW_BATTER
                    || status == CarCondition.TirePositionWarning_LHFTire.HIGH_PRESSURE);
            boolean tempAlert = (status == CarCondition.TirePositionWarning_LHFTire.HIGH_TEMPERATURE);
            boolean errorMode = (status == errorModeConst);

            if (pressureAlert) {
                tvPressure.setTextColor(ContextCompat.getColor(mContext, R.color.color_condition_warning));
                tvTemp.setTextColor(ContextCompat.getColor(mContext, R.color.black));
            } else if (tempAlert) {
                tvPressure.setTextColor(ContextCompat.getColor(mContext, R.color.black));
                tvTemp.setTextColor(ContextCompat.getColor(mContext, R.color.color_condition_warning));
            } else if (errorMode) {
                tvPressure.setTextColor(ContextCompat.getColor(mContext, R.color.color_condition_warning));
                tvTemp.setTextColor(ContextCompat.getColor(mContext, R.color.color_condition_warning));
                tvPressure.setText(convertPressure(Integer.MIN_VALUE));
                tvTemp.setText(convertTemperature(Integer.MIN_VALUE));
            }
        }

        // 刷新压力 & 温度（errorMode 时显示占位值）
        if (status != errorModeConst) {
            int pressureVal = pressureValueLive == null ? Integer.MIN_VALUE : pressureValueLive;
            int tempVal = tempValueLive == null ? Integer.MIN_VALUE : tempValueLive;
            tvPressure.setText(convertPressure(pressureVal));
            tvTemp.setText(convertTemperature(tempVal));
        } else {
            tvPressure.setText(convertPressure(Integer.MIN_VALUE));
            tvTemp.setText(convertTemperature(Integer.MIN_VALUE));
        }
    }

    // 电源模式发生更改
    private void updatePowerMode(Integer status) {
        // FLZCU_9_PowerMode
        //0x0:Off
        //0x1:Comfortable
        //0x2:ON
        //0x3:Reserved"
        if (status == 0x0) {
            if (nextMaintenanceUIAlert != null) { // 保养复位
                nextMaintenanceUIAlert.updatePowerMode(0);
            }
        } else if (status == 0x1) {
            if (nextMaintenanceUIAlert != null) { // 保养复位
                nextMaintenanceUIAlert.updatePowerMode(1);
            }
        } else if (status == 0x2) {
            if (nextMaintenanceUIAlert != null) { // 保养复位
                nextMaintenanceUIAlert.updatePowerMode(2);
            }
        } else if (status == 0x3) {
            if (nextMaintenanceUIAlert != null) { // 保养复位
                nextMaintenanceUIAlert.updatePowerMode(3);
            }
        }
    }

    private void processTargetDialogEvent(TargetDialogInfo targetDialog) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= " + targetDialog);
        if (targetDialog == null) {
            return;
        }
        //具体Tab索引
        if (targetDialog.getTargetTab() == MainActivity.MainTabIndex.CONDITION) {
            switch (targetDialog.getTargetDialog()) {
                //常量-具体窗口
                case ConditionFragment.ENERGY_CONSUMPTION_LIST_DIALOG:
                    //常量-操作
                    if (targetDialog.getOperation() == 1) {
                        showEnergyConsumptionListDialog();
                    } else {
                        hideEnergyConsumptionListDialog();
                    }
                    break;
                case ConditionFragment.DIALOG_ALERT_CONDITION_SUN_SHADE_CURTAIN_MODE:
                    if (targetDialog.getOperation() == CommonConst.DIALOG_OPEN) {
                        if (sunShadeCurtainModeUIAlert == null) {
                            sunShadeCurtainModeUIAlert = new SunShadeCurtainModeUIAlert.Builder(mContext);
                        }
                        sunShadeCurtainModeUIAlert.create().show();
                    }
                    break;
                case ConditionFragment.DIALOG_ALERT_CONDITION_NEXT_MAINTENANCE:
                    if (targetDialog.getOperation() == CommonConst.DIALOG_OPEN) {
                        if (nextMaintenanceUIAlert == null) {
                            nextMaintenanceUIAlert = new NextMaintenanceUIAlert.Builder(mContext);
                        }
                        nextMaintenanceUIAlert.create().show();
                    }
                    break;
                default:
                    break;
            }
        }
        mainActViewModel.getTargetDialogLiveEvent().setValue(null);
    }

    private void updateSunShadeTeachRunStatusUI(Integer signalVal) {
        if (signalVal == CarCondition.SRFR_TeachRun.TEACH_RUN_SUCCESSFUL) {
            if (isRepairInProgress) {
                if (handler != null && timeoutRunnable != null) {
                    handler.removeCallbacks(timeoutRunnable);
                }
                EToast.showToast(requireContext(), getString(R.string.str_sun_shade_curtain_repair_success), 2000, false);
                isRepairInProgress = false;
                viewModel.sunShadeRepairModeLiveData.postValue(0);
            }
        }
    }

    private void updateLeftRearTyreTemperatureUI(int temperature) {
        if (viewModel.leftRearTyrePressureStatusLiveData.getValue()
                != CarCondition.TirePositionWarning_LHRTire.ERROR_MODE) {
            binding.tvLeftRearTyreTemperature.setText(convertTemperature(temperature));
        }
    }

    private void updateWiperRepairDisableUI(Integer status) {
        if (repairCheckUIAlert != null) {
            repairCheckUIAlert.updateWiperRepairDisable(status);
        }
    }

    private void updateMaintainTipsUI(Integer status) {
        if (nextMaintenanceUIAlert != null) {
            nextMaintenanceUIAlert.updateMaintainTips(status);
        }
    }

    private void updateLeftFrontTyrePressureUI(int pressure) {
        if (viewModel.leftFrontTyrePressureStatusLiveData.getValue()
                != CarCondition.TirePositionWarning_LHFTire.ERROR_MODE) {
            binding.tvLeftFrontTyrePressure.setText(convertPressure(pressure));
        }
    }

    private void updateRightFrontTyrePressureUI(int pressure) {
        if (viewModel.rightFrontTyrePressureStatusLiveData.getValue()
                != CarCondition.TirePositionWarning_RHFTire.ERROR_MODE) {
            binding.tvRightFrontTyrePressure.setText(convertPressure(pressure));
        }
    }

    private void updateLeftRearTyrePressureUI(int pressure) {
        if (viewModel.leftRearTyrePressureStatusLiveData.getValue()
                != CarCondition.TirePositionWarning_LHRTire.ERROR_MODE) {
            binding.tvLeftRearTyrePressure.setText(convertPressure(pressure));
        }
    }

    private void updateRightRearTyrePressureUI(int pressure) {
        if (viewModel.rightRearTyrePressureStatusLiveData.getValue()
                != CarCondition.TirePositionWarning_RHRTire.ERROR_MODE) {
            binding.tvRightRearTyrePressure.setText(convertPressure(pressure));
        }
    }

    private void updateLeftFrontTyreTemperatureUI(int temperature) {
        if (viewModel.leftFrontTyrePressureStatusLiveData.getValue()
                != CarCondition.TirePositionWarning_LHFTire.ERROR_MODE) {
            binding.tvLeftFrontTyreTemperature.setText(convertTemperature(temperature));
        }
    }

    private void updateRightFrontTyreTemperatureUI(int temperature) {
        if (viewModel.rightFrontTyrePressureStatusLiveData.getValue()
                != CarCondition.TirePositionWarning_RHFTire.ERROR_MODE) {
            binding.tvRightFrontTyreTemperature.setText(convertTemperature(temperature));
        }
    }

    private void updateRightRearTyreTemperatureUI(int temperature) {
        if (viewModel.rightRearTyrePressureStatusLiveData.getValue()
                != CarCondition.TirePositionWarning_RHRTire.ERROR_MODE) {
            binding.tvRightRearTyreTemperature.setText(convertTemperature(temperature));
        }
    }

    private void updateSunShadeRepairModeUI(Integer status) {
        if (repairCheckUIAlert != null) repairCheckUIAlert.updateSunShadeRepairModeUI(status);
        if (sunShadeCurtainModeUIAlert != null) sunShadeCurtainModeUIAlert.updateConfirmStatus(status);
    }

    private void updateMaintainDaysUI(Integer status) {
        String maintainTime = "---";
        if (status != null && status >= 0) {
            maintainTime = status + getString(R.string.str_condition_next_maintenance_content_day);
        } else {
            maintainTime = "---" + getString(R.string.str_condition_next_maintenance_content_day);
        }
        binding.tvMaintainTime.setText(maintainTime);
        if (nextMaintenanceUIAlert != null) nextMaintenanceUIAlert.updateMaintainTime(maintainTime);
    }

    private void updateMaintainKmUI(Integer status) {
        String maintainKm = "---";
        if (status != null && status >= 0) {
            maintainKm = status + getString(R.string.str_unit_setting_km);
        } else {
            maintainKm = "---" + "km";
        }
        binding.tvMaintainMileage.setText(getString(R.string.str_condition_next_maintenance_until) + maintainKm);
        if (nextMaintenanceUIAlert != null) nextMaintenanceUIAlert.updateMaintainKm(getString(R.string.str_condition_next_maintenance_until) + maintainKm);
    }

    // 转换胎压
    public String convertPressure(int pressure) {
        Integer pressureUnit = viewModel.tyrePressureUnitLiveData.getValue();
        if (pressure == Integer.MIN_VALUE) {
            switch (pressureUnit == null ? 0 : pressureUnit) {
                case 0: return "--- kPa";
                case 1: return "-- psi";
                case 2: return "-.- bar";
                default: return "--- kPa";
            }
        }
        float pressureValue;
        String result;
        int unit = pressureUnit == null ? 0 : pressureUnit;
        switch (unit) {
            case 0: // kPa
                pressureValue = Math.round(pressure * 1.373f);
                result = String.format("%.0f kPa", pressureValue);
                break;
            case 1: // psi
                // 先转换成 kPa，再转成 psi
                pressureValue = pressure * 1.373f * 0.1450337f;
                pressureValue = Math.round(pressureValue); // 四舍五入取整
                result = String.format("%.0f psi", pressureValue);
                break;
            case 2: // bar
                pressureValue = pressure * 1.373f / 100f;
                pressureValue = Math.round(pressureValue * 10f) / 10.0f; // keep one decimal
                result = String.format("%.1f bar", pressureValue);
                break;
            default:
                pressureValue = Math.round(pressure * 1.373f);
                result = String.format("%.0f kPa", pressureValue);
                break;
        }
        return result;
    }

    // 转换温度
    private String convertTemperature(int temperature) {
        if (temperature == Integer.MIN_VALUE) return "--℃";
        double result = temperature * 0.65f - 40;
        return String.format("%d℃", (int) result);
    }

    private void updateWiperRepairModeUI(Integer signalVal) {
        if (repairCheckUIAlert != null) repairCheckUIAlert.updateWiperRepairMode(signalVal);
    }

    private void showEnergyConsumptionListDialog() {
        if (energyConsumptionListDialog == null) energyConsumptionListDialog = new EnergyConsumptionListDialog(requireContext());
        if (energyConsumptionListDialog.isShowing()) return;
        energyConsumptionListDialog.show();
    }

    private void updateTyrePressureUnitUI(Integer signalVal) {
        // 触发当前四个胎压值重绘
        viewModel.leftFrontTyrePressureLiveData.postValue(viewModel.leftFrontTyrePressureLiveData.getValue());
        viewModel.rightFrontTyrePressureLiveData.postValue(viewModel.rightFrontTyrePressureLiveData.getValue());
        viewModel.leftRearTyrePressureLiveData.postValue(viewModel.leftRearTyrePressureLiveData.getValue());
        viewModel.rightRearTyrePressureLiveData.postValue(viewModel.rightRearTyrePressureLiveData.getValue());
    }

    private void hideEnergyConsumptionListDialog() {
        if (energyConsumptionListDialog != null && energyConsumptionListDialog.isShowing()) {
            energyConsumptionListDialog.dismiss();
        }
    }

    private void hideDialogAlertConditionNextMaintenance() {
        if (nextMaintenanceUIAlert != null && nextMaintenanceUIAlert.isShowing()) {
            nextMaintenanceUIAlert.dismiss();
        }
    }

    private void scrollListener() {
        binding.scrollView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            MainActivity activity = (MainActivity) getActivity();
            if (activity != null && (activity.getBinding().ivModel != null)) {
                activity.getBinding().ivModel.handleScroll(scrollY);
            }
        });
    }

    // 清理 builder & dialog 引用，防止内存泄漏
    private void clearDialogBuilders() {
        try {
            if (energyConsumptionListDialog != null) {
                if (energyConsumptionListDialog.isShowing()) energyConsumptionListDialog.dismiss();
                energyConsumptionListDialog = null;
            }
            if (nextMaintenanceUIAlert != null) {
                if (nextMaintenanceUIAlert.isShowing()) nextMaintenanceUIAlert.dismiss();
                nextMaintenanceUIAlert = null;
            }
        } catch (Exception e) {
            Log.w(TAG, "clearDialogBuilders exception", e);
        }
    }

    @Override
    public void onDestroyView() {
        if (handler != null && timeoutRunnable != null) handler.removeCallbacks(timeoutRunnable);
        if (handler != null) handler.removeCallbacksAndMessages(null);
        clearDialogBuilders();
        binding = null;
        super.onDestroyView();
    }

    private enum TyrePosition {LHFTire, RHFTire, LHRTire, RHRTire}

    @Override
    public void onStop() {
        hideEnergyConsumptionListDialog();
        hideDialogAlertConditionNextMaintenance();
        super.onStop();
    }

    @Override
    public boolean isActive() {
        return isActive;
    }
}
