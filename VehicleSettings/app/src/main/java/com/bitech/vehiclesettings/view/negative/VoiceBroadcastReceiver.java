package com.bitech.vehiclesettings.view.negative;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class VoiceBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = "VoiceBroadcastReceiverwzh2whu";

    public static final String VOICE_CHANGE_URI = "com.chery.systemui.dock.volume_dialog_show";
    public static final String AIR_ALERT_URI = "com.mega.hvac.action_hvac_state";// 座椅弹出

    VoiceUIAlert voiceUIAlert;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (VOICE_CHANGE_URI.equals(intent.getAction())) {
            Log.d(TAG, "onReceive: " + "弹出声音弹窗");
            if (voiceUIAlert == null) {
                voiceUIAlert = new VoiceUIAlert(context);

            }
            if (voiceUIAlert.isShowing()) {
                return;
            }
            voiceUIAlert.show();
        }
        if (AIR_ALERT_URI.equals(intent.getAction())) {
            Log.d(TAG, "onReceive: " + "座椅弹出");
            if(voiceUIAlert == null){
                return;
            }
            if (voiceUIAlert.isShowing()) {
                Log.d(TAG, "onReceive: " + "关闭声音弹窗");
                voiceUIAlert.dismiss();
            }
        }
    }
}
