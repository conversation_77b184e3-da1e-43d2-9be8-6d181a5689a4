package com.bitech.vehiclesettings.view.quickcontrol;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.bitech.vehicle3D.VehicleServiceManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.carapi.constants.Car3DModel;
import com.bitech.vehiclesettings.databinding.DialogAlertQ3dModelBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertQRearMirrorBinding;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.ThreeDModelUtil;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.wallpaper3D.aidl.IPropertyCallback;

/**
 * FileName: NoTitleUIAlert
 * Author: WUY1WHU
 * Date: 2024/6/19 10:29
 * Description:通用对话框
 */
public class ThreeDModelUIAlert extends BaseDialog {
    private static final String TAG = ThreeDModelUIAlert.class.getSimpleName();

    private static onProgressChangedListener onProgressChangedListener;

    public ThreeDModelUIAlert(Context context) {
        super(context);
    }

    public ThreeDModelUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected ThreeDModelUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static ThreeDModelUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(ThreeDModelUIAlert.onProgressChangedListener onProgressChangedListener) {
        ThreeDModelUIAlert.onProgressChangedListener = onProgressChangedListener;
    }


    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private String promptText;
        private ThreeDModelUIAlert dialog = null;
        protected DialogAlertQ3dModelBinding binding;
        private boolean globalAlert = false;


        public void setGlobalAlert(boolean flag) {
            this.globalAlert = flag;
        }

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public Builder setContent(String text) {
            this.promptText = text;
            return this;
        }


        public Builder setConfirmAndCancel(boolean flag) {

            return this;
        }

        /**
         * Create the custom dialog
         */
        public ThreeDModelUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new ThreeDModelUIAlert(context,
                        R.style.Dialog);
            }
            binding = DialogAlertQ3dModelBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1992;
            layoutParams.height = 922;
            layoutParams.type = globalAlert ? WindowManager.LayoutParams.TYPE_SYSTEM_ALERT :
                    WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            window.setAttributes(layoutParams);

            initSwitch();

            return dialog;
        }

        private void initSwitch() {
            Log.d(TAG, "initSwitch: ");
            binding.swCarDoorFl.setChecked(Prefs.get(PrefsConst.Q_CAR_DOOR_FL, PrefsConst.DefaultValue.Q_CAR_DOOR_FL));
            binding.swCarDoorFl.setOnCheckedChangeListener((buttonView, b) -> {
                ThreeDModelUtil.setFrontLeftDoor(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "左前门状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_DOOR_FL, b);
                    } else {
                        Log.e(TAG, "左前门状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarDoorFl.setOnCheckedChangeListener(null);
                        binding.swCarDoorFl.setChecked(!b);
                    }
                });
            });

            binding.swCarDoorFr.setChecked(Prefs.get(PrefsConst.Q_CAR_DOOR_FR, PrefsConst.DefaultValue.Q_CAR_DOOR_FR));
            binding.swCarDoorFr.setOnCheckedChangeListener((buttonView, b) -> {
                ThreeDModelUtil.setFrontRightDoor(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "右前门状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_DOOR_FR, b);
                    } else {
                        Log.e(TAG, "右前门状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarDoorFr.setOnCheckedChangeListener(null);
                        binding.swCarDoorFr.setChecked(!b);
                    }
                });
            });

            binding.swCarDoorRl.setChecked(Prefs.get(PrefsConst.Q_CAR_DOOR_RL, PrefsConst.DefaultValue.Q_CAR_DOOR_RL));
            binding.swCarDoorRl.setOnCheckedChangeListener((buttonView, b) -> {
                ThreeDModelUtil.setRearLeftDoor(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "左后门状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_DOOR_RL, b);
                    } else {
                        Log.e(TAG, "左后门状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarDoorRl.setOnCheckedChangeListener(null);
                        binding.swCarDoorRl.setChecked(!b);
                    }
                });
            });

            binding.swCarDoorRr.setChecked(Prefs.get(PrefsConst.Q_CAR_DOOR_RR, PrefsConst.DefaultValue.Q_CAR_DOOR_RR));
            binding.swCarDoorRr.setOnCheckedChangeListener((buttonView, b) -> {
                ThreeDModelUtil.setRearRightDoor(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "右后门状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_DOOR_RR, b);
                    } else {
                        Log.e(TAG, "右后门状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarDoorRr.setOnCheckedChangeListener(null);
                        binding.swCarDoorRr.setChecked(!b);
                    }
                });
            });

            binding.swCarWindowFl.setChecked(Prefs.get(PrefsConst.Q_CAR_WINDOW_FL, PrefsConst.DefaultValue.Q_CAR_WINDOW_FL));
            binding.swCarWindowFl.setOnCheckedChangeListener((buttonView, b) -> {
                ThreeDModelUtil.setFrontLeftWindow(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "左前窗状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_WINDOW_FL, b);
                    } else {
                        Log.e(TAG, "左前窗状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarWindowFl.setOnCheckedChangeListener(null);
                        binding.swCarWindowFl.setChecked(!b);
                    }
                });
            });

            binding.swCarWindowFr.setChecked(Prefs.get(PrefsConst.Q_CAR_WINDOW_FR, PrefsConst.DefaultValue.Q_CAR_WINDOW_FR));
            binding.swCarWindowFr.setOnCheckedChangeListener((buttonView, b) -> {
                ThreeDModelUtil.setFrontRightWindow(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "右前窗状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_WINDOW_FR, b);
                    } else {
                        Log.e(TAG, "右前窗状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarWindowFr.setOnCheckedChangeListener(null);
                        binding.swCarWindowFr.setChecked(!b);
                    }
                });
            });

            binding.swCarWindowRl.setChecked(Prefs.get(PrefsConst.Q_CAR_WINDOW_RL, PrefsConst.DefaultValue.Q_CAR_WINDOW_RL));
            binding.swCarWindowRl.setOnCheckedChangeListener((buttonView, b) -> {
                ThreeDModelUtil.setRearLeftWindow(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "左后窗状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_WINDOW_RL, b);
                    } else {
                        Log.e(TAG, "左后窗状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarWindowRl.setOnCheckedChangeListener(null);
                        binding.swCarWindowRl.setChecked(!b);
                    }
                });
            });

            binding.swCarWindowRr.setChecked(Prefs.get(PrefsConst.Q_CAR_WINDOW_RR, PrefsConst.DefaultValue.Q_CAR_WINDOW_RR));
            binding.swCarWindowRr.setOnCheckedChangeListener((buttonView, b) -> {
                ThreeDModelUtil.setRearRightWindow(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "右后窗状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_WINDOW_RR, b);
                    } else {
                        Log.e(TAG, "右后窗状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarWindowRr.setOnCheckedChangeListener(null);
                        binding.swCarWindowRr.setChecked(!b);
                    }
                 });
            });

            binding.swCarHood.setChecked(Prefs.get(PrefsConst.Q_CAR_HOOD, PrefsConst.DefaultValue.Q_CAR_HOOD));
            binding.swCarHood.setOnCheckedChangeListener(((buttonView, b) -> {
                ThreeDModelUtil.setHoodState(b ? 1 : 0, status -> {
                    if (status) {
                        Log.d(TAG, "引擎盖状态更新成功: " + (b ? "开启" : "关闭"));
                        Prefs.put(PrefsConst.Q_CAR_HOOD, b);
                    } else {
                        Log.e(TAG, "引擎盖状态更新失败: ");
                        // 临时移除监听器，恢复开关状态，然后重新设置监听器
                        binding.swCarHood.setOnCheckedChangeListener(null);
                        binding.swCarHood.setChecked(!b);
                    }
                });
            }));
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    public interface onProgressChangedListener {
    }
}
