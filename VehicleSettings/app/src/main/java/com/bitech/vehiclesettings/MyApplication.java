package com.bitech.vehiclesettings;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.os.Handler;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.room.Room;

import com.bitech.vehicle3D.VehicleServiceManager;
import com.bitech.vehiclesettings.broadcast.TimeChangeReceiver;
import com.bitech.vehiclesettings.carapi.constants.Car3DModel;
import com.bitech.vehiclesettings.carapi.constants.CarSystemColor;
import com.bitech.vehiclesettings.database.SettingDatabase;
import com.bitech.vehiclesettings.manager.CarBaseManager;
import com.bitech.vehiclesettings.manager.CarBtManager;
import com.bitech.vehiclesettings.manager.CarDmManager;
import com.bitech.vehiclesettings.manager.CarWifiManager;
import com.bitech.vehiclesettings.service.GlobalDataObserver;
import com.bitech.vehiclesettings.service.VehicleService;
import com.bitech.vehiclesettings.service.VoiceControlService;
import com.bitech.vehiclesettings.service.sound.VoiceService;
import com.bitech.vehiclesettings.utils.Contacts;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.SystemColorUtil;
import com.bitech.vehiclesettings.view.negative.VoiceBroadcastReceiver;
import com.chery.ivi.vdb.client.VDBus;
import com.chery.ivi.vdb.client.bind.VDServiceDef;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.lion.api.bus.sync.SyncBusProxy;
import com.lion.api.bus.type.ModuleType;

import java.lang.ref.WeakReference;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by zhang on 2018/5/31.
 */

public class MyApplication extends Application {

    private static final String TAG = MyApplication.class.getSimpleName();
    private static final String CURRENT_NIGHT_MODE_TOPIC = "current_night_mode";

    private static MyApplication instance;

    private static Context sContext;

    private GlobalDataObserver globalDataObserver;

    // SharedPreferences对象
    private SharedPreferences sharedPref;

    // 数据库对象
    public static SettingDatabase settingDataBase;
    // 蓝牙管理对象
    private CarBtManager carBtManager;
    // 设备管理对象
    private CarDmManager carDmManager;

    // WIFI管理对象
    private CarWifiManager carWifiManager;

    private TimeChangeReceiver timeChangeReceiver;

    private VoiceBroadcastReceiver voiceBroadcastReceiver;

    public static Context getContext() {
        return sContext;
    }

    public static Context setContext(Context context) {
        return sContext = context;
    }

    private List<WeakReference<Activity>> mList = new LinkedList<>();

    private static Configuration mLastConfig;

    //实例化一次
    public synchronized static MyApplication getInstance() {
        if (null == instance) {
            instance = new MyApplication();
        }
        return instance;
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "TtsServiceAgent.getInstance() start ...");
        sContext = this;
        init(sContext);
        initData(sContext);
        mLastConfig = new Configuration(getResources().getConfiguration());
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_TIME_CHANGED);
        intentFilter.addAction(Intent.ACTION_TIME_TICK);
        intentFilter.addAction(Intent.ACTION_TIMEZONE_CHANGED);
        intentFilter.addAction(TimeChangeReceiver.SYSTEM_UI_FORMAT_CHANGE_URI);
        timeChangeReceiver = new TimeChangeReceiver();
        timeChangeReceiver.updateSunriseSunsetTime();
        registerReceiver(timeChangeReceiver, intentFilter);

        IntentFilter voiceBroadcastReceiverIntentFilter = new IntentFilter();
        voiceBroadcastReceiverIntentFilter.addAction(VoiceBroadcastReceiver.VOICE_CHANGE_URI);
        voiceBroadcastReceiverIntentFilter.addAction(VoiceBroadcastReceiver.AIR_ALERT_URI);
        voiceBroadcastReceiver = new VoiceBroadcastReceiver();
        registerReceiver(voiceBroadcastReceiver, voiceBroadcastReceiverIntentFilter);

        startService(getContext());
        VDBus aDefault = VDBus.getDefault();
        Log.d(TAG, "aDefault= " + aDefault);
        aDefault.init(this);
        aDefault.bindService(VDServiceDef.ServiceType.VR);

        // 初始化数据监听
        initGlobalDataObserver();
        // 添加全局异常处理
        initGlobalExceptionHandler();

        // 雄狮导航 同步总线初始化
        int[] syncBusList = onSyncBusList();
        int[] keepBondList = onKeepBondList();
        SyncBusProxy.getInstance().init(this, syncBusList, keepBondList);
    }

    private static final AtomicBoolean hasHandledCrash = new AtomicBoolean(false);

    private void initGlobalExceptionHandler() {
        Thread.setDefaultUncaughtExceptionHandler((thread, e) -> {
            Log.d("GlobalException", "Uncaught exception in thread: " + thread.getName() + ", exception: " + e.getMessage());

            // 使用 compareAndSet 来原子性地检查并设置值
            if (!hasHandledCrash.compareAndSet(false, true)) {
                Log.w("GlobalException", "Crash already handled, skipping duplicate handling");
                return; // 如果值已经是true，则返回
            }

            Log.i("GlobalException", "Handling crash for the first time");

            VehicleServiceManager.startCameraStatusPollingTask(
                    Car3DModel.ControlModelState.CONTROL_MODEL_STATE_ID,
                    Car3DModel.ControlModelState.LAUNCH
            );

            try {
                Thread.sleep(300);
            } catch (InterruptedException ignored) {
                Log.w("GlobalException", "Sleep interrupted", ignored);
            }

            Log.e("GlobalException", "Killing process due to uncaught exception", e);
            android.os.Process.killProcess(android.os.Process.myPid());
            System.exit(1);
        });

        Log.i("GlobalException", "Global exception handler initialized");
    }

    /**
     * 待绑定的服务列表（若工程不需要绑定同步总线，默认返回null，避免影响通讯效率）
     *
     * @return 服务列表 {@link ModuleType}
     */
    protected int[] onSyncBusList() {
        return new int[]{ModuleType.NAVI};
    }

    /**
     * 配置需要保持实时绑定的服务列表<br>
     * 当列表中的服务奔溃，需要实时拉起<br>
     * 非必要一般不做配置，避免服务杀不掉<br>
     * 与界面强挂钩的服务，可以配置
     *
     * @return 服务列表 {@link ModuleType}
     */
    protected int[] onKeepBondList() {
        return null;
    }

    public GlobalDataObserver getGlobalDataObserver() {
        if (globalDataObserver == null) {
            globalDataObserver = new GlobalDataObserver(getContext(), new Handler());
            globalDataObserver.registerObserver(GlobalDataObserver.KEY_SYSTEM_COLOR);
        }
        return globalDataObserver;
    }

    public String getSystemColorValue() {
        return getGlobalDataObserver().getGlobalData(GlobalDataObserver.KEY_SYSTEM_COLOR, CarSystemColor.SystemColorValue.BLUE);
    }

    private void initGlobalDataObserver() {
        globalDataObserver = getGlobalDataObserver();
        globalDataObserver.setOnGlobalDataChangeListener(new GlobalDataObserver.OnGlobalDataChangeListener() {

            @Override
            public void onGlobalDataChanged(String key, String value) {
                Log.d(TAG, "主题色改变: " + value);
                setTheme(SystemColorUtil.SystemColorValueToStyle(value));
            }
        });
    }

    private void startService(Context context) {
        if (VehicleService.getInstance() == null) {
            Intent startIntent = new Intent(context, VehicleService.class);
            context.startForegroundService(startIntent);
        }
        if (VoiceControlService.getInstance() == null) {
            Intent startIntentVoice = new Intent(context, VoiceControlService.class);
            context.startForegroundService(startIntentVoice);
        }
        if (VoiceService.getInstance() == null) {
            Intent startIntentVoice = new Intent(context, VoiceService.class);
            context.startForegroundService(startIntentVoice);
        }
    }

    /**
     * 车辆中心数据初始化.
     *
     * @param context 环境上下文
     */
    private void initData(Context context) {
        Log.d(TAG, "onCreate: 1");
        // 初始化SharedPreferences
        initSharedPreferences();
        // 初始化数据库
        initDataBase();
        // 初始化蓝牙管理类
        carBtManager = CarBtManager.Companion.getInstance();
        carBtManager.initBtManager(context);
        // 蓝牙回连标志位置为TRUE
        Contacts.INSTANCE.setIsBBtLashBack();
        // 回连蓝牙
        CarBtManager.Companion.getInstance().startBtReconnection();
        // 初始化CarService接口
        CarBaseManager.INSTANCE.initCarBaseManager(context);
        // 初始化WIFI管理类
        carWifiManager = CarWifiManager.Companion.getInstance();
        carWifiManager.initWifiManager(context);
//        String hotspotName = carBtManager.getBtName();
//        carWifiManager.updateFirstHotspotInfo(
//                hotspotName,
//                carWifiManager.getWifiHotspotPassword()
//        );

    }

    /**
     * 初始化SharedPreferences.
     */
    private void initSharedPreferences() {
        sharedPref = getSharedPreferences(Contacts.SETTINGS_DATA_NAME, Context.MODE_PRIVATE);
        Log.i(TAG, "initSharedPreferences : sharedPreferences = $sharedPref");
    }

    private void init(Context context) {
        Prefs.init(context);
        // 注册监听
        if (VehicleService.getInstance() == null) {
            Intent startIntent = new Intent(sContext, VehicleService.class);
            sContext.startForegroundService(startIntent);
        }


    }

    /**
     * 初始化数据库.
     */
    private void initDataBase() {
        settingDataBase = Room.databaseBuilder(
                this,
                SettingDatabase.class,
                Contacts.SETTINGS_DATABASE_NAME
        ).fallbackToDestructiveMigration().allowMainThreadQueries().build();
        //非空
        Log.d(TAG, "initDataBase: 初始化状态:" + settingDataBase);
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        // 关闭数据库
        settingDataBase.close();
    }

    // add Activity
    public void addActivity(WeakReference<Activity> wActivity) {
        mList.add(wActivity);
    }

    //关闭每一个list内的activity
    public void exit() {
        try {
            for (WeakReference<Activity> weakActivity : mList) {
                if ((weakActivity != null) && (weakActivity.get() != null)) {
                    weakActivity.get().finish();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // 当系统语言切换时会这里会得到信息
        if ((newConfig.diff(mLastConfig) & ActivityInfo.CONFIG_LOCALE) > 0) {
            //TODO 语言切换
            Log.d(TAG, "语言切换: " + mLastConfig.getLocales() + ", changed:" + newConfig.getLocales());
            setContext(createConfigurationContext(newConfig));

            Log.d(TAG, "语言切换 onConfigurationChanged: " + MyApplication.getContext().getString(R.string.app_name));
            // exit();

        }
        boolean currentNightMode = (newConfig.uiMode & Configuration.UI_MODE_NIGHT_MASK)
                == Configuration.UI_MODE_NIGHT_YES;

        LiveEventBus.get(CURRENT_NIGHT_MODE_TOPIC).post(currentNightMode);
        Log.d(TAG, "主题模式:" + (currentNightMode ? "深色模式" : "浅色模式"));

        mLastConfig = new Configuration(newConfig);

    }
}
