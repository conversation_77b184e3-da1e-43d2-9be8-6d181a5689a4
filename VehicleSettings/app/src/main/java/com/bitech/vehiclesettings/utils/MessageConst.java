package com.bitech.vehiclesettings.utils;

public class MessageConst {
    // 消息反馈常量类
    public static final int MSG_FEEDBACK_DELAY = 2000;
    public static final int MSG_FEEDBACK_DELAY_65 = 6500;
    public static final int QUICK_CENTRAL_LOCKING = 0x1001;
    public static final int QUICK_REAR_TAIL_GAGE = 0x1002;
    public static final int QUICK_REAR_MIRROR = 0x1003;
    public static final int QUICK_WINDOW = 0x1004;
    // 车窗锁
    public static final int QUICK_WINDOW_LOCK = 0x1005;
    public static final int QUICK_CLOSE_UNLOCK = 0x1006;
    // 遮阳帘
    public static final int QUICK_SUNSHADE = 0x1007;
    // 电动尾翼
    public static final int QUICK_AUTO_TAIL = 0x1008;
    // 天窗
    public static final int QUICK_SKY_WINDOW = 0x1009;
    // 感应靠近解锁
    public static final int QUICK_APPROACHING_UNLOCK = 0x1010;
    // 感应离车上锁
    public static final int QUICK_DEPARTURE_LOCKING = 0x1011;
    //外后视镜自动折叠
    public static final int QUICK_AUTO_REAR_MIRROR_FOLD = 0x1012;
    // 雨天自动加热外后视镜
    public static final int QUICK_AUTO_HOT_REAR_MIRROR = 0x1013;
    // 座椅便携 SeatPortable
    public static final int QUICK_SEAT_PORTABLE = 0x1014;
    // 倒车后视镜自动调节
    public static final int QUICK_BACK_AUTO_REAR_MIRROR_ADJUSE = 0x1015;
    // 锁车自动升窗
    public static final int QUICK_LOCK_AUTO_RAISE_WINDOW = 0x1016;
    // 锁车收起遮阳帘
    public static final int QUICK_LOCK_CAR_SUNROOF_SHADE_CLOSE = 0x1017;
    // 设防提示
    public static final int QUICK_DEFENSE_REMINDER = 0x1018;
    // 左儿童锁
    public static final int QUICK_LEFT_CHILD_LOCK = 0x1019;
    // 右儿童锁
    public static final int QUICK_RIGHT_CHILD_LOCK = 0x1020;
    // 自动落锁
    public static final int QUICK_AUTOMATIC_LOCKING = 0x1021;
    // 驻车自动解锁
    public static final int QUICK_AUTOMATIC_PARKING_UNLOCK = 0x1022;
    // 后尾门开启高度
    public static final int QUICK_HUD_ROATE = 0x1023;
    // 雨刮灵敏度
    public static final int QUICK_WIPERSENS = 0x1024;
    // 副驾安全气囊
    public static final int QUICK_DRIVE_AIR_BAG = 0x1025;
    // 加油小门
    public static final int QUICK_REFUEL_SMALL_DOOR = 0x1026;
    // 车辆控制初始化
    public static final int QUICK_CONTROL_INIT = 0x1027;
    // 遮阳帘前排全开
    public static final int QUICK_SUNSHADE_FRONT_OPEN = 0x1028;
    // 遮阳帘前排全关
    public static final int QUICK_SUNSHADE_FRONT_CLOSE = 0x1029;
    // 遮阳帘后排全开
    public static final int QUICK_SUNSHADE_REAR_OPEN = 0x102A;
    // 遮阳帘后排全关
    public static final int QUICK_SUNSHADE_REAR_CLOSE = 0x102B;
    /**
     * 灯光
     */
    public static final int LIGHT_LAMP_HIGH = 0x1101;
    public static final int LIGHT_LAMP_DELAY = 0x1102;

    public static final int LIGHT_APPROACHING_WELCOME = 0x1103;
    public static final int LIGHT_AUTOMATIC_CEILING = 0x1104;
    public static final int LIGHT_LAMP_MUSIC_RHYTHM = 0x1105;
    public static final int LIGHT_HIGH_LOW_SWITCH = 0x1106;
    public static final int LIGHT_INTELLIGENT_WELCOME = 0x1107;
    public static final int LIGHT_INTELLIGENT_WELCOME_ST = 0x1108;
    public static final int LIGHT_OUT_LAMP_CONTROL = 0x1109;
    public static final int LIGHT_OUT_REAR_FOG_LAMP = 0x1110;

    public static final int LIGHT_INIT_DATA = 0x1111;
    public static final int LIGHT_PICK_COLOR = 0x1112;
    public static final int LIGHT_EFFECT = 0x1113;

    public static final int LIGHT_IN_INIT_DATA = 0x1114;

    public static final int LIGHT_PICK_COLOR_CUSTOM = 0x1115;
    /**
     * 车辆设置
     */
    public static final int CAR_HUD_ROATE = 0x1501;
    public static final int CAR_WIPERSENS = 0x1502;
    public static final int CAR_LOCK_AUTO_RAISE_WINDOW = 0x1503;
    public static final int CAR_DEFENSE_REMINDER = 0x1504;
    public static final int CAR_LEFT_CHILD_LOCK = 0x1505;
    public static final int CAR_RIGHT_CHILD_LOCK = 0x1506;
    public static final int CAR_AUTOMATIC_LOCKING = 0x1507;
    public static final int CAR_AUTOMATIC_PARKING_UNLOCK = 0x1508;
    public static final int CAR_WIPER_REPAIR_MOD = 0x1509;
    public static final int CAR_OVER_SPEED = 0x1510;
    public static final int CAR_FATIGUE_DRIVING_REMINDER = 0x1511;
    public static final int CAR_MAINTAIN_TIPS = 0x1512;
    public static final int CAR_MAINTAIN_RESET = 0x1513;
    public static final int CAR_DRIVE_AIR_BAG = 0x1514;
    public static final int CAR_OVER_SPEED_SEEKBAR = 0x1515;
    public static final int CAR_FATIGUE_DRIVE_SEEKBAR = 0x1516;
    public static final int QUICK_REAR_SCREEN_CONTROL = 0x1517;


    // 智慧识别
    public static final int RECOGNITION_DMS_CAMERA = 0x2000;
    public static final int RECOGNITION_FATIGUE = 0x2001;
    public static final int RECOGNITION_DISTRACTION = 0x2002;
    public static final int RECOGNITION_CALL = 0x2003;
    public static final int RECOGNITION_DRINK = 0x2004;
    public static final int RECOGNITION_SEAT_HEAT = 0x2005;
    public static final int RECOGNITION_SEAT_VENTILATION = 0x2006;
    public static final int RECOGNITION_SIGHT_UNLOCK = 0x2007;
    public static final int RECOGNITION_GREET = 0x2008;
    public static final int RECOGNITION_SMOKE = 0x2009;

    // 系统
    public static final int SYSTEM_ANALYSIS = 0x3001;
    public static final int SYSTEM_PERMISSION = 0x3002;
    public static final int SYSTEM_PERMISSION_APP_SWITCH_CAMERA = 0x3003;
    public static final int SYSTEM_PERMISSION_APP_SWITCH_MICROPHONE = 0x3004;
    public static final int SYSTEM_PERMISSION_APP_SWITCH_LOCATION = 0x3005;
    public static final int SYSTEM_INSTRUMENT_FUEL_UNIT = 0x3006;
    public static final int SYSTEM_TIRE_PRESSURE_UNIT = 0x3007;
    public static final int SYSTEM_POWER_CONSUMPTION_UNIT = 0x3008;

    /**
     * 显示
     */
    public static final int DISPLAY_SHOW_LYRICS = 0x4000;
    public static final int DISPLAY_VIDEO_LIMIT = 0x4001;
    public static final int DISPLAY_FP = 0x4002;

    /**
     * 声音
     */
    public static final int VOICE_CALL_BROADCAST = 0x5000;
    public static final int VOICE_LOW_SPEED_ANALOG = 0x5001;
    public static final int VOICE_EXTERNAL_MODE = 0x5002;
    public static final int VOICE_LOWER_MEDIA_TONE = 0x5003;
    public static final int VOICE_BUTTON_SOUND = 0x5004;
    public static final int VOICE_ALARM_TYPE = 0x5005;
    public static final int VOICE_COMPENSATION = 0x5006;
    public static final int VOICE_HEADREST = 0x5007;
    public static final int VOICE_EQ = 0x5008;
    public static final int VOICE_SURROUND_SOUND = 0x5009;
    public static final int VOICE_VIRTUAL_SCENE = 0x500A;
    public static final int VOICE_SUB_BASS = 0x5010;
    public static final int VOICE_BASS = 0x5011;
    public static final int VOICE_LOW_MID = 0x5012;
    public static final int VOICE_MID = 0x5013;
    public static final int VOICE_HIGH_MID = 0x5014;
    public static final int VOICE_TREBLE = 0x5015;
    public static final int VOICE_SUPER_TREBLE = 0x5016;
    public static final int VOICE_EQ_POSITION = 0x5017;

    /**
     * 连接
     */
    public static final int CONNECT_FRONT_CHARGING = 0x6000; // 前排充电
    public static final int CONNECT_FORGET_REMINDER = 0x6001; // 遗忘提醒
    public static final int CONNECT_BACK_CHARGING = 0x6002; // 后排充电
    public static final int CONNECT_FIVE_G = 0x6003; // 5G
    public static final int CONNECT_WIRELESS_CHARGING_REMINDER = 0x6004;
    public static final int CONNECT_WIRELESS_CHARGING_LAUNCHER = 0x6005;

    /**
     * 驾驶
     */
    public static final int DRIVING_CAR_MODE = 0x7000;
    public static final int QUICK_EXTREME_PURE_ELECTRIC = 0x7001; // 极致充电
    public static final int COMFORT_BRAKING = 0x7002; // 舒适制动
    public static final int AUTO_PARKING = 0x7003; // 自动驻车
    public static final int PARKING_BRAKE = 0x7004; // 驻车制动
    public static final int BODY_STABILITY_CONTROL = 0x7005; // 车身稳定控制
    public static final int STEEP_SLOPE_DESCENT = 0x7006; // 陡坡缓降
    public static final int INTEL_SUSPENSION_AIMING = 0x7007; // 悬架智能预瞄
    public static final int COMFORT_BRAKING_RANK = 0x7008; // 舒适制动等级
    public static final int DRIVING_DRIVING_MODE = 0x7009; // 个性化-驾驶模式
    public static final int DRIVING_SUSPENSION_MODE = 0x7010; // 个性化-悬挂模式
    public static final int DRIVING_SWERVE_MODE = 0x7011; // 个性化-转向模式
    public static final int DRIVING_BRAKING_MODE = 0x7012; // 个性化-制动模式
    public static final int DRIVING_POWER_PROTECTION = 0x7013; // 个性化-保电电量
    public static final int TRACTION_MODE = 0x7014; // 牵引模式
    public static final int CLEAN_MODE = 0x7015;
    public static final int DRIVING_MODE_LIFE_CYCLE = 0x7016;
    public static final int DRIVING_MODE_KEY_EVENT_SERVICE = 0x7017;
    /**
     * 车辆状态
     */
    public static final int CONDITION_MAINTAIN_TIP = 0x8000;

    public static final String DATA_BUNDLE_COLOR = "color";
    public static final String DATA_BUNDLE_BRI = "bri";
    public static final String DATA_BUNDLE_POS = "pos";
}
