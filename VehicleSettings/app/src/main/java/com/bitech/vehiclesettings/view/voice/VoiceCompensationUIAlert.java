package com.bitech.vehiclesettings.view.voice;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundVoiceCompensationBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.viewmodel.VoiceViewModel;

public class VoiceCompensationUIAlert extends BaseDialog {
    private static final String TAG = VoiceCompensationUIAlert.class.getSimpleName();

    private static VoiceCompensationUIAlert.ChangedListener changedListener;

    public VoiceCompensationUIAlert(Context context, int theme) {
        super(context, theme);
    }

    public static void setOnChangedListener(VoiceCompensationUIAlert.ChangedListener onProgressChangedListener) {
        VoiceCompensationUIAlert.changedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private VoiceCompensationUIAlert dialog = null;
        private VoicePresenter voicePresenter;
        private VoiceViewModel viewModel;
        private SafeHandler voiceHandler;
        private DialogAlertSoundVoiceCompensationBinding binding;

        public Builder(Context context, VoicePresenter presenter, VoiceViewModel viewModel, SafeHandler voiceHandler) {
            this.context = context;
            this.voicePresenter = presenter;
            this.viewModel = viewModel;
            this.voiceHandler = voiceHandler;
        }

        /**
         * Create the custom dialog
         */
        public VoiceCompensationUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new VoiceCompensationUIAlert(context, R.style.Dialog);
            binding = DialogAlertSoundVoiceCompensationBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            init();
            setupClickListeners();
            changedListener.initObserve(binding);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);

            return dialog;
        }

        public void init() {
            int selected = Prefs.get(PrefsConst.VOICE_COMPENSATION, CarVoice.Compensation.OFF);
            viewModel.setCompensation(selected);
            binding.spvVC.setItems(R.string.str_voice_compensation_close, R.string.str_voice_compensation_low, R.string.str_voice_compensation_middle, R.string.str_voice_compensation_high);
            binding.spvVC.setSelectedIndex(CarVoice.CompensationUI.signalReverse(selected), false);
        }

        public void setupClickListeners() {
            binding.spvVC.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    switch (index) {
                        case CarVoice.CompensationUI.OFF:
                            onSelected(CarVoice.Compensation.OFF);
                            break;
                        case CarVoice.CompensationUI.LOW:
                            onSelected(CarVoice.Compensation.LOW);
                            break;
                        case CarVoice.CompensationUI.MIDDLE:
                            onSelected(CarVoice.Compensation.MIDDLE);
                            break;
                        case CarVoice.CompensationUI.HIGH:
                            onSelected(CarVoice.Compensation.HIGH);
                    }
                }
            });
        }

        private void onSelected(int selected) {
            viewModel.setCompensation(selected);
            voicePresenter.setCompensation(selected);
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_COMPENSATION);
        }

        public void updateCompensation(int status) {
            switch (status) {
                case CarVoice.Compensation.OFF:
                    status = 0;
                    break;
                case CarVoice.Compensation.LOW:
                    status = 1;
                    break;
                case CarVoice.Compensation.MIDDLE:
                    status = 2;
                    break;
                case CarVoice.Compensation.HIGH:
                    status = 3;
                    break;
            }
            Log.d(TAG, "updateCompensation status: " + status);
            Prefs.put(PrefsConst.VOICE_COMPENSATION, status);
            binding.spvVC.setSelectedIndex(status, true);
        }

        public void dismiss() {
            if (dialog != null) {
                dialog.dismiss();
            }
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface ChangedListener {
        void initObserve(DialogAlertSoundVoiceCompensationBinding binding);
    }
}
