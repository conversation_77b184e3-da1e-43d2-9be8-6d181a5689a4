package com.bitech.vehiclesettings.service.atmosphere;

import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.bean.atmosphere.AmbLigBean;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.platformlib.utils.AtmoLightConst;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.utils.CommonConst;

/**
 * 呼吸灯接口
 */
public class BreathingEffectService {
    private static final String TAG = BreathingEffectService.class.getSimpleName();
    private static final long TARGET_INTERVAL_MS = 1700;
    private final Handler mHandler;
    private Runnable mTaskRunnable;
    private volatile boolean isRunning = false;
    private long mLastExecutionTime;
    int count = 0;
    private LightInBean lInBean;
    private LightManager manager = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
    // 单例实现
    private static volatile BreathingEffectService instance;

    public static BreathingEffectService getInstance() {
        if (instance == null) {
            synchronized (BreathingEffectService.class) {
                if (instance == null) {
                    instance = new BreathingEffectService();
                }
            }
        }
        return instance;
    }

    public BreathingEffectService() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public void start() {
        if (isRunning) {
            return;
        }
        isRunning = true;
        mLastExecutionTime = SystemClock.elapsedRealtime();
        mTaskRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isRunning) {
                    return;
                }
                // 1. 执行任务
                doTask();
                // 2. 计算下一次执行时间（动态补偿误差）
                long currentTime = SystemClock.elapsedRealtime();
                long elapsed = currentTime - mLastExecutionTime;
                long nextDelay = Math.max(0, TARGET_INTERVAL_MS - elapsed);
                // 3. 递归调度下一次任务
                if (count == 1) {
                    mHandler.postDelayed(this, nextDelay - 200);
                } else {
                    mHandler.postDelayed(this, nextDelay);
                }
                mLastExecutionTime = currentTime + nextDelay;
            }
        };
        mHandler.post(mTaskRunnable);
    }

    public void stop() {
        isRunning = false;
        if (mTaskRunnable != null) {
            mHandler.removeCallbacks(mTaskRunnable);
        }
        AmbLigBean ambLigBean = getAmbLigBreathe(0);
        manager.setLightAmbLightCan(ambLigBean);
    }

    private void doTask() {
        Log.d(TAG, "[呼吸功能] doTask: ");
        lInBean = manager.getLightInData();
        boolean isSingleColor = false;
        int singleColorLin = 0;
        int frontColorLin = 0;
        int rearColorLin = 0;
        String singleColorHex = AtmoLightConst.DefaultValue.L_LIGHT_ALL_COLOR;
        String frontColorHex = AtmoLightConst.DefaultValue.L_LIGHT_ALL_COLOR;
        String rearColorHex = AtmoLightConst.DefaultValue.L_LIGHT_ALL_COLOR;
        if (lInBean != null) {
            int theme = lInBean.getLightSel();
//            if (theme == 0) {
            if (lInBean.getColorPos() >= CommonConst.COLOR_POS_MAX) {
                isSingleColor = true;
                singleColorLin = lInBean.getSingleColorLin();
                singleColorHex = lInBean.getSingleColor();
            } else {
                isSingleColor = false;
                frontColorLin = lInBean.getMutiFrontColorLin();
                rearColorLin = lInBean.getMutiRearColorLin();
                frontColorHex = lInBean.getMutiFrontColor();
                rearColorHex = lInBean.getMutiRearColor();
            }
//            } else {
//                frontColorLin = lInBean.getFront().getFrontColorLin();
//                rearColorLin = lInBean.getRear().getRearColorLin();
//                frontColorHex = lInBean.getFront().getFrontColor();
//                rearColorHex = lInBean.getRear().getRearColor();
//            }
        }
        AmbLigBean ambLigBean;
        if (count == 0) {
            // 渐亮
            if (isSingleColor) {
                Log.d(TAG, "[呼吸功能]渐亮 singleColorLin: " + singleColorLin + ",singleColorHex:" + singleColorHex);
                ambLigBean = getAmbLigUp(0, singleColorLin);
                manager.setLightAmbLightCan(ambLigBean);

            } else {
                Log.d(TAG, "[呼吸功能]渐亮 frontColorLin: " + frontColorLin + ",rearColorLin:" + rearColorLin + ",frontColorHex:" + frontColorHex + ",rearColorHex:" + rearColorHex);
                ambLigBean = getAmbLigUp(1, frontColorLin);
                manager.setLightAmbLightCan(ambLigBean);
                ambLigBean = getAmbLigUp(2, rearColorLin);
                manager.setLightAmbLightCan(ambLigBean);
            }
            count = 1;
        } else {
            /// 渐灭
            if (isSingleColor) {
                Log.d(TAG, "[呼吸功能]渐灭 singleColorLin: " + singleColorLin + ",Color:" + lInBean.getSingleColor());
                ambLigBean = getAmbLigDown(0, singleColorLin);
                manager.setLightAmbLightCan(ambLigBean);
            } else {
                Log.d(TAG, "[呼吸功能]渐灭 frontColorLin: " + frontColorLin + ",rearColorLin:" + rearColorLin + ",frontColorHex:" + frontColorHex + ",rearColorHex:" + rearColorHex);
                ambLigBean = getAmbLigDown(1, frontColorLin);
                manager.setLightAmbLightCan(ambLigBean);
                ambLigBean = getAmbLigDown(2, rearColorLin);
                manager.setLightAmbLightCan(ambLigBean);
            }
            count = 0;
        }
    }

    /**
     * 渐亮
     *
     * @param colorIdx
     * @return
     */
    private AmbLigBean getAmbLigUp(int pos, int colorIdx) {
        AmbLigBean up1 = getFrontOrRear(pos);
        up1.setAmbLigBriAdj(0x64).setAmbLigColorAdj(colorIdx).setAmbLigFadeINorOUTStepTi(30).setAmbLigFlngModSel(0x1);
        return up1;
    }

    private AmbLigBean getAmbLigDown(int pos, int colorIdx) {
        AmbLigBean down1 = getFrontOrRear(pos);
        down1.setAmbLigBriAdj(0x00).setAmbLigColorAdj(colorIdx).setAmbLigFadeINorOUTStepTi(30).setAmbLigFlngModSel(0x1);
        return down1;
    }

    private AmbLigBean getAmbLigBreathe(int flngModSel) {
        AmbLigBean up1 = getFrontOrRear(0);
        up1.setAmbLigFlngModSel(flngModSel);
        return up1;
    }

    private AmbLigBean getFrontOrRear(int pos) {
        AmbLigBean ambLigBean = new AmbLigBean();
        if (CommonConst.LIGHT_SW_FRONT == pos) {
            // 2,4,5,7,8
            ambLigBean.setFront();
        } else if (CommonConst.LIGHT_SW_REAR == pos) {
            // 9
            ambLigBean.setRear();
        } else {
            ambLigBean.setAll();
        }
        return ambLigBean;
    }
}
