package com.bitech.vehiclesettings.view.connect;

import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.Topics;
import com.bitech.platformlib.interfaces.connect.IConnectManagerListener;
import com.bitech.platformlib.manager.ConnectManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarConnect;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectWirelessChargingLauncherBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectWirelessChargingTipLauncherBinding;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class WirelessChargingTipsLauncherUIAlert extends BaseDialog {
    private static final String TAG = WirelessChargingTipsLauncherUIAlert.class.getSimpleName();
    private static WirelessChargingTipsLauncherUIAlert.onProgressChangedListener onProgressChangedListener;


    public WirelessChargingTipsLauncherUIAlert(@NonNull Context context) {
        super(context);
    }

    public WirelessChargingTipsLauncherUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected WirelessChargingTipsLauncherUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static WirelessChargingTipsLauncherUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(WirelessChargingTipsLauncherUIAlert.onProgressChangedListener onProgressChangedListener) {
        WirelessChargingTipsLauncherUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private WirelessChargingTipsLauncherUIAlert.OnDialogResultListener listener;

    public static class Builder{
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConnectWirelessChargingTipLauncherBinding binding;
        DetailsUIAlert.Builder detailUIAlert;
        boolean globalAlert = false;
        private boolean isActive;
        public boolean isBlueOpen() {
            return isBlueOpen;
        }
        private final Handler mainHandler = new Handler(Looper.getMainLooper());

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }
        private boolean isBlueOpen = false;
        private WirelessChargingTipsLauncherUIAlert dialog = null;
        public Builder(Context context) {
            this.context = context;
        }
        public WirelessChargingTipsLauncherUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setGlobalAlert(boolean b){
            globalAlert = b;
        }

        public WirelessChargingTipsLauncherUIAlert create() {
            // instantiate the dialog with the custom Theme
            int themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue);
            Context themedContext = new ContextThemeWrapper(context, themeId);
            if (dialog == null)
                dialog = new WirelessChargingTipsLauncherUIAlert(themedContext, R.style.Dialog);
            // 设置dialog的bind
            binding = DialogAlertConnectWirelessChargingTipLauncherBinding.inflate(LayoutInflater.from(themedContext));
            context.setTheme(Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue));
            detailUIAlert = new DetailsUIAlert.Builder(context);
            dialog.setContentView(binding.getRoot());
            dialog.setCancelable(isCan);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            // 不让背景变暗
            window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            layoutParams.dimAmount = 0f;
            layoutParams.type = globalAlert ?WindowManager.LayoutParams.TYPE_SYSTEM_ALERT: WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            layoutParams.width = 1176;
            layoutParams.height = 534;
            window.setAttributes(layoutParams);
            return dialog;
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
    }
}
