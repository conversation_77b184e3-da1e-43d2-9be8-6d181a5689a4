package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.adapter.PermissionAppAdapter;
import com.bitech.vehiclesettings.bean.AppPermissionBean;
import com.bitech.vehiclesettings.bean.PrivacyStatementBean;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionAppBinding;
import com.bitech.vehiclesettings.view.common.PdfDetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class PermissionAppUIAlert extends BaseDialog {
    private static final String TAG = PermissionAppUIAlert.class.getSimpleName();
    private static PermissionAppUIAlert.onProgressChangedListener onProgressChangedListener;

    public PermissionAppUIAlert(@NonNull Context context) {
        super(context);
    }

    public PermissionAppUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PermissionAppUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    // 移除静态监听器
    private onProgressChangedListener progressListener;

    public void setProgressListener(onProgressChangedListener listener) {
        this.progressListener = listener;
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        PermissionAppUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private PermissionAppUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSPermissionAppBinding binding;
        private boolean isNormalDismiss = true;

        PermissionAppAdapter permissionAppAdapter;

        private PermissionAppAuthorizedUIAlert.Builder permissionCameraUIAlert, permissionMicrophoneUIAlert, permissionPositionUIAlert;

        int position, size;
        String title, switchText, permissionLink;
        String closeTextStr = "", openTextStr = "", closeAppStr = "", openAppStr = "";

        private boolean isDetailsDialogShowing = false;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        public PermissionAppUIAlert dialog = null;

        public Builder(Context context, int position,
                       String title, String switchText, String permissionLink,
                       String closeTextStr, String openTextStr, String closeAppStr, String openAppStr) {
            this.context = context;
            this.position = position;
            this.title = title;
            this.switchText = switchText;
            this.permissionLink = permissionLink;
            this.closeTextStr = closeTextStr;
            this.openTextStr = openTextStr;
            this.closeAppStr = closeAppStr;
            this.openAppStr = openAppStr;
        }


        public PermissionAppUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public PermissionAppUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PermissionAppUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSPermissionAppBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1912; // 或者使用具体的像素值
            layoutParams.height = 1080;
            window.setAttributes(layoutParams);

            // 初始化授权应用
            initPermissionApp();
            // 设置开关触发弹窗
            binding.swPermission.setOnClickListener(v -> {
                isNormalDismiss = false;
                switchOn();
            });
            // 初始化switch
            initSwitch();
            // 设置文本
            setText();
            // 设置开关触发文本改变
            switchText();
            // 设置自动更新文本信息
            autoUpdateText();

            return dialog;
        }

        private void autoUpdateText() {
            dialog.setOnShowListener(dialogInterface -> {
                View rootView = Objects.requireNonNull(dialog.getWindow()).getDecorView(); // 获取 Dialog 根视图
                if (rootView.hasFocus()) {
                    switchText();
                }
            });
        }

        private void initSwitch() {
            onProgressChangedListener.initSwitch(this.position);
        }

        private void switchOn() {
            int status = onProgressChangedListener.onSwitch(position);
            if (status == 0) {
                if (position == 0) {
                    permissionCameraUIAlert = new PermissionAppAuthorizedUIAlert.Builder(
                            context, position,
                            context.getString(R.string.str_system_permission_camera_app_open_title),
                            context.getString(R.string.str_system_permission_camera_app_open_content)
                    );
                    permissionCameraUIAlert.create().show();
                } else if (position == 1) {
                    permissionMicrophoneUIAlert = new PermissionAppAuthorizedUIAlert.Builder(
                            context, position,
                            context.getString(R.string.str_system_permission_microphone_app_open_title),
                            context.getString(R.string.str_system_permission_microphone_app_open_content)
                    );
                    permissionMicrophoneUIAlert.create().show();
                } else if (position == 2) {
                    permissionPositionUIAlert = new PermissionAppAuthorizedUIAlert.Builder(
                            context, position,
                            context.getString(R.string.str_system_permission_location_app_open_title),
                            context.getString(R.string.str_system_permission_location_app_open_content)
                    );
                    permissionPositionUIAlert.create().show();
                }
            }
        }

        private void setText() {
            binding.tvTitle.setText(title);
            binding.tvSwitch.setText(switchText);
        }

        public void switchText() {
            binding.swPermission.setOnCheckedChangeListener((buttonView, isChecked) -> {
                updateText(isChecked);
            });
            updateText(binding.swPermission.isChecked());
        }

        public DialogAlertSPermissionAppBinding getBinding() {
            return binding;
        }

        @SuppressLint("SetTextI18n")
        private void updateText(boolean isChecked) {
            String text = isChecked ? openTextStr : closeTextStr;
            int status = onProgressChangedListener.getPermissionDuring(position);
            String link = isChecked ? (status == 0 ? context.getString(R.string.str_system_permission_link_1)
                    : context.getString(R.string.str_system_permission_link_2)) : "";

            SpannableString spannable = new SpannableString(text + link);

            if (!link.isEmpty()) {
                int start = text.length();
                int end = start + link.length();

                spannable.setSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        isNormalDismiss = false;
                        PermissionAppAuthorizedMainUIAlert.Builder permissionAppAuthorizedMainUIAlert = new PermissionAppAuthorizedMainUIAlert.Builder(context, position);
                        permissionAppAuthorizedMainUIAlert.create().show();
                    }
                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(ContextCompat.getColor(context, R.color.blue)); // 链接颜色
                        ds.setUnderlineText(false); // 可选：去掉下划线
                    }
                }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }

            if(title.equals(context.getString(R.string.str_system_permission_3))){
                String link2 = context.getString(R.string.str_system_permission_link_3);
                int start = text.indexOf(link2);
                if(start != -1){
                    int end = start + link2.length();
                    spannable.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            if (isDetailsDialogShowing) {
                                return;
                            }
                            PrivacyStatementBean item = onProgressChangedListener.getPrivacyStatementForLink(0);
                            if (item != null) {
                                isDetailsDialogShowing = true;
                                // 这部分逻辑与 PrivacyStatementUIAlert 中的完全一致
                                PdfDetailsUIAlert.Builder detailsUIAlert = new PdfDetailsUIAlert.Builder(context);
                                Dialog detailsDialog = detailsUIAlert.create(item.getDialogTitle(), item.getDialogContent(), 1912, 1080);

                                detailsDialog.setOnDismissListener(d -> {
                                    isDetailsDialogShowing = false; // 对话框关闭时重置标志位
                                });

                                detailsUIAlert.setScrollable(true);
                                detailsDialog.show();
                            }
                        }
                        @Override
                        public void updateDrawState(@NonNull TextPaint ds) {
                            super.updateDrawState(ds);
                            ds.setColor(ContextCompat.getColor(context, R.color.blue)); // 链接颜色
                            ds.setUnderlineText(false); // 可选：去掉下划线
                        }
                    }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
            }

            binding.tvPermissionText.setText(spannable);
            binding.tvPermissionText.setHighlightColor(Color.TRANSPARENT); // 关键代码：禁用点击背景
            binding.tvPermissionText.setMovementMethod(LinkMovementMethod.getInstance());

            // 处理其它 UI 逻辑
            binding.recyclerView.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            binding.tvPermissionApp.setText(isChecked ? openAppStr : closeAppStr);
            if (isChecked) binding.tvPermissionApp.setText(
                    binding.tvPermissionApp.getText().toString().substring(0, 3) +
                            size +
                            binding.tvPermissionApp.getText().toString().substring(4)
            );
        }


        private void initPermissionApp() {
            permissionAppAdapter = new PermissionAppAdapter(new ArrayList<>(), (i, bean) -> {
                onProgressChangedListener.updatePermissionApp(position, i, bean);
            });
            binding.recyclerView.setLayoutManager(new GridLayoutManager(context, 2));
            binding.recyclerView.setAdapter(permissionAppAdapter);
            if (binding.swPermission.isChecked()) updatePermissionList();
        }

        @SuppressLint("SetTextI18n")
        public void updatePermissionList() {
            List<AppPermissionBean> permissionAppBeans = onProgressChangedListener.updatePermissionList(position);
            this.size = permissionAppBeans.size();
            updateText(true);
            permissionAppAdapter.updateData(permissionAppBeans);

        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public boolean isNormalDismiss() {
            return isNormalDismiss;
        }

        public void setNormalDismiss(boolean normalDismiss) {
            isNormalDismiss = normalDismiss;
        }
    }



    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface onProgressChangedListener {
        List<AppPermissionBean> updatePermissionList(int position);

        int onSwitch(int position);

        void initSwitch(int position);

        int getPermissionDuring(int position);

        void updatePermissionApp(int position, int index, AppPermissionBean bean);

        void openPermissionApp();

        PrivacyStatementBean getPrivacyStatementForLink(int index);
    }
}
