package com.bitech.vehiclesettings.view.condition;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertConditionMaintenceResetConfirmBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertConditionNextMaintenanceBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class MaintenanceConfirmUIAlert extends BaseDialog {
    private static final String TAG = MaintenanceConfirmUIAlert.class.getSimpleName();
    private static MaintenanceConfirmUIAlert.onProgressChangedListener onProgressChangedListener;


    public MaintenanceConfirmUIAlert(@NonNull Context context) {
        super(context);
    }

    public MaintenanceConfirmUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected MaintenanceConfirmUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static MaintenanceConfirmUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(MaintenanceConfirmUIAlert.onProgressChangedListener onProgressChangedListener) {
        MaintenanceConfirmUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private MaintenanceConfirmUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConditionMaintenceResetConfirmBinding binding;
        public boolean isBlueOpen() {
            return isBlueOpen;
        }
        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private MaintenanceConfirmUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public MaintenanceConfirmUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public MaintenanceConfirmUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new MaintenanceConfirmUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertConditionMaintenceResetConfirmBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 502;
            window.setAttributes(layoutParams);
            initData();
            binding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onProgressChangedListener.onConfirm();
                    dialog.dismiss();
                }
            });
            binding.tvCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
            return dialog;
        }

        private void initData() {

        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onConfirm();
    }
}
