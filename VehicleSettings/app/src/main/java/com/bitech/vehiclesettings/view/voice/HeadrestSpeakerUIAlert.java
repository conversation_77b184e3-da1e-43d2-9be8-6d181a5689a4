package com.bitech.vehiclesettings.view.voice;


import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundHeadrestSpeakerBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.bitech.vehiclesettings.viewmodel.VoiceViewModel;

public class HeadrestSpeakerUIAlert extends BaseDialog {
    private static final String TAG = HeadrestSpeakerUIAlert.class.getSimpleName();

    private static HeadrestSpeakerUIAlert.ChangedListener changedListener;

    public HeadrestSpeakerUIAlert(Context context) {
        super(context);
    }

    public HeadrestSpeakerUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected HeadrestSpeakerUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static HeadrestSpeakerUIAlert.ChangedListener getOnChangedListener() {
        return changedListener;
    }

    public static void setOnChangedListener(HeadrestSpeakerUIAlert.ChangedListener onProgressChangedListener) {
        HeadrestSpeakerUIAlert.changedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private VoicePresenter voicePresenter;
        private VoiceViewModel viewModel;
        private SafeHandler voiceHandler;
        private DialogAlertSoundHeadrestSpeakerBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private HeadrestSpeakerUIAlert dialog = null;
        TextView tvHeadrest;

        public Builder(Context context, VoicePresenter presenter, VoiceViewModel viewModel, SafeHandler voiceHandler) {
            this.context = context;
            tvHeadrest = ((Activity) context).findViewById(R.id.tvHeadrest);
            this.voicePresenter = presenter;
            this.viewModel = viewModel;
            this.voiceHandler = voiceHandler;
        }

        public void dismiss() {
            if (dialog != null) {
                dialog.dismiss();
            }
        }

        public HeadrestSpeakerUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public HeadrestSpeakerUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new HeadrestSpeakerUIAlert(context, R.style.Dialog);
            binding = DialogAlertSoundHeadrestSpeakerBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            init();
            setupClickListeners();
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176; // 或者使用具体的像素值
            window.setAttributes(layoutParams);


            changedListener.initObserve(binding);
            return dialog;
        }

        public void init() {
            int type = Prefs.get(PrefsConst.VOICE_HEADREST, CarVoice.HeadRest.DEFAULT);
            viewModel.setHeadRest(type);
            binding.spvHeadrest.setItems(R.string.str_headrest_gx, R.string.str_headrest_jx, R.string.str_headrest_sx);
            switch (type) {
                case CarVoice.HeadRest.SHARE:
                    binding.spvHeadrest.setSelectedIndex(0, false);
                    break;
                case CarVoice.HeadRest.DRIVING:
                    binding.spvHeadrest.setSelectedIndex(1, false);
                    break;
                case CarVoice.HeadRest.PRIVATE:
                    binding.spvHeadrest.setSelectedIndex(2, false);
                    break;
            }
            updateUI(type);
        }

        private void onHeadrestSelected(int headrestType) {
            voicePresenter.setHeadRest(headrestType);
            viewModel.setHeadRest(headrestType);
            updateUI(headrestType);
            tvHeadrest.setText(getTextRes(headrestType));
            voiceHandler.sendMessageDelayed(MessageConst.VOICE_HEADREST);
        }

        public void setupClickListeners() {
            binding.spvHeadrest.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    int i = binding.spvHeadrest.currentIndex;
                    switch (index) {
                        case 0:
                            onHeadrestSelected(CarVoice.HeadRest.SHARE);
                            break;
                        case 1:
                            onHeadrestSelected(CarVoice.HeadRest.DRIVING);
                            break;
                        case 2:
//                        if (voicePresenter.getEQ() == CarVoice.EQ.ALL) {
//                            EToast.showToast(context, context.getText(R.string.str_headrest_sx_false), Toast.LENGTH_SHORT, false);
//                            binding.spvHeadrest.postDelayed(() -> {
//                                binding.spvHeadrest.setSelectedIndex(i, true);
//                            }, 500);
//                        } else {
////                            onHeadrestSelected(VDValueDsp.ExtAMPHeadrestSpeakerMode.HEADREST_SPEAKER_PRIVATE_MODE);
//
//                        }
                            onHeadrestSelected(CarVoice.HeadRest.PRIVATE);
                            break;
                    }
                }

                @Override
                public void onItemClicked(int index, String text) {

                }
            });
            // TODO 头枕扬声器设置
        }

        public void updateHeadRest(int status) {
            Log.d(TAG, "updateHeadRest status: " + status);
            ((Activity) context).runOnUiThread(() -> {
                switch (status) {
                    case CarVoice.HeadRest.SHARE:
                        binding.ivBackground.setImageResource(R.mipmap.ic_sound_headrest_speaker);
                        binding.spvHeadrest.setSelectedIndex(0, true);
                        break;
                    case CarVoice.HeadRest.DRIVING:
                        binding.ivBackground.setImageResource(R.mipmap.ic_sound_headrest_speaker_jx);
                        binding.spvHeadrest.setSelectedIndex(1, true);
                        break;
                    case CarVoice.HeadRest.PRIVATE:
                        binding.ivBackground.setImageResource(R.mipmap.ic_sound_headrest_speaker_sx);
                        binding.spvHeadrest.setSelectedIndex(2, true);
                        break;
                }
            });
            Prefs.put(PrefsConst.VOICE_HEADREST, status);
        }


        private void updateUI(int headrestType) {
            Log.d(TAG, "updateUI status: " + headrestType);
            switch (headrestType) {
                case CarVoice.HeadRest.SHARE:
                    binding.ivBackground.setImageResource(R.mipmap.ic_sound_headrest_speaker);
                    break;
                case CarVoice.HeadRest.DRIVING:
                    binding.ivBackground.setImageResource(R.mipmap.ic_sound_headrest_speaker_jx);
                    break;
                case CarVoice.HeadRest.PRIVATE:
                    binding.ivBackground.setImageResource(R.mipmap.ic_sound_headrest_speaker_sx);
                    break;
            }
            binding.tvDesc.setText(getDescriptionRes(headrestType));
        }

        private int getDescriptionRes(int headrestType) {
            switch (headrestType) {
                case CarVoice.HeadRest.SHARE:
                    return R.string.str_headrest_gx_desc;
                case CarVoice.HeadRest.DRIVING:
                    return R.string.str_headrest_jx_desc;
                case CarVoice.HeadRest.PRIVATE:
                    return R.string.str_headrest_sx_desc;
                default:
                    return R.string.str_headrest_gx_desc;
            }
        }

        private int getTextRes(int headrestType) {
            switch (headrestType) {
                case VoicePresenter.VoiceConstant.HEADREST_GX:
                    return R.string.str_headrest_gx;
                case VoicePresenter.VoiceConstant.HEADREST_JX:
                    return R.string.str_headrest_jx;
                case VoicePresenter.VoiceConstant.HEADREST_SX:
                    return R.string.str_headrest_sx;
                default:
                    return R.string.str_headrest_gx;
            }
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface ChangedListener {
        void initObserve(DialogAlertSoundHeadrestSpeakerBinding binding);
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;
}
