package com.bitech.vehiclesettings.viewmodel;

import android.util.Log;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.connect.IConnectManagerListener;
import com.bitech.platformlib.manager.CarSettingManager;
import com.bitech.platformlib.manager.ConnectManager;
import com.bitech.platformlib.utils.MsgUtil;
import com.bitech.vehiclesettings.carapi.constants.CarConnect;

public class ConnectViewModel extends ViewModel {
    private static final String TAG = ConnectViewModel.class.getSimpleName();
    // 前排充电开关状态
    public MutableLiveData<Integer> frontChargingLiveData = new MutableLiveData<>(CarConnect.CWC_workingSts.CWC_OFF);
    // 遗忘提醒开关
    public MutableLiveData<Integer> forgetReminderLiveData = new MutableLiveData<>(CarConnect.ICC_CWCPhoneforgottenFunStsSet.PHONE_FORGOTTEN_FUN_OFF);
    // 手机遗忘提醒提示音
    public MutableLiveData<Integer> forgetReminderVoiceLiveData = new MutableLiveData<>(0);
    // 5G开关
    public MutableLiveData<Integer> fiveGLiveData = new MutableLiveData<>(CarConnect.FiveG_get.FIVEG_OFF);
    // 手机充电状态
    public MutableLiveData<Integer> phoneChargingLiveData = new MutableLiveData<>(CarConnect.CWC_ChargingSts.NO_CHARGING);

    ConnectManager connectManager = (ConnectManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_CONNECT_MANAGER);
    CarSettingManager carSettingManager = (CarSettingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_SETTING_MANAGER);

    public void initData() {
        addCallBack();
        // 获取遗忘提醒开关状态
        getForgetReminder();
        // 获取5G网络状态
        getMobileNetworkState();
        // 获取工作状态
        getFrontChargingState();
        // 获取手机充电状态
        getPhoneChargeState();
    }

    private void addCallBack() {
        connectManager.addCallback(TAG, new IConnectManagerListener() {
            /**
             * 手机充电状态
             * @param signalVal
             */
            @Override
            public void onPhoneChargeChanged(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                phoneChargingLiveData.postValue(signalVal);
            }

            /**
             * 手机高温提醒
             * @param bytes
             */
            @Override
            public void onHighTemperatureRemindChanged(int bytes) {
                Log.d(TAG, "onHighTemperatureRemindChanged status: " + bytes);

            }

            /**
             * 手机遗忘提醒
             * @param signalVal
             */
            @Override
            public void onForgetReminderChanged(int signalVal) {
                Log.d(TAG, "onForgetReminderChanged status: " + signalVal);
                if (signalVal == Integer.MIN_VALUE) return;
                forgetReminderLiveData.postValue(signalVal);
            }

            /**
             * 5G网络状态
             * @param signalVal
             */
            @Override
            public void onFiveGChanged(int signalVal) {
                if (signalVal == Integer.MIN_VALUE) return;
                fiveGLiveData.postValue(signalVal);
            }

            /**
             * 前排无线充电开关状态
             * @param signVal
             */
            @Override
            public void onFrontChargingChanged(int signVal) {
                // 前排无线充电状态
                Log.d(TAG, "onWirelessChargeChanged: " + signVal);
                if (signVal == Integer.MIN_VALUE) return;
                frontChargingLiveData.postValue(signVal);
            }

            /**
             * 手机遗忘提醒提示音
             */
            @Override
            public void onPhoneForgetReminderVoice(int signalVal) {
                Log.d(TAG, "onPhoneForgetReminderVoice: " + signalVal);
                if (signalVal == Integer.MIN_VALUE) return;
                forgetReminderVoiceLiveData.postValue(signalVal);
            }
        });
        connectManager.registerListener();
    }

    /**
     * 获取5G网络状态
     * @return
     */
    public void getMobileNetworkState() {
        int signVal = connectManager.getFiveGNetwork();
        fiveGLiveData.postValue(signVal);
    }

    public void setMobileNetworkState(int signalVal) {
        if (signalVal == Integer.MIN_VALUE) return;
        if (fiveGLiveData.getValue() == CarConnect.FiveG_get.FIVEG_OFF && signalVal == CarConnect.FiveG_set.FIVEG_OFF) {
            return;
        }
        if (fiveGLiveData.getValue() == CarConnect.FiveG_get.FIVEG_ON && signalVal == CarConnect.FiveG_set.FIVEG_ON) {
            return;
        }
        connectManager.setFiveGNetwork(signalVal);
    }

    public void getFrontChargingState() {
        int signalVal = connectManager.getWirelessCharge();
        if (signalVal == Integer.MIN_VALUE) {
            int value = frontChargingLiveData.getValue();
            frontChargingLiveData.postValue(value);
        } else {
            frontChargingLiveData.postValue(signalVal);
        }
    }

    /**
     * 设置无线充电状态
     * @param signalVal
     */
    public void setFrontChargingState(int signalVal) {
        // 电源模式:Comfort/ON档  CWC_workingSts
        if (MsgUtil.getInstance().supportPowerMode()) {
            if (signalVal == CarConnect.ICC_CWCWorkingStsSet.OFF && frontChargingLiveData.getValue() == CarConnect.CWC_workingSts.CWC_OFF) {
                return;
            }
            if (signalVal == CarConnect.ICC_CWCWorkingStsSet.ON && frontChargingLiveData.getValue() == CarConnect.CWC_workingSts.CWC_ON) {
                return;
            }
            connectManager.setWirelessCharge(signalVal);
        }
    }

    /**
     * 获取遗忘提醒状态
     */
    public void getForgetReminder() {
        int signVal = connectManager.getPhoneLeaveAlert();
        if (signVal == Integer.MIN_VALUE) {
            int value = forgetReminderLiveData.getValue();
            forgetReminderLiveData.postValue(value);
        }else {
            forgetReminderLiveData.postValue(signVal);
        }
    }

    /**
     * 设置遗忘提醒状态
     * @param signalVal
     */
    public void setForgetReminder(int signalVal) {
        if (!MsgUtil.getInstance().supportPowerMode()) return;
        if (signalVal == CarConnect.ICC_CWCPhoneforgottenFunStsSet.PHONE_FORGOTTEN_FUN_ON && forgetReminderLiveData.getValue() == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.ON) {
            return;
        }
        if (signalVal == CarConnect.ICC_CWCPhoneforgottenFunStsSet.PHONE_FORGOTTEN_FUN_OFF && forgetReminderLiveData.getValue() == CarConnect.CWC_Phoneforgotten_ON_OFF_Sts.OFF) {
            return;
        }
        carSettingManager.setPhoneLeaveAlert(signalVal);
    }

    /**
     * 获取手机充电状态
     */
    public void getPhoneChargeState() {
        int signalVal = connectManager.getPhoneChargeStatus();
        if (signalVal == Integer.MIN_VALUE) return;
        phoneChargingLiveData.postValue(signalVal);
    }
}
