package com.bitech.vehiclesettings.fragment;


//import static com.bitech.vehiclesettings.utils.ElasticAnimatioUtil.imageAnimaStart;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;

import androidx.lifecycle.ViewModelProvider;

import com.bitech.base.utils.Util;
import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.quick.IQuickManagerListener;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.bean.TargetDialogInfo;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.carapi.constants.Car3DModel;
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl;
import com.bitech.vehiclesettings.databinding.DialogAlertCDriveAirbagBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertQRearRoateBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertQWiperSensBinding;
import com.bitech.vehiclesettings.databinding.FragmentQuickControlBinding;
import com.bitech.vehiclesettings.presenter.SafeHandler;
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback;
import com.bitech.vehiclesettings.presenter.display.WallpaperPresenter;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenter;
import com.bitech.vehiclesettings.presenter.quick.QuickPresenterListener;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.repository.QuickRepository;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.ElasticAnimationUtil;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.utils.PagUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.utils.ThreeDModelUtil;
import com.bitech.vehiclesettings.view.carsetting.ChildLockUIAlert;
import com.bitech.vehiclesettings.view.carsetting.DriveAirbagConfirmUIAlert;
import com.bitech.vehiclesettings.view.carsetting.DriveAirbagUIAlert;
import com.bitech.vehiclesettings.view.carsetting.LockTipsUIAlert;
import com.bitech.vehiclesettings.view.common.ComfirmUIAlert;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.RearMirrorAdjustUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.RearMirrorUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.RearRoateUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.SteeringWheelUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.ThreeDModelUIAlert;
import com.bitech.vehiclesettings.view.quickcontrol.WiperSensUIAlert;
import com.bitech.vehiclesettings.viewmodel.MainActViewModel;
import com.bitech.vehiclesettings.viewmodel.QuickControlViewModel;
import com.bitech.vehiclesettings.viewmodel.QuickViewModel;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 快捷控制.
 */
public class QuickControlFragment extends BaseFragment<FragmentQuickControlBinding> implements View.OnClickListener, SafeHandlerCallback {
    private static final String TAG = QuickControlFragment.class.getSimpleName();
    private QuickViewModel viewModel;
    private QuickControlViewModel quickControlViewModel;
    QuickPresenterListener quickPresenter;
    private SafeHandler quickHandler;
    private volatile boolean isActive;
    private boolean isDialogShowing = false;

    int carAutotailStatus = 0;
    private RearMirrorUIAlert.Builder rearMirrorUIAlert;
    // 倒车后视镜调节
    private RearMirrorAdjustUIAlert.Builder adjustUIAlert;
    private LockTipsUIAlert.Builder lockTipsUIAlert;
    private ChildLockUIAlert.Builder childLockUIAlert;
    private DriveAirbagUIAlert.Builder driveAirbagUIAlert;
    private RearRoateUIAlert.Builder rearRoateUIAlert;
    private WiperSensUIAlert.Builder wiperSensUIAlert;
    private ThreeDModelUIAlert.Builder threeDModelUIAlert;
    private DetailsUIAlert.Builder detailUIAlert;
    private MainActViewModel mainActViewModel;
    private SteeringWheelUIAlert.Builder steeringWheelUIAlert;
    public static final int QucikControl_CarWindow = 11;
    public static final int RAIN_SCOOP_SENSITIVITY_UI_ALERT = 12;
    public static boolean llToolsIsInvisible;


    private int steeringWheelMode = 0;

    public RearMirrorUIAlert.Builder getRearMirrorUIAlert() {
        return rearMirrorUIAlert;
    }

    public void setRearMirrorUIAlert(RearMirrorUIAlert.Builder rearMirrorUIAlert) {
        this.rearMirrorUIAlert = rearMirrorUIAlert;
    }

    public RearMirrorAdjustUIAlert.Builder getAdjustUIAlert() {
        return adjustUIAlert;
    }

    public void setAdjustUIAlert(RearMirrorAdjustUIAlert.Builder adjustUIAlert) {
        this.adjustUIAlert = adjustUIAlert;
    }

    public LockTipsUIAlert.Builder getLockTipsUIAlert() {
        return lockTipsUIAlert;
    }

    public void setLockTipsUIAlert(LockTipsUIAlert.Builder lockTipsUIAlert) {
        this.lockTipsUIAlert = lockTipsUIAlert;
    }

    public ChildLockUIAlert.Builder getChildLockUIAlert() {
        return childLockUIAlert;
    }

    private MainActivity activity = null;

    private int windowFrontLeftStatus = -1, windowFrontRightStatus = -1, windowRearLeftStatus = -1, windowRearRightStatus = -1;
    private int doorFrontLeftStatus = 0, doorFrontRightStatus = 0, doorRearLeftStatus = 0, doorRearRightStatus = 0;

    private int lockTipsStatus;
    private int driveBagFlag;
    private int isDriveAirBagConfirm;
    private int backRearAdjustStatus;
    // 判断设置安全气囊是否成功
    private boolean switchDriveBagFlag = true;
    private int swWiperSens;
    public int sbHudRoate;
    private boolean[] sunShadeFailure = {
            false, false, false, false, false, false, false
    };
    public static final int REAR_MIRROR_UI_ALERT = 10;
    public static final int REAR_steering_Wheel_ADJUST_UI_ALERT = 13;
    // 车辆下电是否可用
    private boolean isPowerOffWorked = true;

    private static QuickManager quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);

    private void quickControllerRegLightListen() {
        if (quickManager == null) {
            quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);
        }
        quickManager.addCallback(TAG, new IQuickManagerListener() {
            @Override
            public void centerLockCallback(int status) {
                Log.d(TAG, "centerLockCallback: 中控锁" + status);
                if (status == CarQuickControl.GetCentralLockSts.LOCKED || status == CarQuickControl.GetCentralLockSts.SUPERLOCKED) {
                    status = 1;
                } else if (status == CarQuickControl.GetCentralLockSts.UNLOCKED) {
                    status = 0;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getCentralLocking().getValue() != null && viewModel.getCentralLocking().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setCentralLocking(status);
                    }
                }
            }

            @Override
            public void windowLockCallback(int status) {
                Log.d(TAG, "windowLockCallback: 车窗锁" + status);
                if (status == 0) {
                    status = 0x0;
                } else if (status == 1) {
                    // 展开
                    status = 0x1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getWindowLock().getValue() != null && viewModel.getWindowLock().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setWindowLock(status);
                    }
                }
            }

            @Override
            public void approachingUnlockCallback(int status) {
                if (viewModel.getApproachingUnlock().getValue() != null && viewModel.getApproachingUnlock().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setApproachingUnlock(status);
                    }
                }
            }

            @Override
            public void departureLockingCallback(int status) {
                if (viewModel.getDepartureLocking().getValue() != null && viewModel.getDepartureLocking().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setDepartureLocking(status);
                    }
                }

            }

            @Override
            public void rearTailGateCallback(int status) {
                Log.d(TAG, "rearTailGateCallback: 后尾门:" + status);
                if (status == 0 || status == 1) {
                    status = 1;
                } else if (status == 2) {
                    status = 0;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getRearTailgate().getValue() != null && viewModel.getRearTailgate().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setRearTailgate(status);
                    }

                }
            }

            @Override
            public void rearMirrorCallback(int status) {
                Log.d(TAG, "rearMirrorCallback: 后视镜折叠:" + status);
                if (status == 1) {
                    status = 1;
                } else if (status == 2) {
                    status = 0;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getRearMirrorFold().getValue() != null && viewModel.getRearMirrorFold().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setRearMirrorFold(status);
                    }
                }
            }

            @Override
            public void lockAutoRaiseWindowCallback(int status) {
                Log.d(TAG, "lockAutoRaiseWindowCallback 锁车自动升窗: " + status);
                if (status == CarQuickControl.GetLockAutoRaiseWindowSts.DISABLE) {
                    status = CarQuickControl.ButtonSts.OFF;
                } else if (status == CarQuickControl.GetLockAutoRaiseWindowSts.ENABLE) {
                    status = CarQuickControl.ButtonSts.ON;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getLockAutoRaiseWindow().getValue() != null && viewModel.getLockAutoRaiseWindow().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setLockAutoRaiseWindow(status);
                    }
                }
            }

            @Override
            public void automaticLockingCallback(int status) {
                Log.d(TAG, "automaticLockingCallback: 自动落锁: " + status);
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getAutomaticLocking().getValue() != null && viewModel.getAutomaticLocking().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setAutomaticLocking(status);
                    }
                }
            }

            @Override
            public void automaticParkingUnlockCallback(int status) {
                Log.d(TAG, "automaticParkingUnlockCallback: 驻车自动解锁: " + status);
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getAutomaticParkingUnlock().getValue() != null && viewModel.getAutomaticParkingUnlock().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setAutomaticParkingUnlock(status);
                    }
                }
            }

            @Override
            public void seatPortableCallback(int status) {
                Log.d(TAG, "seatPortableCallback: 座椅便携进入: " + status);
                if (status == 0x1) {
                    status = 1;
                } else if (status == 0x2) {
                    status = 0;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getSeatPortable().getValue() != null && viewModel.getSeatPortable().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setSeatPortable(status);
                    }
                }
            }

            @Override
            public void refuelSmallDoorCallback(int status) {
                Log.d(TAG, "refuelSmallDoorCallback: 加油小门: " + status);
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getRefuelSmallDoor().getValue() != null && viewModel.getRefuelSmallDoor().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setRefuelSmallDoor(status);
                    }
                }
            }

//            @Override
//            public void leftChildLockCallback(int status) {
//                Log.d(TAG, "leftChildLockCallback: 左前锁: " + status);
//                if (status == 0x0) {
//                    status = 1;
//                } else if (status == 0x1) {
//                    status = 0;
//                } else {
//                    status = Integer.MIN_VALUE;
//                }
//                if (viewModel.getLeftChildLock().getValue() != null && viewModel.getLeftChildLock().getValue() != status) {
//                    if (status != Integer.MIN_VALUE) {
//                        viewModel.setLeftChildLock(status);
//                    }
//                }
//            }
//
//            @Override
//            public void rightChildLockCallback(int status) {
//                Log.d(TAG, "rightChildLockCallback: 右前锁: " + status);
//                if (status == 0x0) {
//                    status = 1;
//                } else if (status == 0x1) {
//                    status = 0;
//                } else {
//                    status = Integer.MIN_VALUE;
//                }
//                if (viewModel.getRightChildLock().getValue() != null && viewModel.getRightChildLock().getValue() != status) {
//                    if (status != Integer.MIN_VALUE) {
//                        viewModel.setRightChildLock(status);
//                    }
//                }
//            }

            @Override
            public void defenseReminderCallback(int status) {
                Log.d(TAG, "defenseReminderCallback: 设防提醒: " + status);
                if (status == 0x2) {
                    status = 0;
                } else if (status == 0x3) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getDefenseReminder().getValue() != null && viewModel.getDefenseReminder().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        lockTipsStatus = status;
                        viewModel.setDefenseReminder(status);
                    }
                }
            }

            @Override
            public void lockCarSunRoofShadeCloseCallback(int status) {
                Log.d(TAG, "lockCarSunRoofShadeCloseCallback: 锁车收起遮阳帘: " + status);
                if (status == 0x2) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getLockCarSunroofShade().getValue() != null && viewModel.getLockCarSunroofShade().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setLockCarSunroofShade(status);
                    }
                }
            }

            @Override
            public void hudRoateCallback(int status) {
                Log.d(TAG, "hudRoateCallback: 后尾门开启高度: " + status);
                status = status < 50 ? 50 : Math.min(status, 100);
                status = status - 50;
                if (viewModel.getHudRoate().getValue() != null && viewModel.getHudRoate().getValue() != status) {
                    sbHudRoate = status;
                    viewModel.setHudRoate(status);
                }
            }

            @Override
            public void spoilerCtrlFbCallback(int status) {
                Log.d(TAG, "spoilerCtrlFbCallback: 电动尾翼: " + status);
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getAutoTail().getValue() != null && viewModel.getAutoTail().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setAutoTail(status);
                    }
                }
            }

            @Override
            public void autoStsCallback(int status) {
                Log.d(TAG, "autoStsCallback: 后视镜自动折叠: " + status);
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getAutoRearMirrorFold().getValue() != null && viewModel.getAutoRearMirrorFold().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setAutoRearMirrorFold(status);
                    }
                }
            }

            @Override
            public void autoHeatingFbCallback(int status) {
                Log.d(TAG, "autoHeatingFbCallback: 雨天自动加热外后视镜: " + status);
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getAutoHotRearMirror().getValue() != null && viewModel.getAutoHotRearMirror().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setAutoHotRearMirror(status);
                    }
                }
            }

            @Override
            public void backAutoRearMirrorAdjustCallback(int status) {
                Log.d(TAG, "backAutoRearMirrorAdjustCallback: 倒车时后视镜自动调节: " + status);
                // 0 == OFF
                // 1 == Right
                // 2 == Left
                // 3 == Both
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x3) {
                    status = 1;
                } else if (status == 0x2) {
                    status = 2;
                } else if (status == 0x1) {
                    status = 3;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getBackRearAdjust().getValue() != null && viewModel.getBackRearAdjust().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        backRearAdjustStatus = status;
                        viewModel.setBackRearAdjust(status);
                    }
                }
            }

            @Override
            public void windowFrontLeftCallback(int status) {
                Log.d(TAG, "windowFrontLeftCallback: 左前窗: " + status);
                if (status == 0x14) {
                    windowFrontLeftStatus = CarQuickControl.WindowSts.AIR;
                } else if (status == 0x0) {
                    windowFrontLeftStatus = CarQuickControl.WindowSts.CLOSE;
                } else if (status == 0x64) {
                    windowFrontLeftStatus = CarQuickControl.WindowSts.OPEN;
                } else {
                    windowFrontLeftStatus = Integer.MIN_VALUE;
                }

                if (status == 0x0) {
                    ThreeDModelUtil.setFrontLeftWindow(0, null);
                } else if (status <= 0x64) {
                    ThreeDModelUtil.setFrontLeftWindow(1, null);
                }

                if (windowFrontLeftStatus == windowFrontRightStatus && windowFrontRightStatus == windowRearLeftStatus && windowRearLeftStatus == windowRearRightStatus) {
                    if (windowFrontLeftStatus != Integer.MIN_VALUE) {
                        viewModel.setWindowState(windowFrontLeftStatus);
                    }
                } else {
                    viewModel.setWindowState(CarQuickControl.WindowSts.INVALID);
                }
            }

            @Override
            public void vehicleLeftFrontDoorCallback(int status) {
                Log.d(TAG, "vehicleLeftFrontDoorCallback: 左前门: " + status);
                if (status == CarQuickControl.DoorStatus.CLOSE) {
                    doorFrontLeftStatus = 0;
                    ThreeDModelUtil.setFrontLeftDoor(0, null);
                } else if (status == CarQuickControl.DoorStatus.OPEN_1 || status == CarQuickControl.DoorStatus.OPEN_2) {
                    doorFrontLeftStatus = 1;
                    ThreeDModelUtil.setFrontLeftDoor(1, null);
                }
            }

            @Override
            public void windowFrontRightCallback(int status) {
                Log.d(TAG, "windowFrontRightCallback: 右前窗: " + status);
                if (status == 0x14) {
                    windowFrontRightStatus = CarQuickControl.WindowSts.AIR;
                } else if (status == 0x0) {
                    windowFrontRightStatus = CarQuickControl.WindowSts.CLOSE;
                } else if (status == 0x64) {
                    windowFrontRightStatus = CarQuickControl.WindowSts.OPEN;
                } else {
                    windowFrontRightStatus = Integer.MIN_VALUE;
                }

                if (status == 0x0) {
                    ThreeDModelUtil.setFrontRightWindow(0, null);
                } else if (status <= 0x64) {
                    ThreeDModelUtil.setFrontRightWindow(1, null);
                }

                if (windowFrontLeftStatus == windowFrontRightStatus && windowFrontRightStatus == windowRearLeftStatus && windowRearLeftStatus == windowRearRightStatus) {
                    if (windowFrontLeftStatus != Integer.MIN_VALUE) {
                        viewModel.setWindowState(windowFrontLeftStatus);
                    }
                } else {
                    viewModel.setWindowState(CarQuickControl.WindowSts.INVALID);
                }
            }

            @Override
            public void vehicleRightFrontDoorCallback(int status) {
                Log.d(TAG, "vehicleRightFrontDoorCallback: 右前门: " + status);
                if (status == CarQuickControl.DoorStatus.CLOSE) {
                    doorFrontRightStatus = 0;
                    ThreeDModelUtil.setFrontRightDoor(0, null);
                } else if (status == CarQuickControl.DoorStatus.OPEN_1 || status == CarQuickControl.DoorStatus.OPEN_2) {
                    doorFrontRightStatus = 1;
                    ThreeDModelUtil.setFrontRightDoor(1, null);
                }
            }

            @Override
            public void windowRearLeftCallback(int status) {
                Log.d(TAG, "windowRearLeftCallback: 左后窗: " + status);
                if (status == 0x14) {
                    windowRearLeftStatus = CarQuickControl.WindowSts.AIR;
                } else if (status == 0x0) {
                    windowRearLeftStatus = CarQuickControl.WindowSts.CLOSE;
                } else if (status == 0x64) {
                    windowRearLeftStatus = CarQuickControl.WindowSts.OPEN;
                } else {
                    windowRearLeftStatus = Integer.MIN_VALUE;
                }

                if (status == 0x0) {
                    ThreeDModelUtil.setRearLeftWindow(0, null);
                } else if (status <= 0x64) {
                    ThreeDModelUtil.setRearLeftWindow(1, null);
                }

                if (windowFrontLeftStatus == windowFrontRightStatus && windowFrontRightStatus == windowRearLeftStatus && windowRearLeftStatus == windowRearRightStatus) {
                    if (windowFrontLeftStatus != Integer.MIN_VALUE) {
                        viewModel.setWindowState(windowFrontLeftStatus);
                    }
                } else {
                    viewModel.setWindowState(CarQuickControl.WindowSts.INVALID);
                }
            }

            @Override
            public void vehicleLeftRearDoorCallback(int status) {
                Log.d(TAG, "vehicleLeftRearDoorCallback: 左后门: " + status);
                if (status == CarQuickControl.DoorStatus.CLOSE) {
                    doorRearLeftStatus = 0;
                    ThreeDModelUtil.setRearLeftDoor(0, null);
                } else if (status == CarQuickControl.DoorStatus.OPEN_1 || status == CarQuickControl.DoorStatus.OPEN_2) {
                    doorRearLeftStatus = 1;
                    ThreeDModelUtil.setRearLeftDoor(1, null);
                }
            }

            @Override
            public void windowRearRightCallback(int status) {
                Log.d(TAG, "windowRearRightCallback: 右后窗: " + status);
                if (status == 0x14) {
                    windowRearRightStatus = CarQuickControl.WindowSts.AIR;
                } else if (status == 0x0) {
                    windowRearRightStatus = CarQuickControl.WindowSts.CLOSE;
                } else if (status == 0x64) {
                    windowRearRightStatus = CarQuickControl.WindowSts.OPEN;
                } else {
                    windowRearRightStatus = Integer.MIN_VALUE;
                }

                if (status == 0x0) {
                    ThreeDModelUtil.setRearRightWindow(0, null);
                } else if (status <= 0x64) {
                    ThreeDModelUtil.setRearRightWindow(1, null);
                }

                if (windowFrontLeftStatus == windowFrontRightStatus && windowFrontRightStatus == windowRearLeftStatus && windowRearLeftStatus == windowRearRightStatus) {
                    if (windowFrontLeftStatus != Integer.MIN_VALUE) {
                        viewModel.setWindowState(windowFrontLeftStatus);
                    }
                } else {
                    viewModel.setWindowState(CarQuickControl.WindowSts.INVALID);
                }
            }

            @Override
            public void vehicleRightRearDoorCallback(int status) {
                Log.d(TAG, "vehicleRightRearDoorCallback: 右后门: " + status);
                if (status == CarQuickControl.DoorStatus.CLOSE) {
                    doorRearRightStatus = 0;
                    ThreeDModelUtil.setRearRightDoor(0, null);
                } else if (status == CarQuickControl.DoorStatus.OPEN_1 || status == CarQuickControl.DoorStatus.OPEN_2) {
                    doorRearRightStatus = 1;
                    ThreeDModelUtil.setRearRightDoor(1, null);
                }
            }

            @Override
            public void rearScreenControlCallback(int status) {
                Log.d(TAG, "rearScreenControlCallback: 后屏控制: " + status);
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getRearScreenControl().getValue() != null && viewModel.getRearScreenControl().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        viewModel.setRearScreenControl(status);
                    }
                }
            }

            @Override
            public void frontShadStsCallback(int status) {
                Log.d(TAG, "frontShadStsCallback: 前排遮阳帘" + status);

            }

            @Override
            public void rearShadStsCallback(int status) {
                Log.d(TAG, "rearShadStsCallback: 后排遮阳帘" + status);

            }

            @Override
            public void mfsSwitchModeCallback(int status) {
                Log.d(TAG, "mfsSwitchModeCallback: +1:方向盘自定义按键" + status);
                int customButtonType = Prefs.get(PrefsConst.C_CUSTOM_BUTTON, PrefsConst.DefaultValue.C_CUSTOM_BUTTON);
                if (customButtonType != CarQuickControl.CustomButtonType.REAR_MIRROR_ADJUST) return;
                if (status == 0x1) {
                    // 弹出
                    status = 1;
                } else if (status == 0x0) {
                    status = 0;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getRearMirror().getValue() != null && viewModel.getRearMirror().getValue() != status) {
                    if (status == 1) {
                        mContext.runOnUiThread(() -> {
                            openRearMirror(1);
                        });
                    }
                }
            }

            @Override
            public void wiperSensitivityCallback(int status) {
                Log.d(TAG, "wiperSensitivityCallback: 雨刷灵敏度: " + status);
                if (status == 0x1) {
                    status = 0;
                } else if (status == 0x2) {
                    status = 1;
                } else if (status == 0x3) {
                    status = 2;
                } else if (status == 0x4) {
                    status = 3;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getWiperSensitivity().getValue() != null && viewModel.getWiperSensitivity().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        swWiperSens = status;
                        viewModel.setWiperSensitivity(status);
                    }
                }
            }

            @Override
            public void passengerAirbagCallback(int status) {
                Log.d(TAG, "passengerAirbagCallback: 副驾安全气囊: " + status);
                if (status == 0x0) {
                    status = 0;
                } else if (status == 0x1) {
                    status = 1;
                } else {
                    status = Integer.MIN_VALUE;
                }
                if (viewModel.getDriveAirBag().getValue() != null && viewModel.getDriveAirBag().getValue() != status) {
                    if (status != Integer.MIN_VALUE) {
                        driveBagFlag = status;
                        // 不显示二级弹窗
                        driveAirbagUIAlert.setSwitchDriveBagFlag(false);
                        viewModel.setDriveAirBag(status);
                    }
                }
            }

            @Override
            public void gearCallback(int status) {
                Log.d(TAG, "gearCallback: 车辆档位" + status);
                // 0x0:Init
                // 0x1:P
                // 0x2:R
                // 0x3:N
                // 0x4:D
                // 0x5:Reserved
                // 0x6:Reserved
                // 0x7:Reserved
                updatePowerOffUI(status);
            }

            @Override
            public void sunshadeNormalizedStatusCallback(int status) {
                sunShadeFailure[0] = status == CarQuickControl.SunShadeNormalizedSts.DE_INITIALIZED;
                isSunShadeAvailable();
            }

            @Override
            public void sunshadeTeachRunStatusCallback(int status) {
                sunShadeFailure[1] = status == CarQuickControl.SunShadeTeachRunSts.NO_TEACH_RUN;
                isSunShadeAvailable();
            }

            @Override
            public void sunshadeHallSensorStatusCallback(int status) {
                sunShadeFailure[2] = status == CarQuickControl.SunShadeHallSensorSts.FAIL;
                isSunShadeAvailable();
            }

            @Override
            public void sunshadeAntiPinchStatusCallback(int status) {
                sunShadeFailure[3] = status == CarQuickControl.SunShadeAntiPinchSts.ACTIVE;
                isSunShadeAvailable();
            }

            @Override
            public void sunshadeTpProtectionStatusCallback(int status) {
                sunShadeFailure[4] = status == CarQuickControl.SunShadeThermalProtectionSts.ACTIVE;
                isSunShadeAvailable();
            }

            @Override
            public void sunshadeOverVolStatusCallback(int status) {
                sunShadeFailure[5] = status == CarQuickControl.SunShadeOverVoltageSts.OVER_VOLTAGE;
                isSunShadeAvailable();
            }

            @Override
            public void sunshadeUnderVolStatusCallback(int status) {
                sunShadeFailure[6] = status == CarQuickControl.SunShadeUnderVoltageSts.UNDER_VOLTAGE;
                isSunShadeAvailable();
            }

            @Override
            public void vehicleHoodCallback(int status) {
                if (status == CarQuickControl.VehicleHoodSts.CLOSE) {
                    ThreeDModelUtil.setHoodState(Car3DModel.CustomState.CLOSE, null);
                } else if (status == CarQuickControl.VehicleHoodSts.OPEN_1 || status == CarQuickControl.VehicleHoodSts.OPEN_2) {
                    ThreeDModelUtil.setHoodState(Car3DModel.CustomState.OPEN, null);
                }
            }
        });
        quickManager.registerListener();
    }

    private void updatePowerOffUI(int status) {
        if (status == CarQuickControl.VehicleGearSts.P || status == CarQuickControl.VehicleGearSts.N) {
            mContext.runOnUiThread(() -> {
                GrayEffectUtils.removeGrayEffect(binding.rlVehiclePowerOff);
                isPowerOffWorked = true;
            });
        } else {
            mContext.runOnUiThread(() -> {
                GrayEffectUtils.applyGrayEffect(binding.rlVehiclePowerOff);
                isPowerOffWorked = false;
                binding.rlVehiclePowerOff.setEnabled(true);
            });
        }
    }

    private void isSunShadeAvailable() {

    }


    public void setQuickPresenter(QuickPresenterListener quickPresenter) {
        this.quickPresenter = quickPresenter;
    }

    @Override
    protected FragmentQuickControlBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentQuickControlBinding.inflate(getLayoutInflater());
        return binding;
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isActive = true;
        setQuickPresenter(new QuickPresenter<QuickRepository>(getContext()));
        quickHandler = new SafeHandler(this);
        // 初始化3D车模
        if (WallpaperPresenter.getWallpaperState() != WallpaperPresenter.WALLPAPER_STATE_3D) {
            WallpaperPresenter.switch3DWithoutMemory();
        }
        ThreeDModelUtil.set3DWallpaper(Car3DModel.WallpaperState.MODEL, null);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = getLayoutResId(inflater, container).getRoot();

        activity = (MainActivity) getActivity();
        initObserver();
        // 注册监听事件
        quickControllerRegLightListen();
        // 滚动事件监听
        scrollListener();
        // 初始化车模
        init3DModel();
        return rootView;
    }

    private void init3DModel() {
        int flWindow = quickPresenter.getFLWindow();
        Log.d(TAG, "flWindow: " + flWindow);
        if (flWindow == 0x0) {
            ThreeDModelUtil.setFrontLeftWindow(0, null);
        } else if (flWindow <= 0x64) {
            ThreeDModelUtil.setFrontLeftWindow(1, null);
        }

        int frWindow = quickPresenter.getFRWindow();
        Log.d(TAG, "frWindow: " + frWindow);
        if (frWindow == 0x0) {
            ThreeDModelUtil.setFrontRightWindow(0, null);
        } else if (frWindow <= 0x64) {
            ThreeDModelUtil.setFrontRightWindow(1, null);
        }

        int rlWindow = quickPresenter.getRLWindow();
        Log.d(TAG, "rlWindow: " + rlWindow);
        if (rlWindow == 0x0) {
            ThreeDModelUtil.setRearLeftWindow(0, null);
        } else if (rlWindow <= 0x64) {
            ThreeDModelUtil.setRearLeftWindow(1, null);
        }

        int rrWindow = quickPresenter.getRRWindow();
        Log.d(TAG, "rrWindow: " + rrWindow);
        if (rrWindow == 0x0) {
            ThreeDModelUtil.setRearRightWindow(0, null);
        } else if (rrWindow <= 0x64) {
            ThreeDModelUtil.setRearRightWindow(1, null);
        }

        int flDoor = quickPresenter.getFLDoor();
        Log.d(TAG, "flDoor: " + flDoor);
        if (flDoor == CarQuickControl.DoorStatus.CLOSE) {
            ThreeDModelUtil.setFrontLeftDoor(0, null);
        } else if (flDoor == CarQuickControl.DoorStatus.OPEN_1 || flDoor == CarQuickControl.DoorStatus.OPEN_2) {
            ThreeDModelUtil.setFrontLeftDoor(1, null);
        }

        int frDoor = quickPresenter.getFRDoor();
        Log.d(TAG, "frDoor: " + frDoor);
        if (frDoor == CarQuickControl.DoorStatus.CLOSE) {
            ThreeDModelUtil.setFrontRightDoor(0, null);
        } else if (frDoor == CarQuickControl.DoorStatus.OPEN_1 || frDoor == CarQuickControl.DoorStatus.OPEN_2) {
            ThreeDModelUtil.setFrontRightDoor(1, null);
        }

        int rlDoor = quickPresenter.getRLDoor();
        Log.d(TAG, "rlDoor: " + rlDoor);
        if (rlDoor == CarQuickControl.DoorStatus.CLOSE) {
            ThreeDModelUtil.setRearLeftDoor(0, null);
        } else if (rlDoor == CarQuickControl.DoorStatus.OPEN_1 || rlDoor == CarQuickControl.DoorStatus.OPEN_2) {
            ThreeDModelUtil.setRearLeftDoor(1, null);
        }

        int rrDoor = quickPresenter.getRRDoor();
        Log.d(TAG, "rrDoor: " + rrDoor);
        if (rrDoor == CarQuickControl.DoorStatus.CLOSE) {
            ThreeDModelUtil.setRearRightDoor(0, null);
        } else if (rrDoor == CarQuickControl.DoorStatus.OPEN_1 || rrDoor == CarQuickControl.DoorStatus.OPEN_2) {
            ThreeDModelUtil.setRearRightDoor(1, null);
        }
    }

    /**
     * 滚动监听
     */
    private void scrollListener() {
        binding.scrollView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            Log.d(TAG, "onScroll: " + scrollY);
            Prefs.put(PrefsConst.SCROLL_Y, scrollY);
            MainActivity activity = (MainActivity) getActivity();
            if (activity != null) {
                activity.getBinding().ivModel.handleScroll(scrollY);
            }
        });
    }

    @Override
    protected void initObserve() {
        initObserver();
    }

    private void initObserver() {
        mainActViewModel = new ViewModelProvider(requireActivity()).get(MainActViewModel.class);
        processTargetDialogEvent(mainActViewModel.getTargetDialogLiveEvent().getValue());
        mainActViewModel.getTargetDialogLiveEvent().observe(getViewLifecycleOwner(), this::processTargetDialogEvent);
        viewModel = new ViewModelProvider(this).get(QuickViewModel.class);
        viewModel.quickPresenter = quickPresenter;
        viewModel.getCentralLocking().observe(getViewLifecycleOwner(), status -> {
            // 更新中控锁
            updateCentralLockingUI(status);
        });
        viewModel.getRearTailgate().observe(getViewLifecycleOwner(), status -> {
            // 更新后尾门
            updateRearTailGateUI(status);
        });
        viewModel.getRearMirrorFold().observe(getViewLifecycleOwner(), status -> {
            // 更新后视镜折叠
            updateRearMirrorUI(status);
        });
        viewModel.getWindowDesc().observe(getViewLifecycleOwner(), status -> {
            // 车窗
            updateWindowDescUI(status);
        });
        viewModel.getWindowReback().observe(getViewLifecycleOwner(), status -> {
            // 车窗返回
            updateWindowRebackUI(status);
        });
        viewModel.getWindowState().observe(getViewLifecycleOwner(), status -> {
            // 更新车窗状态2
            updateWindowUI(status);
        });
        viewModel.getWindowLock().observe(getViewLifecycleOwner(), status -> {
            // 车窗锁
            updateWindowLockUI(status);
        });
        viewModel.getSunshade().observe(getViewLifecycleOwner(), status -> {
            // 遮阳帘
            updateSunshadeUI(status);
        });
        viewModel.getAutoTail().observe(getViewLifecycleOwner(), status -> {
            // 电动尾翼
            updateAutoTail(status);
        });
        viewModel.getSkyWindow().observe(getViewLifecycleOwner(), status -> {
            // 天窗 1
            updateSkyWindowUI(status);
        });
        viewModel.getApproachingUnlock().observe(getViewLifecycleOwner(), status -> {
            // 感应靠近解锁
            updateApproachingUnlockUI(status);
        });
        viewModel.getDepartureLocking().observe(getViewLifecycleOwner(), status -> {
            // 感应离车解锁
            updateDepartureLockingUI(status);
        });
        viewModel.getLockAutoRaiseWindow().observe(getViewLifecycleOwner(), status -> {
            // 锁车自动升窗
            updateLockAutoRaiseWindowUI(status);
        });
        viewModel.getLockCarSunroofShade().observe(getViewLifecycleOwner(), status -> {
            // 锁车收起遮阳帘
            updateLockCarSunroofShadeUI(status);
        });
        viewModel.getDefenseReminder().observe(getViewLifecycleOwner(), status -> {
            // 设防提示
            updateLockTipsUI();
        });
        viewModel.getLeftChildLock().observe(getViewLifecycleOwner(), status -> {
            // 左儿童锁
            updateLeftChildLockUI(status);
        });
        viewModel.getRightChildLock().observe(getViewLifecycleOwner(), status -> {
            // 右儿童锁
            updateRightChildLockUI(status);
        });
        viewModel.getRearScreenControl().observe(getViewLifecycleOwner(), status -> {
            // 后屏控制锁
            updateRearScreenControlUI(status);
        });
        viewModel.getAutomaticLocking().observe(getViewLifecycleOwner(), status -> {
            // 自动落锁
            updateAutomaticLockingUI(status);
        });
        viewModel.getAutomaticParkingUnlock().observe(getViewLifecycleOwner(), status -> {
            // 驻车自动解锁
            updateAutomaticParkingUnlockUI(status);
        });
        viewModel.getAutoRearMirrorFold().observe(getViewLifecycleOwner(), status -> {
            // 外后视镜自动折叠
            updateAutoArearMirrorFoldUI(status);
        });
        viewModel.getAutoHotRearMirror().observe(getViewLifecycleOwner(), status -> {
            // 雨天自动加热外后视镜
            updateHotMirrorUI(status);
        });
        viewModel.getBackRearAdjust().observe(getViewLifecycleOwner(), status -> {
            // 倒车时后视镜自动调节
            updateBackRearAdjustUI();
        });
        viewModel.getSeatPortable().observe(getViewLifecycleOwner(), status -> {
            // 座椅便携进入退出
            updateSeatPortableUI(status);
        });
        viewModel.getRefuelSmallDoor().observe(getViewLifecycleOwner(), status -> {
            // 加油小门
            updateRefuelSmallDoorUI(status);
            SliceReceiver.notifyChange(ProviderURI.UNLOCK_FUEL_PORT);
        });
        binding.swRefuelSmallDoor.setOnClickListener(v -> {
            if (!binding.swRefuelSmallDoor.isChecked()) {
                binding.swRefuelSmallDoor.setChecked(true);
            } else {
                EToast.showToast(getActivity(), getString(R.string.tip_refuel_small_door_close), 0, false);
            }
        });
        viewModel.getDriveAirBag().observe(getViewLifecycleOwner(), status -> {
            // 副驾安全气囊
            updateDriveAirBagUI();
        });
        viewModel.getWiperSensitivity().observe(getViewLifecycleOwner(), status -> {
            // 更新UI雨刮器
            selWiperSensTranslate();
        });
        viewModel.getHudRoate().observe(getViewLifecycleOwner(), status -> {
            // 更新UI后尾门
            updateHudRoateUI();
        });
        viewModel.getRearMirror().observe(getViewLifecycleOwner(), status -> {
            // 打开后视镜调节
            openRearMirror(status);
        });
        viewModel.getSunshadeFrontOpen().observe(getViewLifecycleOwner(), status -> {
            // 遮阳帘前排打开
            updateSunshadeFrontOpenUI(status);
        });
        viewModel.getSunshadeFrontClose().observe(getViewLifecycleOwner(), status -> {
            // 遮阳帘前排关闭
            updateSunshadeFrontCloseUI(status);
        });
        viewModel.getSunshadeRearOpen().observe(getViewLifecycleOwner(), status -> {
            // 遮阳帘后排打开
            updateSunshadeRearOpenUI(status);
        });
        viewModel.getSunshadeRearClose().observe(getViewLifecycleOwner(), status -> {
            // 遮阳帘后排关闭
            updateSunshadeRearCloseUI(status);
        });
    }

    private void processTargetDialogEvent(TargetDialogInfo targetDialog) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= " + targetDialog);
        if (targetDialog == null) {
            return;
        }
        //具体Tab索引
        if (targetDialog.getTargetTab() == MainActivity.MainTabIndex.QUICK_CONTROL) {
            switch (targetDialog.getTargetDialog()) {
                case REAR_MIRROR_UI_ALERT:
                    if (targetDialog.getOperation() == 1) {
                        openRearMirror(1);
                    } else {
                        rearMirrorUIAlert.dialog.dismiss();
                    }
                    break;
                case QucikControl_CarWindow:
                    //常量-操作
                    if (targetDialog.getOperation() == 1) {
                        binding.llTools.setVisibility(View.INVISIBLE);
                        llToolsIsInvisible = true;
                        binding.llToolsSubWin.setVisibility(View.VISIBLE);
                    } else {
                        binding.llTools.setVisibility(View.VISIBLE);
                        llToolsIsInvisible = false;
                        binding.llToolsSubWin.setVisibility(View.INVISIBLE);
                    }
                    break;
                case RAIN_SCOOP_SENSITIVITY_UI_ALERT:
                    if (targetDialog.getOperation() == 1) {
                        openWiperSens();
                    } else {
                        wiperSensUIAlert.dialog.dismiss();
                    }
                    break;
                case REAR_steering_Wheel_ADJUST_UI_ALERT:
                    if (isDialogShowing) return;
                    if (targetDialog.getOperation() == 1) {
                        isDialogShowing = true;
                        SteeringWheelUIAlert.Builder sWheelUIAlert = new SteeringWheelUIAlert.Builder(mContext);
                        sWheelUIAlert.setMode(quickPresenter.getCustomButton());
                        Dialog dialog = sWheelUIAlert.create();
                        dialog.setOnDismissListener(d -> isDialogShowing = false);
                        dialog.show();
                    } else {
                        steeringWheelUIAlert.dialog.dismiss();
                    }
                    break;
            }
        }
    }

    @Override
    protected void initView() {
        Log.d(TAG, "initView: ");
    }

    public void loadPageAnim(int currentPosition, int position) {
        if (binding == null) return;
        loadPageAnim(binding.scrollView, currentPosition, position);
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    public void onDestroy() {
        isActive = false;
        super.onDestroy();
        if (quickManager != null) {
            quickManager.removeCallback(TAG);
            quickManager = null;
        }
        if (binding != null) {
            binding = null;
        }
        executor.shutdown();
    }


    // 中控锁UI
    private void updateCentralLockingUI(int status) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCenterLockDesc);
        binding.tvCenterLockDesc.setSelected(status == 1);
        binding.flCenterLock.setSelected(status == 1);
        PagUtils.playPagView(mContext, status == 1 ? ((Util.isNight(mContext) ? "night/central_locking_on.pag" : "day/central_locking_on.pag")) : (Util.isNight(mContext) ? "night/central_locking_off.pag" : "day/central_locking_off.pag"), binding.pvCenterLock);
    }

    // 后尾门UI
    private void updateRearTailGateUI(int centralLockingSate) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvTailLockDesc);
        binding.tvTailLockDesc.setSelected(centralLockingSate == 1);
        binding.flTailLock.setSelected(centralLockingSate == 1);
        PagUtils.playPagView(mContext, centralLockingSate == 1 ? ((Util.isNight(mContext) ? "night/rear_tailgate_on.pag" : "day/rear_tailgate_on.pag")) : (Util.isNight(mContext) ? "night/rear_tailgate_off.pag" : "day/rear_tailgate_off.pag"), binding.pvTailLock);
        ThreeDModelUtil.setTrunkLidState(centralLockingSate == 0 ? Car3DModel.CustomState.CLOSE : Car3DModel.CustomState.OPEN, null);
    }

    // 后视镜折叠UI
    private void updateRearMirrorUI(int rearMirrorSate) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvRearMirrorDesc);
        binding.tvRearMirrorDesc.setSelected(rearMirrorSate == 1);
        binding.flRearMirror.setSelected(rearMirrorSate == 1);
        PagUtils.playPagView(mContext, rearMirrorSate == 1 ? ((Util.isNight(mContext) ? "night/folding_rearview_mirror_on.pag" : "day/folding_rearview_mirror_on.pag")) : (Util.isNight(mContext) ? "night/folding_rearview_mirror_off.pag" : "day/folding_rearview_mirror_off.pag"), binding.pvRearMirror);
        ThreeDModelUtil.setRearMirrorState(rearMirrorSate == 0 ? Car3DModel.RearMirrorState.NOT_FOLD : Car3DModel.RearMirrorState.FOLD, null);
    }

    // 车窗UI
    private void updateWindowDescUI(int windowSate) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvWindowDesc);
        binding.tvWindowDesc.setSelected(windowSate == 1);
        binding.flWindowDesc.setSelected(windowSate == 1);
        PagUtils.playPagView(mContext, Util.isNight(mContext) ? "night/window_breathable.pag" : "day/window_breathable.pag", binding.pvWindowDesc);
    }

    // 车窗返回UI
    private void updateWindowRebackUI(int windowSate) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarWindowReback);
        binding.tvCarWindowReback.setSelected(windowSate == 1);
        binding.flCarWindowReback.setSelected(windowSate == 1);
        PagUtils.playPagView(mContext, Util.isNight(mContext) ? "night/window_breathable.pag" : "day/window_breathable.pag", binding.pvCarWindowReback);
    }

    // 遮阳帘前排全开
    private void updateSunshadeFrontOpenUI(int status) {
        if (status == 1) {
            ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarSunshadeFrontOpen);
            binding.tvCarSunshadeFrontOpen.setSelected(true);
            binding.tvCarSunshadeFrontOpen.setText(getString(R.string.str_toggle_click_stop));
        } else {
            binding.tvCarSunshadeFrontOpen.setText(getString(R.string.str_car_sunshade_open));
            binding.tvCarSunshadeFrontOpen.setSelected(false);
        }
    }

    // 遮阳帘前排全关
    private void updateSunshadeFrontCloseUI(int status) {
        if (status == 1) {
            ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarSunshadeFrontClose);
            binding.tvCarSunshadeFrontClose.setSelected(true);
            binding.tvCarSunshadeFrontClose.setText(getString(R.string.str_toggle_click_stop));
        } else {
            binding.tvCarSunshadeFrontClose.setText(getString(R.string.str_car_sunshade_close));
            binding.tvCarSunshadeFrontClose.setSelected(false);
        }
    }

    // 遮阳帘后排全开
    private void updateSunshadeRearOpenUI(int status) {
        if (status == 1) {
            ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarSunshadeRearOpen);
            binding.tvCarSunshadeRearOpen.setSelected(true);
            binding.tvCarSunshadeRearOpen.setText(getString(R.string.str_toggle_click_stop));
        } else {
            binding.tvCarSunshadeRearOpen.setText(getString(R.string.str_car_sunshade_open));
            binding.tvCarSunshadeRearOpen.setSelected(false);
        }
    }

    // 遮阳帘后排全关
    private void updateSunshadeRearCloseUI(int status) {
        if (status == 1) {
            ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarSunshadeRearClose);
            binding.tvCarSunshadeRearClose.setSelected(true);
            binding.tvCarSunshadeRearClose.setText(getString(R.string.str_toggle_click_stop));
        } else {
            binding.tvCarSunshadeRearClose.setText(getString(R.string.str_car_sunshade_close));
            binding.tvCarSunshadeRearClose.setSelected(false);
        }
    }

    // 车窗UI
    private void updateWindowUI(int status) {
        switch (status) {
            case CarQuickControl.WindowSts.CLOSE:
                updateWindowRaise(true);
                updateWindowBreathable(false);
                updateWindowLower(false);
                GrayEffectUtils.applyGrayEffect(binding.tvCarWindowRaise);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowBreathable);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowLower);
                ThreeDModelUtil.setAllWindowState(Car3DModel.WindowState.CLOSE, null);
                break;
            case CarQuickControl.WindowSts.AIR:
                updateWindowRaise(false);
                updateWindowBreathable(true);
                updateWindowLower(false);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowRaise);
                GrayEffectUtils.applyGrayEffect(binding.tvCarWindowBreathable);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowLower);
                ThreeDModelUtil.setAllWindowState(Car3DModel.WindowState.BREATHABLE, null);
                break;
            case CarQuickControl.WindowSts.OPEN:
                updateWindowRaise(false);
                updateWindowBreathable(false);
                updateWindowLower(true);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowRaise);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowBreathable);
                GrayEffectUtils.applyGrayEffect(binding.tvCarWindowLower);
                ThreeDModelUtil.setAllWindowState(Car3DModel.WindowState.OPEN, null);
                break;
            case CarQuickControl.WindowSts.INVALID:
                updateWindowRaise(false);
                updateWindowBreathable(false);
                updateWindowLower(false);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowRaise);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowBreathable);
                GrayEffectUtils.removeGrayEffect(binding.tvCarWindowLower);
                break;
        }
    }

    private void initWindowUI() {
        PagUtils.playPagView(mContext, Util.isNight(mContext) ? "night/window_close.pag" : "day/window_close.pag", binding.pvCarWindowRaise);
        PagUtils.playPagView(mContext, Util.isNight(mContext) ? "night/window_breathable.pag" : "day/window_breathable.pag", binding.pvCarWindowBreathable);
        PagUtils.playPagView(mContext, Util.isNight(mContext) ? "night/window_open.pag" : "day/window_open.pag", binding.pvCarWindowLower);
    }

    private void updateWindowRaise(boolean flag) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarWindowRaise);
        binding.flCarWindowRaise.setSelected(flag);
        binding.flCarWindowRaise.setSelected(flag);
        if (flag) {
            PagUtils.playPagView(mContext, Util.isNight(mContext) ? "night/window_close.pag" : "day/window_close.pag", binding.pvCarWindowRaise);
        }
    }

    private void updateWindowBreathable(boolean flag) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarWindowBreathable);
        binding.flCarWindowBreathable.setSelected(flag);
        binding.flCarWindowBreathable.setSelected(flag);
        if (flag) {
            PagUtils.playPagView(mContext, Util.isNight(mContext) ? "night/window_breathable.pag" : "day/window_breathable.pag", binding.pvCarWindowBreathable);
        }
    }

    private void updateWindowLower(boolean flag) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarWindowLower);
        binding.flCarWindowLower.setSelected(flag);
        binding.flCarWindowLower.setSelected(flag);
        if (flag) {
            PagUtils.playPagView(mContext, Util.isNight(mContext) ? "night/window_open.pag" : "day/window_open.pag", binding.pvCarWindowLower);
        }
    }

    // 车窗锁UI
    private void updateWindowLockUI(int windowLockSate) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvWindowLockDesc);
        binding.tvWindowLockDesc.setSelected(windowLockSate == 1);
        binding.flWindowLock.setSelected(windowLockSate == 1);
        PagUtils.playPagView(mContext, windowLockSate == 1 ? ((Util.isNight(mContext) ? "night/window_lock_on.pag" : "day/window_lock_on.pag")) : (Util.isNight(mContext) ? "night/window_lock_off.pag" : "day/window_lock_off.pag"), binding.pvWindowLock);
    }

    // 遮阳帘UI
    private void updateSunshadeUI(int status) {
        switch (status) {
            case 0:
                ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarSunshadeFront);
                break;
            case 1:
                ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarSunshadeRear);
                break;
            default:
                break;
        }
        binding.tvCarSunshadeFront.setSelected(0 == status);
        binding.tvCarSunshadeRear.setSelected(1 == status);
    }

    // 电动尾翼UI
    private void updateAutoTail(int status) {
        switch (status) {
            case 0:
                ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarAutotailClose);
                break;
            case 1:
                ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarAutotail1);
                break;
            case 2:
                ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarAutotail2);
                break;
            case 3:
                ElasticAnimationUtil.applyElasticTouchEffect(binding.tvCarAutotailAuto);
                break;
            default:
                break;
        }
        binding.tvCarAutotailClose.setSelected(0 == status);
        binding.tvCarAutotail1.setSelected(1 == status);
        binding.tvCarAutotail2.setSelected(2 == status);
        binding.tvCarAutotailAuto.setSelected(3 == status);
    }

    // 天窗UI
    private void updateSkyWindowUI(int status) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding.tvSkylightLockDesc);
        binding.tvSkylightLockDesc.setSelected(status == 1);
    }

    // 感应靠近解锁UI QUICK_APPROACHING_UNLOCK
    private void updateApproachingUnlockUI(int status) {
        binding.swCloseUnlock.setChecked(CommonUtils.IntToBool(status));
    }

    // 感应离车上锁UI
    private void updateDepartureLockingUI(int status) {
        binding.swConditionTail.setChecked(CommonUtils.IntToBool(status));
    }

    // 锁车自动升窗UI
    private void updateLockAutoRaiseWindowUI(Integer status) {
        binding.swAutoWindow.setChecked(CommonUtils.IntToBool(status));
    }

    // 锁车收起遮阳帘UI
    private void updateLockCarSunroofShadeUI(int status) {
        binding.swLockcarSunroofShade.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 设防提示
     */
    private void updateLockTipsUI() {
        if (lockTipsUIAlert != null) {
            Log.d(TAG, "updateLockTipsUI设防提示窗口存在: ");
            lockTipsUIAlert.updateLockTipsUI(lockTipsStatus);
        }
    }

    /**
     * 儿童锁-左
     *
     * @param status
     */

    private void updateLeftChildLockUI(Integer status) {
        if (childLockUIAlert != null) {
            childLockUIAlert.updateLeftChildLockUI(status);
        }
    }

    /**
     * 儿童锁-右
     *
     * @param status
     */
    private void updateRightChildLockUI(Integer status) {
        if (childLockUIAlert != null) {
            childLockUIAlert.updateRightChildLockUI(status);
        }
    }

    private void updateRearScreenControlUI(int status) {
        if (childLockUIAlert != null) {
            childLockUIAlert.updateRearScreenControlUI(status);
        }
    }

    /**
     * 自动落锁UI
     *
     * @param status
     */
    private void updateAutomaticLockingUI(Integer status) {
        binding.swAutoLock.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 驻车自动解锁
     *
     * @param status
     */
    private void updateAutomaticParkingUnlockUI(Integer status) {
        binding.swAutoUnlock.setChecked(CommonUtils.IntToBool(status));
    }

    // 后视镜自动折叠UI
    private void updateAutoArearMirrorFoldUI(int status) {
        if (rearMirrorUIAlert != null) {
            rearMirrorUIAlert.updateAutoArearMirrorFoldUI(status);
        }
    }

    // 雨天自动加热外后视镜UI
    private void updateHotMirrorUI(int status) {
        if (rearMirrorUIAlert != null) {
            rearMirrorUIAlert.updateHotMirrorUI(status);
        }
    }

    // 倒车时后视镜自动调节
    private void updateBackRearAdjustUI() {
        if (rearMirrorUIAlert != null) {
            rearMirrorUIAlert.updateBackRearAdjustUI(backRearAdjustStatus);
        }
    }

    // 座椅便携进入退出
    private void updateSeatPortableUI(int status) {
        binding.swConditionSeatWriting.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 加油小门
     */
    private void updateRefuelSmallDoorUI(int status) {
        binding.swRefuelSmallDoor.setChecked(CommonUtils.IntToBool(status));
    }

    /**
     * 副驾安全气囊
     */
    private void updateDriveAirBagUI() {
        if (driveAirbagUIAlert != null) {
            driveAirbagUIAlert.updateDriveAirBagUI(driveBagFlag);
        }
    }

    /**
     * 雨刮灵敏度
     */
    private void selWiperSensTranslate() {
        if (wiperSensUIAlert != null) {
            wiperSensUIAlert.selWiperSensTranslate(swWiperSens);
        }
    }

    /**
     * 后尾门开启高度
     */
    private void updateHudRoateUI() {
        if (rearRoateUIAlert != null) {
            rearRoateUIAlert.updateHudRoateUI(sbHudRoate);
        }
    }

    @Override
    public boolean isActive() {
        return isActive;
    }

    @Override
    public void handleSafeMessage(Message msg) {
        switch (msg.what) {
            case MessageConst.QUICK_CONTROL_INIT:
                initHandle();
                break;
            case MessageConst.QUICK_CENTRAL_LOCKING:
                centralLockHandle();
                break;
            case MessageConst.QUICK_REAR_TAIL_GAGE:
                rearTailGateHandle();
                break;
            case MessageConst.QUICK_REAR_MIRROR:
                rearMirrorHandle();
                break;
            case MessageConst.QUICK_WINDOW:
                windowHandle();
                break;
            case MessageConst.QUICK_WINDOW_LOCK:
                windowLockHandle();
                break;
            case MessageConst.QUICK_SUNSHADE:
                sunShadeHandle();
                break;
            case MessageConst.QUICK_SUNSHADE_FRONT_OPEN:
                sunShadeFrontOpenHandle();
                break;
            case MessageConst.QUICK_SUNSHADE_FRONT_CLOSE:
                sunShadeFrontCloseHandle();
                break;
            case MessageConst.QUICK_SUNSHADE_REAR_OPEN:
                sunshadeRearOpenHandle();
                break;
            case MessageConst.QUICK_SUNSHADE_REAR_CLOSE:
                sunshadeRearCloseHandle();
                break;
            case MessageConst.QUICK_AUTO_TAIL:
                autoTailHandle();
                break;
            case MessageConst.QUICK_SKY_WINDOW:
                skyWindowHandle();
                break;
            case MessageConst.QUICK_APPROACHING_UNLOCK:
                approachingUnlockHandle();
                break;
            case MessageConst.QUICK_DEPARTURE_LOCKING:
                departureLockingHandle();
                break;
            case MessageConst.QUICK_LOCK_AUTO_RAISE_WINDOW:
                lockAutoRaiseWindowHandle();
                break;
            case MessageConst.QUICK_LOCK_CAR_SUNROOF_SHADE_CLOSE:
                lockCarSunroofShadeHandle();
                break;
            case MessageConst.QUICK_DEFENSE_REMINDER:
                defenseReminderHandle();
                break;
//            case MessageConst.QUICK_LEFT_CHILD_LOCK:
//                leftChildLockHandle();
//                break;
//            case MessageConst.QUICK_RIGHT_CHILD_LOCK:
//                rightChildLockHandle();
//                break;
            case MessageConst.QUICK_AUTOMATIC_LOCKING:
                automaticLockingHandle();
                break;
            case MessageConst.QUICK_AUTOMATIC_PARKING_UNLOCK:
                automaticParkingUnlockHandle();
                break;
            case MessageConst.QUICK_AUTO_REAR_MIRROR_FOLD:
                autoRearMirrorFoldHandle();
                break;
            case MessageConst.QUICK_AUTO_HOT_REAR_MIRROR:
                autoHotRearMirrorHandle();
                break;
//            case MessageConst.QUICK_BACK_AUTO_REAR_MIRROR_ADJUSE:
//                backAutoRearMirrorAdjustHandle();
//                break;
            case MessageConst.QUICK_SEAT_PORTABLE:
                seatPortableHandle();
                break;
            case MessageConst.QUICK_REFUEL_SMALL_DOOR:
                refuelSmallDoorHandle();
                break;
            case MessageConst.QUICK_HUD_ROATE:
                hudRoateHandle();
                break;
            case MessageConst.QUICK_WIPERSENS:
                wiperSensHandle();
                break;
            case MessageConst.QUICK_DRIVE_AIR_BAG:
                driveAirBagHandle();
                break;
            case MessageConst.QUICK_REAR_SCREEN_CONTROL:
                rearScreenControlHandle();
                break;
            default:
                break;
        }
    }

    private void initHandle() {
        centralLockHandle();
        rearTailGateHandle();
        rearMirrorHandle();
        windowHandle();
        windowLockHandle();
        sunShadeHandle();
        autoTailHandle();
        skyWindowHandle();
        approachingUnlockHandle();
        departureLockingHandle();
        lockAutoRaiseWindowHandle();
        lockCarSunroofShadeHandle();
        defenseReminderHandle();
        leftChildLockHandle();
        rightChildLockHandle();
        automaticLockingHandle();
        automaticParkingUnlockHandle();
        autoRearMirrorFoldHandle();
        autoHotRearMirrorHandle();
        backAutoRearMirrorAdjustHandle();
        seatPortableHandle();
        refuelSmallDoorHandle();
        hudRoateHandle();
        wiperSensHandle();
//        driveAirBagHandle();
        rearScreenControlHandle();
    }

    private void rearScreenControlHandle() {
        int rearScreenControl = quickPresenter.getRearScreenControl();
        if (rearScreenControl != viewModel.getRearScreenControl().getValue()) {
            viewModel.setRearScreenControl(rearScreenControl);
        }
    }

    private void driveAirBagHandle() {
        // 副驾安全气囊
        int driveBagStatus = quickPresenter.getDriveAirBag();
        if (driveBagStatus != viewModel.getDriveAirBag().getValue()) {
            switchDriveBagFlag = false;
            driveBagFlag = driveBagStatus;
            viewModel.setDriveAirBag(driveBagStatus);
            EToast.showToast(mContext, "系统设置副驾安全⽓囊失败", 0, false);
        } else {
            switchDriveBagFlag = true;
        }
        if (driveAirbagUIAlert != null) {
            driveAirbagUIAlert.setSwitchDriveBagFlag(switchDriveBagFlag);
        }
    }

    private void wiperSensHandle() {
        // 雨刮器灵敏度
        int wiperSensStatus = quickPresenter.getWiperSens();
        if (wiperSensStatus != viewModel.getWiperSensitivity().getValue()) {
            swWiperSens = wiperSensStatus;
            viewModel.setWiperSensitivity(wiperSensStatus);
        }
    }

    private void hudRoateHandle() {
        // 后尾门高度
        int roateStatus = quickPresenter.getHudRoate();
        Log.d(TAG, "roateStatus:" + roateStatus);
        if (roateStatus != viewModel.getHudRoate().getValue()) {
            sbHudRoate = roateStatus;
            viewModel.setHudRoate(roateStatus);
            if (rearRoateUIAlert != null && rearRoateUIAlert.isShowing()) {
                rearRoateUIAlert.confirmFail();
            }
        } else {
            if (rearRoateUIAlert != null && rearRoateUIAlert.isShowing()) {
                rearRoateUIAlert.confirmSuccess();
            }
        }
    }

    private void refuelSmallDoorHandle() {
        // 加油小门
        int refuelSmallDoorState = quickPresenter.getRefuelSmallDoor();
        Log.d(TAG, "加油小门:" + refuelSmallDoorState);
        if (refuelSmallDoorState != viewModel.getRefuelSmallDoor().getValue()) {
            viewModel.setRefuelSmallDoor(refuelSmallDoorState);
        }
    }

    private void seatPortableHandle() {
        // 座椅便携
        int seatPortableState = quickPresenter.getSeatPortable();
        if (seatPortableState != viewModel.getSeatPortable().getValue()) {
            viewModel.setSeatPortable(seatPortableState);
        }
    }

    private void backAutoRearMirrorAdjustHandle() {
        // 倒车后视镜自动调节
        int backAutoRearMirrorAdjust = quickPresenter.getBackAutoRearMirrorAdjust();
        if (backAutoRearMirrorAdjust != viewModel.getBackRearAdjust().getValue()) {
            backRearAdjustStatus = backAutoRearMirrorAdjust;
            viewModel.setBackRearAdjust(backAutoRearMirrorAdjust);
        }
    }

    private void autoHotRearMirrorHandle() {
        // 外后视镜自动加热
        int hotMirrorState = quickPresenter.getAutoHotRearMirror();
        if (hotMirrorState != viewModel.getAutoHotRearMirror().getValue()) {
            viewModel.setAutoHotRearMirror(hotMirrorState);
        }
    }

    private void autoRearMirrorFoldHandle() {
        // 外后视镜自动折叠
        int arearMirrorFoldState = quickPresenter.getAutoRearMirrorFold();
        if (arearMirrorFoldState != viewModel.getAutoRearMirrorFold().getValue()) {
            viewModel.setAutoRearMirrorFold(arearMirrorFoldState);
        }
    }

    private void automaticParkingUnlockHandle() {
        // 驻车自动解锁
        int pUnlockStatus = quickPresenter.getAutomaticParkingUnlock();
        if (pUnlockStatus != viewModel.getAutomaticParkingUnlock().getValue()) {
            viewModel.setAutomaticParkingUnlock(pUnlockStatus);
        }
    }

    private void automaticLockingHandle() {
        // 自动落锁
        int auLockStatus = quickPresenter.getAutomaticLocking();
        if (auLockStatus != viewModel.getAutomaticLocking().getValue()) {
            viewModel.setAutomaticLocking(auLockStatus);
        }
    }

    private void rightChildLockHandle() {
        // 儿童锁-右
        int rCLockStatus = quickPresenter.getRightChildLock();
        if (rCLockStatus != viewModel.getRightChildLock().getValue()) {
            viewModel.setRightChildLock(rCLockStatus);
        }
    }

    private void leftChildLockHandle() {
        // 儿童锁-左
        int leftCLockStatus = quickPresenter.getLeftChildLock();
        if (leftCLockStatus != viewModel.getLeftChildLock().getValue()) {
            viewModel.setLeftChildLock(leftCLockStatus);
        }
    }

    private void defenseReminderHandle() {
        // 设防提示
        int defenseStatus = quickPresenter.getDefenseReminder();
        if (defenseStatus != viewModel.getDefenseReminder().getValue()) {
            lockTipsStatus = defenseStatus;
            viewModel.setDefenseReminder(defenseStatus);
        }
    }

    private void lockCarSunroofShadeHandle() {
        // 锁车收起遮阳帘
        int lockCarSunroofShadeState = quickPresenter.getLockCarSunRoofShadeClose();
        if (lockCarSunroofShadeState != viewModel.getLockCarSunroofShade().getValue()) {
            viewModel.setLockCarSunroofShade(lockCarSunroofShadeState);
        }
    }

    private void lockAutoRaiseWindowHandle() {
        // 锁车自动升窗
        int raiseStatus = quickPresenter.getLockAutoRaiseWindow();
        if (raiseStatus != viewModel.getLockAutoRaiseWindow().getValue()) {
            viewModel.setLockAutoRaiseWindow(raiseStatus);
        }
    }

    private void departureLockingHandle() {
        // 感应离车上锁
        int departureState = quickPresenter.getDepartureLocking();
        if (departureState != viewModel.getDepartureLocking().getValue()) {
            viewModel.setDepartureLocking(departureState);
        }
    }

    private void approachingUnlockHandle() {
        // 感应靠近解锁
        int approachingUnlockState = quickPresenter.getApproachingUnlock();
        if (approachingUnlockState != viewModel.getApproachingUnlock().getValue()) {
            viewModel.setApproachingUnlock(approachingUnlockState);
        }
    }

    private void skyWindowHandle() {
        // 天窗
        int skyWindowState = quickPresenter.getSkyWindow();
        if (skyWindowState != viewModel.getSkyWindow().getValue()) {
            viewModel.setSkyWindow(skyWindowState);
        }
    }

    private void autoTailHandle() {
        // 电动尾翼
        int autoTailState = quickPresenter.getAutoTail();
        if (autoTailState != viewModel.getAutoTail().getValue()) {
            viewModel.setAutoTail(autoTailState);
        }
    }

    private void sunShadeHandle() {
        // 遮阳帘
        int sunshadeState = quickPresenter.getSunshade();
        if (sunshadeState != viewModel.getSunshade().getValue()) {
            viewModel.setSunshade(sunshadeState);
        }
    }

    private void sunShadeFrontOpenHandle() {
        // 遮阳帘前排开启
        int sunshadeFrontOpenState = quickPresenter.getSunshadeFront();
        if (sunshadeFrontOpenState != viewModel.getSunshadeFrontOpen().getValue()) {
            viewModel.setSunshadeFrontOpen(sunshadeFrontOpenState);
        }
    }

    private void sunShadeFrontCloseHandle() {
        // 遮阳帘前排关闭
        int sunshadeFrontCloseState = quickPresenter.getSunshadeFront();
        if (sunshadeFrontCloseState != viewModel.getSunshadeFrontClose().getValue()) {
            viewModel.setSunshadeFrontClose(sunshadeFrontCloseState);
        }
    }

    private void sunshadeRearOpenHandle() {
        // 遮阳帘后排打开
        int sunshadeRearOpenState = quickPresenter.getSunshadeRear();
        if (sunshadeRearOpenState != viewModel.getSunshadeRearOpen().getValue()) {
            viewModel.setSunshadeRearOpen(sunshadeRearOpenState);
        }
    }

    private void sunshadeRearCloseHandle() {
        // 遮阳帘后排关闭
        int sunshadeRearCloseState = quickPresenter.getSunshadeRear();
        if (sunshadeRearCloseState != viewModel.getSunshadeRearClose().getValue()) {
            viewModel.setSunshadeRearClose(sunshadeRearCloseState);
        }
    }

    private void windowLockHandle() {
        // 车窗锁
        int windowLockState = quickPresenter.getWindowLock();
        if (windowLockState != viewModel.getWindowLock().getValue()) {
            viewModel.setWindowLock(windowLockState);
        }
    }

    private void windowHandle() {
        // 车窗状态
        int windowState = quickPresenter.getWindow();
        if (windowState == CarQuickControl.WindowSts.CLOSE) {
            viewModel.setWindowState(CarQuickControl.WindowSts.CLOSE);
        } else if (windowState == CarQuickControl.WindowSts.OPEN) {
            viewModel.setWindowState(CarQuickControl.WindowSts.OPEN);
        } else if (windowState == CarQuickControl.WindowSts.AIR) {
            viewModel.setWindowState(CarQuickControl.WindowSts.AIR);
        } else {
            viewModel.setWindowState(CarQuickControl.WindowSts.INVALID);
        }
    }

    private void rearMirrorHandle() {
        // 后视镜折叠
        int rearMirrorState = quickPresenter.getRearMirror();
        if (viewModel.getRearMirrorFold().getValue() != rearMirrorState) {
            viewModel.setRearMirrorFold(rearMirrorState);
        }
    }

    private void rearTailGateHandle() {
        // 后尾门
        int rearTailGateState = quickPresenter.getRearTailGate();
        if (viewModel.getRearTailgate().getValue() != rearTailGateState) {
            viewModel.setRearTailgate(rearTailGateState);
        }
    }

    private void centralLockHandle() {
        // 获取中控锁状态
        int centralLockingState = quickPresenter.getCentralLocking();
        if (viewModel.getCentralLocking().getValue() != centralLockingState) {
            viewModel.setCentralLocking(centralLockingState);
        }
    }

    @Override
    protected void setListener() {
        BindingUtil.bindClicks(this, binding.tvCenterLockDesc, binding.tvTailLockDesc, binding.tvRearMirrorDesc, binding.tvWindowDesc, binding.tvWindowLockDesc);
        BindingUtil.bindClicks(this, binding.tvSkylightLockDesc, binding.swConditionRearMirror, binding.rlRearMirror, binding.rlRearMirrorAdjust, binding.llMyGlview);
        BindingUtil.bindClicks(this, binding.tvCarWindowReback, binding.tvCarWindowRebackArrow, binding.tvCarWindowLower);
        BindingUtil.bindClicks(this, binding.tvCarWindowBreathable, binding.tvCarWindowRaise);
        // 遮阳帘
        BindingUtil.bindClicks(this, binding.tvSunshadeDesc, binding.tvCarSunshadeFront, binding.tvCarSunshadeRear, binding.tvCarSunshadeReback, binding.tvCarSunshadeRebackArrow, binding.tvCarSunshadeFrontOpen, binding.tvCarSunshadeFrontClose, binding.tvCarSunshadeRearOpen, binding.tvCarSunshadeRearClose);
        // 电动尾翼
        BindingUtil.bindClicks(this, binding.tvAutotailDesc, binding.tvCarAutotailReback, binding.tvCarAutotailRebackArrow, binding.tvCarAutotailClose, binding.tvCarAutotail1, binding.tvCarAutotail2, binding.tvCarAutotailAuto);
        // 感应靠近解锁
        BindingUtil.bindClicks(this, binding.swAutoLock, binding.swAutoUnlock, binding.swCloseUnlock, binding.swConditionTail, binding.swAutoWindow, binding.swLockcarSunroofShade, binding.swConditionRearMirror, binding.swConditionHeatingRearMirror, binding.swConditionSeatWriting, binding.swRefuelSmallDoor);
        // 方向盘按键自定义
        BindingUtil.bindClicks(this, binding.rlSteeringWhellCustom, binding.rlRearMirrorAdjust, binding.rlVehiclePowerOff);

        BindingUtil.bindClicks(this, binding.rlLockTips, binding.rlChildLock, binding.rlSafe, binding.rlHudRoate, binding.rlWiperSens, binding.ivSafeTips, binding.rl3dModel);

        RearMirrorAdjustUIAlert.setOnProgressChangedListener(status -> {
            Log.d(TAG, "后视镜调节: " + status);
            quickPresenter.setBackAutoRearMirrorAdjust(status);
            quickHandler.sendMessageDelayed(MessageConst.QUICK_BACK_AUTO_REAR_MIRROR_ADJUSE);
        });

        SteeringWheelUIAlert.setOnModeChangedListener(mode -> {
            //todo 自定义方向
            Log.d(TAG, "onMode: " + mode);
            this.steeringWheelMode = mode;
            Prefs.put(PrefsConst.D_STEERING_WHEEL_MODE, mode);
            quickPresenter.setCustomButton(mode);
        });
        ComfirmUIAlert.setOnComfirmListener(function -> {
            Log.d(TAG, "onComfirm: " + function);
            if ("vehicle_power_off".equals(function)) {
                //todo 车辆下电
                quickPresenter.setVehiclePowerOff(1);
            }
        });

        // 设防提示接口
        LockTipsUIAlert.setOnProgressChangedListener(new LockTipsUIAlert.onProgressChangedListener() {
            @Override
            public void onSwitch(int index) {
                lockTipsStatus = index;
                viewModel.setDefenseReminder(index);
                if (quickPresenter.getDefenseReminder() != index) {
                    quickPresenter.setDefenseReminder(index);
                    quickHandler.sendMessageDelayed(MessageConst.QUICK_DEFENSE_REMINDER);
                }
                Log.d(TAG, "设防提示: " + index);
            }

            @Override
            public int getInitStatus() {
                return lockTipsStatus;
            }
        });

        // 后尾门开启高度
        RearRoateUIAlert.setOnProgressChangedListener(new RearRoateUIAlert.onProgressChangedListener() {
            @SuppressLint("SetTextI18n")
            @Override
            public void updateHudRoateUI(int sbHudRoate, DialogAlertQRearRoateBinding binding) {
                binding.isbIvHudRoate.getSeekBar().setProgress(sbHudRoate);
                Log.d(TAG, "后尾门高度进度条sb: " + sbHudRoate);
//                int progress = binding.isbIvHudRoate.getProgress();
//                Log.d(TAG, "后尾门高度进度条sb: " + sbHudRoate);
                binding.ivHudRoateTop.setText((sbHudRoate + 50) + "%");
            }

            @SuppressLint("SetTextI18n")
            @Override
            public void onProgressChanged(DialogAlertQRearRoateBinding binding, SeekBar seekBar, int progress, boolean fromUser) {
                Log.d(TAG, "后尾门高度进度条: " + progress);
                sbHudRoate = progress;
                viewModel.setHudRoate(progress);
            }

            @Override
            public int getInitStatus() {
                Log.d(TAG, "后尾门高度进度条初始化: " + sbHudRoate);
                return quickPresenter.getHudRoate();
            }

            @Override
            public void onConfirm(SeekBar seekBar) {
                // 发送数据
                int progress = seekBar.getProgress();
                quickPresenter.setHudRoate(progress);
                viewModel.setHudRoate(progress);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_HUD_ROATE);
            }
        });
        // 雨刮器灵敏度
        WiperSensUIAlert.setOnProgressChangedListener(new WiperSensUIAlert.onProgressChangedListener() {

            @Override
            public void setSwitch(DialogAlertQWiperSensBinding binding, int index, String text) {
                swWiperSens = index;
                viewModel.setWiperSensitivity(index);
                if (quickPresenter.getWiperSens() != index) {
                    quickPresenter.setWiperSens(index);
                    quickHandler.sendMessageDelayed(MessageConst.QUICK_WIPERSENS);
                }
                Log.d(TAG, "雨刮器灵敏度: " + text);
            }

            @Override
            public int getInitStatus() {
                return swWiperSens;
            }
        });
        // 副驾安全气囊
        DriveAirbagUIAlert.setOnProgressChangedListener(new DriveAirbagUIAlert.onProgressChangedListener() {
            @Override
            public void onSwitch(DialogAlertCDriveAirbagBinding binding) {
                boolean enabled = CommonUtils.IntToBool(quickPresenter.getCarSpeed() <= 120 ? 1 : 0);
                binding.spvDriveAirbag.setEnabled(enabled);
            }

            @Override
            public void switchSuccess(int index) {
                driveBagFlag = index;
                viewModel.setDriveAirBag(index);
                if (quickPresenter.getDriveAirBag() != index) {
                    quickPresenter.setDriveAirBag(index);
                    quickHandler.sendMessageDelayed(MessageConst.QUICK_DRIVE_AIR_BAG);
                }
                Log.d(TAG, "switchSuccess 副驾安全气囊: " + index);
            }

            @Override
            public int getInitStatus() {
                return driveBagFlag;
            }
        });

        DriveAirbagConfirmUIAlert.setOnProgressChangedListener((alert, type) -> {
            Log.d(TAG, "副驾安全气囊confirm: " + type);
            driveBagFlag = type;
        });


        binding.rlSafe.setOnClickListener(v -> {
            // 副驾安全气囊
            Log.d(TAG, "副驾安全气囊set");
            openDriveAirbag();
        });

        binding.ivSafeTips.setOnClickListener(v -> {
            // 副驾安全气囊tips
            Log.d(TAG, "副驾安全气囊tips");
            openTipsDialog(getString(R.string.str_carsetting_safe_1), getString(R.string.str_carsetting_safe_7), 1176, 488);
        });

        binding.rlVehiclePowerOff.setOnClickListener(v -> {
            if (isPowerOffWorked) {
                ComfirmUIAlert.Builder vehiclePowerOffUIAlert = new ComfirmUIAlert.Builder(mContext);
                vehiclePowerOffUIAlert.setTitle(getString(R.string.str_vehicle_power_off));
                vehiclePowerOffUIAlert.setContent(getString(R.string.str_vehicle_power_off_hint));
                vehiclePowerOffUIAlert.setFunction("vehicle_power_off");
                vehiclePowerOffUIAlert.create(1128, 508).show();
            } else {
                EToast.showToast(mContext, mContext.getString(R.string.str_vehicle_power_off_tips), 1000, false);
            }
        });
    }

    private final ExecutorService executor = Executors.newCachedThreadPool();

    @Override
    protected void initData() {
        viewModel.initData();
        this.steeringWheelMode = Prefs.get(PrefsConst.D_STEERING_WHEEL_MODE, 0);
        updatePowerOffUI(quickManager.getDrivingInfoGear());
    }

    private void openLockTips() {
        if (lockTipsUIAlert != null && lockTipsUIAlert.isShowing()) {
            return;
        }
        if (lockTipsUIAlert == null) {
            lockTipsUIAlert = new LockTipsUIAlert.Builder(mContext);
        }
        lockTipsUIAlert.create().show();
    }

    private void openChildLock() {
        if (childLockUIAlert != null && childLockUIAlert.isShowing()) {
            return;
        }
        if (childLockUIAlert == null) {
            childLockUIAlert = new ChildLockUIAlert.Builder(mContext);
        }
        childLockUIAlert.create().show();
    }

    private void openHudRoate() {
        if (rearRoateUIAlert != null && rearRoateUIAlert.isShowing()) {
            return;
        }
        if (rearRoateUIAlert == null) {
            rearRoateUIAlert = new RearRoateUIAlert.Builder(mContext);
        }
        rearRoateUIAlert.create().show();
    }

    private void openWiperSens() {
        if (wiperSensUIAlert != null && wiperSensUIAlert.isShowing()) {
            return;
        }
        if (wiperSensUIAlert == null) {
            wiperSensUIAlert = new WiperSensUIAlert.Builder(mContext);
        }
        wiperSensUIAlert.create().show();
        wiperSensUIAlert.selWiperSensTranslate(swWiperSens);
    }

    private void open3DModel() {
        if (threeDModelUIAlert != null && threeDModelUIAlert.isShowing()) {
            return;
        }
        if (threeDModelUIAlert == null) {
            threeDModelUIAlert = new ThreeDModelUIAlert.Builder(mContext);
        }
        threeDModelUIAlert.create().show();
    }

    private void openDriveAirbag() {
        if (driveAirbagUIAlert != null && driveAirbagUIAlert.isShowing()) {
            return;
        }
        if (driveAirbagUIAlert == null) {
            driveAirbagUIAlert = new DriveAirbagUIAlert.Builder(mContext);
        }
        driveAirbagUIAlert.create().show();
    }

    private void openRearMirror(int status) {
        if (status == 0) return;
        if (rearMirrorUIAlert != null && rearMirrorUIAlert.isShowing()) {
            return;
        }
        if (rearMirrorUIAlert == null) {
            rearMirrorUIAlert = new RearMirrorUIAlert.Builder(mContext);
        }
        rearMirrorUIAlert.create().show();
    }

    // 打开自定义提示窗口
    @SuppressLint("RtlHardcoded")
    private void openTipsDialog(String title, String content, int width, int height) {
        if (detailUIAlert != null && detailUIAlert.isShowing()) {
            return;
        }
        if (detailUIAlert == null) {
            detailUIAlert = new DetailsUIAlert.Builder(mContext);
        }
        detailUIAlert.create(title, content, width, height).show();
        detailUIAlert.setTextSize(com.bitech.base.R.dimen.font_24px);
    }

    /**
     * @param view The view that was clicked.
     */
    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_my_glview:
                break;
            case R.id.tv_center_lock_desc:
                // 设置中控锁
                if (viewModel.getCentralLocking().getValue() == 1) {
                    viewModel.setCentralLocking(0);
                    if (quickPresenter.getCentralLocking() == 1) {
                        quickPresenter.setCentralLocking(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_CENTRAL_LOCKING);
                    }
                } else if (viewModel.getCentralLocking().getValue() == 0) {
                    viewModel.setCentralLocking(1);
                    if (quickPresenter.getCentralLocking() == 0) {
                        quickPresenter.setCentralLocking(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_CENTRAL_LOCKING);
                    }
                }
                break;
            case R.id.tv_tail_lock_desc:
                // 设置车尾门
                if (viewModel.getRearTailgate().getValue() == 1) {
                    viewModel.setRearTailgate(0);
                    if (quickPresenter.getRearTailGate() == 1) {
                        quickPresenter.setRearTailGate(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_REAR_TAIL_GAGE);
                    }
                } else if (viewModel.getRearTailgate().getValue() == 0) {
                    viewModel.setRearTailgate(1);
                    if (quickPresenter.getRearTailGate() == 0) {
                        quickPresenter.setRearTailGate(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_REAR_TAIL_GAGE);
                    }
                }
                break;
            case R.id.tv_rear_mirror_desc:
                // 折叠门
                if (viewModel.getRearMirrorFold().getValue() == 1) {
                    viewModel.setRearMirrorFold(0);
                    if (quickPresenter.getRearMirror() == 1) {
                        quickPresenter.setRearMirror(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_REAR_MIRROR);
                    }
                } else {
                    viewModel.setRearMirrorFold(1);
                    if (quickPresenter.getRearMirror() == 0) {
                        quickPresenter.setRearMirror(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_REAR_MIRROR);
                    }
                }
                break;
            case R.id.sw_close_unlock:
                // 感应靠近解锁
                if (binding.swCloseUnlock.isChecked()) {
                    viewModel.setApproachingUnlock(1);
                    if (quickPresenter.getApproachingUnlock() == 0) {
                        quickPresenter.setApproachingUnlock(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_APPROACHING_UNLOCK);
                    }
                } else {
                    viewModel.setApproachingUnlock(0);
                    if (quickPresenter.getApproachingUnlock() == 1) {
                        quickPresenter.setApproachingUnlock(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_APPROACHING_UNLOCK);
                    }
                }
                break;
            case R.id.sw_condition_tail:
                // 感应离车上锁
                if (viewModel.getDepartureLocking().getValue() == 1) {
                    viewModel.setDepartureLocking(0);
                    if (quickPresenter.getDepartureLocking() == 1) {
                        quickPresenter.setDepartureLocking(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_DEPARTURE_LOCKING);
                    }
                } else {
                    viewModel.setDepartureLocking(1);
                    if (quickPresenter.getDepartureLocking() == 0) {
                        quickPresenter.setDepartureLocking(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_DEPARTURE_LOCKING);
                    }
                }
                break;
            case R.id.sw_auto_window:
                // 自动锁车升窗
                if (viewModel.getLockAutoRaiseWindow().getValue() == 1) {
                    viewModel.setLockAutoRaiseWindow(0);
                    if (quickPresenter.getLockAutoRaiseWindow() == 1) {
                        quickPresenter.setLockAutoRaiseWindow(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_LOCK_AUTO_RAISE_WINDOW);
                    }
                } else {
                    viewModel.setLockAutoRaiseWindow(1);
                    if (quickPresenter.getLockAutoRaiseWindow() == 0) {
                        quickPresenter.setLockAutoRaiseWindow(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_LOCK_AUTO_RAISE_WINDOW);
                    }
                }
                break;
            case R.id.sw_lockcar_sunroof_shade:
                // 锁车收起遮阳帘
                if (viewModel.getLockCarSunroofShade().getValue() == 1) {
                    viewModel.setLockCarSunroofShade(0);
                    if (quickPresenter.getLockCarSunRoofShadeClose() == 1) {
                        quickPresenter.setLockCarSunRoofShadeClose(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_LOCK_CAR_SUNROOF_SHADE_CLOSE);
                    }
                } else {
                    viewModel.setLockCarSunroofShade(1);
                    if (quickPresenter.getLockCarSunRoofShadeClose() == 0) {
                        quickPresenter.setLockCarSunRoofShadeClose(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_LOCK_CAR_SUNROOF_SHADE_CLOSE);
                    }
                }
                break;
            case R.id.rl_lock_tips:
                // 设防提示
                openLockTips();
                break;
            case R.id.rl_child_lock:
                openChildLock();
                break;
            case R.id.sw_auto_lock:
                // 自动落锁
                if (viewModel.getAutomaticLocking().getValue() == 1) {
                    viewModel.setAutomaticLocking(0);
                    if (quickPresenter.getAutomaticLocking() == 1) {
                        quickPresenter.setAutomaticLocking(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTOMATIC_LOCKING);
                    }
                } else {
                    viewModel.setAutomaticLocking(1);
                    if (quickPresenter.getAutomaticLocking() == 0) {
                        quickPresenter.setAutomaticLocking(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTOMATIC_LOCKING);
                    }
                }
                break;
            case R.id.sw_auto_unlock:
                // 驻车自动解锁
                if (viewModel.getAutomaticParkingUnlock().getValue() == 1) {
                    viewModel.setAutomaticParkingUnlock(0);
                    if (quickPresenter.getAutomaticParkingUnlock() == 1) {
                        quickPresenter.setAutomaticParkingUnlock(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTOMATIC_PARKING_UNLOCK);
                    }
                } else {
                    viewModel.setAutomaticParkingUnlock(1);
                    if (quickPresenter.getAutomaticParkingUnlock() == 0) {
                        quickPresenter.setAutomaticParkingUnlock(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTOMATIC_PARKING_UNLOCK);
                    }
                }
                break;
            case R.id.sw_condition_seat_writing:
                // 座椅便携
                if (viewModel.getSeatPortable().getValue() == 1) {
                    viewModel.setSeatPortable(0);
                    if (quickPresenter.getSeatPortable() == 1) {
                        quickPresenter.setSeatPortable(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_SEAT_PORTABLE);
                    }
                } else {
                    viewModel.setSeatPortable(1);
                    if (quickPresenter.getSeatPortable() == 0) {
                        quickPresenter.setSeatPortable(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_SEAT_PORTABLE);
                    }
                }
                break;
            case R.id.sw_refuel_small_door:
                // 加油小门
                Log.d(TAG, "setRefuelSmallDoor: 获取加油小门:" + viewModel.getRefuelSmallDoor().getValue());
                if (viewModel.getRefuelSmallDoor().getValue() == 1) {
                    viewModel.setRefuelSmallDoor(0);
                    if (quickPresenter.getRefuelSmallDoor() == 1) {
                        quickPresenter.setRefuelSmallDoor(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_REFUEL_SMALL_DOOR);
                    }
                } else {
                    viewModel.setRefuelSmallDoor(1);
                    if (quickPresenter.getRefuelSmallDoor() == 0) {
                        quickPresenter.setRefuelSmallDoor(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_REFUEL_SMALL_DOOR);
                    }
                }
                break;
            case R.id.tv_window_desc:
                // 车窗跳转-打开车窗的二级菜单
                if (binding.llToolsSubSunshade.getVisibility() != View.VISIBLE) {
                    binding.llTools.setVisibility(View.INVISIBLE);
                    binding.llToolsSubWin.setVisibility(View.VISIBLE);
                    viewModel.setWindowDesc(1);
                    viewModel.setWindowReback(0);
                    initWindowUI();
                }
                break;
            case R.id.tv_car_window_reback_arrow:
            case R.id.tv_car_window_reback:
                // 车窗跳转-返回
                binding.llTools.setVisibility(View.VISIBLE);
                binding.llToolsSubWin.setVisibility(View.INVISIBLE);
                // 车窗选中效果取消
                viewModel.setWindowDesc(0);
                viewModel.setWindowReback(1);
                break;
            case R.id.tv_sunshade_desc:
                // 遮阳帘跳转-打开车窗的二级菜单
                if (binding.llToolsSubWin.getVisibility() != View.VISIBLE) {
                    binding.llTools.setVisibility(View.INVISIBLE);
                    binding.llToolsSubSunshade.setVisibility(View.VISIBLE);
                    binding.llToolsSubSunshadeAll.setVisibility(View.VISIBLE);
                    binding.llToolsSubSunshadeFront.setVisibility(View.GONE);
                    binding.llToolsSubSunshadeRear.setVisibility(View.GONE);
                }
                break;
            case R.id.tv_car_sunshade_reback:
            case R.id.tv_car_sunshade_reback_arrow:
                // 遮阳帘-返回
                binding.llTools.setVisibility(View.VISIBLE);
                binding.llToolsSubSunshade.setVisibility(View.INVISIBLE);
                break;
            case R.id.tv_car_sunshade_front:
                if (binding.llToolsSubSunshadeRear.getVisibility() != View.VISIBLE) {
                    binding.llToolsSubSunshadeAll.setVisibility(View.GONE);
                    binding.llToolsSubSunshadeFront.setVisibility(View.VISIBLE);
                }
                break;
            case R.id.tv_car_sunshade_rear:
                if (binding.llToolsSubSunshadeFront.getVisibility() != View.VISIBLE) {
                    binding.llToolsSubSunshadeAll.setVisibility(View.GONE);
                    binding.llToolsSubSunshadeRear.setVisibility(View.VISIBLE);
                }
                break;
            case R.id.tv_autotail_desc:
                //电动尾翼
                binding.llTools.setVisibility(View.INVISIBLE);
                binding.llToolsSubAutotail.setVisibility(View.VISIBLE);
                break;
            case R.id.tv_car_autotail_reback:
            case R.id.tv_car_autotail_reback_arrow:
                binding.llTools.setVisibility(View.VISIBLE);
                binding.llToolsSubAutotail.setVisibility(View.INVISIBLE);
                break;
            case R.id.tv_car_window_raise:
                viewModel.setWindowState(0);
                quickPresenter.setWindow(0);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_WINDOW);
                break;
            case R.id.tv_car_window_lower:
                viewModel.setWindowState(1);
                quickPresenter.setWindow(1);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_WINDOW);
                break;
            case R.id.tv_car_window_breathable:
                viewModel.setWindowState(2);
                quickPresenter.setWindow(2);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_WINDOW);
                break;
            case R.id.tv_car_sunshade_front_open:
                // 遮阳帘前排全开
                viewModel.setSunshadeFrontOpen(1);
                quickPresenter.setSunshadeFront(1);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_SUNSHADE_FRONT_OPEN);
                break;
            case R.id.tv_car_sunshade_front_close:
                // 遮阳帘前排全关
                viewModel.setSunshadeFrontClose(1);
                quickPresenter.setSunshadeFront(0);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_SUNSHADE_FRONT_CLOSE);
                break;
            case R.id.tv_car_sunshade_rear_open:
                // 遮阳帘后排全开
                viewModel.setSunshadeRearOpen(1);
                quickPresenter.setSunshadeRear(1);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_SUNSHADE_REAR_OPEN);
                break;
            case R.id.tv_car_sunshade_rear_close:
                // 遮阳帘后排全关
                viewModel.setSunshadeRearClose(1);
                quickPresenter.setSunshadeRear(0);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_SUNSHADE_REAR_CLOSE);
                break;
            case R.id.tv_window_lock_desc:
                // 车窗锁
                if (viewModel.getWindowLock().getValue() == 1) {
                    viewModel.setWindowLock(0);
                    if (quickPresenter.getWindowLock() == 1) {
                        quickPresenter.setWindowLock(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_WINDOW_LOCK);
                    }
                } else {
                    viewModel.setWindowLock(1);
                    if (quickPresenter.getWindowLock() == 0) {
                        quickPresenter.setWindowLock(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_WINDOW_LOCK);
                    }
                }
                break;
            case R.id.tv_car_autotail_close:
                // 电动尾门-关闭
                viewModel.setAutoTail(0);
                quickPresenter.setAutoTail(0);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTO_TAIL);
                break;
            case R.id.tv_car_autotail_1:
                // 电动尾门-1档
                viewModel.setAutoTail(1);
                quickPresenter.setAutoTail(1);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTO_TAIL);
                break;
            case R.id.tv_car_autotail_2:
                // 电动尾门-2档
                viewModel.setAutoTail(2);
                quickPresenter.setAutoTail(2);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTO_TAIL);
                break;
            case R.id.tv_car_autotail_auto:
                // 电动尾门-自动
                viewModel.setAutoTail(3);
                quickPresenter.setAutoTail(3);
                quickHandler.sendMessageDelayed(MessageConst.QUICK_AUTO_TAIL);
                break;
            case R.id.tv_skylight_lock_desc:
                // 天窗
                if (viewModel.getSkyWindow().getValue() == 1) {
                    viewModel.setSkyWindow(0);
                    if (quickPresenter.getSkyWindow() == 1) {
                        quickPresenter.setSkyWindow(0);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_SKY_WINDOW);
                    }
                } else {
                    viewModel.setSkyWindow(1);
                    if (quickPresenter.getSkyWindow() == 0) {
                        quickPresenter.setSkyWindow(1);
                        quickHandler.sendMessageDelayed(MessageConst.QUICK_SKY_WINDOW);
                    }
                }
                break;
            case R.id.rl_rear_mirror:
                // 发送方向盘后视镜调节信号
                backRearAdjustStatus = quickPresenter.getBackAutoRearMirrorAdjust();
                viewModel.setBackRearAdjust(backRearAdjustStatus);
                openRearMirror(1);
                break;
            case R.id.rl_rear_mirror_adjust:
                adjustUIAlert = new RearMirrorAdjustUIAlert.Builder(mContext);
                // backRearAdjustStatus
                adjustUIAlert.setSwRearMirrorAdjust(backRearAdjustStatus);
                adjustUIAlert.create().show();
                break;
            case R.id.rl_steering_whell_custom:
                SteeringWheelUIAlert.Builder sWheelUIAlert = new SteeringWheelUIAlert.Builder(mContext);
                sWheelUIAlert.setMode(quickPresenter.getCustomButton());
                sWheelUIAlert.create().show();
                break;
            case R.id.rl_hud_roate:
                openHudRoate();
                break;
            case R.id.rl_wiper_sens:
                openWiperSens();
                break;
            case R.id.rl_3d_model:
                open3DModel();
            default:
                break;
        }
    }

    public FragmentQuickControlBinding getBinding() {
        return binding;
    }

    @Override
    public void onStop() {
        if (rearMirrorUIAlert != null && rearMirrorUIAlert.isShowing()) {
            rearMirrorUIAlert.dialog.dismiss();
        }
        llToolsIsInvisible = true;
        Log.d(TAG, "onStop");
        super.onStop();
    }

    @Override
    public void onStart() {
        llToolsIsInvisible = false;
        Log.d(TAG, "onStart");
        super.onStart();
    }
}
