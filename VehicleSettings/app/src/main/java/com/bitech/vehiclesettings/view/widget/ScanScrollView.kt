package com.bitech.vehiclesettings.view.widget

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.widget.NestedScrollView
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @Description: 自定义扫描嵌套滚动控件.
 **/
class ScanScrollView(context: Context, attrs: AttributeSet) : NestedScrollView(context, attrs) {

    // 滚动监听对象
    private var smartScrollChangedListener: ISmartScrollChangedListener? = null

    // 滚动状态
    private var canScrollState: Int = -1
    private var fadeTopState: Int = -1
    private var fadeBottomState: Int = -1
    private var curFadeTopState = -1
    private var curFadeBottomState = -1
    private var curScrollState = -1

    // 解决ScrollView与recycle滑动冲突事件
    private var isIntercepted = false

    override fun onScrollChanged(l: Int, t: Int, oldl: Int, oldt: Int) {
        super.onScrollChanged(l, t, oldl, oldt)
        if (scrollY == 0) {
            fadeBottomState = NO_FADE_OUT
            fadeTopState = FADE_OUT
        } else if (scrollY + height - paddingTop - paddingBottom == getChildAt(0).height) {
            fadeBottomState = FADE_OUT
            fadeTopState = NO_FADE_OUT
        } else {
            fadeBottomState = NO_FADE_OUT
            fadeTopState = NO_FADE_OUT
        }
        notifyScrollChangedListeners()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canScrollState = if (canScroll()) {
            CAN_SCROLL
        } else {
            CAN_NO_SCROLL
        }
        if (curScrollState != canScrollState && smartScrollChangedListener != null) {
            smartScrollChangedListener!!.onCanScrolled(canScrollState)
            curScrollState = canScrollState
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        if (isIntercepted) {
            return false
        }
        return super.onInterceptTouchEvent(ev)
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        if (isIntercepted) {
            return false
        }
        return super.onTouchEvent(ev)
    }

    /**
     * 设置是否响应滑动事件.
     *
     * @param isIntercepted
     */
    fun setIntercepted(isIntercepted: Boolean) {
        this.isIntercepted = isIntercepted
    }

    /**
     * 刷新当前滚动改变监听.
     *
     */
    private fun notifyScrollChangedListeners() {
        LogUtil.d(
            TAG,
            "notifyScrollChangedListeners : smartScrollChangedListener = $smartScrollChangedListener"
        )
        if (curFadeTopState != fadeTopState && smartScrollChangedListener != null) {
            smartScrollChangedListener!!.onScrolledToTop(fadeTopState)
            curFadeTopState = fadeTopState
        }
        if (curFadeBottomState != fadeBottomState && smartScrollChangedListener != null) {
            smartScrollChangedListener!!.onScrolledToBottom(fadeBottomState)
            curFadeBottomState = fadeBottomState
        }
    }

    /**
     * 是否能够滚动.
     *
     * @return Boolean
     */
    private fun canScroll(): Boolean {
        val child = getChildAt(0)
        if (child != null) {
            val height = child.height
            return getHeight() < height
        }
        return false
    }

    /**
     * 设置监听.
     *
     * @param scrollChangedListener 监听对象
     */
    fun setScanScrollChangedListener(scrollChangedListener: ISmartScrollChangedListener) {
        smartScrollChangedListener = scrollChangedListener
    }

    /**
     * @ClassName: ISmartScrollChangedListener
     * @Author: chenLin
     * @Date:  2024/1/19 18:45
     * @Description: 滚动条改变监听.
     **/
    interface ISmartScrollChangedListener {
        /**
         * 滚动到底部时.
         *
         * @param fadeBottomState 底部状态
         */
        fun onScrolledToBottom(fadeBottomState: Int)

        /**
         * 滚动到顶部时.
         *
         * @param fadeTopState 顶部状态
         */
        fun onScrolledToTop(fadeTopState: Int)

        /**
         * 可以滚动时.
         *
         * @param canScrollState 可滑动状态
         */
        fun onCanScrolled(canScrollState: Int)
    }

    companion object {
        // 日志标志位
        private const val TAG = "ScanScrollView"

        // 可以滑动标志位
        private const val CAN_SCROLL = 0

        // 不可滑动标志位
        private const val CAN_NO_SCROLL = 1

        // 淡出标志位
        private const val FADE_OUT = 0

        // 非淡出标志位
        private const val NO_FADE_OUT = 1
    }
}
