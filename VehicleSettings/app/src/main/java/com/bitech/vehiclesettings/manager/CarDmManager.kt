package com.bitech.vehiclesettings.manager

import android.annotation.SuppressLint
import android.app.ActivityManager
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.RemoteException
import android.text.TextUtils
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.BtDeviceBean
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.view.dialog.DMConfirmDialog
import com.bitech.vehiclesettings.view.widget.SettingsClickToast
import com.bitech.vehiclesettings.view.widget.SettingsDmBtnToast
import com.bitech.vehiclesettings.view.widget.SettingsDmTipToast
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.chery.aauto.devicelist.AAutoDevice
import com.chery.aauto.devicelist.ISettingBinder
import com.chery.adapter.androidauto.connection.SessionConnectListener
import com.chery.adapter.androidauto.connection.SessionConnectProxy
import com.chery.adapter.androidauto.connection.devicelist.ConnectState
import com.chery.adapter.androidauto.connection.devicelist.ConnectType
import com.chery.adapter.androidauto.connection.devicelist.DeviceListProxy
import com.chery.adapter.carplay.connection.ConnectionControl
import com.chery.adapter.carplay.connection.ConnectionListener
import com.chery.adapter.carplay.connection.ConnectionProxy
import com.chery.adapter.carplay.connection.devicelist.CarplayDevice
import com.chery.adapter.carplay.connection.devicelist.DeviceListener
import com.chery.adapter.carplay.connection.devicelist.DeviceProxy
import com.chery.adapter.common.config.IConstant
import com.chery.adapter.common.connection.bluetooth.BluetoothListener
import com.chery.adapter.common.connection.bluetooth.BluetoothProxy
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.VDEvent
import com.chery.ivi.vdb.event.base.VDKey
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink
import com.chery.ivi.vdb.event.id.phonelink.VDValuePhoneLink
import com.chery.ivi.vdb.event.id.phonelink.bean.VDLinkDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList


/**
 * @ClassName: CarDmManager
 *
 * @Date:  2024/3/14 11:18
 * @Description: 设备管理操作管理类.
 **/
class CarDmManager {
    companion object {
        // 日志标志位
        private const val TAG = "CarDmManager"

        // CarPlay UUID
        private const val DEVICE_CARPLAY_UUID = "2D8D2466-E14D-451C-88BC-7301ABEA291A"

        private const val ACTION_CARPLAY_NOTIFICATION_BROADCAST = "com.chery.carplay.broadcast"
        private const val ACTION_SIG_STATUS_EXTRA = "notification"
        private const val ACTION_OPTION_EXTRA = "option"

        // launcher-allAAP页面Activity
        private const val ALL_APP_ACTIVITY = ".view.activity.AllAppsActivity"

        // dm Cp监听集合
        private var dmCpListenerMap: MutableMap<String, CpDeviceListenerNotify>? = HashMap()

        // dm Aa监听集合
        private var dmAaListenerMap: MutableMap<String, SettingBinderListener>? = HashMap()

        // 回连延时
        private const val DELAY = 1000L

        // 单例对象
        val instance: CarDmManager by lazy(LazyThreadSafetyMode.PUBLICATION) {
            CarDmManager()
        }
    }

    //cp无线切换有线提示
    private var wiredSwitchingToastCp: SettingsDmBtnToast? = null

    // aa无线切换有线提示
    private var wiredSwitchingToastAa: SettingsDmBtnToast? = null

    //cpaa无线切换有线dialog
    private var wiredSwitchingDialog : DMConfirmDialog ?= null

    // 自定义Toast
    private var toast: SettingsToast? = null

    //cp相关
    private val cpDeviceProxy: DeviceProxy = DeviceProxy.getInstance()
    private val cpConnectionProxy: ConnectionProxy = ConnectionProxy.getInstance()
    private val cpDevicesList = CopyOnWriteArrayList<CarplayDevice>()

    //aa相关
    private val aaSessionConnectProxy: SessionConnectProxy = SessionConnectProxy.getInstance()
    private val aaDeviceListProxy: DeviceListProxy = DeviceListProxy.getInstance()
    private val aaDevicesList = CopyOnWriteArrayList<AAutoDevice>()

    //蓝牙相关
    private var isSwitchingCpAa = false

    // 是否重连蓝牙
    private var isReconnectionBt = true
    private val bluetoothProxy: BluetoothProxy = BluetoothProxy.getInstance()
    private val handler = Handler(Looper.getMainLooper())

    // 当前已连接的carPlay设备
    private var carplayDevice: CarplayDevice? = null

    /**
     * 初始化显示设置.
     *
     */
    fun initCarDmManager() {
        LogUtil.d(TAG, "initCarDmManager : ")
        cpConnectionProxy.init(MyApplication.getContext(), CpConnectionListener())
        cpDeviceProxy.init(MyApplication.getContext(), CpDeviceListener())
        aaSessionConnectProxy.init(MyApplication.getContext(), AaSessionConnectListener())
        aaDeviceListProxy.init(MyApplication.getContext(), AaSettingBinderListener())
        bluetoothProxy.init(MyApplication.getContext(), CpBluetoothListener())
        // 注册CP相关广播信息
        registerCpReceiver()
    }

    /**
     * 注册CP相关广播.
     *
     */
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private fun registerCpReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(ACTION_CARPLAY_NOTIFICATION_BROADCAST)
        MyApplication.getContext().registerReceiver(object : BroadcastReceiver() {
            override fun onReceive(p0: Context, intent: Intent) {
                if (TextUtils.equals(ACTION_CARPLAY_NOTIFICATION_BROADCAST, intent.action)) {
                    GlobalScope.launch(Dispatchers.Default) {
                        val status = intent.getStringExtra(ACTION_SIG_STATUS_EXTRA)
                        val isConnect = ("connected") == status
                        val option = intent.getStringExtra(ACTION_OPTION_EXTRA)
                        val isAutoMode = ("auto-mode") == option
                        LogUtil.i(
                            TAG, "registerCpReceiver : " +
                                    "status = $status , " +
                                    "isConnect = $isConnect , " +
                                    "option = $option , " +
                                    "isAutoMode = $isAutoMode"
                        )
                        // 当前已连接，且不进入CP，则判断是否进行弹窗提示
                        if (isConnect && !isAutoMode) {
                            // 当前不处于all-app页面,则需要进行弹窗提示
                            if (!TextUtils.equals(getForegroundActivity(MyApplication.getContext()), ALL_APP_ACTIVITY)) {
                                handler.post {
                                    val settingClickTipToast = SettingsClickToast(MyApplication.getContext())
                                    settingClickTipToast.setClickCallback(object :
                                        SettingsClickToast.OnClickCallback {
                                        override fun onClick() {
                                            LogUtil.d(TAG, "settingClickTipToast click")
                                            requestForegroundCp()
                                        } })
                                    settingClickTipToast.showToast( MyApplication.getContext()
                                            .getString(R.string.dm_cp_connected_success_tips))
                                }
                            }
                        }
                    }
                }
            }
        }, intentFilter)
    }

    /**
     * 释放.
     *
     */
    fun unInitCarDmManager() {
        LogUtil.d(TAG, "initCarDmManager : ")
        toast = null
    }

    /**
     * CP是否已连接或连接中.
     *
     */
    fun hasConnectDeviceCp(): Boolean {
        cpDevicesList.forEach {
            if (it.connectState == IConstant.ConnectState.CONNECTED
                || it.connectState == IConstant.ConnectState.CONNECTING
            ) {
                return true
            }
        }
        return false
    }

    /**
     * CP是否连接中.
     *
     */
    fun hasConnectingDeviceCp(): Boolean {
        cpDevicesList.forEach {
            if (it.connectState == IConstant.ConnectState.CONNECTING) {
                return true
            }
        }
        return false
    }



    /**
     * 是否在CP列表中.
     *
     */
    fun hasDeviceCp(device: BluetoothDevice): Boolean {
        cpDevicesList.forEach {
            if (it.bluetoothAddress.equals(device.address)) {
                return true
            }
        }
        return false
    }


    /**
     * 判断当前蓝牙设备是否连接了CP
     *
     * @param device 当前待判断的蓝牙设备
     * @return Boolean
     */
    fun hasConnectDeviceCp(device: BluetoothDevice): Boolean {
        val carplayDevice =
            cpDevicesList.find { TextUtils.equals(it.bluetoothAddress, device.address) }
        return if (carplayDevice != null) {
            // 在CP列表中已找到,并且连接状态是connected,则返回true
            carplayDevice.connectState == IConstant.ConnectState.CONNECTED
        } else {
            // 在CP设备列表中未找到,则返回false
            false
        }
    }


    /**
     * 判断当前蓝牙设备是否连接了有线CP
     *
     * @param device 当前待判断的蓝牙设备
     * @return Boolean
     */
    fun hasConnectWiredCp(device: BluetoothDevice): Boolean {
        LogUtil.d(TAG, "hasConnectWiredCp : ")
        val carplayDevice =
            cpDevicesList.find { TextUtils.equals(it.bluetoothAddress, device.address) }
        return if (carplayDevice != null) {
            // 在CP列表中已找到,连接状态是connected,并且为有线连接,则返回true
            ((carplayDevice.connectState == IConstant.ConnectState.CONNECTED
                    || carplayDevice.connectState == IConstant.ConnectState.CONNECTING)
                    && !carplayDevice.isWirelessLink)
        } else {
            // 在CP设备列表中未找到,则返回false
            false
        }
    }

    /**
     * AA是否已连接或连接中.
     *
     */
    fun hasConnectDeviceAa(): Boolean {
        aaDevicesList.forEach {
            if (it.connectState == ConnectState.CONNECTING
                || it.connectState == ConnectState.CONNECTED
            ) {
                return true
            }
        }
        return false
    }

    /**
     * 当前蓝牙设备是否连接了AA.
     *
     * @param device 蓝牙设备
     * @return Boolean
     */
    fun hasConnectDeviceAa(device: BluetoothDevice): Boolean {
        val aAutoDevice = aaDevicesList.find { TextUtils.equals(it.btMacAddress, device.address) }
        return if (aAutoDevice != null) {
            // 在aa列表中已找到,连接状态是connected,则返回true
            aAutoDevice.connectState.state == ConnectState.CONNECTED.state
        } else {
            // 未找到,则不存在，返回false
            false
        }
    }

    /**
     * 当前蓝牙设备是否连接了有线AA.
     *
     * @param device 蓝牙设备
     * @return Boolean
     */
    fun hasConnectWiredAa(device: BluetoothDevice): Boolean {
        LogUtil.d(TAG, "hasConnectWiredAa ")
        val aAutoDevice = aaDevicesList.find { TextUtils.equals(it.btMacAddress, device.address) }
        return if (aAutoDevice != null) {
            // 在aa列表中已找到,连接状态是connected,并且为有线连接,则返回true
            ((aAutoDevice.connectState.state == ConnectState.CONNECTED.state
                    || aAutoDevice.connectState.state == ConnectState.CONNECTING.state)
                    && aAutoDevice.connectType.type == ConnectType.CONNECT_WIRED.type)
        } else {
            // 未找到,则不存在，返回false
            false
        }
    }

    /**
     * Delete device cp aa
     * 移除此蓝牙当前CP连接设备
     * @param bt
     */
    fun deleteDeviceCpAa(bt: BluetoothDevice) {
        LogUtil.d(TAG, "deleteDeviceCpAa : ")
        deleteDeviceCp(bt)
        deleteDeviceAa(bt)
    }

    /**
     * 通过mac地址连接蓝牙
     *
     * @param bluetoothAddress 蓝牙mac地址
     * @param connectType 连接类型
     */
    private fun connectBtOfAddress(
        bluetoothAddress: String,
        connectType: Int = Contacts.BT_CONNECT_ALL
    ) {
        LogUtil.d(
            TAG, "connectBtOfAddress : " +
                    "bluetoothAddress = $bluetoothAddress , " +
                    "connectType = $connectType"
        )
        CarBtManager.instance.connectDeviceOfAddress(bluetoothAddress, connectType)
    }

    /**
     * 根据mac地址,断开蓝牙.
     *
     * @param bluetoothAddress 蓝牙mac地址
     * @param connectType 断开类型
     */
    private fun disconnectBtOfAddress(
        bluetoothAddress: String,
        connectType: Int = Contacts.BT_CONNECT_ALL
    ) {
        LogUtil.d(
            TAG, "disconnectBtOfAddress : " +
                    "bluetoothAddress = $bluetoothAddress , " +
                    "connectType = $connectType"
        )
        CarBtManager.instance.disconnectDeviceOfAddress(bluetoothAddress, connectType)
    }

    /**********************CP******************/
    /**
     * Check wireless support
     * 判断是否支持Carplay
     * @param device
     * @return
     */
    fun checkWirelessCp(device: BluetoothDevice): Boolean {
        try {
            if (device.uuids != null) {
                for (uuid in device.uuids) {
                    if (uuid != null) {
                        if (DEVICE_CARPLAY_UUID.equals(
                                uuid.toString(),
                                true
                            )
                        ) {
                            LogUtil.d(TAG, "checkWirelessCp, ${device.name} has carplay uuid service:$uuid")
                            return true
                        }
                    }
                }
            }else{
                LogUtil.d(TAG, "checkWirelessCp, ${device.name} uuid is null")
            }
        }catch (e:NullPointerException){
            LogUtil.e(TAG, "checkWirelessCp : ${e.printStackTrace()}")
        }
        return false
    }

    /**
     * Request cp foreground
     * 打开CP
     */
    fun requestForegroundCp() {
        LogUtil.d(TAG, "requestForegroundCp : ")
        cpConnectionProxy.requestCpForeground("")
    }

    /**
     * Response carplay connect
     * 设置是否允许连接
     * @param can
     */
    fun responseConnectCp(can: Boolean) {
        LogUtil.d(TAG, "responseCarplayConnect : ")
        cpConnectionProxy.responseCarplayConnect(can)
    }

    fun responseConnectAa(can: Boolean) {
        LogUtil.d(TAG, "responseAndroidAutoConnect : ")
        aaSessionConnectProxy.responseAndroidAutoConnect(can)
    }

    /**
     * Check wireless connect cp
     * 检查此蓝牙设备是否存在CP连接
     * @param device
     * @return
     */
    fun checkWirelessConnectCp(device: BluetoothDevice): Boolean {
        cpDevicesList.forEach {
            if (it.connectState == IConstant.ConnectState.CONNECTED) {
                if (it.bluetoothAddress == device.address) {
                    LogUtil.d(
                        TAG,
                        "checkWirelessConnectCp : bluetoothAddress ${it.bluetoothAddress}"
                    )
                    return true
                }
            }
        }
        return false
    }

    /**
     * Check wireless connect aa
     * 检查此蓝牙设备是否存在AA连接
     * @param device
     * @return
     */
    fun checkWirelessConnectAa(device: BluetoothDevice): Boolean {
        aaDevicesList.forEach {
            if (it.connectState == ConnectState.CONNECTED) {
                if (it.btMacAddress == device.address) {
                    LogUtil.d(
                        TAG,
                        "checkWirelessConnectAa : btMacAddress ${it.btMacAddress}"
                    )
                    return true
                }
            }
        }
        return false
    }

    /**
     * Disconnect device cp
     * 移除此蓝牙当前CP连接设备
     */
    private fun deleteDeviceCp(bt: BluetoothDevice) {
        LogUtil.d(TAG, "disconnectDeviceCp : ")
        cpDevicesList.forEach {
            if (it.bluetoothAddress == bt.address) {
                cpDeviceProxy.deleteDevice(it)
            }
        }
    }

    /**
     * Disconnect device cp
     * 断开当前CP连接设备
     */
    fun disconnectDeviceCp() {
        LogUtil.d(TAG, "disconnectDeviceCp : ")
        cpDevicesList.forEach {
            if (it.connectState == IConstant.ConnectState.CONNECTED
                || it.connectState == IConstant.ConnectState.CONNECTING
            ) {
                cpDeviceProxy.disconnectDevice(it)
            }
        }
    }

    /**
     * 断开当前蓝牙设备对应的CP设备连接.
     *
     * @param device 蓝牙设备
     * @param isAutoConnectBt 是否重连蓝牙
     */
    fun disconnectDeviceCp(device: BluetoothDevice, isAutoConnectBt: Boolean) {
        LogUtil.d(TAG, "disconnectDeviceCp : isAutoConnectBt = $isAutoConnectBt")
        cpDevicesList.forEach {
            if (TextUtils.equals(it.bluetoothAddress, device.address)) {
                if (it.connectState == IConstant.ConnectState.CONNECTED
                    || it.connectState == IConstant.ConnectState.CONNECTING
                ) {
                    isReconnectionBt = isAutoConnectBt
                    cpDeviceProxy.disconnectDevice(it)
                }
            }
        }
    }

    /**
     * Get devices list cp
     * 获取当前cp匹配列表
     * @return
     */
    fun getDevicesListCp(): ArrayList<CarplayDevice> {
        return ArrayList(cpDevicesList)
    }

    /**
     * Get connect devices  cp
     * 获取当前连接cp设备
     * @return
     */
    fun getConnectDevicesCp(): CarplayDevice? {
        cpDevicesList.forEach {
            if (it.connectState == IConstant.ConnectState.CONNECTED) {
                return it
            }
        }
        return null
    }

    /**
     * 获取设备的连接状态.
     *
     * @param device 设备
     * @return
     */
    fun getDeviceCpState(device: BluetoothDevice): Int {
        cpDevicesList.forEach {
            if (TextUtils.equals(it.bluetoothAddress, device.address)) {
                return it.connectState
            }
        }
        return IConstant.ConnectState.IDLE
    }

    /**
     * 获取CP设备的连接类型.
     *
     * @param device 设备
     * @return
     */
    fun getDeviceCpLink(device: BluetoothDevice): Int {
        cpDevicesList.forEach {
            if (TextUtils.equals(it.bluetoothAddress, device.address)) {
                return if (it.isWirelessLink) {
                    // 无线
                    Contacts.CP_CONNECT_WIRELESS
                } else {
                    // 有线
                    Contacts.CP_CONNECT_WIRED
                }
            }
        }
        return Contacts.CP_CONNECT_INVALID
    }

    /**
     * Set device listener cp
     * 设置一个cp监听
     * @param callback
     * @param tag 监听标签
     */
    fun setDeviceListenerCp(tag: String, callback: CpDeviceListenerNotify) {
        // 添加监听
        dmCpListenerMap?.set(tag, callback)
    }

    /**
     * 注销cp监听.
     *
     * @param tag 类名
     */
    fun unregisterDeviceListenerCp(tag: String?) {
        LogUtil.d(TAG, "unregisterDeviceListenerCp : tag = $tag")
        // 移除监听
        dmCpListenerMap?.remove(tag)
    }

    /**
     * Add device cp
     * 添加一个Cp设备
     * 需要结合蓝牙配对
     * @param device
     */
    fun addDeviceCp(device: CarplayDevice, bt: BluetoothDevice) {
        LogUtil.d(TAG, "addDeviceCp : ")
        disconnectDeviceAa()
        cpDeviceProxy.addDevice(device)
        connectBtOfAddress(bt.address, Contacts.BT_CONNECT_ALL)
    }

    /**
     * Delete device cp
     * 删除一个cp设备
     * 需要结合蓝牙配对
     * @param device
     */
    fun deleteDeviceCp(device: CarplayDevice) {
        LogUtil.d(TAG, "deleteDeviceCp : ")
        cpDeviceProxy.deleteDevice(device)
        CarBtManager.instance.getBtPairedDevices().forEach {
            if (it.address == device.bluetoothAddress) {
                LogUtil.d(TAG, "deleteDevice : it ${it.address},it ${device.bluetoothAddress} ")
                CarBtManager.instance.removeBond(it)
            }
        }
    }

    /**
     * Disconnect device cp
     * 断开一个cp设备
     * 会走蓝牙连接回调
     * @param device
     */
    fun disconnectDeviceCp(device: CarplayDevice) {
        LogUtil.d(TAG, "disconnectDeviceCp : ")
        cpDeviceProxy.disconnectDevice(device)
    }

    /**
     * 断开CP
     *
     * @param device 设备
     * @param isAutoConnectBt 是否回连蓝牙
     */
    private fun disconnectDeviceCp(device: CarplayDevice, isAutoConnectBt: Boolean) {
        LogUtil.d(TAG, "disconnectDeviceCp : isAutoConnectBt = $isAutoConnectBt")
        isReconnectionBt = isAutoConnectBt
        cpDeviceProxy.disconnectDevice(device)
    }

    /**
     * Response general popup Cp
     * 响应cp请求
     * @param device
     * @param popupId
     * @param state
     */
    fun responseGeneralPopupCp(device: CarplayDevice, popupId: Int, state: Int) {
        LogUtil.d(TAG, "responseGeneralPopupCp device $device popupId $popupId  state $state")
        cpDeviceProxy.responseGeneralPopup(device, popupId, state)
    }

    /**
     * Disconnect device cp
     * 连接一个cp设备
     * 会走蓝牙断开回调
     * @param device
     */
    fun connectDeviceCp(device: CarplayDevice) {
        LogUtil.d(TAG, "connectDeviceCp : ")
        Thread {
            isSwitchingCpAa = false
            getConnectDevicesAa()?.let {
                isSwitchingCpAa = true
                // 断开AA蓝牙
                disconnectBtOfAddress(it.btMacAddress, Contacts.BT_CONNECT_HFP)
                // 断开AA连接
                disconnectDeviceAa(it)
            }
            //连接cp
            cpDeviceProxy.connectDevice(device)
            isSwitchingCpAa = false
        }.start()
    }

    /**
     * 通过蓝牙设备.连接cp(AA切换CP)
     *
     * @param device 蓝牙设备
     */
    fun connectDeviceAaToCp(device: BluetoothDevice) {
        LogUtil.d(TAG, "connectDeviceAAtoCp : device = ${device.name}")
        isSwitchingCpAa = false
        getConnectDevicesAa()?.let {
            isSwitchingCpAa = true
            // 断开AA蓝牙
            disconnectBtOfAddress(it.btMacAddress, Contacts.BT_CONNECT_HFP)
            // 断开AA连接,不回连
            disconnectDeviceAa(it, false)
        }
        // 连接cp
        connectDeviceCp(device)
        isSwitchingCpAa = false
    }

    /**
     * 通过蓝牙设备.连接cp
     *
     * @param device 蓝牙设备
     */
    fun connectDeviceCp(device: BluetoothDevice) {
        LogUtil.d(TAG, "connectDeviceCp : device = ${device.name}(${device.address})")
//        var cpDevice :CarplayDevice? = null
//        cpDevicesList.forEach { carPlayDevice ->
//            if (carPlayDevice.bluetoothAddress == device.address) {
//                cpDevice = carPlayDevice
//                return@forEach
//            }
//        }
//        if(cpDevice == null){
//            val bondState = device.bondState
//            LogUtil.d(TAG, "connectDeviceCp bondState = $bondState")
//            if(device.bondState == BluetoothDevice.BOND_BONDED){
//                firstConnectCp(device)
//            }else{
//                // 开始请求绑定
//                CarBtManager.instance.isUserCreateBond = true
//                device.createBond()
//            }
//        }else{
//            LogUtil.d(TAG, "connectDeviceCp ")
//            cpDeviceProxy.connectDevice(cpDevice)
//        }
        val vdLinkDevice = VDLinkDevice()
        vdLinkDevice.btAddress = device.address
        val payload = Bundle()
        payload.putInt(VDKey.TYPE, VDValuePhoneLink.ServerId.CARPLAY) //互联服务ID,VDValuePhoneLink.ServerId.COMMON
//        payload.putBoolean(VDKey.ENABLE, isSync) // true 数据保存到CLS数据库,目前只有同个手机多个互联才有用到这个字段
        payload.putParcelable(VDKey.DATA, vdLinkDevice) //(VDLinkDevice)device,要连接的设备
        val event = VDEvent(VDEventPhoneLink.CONNECT_DEVICE, payload)
        VDBus.getDefault().set(event)
    }

    fun firstConnectCp(device: BluetoothDevice){
        LogUtil.d(TAG, "firstConnectCp : device = ${device.name}(${device.address})")
//        val arrayUuid = device.uuids
//        if(arrayUuid != null){
//            val stringBuilder = StringBuilder()
//            // 遍历parcelables数组，将每个Parcelable对象转换为字符串并追加到StringBuilder中
//            for (i in arrayUuid.indices) {
//                stringBuilder.append(arrayUuid[i])
//                // 除了最后一个元素外，都在末尾追加逗号
//                if (i != arrayUuid.size - 1) {
//                    stringBuilder.append(",")
//                }
//            }
//            val uuid = stringBuilder.toString()
//            LogUtil.d(TAG, "firstConnectCp : uuid = $uuid")
//
//            isSwitchingCpAa = false
//            getConnectDevicesAa()?.let {
//                isSwitchingCpAa = true
//                // 断开AA蓝牙
//                disconnectBtOfAddress(it.btMacAddress, Contacts.BT_CONNECT_HFP)
//                // 断开AA连接,不回连
//                disconnectDeviceAa(it, false)
//            }
//            getConnectDevicesCp()?.let {
//                isSwitchingCpAa = true
//                disconnectDeviceCp(it,false)
//            }
//            // 连接cp
//            cpDeviceProxy.notifyBtBondState(12,device.address,uuid)
//            isSwitchingCpAa = false
//        }
        val payload = Bundle()
        payload.putInt(VDKey.TYPE, VDValuePhoneLink.ServerId.CARPLAY) //互联服务ID,VDValuePhoneLink.ServerId.COMMON
//        payload.putBoolean(VDKey.ENABLE, isSync) // true 数据保存到CLS数据库,目前只有同个手机多个互联才有用到这个字段
        payload.putParcelable(VDKey.DATA, device) //(VDLinkDevice)device,要连接的设备
        val event = VDEvent(VDEventPhoneLink.CONNECT_DEVICE, payload)
        VDBus.getDefault().set(event)
    }

    /**********************AA******************/
    /**
     * Request aa foreground
     * 打开aa
     */
    fun requestForegroundAa() {
        LogUtil.d(TAG, "requestForegroundAa : ")
        aaSessionConnectProxy.requestAAForeground("")
    }

    /**
     * 是否支持无线AA.
     *
     * @param device 设备
     */
    fun checkWirelessAa(device: BluetoothDevice): Boolean {
        aaDevicesList.forEach {
            if (TextUtils.equals(it.btMacAddress, device.address)) {
                return true
            }
        }
        return false
    }

    /**
     * 获取Aa设备的连接状态.
     *
     * @param device 设备
     * @return
     */
    fun getDeviceAaState(device: BluetoothDevice): Int {
        aaDevicesList.forEach {
            if (TextUtils.equals(it.btMacAddress, device.address)) {
                return it.connectState.state
            }
        }
        return ConnectState.DISCONNECTED.state
    }

    /**
     * 获取Aa设备的连接类型.
     *
     * @param device 设备
     * @return
     */
    fun getDeviceAaLink(device: BluetoothDevice): Int {
        aaDevicesList.forEach {
            if (TextUtils.equals(it.btMacAddress, device.address)) {
                return it.connectType.type
            }
        }
        return ConnectType.CONNECT_INVALID.type
    }

    /**
     * Disconnect device aa
     * 断开当前aa连接设备
     */
    fun disconnectDeviceAa() {
        LogUtil.d(TAG, "disconnectDeviceAa : ")
        aaDevicesList.forEach {
            if (it.connectState == ConnectState.CONNECTING
                || it.connectState == ConnectState.CONNECTED
            ) {
                disconnectDeviceAa(it)
            }
        }
    }

    /**
     * Add device aa
     * 添加一个Cp设备
     * 需要结合蓝牙配对
     * @param bt
     */
    fun addDeviceAa(bt: BluetoothDevice) {
        LogUtil.d(TAG, "addDeviceCp : ")
        // 断开cp
        disconnectDeviceCp()
        // 连接aa
        connectBtOfAddress(bt.address, Contacts.BT_CONNECT_ALL)
    }

    /**
     * Disconnect device cp
     * 移除此蓝牙当前CP连接设备
     */
    private fun deleteDeviceAa(bt: BluetoothDevice) {
        LogUtil.d(TAG, "deleteDeviceAa : ")
        aaDevicesList.forEach {
            if (it.btMacAddress == bt.address) {
                aaDeviceListProxy.delete(it)
            }
        }
    }

    /**
     * Set device listener aa
     * 设置一个aa监听
     * @param callback
     */
    fun setDeviceListenerAa(tag: String, callback: SettingBinderListener) {
        // 添加监听
        dmAaListenerMap?.set(tag, callback)
    }

    /**
     * 注销Aa监听.
     *
     * @param tag 类名
     */
    fun unregisterDeviceListenerAa(tag: String?) {
        LogUtil.d(TAG, "unregisterDeviceListenerAa : tag = $tag")
        // 移除监听
        dmAaListenerMap?.remove(tag)
    }

    /**
     * Get devices list aa
     * 获取aa列表
     * @return
     */
    fun getDevicesListAa(): ArrayList<AAutoDevice> {
        return ArrayList(aaDevicesList)
    }

    /**
     * Get connect devices  aa
     * 获取当前连接aa设备
     * @return
     */
    fun getConnectDevicesAa(): AAutoDevice? {
        aaDevicesList.forEach {
            if (it.connectState == ConnectState.CONNECTED
                || it.connectState == ConnectState.CONNECTING
            ) {
                return it
            }
        }
        return null
    }

    /**
     * Connect device aa
     * 连接一个aa设备
     * @param device
     */
    fun connectDeviceAa(device: AAutoDevice) {
        //断开cp
        Thread {
            isSwitchingCpAa = false
            getConnectDevicesCp()?.let {
                isSwitchingCpAa = true
                // 断开蓝牙
                disconnectBtOfAddress(it.bluetoothAddress)
                // 断开cp
                disconnectDeviceCp(it,false)
            }
            getConnectDevicesAa()?.let {
                isSwitchingCpAa = true
                // 断开蓝牙
                disconnectBtOfAddress(it.btMacAddress)
                // 断开aa
                disconnectDeviceAa(it,false)
            }
            // 连接aa蓝牙电话
            if (device.connectType == ConnectType.CONNECT_WIRELESS) {
                connectBtOfAddress(device.btMacAddress, Contacts.BT_CONNECT_HFP)
            }
            Thread.sleep(1000)
            aaDeviceListProxy.connect(device)
            isSwitchingCpAa = false
        }.start()
    }

    /**
     * 通过蓝牙设备.连接AA(CP切换AA)
     *
     * @param device 蓝牙设备
     */
    fun connectDeviceCpToAA(device: BluetoothDevice) {
        LogUtil.d(TAG, "connectDeviceCpToAA : device = ${device.name}")
        aaDevicesList.forEach { aaDevice ->
            if (TextUtils.equals(aaDevice.btMacAddress, device.address)) {
                isSwitchingCpAa = false
                getConnectDevicesCp()?.let {
                    isSwitchingCpAa = true
                    // 断开蓝牙
                    disconnectBtOfAddress(it.bluetoothAddress)
                    // 断开cp
                    disconnectDeviceCp(it, false)
                }
                // 连接aa
                connectBtOfAddress(aaDevice.btMacAddress)
                Thread.sleep(1000)
                aaDeviceListProxy.connect(aaDevice)
                isSwitchingCpAa = false
                return@forEach
            }
        }
    }

    /**
     * 通过蓝牙设备.连接Aa
     *
     * @param device 蓝牙设备
     */
    fun connectDeviceAa(device: BluetoothDevice) {
        LogUtil.d(TAG, "connectDeviceAa : device = ${device.name}")
        aaDevicesList.forEach { aaDevice ->
            if (TextUtils.equals(aaDevice.btMacAddress, device.address)) {
                // 连接AA蓝牙电话
                connectBtOfAddress(aaDevice.btMacAddress, Contacts.BT_CONNECT_HFP)
                // 连接AA协议
                LogUtil.d(TAG, "connectDeviceAa : proxy connect")
                aaDeviceListProxy.connect(aaDevice)
                return@forEach
            }
        }
    }

    /**
     * Disconnect device aa
     * 断开一个aa设备
     * @param device
     */
    fun disconnectDeviceAa(device: AAutoDevice) {
        aaDeviceListProxy.disconnect(device)
    }

    /**
     * 断开AA
     *
     * @param device AA设备
     * @param isAutoConnectBt 是否回连蓝牙
     */
    private fun disconnectDeviceAa(device: AAutoDevice, isAutoConnectBt: Boolean) {
        LogUtil.i(TAG, "disconnectDeviceAa : isAutoConnectBt = $isAutoConnectBt")
        isReconnectionBt = isAutoConnectBt
        aaDeviceListProxy.disconnect(device)
    }

    /**
     * 断开当前的AA设备
     *
     * @param device 设备
     * @param isAutoConnectBt 是否重连蓝牙
     */
    fun disconnectDeviceAa(device: BluetoothDevice, isAutoConnectBt: Boolean) {
        LogUtil.d(TAG, "disconnectDeviceAa : isAutoConnectBt = $isAutoConnectBt")
        aaDevicesList.forEach {
            if (TextUtils.equals(it.btMacAddress, device.address)) {
                if (it.connectState.state == ConnectState.CONNECTED.state
                    || it.connectState.state == ConnectState.CONNECTING.state
                ) {
                    isReconnectionBt = isAutoConnectBt
                    aaDeviceListProxy.disconnect(it)
                    if (!isReconnectionBt) {
                        // 不自动重连，需断开电话
                        disconnectBtOfAddress(device.address, Contacts.BT_CONNECT_HFP)
                    }
                }
            }
        }
    }

    /**
     * Delete device aa
     * 删除一个aa设备
     * @param device
     */
    fun deleteDeviceAa(device: AAutoDevice) {
        aaDeviceListProxy.delete(device)
        CarBtManager.instance.getBtPairedDevices().forEach {
            if (it.address == device.btMacAddress) {
                LogUtil.d(TAG, "deleteDevice : it ${it.address},it ${device.btMacAddress} ")
                CarBtManager.instance.removeBond(it)
            }
        }
    }

    /**
     * 获取当前任务战前台运行的activity.
     *
     * @param context
     * @return 前台activity名称
     */
    fun getForegroundActivity(context: Context): String? {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val info = activityManager.getRunningTasks(1)[0]
        return if (info.topActivity != null) {
            // launcher-all app类名
            val shortClassName = info.topActivity!!.shortClassName
            LogUtil.i(TAG, "getForegroundActivity : shortClassName = $shortClassName")
            shortClassName
        } else {
            null
        }
    }

    /**
     * 显示提示Toast.
     *
     * @param message 提示信息
     */
    private fun showToast(message: String) {
        GlobalScope.launch(Dispatchers.Main){
            LogUtil.i(TAG, "showToast : message = $message")
            SettingsToast.showToast(message)
        }
    }

    /**
     * cpaa无线连接中，此时接入有线cpaa。则需弹窗询问⽤户是否切换连接，点击确定则切换为有线cpaa连接；
     *点击取消保持连接现状，10S倒计时结束执⾏取消操作；
     *
     */
    private fun showCpAAWiredConnectTipsDialog(deviceName:String?,operationConfirm:(()->Unit),operationCancel: (() -> Unit)){
        LogUtil.i(TAG, "showCpAAWiredConnectTipsDialog : deviceName = $deviceName")
        GlobalScope.launch(Dispatchers.Main){
            if(wiredSwitchingDialog?.isShowing == true){
                wiredSwitchingDialog?.dismiss()
            }
            wiredSwitchingDialog = DMConfirmDialog(MyApplication.getContext())
            wiredSwitchingDialog?.setTips(MyApplication.getContext().getString(R.string.dm_cpaa_wired_tips,deviceName))
            // 添加监听
            wiredSwitchingDialog?.setDialogClickCallback(object :
                DMConfirmDialog.OnConfirmDialogClickCallback {
                override fun onConfirmClick() {
                    LogUtil.d(TAG, "showCpAAWiredConnectTipsDialog onConfirmClick : ")
                    operationConfirm.invoke()
                }

                override fun onCancelClick() {
                    LogUtil.d(TAG, "showCpAAWiredConnectTipsDialog onCancelClick : ")
                    operationCancel.invoke()
                }
            })
            wiredSwitchingDialog?.onShow(canceledOnTouchOutside = false, isAutoDismiss = true)
        }
    }


    /**
     * Response general popup Aa
     * 响应aa请求
     * @param device
     * @param popupId
     * @param state
     */
    fun responseGeneralPopupAa(device: AAutoDevice, popupId: Int, state: Int) {
        LogUtil.d(TAG, "responseGeneralPopupAa device $device popupId $popupId  state $state")
        aaDeviceListProxy.responseGeneralPopup(device, popupId, state)
    }

    private inner class CpConnectionListener : ConnectionListener() {
        override fun notifyConnectState(address: String?, state: Int) {
            LogUtil.d(TAG, "notifyConnectState cp address $address state$state")
        }

        override fun notifyScreenMode(isCarplayUi: Boolean) {
            LogUtil.d(TAG, "notifyConnectState cp isCarplayUi $isCarplayUi")
        }

        override fun notifyServiceConnectStatus(isConnected: Boolean) {
            super.notifyServiceConnectStatus(isConnected)
            LogUtil.i(TAG, "notifyServiceConnectStatus : cp isConnected = $isConnected")
            // CP协议是否连接赋值
            Contacts.isConnectCpProxy = isConnected
            if (isConnected) {
                cpConnectionProxy.registerControlListener(CpConnectionControl())
                // 开始回连
                CarBtManager.instance.startBtReconnection()
            }
        }
    }

    private inner class CpConnectionControl : ConnectionControl() {
        override fun requestCarplayConnect(address: String, name: String?): Int {
            LogUtil.i(TAG, "requestCarplayConnect : address = $address , name = $name")
            //address 不为空，即无线连接
            if(address.isNotEmpty()){
                //T13JSUPPLY-427 无线连接cp，需断开aa
                getConnectDevicesAa()?.let {
                    //断开无线AA蓝牙
                    disconnectBtOfAddress(it.btMacAddress, Contacts.BT_CONNECT_HFP)
                    //断开无线AA协议
                    disconnectDeviceAa(it,false)
                }
                return IConstant.ConnectionControl.ALLOW_CONNECT
            }
            val aaDevice = getConnectDevicesAa()
            val cpDevice = getConnectDevicesCp()
            val btDevicesList = CarBtManager.instance.getBluetoothConnectedDevicesList()
            LogUtil.i(TAG, "requestCarplayConnect : aaDevice = $aaDevice , cpDevice = $cpDevice , btDevicesList = ${btDevicesList.size}")
            //插入有线cp,弹框提示用户
            if(aaDevice != null){
                showCpAAWiredConnectTipsDialog(aaDevice.btDeviceName,
                    {
                        LogUtil.i(TAG, "requestCarplayConnect disconnectDeviceAa ")
                        //断开无线AA蓝牙
                        disconnectBtOfAddress(aaDevice.btMacAddress, Contacts.BT_CONNECT_HFP)
                        //断开无线AA协议
                        disconnectDeviceAa(aaDevice,false)
                        responseConnectCp(true)
                    },
                    {
                        responseConnectCp(false)
                    })
                return IConstant.ConnectionControl.WAIT_CONNECT
            }else if(cpDevice != null){
                showCpAAWiredConnectTipsDialog(cpDevice.deviceName,
                    {
                        LogUtil.i(TAG, "requestCarplayConnect disconnectDeviceCp ")
                        disconnectDeviceCp(cpDevice,false)
                        responseConnectCp(true)
                    },
                    {
                        responseConnectCp(false)
                    })
                return IConstant.ConnectionControl.WAIT_CONNECT
            }else if(btDevicesList.size > 1){
                CarBtManager.instance.showConnectedSelectedDialog(BtDeviceBean(btDevicesList[0]),
                    BtDeviceBean(btDevicesList[1]),{
                        LogUtil.i(TAG, "requestCarplayConnect replace ${it.device.name}")
                        CarBtManager.instance.disconnectBtPhone(it.device)
                        CarBtManager.instance.disconnectBtMusic(it.device)
                        responseConnectCp(true)
                    },{
                        LogUtil.i(TAG, "requestCarplayConnect cancel")
                        responseConnectCp(false)
                    }
                )
                return IConstant.ConnectionControl.WAIT_CONNECT
            }else{
                return IConstant.ConnectionControl.ALLOW_CONNECT
            }

        }
    }

    private inner class CpDeviceListener : DeviceListener() {
        override fun notifyServiceConnectStatus(isConnected: Boolean) {
            LogUtil.i(TAG, "notifyServiceConnectStatus : cp dev isConnected = $isConnected")
            // CP设备列表协议是否连接赋值
            Contacts.isConnectDevCpProxy = isConnected
            if (isConnected) {
                handler.postDelayed({
                    // 开始回连
                    CarBtManager.instance.startBtReconnection()
                }, DELAY)
            }
        }

        override fun updateDeviceList(devices: MutableList<CarplayDevice>) {
            LogUtil.d(TAG, "updateDeviceList cp devices ${devices.size}")
            cpDevicesList.clear()
            devices.forEach { carplayDevice ->
                LogUtil.d(TAG, "updateDeviceList cp deviceName ${carplayDevice.deviceName}")
                LogUtil.d(TAG, "updateDeviceList cp connectState ${carplayDevice.connectState}")
                LogUtil.d(
                    TAG,
                    "updateDeviceList cp bluetoothAddress ${carplayDevice.bluetoothAddress}"
                )
                LogUtil.d(TAG, "updateDeviceList cp isWirelessLink ${carplayDevice.isWirelessLink}")
                LogUtil.d(TAG, "updateDeviceList cp isAutoReconnect ${carplayDevice.isAutoReconnect}")
                if (carplayDevice.connectState == 4) {
                    carplayDevice.connectState = IConstant.ConnectState.IDLE
                }
                cpDevicesList.add(carplayDevice)
            }
            for (listener in dmCpListenerMap!!.values) {
                listener.updateDeviceList(cpDevicesList)
            }
        }

        override fun notifyStateChanged(device: CarplayDevice) {
            LogUtil.d(
                TAG, "notifyStateChanged : " +
                        "device = ${device.deviceName}(${device.bluetoothAddress}), " +
                        "connectState = ${device.connectState}"
            )
            if (device.connectState == 4) {
                // 连接错误时，更新CP设备的连接状态
                device.connectState = IConstant.ConnectState.IDLE
            }
            // 更新设备列表的连接状态
            val ceDevice = cpDevicesList.find {
                TextUtils.equals(it.bluetoothAddress, device.bluetoothAddress)
            }
            if (ceDevice != null) {
                // 更新设备列表中CP设备对应的状态
                ceDevice.connectState = device.connectState
            }
            carplayDevice = device
            if (device.connectState == IConstant.ConnectState.CONNECTED) {
                // 请求拉起CP界面
                //requestForegroundCp()
                if (!device.isWirelessLink) {
                    //T13JSUPPLY-392 无需退出息屏
                    //CarSettingPowerManager.instance.exitStandbyWindow()
                    // 退出清洁模式
                    //CarSettingPowerManager.instance.exitScreenClearWindow()
                }
            }
            for (listener in dmCpListenerMap!!.values) {
                listener.notifyStateChanged(device)
            }
        }

        override fun requestGeneralPopup(device: CarplayDevice, popupId: Int) {
            LogUtil.d(
                TAG, "requestGeneralPopup cp: " +
                        "device = ${device.deviceName}(${device.bluetoothAddress}) , " +
                        "popupId = $popupId , " +
                        "state = ${device.connectState} , "+
                        "isWirelessLink = ${device.isWirelessLink}"
            )
            when (popupId) {
                // 新设备连接
                /*IConstant.DeviceList.NEW_DEVICE -> {
                    handler.post {
                        // 刷新CP连接类型弹窗
                        CarBtManager.instance.refreshConnectTypeDialog(
                            device.bluetoothAddress,
                            Contacts.BT_CONNECT_CP
                        )
                    }
                }*/
                //设备切换
                IConstant.DeviceList.CHANGE_DEVICE -> {
                    //无线连接，默认直接切换，无需弹框
                    if(device.isWirelessLink){
                        isReconnectionBt = false
                        responseGeneralPopupCp(
                            device,
                            popupId,
                            IConstant.DeviceListResponse.RESPONSE_YES
                        )
                    }else{
                        //有线连接，需要弹框提示
                        val cpDevice = getConnectDevicesCp()
                        showCpAAWiredConnectTipsDialog(cpDevice?.deviceName,
                            {
                                LogUtil.i(TAG, "requestGeneralPopup  confirm")
                                isReconnectionBt = false
                                responseGeneralPopupCp(
                                    device,
                                    popupId,
                                    IConstant.DeviceListResponse.RESPONSE_YES
                                )
                            },
                            {
                                LogUtil.i(TAG, "requestGeneralPopup  cancel")
                                responseGeneralPopupCp(
                                    device,
                                    popupId,
                                    IConstant.DeviceListResponse.RESPONSE_NO
                                )
                            })
                    }
                   /* if (wiredSwitchingToastCp != null) {
                        wiredSwitchingToastCp?.onDismiss()
                        wiredSwitchingToastCp = null
                    }
                    wiredSwitchingToastCp = SettingsDmBtnToast(MyApplication.getContext())
                    wiredSwitchingToastCp?.setClickCallback(object :
                        SettingsDmBtnToast.OnConfirmClickCallback {
                        override fun onConfirmClick() {
                            LogUtil.d(TAG, "wiredSwitchingToastCp onConfirmClick : ")
                            responseGeneralPopupCp(
                                device,
                                popupId,
                                IConstant.DeviceListResponse.RESPONSE_YES
                            )
                        }

                        override fun onDismiss() {}
                    })
                    getConnectDevicesCp()?.let {
                        wiredSwitchingToastCp?.showToast(
                            MyApplication.getContext().getString(
                                R.string.dm_cp_to_cp_tips,
                                it.deviceName,
                                device.deviceName
                            )
                        )
                    }*/
                }
                //连接失败
                IConstant.DeviceList.ERROR_CONNECT_FAIL -> {
                    handler.post {
                        showToast(
                            MyApplication.getContext()
                                .getString(R.string.dm_connected_timeout_tips)
                        )
                    }
                }
                //长时间不选择蓝牙或cp
                IConstant.DeviceList.ERROR_WIRELESS_SELECT_BT_OR_CP_TIMEOUT -> {}
                //车机端拒绝连接
                IConstant.DeviceList.ERROR_WIRELESS_CONNECTING_USER_REJECT_CAR -> {}
                //手机端拒绝连接
                IConstant.DeviceList.ERROR_WIRELESS_CONNECTING_USER_REJECT_PHONE -> {
                    //cp手机拒绝提示
                    GlobalScope.launch(Dispatchers.Main){
                        SettingsDmTipToast(MyApplication.getContext()).showToast( MyApplication.getContext().getString(
                            R.string.dm_cp_phone_open_tips,
                        ))
                    }
                }
                //正在蓝牙通话中，无法进入CarPlay
                IConstant.DeviceList.CANNOT_ENTERED_CARPLAY -> {
                    GlobalScope.launch(Dispatchers.Main){
                        SettingsDmTipToast(MyApplication.getContext()).showToast( MyApplication.getContext().getString(
                            R.string.bt_cp_phone_tips,
                        ))
                    }
                }
                //手机端异常
                IConstant.DeviceList.ERROR_WIRELESS_PHONE_ERROR -> {}
                //rfc连接超时
                IConstant.DeviceList.ERROR_WIRELESS_CONNECTING_RFCOMM_TIMEOUT -> {}
            }
            for (listener in dmCpListenerMap!!.values) {
                listener.requestGeneralPopup(device, popupId)
            }
        }

        override fun requestClearAllPopups() {
            LogUtil.d(TAG, "requestClearAllPopups cp")
            // 关闭无线CP或AA切换有线CP弹窗
            if (wiredSwitchingToastCp != null) {
                wiredSwitchingToastCp?.onDismiss()
                wiredSwitchingToastCp = null
            }
            for (listener in dmCpListenerMap!!.values) {
                listener.requestClearAllPopups()
            }
        }
    }

    private inner class AaSessionConnectListener : SessionConnectListener() {
        override fun notifyConnectState(address: String?, state: Int) {
            LogUtil.d(TAG, "notifyConnectState aa address $address state $state")
        }
    }

    private inner class AaSettingBinderListener : SettingBinderListener() {
        override fun updateDeviceList(devices: MutableList<AAutoDevice>) {
            LogUtil.d(TAG, "updateDeviceList aa devices ${devices.size}")
            devices.forEach {
                LogUtil.d(TAG, "updateDeviceList aa btDeviceName ${it.btDeviceName}")
                LogUtil.d(TAG, "updateDeviceList aa connectState ${it.connectState}")
                LogUtil.d(TAG, "updateDeviceList aa btMacAddress ${it.btMacAddress}")
                LogUtil.d(TAG, "updateDeviceList aa connectType ${it.connectType}")
                LogUtil.d(TAG, "updateDeviceList aa instanceId ${it.instanceId}")
                LogUtil.d(TAG, "updateDeviceList aa serialNumber ${it.serialNumber}")
            }
            aaDevicesList.clear()
            //偶现有些设备的mac地址为空，此处过滤，规避空指针异常
            val temList = devices.filter {
                !TextUtils.isEmpty(it.btMacAddress)
            }
            aaDevicesList.addAll(temList)
            for (listener in dmAaListenerMap!!.values) {
                listener.updateDeviceList(aaDevicesList)
            }
        }

        override fun notifyLinkStateChanged(device: AAutoDevice, state: Int) {
            LogUtil.d(TAG, "notifyLinkStateChanged device $device state $state")
            if(device.connectState.state == ConnectState.CONNECTED.state){
                CarBtManager.instance.rejectAaBluetoothConnect(device.btMacAddress,true)
            }else if(device.connectState.state == ConnectState.DISCONNECTED.state){
                CarBtManager.instance.rejectAaBluetoothConnect(device.btMacAddress,false)
            }
            var has = false
            for (device1 in aaDevicesList) {
                if (device1.btMacAddress.equals(device.btMacAddress, true)) {
                    aaDevicesList.remove(device1)
                    aaDevicesList.add(device)
                    has = true
                }
            }
            if (!has) {
                aaDevicesList.add(device)
            }
            if (device.connectState.state == ConnectState.CONNECTED.state
                && device.connectType.type == ConnectType.CONNECT_WIRED.type
                && !TextUtils.equals(device.btMacAddress, AAutoDevice.BT_ADDR_NULL)
            ) {
                //T13JSUPPLY-392 无需退出息屏
                // 退出息屏显示
                //CarSettingPowerManager.instance.exitStandbyWindow()
                // 退出清洁模式
                //CarSettingPowerManager.instance.exitScreenClearWindow()
                handler.post {
                    // 有线AA已连接，则显示提示弹窗
                    showToast(
                        MyApplication
                            .getContext().getString(R.string.dm_wired_aa_connected_tips)
                    )
                }
            }
            val tem = CopyOnWriteArrayList(aaDevicesList)
            for (listener in dmAaListenerMap!!.values) {
                listener.updateDeviceList(tem)
                listener.notifyLinkStateChanged(device, state)
            }
        }

        override fun clearAllPopup(device: AAutoDevice) {
            LogUtil.d(TAG, "clearAllPopup device $device")
            // 关闭无线CP或AA切换有线AA弹窗
            if (wiredSwitchingToastAa != null) {
                wiredSwitchingToastAa?.onDismiss()
                wiredSwitchingToastAa = null
            }
            for (listener in dmAaListenerMap!!.values) {
                listener.clearAllPopup(device)
            }
        }

        override fun requestGeneralPopup(device: AAutoDevice, popupId: Int, message: String) {
            LogUtil.d(TAG, "requestGeneralPopup aa device $device popupId$popupId  message $message")
            when (popupId) {
                //新设备连接
                IConstant.DeviceList.AAUTO_POPUP_ID_NEW_DEVICE -> {
                   /* handler.post {
                        // 刷新AA连接类型弹窗
                        CarBtManager.instance.refreshConnectTypeDialog(
                            device.btMacAddress,
                            Contacts.BT_CONNECT_AA
                        )
                    }*/
                }

                IConstant.DeviceList.AAUTO_POPUP_ID_SWITCH_DEVICE -> {
                    //默认直接切换，无需弹框
                    responseGeneralPopupAa(
                        device,
                        popupId,
                        IConstant.DeviceListResponse.RESPONSE_YES
                    )
                    /*if (wiredSwitchingToastAa != null) {
                        wiredSwitchingToastAa?.onDismiss()
                        wiredSwitchingToastAa = null
                    }
                    wiredSwitchingToastAa = SettingsDmBtnToast(MyApplication.getContext())
                    wiredSwitchingToastAa?.setClickCallback(object :
                        SettingsDmBtnToast.OnConfirmClickCallback {
                        override fun onConfirmClick() {
                            LogUtil.d(TAG, "wiredSwitchingToastAa onConfirmClick : ")
                            responseGeneralPopupAa(
                                device,
                                popupId,
                                IConstant.DeviceListResponse.RESPONSE_YES
                            )
                        }

                        override fun onDismiss() {

                        }
                    })
                    getConnectDevicesAa()?.let {
                        wiredSwitchingToastAa?.showToast(
                            MyApplication.getContext().getString(
                                R.string.dm_aa_to_aa_tips,
                                it.btDeviceName,
                                device.btDeviceName
                            )
                        )
                    }*/
                }

                else -> {}
            }
            for (listener in dmAaListenerMap!!.values) {
                listener.requestGeneralPopup(device, popupId, message)
            }
        }

        override fun requestClearAllPopup(device: AAutoDevice) {
            LogUtil.d(TAG, "requestClearAllPopup aa device $device")
            for (listener in dmAaListenerMap!!.values) {
                listener.requestClearAllPopup(device)
            }
        }

        override fun notifyServiceConnectStatus(isConnected: Boolean) {
            LogUtil.d(TAG, "notifyServiceConnectStatus isConnected $isConnected")
        }

        override fun requestAndroidAutoAConnect(device: AAutoDevice, message: String?): Int {
            LogUtil.d(TAG, "requestAndroidAutoAConnect device $device")
            val aaDevice = getConnectDevicesAa()
            val cpDevice = getConnectDevicesCp()
            val btDevicesList = CarBtManager.instance.getBluetoothConnectedDevicesList()
            LogUtil.i(TAG, "requestAndroidAutoAConnect : aaDevice = $aaDevice , cpDevice = $cpDevice , btDevicesList = ${btDevicesList.size}")
            //插入有线aa,弹框提示用户
            if(aaDevice != null){
                showCpAAWiredConnectTipsDialog(aaDevice.btDeviceName,
                    {
                        LogUtil.i(TAG, "requestAndroidAutoAConnect disconnectDeviceAa ")
                        responseConnectAa(true)
                        connectDeviceAa(device)
                    },
                    {
                        responseConnectAa(false)
                    })
                return IConstant.ConnectionControl.WAIT_CONNECT
            }else if(cpDevice != null){
                showCpAAWiredConnectTipsDialog(cpDevice.deviceName,
                    {
                        LogUtil.i(TAG, "requestAndroidAutoAConnect disconnectDeviceCp ")
                        responseConnectAa(true)
                        connectDeviceAa(device)
                    },
                    {
                        responseConnectAa(false)
                    })
                return IConstant.ConnectionControl.WAIT_CONNECT
            }else if(btDevicesList.size > 1){
                val connectedDevice = btDevicesList.find {
                    it.address == device.btMacAddress
                }
                //已连接的两个设备中没有该aa设备，则弹出替换框
                if(connectedDevice == null){
                    LogUtil.i(TAG, "requestAndroidAutoAConnect showConnectedSelectedDialog ")
                    CarBtManager.instance.showConnectedSelectedDialog(BtDeviceBean(btDevicesList[0]),
                        BtDeviceBean(btDevicesList[1]),{
                            LogUtil.i(TAG, "requestAndroidAutoAConnect replace ${it.device.name}")
                            CarBtManager.instance.disconnectBtPhone(it.device)
                            CarBtManager.instance.disconnectBtMusic(it.device)
                            responseConnectAa(true)
                            connectDeviceAa(device)
                        },{
                            LogUtil.i(TAG, "requestAndroidAutoAConnect replace cancel")
                            responseConnectAa(false)
                        }
                    )
                    return IConstant.ConnectionControl.WAIT_CONNECT
                }else{
                    LogUtil.i(TAG, "requestAndroidAutoAConnect connectDeviceAa1 ")
                    //已连接的两个设备中有该aa设备，则直接连接aa
                    connectDeviceAa(device)
                    return IConstant.ConnectionControl.ALLOW_CONNECT
                }
            }else{
                LogUtil.i(TAG, "requestAndroidAutoAConnect connectDeviceAa2 ")
                connectDeviceAa(device)
                return IConstant.ConnectionControl.ALLOW_CONNECT
            }
        }
    }

    private inner class CpBluetoothListener : BluetoothListener() {
        override fun enableBluetooth() {
            LogUtil.d(TAG, "enableBluetooth")
            CarBtManager.instance.setBluetoothStatus(true)
            //蓝⽛关闭时进⾏有线AA连接，则⾃动打开蓝⽛开关，并弹出toast提示蓝牙已开启
            val list = aaDevicesList.filter {
                (it.connectState.state == ConnectState.CONNECTED.state
                        || it.connectState.state == ConnectState.CONNECTING.state)
                        && it.connectType.type == ConnectType.CONNECT_WIRED.type
            }
            if(list.isNotEmpty()){
                showToast( MyApplication
                    .getContext().getString(R.string.bt_open))
            }
        }

        override fun disableBtConnection(address: String) {
            LogUtil.d(TAG, "disableBtConnection : address $address ")
            if (!isSwitchingCpAa) {
                LogUtil.d(TAG, "disableBtConnection : start disconnect! ")
                disconnectBtOfAddress(address, Contacts.BT_CONNECT_ALL)
            }
        }

        @Throws(RemoteException::class)
        override fun enableBtConnection(address: String) {
            if (!Contacts.isStandbyMode) {
                LogUtil.d(TAG, "enableBtConnection : address $address ")
                if (!isSwitchingCpAa && isReconnectionBt) {
                    LogUtil.d(TAG, "enableBtConnection : start connect! ")
                    connectBtOfAddress(address, Contacts.BT_CONNECT_ALL)
                } else {
                    isReconnectionBt = true
                }
            }
        }

        override fun connectRfcomm(uuid: String?, address: String) {
            LogUtil.d(TAG, "connectRfcomm : address $address ")
        }

        override fun disconnectRfcomm(uuid: String?, address: String?) {
            LogUtil.d(TAG, "disconnectRfcomm : address $address ")
        }

        override fun enableBtA2dp(address: String) {
            LogUtil.d(TAG, "enableBtA2dp : address $address ")
            if (!isSwitchingCpAa && isReconnectionBt) {
                LogUtil.d(TAG, "enableBtA2dp : start connect bt!")
                connectBtOfAddress(address, Contacts.BT_CONNECT_A2DP)
            } else {
                isReconnectionBt = true
            }
        }

        override fun disableBtA2dp(address: String) {
            LogUtil.d(TAG, "disableBtA2dp : address $address ")
            if (!isSwitchingCpAa) {
                LogUtil.d(TAG, "disableBtA2dp : start disconnect bt!")
                disconnectBtOfAddress(address, Contacts.BT_CONNECT_A2DP)
            }
        }
    }

    abstract class CpDeviceListenerNotify : DeviceListener() {
        abstract fun notifyReconnectStatus(isConnected: Boolean)
    }

    open class SettingBinderListener : ISettingBinder.Stub() {
        override fun updateDeviceList(devices: MutableList<AAutoDevice>) {
            LogUtil.d(TAG, "updateDeviceList devices ${devices.size}")
        }

        override fun notifyLinkStateChanged(device: AAutoDevice, state: Int) {
            LogUtil.d(TAG, "notifyLinkStateChanged device $device state $state")

        }

        override fun clearAllPopup(device: AAutoDevice) {
            LogUtil.d(TAG, "clearAllPopup device $device")
        }

        override fun requestGeneralPopup(device: AAutoDevice, popupId: Int, message: String) {
            LogUtil.d(TAG, "requestGeneralPopup device $device message $message")
        }

        override fun notifyServiceConnectStatus(isConnected: Boolean) {
            LogUtil.d(TAG, "notifyServiceConnectStatus isConnected $isConnected")

        }

        override fun requestClearAllPopup(device: AAutoDevice) {
            LogUtil.d(TAG, "requestClearAllPopup device $device")

        }

        override fun requestAndroidAutoAConnect(device: AAutoDevice, message: String?): Int {
            return IConstant.ConnectionControl.ALLOW_CONNECT
        }
    }
}
