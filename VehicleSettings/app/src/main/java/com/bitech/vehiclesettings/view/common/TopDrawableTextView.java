package com.bitech.vehiclesettings.view.common;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;

import androidx.appcompat.widget.AppCompatTextView;

import com.bitech.vehiclesettings.R;

public class TopDrawableTextView extends AppCompatTextView {

    private int mDrawableOffsetX = 0;
    private int mDrawableOffsetY = 0;
    private int mTextOffsetX = 0;
    private int mTextOffsetY = 0;
    private int mCustomDrawablePadding = 0;

    public TopDrawableTextView(Context context) {
        super(context);
        init(context, null);
    }

    public TopDrawableTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public TopDrawableTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.TopDrawableTextView);

            mDrawableOffsetX = a.getDimensionPixelSize(
                    R.styleable.TopDrawableTextView_drawableOffsetX, 0);
            mDrawableOffsetY = a.getDimensionPixelSize(
                    R.styleable.TopDrawableTextView_drawableOffsetY, 0);

            mTextOffsetX = a.getDimensionPixelSize(
                    R.styleable.TopDrawableTextView_textOffsetX, 0);
            mTextOffsetY = a.getDimensionPixelSize(
                    R.styleable.TopDrawableTextView_textOffsetY, 0);

            mCustomDrawablePadding = a.getDimensionPixelSize(
                    R.styleable.TopDrawableTextView_customDrawablePadding, 0);

            a.recycle();
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        // 先绘制背景
        Drawable background = getBackground();
        if (background != null) {
            background.draw(canvas);
        }

        Drawable[] drawables = getCompoundDrawables();
        Drawable topDrawable = drawables[1];

        // 临时移除top drawable避免被父类绘制
        setCompoundDrawables(drawables[0], null, drawables[2], drawables[3]);

        // 保存canvas状态
        canvas.save();

        // 应用文本偏移
        canvas.translate(mTextOffsetX, mTextOffsetY);

        // 绘制文本
        super.onDraw(canvas);

        // 恢复canvas状态
        canvas.restore();

        // 绘制drawable
        if (topDrawable != null) {
            // 计算drawable位置
            int drawableLeft = getPaddingLeft() + mDrawableOffsetX;
            int drawableTop = getPaddingTop() + mDrawableOffsetY;

            // 考虑gravity设置
            int textWidth = (int) getPaint().measureText(getText().toString());
            int availableWidth = getWidth() - getPaddingLeft() - getPaddingRight();

            if ((getGravity() & Gravity.HORIZONTAL_GRAVITY_MASK) == Gravity.CENTER_HORIZONTAL) {
                drawableLeft = (availableWidth - Math.max(textWidth, topDrawable.getIntrinsicWidth())) / 2
                        + getPaddingLeft() + mDrawableOffsetX;
            } else if ((getGravity() & Gravity.HORIZONTAL_GRAVITY_MASK) == Gravity.RIGHT) {
                drawableLeft = getWidth() - getPaddingRight() - topDrawable.getIntrinsicWidth()
                        + mDrawableOffsetX;
            }

            // 设置drawable边界
            topDrawable.setBounds(
                    drawableLeft,
                    drawableTop,
                    drawableLeft + topDrawable.getIntrinsicWidth(),
                    drawableTop + topDrawable.getIntrinsicHeight()
            );

            // 绘制drawable
            canvas.save();
            topDrawable.draw(canvas);
            canvas.restore();
        }

        // 恢复原来的drawables
        setCompoundDrawables(drawables[0], topDrawable, drawables[2], drawables[3]);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        Drawable topDrawable = getCompoundDrawables()[1];
        if (topDrawable != null) {
            // 获取文本高度（包括padding）
            Paint.FontMetrics fm = getPaint().getFontMetrics();
            int textHeight = (int) Math.ceil(fm.descent - fm.ascent);

            // 计算总高度 = drawable高度 + 间距 + 文本高度 + 上下padding
            int totalHeight = topDrawable.getIntrinsicHeight()
                    + Math.max(mCustomDrawablePadding, getCompoundDrawablePadding())
                    + textHeight
                    + getPaddingTop()
                    + getPaddingBottom()
                    + Math.max(mDrawableOffsetY, mTextOffsetY);

            // 确保高度足够
            int measuredHeight = resolveSize(totalHeight, heightMeasureSpec);
            setMeasuredDimension(getMeasuredWidth(), measuredHeight);
        }
    }

    // 以下是设置偏移量的方法
    public void setDrawableOffsetX(int offset) {
        this.mDrawableOffsetX = offset;
        invalidate();
    }

    public void setDrawableOffsetY(int offset) {
        this.mDrawableOffsetY = offset;
        invalidate();
    }

    public void setTextOffsetX(int offset) {
        this.mTextOffsetX = offset;
        invalidate();
    }

    public void setTextOffsetY(int offset) {
        this.mTextOffsetY = offset;
        invalidate();
    }

    public void setCustomDrawablePadding(int padding) {
        this.mCustomDrawablePadding = padding;
        invalidate();
        requestLayout();
    }
}