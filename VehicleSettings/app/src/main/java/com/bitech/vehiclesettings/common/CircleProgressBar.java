package com.bitech.vehiclesettings.common;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.AccelerateInterpolator;

import androidx.annotation.Nullable;

import java.text.DecimalFormat;

public class CircleProgressBar extends View {
    private Paint paintCircleBottom = new Paint();
    private Paint paintArcTop = new Paint();
    private Paint paintText = new Paint();

    private int DEFAULT_WIDTH = 100;//默认宽度
    private int DEFAULT_HEIGHT = 100;//默认宽度
    private int DEFAULT_RADIUS = 50;//默认半径

    private float _angle;//弧形的角度

    public CircleProgressBar(Context context) {
        super(context);
        init();
    }

    public CircleProgressBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CircleProgressBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init(){
        //初始化文本的画笔
        paintText.setFlags(Paint.ANTI_ALIAS_FLAG);
        paintText.setColor(Color.BLACK);
        paintText.setTextAlign(Paint.Align.CENTER);
        paintText.setTextSize(80f);

        //初始化底层圆形的画笔
        paintCircleBottom.setFlags(Paint.ANTI_ALIAS_FLAG);
        paintCircleBottom.setColor(Color.LTGRAY);
        paintCircleBottom.setStrokeWidth(10f);
        paintCircleBottom.setStrokeCap(Paint.Cap.ROUND);
        paintCircleBottom.setStyle(Paint.Style.STROKE);

        //初始化弧形的画笔
        paintArcTop.setFlags(Paint.ANTI_ALIAS_FLAG);
        paintArcTop.setColor(Color.MAGENTA);
        paintArcTop.setStrokeWidth(10f);
        paintArcTop.setStrokeCap(Paint.Cap.ROUND);
        paintArcTop.setStyle(Paint.Style.STROKE);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);

        if (widthMode == MeasureSpec.AT_MOST && heightMode == MeasureSpec.AT_MOST) {
            setMeasuredDimension(DEFAULT_WIDTH, DEFAULT_HEIGHT);
        } else if (widthMode == MeasureSpec.AT_MOST) {
            setMeasuredDimension(DEFAULT_WIDTH, heightMeasureSpec);
        } else if (heightMode == MeasureSpec.AT_MOST) {
            setMeasuredDimension(widthMeasureSpec, DEFAULT_HEIGHT);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        // 圆心坐标是(centerX,centerY)
        int centerX = getWidth()/2;
        int centerY = getHeight()/2;
        //确定半径
        float radius = Math.min(centerX, centerY) - paintCircleBottom.getStrokeWidth();


        //绘制底层圆形
        canvas.drawCircle(centerX, centerY, radius, paintCircleBottom);


        //绘制上层弧形,从顶部开始，顺时针走90°
        canvas.drawArc(centerX - radius,centerY-radius,centerX + radius,centerY + radius,270, _angle,false, paintArcTop);

        //绘制文本,文字中心和圆心保持一致
        DecimalFormat dt = new DecimalFormat("0.##");
        Paint.FontMetrics fontMetrics = paintText.getFontMetrics();
        float distance =(fontMetrics.bottom - fontMetrics.top)/2 - fontMetrics.bottom;
        float baseline= centerY + distance;
        canvas.drawText(dt.format(100 * _angle/360)+"%", centerX, baseline, paintText);//文字中心和圆心一致
    }

    /**
     * 设置进度，展现动画
     * */
    public void setProgress(int progress){
        ValueAnimator animator = ValueAnimator.ofFloat(0,100f);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float cur = (float) animation.getAnimatedValue();
                _angle = cur/100 * 360 * progress/100;
                invalidate();
            }
        });
        animator.setDuration(3000);
        animator.setInterpolator(new AccelerateInterpolator());
        animator.start();
    }
}