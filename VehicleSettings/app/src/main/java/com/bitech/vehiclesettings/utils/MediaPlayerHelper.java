package com.bitech.vehiclesettings.utils;

import android.media.MediaPlayer;
import android.content.Context;

public class MediaPlayerHelper {

    private MediaPlayer mediaPlayer;
    private Context context;

    public MediaPlayerHelper(Context context) {
        this.context = context;
    }

    public void playRawResource(int resourceId) {
        if (mediaPlayer != null) {
            mediaPlayer.release(); // 释放旧的MediaPlayer资源
        }
        mediaPlayer = MediaPlayer.create(context, resourceId); // 创建新的MediaPlayer
        mediaPlayer.start(); // 播放音乐
    }

    public void stopPlaying() {
        if (mediaPlayer != null) {
            mediaPlayer.stop(); // 停止播放
            mediaPlayer.release(); // 释放资源
            mediaPlayer = null;
        }
    }

    public void repeatPlay(){
        if (mediaPlayer != null){
            mediaPlayer.setOnCompletionListener(mediaPlayer -> {
                mediaPlayer.start();
            });
        }
    }
}
