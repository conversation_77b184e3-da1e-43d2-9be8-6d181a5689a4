package com.bitech.vehiclesettings.service;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.interfaces.condition.IConditionManagerListener;
import com.bitech.platformlib.manager.ConditionManager;
import com.bitech.vehiclesettings.widget.TirePressureWidget;

import java.util.concurrent.atomic.AtomicInteger;

public class ConditionLifeCycle implements LifecycleEventObserver {
    private static final String TAG = ConditionLifeCycle.class.getName();
    private final VehicleServiceHandler handler;
    private final LifecycleOwner lifecycleOwner;
    private ConditionManager conditionManager;

    // value
    public static final AtomicInteger leftFrontTyrePressure = new AtomicInteger(Integer.MIN_VALUE);
    public static final AtomicInteger rightFrontTyrePressure = new AtomicInteger(Integer.MIN_VALUE);
    public static final AtomicInteger leftRearTyrePressure = new AtomicInteger(Integer.MIN_VALUE);
    public static final AtomicInteger rightRearTyrePressure = new AtomicInteger(Integer.MIN_VALUE);
    public static final AtomicInteger leftFrontTyrePressureStatus = new AtomicInteger(Integer.MIN_VALUE);
    public static final AtomicInteger rightFrontTyrePressureStatus = new AtomicInteger(Integer.MIN_VALUE);
    public static final AtomicInteger leftRearTyrePressureStatus = new AtomicInteger(Integer.MIN_VALUE);
    public static final AtomicInteger rightRearTyrePressureStatus = new AtomicInteger(Integer.MIN_VALUE);

    public ConditionLifeCycle(VehicleServiceHandler handler, LifecycleOwner lifecycleOwner) {
        this.handler = handler;
        this.lifecycleOwner = lifecycleOwner;
    }


    @Override
    public void onStateChanged(@NonNull LifecycleOwner lifecycleOwner, @NonNull Lifecycle.Event event) {
        switch (event) {
            case ON_CREATE:
                Log.d(TAG, "onCreate: " + lifecycleOwner);
                Log.d(TAG, TAG);
                conditionManager = (ConditionManager) BitechCar.getInstance()
                        .getServiceManager(BitechCar.CAR_CONDITION_MANAGER);
                if (conditionManager != null) {
                    conditionManager.addCallback(TAG, msgCallback);
                    conditionManager.registerListener();
                }
                leftFrontTyrePressure.set(conditionManager.getLeftFrontTireKpaNum());
                rightFrontTyrePressure.set(conditionManager.getRightFrontTireKpaNum());
                leftRearTyrePressure.set(conditionManager.getLeftRearTireKpaNum());
                rightRearTyrePressure.set(conditionManager.getRightRearTireKpaNum());
                leftFrontTyrePressureStatus.set(conditionManager.getLeftFrontTireStatus() == Integer.MIN_VALUE ? 0 : conditionManager.getLeftFrontTireStatus());
                rightFrontTyrePressureStatus.set(conditionManager.getRightFrontTireStatus() == Integer.MIN_VALUE ? 0 : conditionManager.getRightFrontTireStatus());
                leftRearTyrePressureStatus.set(conditionManager.getLeftRearTireStatus() == Integer.MIN_VALUE ? 0 : conditionManager.getLeftRearTireStatus());
                rightRearTyrePressureStatus.set(conditionManager.getRightRearTireStatus() == Integer.MIN_VALUE ? 0 : conditionManager.getRightRearTireStatus());
                notifyConditionWidget();
                break;
            case ON_DESTROY:
                conditionManager.removeCallback(TAG);
                break;
            default:
                break;
        }
    }

    private void notifyConditionWidget() {
        Log.d(TAG, "notifyConditionWidget: ");
        Context context = (Context) lifecycleOwner;
        context.sendBroadcast(new Intent(TirePressureWidget.ACTION_TIRE_PRESSURE_UPDATE)
                .setClassName(context, TirePressureWidget.class.getName()).setPackage(context.getPackageName()));
    }

    private IConditionManagerListener msgCallback = new IConditionManagerListener() {
        @Override
        public void onConfigChanged(boolean success) {

        }

        @Override
        public void onLeftFrontTyrePressureChanged(int pressure) {
            if (pressure == Integer.MIN_VALUE) return;
            leftFrontTyrePressure.set(pressure);
            notifyConditionWidget();
        }

        @Override
        public void onRightFrontTyrePressureChanged(int pressure) {
            if (pressure == Integer.MIN_VALUE) return;
            rightFrontTyrePressure.set(pressure);
            notifyConditionWidget();
        }

        @Override
        public void onLeftRearTyrePressureChanged(int pressure) {
            if (pressure == Integer.MIN_VALUE) return;
            leftRearTyrePressure.set(pressure);
            notifyConditionWidget();
        }

        @Override
        public void onRightRearTyrePressureChanged(int pressure) {
            if (pressure == Integer.MIN_VALUE) return;
            rightRearTyrePressure.set(pressure);
            notifyConditionWidget();
        }

        @Override
        public void LeftFrontTyrePressureStatusCallback(int signalVal) {
            if (signalVal == Integer.MIN_VALUE) return;
            leftFrontTyrePressureStatus.set(signalVal);
            notifyConditionWidget();
        }

        @Override
        public void onRightFrontTyrePressureStatus(int signalVal) {
            if (signalVal == Integer.MIN_VALUE) return;
            rightFrontTyrePressureStatus.set(signalVal);
            notifyConditionWidget();
        }

        @Override
        public void onLeftRearTyrePressureStatus(int signalVal) {
            if (signalVal == Integer.MIN_VALUE) return;
            leftRearTyrePressureStatus.set(signalVal);
            notifyConditionWidget();
        }

        @Override
        public void onRightRearTyrePressureStatus(int signalVal) {
            if (signalVal == Integer.MIN_VALUE) return;
            rightRearTyrePressureStatus.set(signalVal);
            notifyConditionWidget();
        }
    };
}
