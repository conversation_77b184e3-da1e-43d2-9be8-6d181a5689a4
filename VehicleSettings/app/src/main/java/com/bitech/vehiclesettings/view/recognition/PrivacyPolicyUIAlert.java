package com.bitech.vehiclesettings.view.recognition;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;

public class PrivacyPolicyUIAlert extends Dialog {
    private static final String TAG = CameraSwitchOnUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;

    public PrivacyPolicyUIAlert(@NonNull Context context) {
        super(context);
    }

    public PrivacyPolicyUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PrivacyPolicyUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        PrivacyPolicyUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private PrivacyPolicyUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public PrivacyPolicyUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PrivacyPolicyUIAlert(context,
                        R.style.Dialog);
            layout = View.inflate(context, R.layout.dialog_alert_r_privacy_policy, null);
            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1656; // 或者使用具体的像素值
            layoutParams.height = 942;
            window.setAttributes(layoutParams);

            return dialog;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }
}
