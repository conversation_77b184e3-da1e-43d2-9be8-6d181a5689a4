package com.bitech.vehiclesettings.manager

import android.car.CarInfoManager
import android.car.hardware.property.CarPropertyManager
import android.content.ContentResolver

import android.content.pm.IPackageDataObserver
import android.provider.Settings
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.LogUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * @ClassName: CarSystemInfoManager
 * 
 * @Date:  2024/3/8 14:42
 * @Description: 系统设置管理类.
 **/
class CarSystemInfoManager {

    // 蓝牙属性管理对象
    private var carBtManager: CarBtManager? = null

    // WIFI管理对象
    private var carWifiManager: CarWifiManager? = null

    // 车辆属性管理对象
    private var carPropertyManager: CarPropertyManager? = null

    // 车辆信息管理对象
    private var carInfoManager: CarInfoManager? = null

    // 应用数据清除订阅对象
    private var clearUserDataObserver: ClearUserDataObserver? = null

    /**
     * 初始化显示设置.
     *
     */
    fun initCarSystemInfoManager() {
        LogUtil.d(TAG, "initCarSystemInfoManager : ")
        carPropertyManager = CarBaseManager.getCarPropertyManager()
        GlobalScope.launch(Dispatchers.Default) {
            carInfoManager = CarBaseManager.getCarInfoManager()
            carBtManager = CarBtManager.instance
            carWifiManager = CarWifiManager.instance
        }
    }


    private fun compareToGMT(utc: String):Int{
        var state = 0
        if(utc.contains("GMT",true)){
            state = 100
        }else{
            LogUtil.i(TAG, "noGMT : utc = $utc")
            state = -25
        }
        return state
    }

    private fun compareToTime(utc: String):Int{
        if(utc.contains("GMT",true)){
            if(utc.length<6){
                return 0
            }
            if(utc.length<9){
                return utc.substring(3,6).toInt()*100
            }
            return utc.substring(3,6).toInt()*100 + utc.substring(7,9).toInt()
        }
        return -25
    }

    /**
     * 获取系统赶时间
     *
     * @param contentResolver
     * @return 时间制
     */
    fun getHour12or24(contentResolver: ContentResolver?): String {
        val hourSystem = Settings.System.getString(contentResolver, Settings.System.TIME_12_24)
        LogUtil.i(TAG, "getHour12or24 : hourSystem = $hourSystem")
        return hourSystem
    }

    /**
     * 设置系统赶时间
     *
     * @param contentResolver ContentResolver
     * @param hourSystem 时间制
     */
    fun setHour12or24(contentResolver: ContentResolver?, hourSystem: String) {
        LogUtil.i(TAG, "setHour12or24 : hourSystem = $hourSystem")
        Settings.System.putString(contentResolver, Settings.System.TIME_12_24, hourSystem)
    }

//    /**
//     * 设置ICM时间制.
//     *
//     * @param hourSystem 时间制
//     */
//    fun setHour12or24Icm(hourSystem: Int) {
//        LogUtil.i(TAG, "setHourIcm : hourSystem = $hourSystem")
//        carPropertyManager?.setIntProperty(YFVehiclePropertyIds.IPC_TIME_FORMAT_SET, 0, hourSystem)
//    }



    /**
     * 恢复出厂设置蓝牙.
     *
     */
    private fun factoryResetBt() {
        LogUtil.d(TAG, "factoryResetBt : ")
        carBtManager?.factoryRecoveryBt()
    }

    /**
     * 恢复出厂设置WIFI.
     *
     */
    private fun factoryResetWifi() {
        LogUtil.d(TAG, "factoryResetWifi : ")
        // wifi关闭
        carWifiManager?.setWifiState(false)
        // 清空已WIFI配置信息
        carWifiManager?.deleteAllWifiConfig()
        // 网络通知开关关闭
        carWifiManager?.setNetWorkNotification(false)
        // 打开热点
        carWifiManager?.openWifiHotspot()
    }

    /**
     * 恢复出厂设置TBox.
     *
     */
    private fun factoryResetSystemTBox() {
        LogUtil.d(TAG, "factoryResetSystemTBox : ")
        // TODO: 暂未接入TBox
    }

    /**
     * 将时间转换为ICM格式时间.
     *
     * @param time 时间，毫秒级别
     */
    private fun timeToIcmTime(time: Long): String {
        // 构建时间转换格式，更新ICM时间
        val format = SimpleDateFormat(Contacts.ICM_TIME_FORMAT, Locale.getDefault())
        val icmTime = format.format(time)
        LogUtil.i(TAG, "updateGpsTime : icm time = $icmTime")
        return icmTime
    }

    /**
     * 释放.
     *
     */
    fun unInitCarSystemInfoManager() {
        LogUtil.d(TAG, "unInitCarSystemInfoManager : ")
        carInfoManager = null
        carPropertyManager = null
        carBtManager = null
        carWifiManager = null
    }

    /**
     * @ClassName: ClearUserDataObserver
     * 
     * @Date:  2024/4/19 15:38
     * @Description: 清除应用用户数据定义类.
     **/
    class ClearUserDataObserver : IPackageDataObserver.Stub() {
        /**
         * 当应用数据移除完成时.
         *
         * @param packageName 应用包名
         * @param isSucceeded 是否成功
         */
        override fun onRemoveCompleted(packageName: String, isSucceeded: Boolean) {
            LogUtil.i(
                TAG, "onRemoveCompleted : " +
                        "packageName = $packageName, " +
                        "isSucceeded = $isSucceeded"
            )
        }
    }

    companion object {
        // 日志标志位
        private const val TAG = "CarSystemInfoManager"

        // ICM时间KEY
        private const val ICM_TIME_KEY = "Time"

        // 正则表达式
        private const val PATTERN = "GB"

        // android原生时区id过滤
        private const val TIME_ZONE_TAG = "/"
        private const val TIME_ZONE_PREFIX = "Etc/"

        // 单例对象
        val instance: CarSystemInfoManager by lazy(LazyThreadSafetyMode.PUBLICATION) {
            CarSystemInfoManager()
        }
    }
}
