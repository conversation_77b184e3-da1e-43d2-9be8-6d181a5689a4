package com.bitech.vehiclesettings.view.quickcontrol;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

/**
 * 对话框
 */
public class NfcUIAlert extends BaseDialog {
    private static final String TAG = NfcUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public NfcUIAlert(Context context) {
        super(context);
    }

    public NfcUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected NfcUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static NfcUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(NfcUIAlert.onProgressChangedListener onProgressChangedListener) {
        NfcUIAlert.onProgressChangedListener = onProgressChangedListener;
    }


    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private View rlNfcItem1;
        private boolean nfcItem1Flag = false;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private NfcUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public NfcUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new NfcUIAlert(context,
                        R.style.Dialog);
            }
            layout = View.inflate(context, R.layout.dialog_alert_q_nfc, null);


            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            layoutParams.height = 800;
            window.setAttributes(layoutParams);
            rlNfcItem1 = layout.findViewById(R.id.rl_nfc_item1);
            selNfc();
            rlNfcItem1.setOnClickListener(v -> {
                selNfc();
            });
            return dialog;
        }

        private void selNfc() {
            ImageView ivNfc = layout.findViewById(R.id.sw_condition_nfc);
            ImageView ivEdit = layout.findViewById(R.id.sw_condition_edit);
            nfcItem1Flag = !nfcItem1Flag;
            ivNfc.setSelected(nfcItem1Flag);
            ivEdit.setSelected(nfcItem1Flag);
            rlNfcItem1.setSelected(nfcItem1Flag);
        }
    }


    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }

}
