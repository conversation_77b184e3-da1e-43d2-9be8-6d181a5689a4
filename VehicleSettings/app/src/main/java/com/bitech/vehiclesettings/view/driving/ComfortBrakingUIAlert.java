package com.bitech.vehiclesettings.view.driving;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.databinding.DialogAlertDComfortBrakingBinding;
import com.bitech.vehiclesettings.presenter.driving.DrivingAnime;
import com.bitech.vehiclesettings.utils.GrayEffectHelper;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class ComfortBrakingUIAlert extends BaseDialog {
    private static final String TAG = ComfortBrakingUIAlert.class.getSimpleName();
    private static ComfortBrakingUIAlert.onProgressChangedListener onProgressChangedListener;
    private DrivingAnime drivingAnime;


    public ComfortBrakingUIAlert(@NonNull Context context) {
        super(context);
    }

    public ComfortBrakingUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ComfortBrakingUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static ComfortBrakingUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(ComfortBrakingUIAlert.onProgressChangedListener onProgressChangedListener) {
        ComfortBrakingUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private ComfortBrakingUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertDComfortBrakingBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private ComfortBrakingUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public ComfortBrakingUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public ComfortBrakingUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new ComfortBrakingUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertDComfortBrakingBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128;
            layoutParams.height = 800;
            window.setAttributes(layoutParams);

            binding.spvComfortBrakingRank.setItems(R.string.str_comfort_braking_mode_1, R.string.str_comfort_braking_mode_2, R.string.str_comfort_braking_mode_3);

            binding.swComfortBraking.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked) {
                    onProgressChangedListener.onSwitch(true);
                    updateComfortBrakingUI(CarDriving.CST_Status.ACTIVE);
                } else {
                    onProgressChangedListener.onSwitch(false);
                    updateComfortBrakingUI(CarDriving.CST_Status.DISABLED);
                }
            });

            binding.spvComfortBrakingRank.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    if (index == 0) {
                        onProgressChangedListener.onComfortBrakingRank(CarDriving.CST_SensitivityReq.LOW);
                    } else if (index == 1) {
                        onProgressChangedListener.onComfortBrakingRank(CarDriving.CST_SensitivityReq.MEDIUM);
                    } else if (index == 2) {
                        onProgressChangedListener.onComfortBrakingRank(CarDriving.CST_SensitivityReq.HIGH);
                    }
                }

                public void onItemClicked(int index, String text) {

                }
            });
            initData();
            return dialog;
        }

        private void initData() {
            updateComfortBrakingRankUI(onProgressChangedListener.getComfortBrakingRank(), false);
            updateComfortBrakingUI(onProgressChangedListener.getComfortBraking());
        }

        public void updateComfortBrakingRankUI(Integer signalVal) {
            updateComfortBrakingRankUI(signalVal, true);
        }

        public void updateComfortBrakingRankUI(Integer signalVal, boolean flag) {
            boolean isEnable = binding.spvComfortBrakingRank.isEnabled();
            binding.spvComfortBrakingRank.setEnabled(true);
            if (signalVal == CarDriving.CST_SensitivitySts.LOW) {
                binding.spvComfortBrakingRank.setSelectedIndex(0, flag);
            }
            else if (signalVal == CarDriving.CST_SensitivitySts.MEDIUM) {
                binding.spvComfortBrakingRank.setSelectedIndex(1, flag);
            }
            else if (signalVal == CarDriving.CST_SensitivitySts.HIGH) {
                binding.spvComfortBrakingRank.setSelectedIndex(2, flag);
            }
            binding.spvComfortBrakingRank.setEnabled(isEnable);
            if (!isEnable) {
                binding.spvComfortBrakingRank.setAlpha(0.5f);
            }
        }

        public void updateComfortBrakingUI(Integer signalVal) {
            if (signalVal == CarDriving.CST_Status.ACTIVE || signalVal == CarDriving.CST_Status.STANDBY) {
                binding.swComfortBraking.setChecked(true);
                binding.spvComfortBrakingRank.setEnabled(true);
            } else {
                binding.swComfortBraking.setChecked(false);
                binding.spvComfortBrakingRank.setEnabled(false);
                binding.spvComfortBrakingRank.setAlpha(0.5f);
            }
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);

        void onComfortBrakingRank(int status);

        int getComfortBraking();

        int getComfortBrakingRank();
    }
}
