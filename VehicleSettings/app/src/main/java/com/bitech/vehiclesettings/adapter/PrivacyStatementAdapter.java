package com.bitech.vehiclesettings.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.PrivacyStatementBean;

import java.util.List;

public class PrivacyStatementAdapter extends RecyclerView.Adapter<PrivacyStatementAdapter.PermissionViewHolder> {

    private final Context context;
    private final List<PrivacyStatementBean> items;
    private OnItemClickListener listener;

    // 默认宽高
    private int itemWidth = ViewGroup.LayoutParams.MATCH_PARENT;
    private int itemHeight = (int) TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP, 86.67f, getSafeDisplayMetrics());

    // 新增：默认字体大小（单位：px）
    private float textSizePx = -1; // -1 表示使用 XML 默认值

    public interface OnItemClickListener {
        void onItemClick(PrivacyStatementBean item, int position);
    }

    public PrivacyStatementAdapter(Context context, List<PrivacyStatementBean> items) {
        this.context = context;
        this.items = items;
    }

    // 安全获取 DisplayMetrics
    private DisplayMetrics getSafeDisplayMetrics() {
        if (context == null || context.getResources() == null) {
            return new DisplayMetrics(); // 返回默认值
        }
        return context.getResources().getDisplayMetrics();
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.listener = listener;
    }

    // 设置 item 宽度（像素）
    public void setItemWidth(int widthPx) {
        this.itemWidth = widthPx;
        notifyDataSetChanged();
    }

    // 设置 item 高度（像素）
    public void setItemHeight(int heightPx) {
        this.itemHeight = heightPx;
        notifyDataSetChanged();
    }

    // 设置 item 宽度（dp）
    public void setItemWidthDp(float widthDp) {
        this.itemWidth = (int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, widthDp, getSafeDisplayMetrics());
        notifyDataSetChanged();
    }

    // 设置 item 高度（dp）
    public void setItemHeightDp(float heightDp) {
        this.itemHeight = (int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, heightDp, getSafeDisplayMetrics());
        notifyDataSetChanged();
    }

    // 新增：设置字体大小（像素）
    public void setTextSizePx(float textSizePx) {
        this.textSizePx = textSizePx;
        notifyDataSetChanged();
    }

    // 新增：设置字体大小（dp）
    public void setTextSizeDp(float textSizeDp) {
        this.textSizePx = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, textSizeDp, getSafeDisplayMetrics());
        notifyDataSetChanged();
    }

    // 新增：设置字体大小（sp）
    public void setTextSizeSp(float textSizeSp) {
        this.textSizePx = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_SP, textSizeSp, getSafeDisplayMetrics());
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public PermissionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_privacy_statement, parent, false);
        return new PermissionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PermissionViewHolder holder, int position) {
        PrivacyStatementBean item = items.get(position);
        holder.tvPermission.setText(item.getTitle());

        // 设置 item 宽高
        ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new ViewGroup.LayoutParams(itemWidth, itemHeight);
        } else {
            layoutParams.width = itemWidth;
            layoutParams.height = itemHeight;
        }
        holder.itemView.setLayoutParams(layoutParams);

        // 新增：设置字体大小（如果 textSizePx > 0）
        if (textSizePx > 0) {
            holder.tvPermission.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSizePx);
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null && holder.getAdapterPosition() != RecyclerView.NO_POSITION) {
                listener.onItemClick(item, holder.getAdapterPosition());
            }
        });

        Log.d("PrivacyAdapter", "Binding item: " + item.getTitle());
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public static class PermissionViewHolder extends RecyclerView.ViewHolder {
        TextView tvPermission;

        public PermissionViewHolder(@NonNull View itemView) {
            super(itemView);
            tvPermission = itemView.findViewById(R.id.tv_permission_text);
        }
    }
}