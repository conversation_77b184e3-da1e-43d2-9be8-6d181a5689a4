package com.bitech.vehiclesettings.utils;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.LayoutRes;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.R;
import com.orhanobut.logger.Logger;

import java.lang.ref.WeakReference;

/**
 * Created by LvMeng on 15/2/9.
 */
public class EToast extends Toast {
    private static final String TAG = EToast.class.getSimpleName();
    private static WeakReference<Toast> toastRef;

    /**
     * Construct an empty Toast object.  You must call {@link #setView} before you
     * can call {@link #show}.
     *
     * @param context The context to use.  Usually your {@link Application}
     *                or {@link Activity} object.
     */
    public EToast(Context context) {
        super(context);
    }

    public static void show(Context context, String msg) {
        Toast toast = Toast.makeText(context, msg, Toast.LENGTH_SHORT);
        toast.setText(msg);
        toast.setGravity(Gravity.TOP, 0, 0);
        toast.show();
    }

    public static void showToastCustom(Context activity, CharSequence text, int time, boolean waring) {

        showToast(activity, text, time, waring, R.layout.toast_custom_view, Gravity.TOP | Gravity.START, 53, 8);
    }

    public static void showToast(Context activity, CharSequence text, int time, boolean waring, @LayoutRes int resource, int gravity, int xOffset, int yOffset) {
        if (TextUtils.isEmpty(text) || TextUtils.isEmpty(text.toString().trim())) {
            Logger.d(TAG, "text is null, toast return");
            return;
        }
        Logger.d(TAG, "show Toast = " + text + "  " + time);
        cancelToast();

        View mView = LayoutInflater.from(activity).inflate(resource, null);
        LinearLayout toastLL = mView.findViewById(R.id.rl_toast_bg);
        TextView mTvToastMsg = mView.findViewById(R.id.toast_text);
        mTvToastMsg.setText(text);
        Toast toast = EToast.makeText(activity, text, time);
        toast.setGravity(gravity, xOffset, yOffset);
        toast.setView(mView);
        toast.show();
        toastRef = new WeakReference<>(toast);

    }


    public static void showToast(Context activity, CharSequence text, int time, boolean waring) {
        showToast(activity, text, time, waring, R.layout.toast_view, Gravity.TOP | Gravity.FILL_HORIZONTAL, 0, 8);
    }

    public static void cancelToast() {
        if (toastRef!=null && toastRef.get()!=null) {
            toastRef.get().cancel();
        }
    }

    public static void showErrot(Context context, String msg) {
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
    }
}
