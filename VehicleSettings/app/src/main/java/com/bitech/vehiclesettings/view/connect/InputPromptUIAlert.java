package com.bitech.vehiclesettings.view.connect;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

/**
 * FileName: NoTitleUIAlert
 * Author: WUY1WHU
 * Date: 2024/6/19 10:29
 * Description:密码输入框
 */
public class InputPromptUIAlert extends BaseDialog {

    private static onProgressChangedListener onProgressChangedListener;

    public InputPromptUIAlert(Context context) {
        super(context);
    }

    public InputPromptUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected InputPromptUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static InputPromptUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(InputPromptUIAlert.onProgressChangedListener onProgressChangedListener) {
        InputPromptUIAlert.onProgressChangedListener = onProgressChangedListener;
    }


    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private String pwdText;
        private InputPromptUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public Builder setPwd(String text) {
            this.pwdText = text;
            return this;
        }

        public String getPdw() {
            return this.pwdText;
        }


        public Builder setConfirmAndCancel(boolean flag) {

            return this;
        }

        /**
         * Create the custom dialog
         */
        public InputPromptUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new InputPromptUIAlert(context,
                        R.style.Dialog);
            }
            View layout = View.inflate(context, R.layout.dialog_alert_input_prompt, null);
            TextView tvPwd = layout.findViewById(R.id.et_passwordInput);
            //context.setText(pwdText);

            TextView confirmBtn = layout.findViewById(R.id.tv_simply_confirm);
            confirmBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (tvPwd.getText() != null)
                        getOnProgressChangedListener().onProgress(v.getId(), (String) tvPwd.getText().toString());
                }
            });
            TextView cancelBtn = layout.findViewById(R.id.tv_simply_cancel);
            cancelBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (tvPwd.getText() != null)
                        getOnProgressChangedListener().onProgress(v.getId(), (String) tvPwd.getText().toString());
                }
            });
            dialog.setCancelable(isCan);

            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);
            return dialog;
        }

    }

    public interface onProgressChangedListener {
        void onProgress(int progress, String pwd);
    }

}
