package com.bitech.vehiclesettings.utils;

public class CommonConst {
    public static final String BOOT_COMPLETED_ACTION = "android.intent.action.BOOT_COMPLETED";
    public static final String AUTOFLY_ACTION = "com.iflytek.autofly.handMessage";
    public static final int COLOR_POS_MAX = 5;
    public static final String ACTION_UI_UPDATE = "ACTION_UI_UPDATE";

    public static final String ACTION_ISSCROLLING_UP = "isScrollingUp";
    public static final String ACTION_ISSCROLLING_DOWN = "isScrollingDown";

    public static final String TARGET_TAB = "target_tab";
    public static final String TARGET_DIALOG = "target_dialog";
    public static final String OPERATION = "operation";
    public static final int DIALOG_OPEN = 1;
    public static final int DIALOG_CLOSE = 0;
    public static final int INVALID_DIALOG = -1;


    public static final int RED = 0;
    public static final int ORANGE = 1;
    public static final int YELLOW = 2;
    public static final int GREEN = 3;
    public static final int YOUNG = 4;
    public static final int BLUE = 5;
    public static final int PURPLE = 6;

    public static final int MULTI_COLOR_1 = 7;
    public static final int MULTI_COLOR_2 = 8;
    public static final int MULTI_COLOR_3 = 9;
    public static final int MULTI_COLOR_4 = 10;
    public static final int MULTI_COLOR_5 = 11;

    public static final int OTHERS = 99;

    public static final String SETTINGS_NAME = "vehiclesettings";
    public static final int LIGHT_MODE_STATIC = 1;
    public static final int LIGHT_MODE_BREATHING = 2;
    public static final int LIGHT_MODE_GRADIENT = 3;
    public static final int LIGHT_MODE_MUSIC_RHYTHM = 4;

    public static final int LIGHT_SW_ALL = 0;
    public static final int LIGHT_SW_FRONT = 1;
    public static final int LIGHT_SW_REAR = 2;

    public static final int POS_0 = 0;
    public static final int POS_1 = 1;

    public static final int OPEN = 1;
    public static final int CLOSE = 0;

    public static final int TAB_0 = 0;
    public static final int TAB_1 = 1;
    public static final int TAB_2 = 2;
    public static final int TAB_3 = 3;
    public static final int TAB_4 = 4;
    public static final int TAB_5 = 5;

    /**
     * 灯光预警
     */
    public static class Triggered {
        public static final int YES = 1;
        public static final int NO = 0;
    }

    /**
     * 氛围灯开关
     */
    public static class AtmosphereSwitch {
        public static final int OPEN = 1;
        public static final int CLOSE = 0;
    }

    /**
     * 氛围灯开关
     */
    public static class pressed {
        public static final int YES = 1;
        public static final int NO = 0;
    }

    /**
     * 氛围灯开关
     */
    public static class Theme {
        public static final int THEME = 0;
        public static final int FRONT = 1;
        public static final int REAR = 2;
    }

    /**
     * 主题
     */
    public static class AtmosphereTheme {
        public static final int THEME = 0;
        public static final int CUSTOMIZE = 1;
    }

    public static class AtmosphereEffect {
        public static final int EFFECT_0 = 0;
        public static final int EFFECT_1 = 1;
        public static final int EFFECT_2 = 2;
        public static final int EFFECT_3 = 3;
    }


    public static class Content {
        public static final String locationId = "locationId";
        public static final String attributeId = "attributeId";
        public static final String attributeValue = "attributeValue";
    }

    public static class DataPoint {
        public static final String id = "ZB14";
        public static final String supplierCode = "bitech";
        public static final String platformCode = "8678";
    }

    public static class CodeId {
        public static final String ZB141505 = "ZB141505"; // 驾驶-牵引模式
        public static final String ZB141506 = "ZB141506"; // 驾驶-车辆模式
        public static final String ZB141507 = "ZB141507"; // 驾驶-极致纯电
        public static final String ZB141508 = "ZB141508"; // 驾驶-舒适制动
        public static final String ZB141509 = "ZB141509"; // 驾驶-自动驻车
        public static final String ZB141510 = "ZB141510"; // 驾驶-驻车制动
        public static final String ZB141511 = "ZB141811"; // 驾驶-车身稳定控制
        public static final String ZB141512 = "ZB141512"; // 驾驶-陡坡缓降
        public static final String ZB141701 = "ZB141701"; // 连接-蓝牙
        public static final String ZB141702 = "ZB141702"; // 连接-Wi-Fi
        public static final String ZB141703 = "ZB141703"; // 连接-无线充电
        public static final String ZB141801 = "ZB141801";
        public static final String ZB141803 = "ZB141803";
        public static final String ZB141804 = "ZB141804";
        public static final String ZB141805 = "ZB141805";
        public static final String ZB141808 = "ZB141808";
        public static final String ZB141812 = "ZB141812";
        public static final String ZB141815 = "ZB141815";
        public static final String ZB141816 = "ZB141816";
        public static final String ZB141817 = "ZB141817";
        public static final String ZB141818 = "ZB141818";

        public static final String ZB141809 = "ZB141809";
        public static final String ZB141820 = "ZB141820";
        public static final String ZB141802 = "ZB141802";

        public static final String ZB140F0F = "ZB140F0F";
        public static final String ZB141211 = "ZB141211"; // 系统 - 恢复出厂设置

        public static final String ZB141401 = "ZB141401";
        public static final String ZB141402 = "ZB141402";
        public static final String ZB141403 = "ZB141403";
        public static final String ZB141407 = "ZB141407";
        public static final String ZB141408 = "ZB141408";
    }

    public static class EventId {
        public static final String Lighting_Set = "Lighting_Set";
        public static final String Drive_Set = "Drive_Set";
        public static final String Sound_Set = "Sound_Set";
        public static final String App_Open = "App_Open";
        public static final String App_Close = "App_Close";
        public static final String Vehicle_Set = "Vehicle_Set";
        public static final String Connect_Set = "Connect_Set";
        public static final String NEW_ENERGY_SET = "NewEnergy_Set";
    }

    public static class Att {
        public static final String TractionModeSw = "TractionModeSw";               // 驾驶 - 牵引模式
        public static final String VehicleModeSet = "VehicleModeSet";               // 驾驶 - 车辆模式
        public static final String DriveModeSet = "DriveModeSet";                   // 驾驶 - 个性化 - 驾驶模式
        public static final String SuspensionMode = "SuspensionMode";               // 驾驶 - 个性化 - 悬架模式
        public static final String SteerModeSet = "SteerModeSet";                   // 驾驶 - 个性化 - 转向模式
        public static final String BreakModeSet = "BreakModeSet";                   // 驾驶 - 个性化 - 制动模式
        public static final String PowCSet = "PowCSet";                             // 驾驶 - 个性化 - 保电电量
        public static final String UltElectricSw = "UltElectricSw";                 // 驾驶 - 极致纯电
        public static final String ComBreakSw = "ComBreakSw";                       // 驾驶 - 舒适制动
        public static final String ComBreakSet = "ComBreakSet";                     // 驾驶 - 舒适制动等级
        public static final String ATPSw = "ATPSw";                                 // 驾驶 - 自动驻车
        public static final String ParBraSw = "ParBraSw";                           // 驾驶 - 驻车制动
        public static final String VSCSw = "VSCSw";                                 // 驾驶 - 车身稳定控制
        public static final String SSDSw = "SSDSw";                                 // 驾驶 - 陡坡缓降
        public static final String RestoreFSw = "RestoreFSw";                       // 系统 - 恢复出厂设置

        public static final String BTSw = "BTSw";                                   // 连接 - 蓝牙开关
        public static final String BTViSw = "BTViSw";                               // 连接 - 蓝牙可见性开关
        public static final String PairedDevices = "PairedDevices";                 // 连接 - 已配对设备
        public static final String ConnectTime = "ConnectTime";                     // 连接 - 配对连接成功时间
        public static final String WiFiSw = "WiFiSw";                               // 连接 - Wi-Fi开关
        public static final String WiFiPushSw = "WiFiPushSw";                       // 连接 - 网络通知
        public static final String HotspotSw = "HotspotSw";                         // 连接 - 热点开关
        public static final String WirelessPowerSw = "WirelessPowerSw";             // 连接 - 无线充电
        public static final String ForgetSw = "ForgetSw";                           // 连接 - 遗忘提醒

        public static final String AtmosphereLampSw = "AtmosphereLampSw";
        public static final String LightSet = "LightSet";
        public static final String FrontRowSw = "FrontRowSw";
        public static final String BackRowSw = "BackRowSw";
        public static final String ASHLBSw = "ASHLBSw";
        public static final String AutowelampSw = "AutowelampSw";
        public static final String CourtesyLightSw = "CourtesyLightSw";
        public static final String TrendSw = "TrendSw";
        public static final String LightingEffect = "LightingEffect";
        public static final String HLdelaySet = "HLdelaySet";
        public static final String AutoinLampSw = "AutoinLampSw";

        public static final String HeightSet = "HeightSet";
        public static final String ABALSet = "ABALSet";
        public static final String LampTypeSet = "LampTypeSet";
        public static final String FrontwallSw = "FrontwallSw";

        public static final String OpsMode = "OpsMode";
        public static final String OpsTime = "OpsTime";
        public static final String ClsMode = "ClsMode";
        public static final String ClsTime = "ClsTime";

        //预约充电设置	BookSw
        public static final String BookSw = "BookSw";
        //预约充电时间	BookTime
        public static final String BookTime = "BookTime";
        //对外放电	CHGSw
        public static final String CHGSw = "CHGSw";
        //设置时间	SetTime
        public static final String SetTime = "SetTime";
        //充电上限值	CULVSet
        public static final String CULVSet = "CULVSet";
        //放电下限值	LLDSet
        public static final String LLDSet = "LLDSet";
        //能量回收强度	KERSw
        public static final String KERSw = "KERSw";
        //纯电续航显示	PERDSet
        public static final String PERDSet = "PERDSet";
        //行驶里程显示	MileageDisSw
        public static final String MileageDisSw = "MileageDisSw";
        //预约出行	BookTripSw
        public static final String BookTripSw = "BookTripSw";
        //续航里程工况	RangeCSet
        public static final String RangeCSet = "RangeCSet";
        //N档防误触提醒	NAMSw
        public static final String NAMSw = "NAMSw";
        //驻车发电	ParkGenSw
        public static final String ParkGenSw = "ParkGenSw";

    }

    /**
     * 优先级
     */
    public static class priority {
        public static final int priority_0 = 0;
        public static final int priority_1 = 1;
        public static final int priority_2 = 2;
        public static final int priority_3 = 3;
        public static final int priority_4 = 4;
        public static final int priority_5 = 5;
        public static final int priority_6 = 6;
        public static final int priority_7 = 7;
        public static final int priority_8 = 8;
        public static final int priority_9 = 9;
        public static final int priority_10 = 10;
        public static final int priority_11 = 11;
        public static final int priority_12 = 12;
    }

    /**
     * 分开预警
     */
    public static class doorOpenWarn {
        public static final int fLWarn = 0;
        public static final int fRWarn = 1;
        public static final int rLWarn = 2;
        public static final int rRWarn = 3;
    }

    /**
     * 前碰撞
     */
    public static class fcwAcitveWarn {
        public static final int all = 0;

    }

    /**
     * 车道偏移
     */
    public static class trackingWarn {
        public static final int all = 0;
        public static final int left = 1;
        public static final int right = 2;

    }

    /**
     * 转向信号
     */
    public static class TurnSigL {
        public static final int all = 0;
    }

    /**
     * 转向信号
     */
    public static class Dow {
        public static final int all = 0;
        public static final int left = 1;
        public static final int right = 2;
    }

    /**
     * 倒车辅助
     */
    public static class Pdc {
        public static final int frontWarning = 0;
        public static final int rearWarning = 0;
        public static final int leftWarning = 0;
        public static final int rightWarning = 0;
    }

    // fLWarn
    public enum ConfigField {
        CYCLE_MODE, AIR_CLEAN_OPEN
    }


}
