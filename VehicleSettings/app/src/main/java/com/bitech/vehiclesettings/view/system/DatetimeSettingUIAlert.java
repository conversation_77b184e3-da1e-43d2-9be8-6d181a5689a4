package com.bitech.vehiclesettings.view.system;

import static com.bitech.vehiclesettings.broadcast.TimeChangeReceiver.SYSTEM_UI_FORMAT_CHANGE_URI;
import static com.bitech.vehiclesettings.presenter.system.SystemPresenter.SystemConstant.AUTO_CALIBRATION;
import static com.bitech.vehiclesettings.presenter.system.SystemPresenter.SystemConstant.AUTO_CALIBRATION_FALSE;
import static com.bitech.vehiclesettings.presenter.system.SystemPresenter.SystemConstant.TIME_DISPLAY_12;
import static com.bitech.vehiclesettings.presenter.system.SystemPresenter.SystemConstant.TIME_DISPLAY_24;

import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.format.DateFormat;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSDatetimeBinding;
import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class DatetimeSettingUIAlert extends BaseDialog {
    private static final String TAG = DatetimeSettingUIAlert.class.getSimpleName();
    private final Context context;
    private boolean isCan = true;
    protected DialogAlertSDatetimeBinding binding;
    private boolean init;
    private SystemPresenter systemPresenter = SystemPresenter.getInstance();
    private TimeZoneSettingUIAlert.Builder timeZoneSettingUIAlert;
    private TimeSettingUIAlert.Builder timeSettingUIAlert;
    private ContentResolver resolver;
    private ContentObserver observer;
    private Uri autoTimeSettingUri = Settings.Global.getUriFor(Settings.Global.AUTO_TIME);
    private Uri timeFormatSettingUri = Settings.System.getUriFor(Settings.System.TIME_12_24);

    private BroadcastReceiver timeFormatReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (SYSTEM_UI_FORMAT_CHANGE_URI.equals(intent.getAction())) {
                boolean is24Hour = DateFormat.is24HourFormat(context);
                updateTimeFormatUI(is24Hour);
            }
        }
    };

    public DatetimeSettingUIAlert(@NonNull Context context) {
        super(context, R.style.Dialog);
        this.context = context;
    }
    @Override
    protected void onStart() {
        super.onStart();
        this.setCancelable(isCan);
        // 设置dialog的bind
        binding = DialogAlertSDatetimeBinding.inflate(LayoutInflater.from(context));
        this.setContentView(binding.getRoot());
        init = true;
        init();
        setupClickListeners();
        init = false;
        Window window = this.getWindow();
        WindowManager.LayoutParams layoutParams = window.getAttributes();
        layoutParams.width = 1200;
        window.setAttributes(layoutParams);
        registerTimeFormatReceiver();
    }

    public void init() {
        resolver = context.getContentResolver();
        observer = new ContentObserver(new Handler(Looper.getMainLooper())) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                super.onChange(selfChange, uri);
                if (binding != null) {
                    switch (uri.getLastPathSegment()) {
                        case Settings.Global.AUTO_TIME:
                            int value = Prefs.getGlobalValue(Settings.Global.AUTO_TIME, PrefsConst.DefaultValue.AUTO_TIME);
                            binding.swAutoCalibration.setChecked(value == 1);
                            Log.d(TAG, "自动校准时间状态变更回调：设置为 " + value);
                            break;
                        case Settings.System.TIME_12_24:
                            boolean timeDisplay = systemPresenter.getTimeDisplay(context);
                            binding.spvTimeDisplay.setSelectedIndex(timeDisplay ? 1 : 0, true);
                            Log.d(TAG, "时间显示格式状态变更回调：设置为 " + timeDisplay);
                            break;
                    }

                }
            }
        };
        resolver.registerContentObserver(autoTimeSettingUri, false, observer);
        resolver.registerContentObserver(timeFormatSettingUri, false, observer);
        initUI();
    }

    @Override
    protected void onStop() {
        super.onStop();
        this.dismiss();
        unregisterTimeFormatReceiver();
        if (resolver != null && observer != null) {
            resolver.unregisterContentObserver(observer);
            Log.d(TAG, "已注销 ContentObserver");
            observer = null;
        }
    }

    private void initUI() {
        binding.spvTimeDisplay.setItems(R.string.str_system_12_hour, R.string.str_system_24_hour);
        boolean timeDisplay = systemPresenter.getTimeDisplay(context);

        switch (timeDisplay ? 1 : 0) {
            case TIME_DISPLAY_12:
                binding.spvTimeDisplay.setSelectedIndex(0, false);
                break;
            case TIME_DISPLAY_24:
                binding.spvTimeDisplay.setSelectedIndex(1, false);
                break;
        }

        binding.swAutoCalibration.setChecked(CommonUtils.IntToBool(systemPresenter.getAutoCalibration()));
        setManuallyCalibrateClickable(!CommonUtils.IntToBool(systemPresenter.getAutoCalibration()));
    }

    public void setupClickListeners() {

        // 时间显示格式
        binding.spvTimeDisplay.setOnItemSelectedListener((index, text) -> {
            switch (index) {
                case 0:
                    systemPresenter.setTimeDisplay(TIME_DISPLAY_12, context);
                    break;
                case 1:
                    systemPresenter.setTimeDisplay(TIME_DISPLAY_24, context);
                    break;
            }
        });

        //自动校准时间
        binding.swAutoCalibration.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (init) {
                return;
            }
            if (isChecked) {
                systemPresenter.setAutoCalibration(AUTO_CALIBRATION);
                setManuallyCalibrateClickable(false);
            } else {
                systemPresenter.setAutoCalibration(AUTO_CALIBRATION_FALSE);
                setManuallyCalibrateClickable(true);
            }
        });

//        //时区弹窗，国内不上
//        binding.rlTimeZone.setOnClickListener(v -> {
//            if (timeZoneSettingUIAlert == null) {
//                timeZoneSettingUIAlert = new TimeZoneSettingUIAlert.Builder(context);
//            }
//            timeZoneSettingUIAlert.create().show();
//        });

        // 手动校准时间弹窗
        binding.rlManuallyCalibrate.setOnClickListener(v -> {
            if (timeSettingUIAlert == null) {
                timeSettingUIAlert = new TimeSettingUIAlert.Builder(context, systemPresenter);
            }
            timeSettingUIAlert.create().show();
        });

    }

    /**
     * 更新UI
     *
     * @param is24Hour
     */
    private void updateTimeFormatUI(boolean is24Hour) {
        if (binding != null) {
            binding.spvTimeDisplay.setSelectedIndex(is24Hour ? 1 : 0, true);
        }
    }

    /**
     * 负一屏注册广播监听
     */
    private void registerTimeFormatReceiver() {
        IntentFilter filter = new IntentFilter(SYSTEM_UI_FORMAT_CHANGE_URI);
        context.registerReceiver(timeFormatReceiver, filter);
    }

    /**
     * 解注册负一屏广播监听
     */
    private void unregisterTimeFormatReceiver() {
        try {
            context.unregisterReceiver(timeFormatReceiver);
        } catch (IllegalArgumentException e) {
            // 接收器未注册时忽略
        }
    }

    /**
     * 设置手动校准按钮是否可点击
     *
     * @param b
     */
    private void setManuallyCalibrateClickable(boolean b) {
        if (b) {
            binding.tvManuallyCalibrate.setAlpha(1f);
        } else {
            binding.tvManuallyCalibrate.setAlpha(0.5f);
        }
        binding.rlManuallyCalibrate.setEnabled(b);
        binding.rlManuallyCalibrate.setClickable(b);
    }

}
