package com.bitech.vehiclesettings.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.bitech.vehiclesettings.bean.BtPairedBean

/**
 * @ClassName: BtDeviceDao
 * 
 * @Date:  2024/1/29 17:22
 * @Description: 蓝牙设备Dao.
 **/
@Dao
interface BtDeviceDao {
    /**
     * 从蓝牙设备表格中读取所有蓝牙设备.
     *
     * @return
     */
    @Query("SELECT * FROM btDevices")
    fun getAllBtDevices(): MutableList<BtPairedBean>?

    /**
     * 获取设备的配对时间.
     *
     * @param address 设备mac地址.
     */
    @Query("SELECT pairedTime FROM btDevices WHERE btAddress = :address")
    fun getPairedTime(address: String): Long?

    /**
     * 插入或更新数据库蓝牙设备.
     *
     * @param btPairedBean 配对设备
     */
    @Transaction
    fun insertOrUpdateBtDevice(btPairedBean: BtPairedBean) {
        if (getBtDevice(btPairedBean.btAddress) != null) {
            // 更新数据库
            updateBtDevice(btPairedBean)
        } else {
            // 插入数据库
            insertBtDevice(btPairedBean)
        }
    }

    /**
     * 将蓝牙设备插入数据库表.
     *
     * @param btPairedBean 配对设备
     */
    @Insert
    fun insertBtDevice(btPairedBean: BtPairedBean)

    /**
     * 更新数据库表中蓝牙设备数据.
     *
     * @param btPairedBean 配对设备
     */
    @Update
    fun updateBtDevice(btPairedBean: BtPairedBean)

    /**
     * 删除表中蓝牙设备.
     *
     * @param btPairedBean 配对设备
     */
    @Delete
    fun deleteBtDevice(btPairedBean: BtPairedBean)

    /**
     * 清空蓝牙已配对列表.
     *
     */
    @Query("DELETE FROM btDevices")
    fun deleteAllBtPairedDevice()

    /**
     * 根据btAddress从数据库查询出来.
     *
     * @param btAddress 蓝牙设备地址
     * @return BtPairedBean
     */
    @Query("SELECT * FROM btDevices WHERE btAddress = :btAddress")
    fun getBtDevice(btAddress: String): BtPairedBean?
}
