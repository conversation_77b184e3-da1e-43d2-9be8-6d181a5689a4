package com.bitech.vehiclesettings.utils;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.view.newenergy.ChargeSOCDialog;
import com.bitech.vehiclesettings.view.widget.CommonOptionView;

public class BindingAdapter {

    @androidx.databinding.BindingAdapter("layoutWidth")
    public static void setLayoutWidth(View view, float width) {
        ViewGroup.LayoutParams params = view.getLayoutParams();
        params.width = (int) width;
        view.setLayoutParams(params);
    }

    @androidx.databinding.BindingAdapter("layoutHeight")
    public static void setLayoutHeight(View view, float height) {
        ViewGroup.LayoutParams params = view.getLayoutParams();
        params.height = (int) height;
        view.setLayoutParams(params);
    }

    @androidx.databinding.BindingAdapter("layoutMarginLeft")
    public static void setLeftMargin(View view, float leftMargin) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        layoutParams.setMargins(Math.round(leftMargin), layoutParams.topMargin,
                layoutParams.rightMargin, layoutParams.bottomMargin);
        view.setLayoutParams(layoutParams);
    }

    @androidx.databinding.BindingAdapter("layoutMarginRight")
    public static void setRightMargin(View view, float rightMargin) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        layoutParams.setMargins(layoutParams.leftMargin, layoutParams.topMargin,
                Math.round(rightMargin), layoutParams.bottomMargin);
        view.setLayoutParams(layoutParams);
    }

    @androidx.databinding.BindingAdapter("layoutMarginBottom")
    public static void setBottomMargin(View view, float bottomMargin) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        layoutParams.setMargins(layoutParams.leftMargin, layoutParams.topMargin,
                layoutParams.rightMargin, Math.round(bottomMargin));
        view.setLayoutParams(layoutParams);
    }

    @androidx.databinding.BindingAdapter("layoutMarginTop")
    public static void setTopMargin(View view, float topMargin) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        layoutParams.setMargins(layoutParams.leftMargin, Math.round(topMargin),
                layoutParams.rightMargin, layoutParams.bottomMargin);
        view.setLayoutParams(layoutParams);
    }

    @androidx.databinding.BindingAdapter("ovTextContent")
    public static void setCommonOptionViewContentText(CommonOptionView view, String textContent) {
        view.setContentText(textContent);
    }

    @androidx.databinding.BindingAdapter("ovSwChecked")
    public static void setCommonOptionViewChecked(CommonOptionView view, boolean checked) {
        view.setSwitchOpen(checked);
    }

    //启用系统色应用 系统级弹窗使用
    @androidx.databinding.BindingAdapter("systemColorBgEnable")
    public static void setSystemColorBgEnabled(View view, boolean enable) {
        if (enable) {
            TypedValue typedValue = new TypedValue();
            view.getContext().getTheme().resolveAttribute(R.attr.appPrimaryColor, typedValue, true);
            int appPrimaryColor = typedValue.data;
            view.setBackground(new android.graphics.drawable.ColorDrawable(appPrimaryColor));
        }
    }

    @androidx.databinding.BindingAdapter({"iconCharge", "fastCharging"})
    public static void setDrawableStart(TextView textView, String textChargeType, boolean fastCharging) {
        if (TextUtils.isEmpty(textChargeType)) {
            textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
            return;
        }
        int drawableResId = 0;
        Context context = textView.getContext();
        //充电中、放电中、快充枪已连接、慢充枪已连接、放电枪已连接、充电已完成（5秒后隐藏）
        if (textChargeType.equals(context.getString(R.string.ne_charging))) {
            drawableResId = fastCharging ? R.mipmap.ic_ne_charging_quickly : R.mipmap.ic_ne_charging_n;
        } else if (textChargeType.equals(context.getString(R.string.ne_discharging))) {
            drawableResId = R.mipmap.ic_ne_discharge;
        } else if (textChargeType.equals(context.getString(R.string.ne_fast_charging_connected))
                || textChargeType.equals(context.getString(R.string.ne_slow_charging_connected))
                || textChargeType.equals(context.getString(R.string.ne_discharging_connected))) {
            drawableResId = R.mipmap.ic_ne_discharge_connect;
        } else if (textChargeType.equals(context.getString(R.string.ne_charging_complete))
                || textChargeType.equals(context.getString(R.string.ne_discharging_complete))) {
            drawableResId = R.mipmap.ic_ne_charge_finished;
        }
        if (drawableResId > 0) {
            textView.setCompoundDrawablesWithIntrinsicBounds(drawableResId, 0, 0, 0);
        }
    }

    @androidx.databinding.BindingAdapter("batteryPercent")
    public static void setBatteryPercentImg(ImageView view, int percent) {
        //动力电池电量图片
        if (percent <= 0) {
            percent = 0;
        } else if (percent >= 100) {
            percent = 100;
        }
        int resId = view.getContext().getResources().getIdentifier(
                "ic_battery_charge_" + percent,
                "mipmap",
                view.getContext().getPackageName()
        );
        view.setImageResource(resId);
    }

    @androidx.databinding.BindingAdapter("enableAnim")
    public static void setAnimationState(ImageView imageView, boolean enableAnim) {
        AnimationDrawable animDrawable = (AnimationDrawable) imageView.getDrawable();
        if (enableAnim) {
            animDrawable.start();
        } else {
            animDrawable.stop();
        }
    }

    @androidx.databinding.BindingAdapter("engineImageVisible")
    public static void setEngineImage(ImageView view, int energyFlowState) {
        //能量流发动机图片
        int visibility = View.GONE;
        switch (energyFlowState) {
            case CarNewEnergy.HcuEnergyFlowSts.ST04_2WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST05_2WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST08_2WD_Parallel:
            case CarNewEnergy.HcuEnergyFlowSts.ST0A_Engine:
            case CarNewEnergy.HcuEnergyFlowSts.ST0B_Running_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST0C_Parking_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST10_DecelerateCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST06_4WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST07_4WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST09_4WD_Parallel:
            case CarNewEnergy.HcuEnergyFlowSts.ST12_4WD_ExtendRangeChargeByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST13_4WD_ExtendRangeDisChargeByRear:
                visibility = View.VISIBLE;
            default:
                break;
        }
        view.setVisibility(visibility);
    }

    @androidx.databinding.BindingAdapter("carWheelForwardAnimVisible")
    public static void setCarWheelForwardAnimVisible(ImageView view, int energyFlowState) {
        //车轮蓝色箭头光晕
        int visibility = View.GONE;
        switch (energyFlowState) {
            case CarNewEnergy.HcuEnergyFlowSts.ST02_2WD_EV:
            case CarNewEnergy.HcuEnergyFlowSts.ST04_2WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST05_2WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST08_2WD_Parallel:
            case CarNewEnergy.HcuEnergyFlowSts.ST0A_Engine:
            case CarNewEnergy.HcuEnergyFlowSts.ST0B_Running_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST0D_2WD_ReGenerateBrake:
            case CarNewEnergy.HcuEnergyFlowSts.ST0E_4WD_ReGenerateBrake:
            case CarNewEnergy.HcuEnergyFlowSts.ST10_DecelerateCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST03_4WD_EV:
            case CarNewEnergy.HcuEnergyFlowSts.ST06_4WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST07_4WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST09_4WD_Parallel:
            case CarNewEnergy.HcuEnergyFlowSts.ST11_4WD_EVByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST12_4WD_ExtendRangeChargeByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST13_4WD_ExtendRangeDisChargeByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST14_4WD_ReGenerateBrakeByRear:
                visibility = View.VISIBLE;
            default:
                break;
        }
        view.setVisibility(visibility);
    }

    @androidx.databinding.BindingAdapter("pipeLeftFrontAnimVisible")
    public static void setPipeLeftFrontAnimVisible(ImageView view, int energyFlowState) {
        //管道左前
        int visibility = View.GONE;
        switch (energyFlowState) {
            case CarNewEnergy.HcuEnergyFlowSts.ST04_2WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST05_2WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST0B_Running_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST0C_Parking_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST10_DecelerateCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST06_4WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST07_4WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST12_4WD_ExtendRangeChargeByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST13_4WD_ExtendRangeDisChargeByRear:
                visibility = View.VISIBLE;
            default:
                break;
        }
        view.setVisibility(visibility);
    }

    @androidx.databinding.BindingAdapter("frontMotorAnimVisible")
    public static void setFrontMotorAnimVisible(ImageView view, int energyFlowState) {
        //前发电机+管道中前
        int visibility = View.GONE;
        switch (energyFlowState) {
            case CarNewEnergy.HcuEnergyFlowSts.ST02_2WD_EV:
            case CarNewEnergy.HcuEnergyFlowSts.ST04_2WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST05_2WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST08_2WD_Parallel:
            case CarNewEnergy.HcuEnergyFlowSts.ST0B_Running_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST0C_Parking_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST0D_2WD_ReGenerateBrake:
            case CarNewEnergy.HcuEnergyFlowSts.ST0E_4WD_ReGenerateBrake:
            case CarNewEnergy.HcuEnergyFlowSts.ST10_DecelerateCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST03_4WD_EV:
            case CarNewEnergy.HcuEnergyFlowSts.ST06_4WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST07_4WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST09_4WD_Parallel:
            case CarNewEnergy.HcuEnergyFlowSts.ST12_4WD_ExtendRangeChargeByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST13_4WD_ExtendRangeDisChargeByRear:
                visibility = View.VISIBLE;
            default:
                break;
        }
        view.setVisibility(visibility);
    }

    @androidx.databinding.BindingAdapter("backMotorVisible")
    public static void setBackMotorVisible(ImageView view, int energyFlowState) {
        //后发电机+管道右后
        int visibility = View.GONE;
        switch (energyFlowState) {
            case CarNewEnergy.HcuEnergyFlowSts.ST03_4WD_EV:
            case CarNewEnergy.HcuEnergyFlowSts.ST06_4WD_ExtendRangeDisCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST07_4WD_ExtendRangeCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST09_4WD_Parallel:
            case CarNewEnergy.HcuEnergyFlowSts.ST0A_Engine:
            case CarNewEnergy.HcuEnergyFlowSts.ST0B_Running_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST0E_4WD_ReGenerateBrake:
            case CarNewEnergy.HcuEnergyFlowSts.ST10_DecelerateCharge:
            case CarNewEnergy.HcuEnergyFlowSts.ST11_4WD_EVByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST12_4WD_ExtendRangeChargeByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST13_4WD_ExtendRangeDisChargeByRear:
            case CarNewEnergy.HcuEnergyFlowSts.ST14_4WD_ReGenerateBrakeByRear:
                visibility = View.VISIBLE;
            default:
                break;
        }
        view.setVisibility(visibility);
    }

    @androidx.databinding.BindingAdapter("engPipeUpDownRedAnimVisible")
    public static void setEngPipeUpDownRedAnimVisible(ImageView view, int energyFlowState) {
        //发动机管道上下-红色
        int visibility = View.GONE;
        switch (energyFlowState) {
            case CarNewEnergy.HcuEnergyFlowSts.ST08_2WD_Parallel:
            case CarNewEnergy.HcuEnergyFlowSts.ST0A_Engine:
            case CarNewEnergy.HcuEnergyFlowSts.ST0B_Running_Generate:
            case CarNewEnergy.HcuEnergyFlowSts.ST09_4WD_Parallel:
                visibility = View.VISIBLE;
            default:
                break;
        }
        view.setVisibility(visibility);
    }

    @androidx.databinding.BindingAdapter({"currentSoc", "progress", "isCharging"})
    public static void setChargeSocRecommendationText(TextView view, int currentSoc, int progress, boolean isCharging) {
        int targetSoc = progress + (isCharging ? ChargeSOCDialog.CHARGE_SOC_START : ChargeSOCDialog.DISCHARGE_SOC_START);
        int textResId;

        int textColorResId = R.color.text_color_error;
        boolean isVisible = true;

        if (isCharging) {
            // 充电逻辑
            if (currentSoc >= targetSoc) {
                // 当前电量已达到或超过目标电量
                textResId = R.string.charging_soc_recommendationA;
            } else if (targetSoc <= ChargeSOCDialog.RECOMMEND_CHARGE_STOP) {
                // 目标电量低于推荐充电上限
                textResId = R.string.charging_soc_recommendationB;
                textColorResId = R.color.warning_text;
            } else {
                // 90%以上充电建议
                textResId = R.string.charging_soc_recommendationC;
            }
        } else {
            // 放电逻辑
            textResId = R.string.ne_discharge_limit_value_error;
            //当前电量低于放电初始30%起始值 无法放电
            if (currentSoc < ChargeSOCDialog.DISCHARGE_SOC_START) {
                textResId = R.string.ne_discharge_limit_value_error2;
            } else if (targetSoc < currentSoc) {  //正常放电区间 不提示文字
                isVisible = false;
            }
            //提示放电下限小于当前电量
        }

        view.setTextColor(ContextCompat.getColor(view.getContext(), textColorResId));
        view.setText(view.getContext().getString(textResId));
        view.setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }

    @androidx.databinding.BindingAdapter({"currentSoc", "progress", "isCharging"})
    public static void setChargeDialogButtonEnable(Button button, int currentSoc, int progress, boolean isCharging) {
        int targetSoc = progress + (isCharging ? ChargeSOCDialog.CHARGE_SOC_START : ChargeSOCDialog.DISCHARGE_SOC_START);
        boolean isEnabled;

        if (isCharging) {
            // 充电：只有当目标电量大于当前电量时才启用按钮
            isEnabled = targetSoc > currentSoc;
        } else {
            // 放电：只有当当前电量大于目标电量时才启用按钮
            isEnabled = currentSoc > targetSoc;
        }

        button.setEnabled(isEnabled);
        button.setAlpha(isEnabled ? 1f : 0.5f);
    }


}
