package com.bitech.vehiclesettings.presenter.light;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.bitech.base.utils.Util;
import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;

public class LightPresenter implements LightPresenterListener {
    private static final String TAG = LightPresenter.class.getSimpleName();
    private LightInBean lightInBean;
    private Context mContext;

    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);

    public LightPresenter(Context context) {
        this.mContext = context;
    }

    @Override
    public void setSwitchView(int swLightAtmosphere, View flRecommend1, View flRecommend2) {
        if (swLightAtmosphere == 0) {
            flRecommend1.setVisibility(View.VISIBLE);
            flRecommend2.setVisibility(View.GONE);
        } else {
            flRecommend1.setVisibility(View.GONE);
            flRecommend2.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void beginTranslateAndScale(View view, int from, int to, int startWidth, int endWidth) {
        view.setTranslationX(0);
        ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(view, "translationX", from, to);
        final ValueAnimator scaleAnimator = ValueAnimator.ofInt(startWidth, endWidth);
        scaleAnimator.addUpdateListener(animation -> {
            int animatedValue = (int) scaleAnimator.getAnimatedValue();
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.width = animatedValue;
            view.setLayoutParams(params);
        });
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleAnimator, objectAnimator);
        animatorSet.setDuration(300);
        animatorSet.start();
    }

    @Override
    public void setTextView(boolean isDelayer, TextView... textViews) {
        new Handler().postDelayed(() -> {
            for (int i = 0; i < textViews.length; i++) {
                if (i == 0) {
                    textViews[i].setTextColor(mContext.getColor(R.color.white));
                } else {
                    textViews[i].setTextColor(mContext.getColor(Util.isNight(mContext) ? R.color.white : R.color.black));
                }
            }
        }, isDelayer ? 300 : 0);
    }


}
