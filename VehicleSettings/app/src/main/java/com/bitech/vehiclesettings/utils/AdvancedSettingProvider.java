package com.bitech.vehiclesettings.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.net.Uri;

import com.bitech.vehiclesettings.MyApplication;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AdvancedSettingProvider {
    private static final String PREFS_NAME = "advanced_app_settings";

    // 默认值常量
    private static final int DEFAULT_ALARM_TYPE = 0;
    private static final int DEFAULT_FP = 1;
    private static final String KEY_WALLPAPER_URI_LIST = "wallpaper_uri_list";
    private static final SharedPreferences sharedPref = MyApplication.getContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);

    public AdvancedSettingProvider(Context context) {
    }

    public enum SettingKey {
        ALARM_TYPE("System.sound_effects_type", DEFAULT_ALARM_TYPE),
        FP("persist.sys.setting.split_screen", DEFAULT_FP),
        WALLPAPER_URI("wallpaper_uri", "");

        private final String key;
        private final Object defaultValue;

        SettingKey(String key, Object defaultValue) {
            this.key = key;
            this.defaultValue = defaultValue;
        }

        public String getKey() {
            return key;
        }

        public Object getDefaultValue() {
            return defaultValue;
        }
    }

    public static void saveSetting(SettingKey key, Object value) {
        SharedPreferences.Editor editor = sharedPref.edit();

        if (value instanceof String) {
            editor.putString(key.getKey(), (String) value);
        } else if (value instanceof Integer) {
            editor.putInt(key.getKey(), (Integer) value);
        } else if (value instanceof Boolean) {
            editor.putBoolean(key.getKey(), (Boolean) value);
        } else if (value instanceof Float) {
            editor.putFloat(key.getKey(), (Float) value);
        } else {
            throw new IllegalArgumentException("Unsupported value type");
        }

        editor.apply();
    }

    public static Object getSetting(SettingKey key) {
        Object defaultValue = key.getDefaultValue();

        if (defaultValue instanceof String) {
            return sharedPref.getString(key.getKey(), (String) defaultValue);
        } else if (defaultValue instanceof Integer) {
            return sharedPref.getInt(key.getKey(), (Integer) defaultValue);
        } else if (defaultValue instanceof Boolean) {
            return sharedPref.getBoolean(key.getKey(), (Boolean) defaultValue);
        } else if (defaultValue instanceof Float) {
            return sharedPref.getFloat(key.getKey(), (Float) defaultValue);
        } else {
            throw new IllegalArgumentException("Unsupported default value type");
        }
    }
    // 保存 Uri 列表
    public static void saveWallpaperUriList(List<Uri> uriList) {
        Set<String> uriSet = new HashSet<>();
        for (Uri uri : uriList) {
            uriSet.add(uri.toString());
        }
        sharedPref.edit().putStringSet(KEY_WALLPAPER_URI_LIST, uriSet).apply();
    }

    // 获取 Uri 列表
    public static List<Uri> getWallpaperUriList() {
        Set<String> uriSet = sharedPref.getStringSet(KEY_WALLPAPER_URI_LIST, new HashSet<>());
        List<Uri> uriList = new ArrayList<>();
        for (String uriStr : uriSet) {
            uriList.add(Uri.parse(uriStr));
        }
        return uriList;
    }

    // 添加单个 Uri
    @SuppressLint("MutatingSharedPrefs")
    public static void addWallpaperUriList(List<String> uris) {
        Set<String> uriSet = sharedPref.getStringSet(KEY_WALLPAPER_URI_LIST, new HashSet<>());
        uriSet.addAll(uris);
        sharedPref.edit().putStringSet(KEY_WALLPAPER_URI_LIST, uriSet).apply();
    }

    // 移除单个 Uri
    public static void removeWallpaperUri(Uri uri) {
        Set<String> uriSet = sharedPref.getStringSet(KEY_WALLPAPER_URI_LIST, new HashSet<>());
        uriSet.remove(uri.toString());
        sharedPref.edit().putStringSet(KEY_WALLPAPER_URI_LIST, uriSet).apply();
    }

    public static String getString(SettingKey key) {
        return (String) getSetting(key);
    }

    public static int getInt(SettingKey key) {
        return (Integer) getSetting(key);
    }

    public static boolean getBoolean(SettingKey key) {
        return (Boolean) getSetting(key);
    }

    public static float getFloat(SettingKey key) {
        return (Float) getSetting(key);
    }
}