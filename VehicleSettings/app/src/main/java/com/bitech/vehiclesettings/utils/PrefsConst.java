package com.bitech.vehiclesettings.utils;

public class PrefsConst {

    public static final String SETTINGS_NAME = "vehiclesettingsPrefs";
    public static final int TRUE = 1;
    public static final int FALSE = 0;
    public static final String KEY_FIRST_LAUNCH = "key.first.launch";

    public static final String SELECT_TAB = "selectTab";
    public static final String SELECT_THEME = "selectTheme";
    public static final String SELECT_FRONT_REAR = "selectFrontOrRear";
    public static final String SELECT_CUR_COLOR = "selectCurColor";
    /**
     * 车辆设置
     */
    public static final String C_REAR_TAILGATE_ROATE = "car.rear.tailgate.roate";
    public static final String C_WIPER_SENS = "car.wiper.sens";
    // 锁车自动升窗
    public static final String C_LOCK_AUTO_RAISE_WINDOW = "car.lock.auto.raise.window";
    // 设防提示
    public static final String C_DEFENSE_REMINDER = "car.defense.reminder";
    // 左儿童锁
    public static final String C_LEFT_CHILD_LOCK = "car.left.child.lock";
    //  右儿童锁
    public static final String C_RIGHT_CHILD_LOCK = "car.right.child.lock";
    // 自动落锁
    public static final String C_AUTOMATIC_LOCKING = "car.automatic.locking";
    // 驻车自动解锁
    public static final String C_AUTOMATIC_PARKING_UNLOCK = "car.automatic.parking.unlock";
    // 雨刮维修模式
    public static final String C_WIPER_REPAIR_MODE = "car.wiper.repair.mode";
    // 超速报警
    public static final String C_OVER_SPEED = "car.over.speed";
    // 疲劳驾驶提醒
    public static final String C_FATIGUE_DRIVING_REMINDER = "car.fatigue.driving.reminder";
    // 保养提示
    public static final String C_MAINTAIN_TIPS = "car.maintain.tips";
    // 保养重置里程
    public static final String C_MAINTAIN_RESET_KM = "car.maintain.reset.km";
    // 保养重置天数
    public static final String C_MAINTAIN_RESET_DAY = "car.maintain.reset.day";
    // 驾驶安全气囊
    public static final String C_DRIVE_AIR_BAG = "car.drive.air.bag";
    // 超速报警
    public static final String C_OVER_SPEED_SEEKBAR = "car.over.speed.seekbar";
    // 车速
    public static final String C_CAR_SPEED = "car.car.speed";
    // 疲劳驾驶提醒
    public static final String C_FATIGUE_DRIVING_SEEKBAR = "car.fatigue.driving.seekbar";
    // 自定义按键
    public static final String C_CUSTOM_BUTTON = "car.custom.button";
    // 报警音类型
    public static final String VOICE_ALARM_TYPE = "sound_effects_type";

    /**
     * 驾驶模式
     */
    public static final String D_DRIVING_MODE = "driving.driving.mode";

    public static final String D_CAR_MODE = "driving.carMode";

    public static final String D_EXTREME_PURE_SIGNAL = "driving.extreme.pure.signal";
    public static final String D_STEEP_SLOPE_DESCENT = "driving.steep.slope.descent";
    public static final String D_BODY_STABILITY_CONTROL = "driving.body.stability.control";
    public static final String D_AUTO_PARKING = "driving.auto.parking";
    public static final String D_PARKING_BRAKE = "driving.parking.brake";
    public static final String D_COMFORT_BRAKING = "driving.comfort.braking";
    public static final String D_COMFORT_BRAKING_RANK = "driving.comfort.braking.rank";
    public static final String D_BRAKE_PEDAL = "driving.brake.pedal";
    public static final String D_INTELLIGENT_SUSPENSION_AIMING = "driving.intelligent.suspension.aiming";
    public static final String D_POWER_PROTECTION = "driving.power.protection"; // 保电电量
    public static final String D_SWERVE_MODE = "driving.swerve.mode";
    public static final String D_SUSPENSION_MODE = "driving.suspension.mode";
    public static final String D_BRAKING_MODE = "driving.braking.mode";
    public static final String D_TRACTION_MODE = "driving.traction.mode"; // 牵引模式
    public static final String D_INFO_GEAR = "driving.info.gear"; // P档状态
    public static final String D_DMS_OPEN_STATUS = "driving.dms.open.status";
    public static final String D_DMS_PRIVACY_AGREEMENT = "driving.dms.privacy.agreement";
    public static final String D_FATIGUE_detection = "driving.fatigue.detection";
    /**
     * 灯光
     */
    public static final String L_LIGHT = "light";
    public static final String L_LIGHT_SW = "light.sw";
    // 主题切换
    public static final String L_THEME_MODE = "light.theme.mode";

    public static final String L_LIGHT_SEL = "light.sel";
    // 主题
    public static final String L_COLOR_POS = "light.color.pos";

    public static final String L_COLOR_ATMOSPHERE = "light.front.atmosphere";

    public static final String L_BRIGHTNESS_POS = "light.brightness.pos";
    public static final String L_BRIGHTNESS_AUOT = "light.brightness.auot";
    public static final String L_LIGHT_SYNC = "light.sync";
    // 全部
    public static final String L_LIGHT_ALL = "light.all";
    public static final String L_LIGHT_ALL_COLOR = "light.all.color";
    public static final String L_LIGHT_ALL_COLOR_LIN = "light.all.color.lin";
    public static final String L_COLOR_MUTI_FRONT_LIN = "light.color.muti.front.lin";  // 多色
    public static final String L_COLOR_MUTI_REAR_LIN = "light.color.muti.rear.lin";
    public static final String L_LIGHT_ALL_BRIGHTNESS_LEVEL = "light.all.brightness.level";
    public static final String L_LIGHT_ALL_ATMOSPHERE = "light.all.atmosphere";
    public static final String L_COLOR_MUTI_FRONT_ATMOSPHERE = "light.front.atmosphere";  // 多色
    public static final String L_COLOR_MUTI_REAR_ATMOSPHERE = "light.front.atmosphere";
    //    public static final String L_COLOR_BRIGHTNESS_PROGRESS = "light.color.brightness.progress";    public static final String L_COLOR_BRIGHTNESS_PROGRESS = "light.color.brightness.progress";
    // 车灯效果
    public static final String L_LIGHT_EFFECT = "light.light.effect";
    // 自动顶灯
    public static final String L_AUTOMATIC_CEILING = "light.automaticCeiling";

    // 前排
    public static final String L_LIGHT_FRONT = "light.front";
    public static final String L_LIGHT_FRONT_COLOR = "light.front.color";
    public static final String L_LIGHT_FRONT_BRIGHTNESS = "light.front.brightness";
    public static final String L_LIGHT_FRONT_BRIGHTNESS_AUTO = "light.front.brightness.auto";
    public static final String L_LIGHT_FRONT_COLOR_LIN = "light.front.color.lin";
    public static final String L_LIGHT_FRONT_BRIGHTNESS_LEVEL = "light.front.brightness.level";
    public static final String L_LIGHT_FRONT_ATMOSPHERE = "light.front.atmosphere";
    // 后排
    public static final String L_LIGHT_REAR = "light.rear";
    public static final String L_LIGHT_REAR_COLOR = "light.rear.color";
    public static final String L_LIGHT_REAR_BRIGHTNESS = "light.rear.brightness";
    public static final String L_LIGHT_REAR_BRIGHTNESS_AUTO = "light.rear.brightness.auto";
    public static final String L_LIGHT_REAR_COLOR_LIN = "light.rear.color.lin";
    public static final String L_LIGHT_REAR_BRIGHTNESS_LEVEL = "light.rear.brightness.level";
    public static final String L_LIGHT_REAR_ATMOSPHERE = "light.rear.atmosphere";
    // 主题

    // 前排
    public static final String L_FRONT_LIGHT = "light.front.light";
    // 后排
    public static final String L_REAR_LIGHT = "light.rear.light";

    // 车灯控制
    public static final String L_LAMP_CONTROL = "light.lamp.control";
    // 后雾灯
    public static final String L_REAR_FOG_LAMP = "light.rear.fog.lamp";


    //  大灯高度
    public static final String L_LAMP_HIGH = "light.lamp_high";
    //  大灯延时关闭
    public static final String L_LAMP_DELAY = "light.lamp_delay";

    // 靠近迎宾
    public static final String L_APPROACHING_WELCOME = "light.approachingWelcome";

    // 大灯音乐律动
    public static final String L_LAMP_MUSIC_RHYTHM = "light.lampMusicRhythm";
    // 智能远近光灯
    public static final String L_HIGH_LOW_SWITCH = "light.highLowSwitch";
    // 智能迎宾灯
    public static final String L_INTELLIGENT_WELCOME = "light.intelligentWelcome";
    public static final String L_INTELLIGENT_WELCOME_FRONT = "light.intelligentWelcome.front";
    public static final String L_INTELLIGENT_WELCOME_REAR = "light.intelligentWelcome.rear";
    /**
     * 快捷控制
     */
    // 中控锁
    public static final String Q_CENTER_LOCK = "quick.center.lock";
    // 后尾门
    public static final String Q_REAR_TAIL_GATE = "quick.rear.tail.gate";
    // 后视镜折叠
    public static final String Q_REAR_MIRROR_FOLD = "quick.rear.mirror.fold";
    // 车窗
    public static final String Q_WINDOW = "quick.car.window";
    public static final String Q_WINDOW_FL = "quick.car.window.fl";
    public static final String Q_WINDOW_FR = "quick.car.window.fr";
    public static final String Q_WINDOW_RL = "quick.car.window.rl";
    public static final String Q_WINDOW_RR = "quick.car.window.rr";
    // 车窗锁
    public static final String Q_WINDOW_LOCK = "quick.car.window.lock";
    // 遮阳帘
    public static final String Q_SUNSHADE = "quick.car.sunshade";
    // 电动尾翼
    public static final String Q_AUTO_TAIL = "quick.auto.tail";
    // 天窗
    public static final String Q_SKY_WINDOW = "quick.sky.window";
    // 感应靠近解锁
    public static final String Q_APPROACHING_UNLOCK = "quick.approaching.unlock";

    public static final String Q_DEPARTURE_LOCKING = "quick.departure.locking";
    //外后视镜自动折叠
    public static final String Q_AUTO_REAR_MIRROR_FOLD = "quick.auto.rear.mirror.fold";
    // 雨天自动加热外后视镜
    public static final String Q_AUTO_HOT_REAR_MIRROR = "quick.auto.hot.rear.mirror";
    // 座椅便携 SeatPortable
    public static final String Q_SEAT_PORTABLE = "quick.seat.portable";
    // 加油小门
    public static final String Q_REFUEL_SMALL_DOOR = "quick.refuel.small.door";
    // 倒车时后视镜自动调节
    public static final String Q_BACK_AUTO_REAR_MIRROR_ADJUSE = "quick.back.auto.rear.mirror.adjust";
    // 车辆下电
    public static final String Q_VECHICE_POWER_OFF = "quick.vechice.power.off";
    // 锁车自动升窗
    public static final String Q_LOCK_AUTO_RAISE_WINDOW = "quick.lock.auto.raise.window";
    // 锁车收起遮阳帘
    public static final String Q_LOCK_CAR_SUNROOF_SHADE = "quick.lock.car.sunroof.shade";
    // 设防提示
    public static final String Q_DEFENSE_REMINDER = "quick.defense.reminder";
    // 左儿童锁
    public static final String Q_LEFT_CHILD_LOCK = "quick.left.child.lock";
    //  右儿童锁
    public static final String Q_RIGHT_CHILD_LOCK = "quick.right.child.lock";
    // 自动落锁
    public static final String Q_AUTOMATIC_LOCKING = "quick.automatic.locking";
    // 驻车自动解锁
    public static final String Q_AUTOMATIC_PARKING_UNLOCK = "quick.automatic.parking.unlock";
    // 后尾门开启高度
    public static final String Q_REAR_TAILGATE_ROATE = "quick.rear.tailgate.roate";
    // 雨刮灵敏度
    public static final String Q_WIPER_SENS = "quick.wiper.sens";
    // 驾驶安全气囊
    public static final String Q_DRIVE_AIR_BAG = "quick.drive.air.bag";

    public static final String Q_CAR_DOOR_FL = "quick.car.door.fl";
    public static final String Q_CAR_DOOR_FR = "quick.car.door.fr";
    public static final String Q_CAR_DOOR_RL = "quick.car.door.rl";
    public static final String Q_CAR_DOOR_RR = "quick.car.door.rr";
    public static final String Q_CAR_WINDOW_FL = "quick.car.window.fl";
    public static final String Q_CAR_WINDOW_FR = "quick.car.window.fr";
    public static final String Q_CAR_WINDOW_RL = "quick.car.window.rl";
    public static final String Q_CAR_WINDOW_RR = "quick.car.window.rr";

    // 智慧识别
    public static final String R_DMS_CAMERA_STATUS = "recognition.camera";
    public static final String R_FATIGUE_DETECTION_STATUS = "recognition.fatigue";
    public static final String R_DISTRACTION_STATUS = "recognition.distraction";
    public static final String R_CALL_STATUS = "recognition.call";
    public static final String R_DRINK_STATUS = "recognition.drink";
    public static final String R_SEAT_HEAT_STATUS = "recognition.seat.heat";
    public static final String R_SEAT_VENTILATION_STATUS = "recognition.seat.ventilation";
    public static final String R_SIGHT_UNLOCK_STATUS = "recognition.sight.unlock";
    public static final String R_GREET_STATUS = "recognition.greet";
    public static final String R_SMOKE_STATUS = "recognition.smoke";
    public static final String R_VISUAL_SERVICE = "recognition.visualService";

    // 显示
    public static final String DISPLAY_SHOW_LYRICS = "display.show.lyrics";//仪表显示歌词
    public static final String DISPLAY_VIDEO_LIMIT = "display.video.limit";//视频限制
    public static final String DISPLAY_ZKP = "display.zkp";//中控屏亮度
    public static final String DISPLAY_YBP = "display.ybp";//仪表屏亮度
    public static final String DISPLAY_AUTO_DAY_NIGHT = "display.auto";// 自动模式
    public static final String DISPLAY_DAY_NIGHT = "display.dayNight";// 浅色：0；深色：1；自动：2
    public static final String DISPLAY_MODE = "display.mode";// 浅色：0；深色：1；自动：2

    // 连接
    public static final String CONNECT_5G = "connect.5g";
    public static final String CONNECT_FRONT_CHARGING = "connect.frontCharging";
    public static final String CONNECT_CHARGING_TIPS_LAUNCHER = "connect.chargingTips.launcher";
    public static final String CONNECT_CHARGING_REMIND_STATE = "connect.ChargingRemindState";
    // 遗忘提醒
    public static final String CONNECT_FORGET_REMIND_STATE = "connect.ForgetRemindState";
    public static final String CONNECT_HIGH_TEMPERATURE_REMIND = "connect.highTemperatureRemind";
    public static final String CONNECT_FORGET_REMIND_TIP = "connect.forgetRemindTip";
    // 声音
    public static final String VOICE_EQ = "voice.eq";
    public static final String VOICE_MEDIA = "voice.media";
    public static final String VOICE_NAVI = "voice.navi";
    public static final String VOICE_VR = "voice.vr";
    public static final String VOICE_ALARM = "voice.alarm";
    public static final String VOICE_SURROUND_SOUND = "voice.surround.sound";
    public static final String VOICE_VIRTUAL_SCENE = "voice.virtual.scene";
    public static final String VOICE_CUSTOM_X = "voice.custom.x";
    public static final String VOICE_CUSTOM_Y = "voice.custom.y";
    public static final String VOICE_HEADREST = "voice.headrest";
    public static final String VOICE_SUB_BASS = "voice.subBass";
    public static final String VOICE_BASS = "voice.bass";
    public static final String VOICE_LOW_MID = "voice.lowMid";
    public static final String VOICE_MID = "voice.mid";
    public static final String VOICE_HIGH_MID = "voice.highMid";
    public static final String VOICE_TREBLE = "voice.treble";
    public static final String VOICE_SUPER_TREBLE = "voice.superTreble";
    public static final String VOICE_COMPENSATION = "voice.compensation";
    public static final String VOICE_CALL_BROADCAST = "voice.broadcast";
    public static final String VOICE_LOW_SPEED_ANALOG = "voice.lowSpeedAnalog";
    public static final String VOICE_EXTERNAL_MODE = "voice.externalMode";
    public static final String VOICE_LOWER_MEDIA_TONE = "voice.lowerMediaTone";
    public static final String VOICE_BUTTON_SOUND = "voice.buttonSound";

    // 系统
    public static final String SYSTEM_ANALYSIS = "system.analysis";
    public static final String SYSTEM_DATE_DISPLAY = "system.dateDisplay";
    public static final String SYSTEM_TIME_DISPLAY = "system.timeDisplay";
    public static final String SYSTEM_AUTO_CALIBRATION = "system.autoCalibration";
    public static final String SYSTEM_INSTRUMENT_FUEL_UNIT = "system.instrumentFuelUnit";
    public static final String SYSTEM_TIRE_PRESSURE_UNIT = "system.tirePressureUnit";
    public static final String SYSTEM_POWER_CONSUMPTION_UNIT = "system.powerConsumptionUnit";
    public static final String SYSTEM_UNIT_SETTING = "system.unitSetting";
    public static final String SYSTEM_TEMPERATURE_UNIT = "system.temperatureUnit";
    public static final String SYSTEM_PERMISSION_APP_CAMERA = "system.permissionAppCamera";
    public static final String SYSTEM_PERMISSION_APP_CAMERA_DURATION = "system.permissionAppCameraDuration";
    public static final String SYSTEM_PERMISSION_APP_CAMERA_DURATION_TYPE = "system.permissionAppCameraDurationType";
    public static final String SYSTEM_PERMISSION_APP_MICROPHONE = "system.permissionAppMicrophone";
    public static final String SYSTEM_PERMISSION_APP_MICROPHONE_DURATION = "system.permissionAppMicrophoneDuration";
    public static final String SYSTEM_PERMISSION_APP_MICROPHONE_DURATION_TYPE = "system.permissionAppMicrophoneDurationType";
    public static final String SYSTEM_PERMISSION_APP_LOCATION = "system.permissionAppLocation";
    public static final String SYSTEM_PERMISSION_APP_LOCATION_DURATION = "system.permissionAppLocationDuration";
    public static final String SYSTEM_PERMISSION_APP_LOCATION_DURATION_TYPE = "system.permissionAppLocationDurationType";
    public static final String SYSTEM_SERVICE_PRIVACY_AGREEMENT = "system.servicePrivacyAgreement";
    public static final String SYSTEM_PRIVACY_STATEMENT_ACTIVITY_DATA = "system.privacyStatementActivityData";
    public static final String SYSTEM_PRIVACY_STATEMENT_SETTING_DATA = "system.privacyStatementSettingData";
    public static final String SYSTEM_PRIVACY_STATEMENT_BROWSE_DATA = "system.privacyStatementBROWSEData";
    public static final String SYSTEM_DEVICE_INFO_NAME = "system.deviceInfoName";
    public static final String SYSTEM_FUELUNIT = "system.fuelUnit";
    public static final String SYSTEM_SYSTEM_SOFTWARE_VERSION = "system.systemSoftWareVersion";
    public static final String SYSTEM_SYSTEM_HARDWARE_VERSION = "system.systemHardWareVersion";
    public static final String SYSTEM_TBOX_SOFTWARE_VERSION = "system.TBoxSoftWareVersion";
    public static final String SYSTEM_TBOX_HARDWARE_VERSION = "system.TBoxHardWareVersion";

    // 车辆状态
    public static final String CONDITION_MAINTAIN_KM = "condition.maintain.km";
    public static final String CONDITION_MAINTAIN_TIME = "condition.maintain.time";
    public static final String PRIVACY_POLICY_STATUS = "privacy.policy.status";
    public static final String SCROLL_Y = "scroll.y";
    // 记忆媒体音量
    public static final String C_REMEMBER_MEDIA_VOLUME = "volume.remember.media";
    // 记忆导航音量
    public static final String C_REMEMBER_NAVI_VOLUME = "volume.remember.navi";
    // 记忆电话音量
    public static final String C_REMEMBER_PHONE_VOLUME = "volume.remember.phone";
    // 记忆语音音量
    public static final String C_REMEMBER_VOICE_VOLUME = "volume.remember.voice";
    // 记忆报警音音量
    public static final String C_REMEMBER_ALARM_VOLUME = "volume.remember.alarm";

    public static final String LIGHT_SCROLL_Y = "light.scroll.y";
    public static final String SYSTEM_COLOR = "system.color";
    public static final String D_STEERING_WHEEL_MODE = "steering.mode";
    public static final String Q_CAR_HOOD = "car.hood";
    public static final String WASH_MODE = "quick.wash.mode"; // 洗车模式
    public static final String D_DMS_INIT_STATUS = "drive.dms.init.status";
    public static final String Q_MUSIC_WALLPAPER = "quick.music.wallpaper";


    public static class DefaultValue {
        public static final int DEFAULT_VALUE_CLOSE = 0;
        public static final int DEFAULT_VALUE_OPEN = 1;
        //中控锁
        public static final int Q_CENTER_LOCK = 0;
        //后尾门
        public static final int Q_REAR_TAIL_GATE = 0;
        //后视镜
        public static final int Q_REAR_MIRROR_FOLD = 0;
        // 后视镜弹窗开启状态
        public static final int Q_REAR_MIRROR_FOLD_DIALOG = 0;
        // 车窗状态
        public static final int Q_WINDOW = -1;
        //车窗锁
        public static final int Q_WINDOW_LOCK = 0;
        // 遮阳帘
        public static final int Q_SUNSHADE = -1;
        // 电动尾翼
        public static final int Q_AUTO_TAIL = 0;
        // 天窗
        public static final int Q_SKY_WINDOW = 0;
        //感应靠近解锁
        public static final int Q_APPROACHING_UNLOCK = 1;
        //感应离车解锁
        public static final int Q_DEPARTURE_LOCKING = 1;
        // 自动锁车升窗
        public static final int Q_LOCK_AUTO_RAISE_WINDOW = 0;
        // 锁车收起遮阳帘
        public static final int Q_LOCK_CAR_SUNROOF_SHADE = 1;
        // 设防提示
        public static final int Q_DEFENSE_REMINDER = 1;
        // 左儿童锁
        public static final int Q_LEFT_CHILD_LOCK = 0;
        // 右儿童锁
        public static final int Q_RIGHT_CHILD_LOCK = 0;
        // 自动落锁
        public static final int Q_AUTOMATIC_LOCKING = 1;
        // 驻车自动解锁
        public static final int Q_AUTOMATIC_PARKING_UNLOCK = 0;
        //外后视镜自动折叠
        public static final int Q_AUTO_REAR_MIRROR_FOLD = 1;
        //雨天自动加热外后视镜
        public static final int Q_AUTO_HOT_REAR_MIRROR = 0;
        // 座椅便携进入退出
        public static final int Q_SEAT_PORTABLE = 1;
        // 后视镜调节窗口状态
        public static final int Q_BACK_AUTO_REAR_MIRROR_ADJUST_WINDOW_STATE = 0;
        // 后排屏
        public static final int Q_REAR_SCREEN_CONTROL = 1;
        // 加油小门
        public static final int Q_REFUEL_SMALL_DOOR = 0;
        // 后视镜自动折叠
        public static final int Q_BACK_AUTO_REAR_MIRROR_ADJUST = 1;
        // 车辆下电
        public static final int Q_VECHICE_POWER_OFF = 0;
        // 后尾门高度
        public static final int Q_REAR_TAILGATE_ROATE = 95;
        // 雨刮器灵敏度
        public static final int Q_WIPER_SENS = 2;
        // 副驾安全气囊
        public static final int Q_DRIVE_AIR_BAG = 1;
        // 后尾门高度
        public static final int C_REAR_TAILGATE_ROATE = 95;
        // 雨刮器灵敏度
        public static final int C_WIPER_SENS = 2;
        // 设防提示
        public static final int C_DEFENSE_REMINDER = 1;
        // 自动锁车升窗
        public static final int C_LOCK_AUTO_RAISE_WINDOW = 0;
        // 左儿童锁
        public static final int C_LEFT_CHILD_LOCK = 0;
        // 右儿童锁
        public static final int C_RIGHT_CHILD_LOCK = 0;
        // 自动落锁
        public static final int C_AUTOMATIC_LOCKING = 1;
        // 驻车自动解锁
        public static final int C_AUTOMATIC_PARKING_UNLOCK = 0;
        // 雨刮维修模式
        public static final int C_WIPER_REPAIR_MODE = 0;
        // 自定义按键
        public static final int C_CUSTOM_BUTTON = 0;
        // FACE_ID
        public static final int C_FACE_ID = 5;
        // 位置选择
        public static final int C_POSITION_SELECT = 0;

        public static final boolean Q_CAR_DOOR_FL = false;
        public static final boolean Q_CAR_DOOR_FR = false;
        public static final boolean Q_CAR_DOOR_RL = false;
        public static final boolean Q_CAR_DOOR_RR = false;
        public static final boolean Q_CAR_WINDOW_FL = false;
        public static final boolean Q_CAR_WINDOW_FR = false;
        public static final boolean Q_CAR_WINDOW_RL = false;
        public static final boolean Q_CAR_WINDOW_RR = false;
        /***车内灯光********************/
        // 灯光开关
        public static final int L_LIGHT_EFFECT = 0;
        public static final int L_BRIGHTNESS_AUOT = 1;

        public static final String CUR_COLOR = "#FF0000";
        /***车外灯光********************/
        public static final int L_LAMP_CONTROL = 3;
        public static final int L_REAR_FOG_LAMP = 0;
        public static final int L_LAMP_HIGH = 3;

        // 靠近迎宾
        public static final int L_APPROACHING_WELCOME = 0;
        // 大灯音乐律动
        public static final int L_LAMP_MUSIC_RHYTHM = 0;
        // 智能远近光灯
        public static final int L_HIGH_LOW_SWITCH = 0;
        // 智能迎宾灯
        public static final int L_INTELLIGENT_WELCOME = 0;


        /***语音********************/
        // 媒体音量
        public static final int V_MEDIA = 12;
        // 导航音量
        public static final int V_NAVI = 5;
        // 语音音量
        public static final int V_VR = 5;
        // 电话音量
        public static final int V_PHONE = 12;
        // 报警音量
        public static final int V_ALARM = 5;
        // 车速
        public static final int C_CAR_SPEED = 120;
        // 超速报警
        public static final int C_OVER_SPEED = 1;
        public static final int C_OVER_SPEED_SEEKBAR = 10;
        // 疲劳驾驶提醒
        public static final int C_FATIGUE_DRIVING_SEEKBAR = 2;


        public static final int C_DRIVE_AIR_BAG = 1;

        // DMS摄像头开关
        public static final int R_DMS_CAMERA_STATUS = 0;

        // 油耗单位
        public static final int SYSTEM_INSTRUMENT_FUEL_UNIT = 0;
        // 胎压单位
        public static final int SYSTEM_TIRE_PRESSURE_UNIT = 0;
        // 电耗单位
        public static final int SYSTEM_POWER_CONSUMPTION_UNIT = 0;
        // 日期格式
        public static final int SYSTEM_DATE_TIME_FORMAT = 1;
        // 分析与改进
        public static final int SYSTEM_ANALYSIS = 1;
        //系统设备名称
        public static final String SYSTEM_DEVICE_INFO_NAME = "风云A9";

        public static final String SYSTEM_SYSTEM_SOFTWARE_VERSION = "00.10.00";
        public static final String SYSTEM_SYSTEM_HARDWARE_VERSION = "4.0.0";
        public static final String SYSTEM_TBOX_SOFTWARE_VERSION = "system.TBoxSoftWareVersion";
        public static final String SYSTEM_TBOX_HARDWARE_VERSION = "system.TBoxHardWareVersion";

        // 前排充电
        public static final int CONNECT_FRONT_CHARGING = 0;
        // 遗忘提醒
        public static final int CONNECT_FORGET_REMIND_STATE = 0;
        // 遗忘提醒提示
        public static final int CONNECT_FORGET_REMIND_TIP = 0;
        // 充电提醒
        public static final int CONNECT_CHARGING_REMIND_STATE = 0;
        // 高温提醒
        public static final int CONNECT_HIGH_TEMPERATURE_REMIND = 0;
        // 疲劳驾驶
        public static final int C_FATIGUE_DRIVING_REMINDER = 0;
        // 保养提示
        public static final int C_MAINTAIN_TIPS = 0;
        // 保养重置里程
        public static final int C_MAINTAIN_RESET_KM = 0;
        // 保养重置天数
        public static final int C_MAINTAIN_RESET_DAY = 0;
        // 分屏
        public static final int DISPLAY_FP = 0;
        // 疲劳检测
        public static final int R_FATIGUE_DETECTION_STATUS = 0;
        // 实现分心提醒
        public static final int R_DISTRACTION_STATUS = 0;
        //
        public static final int AUTO_TIME = 0;
        // 主驾打电话提醒
        public static final int R_CALL_STATUS = 0;
        // 主驾喝水提醒
        public static final int R_DRINK_STATUS = 0;
        // 主驾座椅加热
        public static final int R_SEAT_HEAT_STATUS = 0;
        // 座椅通风
        public static final int R_SEAT_VENTILATION_STATUS = 0;
        // 座位解锁
        public static final int R_SIGHT_UNLOCK_STATUS = 0;
        // 个性化问候
        public static final int R_GREET_STATUS = 0;
        // 抽烟关怀
        public static final int R_SMOKE_STATUS = 0;
        // 来电播报
        public static final int VOICE_CALL_BROADCAST = 0;
        // 导航压低媒体音
        public static final int LOWER_MEDIA_TONE = 1;
        // 声音记忆监听值
        // 导航
        public static final int NAVI_STATUS = 1;
        // 语音
        public static final int VOICE_STATUS = 1;
        // 电话
        public static final int PHONE_STATUS = 1;
        // 媒体
        public static final int MEDIA_STATUS = 1;
        // 报警
        public static final int ALARM_STATUS = 1;
        public static final Boolean Q_CAR_HOOD = false;
        public static final Object D_DMS_OPEN_STATUS = 0;
        public static final Boolean Q_MUSIC_WALLPAPER = false;
    }

    public static class GlobalValue {
        public static final String PREFIX = "content://settings/global/";
        // 超速报警
        public static final String C_OVER_SPEED = "vehiclesetting_OverSpeed";
        // 媒体音
        public static final String VOICE_MEDIA = "media_progress";
        // 导航音
        public static final String VOICE_NAVI = "navi_progress";
        // 语音
        public static final String VOICE_VR = "vr_progress";
        // 电话音
        public static final String VOICE_PHONE = "phone_progress";
        // 报警音
        public static final String VOICE_ALARM = "alarm_progress";
        // 超速报警阈值
        public static final String C_OVER_SPEED_VALUE = "vehiclesetting_OverSpeedValue";
        // 当前车速
        public static final String C_CAR_SPEED = "vehiclesetting_CarSpeed";
        // 疲劳驾驶
        public static final String C_FATIGUE_DRIVING_REMINDER = "vehiclesetting_DrivingReminder";
        // 疲劳驾驶阈值
        public static final String C_FATIGUE_DRIVING_REMINDER_VALUE = "vehiclesetting_DrivingReminderValue";
        // 保养提示
        public static final String C_MAINTAIN_TIPS = "vehiclesetting_MaintainTips";
        // 保养重置里程
        public static final String C_MAINTAIN_RESET_KM = "vehiclesetting_MaintainResetKm";
        // 保养重置天数

        public static final String L_LIGHT = "vehiclesetting_light";
        public static final String C_MAINTAIN_RESET_DAY = "vehiclesetting_MaintainResetDay";
        // 音律律动
        public static final String L_RHYTHM_CADENC = "vehiclesetting_rhythm_and_cadence";
        // 氛围灯开关
        public static final String L_LIGHT_SW = "vehiclesetting_light_sw";
        // 恢复
        public static final String L_LIGHT_WARNING_TRIGGERED = "vehiclesetting_light_warning_triggered";
        //
        public static final String L_SCENEMODE_ATMOSPHERE = "vehiclesetting_scenemode_atmosphere";

        // DMS摄像头
        public static final String R_DMS_CAMERA_STATUS = "recognition.camera";
        // 疲劳检测
        public static final String R_FATIGUE_DETECTION_STATUS = "recognition.fatigue";
        // 实现分心提醒
        public static final String R_DISTRACTION_STATUS = "recognition.distraction";
        // 主驾打电话提醒
        public static final String R_CALL_STATUS = "recognition.call";
        // 主驾喝水提醒
        public static final String R_DRINK_STATUS = "recognition.drink";
        // 座椅加热
        public static final String R_SEAT_HEAT_STATUS = "recognition.seat.heat";
        // 座椅通风
        public static final String R_SEAT_VENTILATION_STATUS = "recognition.seat.ventilation";
        // 视线解锁屏保
        public static final String R_SIGHT_UNLOCK_STATUS = "recognition.sight.unlock";
        // 个性化问候
        public static final String R_GREET_STATUS = "recognition.greet";
        // 抽烟关怀
        public static final String R_SMOKE_STATUS = "recognition.smoke";
        // 摄像头服务
        public static final String R_VISUAL_SERVICE = "recognition.visualService";

        // 视频限制
        public static final String DISPLAY_VIDEO_LIMIT = "persist.sys.setting.restrict";
        // 来电播报
        public static final String VOICE_CALL_BROADCAST = "incall.ttsbroadcast.mode";
        // 车外低速模拟音
        public static final String VOICE_LOW_SPEED_ANALOG = "voice.lowSpeedAnalog";
        // 仪表显示歌词
        public static final String DISPLAY_SHOW_LYRICS = "voice.showLyrics";
        // 分析与改进
        public static final String SYSTEM_ANALYSIS = "system.analysis";
        // 分屏
        public static final String DISPLAY_FP = "persist.sys.setting.split_screen";
        // 中控屏自动亮度
        public static final String DISPLAY_AUTO_ZKP = "display.auto.zkp";
        // 仪表屏自动亮度
        public static final String DISPLAY_AUTO_YBP = "display.auto.ybp";
        // 壁纸状态：3D:1 ; 2D:0
        public static final String WALLPAPER_STATE = "wallpaper.state";
        // 导航压低媒体音
        public static final String LOWER_MEDIA_TONE = "navi_duck_media";
        // 3D壁纸状态
        public static final String WALLPAPER_STATE_3D = "Wallpaper.state.3d";
        // 上一次3D壁纸状态
        public static final String WALLPAPER_STATE_3D_PRE = "Wallpaper.state.3d.pre";
        // 空调手势
        public static final String HVAC_GESTURE = "disable_hvac_gesture";
    }
}
