package com.bitech.vehiclesettings.adapter

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.BtDeviceBean
import com.bitech.vehiclesettings.databinding.ItemBtScannedBinding
import com.bitech.vehiclesettings.utils.EToast
import com.bitech.vehiclesettings.utils.LogUtil
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @ClassName: BtScanListAdapter
 * 
 * @Date:  2024/1/23 9:59
 * @Description: 蓝牙可用列表适配器.
 **/
class BtScanListAdapter(
    private var scanList: CopyOnWriteArrayList<BtDeviceBean>,
    private val onItemClick: (BtDeviceBean) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    // 2. 定义两种视图类型常量
    private val VIEW_TYPE_ITEM = 0
    private val VIEW_TYPE_EMPTY = 1

    // 根据列表是否为空返回不同的视图类型
    override fun getItemViewType(position: Int): Int {
        return if (scanList.isEmpty()) {
            VIEW_TYPE_EMPTY
        } else {
            VIEW_TYPE_ITEM
        }
    }

    // 视图创建，根据viewType加载不同布局
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == VIEW_TYPE_ITEM) {
            val binding = ItemBtScannedBinding.bind(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_bt_scanned, parent, false)
            )
            ScanViewHolder(binding)
        } else {
            val emptyView = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_bt_device_empty, parent, false)
            EmptyViewHolder(emptyView)
        }
    }

    override fun getItemCount(): Int {
        // 如果列表为空，我们仍然需要显示1个项目（即“空状态”提示），所以返回1
        return if (scanList.isEmpty()) 1 else scanList.size
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ScanViewHolder) {
            // 如果是正常的ScanViewHolder，执行您原来的绑定逻辑
            scanList.getOrNull(position)?.let {
                val btDevice = scanList[position]
                if (btDevice.device.name == null) {
                    // 蓝牙名称为空，显示mac地址
                    holder.btNameTv.text = btDevice.device.address
                } else {
                    // 蓝牙名称不为空，显示蓝牙名称
                    holder.btNameTv.text = btDevice.device.name
                }
                if (btDevice.isSupportWirelessCP) {
                    // 显示支持carPlay文言
                    holder.btCarplayTipsTv.visibility = View.VISIBLE
                } else {
                    // 隐藏支持carPlay文言
                    holder.btCarplayTipsTv.visibility = View.GONE
                }
                if (btDevice.bondState == BluetoothDevice.BOND_BONDING) {
                    // 显示蓝牙配对中动画
                    holder.btPairedLoadPb.visibility = View.VISIBLE
                } else {
                    // 隐藏蓝牙配对中动画
                    holder.btPairedLoadPb.visibility = View.GONE
                }
                holder.itemView.setOnClickListener {
                    if (holder.adapterPosition in 0 until scanList.size) {
                        LogUtil.d(TAG, "scan item is clicked position = ${holder.adapterPosition}")
                        EToast.showToast(MyApplication.getContext(), "正在与附近的设备配对，请检查设备消息", Toast.LENGTH_SHORT, false)
                        onItemClick(scanList[holder.adapterPosition])
                    }
                }
            }
        }
    }

    /**
     * 设置扫描列表.
     *
     * @param btScanList 蓝牙可用设备扫描列表.
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setScanList(btScanList: CopyOnWriteArrayList<BtDeviceBean>) {
//        scanList = btScanList
        scanList = CopyOnWriteArrayList(btScanList.filter { it.device.name != null })
        notifyDataSetChanged()
    }


    inner class EmptyViewHolder(view: View) : RecyclerView.ViewHolder(view)

    /**
     * @ClassName: ScanViewHolder
     * 
     * @Date:  2024/1/23 11:12
     * @Description: 蓝牙扫描列表ViewHolder.
     **/
    inner class ScanViewHolder(binding: ItemBtScannedBinding) :
        RecyclerView.ViewHolder(binding.root) {
        var btNameTv = binding.btNameTv
        var btCarplayTipsTv = binding.btCarplayTipsTv
        var btPairedLoadPb = binding.btPairedLoadingPb
    }

    companion object {
        // 日志标志位
        private const val TAG = "BtScanListAdapter"
    }
}
