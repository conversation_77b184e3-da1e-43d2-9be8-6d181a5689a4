package com.bitech.vehiclesettings.view.widget

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.PathInterpolator

object AnimationHelper {
    const val DEFAULT_DURATION = 300L
    const val OUT_ANIMATION_DELAY = 3000L

    var lastDialogCloseAnimatorSet: AnimatorSet? = null
    var lastLoadShedCloseAnimatorSet: AnimatorSet? = null

    fun startToastAnimation(toastView: View) {
        val interpolatorInAlpha = PathInterpolator(0.22f, 1f, 0.36f, 1.0f)
        val interpolatorInTransition = PathInterpolator(0.26f, 1.28f, 0.64f, 1.0f)
        val interpolatorOutAlpha = PathInterpolator(0.64f, 0.0f, 0.78f, 0.0f)
        val interpolatorOutTransition = PathInterpolator(0.36f, 0.0f, 0.63f, -0.29f)

        val inTransitionAnimator: Animator = ObjectAnimator.ofFloat(toastView, "translationY", 0f, 100f)
        inTransitionAnimator.interpolator = interpolatorInTransition

        val inAlphaAnimator: Animator = ObjectAnimator.ofFloat(toastView, "alpha", 0f, 1f)
        inAlphaAnimator.interpolator = interpolatorInAlpha

        val outTransitionAnimator: Animator = ObjectAnimator.ofFloat(toastView, "translationY", 100f, 0f)
        outTransitionAnimator.interpolator = interpolatorOutTransition
        outTransitionAnimator.startDelay = OUT_ANIMATION_DELAY

        val outAlphaAnimator: Animator = ObjectAnimator.ofFloat(toastView, "alpha", 1f, 0f)
        outAlphaAnimator.interpolator = interpolatorOutAlpha
        outAlphaAnimator.startDelay = OUT_ANIMATION_DELAY

        val toastAnimatorSet = AnimatorSet()
        toastAnimatorSet.play(inTransitionAnimator)
            .with(inAlphaAnimator)
            .with(outTransitionAnimator)
            .with(outAlphaAnimator)
        toastAnimatorSet.duration = DEFAULT_DURATION
        toastAnimatorSet.start()
    }

    fun startAnimation(toastView: View) {
        val inInterpolator = PathInterpolator(0.4f, 0.0f, 0.2f, 1.0f)

        val inTransitionAnimator: Animator = ObjectAnimator.ofFloat(toastView, "translationY", 80f, 0f)
        inTransitionAnimator.interpolator = inInterpolator

        val inAlphaAnimator: Animator = ObjectAnimator.ofFloat(toastView, "alpha", 0f, 1f)
        inAlphaAnimator.interpolator = inInterpolator

        lastDialogCloseAnimatorSet?.cancel()
        val animatorSet = AnimatorSet()
        animatorSet.duration = DEFAULT_DURATION
        animatorSet.play(inTransitionAnimator).with(inAlphaAnimator)
        animatorSet.start()
    }

    fun stopAnimation(toastView: View, animatorListener: IAnimatorListener) {
        val interpolator1 = PathInterpolator(0.4f, 0.0f, 0.2f, 1.0f)
        val interpolator2 = PathInterpolator(0.4f, 0.0f, 1.0f, 1.0f)

        val outTransitionAnimator: Animator = ObjectAnimator.ofFloat(toastView, "translationY", 0f, 80f)
        outTransitionAnimator.interpolator = interpolator2

        val outAlphaAnimator: Animator = ObjectAnimator.ofFloat(toastView, "alpha", 1f, 0f)
        outAlphaAnimator.interpolator = interpolator1

        lastDialogCloseAnimatorSet?.cancel()
        val dialogCloseAnimatorSet = AnimatorSet()
        dialogCloseAnimatorSet.duration = DEFAULT_DURATION
        dialogCloseAnimatorSet.play(outTransitionAnimator).with(outAlphaAnimator)
        dialogCloseAnimatorSet.addAnimatorListener {
            animationEnd { animation -> animatorListener.onAnimationEnd(animation) }
//            animationStart { animation -> animatorListener.onAnimationStart(animation) }
            animationCancel { animation -> animatorListener.onAnimationEnd(animation) }
//            animationRepeat { animation -> animatorListener.onAnimationRepeat(animation) }
        }
        dialogCloseAnimatorSet.start()
        lastDialogCloseAnimatorSet = dialogCloseAnimatorSet
    }

    fun startAnimation(contentView: View, bgView: View) {
        val alphaAnimator: Animator = ObjectAnimator.ofFloat(bgView, "alpha", 0.1f, 1f)

        contentView.pivotX = 0.01f
        contentView.pivotY = 0.01f

        val scaleXAnimator: Animator = ObjectAnimator.ofFloat(contentView, View.SCALE_X, 0.7f, 1f)
        val scaleYAnimator: Animator = ObjectAnimator.ofFloat(contentView, View.SCALE_Y, 0.7f, 1f)

        lastLoadShedCloseAnimatorSet?.cancel()
        val animatorSet = AnimatorSet()
        animatorSet.duration = DEFAULT_DURATION
        animatorSet.play(alphaAnimator)
            .with(scaleXAnimator)
            .with(scaleYAnimator)
        animatorSet.start()
    }

    fun stopAnimation(contentView: View, bgView: View, animatorListener: IAnimatorListener) {
        val bgAlphaAnimator: Animator = ObjectAnimator.ofFloat(bgView, "alpha", 1f, 0.0f)
        val contentAlphaAnimator: Animator = ObjectAnimator.ofFloat(contentView, "alpha", 1f, 0.0f)

        contentView.pivotX = 0.01f
        contentView.pivotY = 0.01f

        val scaleXAnimator: Animator = ObjectAnimator.ofFloat(contentView, View.SCALE_X, 1f, 0.7f)
        val scaleYAnimator: Animator = ObjectAnimator.ofFloat(contentView, View.SCALE_Y, 1f, 0.7f)

        lastLoadShedCloseAnimatorSet?.cancel()
        val animatorSet = AnimatorSet()
        animatorSet.duration = DEFAULT_DURATION
        animatorSet.play(bgAlphaAnimator)
            .with(contentAlphaAnimator)
            .with(scaleXAnimator)
            .with(scaleYAnimator)
        animatorSet.addAnimatorListener {
            animationEnd { animation -> animatorListener.onAnimationEnd(animation) }
            animationCancel { animation -> animatorListener.onAnimationEnd(animation) }
        }
        animatorSet.start()
        lastLoadShedCloseAnimatorSet = animatorSet
    }

    interface IAnimatorListener {
        fun onAnimationEnd(animation: Animator?) {}
//        fun onAnimationStart(animation: Animator?) {}
//        fun onAnimationCancel(animation: Animator?) {}
//        fun onAnimationRepeat(animation: Animator?) {}
    }

    inline fun Animator.addAnimatorListener(bridge: AnimatorListenerBridge.() -> Unit) =
        addListener(AnimatorListenerBridge().apply(bridge))

    class AnimatorListenerBridge : Animator.AnimatorListener {

        private var animationStart: ((Animator?) -> Unit)? = null
        private var animationEnd: ((Animator?) -> Unit)? = null
        private var animationCancel: ((Animator?) -> Unit)? = null
        private var animationRepeat: ((Animator?) -> Unit)? = null

        override fun onAnimationStart(animation: Animator) {
            animationStart?.invoke(animation)
        }

        override fun onAnimationEnd(animation: Animator) {
            animationEnd?.invoke(animation)
        }

        override fun onAnimationCancel(animation: Animator) {
            animationCancel?.invoke(animation)
        }

        override fun onAnimationRepeat(animation: Animator) {
            animationRepeat?.invoke(animation)
        }

        fun animationStart(listener: (Animator?) -> Unit) {
            animationStart = listener
        }

        fun animationEnd(listener: (Animator?) -> Unit) {
            animationEnd = listener
        }

        fun animationCancel(listener: (Animator?) -> Unit) {
            animationCancel = listener
        }

        fun animationRepeat(listener: (Animator?) -> Unit) {
            animationRepeat = listener
        }

    }

}