package com.bitech.vehiclesettings.viewmodel;

import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.vehiclesettings.utils.PrefsConst;

public class LightOutViewModel extends ViewModel {
    public LightOutViewModel() {
        lampControl.setValue(PrefsConst.DefaultValue.L_LAMP_CONTROL);
        rearFogLamp.setValue(PrefsConst.DefaultValue.L_REAR_FOG_LAMP);
        approachingWelcome.setValue(PrefsConst.DefaultValue.L_APPROACHING_WELCOME);

        highLowSwitch.setValue(PrefsConst.DefaultValue.L_HIGH_LOW_SWITCH);
        intelligentWelcome.setValue(PrefsConst.DefaultValue.L_INTELLIGENT_WELCOME);
    }

    // 车灯控制
    private MutableLiveData<Integer> lampControl = new MediatorLiveData<>();
    // 后雾灯 Rear fog lamp
    private MutableLiveData<Integer> rearFogLamp = new MediatorLiveData<>();
    // 大灯高度调节
    // 大灯延时关闭
    // 车灯设置 - 靠近迎宾
    private MutableLiveData<Integer> approachingWelcome = new MediatorLiveData<>();

    // 智能远近光切换 sw_high_low_switching
    private MutableLiveData<Integer> highLowSwitch = new MediatorLiveData<>();
    // 智能迎宾灯开关 intelligent_welcome
    private MutableLiveData<Integer> intelligentWelcome = new MediatorLiveData<>();
    // 智能迎宾灯

    public MutableLiveData<Integer> getLampControl() {
        return lampControl;
    }

    public void setLampControl(Integer status) {
        lampControl.postValue(status);
    }

    public MutableLiveData<Integer> getRearFogLamp() {
        return rearFogLamp;
    }

    public void setRearFogLamp(Integer status) {
        rearFogLamp.postValue(status);
    }

    public MutableLiveData<Integer> getApproachingWelcome() {
        return approachingWelcome;
    }

    public void setApproachingWelcome(Integer status) {
        approachingWelcome.postValue(status);
    }


    public MutableLiveData<Integer> getHighLowSwitch() {
        return highLowSwitch;
    }

    public void setHighLowSwitch(Integer status) {
        highLowSwitch.postValue(status);
    }

    public MutableLiveData<Integer> getIntelligentWelcome() {
        return intelligentWelcome;
    }

    public void setIntelligentWelcome(Integer status) {
        intelligentWelcome.postValue(status);
    }


}
