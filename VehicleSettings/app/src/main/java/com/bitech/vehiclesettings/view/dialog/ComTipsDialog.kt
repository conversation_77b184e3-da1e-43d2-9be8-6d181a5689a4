package com.bitech.vehiclesettings.view.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.ContextThemeWrapper
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogConfirmOneBtnBinding
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst

/**
 * @ClassName: ComTipsDialog
 * 
 * @Date:  2024/1/22 16:39
 * @Description: 自定义提示弹窗.
 **/
class ComTipsDialog(context: Context, private val type: Int) :
    Dialog(context, R.style.dialog), View.OnClickListener {

    // 提示弹窗视图对象
    private lateinit var binding: DialogConfirmOneBtnBinding

    // 提示弹窗消息
    private lateinit var message: String

    // 页面点击事件监听
    private var confirmDialogClickCallback: OnConfirmDialogClickCallback? = null

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        val themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue)
        val themedContext = ContextThemeWrapper(context, themeId)
        binding = DialogConfirmOneBtnBinding.inflate(
            LayoutInflater.from(themedContext)
        )
        setContentView(binding.root)
        // 初始化视图
        initView()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        // 设置对话框窗口属性
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_DISPLAY_OVERLAY
        attributes?.windowAnimations = 0
        window?.attributes = attributes
        // 外部点击可关闭
        setCanceledOnTouchOutside(true)
        // 对话框按钮监听
        binding.dialogConfirmKnowBtn.setOnClickListener(this)
        when (type) {
            Contacts.BT_PAIRED_FAIL_DIALOG -> {
                binding.dialogTitleTv.setText(R.string.bt_paired_fail)
                binding.dialogTipsSsv.visibility = View.VISIBLE
                binding.dialogTipsTv2.visibility = View.GONE
                binding.dialogTipsTv.text = message
                binding.dialogTipsTv.textSize = 24f
                //dialogTipsTv的文字靠左对齐
                binding.dialogTipsTv.textAlignment = View.TEXT_ALIGNMENT_TEXT_START
                binding.dialogConfirmKnowBtn.text = context.getString(R.string.dialog_ok_text)
                //binding.dialogConfirmKnowBtn的宽度设置为620dp
                binding.dialogConfirmKnowBtn.layoutParams.width = 900
            }

            Contacts.SC_SMART_HIGH_BEAM_ASSIST_DIALOG -> {
                binding.dialogTitleTv.setText(R.string.sc_light_smart_full_beam_text)
                binding.dialogTipsSsv.visibility = View.GONE
                binding.dialogTipsTv2.visibility = View.VISIBLE
                binding.dialogTipsTv2.text = message
            }

            Contacts.AD_LIMIT_SPEED_ASSIST_DIALOG -> {
                binding.dialogTitleTv.setText(R.string.ad_limit_speed_assist_text)
                binding.dialogTipsSsv.visibility = View.GONE
                binding.dialogTipsTv2.visibility = View.VISIBLE
                binding.dialogTipsTv2.text = message
            }

            Contacts.AD_INTELLIGENT_NAVI_EXIT_DIALOG -> {
                binding.dialogTitleTv.setText(R.string.ad_intelligent_navi_exit_text)
                binding.dialogTipsSsv.visibility = View.GONE
                binding.dialogTipsTv2.visibility = View.VISIBLE
                binding.dialogTipsTv2.text = message
            }


            else -> {
                // TODO:
            }
        }
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    /**
     * 设置标题警告提示语.
     *
     * @param tips 提示信息
     */
    fun setTips(tips: String) {
        message = tips
    }

    /**
     * dialog 按钮点击事件.
     *
     * @param view View
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.dialog_confirm_know_btn -> {
                LogUtil.d(TAG, "onClick : know is click!")
                confirmDialogClickCallback?.onDmsConfirmClick()
                dismiss()
            }

            else -> {
                // TODO:
            }
        }
    }

    /**
     * 设置确认按钮点击事件监听.
     *
     * @param callback
     */
    fun setDialogClickCallback(callback: OnConfirmDialogClickCallback) {
        confirmDialogClickCallback = callback
    }

    /**
     * @ClassName: OnConfirmDialogClickCallback
     * 
     * @Date:  2024/1/22 17:43
     * @Description: 确认按钮点击事件回调.
     **/
    interface OnConfirmDialogClickCallback {
        /**
         * 确认按钮被点击.
         *
         */
        fun onDmsConfirmClick()
    }

    companion object {
        // 日志标志位
        private const val TAG = "ComTipsDialog"
    }
}
