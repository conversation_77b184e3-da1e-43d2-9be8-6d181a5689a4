package com.bitech.vehiclesettings.carapi.constants;

public class CarDriving {

    public static class Prefs_WASH_MODE {
        public static final int REGULAR_WASH = 0;
        public static final int CONVEYOR_WASH = 1;
    }

    public static class DrivingKeyInputEvent {
        public static final int SHORT_PRESS = 0x1;
        public static final int LONG_PRESS = 0x2;
    }

    ;

    /**
     * 隐私政策同意情况
     * 0x0: 未同意
     * 0x1: 同意
     */
    public static class PrivacyPolicySts {
        public static final int NOT_AGREE = 0x0;
        public static final int AGREE = 0x1;
    }

    public static class SpeedVSOSig {

    }

    /**
     * 车辆模式设置
     */
    public static final class ICC_DriveModeSet_Req {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ECO = 0x1;
        public static final int NORMAL = 0x2;
        public static final int SPORT = 0x3;
        public static final int RAIN_SNOW = 0x4;
        public static final int MUD = 0x5;
        public static final int OFF_ROAD = 0x6;
        public static final int INDIVIDUAL = 0x7;
        public static final int ENERGY_SAVING_HYBRID = 0x8;
    }

    /**
     * 车辆模式反馈
     * 0x0:Not Active
     * 0x1:ECO
     * 0x2:Normal
     * 0x3:Sport
     * 0x4:Snow
     * 0x5:Off Road
     * 0x6:Individual
     * 0x7:Energy-saving hybrid
     * 0x8:Sand
     * 0x9:AI
     * 0xA:Wade
     * 0xB:Rock
     * 0xC:Mud
     * 0xD:Smart
     */
    public static final class VCC_1_DriveMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ECO = 0x1;
        public static final int NORMAL = 0x2;
        public static final int SPORT = 0x3;
        public static final int SNOW = 0x4;
        public static final int OFF_ROAD = 0x5;
        public static final int INDIVIDUAL = 0x6;
        public static final int ENERGY_SAVING_HYBRID = 0x7;
        public static final int SAND = 0x8;
        public static final int AI = 0x9;
        public static final int WADE = 0xA;
        public static final int ROCK = 0xB;
        public static final int MUD = 0xC;
        public static final int SMART = 0xD;
    }

    /**
     * 极致纯电发送
     */
    public static final class ICC_ForcedEVMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
    }

    /**
     * 极致纯电接收
     */
    public static final class ForcedEVMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 驾驶模式信号发送(动力模式)
     */
    public static final class ICC_PropulsionMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int COMFORTABLE = 0x1;
        public static final int NORMAL = 0x2;
        public static final int SPORT = 0x3;
        public static final int SNOW = 0x4;
        public static final int OFF_ROAD = 0x5;
    }

    /**
     * 驾驶模式信号接收(动力模式) // 默认标准
     */
    public static final class FLZCU_PropulsionMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int COMFORTABLE = 0x1;
        public static final int NORMAL = 0x2;
        public static final int SPORT = 0x3;
        public static final int SNOW = 0x4;
        public static final int OFF_ROAD = 0x5;
    }

    /**
     * 转向模式
     */
    public static final class ICC_SteeringMode {
        public static final int NORMAL = 0x1;
        public static final int SPORT = 0x2;
    }

    /**
     * 转向模式反馈 默认Normal
     */
    public static final class FLZCU_SteeringMode {
        public static final int NORMAL = 0x1;
        public static final int SPORT = 0x2;
    }

    /**
     * 悬架模式发送
     */
    public static final class ICC_SuspensionMode {
        public static final int SOFT = 0x1;
        public static final int HARD = 0x3;
    }

    /**
     * 悬架模式反馈 默认Normal
     */
    public static final class FLZCU_SuspensionDamping {
        public static final int SOFT = 0x1;
        public static final int HARD = 0x3;
    }

    /**
     * 个性化-制动模式发送
     */
    public static final class ICC_BrakePedalFeelMode {
        public static final int COMFORTABLE = 0x1;
        public static final int SPORT = 0x2;
    }

    /**
     * 个性化-制动模式反馈 默认Comfortable
     */
    public static final class FLZCU_BrakePedalFeelMode {
        public static final int COMFORTABLE = 0x1;
        public static final int SPORT = 0x2;
    }

    /**
     * 陡坡缓降
     */
    public static final class HDCCtrlSts {
        public static final int OFF = 0x0;
        public static final int NOT_ACTIVE = 0x1;
        public static final int ON_ACTIVE_BRAKING = 0x2;
    }

    /**
     * 陡坡缓降设置
     */
    public static final class TIHU_SetHDCOnOff {
        public static final int OFF = 0x2;
        public static final int ON = 0x1;
    }

    /**
     * 车身稳定控制发送
     */
    public static final class Set_ESPFunctionSts {
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
    }

    /**
     * 车身稳定控制接收
     */
    public static final class ESPSwitchStatus {
        public static final int ON = 0x0;
        public static final int OFF = 0x1;
    }

    /**
     * 自动驻车发送
     */
    public static final class IHU_AutHldSet {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 自动驻车获取
     */
    public static final class AVHSts {
        public static final int OFF = 0x0;
        public static final int STANDBY = 0x1;
        public static final int ACTIVE = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 刹车
     */
    public static class BrakePedalSts {
        public static final int NOT_APPLIED = 0x0;
        public static final int APPLIED = 0x1;
    }

    /**
     * EPB Set Command
     */
    public static class EPBSetCmd {
        public static final int NOT_ACTIVE = 0x0;
        public static final int RELEASE = 0x1;
        public static final int APPLY = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * EPB卡钳执行状态
     */
    public static class EPBActrSt {
        public static final int UNKNOW = 0x0;
        public static final int APPLIED = 0x1;
        public static final int RELEASED = 0x2;
        public static final int APPLYING = 0x3;
        public static final int RELEASING = 0x4;
        public static final int COMPLETELY_RELEASED = 0x5;
        public static final int HALF_APPLIED = 0x6;
        public static final int RESERVED = 0x7;
    }

    /**
     * 舒适制动 - 发送
     */
    public static class Set_CSTFunctionSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int CST_ON = 0x1;
        public static final int CST_OFF = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * 舒适制动 - 接收
     */
    public static class CST_Status {
        public static final int DISABLED = 0x0;
        public static final int STANDBY = 0x1;
        public static final int ACTIVE = 0x2;
        public static final int FAILURE = 0x3;
    }

    /**
     * 舒适制动等级-发送
     */
    public static class CST_SensitivityReq {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LOW = 0x1;
        public static final int MEDIUM = 0x2;
        public static final int HIGH = 0x3;
    }

    /**
     * 舒适制动等级-接收
     */
    public static class CST_SensitivitySts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LOW = 0x1;
        public static final int MEDIUM = 0x2;
        public static final int HIGH = 0x3;
    }

    /**
     * 悬架智能预瞄-发送
     */
    public static class ICC_ASUPreviewCont {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 悬架智能预瞄-接收
     */
    public static class ASU_1_PreviewContFb {
        public static final int INACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 牵引模式
     */
    public static class ICC_TowingMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 牵引模式接收
     */
    public static class VCU_TowingMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
        public static final int RESERVED = 0x2;
    }

    /**
     * 获取档位信号 VCU_PRNDGearAct
     * 0x0:Init
     * 0x1:P
     * 0x2:R
     * 0x3:N
     * 0x4:D
     */
    public static class VCU_PRNDGearAct {
        public static final int INIT = 0x0;
        public static final int P = 0x1;
        public static final int R = 0x2;
        public static final int N = 0x3;
        public static final int D = 0x4;
    }

    /**
     * 牵引模式是否可用
     */
    public static class VCU_TowModeAvailable {
        public static final int AVAILABLE = 0x0;
        public static final int NOT_AVAILABLE = 0x1;
    }

    /**
     * DMS开启状态
     */
    public static class DMS_OPEN_Status {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * DMS隐私协议同意情况
     */
    public static class DMS_Privacy_Agreement {
        public static final int NOT_AGREE = 0x0;
        public static final int AGREE = 0x1;
    }

    /**
     * 洗车模式 - 重上锁禁用状态
     */
    public static class FLZCU_RelockInhibit {
        public static final int NOT_INHIBIT = 0x0;
        public static final int INHIBIT = 0x1;
    }

    /**
     * 洗车模式 - 自动雨刮禁用状态
     */
    public static class FLZCU_AutoWipingInhibit {
        public static final int NOT_INHIBIT = 0x0;
        public static final int INHIBIT = 0x1;
    }

    /**
     * 洗车模式 - 尾翼禁用状态
     */
    public static class FLZCU_SopilerInhibit {
        public static final int NOT_INHIBIT = 0x0;
        public static final int INHIBIT = 0x1;
    }

    /**
     * 洗车模式 - 前左车窗
     */
    public static class FLWinPosn {

    }

    /**
     * 洗车模式 - 前右车窗
     */
    public static class FRWinPosn {

    }

    /**
     * 洗车模式 - 后左车窗
     */
    public static class RLWinPosn {

    }

    /**
     * 洗车模式 - 后右车窗
     */
    public static class RRWinPosn {

    }

    /**
     * 洗车模式 - 充电口禁用状态
     */
    public static class FLZCU_ChargeDoorInhibit {
        public static final int NOT_INHIBIT = 0x0;
        public static final int INHIBIT = 0x1;
    }

    /**
     * 洗车模式 - 离车上锁禁用状态
     */
    public static class FLZCU_LeavelockInhibit {
        public static final int NOT_INHIBIT = 0x0;
        public static final int INHIBIT = 0x1;
    }

    /**
     * 洗车模式 - 外后视镜折叠
     */
    public static class RearViewFoldSts {
        public static final int INVALID = 0x0;
        public static final int FOLD = 0x1;
        public static final int UNFOLD = 0x2;
    }

    /**
     * 洗车模式 - 外后世折叠发送
     * 0x0:Not Active
     * 0x1:Fold
     * 0x2:Unfold
     * 0x3:Reserved
     */
    public static class RearViewFoldCont {
        public static final int NOT_ACTIVE = 0x0;
        public static final int FOLD = 0x1;
        public static final int UNFOLD = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * 洗车模式 - 空调内循环状态
     */
    public static class TMS_CirculationModeDisplaySts {
        public static final int RECIRCULATION_MODE = 0x0;
        public static final int AIR_CIRCULATION_MODE = 0x1;
        public static final int TWO_LAYER_FLOW_MODE = 0x2;
        public static final int AUTO = 0x3;
    }

    /**
     * 洗车模式 - 电动门禁用
     */
    public static class FLDoorFunctionInhibit {
        public static final int NOT_INHIBIT = 0x0;
        public static final int INHIBIT = 0x1;
    }

    /**
     * 洗车模式 - 电释放
     */
    public static class FLZCU_OutsideReleaseInhibit {
        public static final int NOT_INHIBIT = 0x0;
        public static final int INHIBIT = 0x1;
    }

    /**
     * 洗车模式 - 发送
     * 0x0:NotActive
     * 0x1:OFF
     * 0x2:ON
     * 0x3:Reserved
     */
    public static class ICC_CleanMode {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int RESERVED = 0x3;
    }


    /**
     * 洗车模式 - 接收
     * 0x0:Not active
     * 0x1:OFF
     * 0x2:ON
     * 0x3:Reserved
     */
    public static class FLZCU_CleanModeStatus {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * 牵引模式失败原因
     * 0x0:No Command
     * 0x1:rake pedal applied
     * 0x2:Over Speed
     * 0x3:Charge Gun Connect
     * 0x4:EPB Can Not Lock
     * 0x5:EPB Can Not Release
     * 0x6:Invalid shift
     * 0x7:Shift fault
     */
    public static class VCU_DrvGearShiftFailureIndcn {
        public static final int NO_COMMAND = 0x0;
        public static final int RAKE_PEDAL_APPLIED = 0x1;
        public static final int OVER_SPEED = 0x2;
        public static final int CHARGE_GUN_CONNECT = 0x3;
        public static final int EPB_CAN_NOT_LOCK = 0x4;
        public static final int EPB_CAN_NOT_RELEASE = 0x5;
        public static final int INVALID_SHIFT = 0x6;
        public static final int SHIFT_FAULT = 0x7;
    }

    /**
     * 电源模式
     * 0x0:Off
     * 0x1:Comfortable
     * 0x2:ON
     * 0x3:Reserved
     */

    public static class FLZCU_9_PowerMode {
        public static final int OFF = 0x0;
        public static final int COMFORTABLE = 0x1;
        public static final int ON = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * DMS摄像头状态
     * 0x0:Init
     * 0x1:off/standby
     * 0x2:active
     * 0x3:fault
     * 0x4:camera blocked
     */
    public static class DMS_CameraStatus {
        public static final int INIT = 0x0;
        public static final int OFF = 0x1;
        public static final int ACTIVE = 0x2;
        public static final int FAULT = 0x3;
        public static final int CAMERA_BLOCKED = 0x4;
    }

    /**
     * 洗车模式失败原因
     * 0x0:Not Fail
     * 0x1:Fail
     */
    public static class FLZCU_CleanModeFailCause {
        public static final int NOT_FAIL = 0x0;
        public static final int FAIL = 0x1;
    }
}
