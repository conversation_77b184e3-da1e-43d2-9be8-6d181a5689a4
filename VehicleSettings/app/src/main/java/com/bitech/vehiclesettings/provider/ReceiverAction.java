package com.bitech.vehiclesettings.provider;

public class ReceiverAction {
    /**
     * 负一屏
     */
    public static final String ACTION_POWER_OFF_CLICK = "SliceReceiver.broadcast.PowerOff.click";
    public static final String ACTION_LIGHT = "SliceReceiver.broadcast.light";
    public static final String ACTION_VOICE = "SliceReceiver.broadcast.voice";
    public static final String ACTION_CLEAN_SCREEN = "SliceReceiver.broadcast.clean_screen";
    public static final String ACTION_UNLOCK_FUEL_PORT = "SliceReceiver.broadcast.unlock_fuel_port";
    public static final String ACTION_LOCK_SCREEN = "SliceReceiver.broadcast.lock_screen";
    public static final String ACTION_EPB = "SliceReceiver.broadcast.epb";
    public static final String ACTION_ESP = "SliceReceiver.broadcast.esp";
    public static final String ACTION_HDC = "SliceReceiver.broadcast.hdc";
    public static final String ACTION_AUTO_HOLD = "SliceReceiver.broadcast.auto_hold";
    public static final String ACTION_AVAS = "SliceReceiver.broadcast.avas";
    public static final String ACTION_BOOK_CHARGE = "SliceReceiver.broadcast.book_charge";
    public static final String ACTION_WIPER_LEVEL = "SliceReceiver.broadcast.wiper_level";
    public static final String ACTION_CENTER_LOCK = "SliceReceiver.broadcast.central_locking";
    public static final String ACTION_MIRROR_FOLD = "SliceReceiver.broadcast.mirror_fold";
    public static final String ACTION_MIRROR_ADJUST = "SliceReceiver.broadcast.mirror_adjust";
    public static final String ACTION_SUNSHADE = "SliceReceiver.broadcast.sunshade";
    public static final String ACTION_TAILGATE = "SliceReceiver.broadcast.tailgate";
    public static final String ACTION_WINDOW_LOCK = "SliceReceiver.broadcast.window_lock";
    public static final String ACTION_WINDOW_MODE_OPEN = "SliceReceiver.broadcast.window_mode_open";
    public static final String ACTION_WINDOW_MODE_AIR = "SliceReceiver.broadcast.window_mode_air";
    public static final String ACTION_WINDOW_MODE_CLOSE = "SliceReceiver.broadcast.window_mode_close";
    public static final String ACTION_CHILD_LOCK = "SliceReceiver.broadcast.child_lock";
    public static final String ACTION_BATTERY_LIFE = "SliceReceiver.broadcast.battery_life";
    public static final String ACTION_LIGHT_MODE = "SliceReceiver.broadcast.light_mode";
    public static final String ACTION_BROADCAST = "SliceReceiver.broadcast.broadcast";
    public static final String ACTION_DISPLAY_MODE_DAY = "SliceReceiver.broadcast.display_mode_day";
    public static final String ACTION_DISPLAY_MODE_NIGHT = "SliceReceiver.broadcast.display_mode_night";
    public static final String ACTION_DISPLAY_MODE_AUTO = "SliceReceiver.broadcast.display_mode_auto";
    public static final String ACTION_HEAD_LAMP_OFF = "SliceReceiver.broadcast.head_lamp_off";
    public static final String ACTION_HEAD_LAMP_LOCATION = "SliceReceiver.broadcast.head_lamp_location";
    public static final String ACTION_HEAD_LAMP_NEAR = "SliceReceiver.broadcast.head_lamp_near";
    public static final String ACTION_HEAD_LAMP_AUTO = "SliceReceiver.broadcast.head_lamp_auto";

    /**
     * 工程模式恢复出厂设置
     */
    public static final String ACTION_ENG_MODE_RESET_SYSTEM = "com.engmode.bitech.ResetSystem";
}
