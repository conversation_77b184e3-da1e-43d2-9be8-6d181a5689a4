package com.bitech.vehiclesettings.service;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.provider.ProviderURI;
import com.bitech.vehiclesettings.service.atmosphere.EffectDiapatcher;
import com.bitech.vehiclesettings.service.atmosphere.MusicRhythmEffectService;
import com.bitech.vehiclesettings.service.atmosphere.rhythm.VisualizerManager;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.PrefsConst;

/**
 * <AUTHOR>
 */
public class GlobalSettingsObserver extends ContentObserver {
    private static final String TAG = GlobalSettingsObserver.class.getSimpleName();
    private final Context mContext;
    private double curFrequency = 0.0f;
    private float currentDB = 0.0f;

    private EffectDiapatcher effectDiapatcher;


    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);

    public GlobalSettingsObserver(Context context, String settingKey, Handler vHandler) {
        super(vHandler);
        mContext = context;
        effectDiapatcher = EffectDiapatcher.getInstance();

    }

    @Override
    public void onChange(boolean selfChange, Uri uri) {
        if (Settings.Global.getUriFor(PrefsConst.GlobalValue.L_LIGHT_WARNING_TRIGGERED).equals(uri)) {
            int trigger = Settings.Global.getInt(mContext.getContentResolver(), PrefsConst.GlobalValue.L_LIGHT_WARNING_TRIGGERED, CommonConst.Triggered.NO);
            Log.d(TAG, "vehiclesetting_light_warning_triggered: " + trigger);
        }
        if (Settings.Global.getUriFor(PrefsConst.GlobalValue.L_LIGHT_SW).equals(uri)) {
            effectDiapatcher.handleEffectChange();
            SliceReceiver.notifyChange(ProviderURI.LIGHT_MODE);
        }
        if (Settings.Global.getUriFor(PrefsConst.GlobalValue.L_RHYTHM_CADENC).equals(uri)) {
            effectDiapatcher.handleEffectChange();
        }

    }


    private void RhythmStart() {
        VisualizerManager.getInstance().release();
        // 音乐律动
        VisualizerManager.getInstance().initVisualizer();
        VisualizerManager.getInstance().registerMusicDataListener(new VisualizerManager.MusicDataListener() {
            @Override
            public void onWaveChanged(double frequency) {
                curFrequency = frequency;
                //Log.d(TAG, "frequency:" + frequency);
            }

            @Override
            public void onFftChanged(float[] magnitudes, float cDB) {
                currentDB = cDB;
                //Log.d(TAG, "onFftChanged: " + Arrays.toString(magnitudes) + ",currentDB:" + cDB);
                MusicRhythmEffectService.getInstance().sendMusicColor(curFrequency, currentDB);
            }

            @Override
            public void onForceDestroy() {
                Log.d(TAG, "onForceDestroy");
            }
        });
    }


    private void RhythmStop() {
        VisualizerManager.getInstance().release();
    }


}
