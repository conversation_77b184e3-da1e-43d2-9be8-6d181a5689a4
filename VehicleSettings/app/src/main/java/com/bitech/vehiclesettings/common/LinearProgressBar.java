package com.bitech.vehiclesettings.common;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.animation.LinearInterpolator;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.utils.SystemColorUtil;

public class LinearProgressBar extends View {
    private int targetProgress = 0;
    private float currentProgress = 0f;
    private Context context;

    private Paint bgPaint;
    private Paint fgPaint;

    private ValueAnimator animator; //  控制动画引用

    public LinearProgressBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        init();
    }

    private void init() {
        bgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        bgPaint.setColor(Color.LTGRAY);

        fgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        // 获得当前系统色
        String systemColorValue = MyApplication.getInstance().getSystemColorValue();
        int color = SystemColorUtil.SystemColorStringToValueInt(systemColorValue);
        fgPaint.setColor(ContextCompat.getColor(context, color));
    }

    public void setProgress(int progress) {
        progress = Math.max(0, Math.min(progress, 100));
        this.targetProgress = progress;

        if (animator != null && animator.isRunning()) {
            animator.cancel();
        }

        animator = ValueAnimator.ofFloat(0, progress);
        animator.setInterpolator(new LinearInterpolator());

        animator.addUpdateListener(animation -> {
            currentProgress = (float) animation.getAnimatedValue();
            invalidate();
        });

        long duration = (long) (3000 * (progress / 100f));
        animator.setDuration(duration);
        animator.start();
    }

    public void setProgressClean() {
        if (animator != null && animator.isRunning()) {
            animator.cancel();
        }
        currentProgress = 0;
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        canvas.drawRect(0, 0, getWidth(), getHeight(), bgPaint);

        float progressWidth = getWidth() * (currentProgress / 100f);
        canvas.drawRect(0, 0, progressWidth, getHeight(), fgPaint);
    }

    public void setProgressColor(int color) {
        fgPaint.setColor(color);
        invalidate();
    }

    public void setBackgroundColorCustom(int color) {
        bgPaint.setColor(color);
        invalidate();
    }
}
