package com.bitech.vehiclesettings.provider;

import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.car.media.CarAudioManager;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.media.AudioAttributes;
import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.graphics.drawable.IconCompat;
import androidx.slice.Slice;
import androidx.slice.SliceManager;
import androidx.slice.SliceProvider;
import androidx.slice.builders.ListBuilder;
import androidx.slice.builders.SliceAction;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.platformlib.manager.NewEnergyManager;
import com.bitech.platformlib.manager.QuickManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.broadcast.SliceReceiver;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.manager.CarWifiManager;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.utils.DialogNavigationUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.viewmodel.WifiViewModel;

import java.util.Objects;

@SuppressLint("WrongConstant")
public class VehicleSliceProvider extends SliceProvider {

    private static final String TAG = VehicleSliceProvider.class.getSimpleName();
    private static final String HIDE = "{\"hidePanelWhenClick\": true}";

    private QuickManager quickManager = (QuickManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER);
    private DrivingManager drivingManager = (DrivingManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_DRIVING_MANAGER);
    private NewEnergyManager newEnergyManager = (NewEnergyManager) BitechCar.getInstance().getServiceManager(BitechCar.CAR_ENERGY_MANAGER);
    private LightManager lightManager;
    private CarWifiManager carWifiManager = CarWifiManager.Companion.getInstance();

    public VehicleSliceProvider() {
        super("android.permission.BIND_QUICK_SETTINGS_TILE");
    }

    @NonNull
    @Override
    public Uri onMapIntentToUri(@NonNull Intent intent) {
        Uri.Builder uriBuilder = new Uri.Builder().scheme(ContentResolver.SCHEME_CONTENT);

        if (intent == null) return uriBuilder.build();

        Uri data = intent.getData();
        if (data != null && data.getPath() != null) {
            String path = data.getPath().replace("/", "");
            uriBuilder = uriBuilder.path(path);

            Context context = getContext();
            if (context != null) {
                uriBuilder = uriBuilder.authority(context.getPackageName());
                return uriBuilder.build();
            }
        }
        return uriBuilder.build();

    }

    @Override
    public boolean onCreateSliceProvider() {
        lightManager = (LightManager) BitechCar.getInstance().getServiceManager(getContext(), BitechCar.CAR_LIGHT_MANAGER);
        try {
            ProviderURI.getAllUrisUsingReflection().forEach(uri -> {
                Log.d(TAG, "bind uri: " + uri);
                SliceManager.getInstance(Objects.requireNonNull(getContext())).grantSlicePermission("com.android.systemui", Uri.parse(uri));
            });
        } catch (IllegalAccessException e) {
            Log.d(TAG, "URI异常: ");
        }
        return true;
    }

//    @Override
//    @NonNull
//    public Uri onMapIntentToUri(@Nullable Intent intent) {
//        // Note: implementing this is only required if you plan on catching URL requests.
//        // This is an example solution.
//        Uri.Builder uriBuilder = new Uri.Builder().scheme(ContentResolver.SCHEME_CONTENT);
//        if (intent == null) {
//            return uriBuilder.build();
//        }
//        Uri data = intent.getData();
//        if (data != null && data.getPath() != null) {
//            String path = data.getPath().replace("/", "");
//            uriBuilder = uriBuilder.path(path);
//        }
//        Context context = getContext();
//        if (context != null) {
//            uriBuilder = uriBuilder.authority(context.getPackageName());
//        }
//        return uriBuilder.build();
//    }

    @Override
    public Slice onBindSlice(Uri sliceUri) {
        Log.i(TAG, "onBindSlice: " + sliceUri);
        Context context = getContext();
        if (context == null) {
            return null;
        }

        String uri = sliceUri.toString().replace(ProviderURI.PREFIX, "");

        switch (uri) {
            // 车辆下电
            case ProviderURI.POWER_OFF:
                int vcuPrndGearAct = drivingManager.getVCU_PRNDGearAct();
                boolean isGray = false;
                if (vcuPrndGearAct == CarDriving.VCU_PRNDGearAct.P || vcuPrndGearAct == CarDriving.VCU_PRNDGearAct.N) {
                    isGray = false;
                } else {
                    isGray = true;
                }

                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_vehicle_power_off))
                                .setSubtitle(setButtonGray(isGray))
                                .setPrimaryAction(createPowerOffAction(context)))
                        .build();
            // 屏幕清洁
            case ProviderURI.CLEAN_SCREEN:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_qjpm))
                                .setPrimaryAction(createCleanScreenAction(context)))
                        .build();
            // 加油口解锁、加油小门
            case ProviderURI.UNLOCK_FUEL_PORT:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_UnlockFuelPort))
                                .setPrimaryAction(createUnlockFuelPortAction(context)))
                        .build();
            // 屏保
            case ProviderURI.LOCK_SCREEN:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_LockScreen))
                                .setPrimaryAction(createLockScreenAction(context)))
                        .build();
            // 驻车制动
            case ProviderURI.EPB:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_EPB))
                                .setPrimaryAction(createEPBAction(context)))
                        .build();
            // ESP
            case ProviderURI.ESP:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_ESP))
                                .setPrimaryAction(createESPAction(context)))
                        .build();
            // 陡坡缓降
            case ProviderURI.HDC:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_HDC))
                                .setPrimaryAction(createHDCAction(context)))
                        .build();
            // 自动驻车
            case ProviderURI.AUTO_HOLD:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_AUTO_HOLD))
                                .setPrimaryAction(createAutoHoldAction(context)))
                        .build();
            // AVAS
            case ProviderURI.AVAS:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_AVM))
                                .setPrimaryAction(createAVASAction(context)))
                        .build();
            // 预约充电
            case ProviderURI.BOOK_CHARGE:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_BOOK_CHARGE))
                                .setPrimaryAction(createBookChargeAction(context)))
                        .build();
            // 雨刮灵敏度
            case ProviderURI.WIPER_LEVEL:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_WIPER_LEVEL))
                                .setPrimaryAction(createWiperLevelAction(context)))
                        .build();
            // 中控锁
            case ProviderURI.CENTER_LOCK:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_CENTER_LOCK))
                                .setPrimaryAction(createCenterLockAction(context)))
                        .build();
            // 后视镜折叠
            case ProviderURI.MIRROR_FOLD:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_MIRROR_FOLD))
                                .setPrimaryAction(createMirrorFoldAction(context)))
                        .build();
            // 后视镜调节
            case ProviderURI.MIRROR_ADJUST:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_MIRROR_ADJUST))
                                .setPrimaryAction(createMirrorAdjustAction(context)))
                        .build();
            // 遮阳帘
            case ProviderURI.SUNSHADE:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_SUNSHADE))
                                .setPrimaryAction(createSunshadeAction(context)))
                        .build();
            // 后尾门
            case ProviderURI.TAILGATE:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_TAILGATE))
                                .setPrimaryAction(createTailGateAction(context)))
                        .build();
            // 车窗锁
            case ProviderURI.WINDOW_LOCK:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_WINDOW_LOCK))
                                .setPrimaryAction(createWindowLockAction(context)))
                        .build();
            // 儿童锁
            case ProviderURI.CHILD_LOCK:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_CHILD_LOCK))
                                .setPrimaryAction(createChildLockAction(context)))
                        .build();
            // 驻车保电
            case ProviderURI.BATTERY_LIFE:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_BATTERY_LIFE))
                                .setPrimaryAction(createBatteryLifeAction(context)))
                        .build();
            // 氛围灯
            case ProviderURI.LIGHT_MODE:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_LIGHT_MODE))
                                .setPrimaryAction(createLightModeAction(context)))
                        .build();
            // 热点
            case ProviderURI.BROADCAST:
                int size = WifiViewModel.Companion.getInstance().getHotspotConnectedList().size();
                Log.d(TAG, "BROADCAST: size=" + size);
                String string = context.getString(R.string.str_BROADCAST);
                boolean hotspotState = carWifiManager.getHotspotState();
                Log.d(TAG, "BROADCAST: hotspotState=" + hotspotState);
                if (hotspotState) {
                    string = size + "个设备连接";
                }
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(string)
                                .addEndItem(createLongPressBroadcastAction(context))
                                .setPrimaryAction(createBroadcastAction(context, hotspotState)))
                        .build();
            // 显示模式
            case ProviderURI.DISPLAY_MODE:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_DISPLAY_MODE_DAY))
                                .setPrimaryAction(createDisplayModeDayAction(context)))
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_DISPLAY_MODE_NIGHT))
                                .setPrimaryAction(createDisplayModeNightAction(context)))
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_DISPLAY_MODE_AUTO))
                                .setPrimaryAction(createDisplayModeAutoAction(context)))
                        .build();
            // 车窗
            case ProviderURI.WINDOW_MODE:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_WINDOW_MODE_OPEN))
                                .setPrimaryAction(createWindowModeOpenAction(context)))
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_WINDOW_MODE_AIR))
                                .setPrimaryAction(createWindowModeAirAction(context)))
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_WINDOW_MODE_CLOSE))
                                .setPrimaryAction(createWindowModeCloseAction(context)))
                        .build();
            // 大灯
            case ProviderURI.HEAD_LAMP:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_head_lamp_off))
                                .setPrimaryAction(createHeadLampOFFAction(context)))
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_head_lamp_location))
                                .setPrimaryAction(createHeadLampLocationAction(context)))
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_head_lamp_near))
                                .setPrimaryAction(createHeadLampNearAction(context)))
                        .addRow(new ListBuilder.RowBuilder()
                                .setTitle(context.getString(R.string.str_head_lamp_auto))
                                .setPrimaryAction(createHeadLampAutoAction(context)))
                        .build();
            // 亮度
            case ProviderURI.LIGHT:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addInputRange(createLightSeekbarRow(context))
                        .build();
            // 音量
            case ProviderURI.VOICE:
                return new ListBuilder(context, sliceUri, ListBuilder.INFINITY)
                        .addInputRange(createVoiceSeekbarRow(context))
                        .build();

        }
        return null;
    }

    private SliceAction createBatteryLifeAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_BATTERY_LIFE);
        intent.setClass(context, SliceReceiver.class);
        int parkPowerStatus = newEnergyManager.getParkPowerStatus();
        boolean isChecked = parkPowerStatus == CarNewEnergy.ParkPowerSet.ON;
        IconCompat withResource = createIcon(context, R.mipmap.ic_battery_life, isChecked);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                HIDE,
                isChecked
        );
    }

    private SliceAction createSunshadeAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_SUNSHADE);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = false;
        IconCompat withResource = createIcon(context, R.mipmap.ic_sunshade, false);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                HIDE,
                isChecked
        );
    }

    private SliceAction createHeadLampOFFAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_HEAD_LAMP_OFF);
        intent.setClass(context, SliceReceiver.class);
        int lightMainSwitchSts = lightManager.getLightMainSwitchSts();
        boolean isChecked = lightMainSwitchSts == CarLight.LightMainSwitchSts.OFF;
        IconCompat withResource = createIcon(context, R.mipmap.ic_headlamp_off, isChecked);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                "headlamp",
                isChecked
        );
    }

    private SliceAction createHeadLampLocationAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_HEAD_LAMP_LOCATION);
        intent.setClass(context, SliceReceiver.class);
        int lightMainSwitchSts = lightManager.getLightMainSwitchSts();
        boolean isChecked = lightMainSwitchSts == CarLight.LightMainSwitchSts.POSITION_LAMP_SWITCH;
        IconCompat withResource = createIcon(context, R.mipmap.ic_headlamp_location, isChecked);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                "headlamp",
                isChecked
        );
    }

    private SliceAction createHeadLampNearAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_HEAD_LAMP_NEAR);
        intent.setClass(context, SliceReceiver.class);
        int lightMainSwitchSts = lightManager.getLightMainSwitchSts();
        boolean isChecked = lightMainSwitchSts == CarLight.LightMainSwitchSts.LOW_BEAM_SWITCH;
        IconCompat withResource = createIcon(context, R.mipmap.ic_headlamp_near, isChecked);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                "headlamp",
                isChecked
        );
    }

    private SliceAction createHeadLampAutoAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_HEAD_LAMP_AUTO);
        intent.setClass(context, SliceReceiver.class);
        int lightMainSwitchSts = lightManager.getLightMainSwitchSts();
        boolean isChecked = lightMainSwitchSts == CarLight.LightMainSwitchSts.AUTO;
        IconCompat withResource = createIcon(context, R.mipmap.ic_headlamp_auto, isChecked);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                "headlamp",
                isChecked
        );
    }

    private SliceAction createWindowModeCloseAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_WINDOW_MODE_CLOSE);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = false;
        IconCompat withResource = createIcon(context, R.mipmap.ic_windowmode_close, isChecked);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                "Window Lock Close",
                isChecked
        );
    }

    private SliceAction createWindowModeAirAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_WINDOW_MODE_AIR);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = false;
        IconCompat withResource = createIcon(context, R.mipmap.ic_windowmode_air, isChecked);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                "Window Lock Air",
                isChecked
        );
    }

    private SliceAction createWindowModeOpenAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_WINDOW_MODE_OPEN);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = false;
        IconCompat withResource = createIcon(context, R.mipmap.ic_windowmode_open, isChecked);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                "Window Lock Open",
                isChecked
        );
    }

    private SliceAction createWindowLockAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_WINDOW_LOCK);
        intent.setClass(context, SliceReceiver.class);
        int windowLock = quickManager.getWindowLock();
        boolean isChecked = windowLock == CarQuickControl.GetWindowLockSts.INHIBIT;
        IconCompat withResource = createIcon(context, R.mipmap.ic_windowlock, isChecked);
        switch (windowLock) {
            case CarQuickControl.GetWindowLockSts.INHIBIT:
                withResource = createIcon(context, R.mipmap.ic_windowlock, isChecked);
                break;
            case CarQuickControl.GetWindowLockSts.PERMIT:
                withResource = createIcon(context, R.mipmap.ic_windowunlock, isChecked);
                break;
        }
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ), withResource,
                "Window Lock",
                isChecked
        );
    }

    private SliceAction createPowerOffAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_POWER_OFF_CLICK);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = false;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_power_off, isChecked),
                HIDE,
                isChecked
        );
    }

    private SliceAction createCleanScreenAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_CLEAN_SCREEN);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = false;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_cleanscreen, isChecked),
                HIDE,
                isChecked
        );
    }

    private SliceAction createUnlockFuelPortAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_UNLOCK_FUEL_PORT);
        boolean isChecked = quickManager.getOilLidSwitch() == CarQuickControl.ButtonSts.ON;
        intent.setClass(context, SliceReceiver.class);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_unlock_fuel_port, isChecked),
                "Unlock Fuel Port",
                isChecked
        );
    }

    private SliceAction createLockScreenAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_LOCK_SCREEN);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = false;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_turn_off, isChecked),
                "Lock Screen",
                isChecked
        );
    }

    private SliceAction createEPBAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_EPB);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = drivingManager.getEPBStatus() == CarDriving.EPBActrSt.APPLIED;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_epb, isChecked),
                HIDE,
                isChecked
        );
    }

    private SliceAction createESPAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_ESP);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = drivingManager.getBodyInfoESC() != CarDriving.ESPSwitchStatus.ON;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_esp, isChecked),
                "ESP",
                isChecked
        );
    }


    private SliceAction createDisplayModeDayAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_DISPLAY_MODE_DAY);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = !DisplayPresenter.getAutoMode() && !DisplayPresenter.getDisplayMode();
        int iconRes = R.mipmap.ic_displaymode_day;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, iconRes, isChecked),
                "Light Mode Day",
                isChecked
        );
    }

    private SliceAction createDisplayModeNightAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_DISPLAY_MODE_NIGHT);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = !DisplayPresenter.getAutoMode() && DisplayPresenter.getDisplayMode();
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_displaymode_night, isChecked),
                "Light Mode Night",
                isChecked
        );
    }

    private SliceAction createDisplayModeAutoAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_DISPLAY_MODE_AUTO);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = DisplayPresenter.getAutoMode();
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_displaymode_auto, isChecked),
                "Light Mode Auto",
                isChecked
        );
    }

    private SliceAction createBroadcastAction(Context context, boolean isChecked) {
        Intent intent = new Intent(ReceiverAction.ACTION_BROADCAST);
        intent.setClass(context, SliceReceiver.class);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_broadcast, isChecked),
                "broadcast",
                isChecked
        );
    }
    private SliceAction createLongPressBroadcastAction(Context context) {
        Intent intent = DialogNavigationUtils.getHotspotIntent();
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_broadcast, true),
                "broadcast",
                true
        );
    }

    private SliceAction createLightModeAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_LIGHT_MODE);
        int sw = Prefs.getGlobalValue(PrefsConst.GlobalValue.L_LIGHT_SW, PrefsConst.FALSE);
        boolean isChecked = (sw == PrefsConst.TRUE);
        Log.d(TAG, "氛围灯开关: " + isChecked);
        intent.setClass(context, SliceReceiver.class);
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_light_mode, isChecked),
                "Light Mode",
                isChecked
        );
    }

    private SliceAction createChildLockAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_CHILD_LOCK);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = false;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_child_lock, isChecked),
                HIDE,
                isChecked
        );
    }

    private SliceAction createHDCAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_HDC);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = drivingManager.getHillDescentControl() != CarDriving.HDCCtrlSts.OFF;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_hdc, isChecked),
                "HDC",
                isChecked
        );
    }

    private SliceAction createAVASAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_AVAS);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = VoicePresenter.getInstance().getLowSpeedAnalog() == CarVoice.AVAS.ON;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_avas, isChecked),
                "AVAS",
                isChecked
        );
    }

    private SliceAction createAutoHoldAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_AUTO_HOLD);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = drivingManager.getAutoHold() != CarDriving.AVHSts.OFF;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_auto_hold, isChecked),
                "Auto Hold",
                isChecked
        );
    }

    private SliceAction createBookChargeAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_BOOK_CHARGE);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = newEnergyManager.getBookChargeSwitch() == CarNewEnergy.BookChargeSts.ON;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_book_charge, isChecked),
                "BookCharge",
                isChecked
        );
    }

    private SliceAction createWiperLevelAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_WIPER_LEVEL);
        intent.setClass(context, SliceReceiver.class);
        int wiperSensitivity = quickManager.getWiperSensitivity();
        IconCompat withResource;
        boolean isChecked = true;
        switch (wiperSensitivity) {
            case CarQuickControl.GetWipeSensitivitySts.LEVEL_1:
                withResource = IconCompat.createWithResource(context, R.mipmap.ic_wiper_level_low);
                break;
            case CarQuickControl.GetWipeSensitivitySts.LEVEL_2:
                withResource = IconCompat.createWithResource(context, R.mipmap.ic_wiper_level_standard);
                break;
            case CarQuickControl.GetWipeSensitivitySts.LEVEL_3:
                withResource = IconCompat.createWithResource(context, R.mipmap.ic_wiper_level_high);
                break;
            case CarQuickControl.GetWipeSensitivitySts.LEVEL_4:
                withResource = IconCompat.createWithResource(context, R.mipmap.ic_wiper_level_highest);
                break;
            default:
                withResource = IconCompat.createWithResource(context, R.mipmap.ic_wiper_level_low);
                break;
        }
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                withResource,
                "WiperLevel",
                isChecked
        );
    }

    private SliceAction createCenterLockAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_CENTER_LOCK);
        intent.setClass(context, SliceReceiver.class);
        int centerLock = quickManager.getCenterLock();
        boolean isChecked = centerLock == CarQuickControl.GetCentralLockSts.LOCKED ||
                centerLock == CarQuickControl.GetCentralLockSts.SUPERLOCKED;
        IconCompat withResource = createIcon(context, R.mipmap.ic_center_lock, isChecked);
        switch (centerLock) {
            case CarQuickControl.GetCentralLockSts.LOCKED:
            case CarQuickControl.GetCentralLockSts.SUPERLOCKED:
                withResource = createIcon(context, R.mipmap.ic_center_locked, isChecked);
                break;
            case CarQuickControl.GetCentralLockSts.UNLOCKED:
                withResource = createIcon(context, R.mipmap.ic_center_lock, isChecked);
                break;
        }
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                withResource,
                "CenterLock",
                isChecked
        );
    }

    private SliceAction createMirrorFoldAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_MIRROR_FOLD);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = quickManager.getRearMirror() == CarQuickControl.GetRearMirrorFoldSts.FOLD;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_mirror_fold, isChecked),
                "MirrorFold",
                isChecked
        );
    }

    private SliceAction createTailGateAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_TAILGATE);
        intent.setClass(context, SliceReceiver.class);
        int rearTailGate = quickManager.getRearTailGate();
        boolean isChecked = rearTailGate == CarQuickControl.GetRearTailGateSts.OPEN ||
                rearTailGate == CarQuickControl.GetRearTailGateSts.SECONDARY;
        IconCompat withResource = createIcon(context, R.mipmap.ic_reartailgate_off, isChecked);
        ;
        switch (rearTailGate) {
            case CarQuickControl.GetRearTailGateSts.OPEN:
            case CarQuickControl.GetRearTailGateSts.SECONDARY:
                withResource = createIcon(context, R.mipmap.ic_reartailgate_on, isChecked);
                break;
            case CarQuickControl.GetRearTailGateSts.LATCHED:
                withResource = createIcon(context, R.mipmap.ic_reartailgate_off, isChecked);
                break;
        }
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                withResource,
                "TailGate",
                isChecked
        );
    }

    private SliceAction createMirrorAdjustAction(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_MIRROR_ADJUST);
        intent.setClass(context, SliceReceiver.class);
        boolean isChecked = true;
        return SliceAction.createToggle(
                PendingIntent.getBroadcast(
                        context,
                        1,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
                ),
                createIcon(context, R.mipmap.ic_mirror_adjust, isChecked),
                HIDE,
                isChecked
        );
    }

    private ListBuilder.InputRangeBuilder createLightSeekbarRow(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_LIGHT);
        intent.setClass(context, SliceReceiver.class);

        PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context,
                2,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
        );

        int zkpBrightness = DisplayPresenter.getInstance().getZKPBrightness();
        zkpBrightness = Math.max(1, Math.min(zkpBrightness, 10));
        Log.d(TAG, "绑定负一屏亮度：" + zkpBrightness);
        boolean zkpAuto = CommonUtils.IntToBool(DisplayPresenter.getInstance().getZKPAuto());
        return new ListBuilder.InputRangeBuilder()
                .setMin(1)
                .setMax(10)
                .setTitle("{\"isAutoMode\": " + zkpAuto + "}")
                .setValue(zkpBrightness)
                .setInputAction(pendingIntent)
                .setPrimaryAction(SliceAction.createToggle(pendingIntent, "not used", true));
    }

    private ListBuilder.InputRangeBuilder createVoiceSeekbarRow(Context context) {
        Intent intent = new Intent(ReceiverAction.ACTION_VOICE);
        intent.setClass(context, SliceReceiver.class);
        CarAudioManager carAudioManager = VoicePresenter.getInstance().carAudioManager;
        int groupId = carAudioManager.getCurActiveGroupId();
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context,
                3,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
        );
        int volumeMax;
        int volumeMin;
        int usage;
        int volume;
        if (groupId == VoicePresenter.getInstance().groupMedia) {
            volumeMax = CarVoice.Media.MAX;
            volumeMin = CarVoice.Media.MIN;
            usage = AudioAttributes.USAGE_MEDIA;
        } else if (groupId == VoicePresenter.getInstance().groupPhone) {
            volumeMax = CarVoice.Phone.MAX;
            volumeMin = CarVoice.Phone.MIN;
            usage = AudioAttributes.USAGE_VOICE_COMMUNICATION;
        } else if (groupId == VoicePresenter.getInstance().groupNavi) {
            volumeMax = CarVoice.Navi.MAX;
            volumeMin = CarVoice.Navi.MIN;
            usage = AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE;
        } else if (groupId == VoicePresenter.getInstance().groupVR) {
            volumeMax = CarVoice.VR.MAX;
            volumeMin = CarVoice.VR.MIN;
            usage = AudioAttributes.USAGE_ASSISTANT;
        } else {
            volumeMax = CarVoice.Media.MAX;
            volumeMin = CarVoice.Media.MIN;
            usage = AudioAttributes.USAGE_MEDIA;
        }
        volume = carAudioManager.getCurGroupVolume(CarAudioManager.PRIMARY_AUDIO_ZONE);
        volume = Math.min(volume, volumeMax);
        Log.d(TAG, "最终音量：" + volume);
        return new ListBuilder.InputRangeBuilder()
                .setMin(volumeMin)
                .setMax(volumeMax)
                .setTitle("" + usage)
                .setValue(volume)
                .setInputAction(pendingIntent)
                .setPrimaryAction(SliceAction.createToggle(pendingIntent, "not used", true));
    }


    @Override
    public void onSlicePinned(Uri sliceUri) {
        Log.i(TAG, "onSlicePinned: " + sliceUri);
    }

    @Override
    public void onSliceUnpinned(Uri sliceUri) {
        Log.i(TAG, "onSliceUnpinned: " + sliceUri);
    }


    private IconCompat createIcon(Context context, int iconRes, boolean isChecked) {
        if (DisplayPresenter.getDisplayMode() && isChecked) {
            Bitmap bitmap = BitmapFactory.decodeResource(context.getResources(), iconRes);
            Bitmap invertedBitmap = invertBitmap(bitmap); // 反转颜色
            return IconCompat.createWithBitmap(invertedBitmap);
        } else {
            return IconCompat.createWithResource(context, iconRes);
        }
    }

    // 反转 Bitmap 颜色（黑变白、白变黑）
    private Bitmap invertBitmap(Bitmap src) {
        Bitmap output = Bitmap.createBitmap(src.getWidth(), src.getHeight(), src.getConfig());
        var canvas = new Canvas(output);
        Paint paint = new Paint();
        ColorMatrix matrix = new ColorMatrix(new float[]{
                -1, 0, 0, 0, 255, // R
                0, -1, 0, 0, 255, // G
                0, 0, -1, 0, 255, // B
                0, 0, 0, 1, 0     // A
        });
        paint.setColorFilter(new ColorMatrixColorFilter(matrix));
        canvas.drawBitmap(src, 0, 0, paint);
        return output;
    }

    private String setButtonGray(boolean gray) {
        return "{'isSetButtonGray':" + gray + "}";
    }
}