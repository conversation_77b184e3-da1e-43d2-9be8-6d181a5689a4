package com.bitech.vehiclesettings.presenter.driving;

public interface DrivingPresenterListener {
    // 加载和设置车辆模式
    void setCarMode(int state);
    int getCarMode();

    // 接受和发送极致纯电信号
    void setExtremePureSignal(int status);
    int getExtremePureSignal();

    // 陡坡缓降
    void setSteepSlopeDescent(int status);
    int getSteepSlopeDescent();

    // 车身稳定控制
    void setBodyStabilityControl(int status);
    int getBodyStabilityControl();

    // 自动驻车
    void setAutoParking(int status);
    int getAutoParking();

    // 驻车制动
    void setParkingBrake(int status);
    int getParkingBrake();

    // 舒适制动
    void setComfortBraking(int status);
    int getComfortBraking();

    // 舒适制动等级
    void setComfortBrakingRank(int status);
    int getComfortBrakingRank();

    // 用户是否踩了刹车
    int getBrakePedal();

    // 悬架智能预瞄
    void setIntelligentSuspensionAiming(int status);
    int getIntelligentSuspensionAiming();

    // 个性化-车辆模式
    void setDrivingMode(int status);
    int getDrivingMode();
    int enableChangeDrivingMode();
    // 个性化-保电电量
    void setPowerProtection(int status);
    int getPowerProtection();

    // 个性化-转向模式
    void setSwerveMode(int status);
    int getSwerveMode();

    // 个性化-悬架模式
    void setSuspensionMode(int status);
    int getSuspensionMode();

    // 个性化-制动模式
    void setBrakingMode(int status);
    int getBrakingMode();

    // 牵引模式
    void setTractionMode(int status);
    int getTractionMode();
    int enableTractionMode();
    // 牵引模式失败原因
    int getTractionModeFailReason();

    void setCarModeToICU(int state);

    void setPrivacyPolicyStatus(int status);
    int getPrivacyPolicyStatus();

    void setSwCamera(int status);
    int getSwCamera();
}
