package com.bitech.vehiclesettings.service.voice

import android.car.Car
import android.car.media.CarAudioManager
import android.content.Context
import android.media.AudioAttributes
import android.util.Log
import com.bitech.platformlib.manager.QuickManager
import com.bitech.platformlib.manager.VoiceManager
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarDisplay
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.fragment.VoiceFragment
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.DialogNavigationUtils
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import com.bitech.vehiclesettings.utils.PrefsConst.DefaultValue.ALARM_STATUS
import com.bitech.vehiclesettings.utils.PrefsConst.DefaultValue.MEDIA_STATUS
import com.bitech.vehiclesettings.utils.PrefsConst.DefaultValue.NAVI_STATUS
import com.bitech.vehiclesettings.utils.PrefsConst.DefaultValue.PHONE_STATUS
import com.bitech.vehiclesettings.utils.PrefsConst.DefaultValue.VOICE_STATUS
import com.bitech.vehiclesettings.view.voice.EffectAdjustmentUIAlert
import com.bitech.vehiclesettings.view.voice.HeadrestSpeakerUIAlert
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P

internal class VoiceControl() {
    private val mQuickManager: QuickManager = QuickManager.getInstance()
    private val mcontext: Context = MyApplication.getContext()
    private val mVoiceManager: VoiceManager = VoiceManager.getInstance()
    private val mVoicePresenter: VoicePresenter = VoicePresenter.getInstance()


    fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    //多回复提示语id
    private fun sendResultCode(respondId: String, mValue: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    //特殊提示语id
    private fun sendResultCode(respondId: String, mValue: String, mUnique: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue
        param.unique = mUnique
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")

    }

    /**
     * 头枕扬声器界面拉起
     * @parameter null
     * @return null
     */
    fun headrestdilog() {
        // 判断界面是否拉起
        if (!HeadrestSpeakerUIAlert.isShow) {
            DialogNavigationUtils.launchMainActivity(
                mcontext,
                MainActivity.MainTabIndex.VOICE,
                VoiceFragment.HEAD_REST,
                CommonConst.DIALOG_OPEN
            )
        }
    }

    /**
     * 取消静音
     * @parameter value 0=导航，1=语音，2=电话，3=媒体，4=报警，5=标准，6=蓝牙音乐，7=开机背景，98=未指定音源，99=无效值
     * @return null
     */
    fun setUnmute(value: String) {
        val volumeType = value.split(" ")[0]
        // 车设定义参数 SOUND_TYPE_NAVI = 0 SOUND_TYPE_VOICE = 1 SOUND_TYPE_PHONE = 2 SOUND_TYPE_MEDIA = 3 SOUND_TYPE_ALARM = 4
        when (volumeType) {
            CarSettingConstant.SOUND_TYPE_NAVI -> {
                // 导航
                if (mVoicePresenter.getVoiceNavi() == CarSettingConstant.SOUND_OFF) {
                    sendResultCode(VDVRRespondID.volume_unmute_1, "导航")
                    mVoicePresenter.setVoiceNavi(
                        Prefs.get(
                            PrefsConst.C_REMEMBER_NAVI_VOLUME, NAVI_STATUS
                        )
                    )
                } else {
                    sendResultCode(VDVRRespondID.volume_unmute_2)
                }
            }

            CarSettingConstant.SOUND_TYPE_VOICE -> {
                // 语音
                if (mVoicePresenter?.getVoiceVR() == CarSettingConstant.SOUND_OFF) {
                    sendResultCode(VDVRRespondID.volume_unmute_1, "语音")
                    mVoicePresenter?.setVoiceVR(
                        Prefs.get(
                            PrefsConst.C_REMEMBER_VOICE_VOLUME, VOICE_STATUS
                        )
                    )
                } else {
                    sendResultCode(VDVRRespondID.volume_unmute_2)
                }
            }

            CarSettingConstant.SOUND_TYPE_PHONE -> {
                // 电话
                if (mVoicePresenter?.getVoicePhone() == CarSettingConstant.SOUND_OFF) {
                    sendResultCode(VDVRRespondID.volume_unmute_1, "电话")
                    mVoicePresenter?.setVoicePhone(
                        Prefs.get(
                            PrefsConst.C_REMEMBER_PHONE_VOLUME, PHONE_STATUS
                        )
                    )
                } else {
                    sendResultCode(VDVRRespondID.volume_unmute_2)
                }
            }

            CarSettingConstant.SOUND_TYPE_MEDIA -> {
                // 媒体
                if (mVoicePresenter?.getVoiceMedia() == CarSettingConstant.SOUND_OFF) {
                    sendResultCode(VDVRRespondID.volume_unmute_1, "媒体")
                    mVoicePresenter?.setVoiceMedia(
                        Prefs.get(
                            PrefsConst.C_REMEMBER_MEDIA_VOLUME, MEDIA_STATUS
                        )
                    )
                } else {
                    sendResultCode(VDVRRespondID.volume_unmute_2)
                }
            }

            CarSettingConstant.SOUND_TYPE_ALARM -> {
                // 报警音
                if (mVoicePresenter.getVoiceAlarm() == CarSettingConstant.SOUND_OFF) {
                    sendResultCode(VDVRRespondID.volume_unmute_1, "报警音")
                    mVoicePresenter.setVoiceAlarm(
                        Prefs.get(
                            PrefsConst.C_REMEMBER_ALARM_VOLUME, ALARM_STATUS
                        )
                    )
                } else {
                    sendResultCode(VDVRRespondID.volume_unmute_2)
                }
            }

            "5" -> sendResultCode(VDVRRespondID.volume_unmute_3)
            "6" -> sendResultCode(VDVRRespondID.volume_unmute_3)
            "7" -> sendResultCode(VDVRRespondID.volume_unmute_3)
            "98" -> sendResultCode(VDVRRespondID.volume_unmute_3)
            "99" -> sendResultCode(VDVRRespondID.volume_unmute_3)
        }
    }

    /**
     * 设置按键音
     * @parameter value
     * @return null
     */
    fun setModeSound(value: String) {
        // 车设定义参数 SOUND_ON = 1 SOUND_OFF = 0
        when (value.toInt()) {
            // 打开按键音
            Constant.VR_PARAM_SOUND_ON -> {
                // 判断按键音状态
                if (mVoicePresenter.getButtonSound() == CarSettingConstant.SOUND_ON) {
                    sendResultCode(VDVRRespondID.set_volume_mode_2)
                } else {
                    sendResultCode(VDVRRespondID.set_volume_mode_1)
                    mVoicePresenter.setButtonSound(CarSettingConstant.SOUND_ON)
                }
            }
            // 关闭按键音
            Constant.VR_PARAM_SOUND_OFF -> {
                // 判断按键音状态
                if (mVoicePresenter.getButtonSound() == CarSettingConstant.SOUND_OFF) {
                    sendResultCode(VDVRRespondID.close_volume_mode_2)
                } else {
                    sendResultCode(VDVRRespondID.close_volume_mode_1)
                    mVoicePresenter.setButtonSound(CarSettingConstant.SOUND_OFF)
                }
            }
        }
    }

    /**
     * 设置随速补偿
     * @parameter flag flag = 1为开启随音补偿，flag = 0为关闭随音补偿
     * @return null
     */
    fun setVolumeCompensation(flag: Boolean) {
        // 车设定义参数 SPEED_COMPENSATION_OFF = 1 SPEED_COMPENSATION_LOW = 2 SPEED_COMPENSATION_MID = 3 SPEED_COMPENSATION_HIGH = 4
        if (flag) {
            if (mVoiceManager.getCompensation() == CarSettingConstant.SPEED_COMPENSATION_OFF) {
                sendResultCode(VDVRRespondID.open_noise_volume_compensation_2)
                mVoiceManager.setCompensation(CarSettingConstant.SPEED_COMPENSATION_MID)
            } else {
                sendResultCode(VDVRRespondID.open_noise_volume_compensation_3)
            }
        } else {
            if (mVoiceManager.getCompensation() == CarSettingConstant.SPEED_COMPENSATION_OFF) {
                sendResultCode(VDVRRespondID.close_noise_volume_compensation_3)
            } else {
                sendResultCode(VDVRRespondID.close_noise_volume_compensation_2)
                mVoiceManager.setCompensation(CarSettingConstant.SPEED_COMPENSATION_OFF)
            }
        }
    }

    /**
     * 随速补偿设置为高中低
     * @parameter value = 0为低,value = 1为中,value = 2为高
     * @return null
     */
    fun setSpeedRewardSet(value: String) {
        // 车设定义参数 SPEED_COMPENSATION_OFF = 1 SPEED_COMPENSATION_LOW = 2 SPEED_COMPENSATION_MID = 3 SPEED_COMPENSATION_HIGH = 4
        when (value) {
            // 随速补偿设置为低
            Constant.VR_PARAM_SPEED_COMPENSATION_LOW -> {
                if (mVoiceManager.getCompensation() == CarSettingConstant.SPEED_COMPENSATION_LOW) {
                    sendResultCode(VDVRRespondID.Speed_compensation_setting_2)
                } else {
                    sendResultCode(VDVRRespondID.Speed_compensation_setting_1)
                    mVoiceManager.setCompensation(CarSettingConstant.SPEED_COMPENSATION_LOW)
                }
            }
            // 随速补偿设置为中
            Constant.VR_PARAM_SPEED_COMPENSATION_MID -> {
                if (mVoiceManager.getCompensation() == CarSettingConstant.SPEED_COMPENSATION_MID) {
                    sendResultCode(VDVRRespondID.Speed_compensation_setting_2)
                } else {
                    sendResultCode(VDVRRespondID.Speed_compensation_setting_1)
                    mVoiceManager.setCompensation(CarSettingConstant.SPEED_COMPENSATION_MID)
                }
            }
            // 随速补偿设置为高
            Constant.VR_PARAM_SPEED_COMPENSATION_HIGH -> {
                if (mVoiceManager.getCompensation() == CarSettingConstant.SPEED_COMPENSATION_HIGH) {
                    sendResultCode(VDVRRespondID.Speed_compensation_setting_2)
                } else {
                    sendResultCode(VDVRRespondID.Speed_compensation_setting_1)
                    mVoiceManager.setCompensation(CarSettingConstant.SPEED_COMPENSATION_HIGH)
                }
            }
            // 无效参数
            Constant.VR_PARAM_SPEED_COMPENSATION_INVALID -> {
                sendResultCode(VDVRRespondID.Speed_compensation_setting_3)
            }
        }

    }


    /**
     * 设置环绕音
     * @parameter flag true为开启环绕音效，false为关闭环绕音效
     * @return null
     */
    fun setSurroundSoundMode(flag: Boolean) {
        var status = mVoiceManager.getSurroundSound()
        if (flag) {
            // 判断环绕音开启状态
            if (status == CarSettingConstant.SURROUND_SOUND_MODE_ON) {
                sendResultCode(VDVRRespondID.open_surround_sound_mode_2)
            } else {
                // 判断头枕扬声器是否开启私享模式
                if (mVoiceManager.getHeadRest() == CarSettingConstant.HEADREST_SX) {
                    // 拉起音效设置界面
                    if (!EffectAdjustmentUIAlert.isShow) {
                        DialogNavigationUtils.launchMainActivity(
                            mcontext,
                            MainActivity.MainTabIndex.VOICE,
                            VoiceFragment.EFFECT_ADJUST,
                            CommonConst.DIALOG_OPEN
                        )
                    }
                    sendResultCode(VDVRRespondID.open_surround_sound_mode_4)
                }
                // 判断声场模式是否为全车模式
                else if (mVoiceManager.getEQ() != CarSettingConstant.EQ_ALL) {
                    // 拉起音效设置界面
                    if (!EffectAdjustmentUIAlert.isShow) {
                        DialogNavigationUtils.launchMainActivity(
                            mcontext,
                            MainActivity.MainTabIndex.VOICE,
                            VoiceFragment.EFFECT_ADJUST,
                            CommonConst.DIALOG_OPEN
                        )
                    }
                    sendResultCode(VDVRRespondID.open_surround_sound_mode_5)
                }
                // 执行打开环绕音操作
                else {
                    sendResultCode(VDVRRespondID.open_surround_sound_mode_1)
                    status = CarSettingConstant.SURROUND_SOUND_MODE_ON
                    mVoiceManager.setSurroundSound(status)
                }
            }
        } else {
            // 判断环绕音开启状态
            if (status == CarSettingConstant.SURROUND_SOUND_MODE_OFF) {
                sendResultCode(VDVRRespondID.close_surround_sound_mode_1)
            } else {
                sendResultCode(VDVRRespondID.close_surround_sound_mode_2)
                status = CarSettingConstant.SURROUND_SOUND_MODE_OFF
                mVoiceManager.setSurroundSound(status)
            }
        }
    }

    /**
     * 设置车外音效外放模式
     * @parameter flag 0为车外音效模式打开状态，1为关闭状态
     * @return null
     */
    fun setExternalSoundMode(flag: Boolean) {
        // status 获取车外音效模式状态 1为开启状态，0为关闭状态
        var status = mVoicePresenter.getVoiceExternalMode()
        if (flag) {
            // 判断车外音效模式开启状态
            if (status == CarSettingConstant.EXTERNAL_SOUND_MODE_ON) {
                sendResultCode(VDVRRespondID.open_outside_car_sound_playback_mode_2)
            } else {
                // 判断车速是否等于0
                if (mQuickManager.getCarSpeed() == CarSettingConstant.SPEED_0) {
                    // 执行开启车外音效外放模式
                    status = CarSettingConstant.EXTERNAL_SOUND_MODE_ON
                    mVoicePresenter.setVoiceExternalMode(status)
                    sendResultCode(VDVRRespondID.open_outside_car_sound_playback_mode_1)
                } else if (mQuickManager.getCarSpeed() > CarSettingConstant.SPEED_0) {
                    sendResultCode(VDVRRespondID.open_outside_car_sound_playback_mode_3)
                }
            }
        } else {
            // 判断车外音效模式开启状态
            if (status == CarSettingConstant.EXTERNAL_SOUND_MODE_OFF) {
                sendResultCode(VDVRRespondID.close_outside_car_sound_playback_mode_1)
            } else {
                sendResultCode(VDVRRespondID.close_outside_car_sound_playback_mode_2)
                status = CarSettingConstant.EXTERNAL_SOUND_MODE_OFF
                mVoicePresenter.setVoiceExternalMode(status)
            }
        }
    }


    /**
     * 虚拟现场模式
     * @parameter value 0:关闭, 1: 音乐厅, 2:演唱会，3: 风云HIFI, 4:体育馆
     * @return null
     */
    fun setVirtualSceneMode(value: String) {
        headrestdilog()
        when (value) {
            // 关闭虚拟现场模式
            Constant.VR_PARAM_VIRTUAL_SCENE_MODE_CLOSE -> {
                sendResultCode(VDVRRespondID.close_virtual_live_mode_4)
            }
            // 演唱会
            Constant.VR_PARAM_VIRTUAL_SCENE_MODE_CONCERT -> {
                if (mVoiceManager.getVirtualScene() == CarSettingConstant.VIRTUAL_SCENE_MODE_CONCERT) {
                    sendResultCode(VDVRRespondID.open_virtual_live_mode_2, "音乐厅")
                } else {
                    if (mVoiceManager.getSurroundSound() == CarSettingConstant.SURROUND_SOUND_MODE_ON) {
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_4)
                    } else if (mVoiceManager.getHeadRest() == CarSettingConstant.HEADREST_SX) {
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_5)
                    } else if (mVoiceManager.getEQ() != CarSettingConstant.EQ_ALL) {
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_6)
                    } else {
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_1, "音乐厅")
                        mVoiceManager.setVirtualScene(CarSettingConstant.VIRTUAL_SCENE_MODE_CONCERT)
                    }
                }
            }
            // 音乐厅
            Constant.VR_PARAM_VIRTUAL_SCENE_MODE_MUSICHALL -> {
                if (mVoiceManager.getVirtualScene() == CarSettingConstant.VIRTUAL_SCENE_MODE_MUSICHALL) {
                    sendResultCode(VDVRRespondID.open_virtual_live_mode_2, "演唱会")
                } else {
                    if (mVoiceManager.getSurroundSound() == CarSettingConstant.SURROUND_SOUND_MODE_ON) {
                        // 拉起头枕扬声器界面
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_4)
                    } else if (mVoiceManager.getHeadRest() == CarSettingConstant.HEADREST_SX) {
                        // 拉起头枕扬声器界面
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_5)
                    } else if (mVoiceManager.getEQ() != CarSettingConstant.EQ_ALL) {
                        // 拉起头枕扬声器界面
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_6)
                    } else {
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_1, "演唱会")
                        mVoiceManager.setVirtualScene(CarSettingConstant.VIRTUAL_SCENE_MODE_MUSICHALL)
                    }
                }
            }
            // 风云HIFI
            Constant.VR_PARAM_VR_PARAM_VIRTUAL_SCENE_MODE_FYHIFI -> {
                if (mVoiceManager.getVirtualScene() == CarSettingConstant.VIRTUAL_SCENE_MODE_FYHIFI) {
                    sendResultCode(VDVRRespondID.open_virtual_live_mode_2, "风云HIFI")
                } else {
                    if (mVoiceManager.getSurroundSound() == CarSettingConstant.SURROUND_SOUND_MODE_ON) {
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_4)
                    } else if (mVoiceManager.getHeadRest() == CarSettingConstant.HEADREST_SX) {
                        // 拉起头枕扬声器界面
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_5)
                    } else if (mVoiceManager.getEQ() != CarSettingConstant.EQ_ALL) {
                        // 拉起头枕扬声器界面
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_6)
                    } else {
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_1, "风云HIFI")
                        mVoiceManager.setVirtualScene(CarSettingConstant.VIRTUAL_SCENE_MODE_FYHIFI)
                    }
                }
            }
            // 体育馆
            Constant.VR_PARAM_VIRTUAL_VIRTUAL_SCENE_MODE_STADIUM -> {
                if (mVoiceManager.getVirtualScene() == CarSettingConstant.VIRTUAL_SCENE_MODE_STADIUM) {
                    sendResultCode(VDVRRespondID.open_virtual_live_mode_2, "体育馆")
                } else {
                    if (mVoiceManager.getSurroundSound() == CarSettingConstant.SURROUND_SOUND_MODE_ON) {
                        // 拉起头枕扬声器界面
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_4)
                    } else if (mVoiceManager.getHeadRest() == CarSettingConstant.HEADREST_SX) {
                        // 拉起头枕扬声器界面
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_5)
                    } else if (mVoiceManager.getEQ() != CarSettingConstant.EQ_ALL) {
                        // 拉起头枕扬声器界面
                        headrestdilog()
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_6)
                    } else {
                        sendResultCode(VDVRRespondID.open_virtual_live_mode_1, "体育馆")
                        mVoiceManager.setVirtualScene(CarSettingConstant.VIRTUAL_SCENE_MODE_STADIUM)
                    }
                }
            }
            // 未定义参数
            else -> {
                sendResultCode(VDVRRespondID.close_virtual_live_mode_3)
            }
        }
    }

    /**
     * 设置音效模式
     * @parameter flag 1 打开音效模式界面
     * @return null
     */
    fun setSoundMode(flag: Boolean) {
        if (flag) {
            // 判断界面是否打开
            if (!EffectAdjustmentUIAlert.isShow) {
                // 拉起界面
                DialogNavigationUtils.launchMainActivity(
                    mcontext,
                    MainActivity.MainTabIndex.VOICE,
                    VoiceFragment.EFFECT_ADJUST,
                    CommonConst.DIALOG_OPEN
                )
            }
            sendResultCode(VDVRRespondID.open_acoustics_mode_1)
        } else {
            sendResultCode(VDVRRespondID.close_acoustics_mode_1)
        }
    }

    /**
     * 报警音设置
     * @parameter 0:叠引空间 1:音乐律动 2:梦幻科技 3:国风 4:科技 5:新潮
     * @return
     */
    fun setTypeAlarm(value: String) {
        // 车设定义参数 0: 国风 1: 科技 2: 新潮
        when (value) {
            // 国风
            Constant.VR_PARAM_ALARM_NATIONAL -> {
                // 判断状态是否为国风
                if (VoicePresenter.getAlarmType() != CarSettingConstant.ALARM_NATIONAL) {
                    sendResultCode(VDVRRespondID.set_the_instrument_alarm_tone_to_number_1, "国风")
                    VoicePresenter.setAlarmType(CarSettingConstant.ALARM_NATIONAL)
                } else {
                    sendResultCode(VDVRRespondID.set_the_instrument_alarm_tone_to_number_2, "国风")
                }
            }
            // 科技
            Constant.VR_PARAM_ALARM_TECHNOLOGY -> {
                // 判断状态是否为科技
                if (VoicePresenter.getAlarmType() != CarSettingConstant.ALARM_TECHNOLOGY) {
                    sendResultCode(VDVRRespondID.set_the_instrument_alarm_tone_to_number_1, "科技")
                    VoicePresenter.setAlarmType(CarSettingConstant.ALARM_TECHNOLOGY)
                } else {
                    sendResultCode(VDVRRespondID.set_the_instrument_alarm_tone_to_number_2, "科技")
                }
            }
            // 新潮
            Constant.VR_PARAM_ALARM_SMART -> {
                // 判断状态是否为新潮
                if (VoicePresenter.getAlarmType() != CarSettingConstant.ALARM_SMART) {
                    sendResultCode(VDVRRespondID.set_the_instrument_alarm_tone_to_number_1, "新潮")
                    VoicePresenter.setAlarmType(CarSettingConstant.ALARM_SMART)
                } else {
                    sendResultCode(VDVRRespondID.set_the_instrument_alarm_tone_to_number_2, "新潮")
                }
            }
            // 未定义的参数
            else -> {
                sendResultCode(VDVRRespondID.set_the_instrument_alarm_tone_to_number_3)
            }
        }
    }

    /**
     * 声场模式设置
     * @parameter value 0=全车模式，1=主驾模式，2=会员模式, 3=全车均衡，4=主驾均衡,5=老板位, 6=自定义, 7=后排静音，8=前排均衡，9=后排均衡，10=自适应
     * @return null
     */
    fun setModeSoundField(value: String) {
        // 车设定义参数 0：不存在 1：自定义 2：全车模式 3：主驾模式 4：前排均衡 5：后排均衡
        when (value) {
            // 全车均衡
            Constant.VR_PARAM_EQ_ALL -> {
                if (mVoiceManager.getEQ() == CarSettingConstant.EQ_ALL) {
                    sendResultCode(VDVRRespondID.sound_field_set_5, "全车均衡")
                } else {
                    sendResultCode(VDVRRespondID.sound_field_set_1, "全车均衡")
                    mVoiceManager.setEQ(CarSettingConstant.EQ_ALL)
                }
            }
            // 主驾均衡
            Constant.VR_PARAM_EQ_DRIVER -> {
                if (mVoiceManager.getEQ() == CarSettingConstant.EQ_DRIVER) {
                    sendResultCode(VDVRRespondID.sound_field_set_5, "主驾均衡")
                } else {
                    if (mVoiceManager.getSurroundSound() == CarSettingConstant.SURROUND_SOUND_MODE_ON) {
                        sendResultCode(VDVRRespondID.sound_field_set_3_1, "主驾均衡")
                        mVoiceManager.setSurroundSound(CarSettingConstant.SURROUND_SOUND_MODE_OFF)
                        mVoiceManager.setEQ(CarSettingConstant.EQ_DRIVER)
                    } else if (mVoiceManager.getVirtualScene() != CarSettingConstant.VIRTUAL_SCENE_MODE_OFF) {
                        sendResultCode(VDVRRespondID.sound_field_set_4, "主驾均衡")
                        mVoiceManager.setVirtualScene(CarSettingConstant.VIRTUAL_SCENE_MODE_OFF)
                        mVoiceManager.setEQ(CarSettingConstant.EQ_DRIVER)
                    }
                }
            }
            // 自定义
            Constant.VR_PARAM_EQ_CUSTOMIZED -> {
                if (mVoiceManager.getEQ() == CarSettingConstant.EQ_CUSTOMIZED) {
                    sendResultCode(VDVRRespondID.sound_field_set_5, "自定义")
                } else {
                    sendResultCode(VDVRRespondID.sound_field_set_1, "自定义")
                    mVoiceManager.setEQ(CarSettingConstant.EQ_CUSTOMIZED)
                }
            }
            // 前排均衡
            Constant.VR_PARAM_EQ_SLEEP -> {
                if (mVoiceManager.getEQ() == CarSettingConstant.EQ_SLEEP) {
                    sendResultCode(VDVRRespondID.sound_field_set_5, "前排均衡")
                } else {
                    sendResultCode(VDVRRespondID.sound_field_set_1, "前排均衡")
                    mVoiceManager.setEQ(CarSettingConstant.EQ_SLEEP)
                }
            }
            // 后排均衡
            Constant.VR_PARAM_EQ_VIP -> {
                if (mVoiceManager.getEQ() == CarSettingConstant.EQ_VIP) {
                    sendResultCode(VDVRRespondID.sound_field_set_5, "后排均衡")
                } else {
                    if (mVoiceManager.getSurroundSound() == CarSettingConstant.SURROUND_SOUND_MODE_ON) {
                        sendResultCode(VDVRRespondID.sound_field_set_3_1, "后排均衡")
                        mVoiceManager.setSurroundSound(CarSettingConstant.SURROUND_SOUND_MODE_OFF)
                        mVoiceManager.setEQ(CarSettingConstant.EQ_VIP)
                    } else if (mVoiceManager.getVirtualScene() != CarSettingConstant.VIRTUAL_SCENE_MODE_OFF) {
                        sendResultCode(VDVRRespondID.sound_field_set_4, "后排均衡")
                        mVoiceManager.setVirtualScene(CarSettingConstant.VIRTUAL_SCENE_MODE_OFF)
                        mVoiceManager.setEQ(CarSettingConstant.EQ_VIP)
                    }
                }

            }
            // 未定义的参数
            else -> {
                sendResultCode(VDVRRespondID.sound_field_set_3)
            }
        }
    }



    /**打开静音
     * 0x0:Not Active
     * @param value
     */
    fun setMute(value: String) {
        val volumeType = value.split("-")[0]
        // volumeType 0=导航，1=语音，2=电话，3=媒体，4=报警，5=标准，6=蓝牙音乐，7=开机背景，98=未指定音源，99=无效值
        // SOUND_TYPE_NAVI = 0 SOUND_TYPE_VOICE = 1 SOUND_TYPE_PHONE = 2
        // SOUND_TYPE_MEDIA = 3 SOUND_TYPE_ALARM = 4
        val mCar = Car.createCar(mcontext)
        val mCarAudioManager = mCar.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
        // 获取当前活动的音频组ID
        val currentGroupId =
            mCarAudioManager.getCurActiveGroupId(CarAudioManager.PRIMARY_AUDIO_ZONE)
        // 根据不同使用场景获取对应的组ID
        val mediaGroupId =
            mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA)
        val navGroupId =
            mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE)
        val phoneGroupId =
            mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
        val vrGroupId =
            mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT)
        val alarmGroupId =
            mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ALARM)
        // 判断当前活动的是哪个音频组并返回对应信息
        when (volumeType) {
            CarSettingConstant.SOUND_TYPE_NAVI -> {
                // 导航
                if (mVoicePresenter?.getVoiceNavi() != CarSettingConstant.SOUND_OFF) {
                    Prefs.put(PrefsConst.C_REMEMBER_NAVI_VOLUME, mVoicePresenter?.getVoiceNavi())
                    mVoicePresenter?.setVoiceNavi(CarSettingConstant.SOUND_OFF)
                    sendResultCode(VDVRRespondID.volume_mute_1, "导航")
                } else {
                    sendResultCode(VDVRRespondID.volume_mute_2)
                }
            }

            CarSettingConstant.SOUND_TYPE_VOICE -> {
                // 语音
                if (mVoicePresenter?.getVoiceVR() != CarSettingConstant.SOUND_OFF) {
                    Log.d("sendResultCode", "sendResultCode:语音音关闭")
                    Prefs.put(PrefsConst.C_REMEMBER_VOICE_VOLUME, mVoicePresenter?.getVoiceVR())
                    mVoicePresenter?.setVoiceVR(CarSettingConstant.SOUND_OFF)
                    sendResultCode(VDVRRespondID.volume_mute_1, "语音")
                } else {
                    Log.d("sendResultCode", "sendResultCode:语音音已关闭")
                    sendResultCode(VDVRRespondID.volume_mute_2)
                }
            }

            CarSettingConstant.SOUND_TYPE_PHONE -> {
                // 电话
                if (mVoicePresenter?.getVoicePhone() != CarSettingConstant.SOUND_OFF) {
                    Prefs.put(PrefsConst.C_REMEMBER_PHONE_VOLUME, mVoicePresenter?.getVoicePhone())
                    mVoicePresenter?.setVoicePhone(CarSettingConstant.SOUND_OFF)
                    mVoicePresenter?.setVoicePhone(0)
                    sendResultCode(VDVRRespondID.volume_mute_1, "电话")
                } else {
                    sendResultCode(VDVRRespondID.volume_mute_2)
                }
            }

            CarSettingConstant.SOUND_TYPE_MEDIA -> {
                // 媒体
                var voice = mVoicePresenter?.getVoiceMedia()
                Log.d("voice", "voice: $voice")
                if (mVoicePresenter?.getVoiceMedia() != CarSettingConstant.SOUND_OFF) {
                    Prefs.put(PrefsConst.C_REMEMBER_MEDIA_VOLUME, mVoicePresenter?.getVoiceMedia())
                    mVoicePresenter?.setVoiceMedia(CarSettingConstant.SOUND_OFF)
                    sendResultCode(VDVRRespondID.volume_mute_1, "媒体")
                } else {
                    sendResultCode(VDVRRespondID.volume_mute_2)
                }
            }

            CarSettingConstant.SOUND_TYPE_ALARM -> {
//                 报警音
                Log.d("sendResultCode", "sendResultCode:报警音无法关闭")
                sendResultCode(VDVRRespondID.volume_mute_alarm_1)
            }

            CarSettingConstant.SOUND_TYPE_STANDARD -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BLUETOOTH -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BOOT -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_UNKNOWN -> {
                when (currentGroupId) {
                    mediaGroupId -> {
                        var voice = mVoicePresenter?.getVoiceMedia()
                        Log.d("voice", "voice: $voice")
                        if (mVoicePresenter?.getVoiceMedia() != CarSettingConstant.SOUND_OFF) {
                            mVoicePresenter?.setVoiceMedia(CarSettingConstant.SOUND_OFF)
                            Prefs.put(
                                PrefsConst.C_REMEMBER_MEDIA_VOLUME, mVoiceManager.getVoiceMedia()
                            )
                            sendResultCode(VDVRRespondID.volume_mute_1, "媒体")
                        } else {
                            sendResultCode(VDVRRespondID.volume_mute_2)
                        }
                    }

                    navGroupId -> {
                        if (mVoicePresenter?.getVoiceNavi() != CarSettingConstant.SOUND_OFF) {
                            mVoicePresenter?.setVoiceNavi(CarSettingConstant.SOUND_OFF)
                            Prefs.put(
                                PrefsConst.C_REMEMBER_NAVI_VOLUME, mVoiceManager.getVoiceNavi()
                            )
                            sendResultCode(VDVRRespondID.volume_mute_1, "导航")
                        } else {
                            sendResultCode(VDVRRespondID.volume_mute_2)
                        }
                    }

                    phoneGroupId -> {
                        if (mVoicePresenter?.getVoicePhone() != CarSettingConstant.SOUND_OFF) {
                            mVoicePresenter?.setVoicePhone(CarSettingConstant.SOUND_OFF)
                            Prefs.put(
                                PrefsConst.C_REMEMBER_PHONE_VOLUME, mVoiceManager.getVoicePhone()
                            )
                            mVoicePresenter?.setVoicePhone(0)
                            sendResultCode(VDVRRespondID.volume_mute_1, "电话")
                        } else {
                            sendResultCode(VDVRRespondID.volume_mute_2)
                        }
                    }

                    vrGroupId -> {
                        if (mVoicePresenter?.getVoiceVR() != CarSettingConstant.SOUND_OFF) {
                            Log.d("sendResultCode", "sendResultCode:语音音关闭")
                            mVoicePresenter?.setVoiceVR(CarSettingConstant.SOUND_OFF)
                            Prefs.put(
                                PrefsConst.C_REMEMBER_VOICE_VOLUME, mVoiceManager.getVoiceVR()
                            )
                            sendResultCode(VDVRRespondID.volume_mute_1, "语音")
                        } else {
                            Log.d("sendResultCode", "sendResultCode:语音音已关闭")
                            sendResultCode(VDVRRespondID.volume_mute_2)
                        }
                    }

                    alarmGroupId -> {
                        if (mVoicePresenter?.getVoiceAlarm() != CarSettingConstant.SOUND_OFF) {
                            mVoicePresenter!!.setVoiceAlarm(CarSettingConstant.SOUND_OFF)
                            Prefs.put(
                                PrefsConst.C_REMEMBER_ALARM_VOLUME, mVoicePresenter.getVoiceAlarm()
                            )
                            sendResultCode(VDVRRespondID.volume_mute_1, "报警")
                        } else {
                            sendResultCode(VDVRRespondID.volume_mute_2)
                        }
                        Log.d("sendResultCode", "sendResultCode:报警音无法关闭")
                        sendResultCode(VDVRRespondID.volume_mute_alarm_1)
                    }

                    else -> {
                        // 默认返回媒体音量
                        var voice = mVoicePresenter?.getVoiceMedia()
                        Log.d("voice", "voice: $voice")
                        if (mVoicePresenter?.getVoiceMedia() != CarSettingConstant.SOUND_OFF) {
                            mVoicePresenter?.setVoiceMedia(CarSettingConstant.SOUND_OFF)
                            Prefs.put(
                                PrefsConst.C_REMEMBER_MEDIA_VOLUME, mVoiceManager.getVoiceMedia()
                            )
                            sendResultCode(VDVRRespondID.volume_mute_1, "媒体")
                        } else {
                            sendResultCode(VDVRRespondID.volume_mute_2)
                        }
                    }
                }
            }

            else -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
        }
    }

    /**查询音量
     * @param value
     */
    fun getVolume(value: String) {
//        0=导航，1=语音，2=电话，3=媒体，4=报警，98=未指定音源，99=无效值
        val mCar = Car.createCar(mcontext)
        val mCarAudioManager = mCar.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
        // 获取当前活动的音频组ID
        val currentGroupId =
            mCarAudioManager.getCurActiveGroupId(CarAudioManager.PRIMARY_AUDIO_ZONE)
        val mediaGroupId = mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA)
        val navGroupId =
            mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE)
        val phoneGroupId =
            mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
        val vrGroupId = mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT)
        val alarmGroupId = mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ALARM)
        when (value) {
            CarSettingConstant.SOUND_TYPE_NAVI -> {
                val voice = mVoicePresenter?.getVoiceNavi().toString()
                val reply = "导航-" + voice
                sendResultCode(VDVRRespondID.query_current_volume_1, reply)
            }

            CarSettingConstant.SOUND_TYPE_VOICE -> {
                val voice = mVoicePresenter?.getVoiceVR().toString()
                val reply = "语音-" + voice
                sendResultCode(VDVRRespondID.query_current_volume_1, reply)
            }

            CarSettingConstant.SOUND_TYPE_PHONE -> {
                val voice = mVoicePresenter?.getVoicePhone().toString()
                val reply = "电话-" + voice
                sendResultCode(VDVRRespondID.query_current_volume_1, reply)
            }

            CarSettingConstant.SOUND_TYPE_MEDIA -> {
                val voice = mVoicePresenter?.getVoiceMedia().toString()
                val reply = "媒体-" + voice
                sendResultCode(VDVRRespondID.query_current_volume_1, reply)
            }

            CarSettingConstant.SOUND_TYPE_ALARM -> {
                val voice = mVoicePresenter?.getVoiceAlarm().toString()
                val reply = "报警-" + voice
                sendResultCode(VDVRRespondID.query_current_volume_1, reply)
            }

            CarSettingConstant.SOUND_TYPE_UNKNOWN -> {
                when (currentGroupId) {
                    mediaGroupId -> {
                        val voice = mVoicePresenter?.getVoiceMedia().toString()
                        val reply = "媒体-" + voice
                        sendResultCode(VDVRRespondID.query_current_volume_1, reply)
                    }

                    navGroupId -> {
                        val voice = mVoicePresenter?.getVoiceNavi().toString()
                        val reply = "导航-" + voice
                        sendResultCode(VDVRRespondID.query_current_volume_1, reply)
                    }

                    phoneGroupId -> {
                        val voice = mVoicePresenter?.getVoicePhone().toString()
                        val reply = "电话-" + voice
                        sendResultCode(VDVRRespondID.query_current_volume_1, reply)
                    }

                    vrGroupId -> {
                        val voice = mVoicePresenter?.getVoiceVR().toString()
                        val reply = "语音-" + voice
                        sendResultCode(VDVRRespondID.query_current_volume_1, reply)
                    }

                    alarmGroupId -> {
                        val voice = mVoicePresenter?.getVoiceAlarm().toString()
                        val reply = "报警-" + voice
                        sendResultCode(VDVRRespondID.query_current_volume_1, reply)
                    }

                    else -> {
                        // 默认返回媒体音量
                        val voice = mVoicePresenter?.getVoiceMedia().toString()
                        val reply = "媒体-" + voice
                        sendResultCode(VDVRRespondID.query_current_volume_1, reply)
                    }
                }
            }

            else -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
        }


    }

    /**设置音量
     * @param value
     */
    fun setVolume(value: String) {
        // 音量调到具体值：【0=导航，1=语音，2=电话，3=媒体，4=报警，5=标准，6=蓝牙音乐，7=开机背景】【XX】
//        public static final int SETTING_ADJUST_VOLUME = SETTING_ADJUST_VOLUME_DOWN + 1;
        val parts = value.split("-") // 按空格分割
        var first = parts[0]
        val second = parts[1]
        val number = second.toDouble()
        if (first == CarSettingConstant.SOUND_TYPE_UNKNOWN) {
            val mCar = Car.createCar(mcontext)
            val mCarAudioManager = mCar.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager

            // 获取当前活动的音频组ID
            val currentGroupId =
                mCarAudioManager.getCurActiveGroupId(CarAudioManager.PRIMARY_AUDIO_ZONE)

            // 根据不同使用场景获取对应的组ID
            val mediaGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA)
            val navGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE)
            val phoneGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
            val vrGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT)
            val alarmGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ALARM)
            // 判断当前活动的是哪个音频组并返回对应信息
            when (currentGroupId) {
                mediaGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }

                navGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_NAVI
                }

                phoneGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_PHONE
                }

                vrGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_VOICE
                }

                alarmGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_ALARM
                }

                else -> {
                    // 默认返回媒体音量
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }
            }
        }
        when (first) {
            CarSettingConstant.SOUND_TYPE_NAVI -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter?.setVoiceNavi(roundedNumber)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10, roundedNumber.toString()
                    )
                }
                if (second.toInt() > CarSettingConstant.VOLUME_10) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() == mVoicePresenter?.getVoiceNavi()) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_2, second)
                } else {
                    mVoicePresenter?.setVoiceNavi(second.toInt())
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_VOICE -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter?.setVoiceVR(roundedNumber)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10, roundedNumber.toString()
                    )
                }
                if (second.toInt() > CarSettingConstant.VOLUME_10) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() == mVoicePresenter?.getVoiceVR()) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_2, second)
                } else {
                    mVoicePresenter?.setVoiceVR(second.toInt())
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_PHONE -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter?.setVoicePhone(roundedNumber)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10, roundedNumber.toString()
                    )
                }
                if (second.toInt() > CarSettingConstant.VOLUME_25) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() == mVoicePresenter?.getVoicePhone()) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_2, second)
                } else {
                    mVoicePresenter?.setVoicePhone(second.toInt())
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_MEDIA -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter?.setVoiceMedia(roundedNumber)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10, roundedNumber.toString()
                    )
                }
                if (second.toInt() > CarSettingConstant.VOLUME_31) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_31 && second.toInt() > CarSettingConstant.VOLUME_25) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_5, second)
                    //todo 31-25讯飞处理二次确认
                    mVoicePresenter?.setVoiceMedia(second.toInt())
                } else {
                    mVoicePresenter?.setVoiceMedia(second.toInt())
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_ALARM -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter!!.setVoiceAlarm(roundedNumber)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10, roundedNumber.toString()
                    )
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0 || second.toInt() > CarSettingConstant.VOLUME_10) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                } else {
                    mVoicePresenter!!.setVoiceAlarm(second.toInt())
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_2, second)
                }
            }

            CarSettingConstant.SOUND_TYPE_STANDARD -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BLUETOOTH -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BOOT -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            else -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
        }
    }

    /**
     * 调节音量
     * @param value
     */
    fun setVolumePrint(value: String) {
        val parts = value.split("-") // 按空格分割
        var first = parts[0]
        val second = parts[1]
        val number = second.toDouble()
        if (first == CarSettingConstant.SOUND_TYPE_UNKNOWN) {
            val mCar = Car.createCar(mcontext)
            val mCarAudioManager = mCar.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
            // 获取当前活动的音频组ID
            val currentGroupId =
                mCarAudioManager.getCurActiveGroupId(CarAudioManager.PRIMARY_AUDIO_ZONE)
            // 根据不同使用场景获取对应的组ID
            val mediaGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA)
            val navGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE)
            val phoneGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
            val vrGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT)
            val alarmGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ALARM)
            // 判断当前活动的是哪个音频组并返回对应信息
            when (currentGroupId) {
                mediaGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }

                navGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_NAVI
                }

                phoneGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_PHONE
                }

                vrGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_VOICE
                }

                alarmGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_ALARM
                }

                else -> {
                    // 默认返回媒体音量
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }
            }
        }
        when (first) {
            CarSettingConstant.SOUND_TYPE_NAVI -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter?.setVoiceNavi((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10,
                        ((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR).toString()
                    )
                }
                val numberx =
                    (second.toInt() * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR
                if (second.toInt() > CarSettingConstant.VOLUME_10) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() == mVoicePresenter?.getVoiceNavi()) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_2, second)
                } else {
                    mVoicePresenter?.setVoiceNavi(numberx)
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_VOICE -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter?.setVoiceVR((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10,
                        ((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR).toString()
                    )
                }
                val numberx =
                    (second.toInt() * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR
                if (second.toInt() > CarSettingConstant.VOLUME_10) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() == mVoicePresenter?.voiceVR) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_2, second)
                } else {
                    mVoicePresenter?.setVoiceVR(numberx)
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_PHONE -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter?.setVoicePhone((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10,
                        ((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR).toString()
                    )
                }
                val numberx =
                    (second.toInt() * CarSettingConstant.VOLUME_25) / CarSettingConstant.VOLUME_DIVISOR
                if (second.toInt() > CarSettingConstant.VOLUME_25) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() == mVoicePresenter?.getVoicePhone()) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_2, second)
                } else {
                    mVoicePresenter?.setVoicePhone(numberx)
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_MEDIA -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter?.setVoiceMedia((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10,
                        ((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR).toString()
                    )
                }
                val numberx =
                    (second.toInt() * CarSettingConstant.VOLUME_31) / CarSettingConstant.VOLUME_DIVISOR
                if (second.toInt() > CarSettingConstant.VOLUME_31) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_31 && second.toInt() > CarSettingConstant.VOLUME_25) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_5, second)
                    //todo 31-25讯飞处理二次确认
                    mVoicePresenter?.setVoiceMedia(numberx)
                } else {
                    mVoicePresenter?.setVoiceMedia(numberx)
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_ALARM -> {
                if (number != number.toLong().toDouble()) {
                    val roundedNumber = Math.round(number).toInt()
                    mVoicePresenter!!.setVoiceAlarm((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR)
                    sendResultCode(
                        VDVRRespondID.adjust_volume_to_number_10,
                        ((roundedNumber * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR).toString()
                    )
                }
                val numberx =
                    (second.toInt() * CarSettingConstant.VOLUME_10) / CarSettingConstant.VOLUME_DIVISOR
                if (second.toInt() > CarSettingConstant.VOLUME_10) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                }
                if (second.toInt() < CarSettingConstant.VOLUME_0) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                }
                if (second.toInt() == mVoicePresenter?.getVoiceAlarm()) {
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_2, second)
                } else {
                    mVoicePresenter!!.setVoiceAlarm(numberx)
                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                }
            }

            CarSettingConstant.SOUND_TYPE_STANDARD -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BLUETOOTH -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BOOT -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            else -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
        }
    }

    /**
     *音量调大一点/小一点/最大/最小/高档/中档/低档：
     *【0=导航，1=语音，2=电话，3=媒体，4=报警，5=标准，6=蓝牙音乐，7=开机背景】
     *【0=大一点，1=小一点，2=最大，3=最小，4=高档，5=中档，6=低档】
     * @param value
     */
    fun setVolumeUpLit(value: String) {
        val parts = value.split("-") // 按空格分割
        var first = parts[0]
        val second = parts[1]
        if (first == "98") {
            val mCar = Car.createCar(mcontext)
            val mCarAudioManager = mCar.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
            // 获取当前活动的音频组ID
            val currentGroupId =
                mCarAudioManager.getCurActiveGroupId(CarAudioManager.PRIMARY_AUDIO_ZONE)
            // 根据不同使用场景获取对应的组ID
            val mediaGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA)
            val navGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE)
            val phoneGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
            val vrGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT)
            val alarmGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ALARM)
            // 判断当前活动的是哪个音频组并返回对应信息
            when (currentGroupId) {
                mediaGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }

                navGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_NAVI
                }

                phoneGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_PHONE
                }

                vrGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_PHONE
                }

                alarmGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_ALARM
                }

                else -> {
                    // 默认返回媒体音量
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }
            }
        }
        when (first) {
            CarSettingConstant.SOUND_TYPE_NAVI -> {
                when (second) {
                    CarSettingConstant.VOLUME_ADJUST_HIGH_Little -> {
                        if (mVoicePresenter?.getVoiceNavi() == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                        } else {
                            mVoicePresenter?.let { presenter ->
                                val currentVolume =
                                    presenter.voiceNavi ?: CarSettingConstant.VOLUME_0
                                mVoicePresenter?.setVoiceNavi(currentVolume + CarSettingConstant.VOLUME_PLUS_ONE)
                            }

                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW_Little -> {
                        var number = mVoicePresenter?.getVoiceNavi()
                        if (number == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                        } else {
                            mVoicePresenter?.let { presenter ->
                                val currentVolume =
                                    presenter.voiceNavi ?: CarSettingConstant.VOLUME_0
                                mVoicePresenter?.setVoiceNavi(currentVolume - CarSettingConstant.VOLUME_PLUS_ONE)
                            }
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MAX -> {
                        var number = mVoicePresenter?.getVoiceNavi()
                        if (number == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_2, "导航")
                        } else {
                            mVoicePresenter?.setVoiceNavi(CarSettingConstant.VOLUME_10)
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_1, "导航")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MIN -> {
                        var number = mVoicePresenter?.getVoiceNavi()
                        if (number == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_min_2, "导航")
                        } else {
                            mVoicePresenter?.setVoiceNavi(CarSettingConstant.VOLUME_0)
                            sendResultCode(VDVRRespondID.adjust_volume_to_min_1, "导航")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_UPSCALE -> {
                        if (mVoicePresenter?.getVoiceNavi() == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "高档")
                        } else {
                            mVoicePresenter?.setVoiceNavi(CarSettingConstant.VOLUME_10)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MID -> {
                        if (mVoicePresenter?.getVoiceNavi() == CarSettingConstant.VOLUME_5) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "中档")
                        } else {
                            mVoicePresenter?.setVoiceNavi(CarSettingConstant.VOLUME_5)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW -> {
                        if (mVoicePresenter?.getVoiceNavi() == CarSettingConstant.VOLUME_1) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "低档")
                        } else {
                            mVoicePresenter?.setVoiceNavi(CarSettingConstant.VOLUME_1)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_VOICE -> {
                when (second) {
                    CarSettingConstant.VOLUME_ADJUST_HIGH_Little -> {
                        if (mVoicePresenter?.getVoiceVR() == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                        } else {
                            mVoicePresenter?.let { presenter ->
                                val currentVolume = presenter.voiceVR ?: CarSettingConstant.VOLUME_0
                                mVoicePresenter?.setVoiceVR(currentVolume + CarSettingConstant.VOLUME_PLUS_ONE)
                            }
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW_Little -> {
                        if (mVoicePresenter?.getVoiceVR() == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                        } else {
                            mVoicePresenter?.let { presenter ->
                                val currentVolume = presenter.voiceVR ?: CarSettingConstant.VOLUME_0
                                mVoicePresenter?.setVoiceVR(currentVolume - CarSettingConstant.VOLUME_PLUS_ONE)
                            }
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MAX -> {
                        if (mVoicePresenter?.getVoiceVR() == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_2, "语音")
                        } else {
                            mVoicePresenter?.setVoiceVR(CarSettingConstant.VOLUME_10)
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_1, "语音")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MIN -> {
                        if (mVoicePresenter?.getVoiceVR() == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_min_2, "语音")
                        } else {
                            mVoicePresenter?.setVoiceVR(CarSettingConstant.VOLUME_0)
                            sendResultCode(VDVRRespondID.adjust_volume_to_min_1, "语音")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_UPSCALE -> {
                        if (mVoicePresenter?.getVoiceVR() == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "高档")
                        } else {
                            mVoicePresenter?.setVoiceVR(CarSettingConstant.VOLUME_10)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MID -> {
                        if (mVoicePresenter?.getVoiceVR() == CarSettingConstant.VOLUME_5) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "中档")
                        } else {
                            mVoicePresenter?.setVoiceVR(CarSettingConstant.VOLUME_5)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW -> {
                        if (mVoicePresenter?.getVoiceVR() == CarSettingConstant.VOLUME_1) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "低档")
                        } else {
                            mVoicePresenter?.setVoiceVR(CarSettingConstant.VOLUME_1)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_PHONE -> {
                when (second) {
                    CarSettingConstant.VOLUME_ADJUST_HIGH_Little -> {
                        if (mVoicePresenter?.getVoicePhone() == CarSettingConstant.VOLUME_25) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                        } else {
                            mVoicePresenter?.let { presenter ->
                                val currentVolume =
                                    presenter.voicePhone ?: CarSettingConstant.VOLUME_0
                                mVoicePresenter?.setVoicePhone(currentVolume + CarSettingConstant.VOLUME_PLUS_TWO)
                            }
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW_Little -> {
                        if (mVoicePresenter?.getVoicePhone() == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                        } else {
                            mVoicePresenter?.let { presenter ->
                                val currentVolume =
                                    presenter.voicePhone ?: CarSettingConstant.VOLUME_0
                                mVoicePresenter?.setVoicePhone(currentVolume - CarSettingConstant.VOLUME_PLUS_TWO)
                            }
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MAX -> {
                        if (mVoicePresenter?.getVoicePhone() == CarSettingConstant.VOLUME_25) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_2, "电话")
                        } else {
                            mVoicePresenter?.setVoicePhone(CarSettingConstant.VOLUME_25)
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_1, "电话")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MIN -> {
                        if (mVoicePresenter?.getVoicePhone() == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_min_2, "电话")
                        } else {
                            mVoicePresenter?.setVoicePhone(CarSettingConstant.VOLUME_0)
                            sendResultCode(VDVRRespondID.adjust_volume_to_min_1, "电话")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_UPSCALE -> {
                        if (mVoicePresenter?.getVoicePhone() == CarSettingConstant.VOLUME_25) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "高档")
                        } else {
                            mVoicePresenter?.setVoicePhone(CarSettingConstant.VOLUME_25)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MID -> {
                        if (mVoicePresenter?.getVoicePhone() == CarSettingConstant.VOLUME_12) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "中档")
                        } else {
                            mVoicePresenter?.setVoicePhone(CarSettingConstant.VOLUME_12)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW -> {
                        if (mVoicePresenter?.getVoicePhone() == CarSettingConstant.VOLUME_1) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "低档")
                        } else {
                            mVoicePresenter?.setVoicePhone(CarSettingConstant.VOLUME_1)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_MEDIA -> {
                when (second) {
                    CarSettingConstant.VOLUME_ADJUST_HIGH_Little -> {
                        val number = mVoicePresenter?.getVoiceMedia()
                        if (mVoicePresenter?.getVoiceMedia() == CarSettingConstant.VOLUME_31) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                        } else {
                            if (number != null) {
                                if (number + CarSettingConstant.VOLUME_PLUS_TWO == CarSettingConstant.VOLUME_31) {
                                    sendResultCode(VDVRRespondID.adjust_volume_to_number_6)
                                }
                                if (number + CarSettingConstant.VOLUME_PLUS_TWO > CarSettingConstant.VOLUME_25 && number + CarSettingConstant.VOLUME_PLUS_TWO < CarSettingConstant.VOLUME_31) {
                                    sendResultCode(VDVRRespondID.adjust_volume_to_number_5, second)
                                } else {
                                    mVoicePresenter?.setVoiceMedia(number + CarSettingConstant.VOLUME_PLUS_TWO)
                                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                                }
                            }
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW_Little -> {
                        val number = mVoicePresenter?.getVoiceMedia()
                        if (mVoicePresenter?.getVoiceMedia() == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                        } else {
                            if (number != null) {
                                if (number - CarSettingConstant.VOLUME_PLUS_TWO == CarSettingConstant.VOLUME_0) {
                                    sendResultCode(VDVRRespondID.adjust_volume_to_number_6)
                                } else {
                                    mVoicePresenter?.setVoiceMedia(number - CarSettingConstant.VOLUME_PLUS_TWO)
                                    sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                                }
                            }
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MAX -> {
                        if (mVoicePresenter?.getVoiceMedia() == CarSettingConstant.VOLUME_31) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_2, "媒体")
                        } else {
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_3)
                            //todo  讯飞二次确认
                            mVoicePresenter?.setVoiceMedia(CarSettingConstant.VOLUME_31)
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_4, "媒体")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MIN -> {
                        if (mVoicePresenter?.getVoiceMedia() == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_min_2, "媒体")
                        } else {
                            mVoicePresenter?.setVoiceMedia(CarSettingConstant.VOLUME_0)
                            sendResultCode(VDVRRespondID.adjust_volume_to_min_1, "媒体")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_UPSCALE -> {
                        if (mVoicePresenter?.getVoiceMedia() == CarSettingConstant.VOLUME_31) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "高档")
                        } else {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_6)
                            //  todo 讯飞二次确认
                            mVoicePresenter?.setVoiceMedia(CarSettingConstant.VOLUME_31)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MID -> {
                        if (mVoicePresenter?.getVoiceMedia() == CarSettingConstant.VOLUME_15) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "中档")
                        } else {
                            mVoicePresenter?.setVoiceMedia(CarSettingConstant.VOLUME_15)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW -> {
                        if (mVoicePresenter?.getVoiceMedia() == CarSettingConstant.VOLUME_1) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "低档")
                        } else {
                            mVoicePresenter?.setVoiceMedia(CarSettingConstant.VOLUME_1)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_ALARM -> {
                when (second) {
                    CarSettingConstant.VOLUME_ADJUST_HIGH_Little -> {
                        if (mVoicePresenter?.getVoiceAlarm() == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                        } else {
                            mVoicePresenter!!.setVoiceAlarm(mVoicePresenter.getVoiceAlarm() - CarSettingConstant.VOLUME_PLUS_ONE)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW_Little -> {
                        if (mVoicePresenter?.getVoiceAlarm() == CarSettingConstant.VOLUME_0) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                        } else {
                            mVoicePresenter.setVoiceAlarm(mVoicePresenter.getVoiceAlarm() - CarSettingConstant.VOLUME_PLUS_ONE)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MAX -> {
                        if (mVoicePresenter?.getVoiceAlarm() == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_2, "报警")
                        } else {
                            mVoicePresenter!!.setVoiceAlarm(CarSettingConstant.VOLUME_10)
                            sendResultCode(VDVRRespondID.adjust_volume_to_max_1, "报警")
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MIN -> {
                        sendResultCode(VDVRRespondID.volume_mute_alarm_1)
                    }

                    CarSettingConstant.VOLUME_ADJUST_UPSCALE -> {
                        if (mVoicePresenter?.getVoiceAlarm() == CarSettingConstant.VOLUME_10) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "高档")
                        } else {
                            mVoicePresenter!!.setVoiceAlarm(CarSettingConstant.VOLUME_10)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_MID -> {
                        if (mVoicePresenter?.getVoiceAlarm() == CarSettingConstant.VOLUME_5) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "中档")
                        } else {
                            mVoicePresenter!!.setVoiceAlarm(CarSettingConstant.VOLUME_5)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }

                    CarSettingConstant.VOLUME_ADJUST_LOW -> {
                        if (mVoicePresenter?.getVoiceAlarm() == CarSettingConstant.VOLUME_1) {
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_2, "低档")
                        } else {
                            mVoicePresenter!!.setVoiceAlarm(CarSettingConstant.VOLUME_1)
                            sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                        }
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_STANDARD -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BLUETOOTH -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BOOT -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            else -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)

        }
    }

    /**调大一点/小一点/最大/最小/高档/中档/低档：【0=导航，1=语音，2=电话，3=媒体，4=报警，5=标准，6=蓝牙音乐，7=开机背景】
     *【0=大一点，1=小一点，2=最大，3=最小，4=高档，5=中档，6=低档】
     * @param value
     */
    fun setVolumeUp(value: String) {
        val parts = value.split("-") // 按空格分割
        var first = parts[0]
        val second = parts[1]
        if (first == CarSettingConstant.SOUND_TYPE_UNKNOWN) {
            val mCar = Car.createCar(mcontext)
            val mCarAudioManager = mCar.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
            // 获取当前活动的音频组ID
            val currentGroupId =
                mCarAudioManager.getCurActiveGroupId(CarAudioManager.PRIMARY_AUDIO_ZONE)
            // 根据不同使用场景获取对应的组ID
            val mediaGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA)
            val navGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE)
            val phoneGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
            val vrGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT)
            val alarmGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ALARM)
            // 判断当前活动的是哪个音频组并返回对应信息
            when (currentGroupId) {
                mediaGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }

                navGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_NAVI
                }

                phoneGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_PHONE
                }

                vrGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_VOICE
                }

                alarmGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_ALARM
                }

                else -> {
                    // 默认返回媒体音量
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }
            }
        }
        when (first) {
            CarSettingConstant.SOUND_TYPE_NAVI -> {
                val number = mVoicePresenter?.voiceNavi
                if (number != null) {
                    if (number + second.toInt() > CarSettingConstant.VOLUME_10) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                    } else {
                        mVoicePresenter?.setVoiceNavi(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_VOICE -> {
                val number = mVoicePresenter?.voiceVR
                if (number != null) {
                    if (number + second.toInt() > CarSettingConstant.VOLUME_10) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                    } else {
                        mVoicePresenter?.setVoiceVR(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_PHONE -> {
                val number = mVoicePresenter?.voicePhone
                if (number != null) {
                    if (number + second.toInt() > CarSettingConstant.VOLUME_25) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                    } else {
                        mVoicePresenter?.setVoicePhone(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_MEDIA -> {
                val number = mVoicePresenter?.voiceMedia
                if (number != null) {
                    if (number + second.toInt() > CarSettingConstant.VOLUME_31) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                    }
                    if (number + second.toInt() < CarSettingConstant.VOLUME_31 && number + second.toInt() > CarSettingConstant.VOLUME_25) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_5, second)
                        //todo 讯飞二次确认
                        mVoicePresenter?.setVoiceMedia(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                    if (number + second.toInt() == CarSettingConstant.VOLUME_31) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_6)
                        //todo 讯飞二次确认
                        mVoicePresenter?.setVoiceMedia(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    } else {
                        mVoicePresenter?.setVoiceMedia(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_ALARM -> {
                val number = mVoicePresenter?.getVoiceAlarm()
                if (number != null) {
                    if (number + second.toInt() > CarSettingConstant.VOLUME_10) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_3)
                    } else {
                        mVoicePresenter!!.setVoiceAlarm(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_STANDARD -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BLUETOOTH -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BOOT -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            else -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
        }
    }

    fun setVolumeDown(value: String) {
        val parts = value.split("-") // 按空格分割
        var first = parts[0]
        val second = parts[1]
        if (first == CarSettingConstant.SOUND_TYPE_UNKNOWN) {
            val mCar = Car.createCar(mcontext)
            val mCarAudioManager = mCar.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
            // 获取当前活动的音频组ID
            val currentGroupId =
                mCarAudioManager.getCurActiveGroupId(CarAudioManager.PRIMARY_AUDIO_ZONE)
            // 根据不同使用场景获取对应的组ID
            val mediaGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_MEDIA)
            val navGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANCE_NAVIGATION_GUIDANCE)
            val phoneGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
            val vrGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ASSISTANT)
            val alarmGroupId =
                mCarAudioManager.getVolumeGroupIdForUsage(AudioAttributes.USAGE_ALARM)
            // 判断当前活动的是哪个音频组并返回对应信息
            when (currentGroupId) {
                mediaGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }

                navGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_NAVI
                }

                phoneGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_PHONE
                }

                vrGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_VOICE
                }

                alarmGroupId -> {
                    first = CarSettingConstant.SOUND_TYPE_ALARM
                }

                else -> {
                    // 默认返回媒体音量
                    first = CarSettingConstant.SOUND_TYPE_MEDIA
                }
            }
        }
        when (first) {
            CarSettingConstant.SOUND_TYPE_NAVI -> {
                val number = mVoicePresenter?.getVoiceNavi()
                if (number != null) {
                    if (number + second.toInt() < CarSettingConstant.VOLUME_0) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                    } else {
                        mVoicePresenter?.setVoiceNavi(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_VOICE -> {
                val number = mVoicePresenter?.getVoiceVR()
                if (number != null) {
                    if (number + second.toInt() < CarSettingConstant.VOLUME_0) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                    } else {
                        mVoicePresenter?.setVoiceVR(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_PHONE -> {
                val number = mVoicePresenter?.getVoicePhone()
                if (number != null) {
                    if (number + second.toInt() < CarSettingConstant.VOLUME_0) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                    } else {
                        mVoicePresenter?.setVoicePhone(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_MEDIA -> {
                val number = mVoicePresenter?.getVoiceMedia()
                if (number != null) {
                    if (number + second.toInt() < CarSettingConstant.VOLUME_0) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                    } else {
                        mVoicePresenter?.setVoiceMedia(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_ALARM -> {
                val number = mVoicePresenter?.getVoiceAlarm()
                if (number != null) {
                    if (number + second.toInt() < CarSettingConstant.VOLUME_0) {
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_4)
                    } else {
                        mVoicePresenter!!.setVoiceAlarm(number + second.toInt())
                        sendResultCode(VDVRRespondID.adjust_volume_to_number_1)
                    }
                }
            }

            CarSettingConstant.SOUND_TYPE_STANDARD -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BLUETOOTH -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            CarSettingConstant.SOUND_TYPE_BOOT -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
            else -> sendResultCode(VDVRRespondID.adjust_volume_to_number_9)
        }
    }

    /**
     * 打开/关闭蓝牙无缝切换
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setChangeBluetooth(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_bluetooth_seamlessly_switch_3)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_bluetooth_seamlessly_switch_3)
            }
        }
    }

    /**
     * 切换蓝牙连接设备，无指定值
     */
    fun setSwitchBtDevice() {
        sendResultCode(VDVRRespondID.switch_bluetooth_1)
    }

    /**
     * 查看蓝牙设备信息
     */
    fun setBtDeviceInfo() {
        sendResultCode(VDVRRespondID.check_bluetooth_device_info_1)
    }

    /**
     * 打开/关闭蓝牙自动连接功能
     */
    fun setAutolinkBluetooth(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_bluetooth_auto_connect_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_bluetooth_auto_connect_1)
            }
        }
    }

    /**
     * wifi密码设置为可见
     */
    fun setVisiblityWifiPassword(voiceCommandState: Boolean) {
        sendResultCode(VDVRRespondID.set_wifi_password_visible_1)
    }

    /**
     * 切换wifi连接，无指定值
     */
    fun setSwitchWifiLink() {
        sendResultCode(VDVRRespondID.switch_wifi_1)
    }

    /**
     * 设置WiFi连接频段
     */
    fun setWifiFrequency(value: String) {
        sendResultCode(VDVRRespondID.set_wifi_band_1)
    }

    /**
     * 切换WiFi连接频段，无指定值
     */
    fun setSwitchWifiFrequency() {
        sendResultCode(VDVRRespondID.set_wifi_band_2)
    }

    /**
     * 热点密码设置为不可见
     */
    fun setVisiblityHotspotPassword(voiceCommandState: Boolean) {
        sendResultCode(VDVRRespondID.set_hotspot_password_unvisible_1)
    }

    /**
     * 设置热点频段
     */
    fun setHotspotFrequency(value: String) {
        sendResultCode(VDVRRespondID.set_hotspot_band_3)
    }

    /**
     * 切换热点连接频段，无指定值
     */
    fun setSwitchHotspotFrequency() {
        sendResultCode(VDVRRespondID.switch_hotspot_band_1)
    }

    /**
     * 还原网络设置
     */
    fun setRestoreNetworkSetting() {
        sendResultCode(VDVRRespondID.reset_network_settings_1)
    }

    /**
     * 切换声音模式，无指定值
     */
    fun setSwitchVolumeMode() {
        sendResultCode(VDVRRespondID.switch_volume_mode_1)
    }

    /**
     * 打开/关闭主动声浪
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setDrivingSoundWave(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_driving_sound_wave_3)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_driving_sound_wave_3)
            }
        }
    }

    /**
     * 主动声浪设置为指定模式
     */
    fun setSetModeDrivingWave(value: String) {
        sendResultCode(VDVRRespondID.set_driving_sound_wave_mode_4)
    }

    /**
     * 切换主动声浪模式，无指定值
     */
    fun setSwitchDrivingWave() {
//        sendResultCode(VDVRRespondID.switch_driving_sound_wave_mode_2)
    }

    /**
     * 主动声浪音量调高/调低/调最高/调最低
     */
    fun setAdjustDrivingWave(value: String) {
        sendResultCode(VDVRRespondID.raise_driving_sound_wave_volume_1)
    }

    /**
     * 打开/关闭震动反馈
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setVibrationFeedback(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_haptics_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_haptics_1)
            }
        }
    }

    /**
     * 打开/关闭声音渐入渐出功能
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setSoundFade(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_volume_gradually_in_and_out_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_volume_gradually_in_and_out_1)
            }
        }
    }

    /**
     * 打开/关闭音量平衡
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setVolumeBalance(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_volume_banlance_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_volume_banlance_1)
            }
        }
    }

    /**
     * 打开/关闭等响度
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setLoudness(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_loundness_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_loundness_1)
            }
        }
    }

    /**
     * 打开/关闭车内声音交流补偿
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setSoundRepair(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_in_car_communication_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_in_car_communication_1)
            }
        }
    }

    /**
     * 打开/关闭低品质音源修复
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setAudioRepair(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_low_quality_audio_source_repair_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_low_quality_audio_source_repair_1)
            }
        }
    }

    /**
     * 打开最佳听音位为指定位置/关闭最佳听音位
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setBestListenPosition(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_best_listening_position_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_best_listening_position_1)
            }
        }
    }

    /**
     * 切换最佳听音位，无指定值
     */
    fun setSwitchBestListenPosition() {
        sendResultCode(VDVRRespondID.switch_best_listening_position_1)
    }

    /**
     * 打开/关闭自动降音功能
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setAutomaticReduction(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_auto_volume_reduction_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_auto_volume_reduction_1)
            }
        }
    }

    /**
     * 打开/关闭均衡器
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setEqualizer(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_eq_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_eq_1)
            }
        }
    }

    /**
     * 设置均衡器预设EQ为指定音效/关闭均衡器预设EQ
     */
    fun setSetPresetEq(value: String) {
        sendResultCode(VDVRRespondID.set_eq_mode_1)
    }

    /**
     * 切换均衡器预设EQ，无指定值
     */
    fun setSwitchPresetEq() {
        sendResultCode(VDVRRespondID.switch_eq_mode_1)
    }

    /**
     * 开启/关闭主动降噪
     * @param voiceCommandState true:打开 false:关闭
     */
    fun setNoiseSuppression(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.open_anc_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.close_anc_1)
            }
        }
    }

    /**
     * 屏幕位置移动
     */
    fun setScreenMove(value: String) {
        sendResultCode(VDVRRespondID.move_screen_to_direction_1)
    }

    /**
     * 旋转屏幕，无指定值
     */
    fun setRotateScreen() {
        sendResultCode(VDVRRespondID.screen_rotate_1)
    }

    /**
     * 向指定方向旋转屏幕
     */
    fun setScreenRotate(value: String) {
        sendResultCode(VDVRRespondID.rotate_screen_to_direction_1)
    }

    /**
     * 设置屏幕显示模式
     */
    fun setSetModeScreenDisplay(value: String) {
        val displayPresenter = DisplayPresenter.getInstance()
        when (value) {
            "0" -> {//自动模式
                if (!DisplayPresenter.getAutoMode()) {// 不是自动模式
                    displayPresenter.setDisplayMode(CarDisplay.AUTO, false)
                    sendResultCode(VDVRRespondID.set_screen_display_mode_2)
                } else {// 已切换至自动模式，回复语
                    sendResultCode(VDVRRespondID.set_screen_display_mode_4, "自动模式")
                }
            }

            "2" -> {//浅色模式
                if (!DisplayPresenter.getAutoMode()) {// 不是自动模式
                    if (!DisplayPresenter.getDisplayMode()) { // 是浅色
                        sendResultCode(VDVRRespondID.set_screen_display_mode_4, "浅色模式")
                    } else {
                        displayPresenter.setDisplayMode(CarDisplay.DAY, false);
                        sendResultCode(VDVRRespondID.set_screen_display_mode_2)
                    }
                } else {//
                    displayPresenter.setDisplayMode(CarDisplay.DAY, false);
                    sendResultCode(VDVRRespondID.set_screen_display_mode_2)
                }
            }

            "4" -> {//深色模式
                if (!DisplayPresenter.getAutoMode()) {// 不是自动模式
                    if (DisplayPresenter.getDisplayMode()) {// 是深色
                        sendResultCode(VDVRRespondID.set_screen_display_mode_4, "深色模式")
                    } else {
                        displayPresenter.setDisplayMode(CarDisplay.NIGHT, false);
                        sendResultCode(VDVRRespondID.set_screen_display_mode_2)
                    }
                } else {
                    displayPresenter.setDisplayMode(CarDisplay.NIGHT, false);
                    sendResultCode(VDVRRespondID.set_screen_display_mode_2)
                }
            }

            else -> {// 未匹配
                sendResultCode(VDVRRespondID.set_screen_display_mode_1)
            }
        }
    }

    /**
     * 关闭屏幕显示模式
     */
    fun setSystemUiScreenShow(voiceCommandState: Boolean) {
        sendResultCode(VDVRRespondID.close_screen_display_mode_1)
    }

    /**
     * 设置/关闭屏幕色彩模式
     */
    fun setSetModeScreenColor(value: String) {
        sendResultCode(VDVRRespondID.set_screen_color_mode_1)
    }

    /**
     * 切换屏幕色彩模式，无指定值
     */
    fun setSwitchScreenColorMode() {
        sendResultCode(VDVRRespondID.switch_screen_color_mode_1)
    }

    /**
     * 锁屏/解锁屏幕
     */
    fun setControlScreen(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.lock_screen_1)
            }

            false -> {
                sendResultCode(VDVRRespondID.unlock_screen_1)
            }
        }
    }

    /**
     * 打开or关闭导航时压低媒体音
     */
    fun openOrcloseNavigatemeDiatones(flag: Boolean) {
        if (flag) {
            if (mVoicePresenter.getVoiceLowerMediaTone() == CarSettingConstant.NAV_LOWER_MEDIA_ON) {
                sendResultCode(VDVRRespondID.open_mapU_state_Lower_the_media_volume_1)
            } else {
                mVoicePresenter.setVoiceLowerMediaTone(CarSettingConstant.NAV_LOWER_MEDIA_ON)
                sendResultCode(VDVRRespondID.open_mapU_state_Lower_the_media_volume_2)
            }
        } else {
            if (mVoicePresenter.getVoiceLowerMediaTone() == CarSettingConstant.NAV_LOWER_MEDIA_OFF) {
                sendResultCode(VDVRRespondID.close_mapU_state_Lower_the_media_volume_2)
            } else {
                mVoicePresenter.setVoiceLowerMediaTone(CarSettingConstant.NAV_LOWER_MEDIA_OFF)
                sendResultCode(VDVRRespondID.close_mapU_state_Lower_the_media_volume_1)
            }
        }
    }

    /**
     * 打开/关闭桌面歌词
     */
    fun setPassengerDesktopLyrics(voiceCommandState: Boolean) {
        when (voiceCommandState) {
            true -> {
                sendResultCode(VDVRRespondID.Open_desktop_lyrics_3)
            }

            false -> {
                sendResultCode(VDVRRespondID.Close_desktop_lyrics_3)
            }
        }
    }

    /**
     * 设置头枕音响的指定模式
     * @param value
     * 0:私享
     * 1:驾享
     * 2:共享
     */
    fun setHeadrestdilog(value: String) {
        val headRestStatus = mVoicePresenter.getHeadRest()
// todo : 配置字
        when (value) {
            CarSettingConstant.Headrest_Sound_Mode_Private.toString() -> {
                if (headRestStatus == CarSettingConstant.Headrest_Sound_Mode_Private) {
                    sendResultCode(VDVRRespondID.set_headrest_audio_system_mode_2)
                } else {
                    mVoicePresenter.setHeadRest(CarSettingConstant.Headrest_Sound_Mode_Private)
                    sendResultCode(VDVRRespondID.set_headrest_audio_system_mode_1, "私享模式")
                }
            }

            CarSettingConstant.Headrest_Sound_Mode_Drive.toString() -> {
                if (headRestStatus == CarSettingConstant.Headrest_Sound_Mode_Drive) {
                    sendResultCode(VDVRRespondID.set_headrest_audio_system_mode_2)
                } else {
                    mVoicePresenter.setHeadRest(CarSettingConstant.Headrest_Sound_Mode_Drive)
                    sendResultCode(VDVRRespondID.set_headrest_audio_system_mode_1, "驾享模式")
                }
            }

            CarSettingConstant.Headrest_Sound_Mode_Shared.toString() -> {
                if (headRestStatus == CarSettingConstant.Headrest_Sound_Mode_Shared) {
                    sendResultCode(VDVRRespondID.set_headrest_audio_system_mode_2)
                } else {
                    mVoicePresenter.setHeadRest(CarSettingConstant.Headrest_Sound_Mode_Shared)
                    sendResultCode(VDVRRespondID.set_headrest_audio_system_mode_1, "共享模式")
                }
            }

            else -> {
                sendResultCode(VDVRRespondID.set_headrest_audio_system_mode_3)
            }
        }
    }

    /**
     *切换头枕音响模式，无指定值
     */
    fun randomHeadrestdilog() {
//        sendResultCode(VDVRRespondID.switch_headrest_audio_system_mode_2)
    }

    object Constant {
        const val VR_PARAM_SPEED_COMPENSATION_LOW: String = "0"
        const val VR_PARAM_SPEED_COMPENSATION_MID: String = "1"
        const val VR_PARAM_SPEED_COMPENSATION_HIGH: String = "2"
        const val VR_PARAM_SPEED_COMPENSATION_INVALID: String = "99"
        const val VR_PARAM_SOUND_ON = 0
        const val VR_PARAM_SOUND_OFF = 1
        const val VR_PARAM_VIRTUAL_SCENE_MODE_CLOSE = "0"
        const val VR_PARAM_VIRTUAL_SCENE_MODE_CONCERT = "1"
        const val VR_PARAM_VIRTUAL_SCENE_MODE_MUSICHALL = "2"
        const val VR_PARAM_VR_PARAM_VIRTUAL_SCENE_MODE_FYHIFI = "3"
        const val VR_PARAM_VIRTUAL_VIRTUAL_SCENE_MODE_STADIUM = "4"
        const val VR_PARAM_ALARM_NATIONAL = "3"
        const val VR_PARAM_ALARM_TECHNOLOGY = "4"
        const val VR_PARAM_ALARM_SMART = "5"
        const val VR_PARAM_EQ_ALL = "3"
        const val VR_PARAM_EQ_DRIVER = "4"
        const val VR_PARAM_EQ_CUSTOMIZED = "6"
        const val VR_PARAM_EQ_SLEEP = "8"
        const val VR_PARAM_EQ_VIP = "9"
    }
}
