package com.bitech.vehiclesettings.service.system;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleService;

public class SystemService extends LifecycleService {
    private static SystemService instance;

    SystemLifeCycle systemLifeCycle;

    public static SystemService getInstance() {
        return instance;
    }
    @Override
    public void onCreate() {
        super.onCreate();
        setForeground();
        instance = this;
        systemLifeCycle = new SystemLifeCycle();
        getLifecycle().addObserver(systemLifeCycle);

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        instance = null;
        getLifecycle().removeObserver(systemLifeCycle);
    }

    @Override
    public int onStartCommand(@Nullable Intent intent, int flags, int startId) {
        return super.onStartCommand(intent, flags, startId);
    }

    @SuppressLint("ForegroundServiceType")
    private void setForeground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            final String CHANNEL_ID = "2";
            final CharSequence CHANNEL_NAME = "SystemService";
            NotificationManager notificationManager = (NotificationManager)
                    this.getSystemService(Context.NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_MIN);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
            Notification notification = new Notification.Builder(
                    this, CHANNEL_ID).build();
            startForeground(2, notification);
        } else {
            Notification notification = new Notification();
            startForeground(2, notification);
        }
    }
}
