package com.bitech.vehiclesettings.view.connect;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectChargingRemindBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertConnectHighTemperatureRemindBinding;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class HighTemperatureRemindUIAlert extends BaseDialog {
    private static final String TAG = HighTemperatureRemindUIAlert.class.getSimpleName();
    private static HighTemperatureRemindUIAlert.onProgressChangedListener onProgressChangedListener;


    public HighTemperatureRemindUIAlert(@NonNull Context context) {
        super(context);
    }

    public HighTemperatureRemindUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected HighTemperatureRemindUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static HighTemperatureRemindUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(HighTemperatureRemindUIAlert.onProgressChangedListener onProgressChangedListener) {
        HighTemperatureRemindUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private HighTemperatureRemindUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConnectHighTemperatureRemindBinding binding;
        DetailsUIAlert.Builder detailUIAlert;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private HighTemperatureRemindUIAlert dialog = null;
        private View layout;
        public Builder(Context context) {
            this.context = context;
        }


        public HighTemperatureRemindUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public HighTemperatureRemindUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new HighTemperatureRemindUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertConnectHighTemperatureRemindBinding.inflate(LayoutInflater.from(context));
            detailUIAlert = new DetailsUIAlert.Builder(context);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1656; // 或者使用具体的像素值
            layoutParams.height = 470;
            window.setAttributes(layoutParams);

            binding.tvConfirm.setOnClickListener(v -> {
                dialog.dismiss();
            });

            return dialog;
        }

        public HighTemperatureRemindUIAlert getDialog() {
            return dialog;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
    }
}

