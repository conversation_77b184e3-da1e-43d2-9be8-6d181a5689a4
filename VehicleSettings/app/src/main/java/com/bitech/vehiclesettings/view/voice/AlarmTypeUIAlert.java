package com.bitech.vehiclesettings.view.voice;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarVoice;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundAlarmTypeBinding;
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class AlarmTypeUIAlert extends BaseDialog {
    private static final String TAG = AlarmTypeUIAlert.class.getSimpleName();
    private final Context context;

    private DialogAlertSoundAlarmTypeBinding binding;

    public AlarmTypeUIAlert(Context context) {
        super(context, R.style.Dialog);
        this.context = context;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DialogAlertSoundAlarmTypeBinding.inflate(LayoutInflater.from(context));
        this.setCancelable(true);
        this.setContentView(binding.getRoot());
        // 获取对话框的Window对象
        Window window = this.getWindow();
        WindowManager.LayoutParams layoutParams = window.getAttributes();
        layoutParams.width = 1128;
        window.setAttributes(layoutParams);
        init();
        setupClickListeners();
    }

    public void init() {
        binding.spvAlarm.setItems(R.string.str_alarm_sound_type_national, R.string.str_alarm_sound_type_technology, R.string.str_alarm_sound_type_smart);
        int alarmType = VoicePresenter.getAlarmType();
        Log.d(TAG, "init() alarm type:"+ alarmType);
        binding.spvAlarm.setSelectedIndex(CarVoice.AlarmType.reverseUI(alarmType), false);
    }

    public void updateAlarmUI(Integer status) {
        binding.spvAlarm.setSelectedIndex(status, true);
    }

    public void setupClickListeners() {
        binding.spvAlarm.setOnItemSelectedListener((index, text) -> {
            switch (index){
                case 0:
                    VoicePresenter.setAlarmType(CarVoice.AlarmType.NATIONAL);
                    break;
                case 1:
                    VoicePresenter.setAlarmType(CarVoice.AlarmType.TECHNOLOGY);
                    break;
                case 2:
                    VoicePresenter.setAlarmType(CarVoice.AlarmType.SMART);
                    break;
            }
        });
    }

}
