package com.bitech.vehiclesettings.view.quickcontrol;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

/**
 * 对话框
 */
public class RearMirrorAdjustUIAlert extends BaseDialog {
    private static final String TAG = RearMirrorAdjustUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public RearMirrorAdjustUIAlert(Context context) {
        super(context);
    }

    public RearMirrorAdjustUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected RearMirrorAdjustUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static RearMirrorAdjustUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(RearMirrorAdjustUIAlert.onProgressChangedListener onProgressChangedListener) {
        RearMirrorAdjustUIAlert.onProgressChangedListener = onProgressChangedListener;
    }


    public static class Builder implements View.OnClickListener {

        private final Context context;
        private boolean isCan = true;

        private int RearMirrorAdjustSel = 1;
        TextView tvRearMirrorAdjust1;
        TextView tvRearMirrorAdjust2;
        TextView tvRearMirrorAdjust3;
        TextView tvRearMirrorAdjust4;
        private final int spacing = 8;
        private int swRearMirrorAdjust, preSelRearMirrorAdjust;
        private int offsetRearMirrorAdjust;
        private final int animRearMirrorAdjustWidth = 207;
        private View rearMirrorAdjustView;
        private LinearLayout rearMirrorAdjustLL;

        public int getRearMirrorAdjustSel() {
            return RearMirrorAdjustSel;
        }

        public void setRearMirrorAdjustSel(int RearMirrorAdjustSel) {
            this.RearMirrorAdjustSel = RearMirrorAdjustSel;
        }


        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private RearMirrorAdjustUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setSwRearMirrorAdjust(int stat) {
            swRearMirrorAdjust = stat;
        }

        public void updateSwRearMirrorAdjustUI(int stat) {
            swRearMirrorAdjust = stat;
            selRearMirrorAdjustTranslate();
            setTextView(true, tvRearMirrorAdjust1, tvRearMirrorAdjust2, tvRearMirrorAdjust3, tvRearMirrorAdjust4);
        }

        /**
         * Create the custom dialog
         */
        public RearMirrorAdjustUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new RearMirrorAdjustUIAlert(context,
                        R.style.Dialog);
            }
            layout = View.inflate(context, R.layout.dialog_alert_q_rear_mirror_adjust, null);
            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            layoutParams.height = 508;
            window.setAttributes(layoutParams);
            tvRearMirrorAdjust1 = layout.findViewById(R.id.tv_rear_mirror_adjust_1);
            tvRearMirrorAdjust2 = layout.findViewById(R.id.tv_rear_mirror_adjust_2);
            tvRearMirrorAdjust3 = layout.findViewById(R.id.tv_rear_mirror_adjust_3);
            tvRearMirrorAdjust4 = layout.findViewById(R.id.tv_rear_mirror_adjust_4);
            rearMirrorAdjustView = layout.findViewById(R.id.view_rear_mirror_adjust);
            rearMirrorAdjustLL = layout.findViewById(R.id.ll_rear_mirror_adjust);
            offsetRearMirrorAdjust = swRearMirrorAdjust * (animRearMirrorAdjustWidth + spacing);
            rearMirrorAdjustView.setTranslationX(offsetRearMirrorAdjust);
            if (swRearMirrorAdjust == 0) {
                setTextView(false, tvRearMirrorAdjust1, tvRearMirrorAdjust2, tvRearMirrorAdjust3, tvRearMirrorAdjust4);
            } else if (swRearMirrorAdjust == 1) {
                setTextView(false, tvRearMirrorAdjust2, tvRearMirrorAdjust1, tvRearMirrorAdjust3, tvRearMirrorAdjust4);
            } else if (swRearMirrorAdjust == 2) {
                setTextView(false, tvRearMirrorAdjust3, tvRearMirrorAdjust1, tvRearMirrorAdjust2, tvRearMirrorAdjust4);
            } else if (swRearMirrorAdjust == 3) {
                setTextView(false, tvRearMirrorAdjust4, tvRearMirrorAdjust1, tvRearMirrorAdjust2, tvRearMirrorAdjust3);
            }
            preSelRearMirrorAdjust = swRearMirrorAdjust;
            tvRearMirrorAdjust1.setOnClickListener(this);
            tvRearMirrorAdjust2.setOnClickListener(this);
            tvRearMirrorAdjust3.setOnClickListener(this);
            tvRearMirrorAdjust4.setOnClickListener(this);
            return dialog;
        }

        private void setTextView(boolean isDelayer, TextView... textViews) {
            new Handler().postDelayed(() -> {
                for (int i = 0; i < textViews.length; i++) {
                    if (i == 0) {
                        textViews[i].setTextColor(context.getColor(R.color.white));
                    } else {
                        textViews[i].setTextColor(context.getColor(Util.isNight(context) ? R.color.white : R.color.black));
                    }
                }
            }, isDelayer ? 300 : 0);
        }

        @SuppressLint("NonConstantResourceId")
        @Override
        public void onClick(View view) {
            switch (view.getId()) {
                case R.id.tv_rear_mirror_adjust_1:
                    swRearMirrorAdjust = 0;
                    selRearMirrorAdjustTranslate();
                    setTextView(true, tvRearMirrorAdjust1, tvRearMirrorAdjust2, tvRearMirrorAdjust3, tvRearMirrorAdjust4);
                    break;
                case R.id.tv_rear_mirror_adjust_2:
                    swRearMirrorAdjust = 1;
                    selRearMirrorAdjustTranslate();
                    setTextView(true, tvRearMirrorAdjust2, tvRearMirrorAdjust1, tvRearMirrorAdjust3, tvRearMirrorAdjust4);
                    break;
                case R.id.tv_rear_mirror_adjust_3:
                    swRearMirrorAdjust = 2;
                    selRearMirrorAdjustTranslate();
                    setTextView(true, tvRearMirrorAdjust3, tvRearMirrorAdjust1, tvRearMirrorAdjust2, tvRearMirrorAdjust4);
                    break;
                case R.id.tv_rear_mirror_adjust_4:
                    swRearMirrorAdjust = 3;
                    selRearMirrorAdjustTranslate();
                    setTextView(true, tvRearMirrorAdjust4, tvRearMirrorAdjust1, tvRearMirrorAdjust2, tvRearMirrorAdjust3);
                    break;
            }
            onProgressChangedListener.onRearMirrorSwitch(swRearMirrorAdjust);
        }

        private void selRearMirrorAdjustTranslate() {
            if (preSelRearMirrorAdjust == swRearMirrorAdjust) {
                return;
            }
            int toDistance = (animRearMirrorAdjustWidth + spacing) * swRearMirrorAdjust + spacing;
            beginTranslateAndScale(rearMirrorAdjustView, offsetRearMirrorAdjust, toDistance,
                    rearMirrorAdjustLL.getChildAt(preSelRearMirrorAdjust).getWidth(), rearMirrorAdjustLL.getChildAt(swRearMirrorAdjust).getWidth());
            offsetRearMirrorAdjust = toDistance;
            preSelRearMirrorAdjust = swRearMirrorAdjust;
        }

        public void beginTranslateAndScale(View view, int from, int to, int startWidth, int endWidth) {
            view.setTranslationX(0);
            ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(view, "translationX", from, to);
            final ValueAnimator scaleAnimator = ValueAnimator.ofInt(startWidth, endWidth);
            scaleAnimator.addUpdateListener(animation -> {
                int animatedValue = (int) scaleAnimator.getAnimatedValue();
                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                params.width = animatedValue;
                params.setMargins(0, 8, 8, 8);
                view.setLayoutParams(params);
            });
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(scaleAnimator, objectAnimator);
            animatorSet.setDuration(300);
            animatorSet.start();
        }
    }


    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onRearMirrorSwitch(int status);


    }

}
