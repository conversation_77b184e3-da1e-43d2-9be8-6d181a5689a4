package com.bitech.vehiclesettings.view.quickcontrol;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertQSteeringWhellBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

/**
 * FileName: NoTitleUIAlert
 * Author: WUY1WHU
 * Date: 2024/6/19 10:29
 * Description:通用对话框
 */
public class SteeringWheelUIAlert extends BaseDialog {

    private static onModeChangedListener onModeChangedListener;

    public SteeringWheelUIAlert(Context context) {
        super(context);
    }

    public SteeringWheelUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected SteeringWheelUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static SteeringWheelUIAlert.onModeChangedListener getOnModeChangedListener() {
        return onModeChangedListener;
    }

    public static void setOnModeChangedListener(SteeringWheelUIAlert.onModeChangedListener onModeChangedListener) {
        SteeringWheelUIAlert.onModeChangedListener = onModeChangedListener;
    }


    public static class Builder implements View.OnClickListener {

        private final Context context;
        private boolean isCan = true;
        public SteeringWheelUIAlert dialog = null;
        private DialogAlertQSteeringWhellBinding binding;
        private int selMode;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public Builder setMode(int selMode) {
            this.selMode = selMode;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public SteeringWheelUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null) {
                dialog = new SteeringWheelUIAlert(context,
                        R.style.Dialog);
            }
            binding = DialogAlertQSteeringWhellBinding.inflate(LayoutInflater.from(context));
            if (selMode == 0) {
                binding.tvSteelMode1.setSelected(true);
            } else if (selMode == 1) {
                binding.tvSteelMode2.setSelected(true);
            } else if (selMode == 2) {
                binding.tvSteelMode3.setSelected(true);
            } else if (selMode == 3) {
                binding.tvSteelMode4.setSelected(true);
            } else if (selMode == 4) {
                binding.tvSteelMode5.setSelected(true);
            } else if (selMode == 5) {
                binding.tvSteelMode6.setSelected(true);
            } else if (selMode == 6) {
                binding.tvSteelMode7.setSelected(true);
            }

            BindingUtil.bindClicks(this, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode6, binding.tvSteelMode7);
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1584;
            layoutParams.height = 984;
            window.setAttributes(layoutParams);
            return dialog;
        }

        @SuppressLint("NonConstantResourceId")
        @Override
        public void onClick(View view) {
            switch (view.getId()) {
                case R.id.tv_steel_mode_1:
                    // 行车记录仪抓拍
                    selMode = 0;
                    binding.scrollView.smoothScrollTo(0, binding.tvSteelMode1.getTop());
                    onModeChangedListener.onMode(selMode);
//                    setTexView(binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode6);
                    break;
                case R.id.tv_steel_mode_2:
                    // AVM进入/退出
                    selMode = 1;
                    onModeChangedListener.onMode(selMode);
//                    setTexView(binding.tvSteelMode2, binding.tvSteelMode1, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode6);
                    break;
                case R.id.tv_steel_mode_3:
                    // 方向盘调节HUD
                    selMode = 2;
                    onModeChangedListener.onMode(selMode);
//                    setTexView(binding.tvSteelMode3, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode6);
                    break;
                case R.id.tv_steel_mode_4:
                    // 后视镜调节
                    selMode = 3;
                    onModeChangedListener.onMode(selMode);
//                    setTexView(binding.tvSteelMode4, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode5, binding.tvSteelMode6);
                    break;
                case R.id.tv_steel_mode_5:
                    // 音源切换
                    selMode = 4;
                    onModeChangedListener.onMode(selMode);
//                    setTexView(binding.tvSteelMode5, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode6);
                    break;
                case R.id.tv_steel_mode_6:
                    // 方向盘调节
                    selMode = 5;
                    onModeChangedListener.onMode(selMode);
//                    setTexView(binding.tvSteelMode6, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5);
                    break;
                case R.id.tv_steel_mode_7:
                    // 全息影像
                    selMode = 6;
                    binding.scrollView.smoothScrollTo(0, binding.tvSteelMode6.getBottom());
                    onModeChangedListener.onMode(selMode);
//                    setTexView(binding.tvSteelMode7, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5);
                    break;
            }
            setSelected(selMode);
        }

        public void setSelected(int index) {
            switch (index) {
                case 0:
                    setTexView(binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode6,binding.tvSteelMode7);
                    break;
                case 1:
                    setTexView(binding.tvSteelMode2, binding.tvSteelMode1, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode6, binding.tvSteelMode7);
                    break;
                case 2:
                    setTexView(binding.tvSteelMode3, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode6, binding.tvSteelMode7);
                    break;
                case 3:
                    setTexView(binding.tvSteelMode4, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode5, binding.tvSteelMode6, binding.tvSteelMode7);
                    break;
                case 4:
                    setTexView(binding.tvSteelMode5, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode6, binding.tvSteelMode7);
                    break;
                case 5:
                    setTexView(binding.tvSteelMode6, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode7);
                    break;
                case 6:
                    setTexView(binding.tvSteelMode7, binding.tvSteelMode1, binding.tvSteelMode2, binding.tvSteelMode3, binding.tvSteelMode4, binding.tvSteelMode5, binding.tvSteelMode6);
                    break;
                default:
                    break;
            }
        }

        private void setTexView(TextView... textViews) {
            for (int i = 0; i < textViews.length; i++) {
                if (i == 0) {
                    textViews[i].setSelected(true);
                } else {
                    textViews[i].setSelected(false);
                }
            }
        }
    }
    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;
    public void dismiss(){
        super.dismiss();
    }

    public interface onModeChangedListener {
        void onMode(int mode);
    }

}
