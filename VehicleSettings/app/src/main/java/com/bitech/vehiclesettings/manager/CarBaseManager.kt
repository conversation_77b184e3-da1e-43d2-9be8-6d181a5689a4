package com.bitech.vehiclesettings.manager

import android.car.Car
import android.car.CarInfoManager
import android.car.hardware.power.CarPowerManager
import android.car.hardware.property.CarPropertyManager
import android.car.media.CarAudioManager
import android.content.Context
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @Description: 建立与Car连接的管理类,用于管理车辆信息各类对象.
 **/
object CarBaseManager : Car.CarServiceLifecycleListener {
    private const val TAG = "CarBaseManager"

    // 车辆对象
    private var mCarApi: Car? = null

    // 车辆代理对象
    private var mCarPropertyManager: CarPropertyManager? = null

    // 车辆音频管理对象
    private var mCarAudioManager: CarAudioManager? = null

    // 车辆信息管理对象
    private var mCarInfoManager: CarInfoManager? = null

    // 车辆电源管理对象
    private var mCarPowerManager: CarPowerManager? = null

    /**
     * CarBaseManager初始化.
     */
    fun initCarBaseManager(context: Context) {
        LogUtil.i(TAG, "init:")
        if (mCarApi == null) {
            mCarApi = Car.createCar(context)
            if (mCarApi != null) {
                mCarApi?.run {
                    val isCarApiConnected = isConnected
                    LogUtil.i(TAG, "CarBaseManager : isCarApiConnected:$isCarApiConnected")
                    mCarPropertyManager = getCarManager(Car.PROPERTY_SERVICE) as CarPropertyManager
                    LogUtil.i(TAG, "CarBaseManager : isCarApiConnected mCarPropertyManager")
                    mCarAudioManager = getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
                    LogUtil.i(TAG, "CarBaseManager : isCarApiConnected mCarAudioManager")
                    mCarInfoManager = getCarManager(Car.INFO_SERVICE) as CarInfoManager
                    LogUtil.i(TAG, "CarBaseManager : isCarApiConnected mCarInfoManager")
                    mCarPowerManager = getCarManager(Car.POWER_SERVICE) as CarPowerManager
                    LogUtil.i(TAG, "CarBaseManager : isCarApiConnected mCarPowerManager")
                }
            } else {
                LogUtil.i(TAG, "CarBaseManager: isCarApiConnected: mCarApi == null")
            }
        }
    }

    /**
     * 获取CarPropertyManager对象.
     *
     * @return CarPropertyManager
     */
    fun getCarPropertyManager(): CarPropertyManager? {
        if (mCarPropertyManager == null) {
            if (mCarApi != null) {
                mCarPropertyManager =
                    mCarApi!!.getCarManager(Car.PROPERTY_SERVICE) as CarPropertyManager
            }
        }
        return mCarPropertyManager
    }

    /**
     * 获取CarAudioManager对象.
     *
     * @return CarAudioManager
     */
    fun getCarAudioManager(): CarAudioManager? {
        if (mCarAudioManager == null) {
            if (mCarApi != null) {
                mCarAudioManager = mCarApi!!.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
            }
        }
        return mCarAudioManager
    }

    /**
     * 获取CarInfoManager对象.
     *
     * @return CarInfoManager
     */
    fun getCarInfoManager(): CarInfoManager? {
        if (mCarInfoManager == null) {
            if (mCarApi != null) {
                mCarInfoManager = mCarApi!!.getCarManager(Car.INFO_SERVICE) as CarInfoManager
            }
        }
        return mCarInfoManager
    }

    /**
     * 获取CarPowerManager对象.
     *
     * @return CarPowerManager
     */
    fun getCarPowerManager(): CarPowerManager? {
        if (mCarPowerManager == null) {
            if (mCarApi != null) {
                mCarPowerManager = mCarApi!!.getCarManager(Car.POWER_SERVICE) as CarPowerManager
            }
        }
        return mCarPowerManager
    }

    /**
     * 断开与车辆建立的连接.
     *
     */
    fun disconnectCarManager() {
        if (mCarApi != null) {
            mCarApi!!.disconnect()
            mCarApi = null
        }
        if (mCarPropertyManager != null) {
            mCarPropertyManager!!.onCarDisconnected()
            mCarPropertyManager = null
        }
        if (mCarInfoManager != null) {
            mCarInfoManager!!.onCarDisconnected()
            mCarInfoManager = null
        }
        if (mCarAudioManager != null) {
            mCarAudioManager!!.onCarDisconnected()
            mCarAudioManager = null
        }
        if (mCarPowerManager != null) {
            mCarPowerManager!!.onCarDisconnected()
            mCarPowerManager = null
        }
    }

    override fun onLifecycleChanged(car: Car?, status: Boolean) {
        //  status为false时，服务断开需要做处理
        LogUtil.d(TAG, "onLifecycleChanged : $status")
    }
}
