package com.bitech.vehiclesettings.view.recognition;

import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertRCameraSwitchOffBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertRCameraSwitchOnBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.iflytek.autofly.icvp.sdk.ICVPCallBack;
import com.iflytek.autofly.icvp.sdk.ICVPManager;
import com.iflytek.autofly.icvp.sdk.common.ICVPCameraConstants;

public class CameraSwitchOffUIAlert extends Dialog {
    private static final String TAG = CameraSwitchOnUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public CameraSwitchOffUIAlert(@NonNull Context context) {
        super(context);
    }

    public CameraSwitchOffUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected CameraSwitchOffUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        CameraSwitchOffUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private boolean cameraConfirmFlag = false;
        private boolean cameraCancelFlag = false;
        protected DialogAlertRCameraSwitchOffBinding binding;

        private PrivacyPolicyUIAlert.Builder privacyPolicyUIAlert;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private CameraSwitchOffUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }


        /**
         * Create the custom dialog
         */
        public CameraSwitchOffUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new CameraSwitchOffUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertRCameraSwitchOffBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            layoutParams.height = 508;
            window.setAttributes(layoutParams);

            // 设置按钮效果
            // 设置确定按钮效果
            clickConfirmCamera();
            // 设置取消按钮效果
            clickCancelCamera();

            return dialog;
        }

        // 摄像头关闭接口模拟
        private boolean closeConfirmCamera() {
            return Math.random() < 0.8;
        }

        // 设置确定按钮点击逻辑
        private void clickConfirmCamera() {
            BindingUtil.bindClick(binding.tvCameraConfirm, v -> {
                // 设置确定按钮不可按
                GrayEffectUtils.applyGrayEffect(binding.tvCameraConfirm);
                EToast.showToast(context, "摄像头关闭中，请稍后", 0, false);

                // 打开摄像头
                onProgressChangedListener.onDataReceived("close");
            });
        }

        public void openSuccess() {
            EToast.showToast(context, "摄像头关闭成功", 0, false);
            dialog.dismiss();
            // 设置确定按钮可按
            GrayEffectUtils.removeGrayEffect(binding.tvCameraConfirm);
        }

        public void openFail() {
            EToast.showToast(context, "摄像头关闭失败，请检查后重试", 0, false);
            dialog.dismiss();
            // 设置确定按钮可按
            GrayEffectUtils.removeGrayEffect(binding.tvCameraConfirm);
        }

        // 设置按钮锁
        private void lockedBtn(boolean flag) {
            if (flag) {
                cameraConfirmFlag = false;
                cameraCancelFlag = false;
            } else {
                cameraConfirmFlag = true;
                cameraCancelFlag = true;
            }
            setConfirmCamera();
            setCancelCamera();
        }

        // 更改确定按钮状态
        private void setConfirmCamera() {
            binding.tvCameraConfirm.setClickable(cameraConfirmFlag);
            binding.tvCameraConfirm.setBackgroundResource(cameraConfirmFlag ? R.drawable.selector_bg_open : R.drawable.selector_bg_disabled);
        }

        // 更改取消按钮状态
        private void setCancelCamera() {
            binding.tvCameraCancel.setClickable(cameraCancelFlag);
            binding.tvCameraCancel.setBackgroundResource(cameraCancelFlag ? R.drawable.selector_bg_cancel : R.drawable.selector_bg_cancel_disabled);
        }

        // 设置取消按钮点击逻辑
        private void clickCancelCamera() {
            binding.tvCameraCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onDataReceived(String data);
    }
}
