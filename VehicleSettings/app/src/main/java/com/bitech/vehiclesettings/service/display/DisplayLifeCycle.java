package com.bitech.vehiclesettings.service.display;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.bitech.platformlib.interfaces.display.IDisplayManagerListener;
import com.bitech.platformlib.manager.DisplayManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.utils.PrefsConst;

import java.util.concurrent.atomic.AtomicInteger;

public class DisplayLifeCycle implements LifecycleEventObserver {
    private static final String TAG = DisplayLifeCycle.class.getName() + "wzh2whu";

    private DisplayManager displayManager;
    public static AtomicInteger apcLevelLimit = new AtomicInteger(CarNewEnergy.ApcLevelLimit.LEVEL_0);
    public static AtomicInteger backLight = new AtomicInteger(0);


    @Override
    public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
        switch (event) {
            case ON_CREATE:
                Log.i(TAG, "onCreate: " + source);
                displayManager = DisplayManager.getInstance();
                if (displayManager != null) {
                    displayManager.addCallback(TAG, msgCallback);
                    displayManager.registerListener();
                }
                break;
            case ON_DESTROY:
                Log.i(TAG, "onDestroy: " + source);
                if (displayManager != null) {
                    displayManager.removeCallback(TAG);
                }
                break;
            default:
                break;
        }
    }

    private IDisplayManagerListener msgCallback = new IDisplayManagerListener() {

        @Override
        public void getAPCCallback(int status) {
            apcLevelLimit.set(status);
            Log.d(TAG, "lifecycle--getAPCCallback: " + status);
            int zkpBrightness = DisplayPresenter.getInstance().getZKPBrightness();
            zkpBrightness = DisplayPresenter.getInstance().getZKPBrightnessLimit(zkpBrightness);
            DisplayPresenter.getInstance().setZKPBrightness(zkpBrightness);
        }

        @Override
        public void getBacklightModeCallback(int status) {
            backLight.set(status);
            if (DisplayPresenter.getInstance().getZKPAuto() == PrefsConst.TRUE) {
                Log.d(TAG, "lifecycle--getBacklightModeCallback: " + status);
                int level = 0;
                if (DisplayPresenter.getDisplayMode()) {
                    level = DisplayPresenter.zkpAutoLightReverseNight(status);
                } else {
                    level = DisplayPresenter.zkpAutoLightReverseDay(status);
                }
                Log.d(TAG, "lifecycle--getBacklightModeCallback--中控屏level: " + level);
                Log.d(TAG, "中控屏开启自动亮度:" + level);
                int zkpBrightnessLimit = DisplayPresenter.getInstance().getZKPBrightnessLimit(level);
                DisplayPresenter.getInstance().setZKPBrightness(zkpBrightnessLimit);
            }
            if(DisplayPresenter.getInstance().getYBPAuto() == PrefsConst.TRUE){
                Log.d(TAG, "lifecycle--getBacklightModeCallback: " + status);
                int level = 0;
                if (DisplayPresenter.getDisplayMode()) {
                    level = DisplayPresenter.ybpAutoLightReverseNight(status);
                } else {
                    level = DisplayPresenter.ybpAutoLightReverseDay(status);
                }
                Log.d(TAG, "lifecycle--getBacklightModeCallback--仪表盘level: " + level);
                Log.d(TAG, "仪表盘开启自动亮度:" + level);
                DisplayPresenter.getInstance().setYBPBrightness(level);
            }

        }

        @Override
        public void getSystemColorCallback(int status) {

        }
    };
}
