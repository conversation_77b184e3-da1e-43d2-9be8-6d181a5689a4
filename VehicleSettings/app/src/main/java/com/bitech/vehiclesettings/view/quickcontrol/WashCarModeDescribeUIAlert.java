package com.bitech.vehiclesettings.view.quickcontrol;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertWashDescribeBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class WashCarModeDescribeUIAlert extends BaseDialog {
    private static final String TAG = WashCarModeDescribeUIAlert.class.getSimpleName();

    public WashCarModeDescribeUIAlert(@NonNull Context context) {
        super(context);
    }

    public WashCarModeDescribeUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected WashCarModeDescribeUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    private Builder builderRef;

    public void setBuilderRef(Builder builder) {
        this.builderRef = builder;
    }

    public static class Builder{

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertWashDescribeBinding binding;
        private volatile boolean isActive;
        boolean globalAlert = false;


        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private WashCarModeDescribeUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }
        public void setGlobalAlert(boolean b){
            globalAlert = b;
        }

        public WashCarModeDescribeUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public WashCarModeDescribeUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new WashCarModeDescribeUIAlert(context,
                        R.style.Dialog);
            binding = DialogAlertWashDescribeBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            isActive = true;
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1584;
            layoutParams.height = 1049;
            layoutParams.type = globalAlert ?WindowManager.LayoutParams.TYPE_SYSTEM_ALERT: WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG;
            dialog.setBuilderRef(this);
            window.setAttributes(layoutParams);
            return dialog;
        }

        private void initView() {
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        // 在Builder类中添加
        public void destroy() {
            isActive = false;
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        if (builderRef != null) {
            builderRef.destroy(); // 销毁 builder 内部资源
        }
        unregisterReceiver(this.getContext());
        super.dismiss();
    }
    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (!hasFocus) {
            dismiss();
        }
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }
}