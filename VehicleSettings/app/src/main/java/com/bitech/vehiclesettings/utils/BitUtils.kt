package com.bitech.vehiclesettings.utils

object BitUtils {

    /**
     * 获取整数中指定位置的 bit 位值
     * @param value 要操作的整数
     * @param position 要获取的 bit 位位置，最高位为 0，最低位为 31
     * @return 指定位置的 bit 位值（0 或 1）
     */
    fun getBit(value: Int, position: Int): Int {
        require(position in 0..31) { "Position must be between 0 and 31" }
        return (value shr (position)) and 1
    }

    /**
     * 获取长整数中指定位置的 bit 位值
     * @param value 要操作的长整数
     * @param position 要获取的 bit 位位置，最高位为 0，最低位为 63
     * @return 指定位置的 bit 位值（0 或 1）
     */
    fun getBit(value: Long, position: Int): Int {
        require(position in 0..63) { "Position must be between 0 and 63" }
        return ((value shr (position)) and 1L).toInt()
    }
}
