package com.bitech.vehiclesettings.view.quickcontrol;

import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.SegmentItemBean;
import com.bitech.vehiclesettings.databinding.DialogAlertCDriveAirbagBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertQWiperSensBinding;
import com.bitech.vehiclesettings.utils.MessageConst;
import com.bitech.vehiclesettings.view.carsetting.DriveAirbagConfirmUIAlert;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class WiperSensUIAlert extends BaseDialog {
    private static final String TAG = WiperSensUIAlert.class.getSimpleName();
    private static WiperSensUIAlert.onProgressChangedListener onProgressChangedListener;

    public WiperSensUIAlert(@NonNull Context context) {
        super(context);
    }

    public WiperSensUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected WiperSensUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static WiperSensUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(WiperSensUIAlert.onProgressChangedListener onProgressChangedListener) {
        WiperSensUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertQWiperSensBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        public WiperSensUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public WiperSensUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public WiperSensUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new WiperSensUIAlert(context,
                        R.style.Dialog);
            binding = DialogAlertQWiperSensBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 400;
            window.setAttributes(layoutParams);

            initPicker();

            return dialog;
        }

        private void initPicker() {
            binding.spvDriveAirbag.setItems(R.string.str_carsetting_wiper_sens_1, R.string.str_carsetting_wiper_sens_2,
                    R.string.str_carsetting_wiper_sens_3, R.string.str_carsetting_wiper_sens_4);
            binding.spvDriveAirbag.setSelectedIndex(onProgressChangedListener.getInitStatus(), false);
            binding.spvDriveAirbag.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    onProgressChangedListener.setSwitch(binding, index, text);
                }

                @Override
                public void onItemClicked(int index, String text) {

                }
            });

        }


        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public void selWiperSensTranslate(int status) {
            if (dialog != null) {
                binding.spvDriveAirbag.setSelectedIndex(status, true);
            }
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }
    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }

    public static boolean isShow = false;


    public interface onProgressChangedListener {
        void setSwitch(DialogAlertQWiperSensBinding binding, int index, String text);
        int getInitStatus();
    }
}
