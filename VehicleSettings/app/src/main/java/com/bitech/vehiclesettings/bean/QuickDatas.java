package com.bitech.vehiclesettings.bean;

public class QuickDatas extends BaseData {
    private String tag;

    /**
     * 车窗
     */
    private boolean windowFlag = false;

    /**
     * 电动尾翼
     */
    private boolean autotailFlag = false;



    private int carWindowStatus = 0;
    private int carAutotailStatus = 0;

    private int closeUnlockStatus = 0;

    @Override
    public String getTag() {
        return tag;
    }

    @Override
    public void setTag(String tag) {
        this.tag = tag;
    }

    public boolean isWindowFlag() {
        return windowFlag;
    }

    public void setWindowFlag(boolean windowFlag) {
        this.windowFlag = windowFlag;
    }

    public boolean isAutotailFlag() {
        return autotailFlag;
    }

    public void setAutotailFlag(boolean autotailFlag) {
        this.autotailFlag = autotailFlag;
    }

    public int getCarWindowStatus() {
        return carWindowStatus;
    }

    public void setCarWindowStatus(int carWindowStatus) {
        this.carWindowStatus = carWindowStatus;
    }

    public int getCarAutotailStatus() {
        return carAutotailStatus;
    }

    public void setCarAutotailStatus(int carAutotailStatus) {
        this.carAutotailStatus = carAutotailStatus;
    }

    public int getCloseUnlockStatus() {
        return closeUnlockStatus;
    }

    public void setCloseUnlockStatus(int closeUnlockStatus) {
        this.closeUnlockStatus = closeUnlockStatus;
    }
}
