package com.bitech.vehiclesettings.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Message
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import androidx.lifecycle.ViewModelProvider
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.manager.QuickManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl
import com.bitech.vehiclesettings.databinding.DialogAlertCDriveAirbagBinding
import com.bitech.vehiclesettings.databinding.DialogAlertQRearRoateBinding
import com.bitech.vehiclesettings.databinding.DialogAlertQWiperSensBinding
import com.bitech.vehiclesettings.databinding.FragmentQuickControlBinding
import com.bitech.vehiclesettings.presenter.SafeHandler
import com.bitech.vehiclesettings.presenter.SafeHandlerCallback
import com.bitech.vehiclesettings.utils.BindingUtil
import com.bitech.vehiclesettings.utils.CommonUtils
import com.bitech.vehiclesettings.utils.ElasticAnimatioUtil
import com.bitech.vehiclesettings.utils.ElasticAnimationUtil
import com.bitech.vehiclesettings.utils.MessageConst
import com.bitech.vehiclesettings.view.carsetting.ChildLockUIAlert
import com.bitech.vehiclesettings.view.carsetting.DriveAirbagConfirmUIAlert
import com.bitech.vehiclesettings.view.carsetting.DriveAirbagUIAlert
import com.bitech.vehiclesettings.view.carsetting.LockTipsUIAlert
import com.bitech.vehiclesettings.view.common.ComfirmUIAlert
import com.bitech.vehiclesettings.view.common.DetailsUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.RearMirrorAdjustUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.RearMirrorUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.RearRoateUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.SteeringWheelUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.ThreeDModelUIAlert
import com.bitech.vehiclesettings.view.quickcontrol.WiperSensUIAlert
import com.bitech.vehiclesettings.viewmodel.MainActViewModel
import com.bitech.vehiclesettings.viewmodel.QuickControlViewModel

class QuickFragment : BaseFragment<FragmentQuickControlBinding>(), View.OnClickListener,
    SafeHandlerCallback {
    private val TAG: String = QuickFragment::class.java.simpleName
    private var viewModel: QuickControlViewModel? = null
    private var quickHandler: SafeHandler? = null
    private var isActive = false
    var carAutotailStatus: Int = 0
    private var rearMirrorUIAlert: RearMirrorUIAlert.Builder? = null

    // 倒车后视镜调节
    private var adjustUIAlert: RearMirrorAdjustUIAlert.Builder? = null
    private var lockTipsUIAlert: LockTipsUIAlert.Builder? = null
    private var childLockUIAlert: ChildLockUIAlert.Builder? = null
    private var driveAirbagUIAlert: DriveAirbagUIAlert.Builder? = null
    private var rearRoateUIAlert: RearRoateUIAlert.Builder? = null
    private var wiperSensUIAlert: WiperSensUIAlert.Builder? = null
    private val threeDModelUIAlert: ThreeDModelUIAlert.Builder? = null
    private var detailUIAlert: DetailsUIAlert.Builder? = null
    private val steeringWheelUIAlert: SteeringWheelUIAlert.Builder? = null

    private val mainActViewModel: MainActViewModel? = null
    private val activity: MainActivity? = null


    @JvmField
    val QucikControl_CarWindow: Int = 11

    @JvmField
    val RAIN_SCOOP_SENSITIVITY_UI_ALERT: Int = 12

    @JvmField
    val REAR_MIRROR_UI_ALERT: Int = 10

    @JvmField
    val REAR_steering_Wheel_ADJUST_UI_ALERT: Int = 13

    private val steeringWheelMode = 0

    private var lockTipsStatus = 0
    private var driveBagFlag = 0
    private val isDriveAirBagConfirm = 0
    private var backRearAdjustStatus = 0

    // 判断设置安全气囊是否成功
    private var switchDriveBagFlag: Boolean = true
    private var swWiperSens = 0
    var sbHudRoate: Int = 0
    private val sunShadeFailure = booleanArrayOf(
        false, false, false, false, false, false, false
    )

    @JvmField
    val quickManager: QuickManager =
        BitechCar.getInstance().getServiceManager(BitechCar.CAR_QUICK_MANAGER) as QuickManager

    override fun getLayoutResId(
        inflater: LayoutInflater?,
        container: ViewGroup?
    ): FragmentQuickControlBinding {
        binding = FragmentQuickControlBinding.inflate(layoutInflater)
        return binding as FragmentQuickControlBinding
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isActive = true
        quickHandler = SafeHandler(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        var rootView = getLayoutResId(inflater, container).root
        initObserve()
        // 注册监听事件
//        quickControllerRegLightListen()
        // 滚动事件监听
        scrollListener()
        return rootView
    }

    /**
     * 滚动监听
     */
    private fun scrollListener() {
        binding?.scrollView?.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val activity = activity as MainActivity?
            activity?.binding?.ivModel?.handleScroll(scrollY)
        }
    }


    override fun initObserve() {
        viewModel = ViewModelProvider(this).get(QuickControlViewModel::class.java)
        viewModel?.centralLocking?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 更新中控锁
            if (status != null) {
                val uiState = viewModel?.centerLockingUIState(status)
                if (uiState != null) {
                    updateCentralLockingUI(uiState)
                }
            }
        }
        viewModel?.rearTailgate?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 更新后尾门
            if (status != null) {
                val uiState = viewModel?.rearTailGateUIState(status)
                if (uiState != null) {
                    updateRearTailGateUI(uiState)
                }
            }
        }
        viewModel?.rearMirrorFold?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 更新后视镜折叠 3
            if (status != null) {
                val uiState = viewModel?.rearMirrorUIState(status)
                if (uiState != null) {
                    updateRearMirrorUI(uiState)
                }
            }
        }
        viewModel?.windowState?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 更新车窗状态2
            if (status != null) {
                updateWindowUI(status)
            }
        }
        viewModel?.windowLock?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 车窗锁
            if (status != null) {
                val statusUI = viewModel?.windowLockUIState(status)
                if (statusUI != null) {
                    updateWindowLockUI(statusUI)
                }
            }
        }
        viewModel?.sunShade?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 遮阳帘
            if (status != null) {
                val statusUI = viewModel?.sunshadeUIState(status)
                if (statusUI != null) {
                    updateSunshadeUI(statusUI)
                }
            }
        }
        viewModel?.autoTail?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 电动尾翼
            if (status != null) {
                val statusUI = viewModel?.autoTailUIState(status)
                if (statusUI != null) {
                    updateAutoTail(statusUI)
                }
            }
        }
        viewModel?.skyWindow?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 天窗 1
            if (status != null) {
                updateSkyWindowUI(status)
            }
        }
        viewModel?.approachingUnlock?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 感应靠近解锁
            if (status != null) {
                val uiStatus = viewModel?.approachingUnlockUIState(status)
                if (uiStatus != null) {
                    updateApproachingUnlockUI(uiStatus)
                }
            }
        }
        viewModel?.departureLocking?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 感应离车解锁
            if (status != null) {
                val uiStatus = viewModel?.departureLockingUIState(status)
                if (uiStatus != null) {
                    updateDepartureLockingUI(uiStatus)
                }
            }
        }
        viewModel?.lockAutoRaiseWindow?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 锁车自动升窗
            if (status != null) {
                val uiStatus = viewModel?.lockAutoRaiseWindowUIState(status)
                if (uiStatus != null) {
                    updateLockAutoRaiseWindowUI(uiStatus)
                }
            }
        }
        viewModel?.lockCarSunRoofShade?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 锁车收起遮阳帘
            if (status != null) {
                val uiStatus = viewModel?.lockCarSunRoofShadeUIState(status)
                if (uiStatus != null) {
                    updateLockCarSunroofShadeUI(uiStatus)
                }
            }
        }
        viewModel?.defenseReminder?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 设防提示
            updateLockTipsUI()
        }
        viewModel?.leftChildLock?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 左儿童锁
            if (status != null) {
                val uiStatus = viewModel?.leftChildLockUIState(status)
                if (uiStatus != null) {
                    updateLeftChildLockUI(uiStatus)
                }
            }
        }
        viewModel?.rightChildLock?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 右儿童锁
            if (status != null) {
                val uiStatus = viewModel?.rightChildLockUIState(status)
                if (uiStatus != null) {
                    updateRightChildLockUI(uiStatus)
                }
            }
        }
        viewModel?.rearScreenControl?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 后屏控制锁
            if (status != null) {
                val uiStatus = viewModel?.rearScreenControlUIState(status)
                if (uiStatus != null) {
                    updateRearScreenControlUI(uiStatus)
                }
            }
        }
        viewModel?.automaticLocking?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 自动落锁
            if (status != null) {
                val uiStatus = viewModel?.automaticLockingUIState(status)
                if (uiStatus != null) {
                    updateAutomaticLockingUI(uiStatus)
                }
            }
        }
        viewModel?.automaticParkingUnlock?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 驻车自动解锁
            if (status != null) {
                val uiStatus = viewModel?.automaticParkingUnlockUIState(status)
                if (uiStatus != null) {
                    updateAutomaticParkingUnlockUI(uiStatus)
                }
            }
        }
        viewModel?.autoRearMirrorFold?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 外后视镜自动折叠
            if (status != null) {
                val uiStatus = viewModel?.autoRearMirrorFoldUIState(status)
                if (uiStatus != null) {
                    updateAutoArearMirrorFoldUI(uiStatus)
                }
            }
        }
        viewModel?.autoHotRearMirror?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 雨天自动加热外后视镜
            if (status != null) {
                val uiStatus = viewModel?.autoHotRearMirrorUIState(status)
                if (uiStatus != null) {
                    updateHotMirrorUI(uiStatus)
                }
            }
        }
        viewModel?.backRearAdjust?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 倒车时后视镜自动调节
            updateBackRearAdjustUI()
        }
        viewModel?.seatPortable?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 座椅便携进入退出
            if (status != null) {
                val uiStatus = viewModel?.seatPortableUIState(status)
                if (uiStatus != null) {
                    updateSeatPortableUI(uiStatus)
                }
            }
        }
        viewModel?.refuelSmallDoor?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 加油小门
            if (status != null) {
                val uiStatus = viewModel?.refuelSmallDoorUIState(status)
                if (uiStatus != null) {
                    updateRefuelSmallDoorUI(uiStatus)
                }
            }
        }
        viewModel?.driveAirbag?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 副驾安全气囊
            updateDriveAirBagUI()
        }
        viewModel?.wiperSensitivity?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 更新UI雨刮器
            selWiperSensTranslate()
        }
        viewModel?.hudRoate?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 更新UI后尾门
            updateHudRoateUI()
        }
        viewModel?.rearMirrorAdjust?.observe(
            viewLifecycleOwner
        ) { status: Int? ->
            // 打开后视镜调节
            if (status != null) {
                openRearMirror(status)
            }
        }
    }

    override fun initView() {
    }

    fun loadPageAnim(currentPosition: Int, position: Int) {
        loadPageAnim(binding?.scrollView, currentPosition, position)
    }

    override fun onResume() {
        super.onResume()
        // 手动触发一次滚动监听
        binding?.scrollView?.post {
            val scrollY = binding?.scrollView?.scrollY
            val activity = activity as MainActivity?
            if (scrollY != null) {
                activity?.binding?.ivModel?.handleScroll(scrollY)
            }
        }
    }

    override fun onDestroy() {
        isActive = false
        super.onDestroy()
        if (binding != null) {
            binding = null
        }
    }

    // 中控锁UI
    private fun updateCentralLockingUI(status: Int) {
        Log.d(TAG, "设置中控锁UI: $status")
        ElasticAnimationUtil.applyElasticTouchEffect(binding?.tvCenterLockDesc)
        binding?.tvCenterLockDesc?.isSelected = status == 1
    }

    // 后尾门UI
    private fun updateRearTailGateUI(centralLockingSate: Int) {
        ElasticAnimationUtil.applyElasticTouchEffect(binding?.tvTailLockDesc)
        binding?.tvTailLockDesc?.isSelected = centralLockingSate == 1
    }

    // 后视镜折叠UI
    private fun updateRearMirrorUI(centralLockingSate: Int) {
        ElasticAnimatioUtil.imageAnimaStart(binding?.tvRearMirrorDesc)
        binding?.tvRearMirrorDesc?.isSelected = centralLockingSate == 1
    }

    // 车窗UI
    private fun updateWindowUI(status: Int) {
        when (status) {
            0 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarWindowRaise)
            1 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarWindowLower)
            2 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarWindowBreathable)
        }
    }

    // 车窗锁UI
    private fun updateWindowLockUI(windowLockSate: Int) {
        ElasticAnimatioUtil.imageAnimaStart(binding?.tvWindowLockDesc)
        binding?.tvWindowLockDesc?.isSelected = windowLockSate == 1
    }

    // 遮阳帘UI
    private fun updateSunshadeUI(status: Int) {
        when (status) {
            0 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarSunshadeFront)
            1 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarSunshadeRear)
            else -> {}
        }
        binding?.tvCarSunshadeFront?.isSelected = 0 == status
        binding?.tvCarSunshadeRear?.isSelected = 1 == status
    }

    // 电动尾翼UI
    private fun updateAutoTail(status: Int) {
        when (status) {
            0 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarAutotailClose)
            1 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarAutotail1)
            2 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarAutotail2)
            3 -> ElasticAnimatioUtil.imageAnimaStart(binding?.tvCarAutotailAuto)
            else -> {}
        }
        binding?.tvCarAutotailClose?.isSelected = 0 == status
        binding?.tvCarAutotail1?.isSelected = 1 == status
        binding?.tvCarAutotail2?.isSelected = 2 == status
        binding?.tvCarAutotailAuto?.isSelected = 3 == status
    }

    // 天窗UI
    private fun updateSkyWindowUI(status: Int) {
        ElasticAnimatioUtil.imageAnimaStart(binding?.tvSkylightLockDesc)
        binding?.tvSkylightLockDesc?.isSelected = status == 1
    }

    // 感应靠近解锁UI QUICK_APPROACHING_UNLOCK
    private fun updateApproachingUnlockUI(status: Int) {
        binding?.swCloseUnlock?.isChecked = CommonUtils.IntToBool(status)
    }

    // 感应离车上锁UI
    private fun updateDepartureLockingUI(status: Int) {
        binding?.swConditionTail?.isChecked = CommonUtils.IntToBool(status)
    }

    // 锁车自动升窗UI
    private fun updateLockAutoRaiseWindowUI(status: Int) {
        binding?.swAutoWindow?.isChecked = CommonUtils.IntToBool(status)
    }

    // 锁车收起遮阳帘UI
    private fun updateLockCarSunroofShadeUI(status: Int) {
        binding?.swLockcarSunroofShade?.isChecked = CommonUtils.IntToBool(status)
    }

    /**
     * 设防提示
     */
    private fun updateLockTipsUI() {
        if (lockTipsUIAlert != null) {
            Log.d(TAG, "updateLockTipsUI设防提示窗口存在: ")
            lockTipsUIAlert?.updateLockTipsUI(lockTipsStatus)
        }
    }

    /**
     * 儿童锁-左
     *
     * @param status
     */
    private fun updateLeftChildLockUI(status: Int) {
        childLockUIAlert?.updateLeftChildLockUI(status)
    }

    /**
     * 儿童锁-右
     *
     * @param status
     */
    private fun updateRightChildLockUI(status: Int) {
        childLockUIAlert?.updateRightChildLockUI(status)
    }

    private fun updateRearScreenControlUI(status: Int) {
        childLockUIAlert?.updateRearScreenControlUI(status)
    }

    /**
     * 自动落锁UI
     *
     * @param status
     */
    private fun updateAutomaticLockingUI(status: Int) {
        binding?.swAutoLock?.isChecked = CommonUtils.IntToBool(status)
    }

    /**
     * 驻车自动解锁
     *
     * @param status
     */
    private fun updateAutomaticParkingUnlockUI(status: Int) {
        binding?.swAutoUnlock?.isChecked = CommonUtils.IntToBool(status)
    }

    // 后视镜自动折叠UI
    private fun updateAutoArearMirrorFoldUI(status: Int) {
        rearMirrorUIAlert?.updateAutoArearMirrorFoldUI(status)
    }

    // 雨天自动加热外后视镜UI
    private fun updateHotMirrorUI(status: Int) {
        rearMirrorUIAlert?.updateHotMirrorUI(status)
    }

    // 倒车时后视镜自动调节
    private fun updateBackRearAdjustUI() {
        rearMirrorUIAlert?.updateBackRearAdjustUI(backRearAdjustStatus)
    }

    // 座椅便携进入退出
    private fun updateSeatPortableUI(status: Int) {
        binding?.swConditionSeatWriting?.isChecked =
            CommonUtils.IntToBool(status)
    }

    /**
     * 加油小门
     */
    private fun updateRefuelSmallDoorUI(status: Int) {
        binding?.swRefuelSmallDoor?.isChecked = CommonUtils.IntToBool(status)
    }

    /**
     * 副驾安全气囊
     */
    private fun updateDriveAirBagUI() {
        driveAirbagUIAlert?.updateDriveAirBagUI(driveBagFlag)
    }

    /**
     * 雨刮灵敏度
     */
    private fun selWiperSensTranslate() {
        wiperSensUIAlert?.selWiperSensTranslate(swWiperSens)
    }

    /**
     * 后尾门开启高度
     */
    private fun updateHudRoateUI() {
        rearRoateUIAlert?.updateHudRoateUI(sbHudRoate)
    }

    override fun setListener() {
        BindingUtil.bindClicks(
            this,
            binding?.tvCenterLockDesc,
            binding?.tvTailLockDesc,
            binding?.tvRearMirrorDesc,
            binding?.tvWindowDesc,
            binding?.tvWindowLockDesc
        )
        BindingUtil.bindClicks(
            this,
            binding?.tvSkylightLockDesc,
            binding?.swConditionRearMirror,
            binding?.rlRearMirror,
            binding?.rlRearMirrorAdjust,
            binding?.llMyGlview
        )
        BindingUtil.bindClicks(
            this,
            binding?.tvCarWindowReback,
            binding?.tvCarWindowRebackArrow,
            binding?.tvCarWindowLower
        )
        BindingUtil.bindClicks(
            this,
            binding?.tvCarWindowBreathable,
            binding?.tvCarWindowRaise
        )

        // 遮阳帘
        BindingUtil.bindClicks(
            this,
            binding?.tvSunshadeDesc,
            binding?.tvCarSunshadeFront,
            binding?.tvCarSunshadeRear,
            binding?.tvCarSunshadeReback,
            binding?.tvCarSunshadeRebackArrow
        )

        // 电动尾翼
        BindingUtil.bindClicks(
            this,
            binding?.tvAutotailDesc,
            binding?.tvCarAutotailReback,
            binding?.tvCarAutotailRebackArrow,
            binding?.tvCarAutotailClose,
            binding?.tvCarAutotail1,
            binding?.tvCarAutotail2,
            binding?.tvCarAutotailAuto
        )

        // 感应靠近解锁
        BindingUtil.bindClicks(
            this,
            binding?.swAutoLock,
            binding?.swAutoUnlock,
            binding?.swCloseUnlock,
            binding?.swConditionTail,
            binding?.swAutoWindow,
            binding?.swLockcarSunroofShade,
            binding?.swConditionRearMirror,
            binding?.swConditionHeatingRearMirror,
            binding?.swConditionSeatWriting,
            binding?.swRefuelSmallDoor
        )

        // 方向盘按键自定义
        BindingUtil.bindClicks(
            this,
            binding?.rlSteeringWhellCustom,
            binding?.rlVehiclePowerOff
        )

        BindingUtil.bindClicks(
            this,
            binding?.rlLockTips,
            binding?.rlChildLock,
            binding?.rlSafe,
            binding?.rlHudRoate,
            binding?.rlWiperSens,
            binding?.ivSafeTips
        )

        RearMirrorAdjustUIAlert.setOnProgressChangedListener { status: Int ->
            Log.d(TAG, "后视镜调节: $status")
            viewModel?.setBackAutoRearMirrorAdjust(status)
        }

        SteeringWheelUIAlert.setOnModeChangedListener { mode: Int ->
            Log.d(TAG, "onMode: $mode")
            viewModel?.setCustomButton(mode)
        }
        ComfirmUIAlert.setOnComfirmListener { function: String ->
            Log.d(TAG, "onComfirm: $function")
            if ("vehicle_power_off" == function) {
                viewModel?.setVehiclePowerOff(1)
            }
        }


        // 设防提示接口
        LockTipsUIAlert.setOnProgressChangedListener(object :
            LockTipsUIAlert.onProgressChangedListener {
            override fun onSwitch(index: Int) {
                lockTipsStatus = index
                viewModel?.setDefenseReminder(index)
                Log.d(TAG, "设防提示: $index")
            }

            override fun getInitStatus(): Int {
                return lockTipsStatus
            }
        })

        // 后尾门开启高度
        RearRoateUIAlert.setOnProgressChangedListener(object :
            RearRoateUIAlert.onProgressChangedListener {
            @SuppressLint("SetTextI18n")
            override fun updateHudRoateUI(sbHudRoate: Int, binding: DialogAlertQRearRoateBinding) {
                binding.isbIvHudRoate.getSeekBar().setProgress(sbHudRoate)
                Log.d(TAG, "后尾门高度进度条sb: $sbHudRoate")
                binding.ivHudRoateTop.setText((sbHudRoate + 50).toString() + "%")
            }

            @SuppressLint("SetTextI18n")
            override fun onProgressChanged(
                binding: DialogAlertQRearRoateBinding?,
                seekBar: SeekBar?,
                progress: Int,
                fromUser: Boolean
            ) {
                Log.d(TAG, "后尾门高度进度条: $progress")
                sbHudRoate = progress
                viewModel!!.setHudRoate(progress)
            }

            override fun getInitStatus(): Int {
                Log.d(TAG, "后尾门高度进度条初始化: $sbHudRoate")
                return sbHudRoate
            }

            override fun onConfirm(seekBar: SeekBar) {
                // 发送数据
                val progress = seekBar.progress
                viewModel!!.setHudRoate(progress)
            }
        })

        // 雨刮器灵敏度
        WiperSensUIAlert.setOnProgressChangedListener(object :
            WiperSensUIAlert.onProgressChangedListener {
            override fun setSwitch(
                binding: DialogAlertQWiperSensBinding?,
                index: Int,
                text: String
            ) {
                swWiperSens = index
                viewModel?.setWiperSens(index)
                Log.d(TAG, "雨刮器灵敏度: $text")
            }

            override fun getInitStatus(): Int {
                return swWiperSens
            }
        })

        // 副驾安全气囊
        DriveAirbagUIAlert.setOnProgressChangedListener(object :
            DriveAirbagUIAlert.onProgressChangedListener {
            override fun onSwitch(binding: DialogAlertCDriveAirbagBinding) {
                val carSpeed = viewModel?.getCarSpeed() ?: 0
                val enabled = CommonUtils.IntToBool(if (carSpeed <= 120) 1 else 0)
                binding.spvDriveAirbag.setEnabled(enabled)
            }

            override fun switchSuccess(index: Int) {
                driveBagFlag = index
                viewModel!!.setDriveAirBag(index)
                Log.d(TAG, "switchSuccess 副驾安全气囊: $index")
            }

            override fun getInitStatus(): Int {
                return driveBagFlag
            }
        })

        DriveAirbagConfirmUIAlert.setOnProgressChangedListener { alert: DriveAirbagConfirmUIAlert?, type: Int ->
            Log.d(TAG, "副驾安全气囊confirm: $type")
            driveBagFlag = type
        }

        binding?.tvCenterLockDesc?.setOnClickListener { v ->
            // 设置中控锁
            if (v.isSelected) {
                viewModel?.setCentralLocking(CarQuickControl.ButtonSts.OFF)
            } else {
                viewModel?.setCentralLocking(CarQuickControl.ButtonSts.ON)
            }
            quickHandler?.sendMessageDelayed(MessageConst.QUICK_CENTRAL_LOCKING)
        }
        binding?.tvTailLockDesc?.setOnClickListener { v ->
            // 设置车尾门
            if (v.isSelected) {
                viewModel?.setRearTailgate(CarQuickControl.SetRearTailGateSts.OFF)
            } else {
                viewModel?.setRearTailgate(CarQuickControl.SetRearTailGateSts.ON)
            }
        }

        binding?.tvRearMirrorDesc?.setOnClickListener { v ->
            // 后视镜折叠
            when (viewModel?.rearMirrorFold?.value) {
                CarQuickControl.GetRearMirrorFoldSts.FOLD -> {
                    viewModel?.setRearMirror(CarQuickControl.SetRearMirrorFoldSts.UNFOLD)
                }

                CarQuickControl.GetRearMirrorFoldSts.UNFOLD -> {
                    viewModel?.setRearMirror(CarQuickControl.SetRearMirrorFoldSts.FOLD)
                }
            }
        }

        binding?.tvWindowDesc?.setOnClickListener { v ->
            // 车窗跳转-打开车窗的二级菜单
            binding?.llTools?.visibility = View.INVISIBLE
            binding?.llToolsSubWin?.visibility = View.VISIBLE
        }

        binding?.tvCarWindowBreathable?.setOnClickListener { v ->
            // 车窗跳转-返回
            binding?.llTools?.visibility = View.VISIBLE
            binding?.llToolsSubWin?.visibility = View.INVISIBLE
        }
        binding?.tvCarWindowReback?.setOnClickListener { v ->
            // 车窗跳转-返回
            binding?.llTools?.visibility = View.VISIBLE
            binding?.llToolsSubWin?.visibility = View.INVISIBLE
        }

        binding?.tvSunshadeDesc?.setOnClickListener { v ->
            // 遮阳帘跳转-打开车窗的二级菜单
            binding?.llTools?.visibility = View.INVISIBLE
            binding?.llToolsSubSunshade?.visibility = View.VISIBLE
        }

        binding?.tvCarSunshadeReback?.setOnClickListener { v ->
            // 遮阳帘-返回
            binding?.llTools?.visibility = View.VISIBLE
            binding?.llToolsSubSunshade?.visibility = View.INVISIBLE
        }
        binding?.tvCarSunshadeRebackArrow?.setOnClickListener { v ->
            // 遮阳帘-返回
            binding?.llTools?.visibility = View.VISIBLE
            binding?.llToolsSubSunshade?.visibility = View.INVISIBLE
        }

        binding?.tvAutotailDesc?.setOnClickListener { v ->
            // 电动尾翼
            binding?.llTools?.visibility = View.INVISIBLE
            binding?.llToolsSubAutotail?.visibility = View.VISIBLE
        }

        binding?.tvCarAutotailReback?.setOnClickListener { v ->
            binding?.llTools?.visibility = View.VISIBLE
            binding?.llToolsSubAutotail?.visibility = View.INVISIBLE
        }
        binding?.tvCarAutotailRebackArrow?.setOnClickListener { v ->
            binding?.llTools?.visibility = View.VISIBLE
            binding?.llToolsSubAutotail?.visibility = View.INVISIBLE
        }

        binding?.tvCarWindowRaise?.setOnClickListener { v ->
            viewModel?.setWindowState(CarQuickControl.SetWindowSts.CLOSE)
        }

        binding?.tvCarWindowLower?.setOnClickListener { v ->
            viewModel?.setWindowState(CarQuickControl.SetWindowSts.OPEN)
        }

        binding?.tvCarWindowBreathable?.setOnClickListener { v ->
            viewModel?.setWindowState(CarQuickControl.SetWindowSts.AIR)
        }

        binding?.tvCarSunshadeFront?.setOnClickListener { v ->
            // 遮阳帘前排
            viewModel?.setSunshade(CarQuickControl.SetSunshadeSts.FRONT)
        }

        binding?.tvCarSunshadeRear?.setOnClickListener { v ->
            // 遮阳帘后排
            viewModel?.setSunshade(CarQuickControl.SetSunshadeSts.REAR)
        }

        binding?.tvWindowLockDesc?.setOnClickListener { v ->
            // 车窗锁
            when (viewModel?.windowLock?.value) {
                CarQuickControl.GetWindowLockSts.PERMIT -> {
                    viewModel?.setWindowLock(CarQuickControl.SetWindowLockSts.INHIBIT)
                }

                CarQuickControl.GetWindowLockSts.INHIBIT -> {
                    viewModel?.setWindowLock(CarQuickControl.SetWindowLockSts.PERMIT)
                }
            }
        }

        binding?.tvCarAutotailClose?.setOnClickListener { v ->
            // 电动尾门-关闭
            viewModel?.setAutoTail(CarQuickControl.SetAutoTailSts.CLOSE)
        }

        binding?.tvCarAutotail1?.setOnClickListener { v ->
            // 电动尾门-1档
            viewModel?.setAutoTail(CarQuickControl.SetAutoTailSts.OPEN_LEVEL_1)
        }

        binding?.tvCarAutotail2?.setOnClickListener { v ->
            // 电动尾门-2档
            viewModel?.setAutoTail(CarQuickControl.SetAutoTailSts.OPEN_LEVEL_2)
        }

        binding?.tvCarAutotailAuto?.setOnClickListener { v ->
            // 电动尾门-自动
            viewModel?.setAutoTail(CarQuickControl.SetAutoTailSts.AUTO)
        }

        binding?.tvSkylightLockDesc?.setOnClickListener { v ->
            // 天窗
        }

        binding?.swCloseUnlock?.setOnCheckedChangeListener { buttonView, isChecked ->
            // 感应靠近解锁
            if (isChecked) {
                viewModel?.setApproachingUnlock(CarQuickControl.SetApproachingUnlockSts.ON)
            } else {
                viewModel?.setApproachingUnlock(CarQuickControl.SetApproachingUnlockSts.OFF)
            }
        }

        binding?.swConditionTail?.setOnCheckedChangeListener { buttonView, isChecked ->
            // 感应离车上锁
            if (isChecked) {
                viewModel?.setDepartureLocking(CarQuickControl.SetDepartureLockingSts.ON)
            } else {
                viewModel?.setDepartureLocking(CarQuickControl.SetDepartureLockingSts.OFF)
            }
        }

        binding?.swAutoWindow?.setOnCheckedChangeListener { buttonView, isChecked ->
            // 自动锁车升窗
            if (isChecked) {
                viewModel?.setLockAutoRaiseWindow(CarQuickControl.SetLockAutoRaiseWindowSts.ON)
            } else {
                viewModel?.setLockAutoRaiseWindow(CarQuickControl.SetLockAutoRaiseWindowSts.OFF)
            }
        }

        binding?.swLockcarSunroofShade?.setOnCheckedChangeListener { buttonView, isChecked ->
            // 锁车收起遮阳帘
            if (isChecked) {
                viewModel?.setLockCarSunRoofShade(CarQuickControl.SetLockCarSunRoofShadeCloseSts.OFF)
            } else {
                viewModel?.setLockCarSunRoofShade(CarQuickControl.SetLockCarSunRoofShadeCloseSts.ON)
            }
        }

        binding?.swAutoLock?.setOnCheckedChangeListener { buttonView, isChecked ->
            // 自动落锁
            if (isChecked) {
                viewModel?.setAutomaticLocking(CarQuickControl.SetAutoLockSts.AUTOLOCK_MODE)
            } else {
                viewModel?.setAutomaticLocking(CarQuickControl.SetAutoLockSts.NOT_AUTOLOCK_MODE)
            }
        }

        binding?.swAutoUnlock?.setOnCheckedChangeListener { buttonView, isChecked ->
            // 驻车自动解锁
            if (isChecked) {
                viewModel?.setAutomaticParkingUnlock(CarQuickControl.SetAutomaticParkUnlockSts.ENABLE)
            } else {
                viewModel?.setAutomaticParkingUnlock(CarQuickControl.SetAutomaticParkUnlockSts.DISABLE)
            }
        }

        binding?.swConditionSeatWriting?.setOnCheckedChangeListener { buttonView, isChecked ->
            // 座椅便携
            if (isChecked) {
                viewModel?.setSeatPortable(CarQuickControl.SetSeatPortableSts.ON)
            } else {
                viewModel?.setSeatPortable(CarQuickControl.SetSeatPortableSts.OFF)
            }
        }

        binding?.swRefuelSmallDoor?.setOnCheckedChangeListener { buttonView, isChecked ->
            // 加油小门
            if (isChecked) {
                viewModel?.setRefuelSmallDoor(CarQuickControl.SetRefuelSmallDoorSts.REQUEST)
            } else {
                viewModel?.setRefuelSmallDoor(CarQuickControl.SetRefuelSmallDoorSts.NO_REQUEST)
            }
        }

        binding?.rlSafe?.setOnClickListener { v ->
            // 副驾安全气囊
            Log.d(TAG, "副驾安全气囊set")
            openDriveAirbag()
        }

        binding?.ivSafeTips?.setOnClickListener { v ->
            // 副驾安全气囊tips
            Log.d(TAG, "副驾安全气囊tips")
            openTipsDialog(
                getString(R.string.str_carsetting_safe_1),
                getString(R.string.str_carsetting_safe_7),
                1176,
                488
            )
        }

        binding?.rlLockTips?.setOnClickListener { v ->
            // 设防提示
            openLockTips()
        }

        binding?.rlChildLock?.setOnClickListener { v ->
            // 儿童锁
            openChildLock()
        }


        binding?.rlRearMirror?.setOnClickListener { v ->
            // 发送方向盘后视镜调节信号
            viewModel?.sendMirrorAdjust()
            backRearAdjustStatus =
                viewModel?.backAutoRearMirrorAdjustUIState(viewModel?.backRearAdjust?.value)!!
            viewModel?.setBackAutoRearMirrorAdjust(backRearAdjustStatus)
            openRearMirror(1)
        }

        binding?.rlRearMirrorAdjust?.setOnClickListener { v ->
            adjustUIAlert = RearMirrorAdjustUIAlert.Builder(mContext)
            adjustUIAlert?.setSwRearMirrorAdjust(backRearAdjustStatus)
            adjustUIAlert?.create()?.show()
        }

        binding?.rlSteeringWhellCustom?.setOnClickListener { v ->
            val sWheelUIAlert = SteeringWheelUIAlert.Builder(mContext)
            viewModel?.getCustomButton()
            sWheelUIAlert.create().show()
        }

        binding?.rlVehiclePowerOff?.setOnClickListener { v ->
            val vehiclePowerOffUIAlert = ComfirmUIAlert.Builder(mContext)
            vehiclePowerOffUIAlert.setTitle(getString(R.string.str_vehicle_power_off))
            vehiclePowerOffUIAlert.setContent(getString(R.string.str_vehicle_power_off_hint))
            vehiclePowerOffUIAlert.setFunction("vehicle_power_off")
            vehiclePowerOffUIAlert.create(1128, 508).show()
        }

        binding?.rlHudRoate?.setOnClickListener { v ->
            openHudRoate()
        }

        binding?.rlWiperSens?.setOnClickListener { v ->
            openWiperSens()
        }
    }

    private fun openLockTips() {
        if (lockTipsUIAlert != null && lockTipsUIAlert?.isShowing == true) {
            return
        }
        if (lockTipsUIAlert == null) {
            lockTipsUIAlert = LockTipsUIAlert.Builder(mContext)
        }
        lockTipsUIAlert?.create()?.show()
    }

    private fun openChildLock() {
        if (childLockUIAlert != null && childLockUIAlert?.isShowing == true) {
            return
        }
        if (childLockUIAlert == null) {
            childLockUIAlert = ChildLockUIAlert.Builder(mContext)
        }
        childLockUIAlert?.create()?.show()
    }

    private fun openHudRoate() {
        if (rearRoateUIAlert != null && rearRoateUIAlert?.isShowing == true) {
            return
        }
        if (rearRoateUIAlert == null) {
            rearRoateUIAlert = RearRoateUIAlert.Builder(mContext)
        }
        rearRoateUIAlert?.create()?.show()
    }

    private fun openWiperSens() {
        if (wiperSensUIAlert != null && wiperSensUIAlert?.isShowing == true) {
            return
        }
        if (wiperSensUIAlert == null) {
            wiperSensUIAlert = WiperSensUIAlert.Builder(mContext)
        }
        wiperSensUIAlert?.create()?.show()
        wiperSensUIAlert?.selWiperSensTranslate(swWiperSens)
    }

    private fun openDriveAirbag() {
        if (driveAirbagUIAlert != null && driveAirbagUIAlert?.isShowing == true) {
            return
        }
        if (driveAirbagUIAlert == null) {
            driveAirbagUIAlert = DriveAirbagUIAlert.Builder(mContext)
        }
        driveAirbagUIAlert?.create()?.show()
    }

    private fun openRearMirror(status: Int) {
        if (status == 0) return
        if (rearMirrorUIAlert != null && rearMirrorUIAlert!!.isShowing) {
            return
        }
        if (rearMirrorUIAlert == null) {
            rearMirrorUIAlert = RearMirrorUIAlert.Builder(mContext)
        }
        rearMirrorUIAlert!!.create().show()
    }

    // 打开自定义提示窗口
    @SuppressLint("RtlHardcoded")
    private fun openTipsDialog(title: String, content: String, width: Int, height: Int) {
        if (detailUIAlert != null && detailUIAlert?.isShowing == true) {
            return
        }
        if (detailUIAlert == null) {
            detailUIAlert = DetailsUIAlert.Builder(mContext)
        }
        detailUIAlert?.create(title, content, width, height)?.show()
        detailUIAlert?.setTextSize(com.bitech.base.R.dimen.font_24px)
    }

    override fun initData() {
    }

    override fun onClick(v: View?) {
    }

    fun getBinding(): FragmentQuickControlBinding? {
        return binding
    }

    override fun handleSafeMessage(msg: Message?) {
        when (msg!!.what) {
//            MessageConst.QUICK_CONTROL_INIT -> initHandle()
            MessageConst.QUICK_CENTRAL_LOCKING -> viewModel?.centralLocking
//            MessageConst.QUICK_REAR_TAIL_GAGE -> rearTailGateHandle()
//            MessageConst.QUICK_REAR_MIRROR -> rearMirrorHandle()
//            MessageConst.QUICK_WINDOW -> windowHandle()
//            MessageConst.QUICK_WINDOW_LOCK -> windowLockHandle()
//            MessageConst.QUICK_SUNSHADE -> sunShadeHandle()
//            MessageConst.QUICK_AUTO_TAIL -> autoTailHandle()
//            MessageConst.QUICK_SKY_WINDOW -> skyWindowHandle()
//            MessageConst.QUICK_APPROACHING_UNLOCK -> approachingUnlockHandle()
//            MessageConst.QUICK_DEPARTURE_LOCKING -> departureLockingHandle()
//            MessageConst.QUICK_LOCK_AUTO_RAISE_WINDOW -> lockAutoRaiseWindowHandle()
//            MessageConst.QUICK_LOCK_CAR_SUNROOF_SHADE_CLOSE -> lockCarSunroofShadeHandle()
//            MessageConst.QUICK_DEFENSE_REMINDER -> defenseReminderHandle()
//            MessageConst.QUICK_LEFT_CHILD_LOCK -> leftChildLockHandle()
//            MessageConst.QUICK_RIGHT_CHILD_LOCK -> rightChildLockHandle()
//            MessageConst.QUICK_AUTOMATIC_LOCKING -> automaticLockingHandle()
//            MessageConst.QUICK_AUTOMATIC_PARKING_UNLOCK -> automaticParkingUnlockHandle()
//            MessageConst.QUICK_AUTO_REAR_MIRROR_FOLD -> autoRearMirrorFoldHandle()
//            MessageConst.QUICK_AUTO_HOT_REAR_MIRROR -> autoHotRearMirrorHandle()
//            MessageConst.QUICK_BACK_AUTO_REAR_MIRROR_ADJUSE -> backAutoRearMirrorAdjustHandle()
//            MessageConst.QUICK_SEAT_PORTABLE -> seatPortableHandle()
//            MessageConst.QUICK_REFUEL_SMALL_DOOR -> refuelSmallDoorHandle()
//            MessageConst.QUICK_HUD_ROATE -> hudRoateHandle()
//            MessageConst.QUICK_WIPERSENS -> wiperSensHandle()
//            MessageConst.QUICK_DRIVE_AIR_BAG -> driveAirBagHandle()
//            MessageConst.QUICK_REAR_SCREEN_CONTROL -> rearScreenControlHandle()
            else -> {}
        }
    }

    override fun isActive(): Boolean {
        return isActive
    }
}