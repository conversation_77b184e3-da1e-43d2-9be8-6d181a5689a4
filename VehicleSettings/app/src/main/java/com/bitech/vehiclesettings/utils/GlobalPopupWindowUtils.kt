package com.bitech.vehiclesettings.utils

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.core.content.ContextCompat
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.StatusbarChargingInfoBinding
import com.bitech.vehiclesettings.service.NewEnergyLifeCycle
import com.jeremyliao.liveeventbus.LiveEventBus

@SuppressLint("StaticFieldLeak")
object GlobalPopupWindowUtils {

    const val TAG = "GlobalPopupWindowUtils"
    const val CURRENT_NIGHT_MODE_TOPIC = "current_night_mode"
    const val CHARGING_WINDOW_WIDTH = 672;
    const val CHARGING_WINDOW_HEIGHT = 84;
    const val CHARGING_WINDOW_TYPE = 2036
    const val CHARGING_WINDOW_X = 0;
    const val CHARGING_WINDOW_Y = -660;

    const val RUNAWAY_WINDOW_WIDTH = 2560;
    const val RUNAWAY_WINDOW_HEIGHT = 1440;
    const val RUNAWAY_WINDOW_TYPE = 2027
    const val RUNAWAY_WINDOW_X = 0;
    const val RUNAWAY_WINDOW_Y = 0;


    private var windowManager: WindowManager? =
        MyApplication.getContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var thermalRunawayView: View? = null
    private var statusBarChargingInfoBinding: StatusbarChargingInfoBinding? = null

    private var thermalRunawayViewOn = false
    private var thermalRunawayViewAttached = false
    private var statusBarChargingViewOn = false
    private var statusBarChargingViewAttached = false

    //监听主题变化
    private val nightModeObserver: (Boolean) -> Unit = {
        Log.d(TAG, "nightModeObserver refreshChargingContent : $it")
        refreshChargingContent()
    }

    /**
     * 显示电池热失控弹窗
     */
    fun showThermalRunawayView() {
        Log.d(TAG, "showThermalRunawayView")
        thermalRunawayViewOn = true
        if (thermalRunawayView?.isAttachedToWindow == true) {
            Log.d(TAG, "return: thermalRunawayView isAttachedToWindow")
            return
        }
        if (thermalRunawayViewAttached) {
            Log.d(TAG, "return: thermalRunawayViewAttached")
            return
        }
        thermalRunawayViewAttached = true

        thermalRunawayView =
            LayoutInflater.from(MyApplication.getContext())
                .inflate(R.layout.view_thermal_run_away, null)
        thermalRunawayView?.setOnLongClickListener {
            hideThermalRunawayView()
            true
        }
        thermalRunawayView?.addOnAttachStateChangeListener(object :
            View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                Log.i(TAG, "onViewAttachedToWindow thermalRunawayView")
                if (!thermalRunawayViewOn && v.isAttachedToWindow) {
                    Log.w(TAG, "!thermalRunawayViewOn and remove thermalRunawayView")
                    windowManager?.removeViewImmediate(v)
                }
            }

            override fun onViewDetachedFromWindow(v: View) {
                Log.i(TAG, "onViewDetachedFromWindow thermalRunawayView")
                thermalRunawayView = null
                thermalRunawayViewOn = false
                thermalRunawayViewAttached = false

            }
        })

        val params = WindowManager.LayoutParams().apply {
            width = RUNAWAY_WINDOW_WIDTH
            height = RUNAWAY_WINDOW_HEIGHT
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            format = PixelFormat.TRANSLUCENT
            //TYPE_MAGNIFICATION_OVERLAY
            type = RUNAWAY_WINDOW_TYPE
            x = RUNAWAY_WINDOW_X
            y = RUNAWAY_WINDOW_Y
        }
        windowManager?.addView(thermalRunawayView, params)
    }


    /**
     * 隐藏电池热失控弹窗
     */
    fun hideThermalRunawayView() {
        thermalRunawayViewOn = false
        if (windowManager != null && thermalRunawayView != null && thermalRunawayView?.isAttachedToWindow == true) {
            windowManager?.removeViewImmediate(thermalRunawayView)
        }
    }

    /**
     * 显示状态栏充放电信息
     */
    fun showChargingView() {
        Log.d(TAG, "showChargingView")
        statusBarChargingViewOn = true
        if (statusBarChargingInfoBinding?.root?.isAttachedToWindow == true) {
            Log.d(TAG, "return: chargingView isAttachedToWindow")
            return
        }
        if (statusBarChargingViewAttached) {
            Log.d(TAG, "return: statusBarChargingViewAttached")
            return
        }
        statusBarChargingViewAttached = true

        statusBarChargingInfoBinding = StatusbarChargingInfoBinding.bind(
            LayoutInflater.from(MyApplication.getContext())
                .inflate(R.layout.statusbar_charging_info, null)
        )


        statusBarChargingInfoBinding?.root?.addOnAttachStateChangeListener(object :
            View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                Log.i(TAG, "onViewAttachedToWindow statusBarChargingInfoBinding")
                if (!statusBarChargingViewOn && v.isAttachedToWindow) {
                    windowManager?.removeViewImmediate(v)
                } else {
                    //监听主题变化
                    LiveEventBus.get(CURRENT_NIGHT_MODE_TOPIC, Boolean::class.java).observeForever(
                        nightModeObserver
                    )
                }
            }

            override fun onViewDetachedFromWindow(v: View) {
                Log.i(TAG, "onViewDetachedFromWindow statusBarChargingInfoBinding")

                LiveEventBus.get(CURRENT_NIGHT_MODE_TOPIC, Boolean::class.java)
                    .removeObserver(nightModeObserver)
                statusBarChargingInfoBinding = null
                statusBarChargingViewOn = false
                statusBarChargingViewAttached = false
            }
        })
        setChargeViewContent()

        val params = WindowManager.LayoutParams().apply {
            width = CHARGING_WINDOW_WIDTH
            height = CHARGING_WINDOW_HEIGHT
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            format = PixelFormat.TRANSLUCENT
            //TYPE_MAGNIFICATION_OVERLAY
            type = CHARGING_WINDOW_TYPE
            x = CHARGING_WINDOW_X
            y = CHARGING_WINDOW_Y
        }
        windowManager?.addView(statusBarChargingInfoBinding?.root, params)
    }

    private fun setChargeViewContent() {
        val currentSoc = NewEnergyLifeCycle.mCurrentSOC.get()
        //获取当前窗口宽度
        val totalWidth = CHARGING_WINDOW_WIDTH
        val backgroundWidth = (currentSoc.toFloat() / 100 * totalWidth).coerceAtLeast(0f).toInt()
        statusBarChargingInfoBinding?.ivBatteryBackground?.apply {
            layoutParams.width = backgroundWidth
            requestLayout()
        }
        if (NewEnergyLifeCycle.chargingStatus.get()) {
            statusBarChargingInfoBinding?.root?.setCardBackgroundColor(
                ContextCompat.getColor(
                    MyApplication.getContext(),
                    R.color.status_bar_charging_background
                )
            )
            statusBarChargingInfoBinding?.tvBatteryStatus?.text =
                if (currentSoc < 100) MyApplication.getContext().resources.getString(R.string.ne_charging) else MyApplication.getContext().resources.getString(
                    R.string.ne_charge_completed
                )
            statusBarChargingInfoBinding?.tvBatteryStatus?.setTextColor(
                ContextCompat.getColor(MyApplication.getContext(), R.color.text_status_bar_charging)
            )
            statusBarChargingInfoBinding?.ivBatteryBackground?.background =
                ContextCompat.getDrawable(
                    MyApplication.getContext(),
                    R.drawable.bg_status_bar_charging
                )

        } else if (NewEnergyLifeCycle.dischargingStatus.get()) {
            statusBarChargingInfoBinding?.root?.setCardBackgroundColor(
                ContextCompat.getColor(
                    MyApplication.getContext(),
                    R.color.status_bar_discharging_background
                )
            )

            statusBarChargingInfoBinding?.tvBatteryStatus?.text =
                MyApplication.getContext().resources.getString(R.string.ne_discharging)

            statusBarChargingInfoBinding?.tvBatteryStatus?.setTextColor(
                ContextCompat.getColor(MyApplication.getContext(), R.color.text_status_bar_charging)
            )
            statusBarChargingInfoBinding?.root?.setCardBackgroundColor(
                ContextCompat.getColor(
                    MyApplication.getContext(),
                    R.color.status_bar_discharging_background
                )
            )
            statusBarChargingInfoBinding?.ivBatteryBackground?.background =
                ContextCompat.getDrawable(
                    MyApplication.getContext(),
                    R.drawable.bg_status_bar_discharging
                )
        } else {
            statusBarChargingInfoBinding?.tvBatteryStatus?.text = ""
            statusBarChargingInfoBinding?.ivBatteryBackground?.background =
                ContextCompat.getDrawable(
                    MyApplication.getContext(),
                    R.drawable.bg_status_bar_charging
                )
        }

        statusBarChargingInfoBinding?.tvBatteryLevel?.text = currentSoc.toString() + "%"
        statusBarChargingInfoBinding?.tvBatteryLevel?.setTextColor(
            ContextCompat.getColor(MyApplication.getContext(), R.color.text_status_bar_charging)
        )
        statusBarChargingInfoBinding?.ivBatteryStatus?.setImageResource(
            if (NewEnergyLifeCycle.dischargingStatus.get()) {
                R.mipmap.ic_status_bar_discharging
            } else {
                R.mipmap.ic_status_bar_charging
            }
        )
    }

    fun refreshChargingContent() {
        Log.d(TAG, "refreshChargingContent")
        if (windowManager != null && statusBarChargingInfoBinding != null
            && statusBarChargingInfoBinding?.root?.isAttachedToWindow == true
        ) {
            setChargeViewContent()
        }
    }

    /**
     * 隐藏状态栏充放电信息
     */
    fun hideChargingView() {
        Log.d(TAG, "hideChargingView")
        statusBarChargingViewOn = false
        if (windowManager != null && statusBarChargingInfoBinding != null
            && statusBarChargingInfoBinding?.root?.isAttachedToWindow == true
        ) {
            windowManager?.removeViewImmediate(statusBarChargingInfoBinding?.root)
        }
    }

}
