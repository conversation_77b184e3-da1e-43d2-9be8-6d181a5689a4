package com.bitech.vehiclesettings.viewmodel;

import android.content.Context;
import android.content.res.ColorStateList;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.bitech.vehiclesettings.R;

public class ColorViewModel extends ViewModel {
    private final MutableLiveData<ColorStateList> dynamicColorStateList = new MutableLiveData<>();

    // 初始化时传入上下文，用于加载颜色
    public void init(Context context) {
        if (dynamicColorStateList.getValue() == null) {
            ColorStateList defaultColor = ContextCompat.getColorStateList(context, R.color.system_color_blue);
            dynamicColorStateList.postValue(defaultColor);
        }
    }

    public LiveData<ColorStateList> getDynamicColorStateList() {
        return dynamicColorStateList;
    }

    public void updateColor(Context context, int colorResId) {
        ColorStateList color = ContextCompat.getColorStateList(context, colorResId);
        dynamicColorStateList.postValue(color);
    }
}
