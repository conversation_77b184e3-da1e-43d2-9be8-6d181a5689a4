package com.bitech.vehiclesettings.view.voice;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class ResetVoiceUIAlert extends BaseDialog {
    private static final String TAG = ResetVoiceUIAlert.class.getSimpleName();

    public ResetVoiceUIAlert(Context context) {
        super(context);
    }

    public ResetVoiceUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected ResetVoiceUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder {
        public interface OnDialogClickListener {
            void onConfirm();

            void onCancel();
        }

        public void dismiss() {
            if (dialog != null) {
                dialog.dismiss();
            }
        }

        private final Context context;
        private boolean isCan = true;
        private ResetVoiceUIAlert dialog = null;
        private View layout;
        private OnDialogClickListener onDialogClickListener;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public Builder setOnDialogClickListener(OnDialogClickListener listener) {
            this.onDialogClickListener = listener;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public ResetVoiceUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new ResetVoiceUIAlert(context, R.style.Dialog);
            layout = View.inflate(context, R.layout.dialog_alert_voice_reset, null);

            Button confirmBtn = layout.findViewById(R.id.btn_confirm);
            confirmBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onDialogClickListener != null) {
                        onDialogClickListener.onConfirm();
                    }
                    dialog.dismiss();
                }
            });
            Button cancelBtn = layout.findViewById(R.id.btn_cancel);
            cancelBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onDialogClickListener != null) {
                        onDialogClickListener.onCancel();
                    }
                    dialog.dismiss();
                }
            });
            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);
//            dialog.setCanceledOnTouchOutside(false);
//            dialog.setCancelable(false);
            return dialog;
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

}
