package com.bitech.vehiclesettings.service

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.bitech.vehiclesettings.bean.report.DataPoint
import com.bitech.vehiclesettings.utils.LogUtil
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lion.datapoint.log.LogDataInterfaceManager
import com.lion.datapoint.log.LogDataUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * @description: 数据埋点上报
 */
class DataPointReportLifeCycle(
    private val lifecycleOwner: LifecycleOwner
) : LifecycleEventObserver {

    private val dataPointObserver: (value: DataPoint) -> Unit = {
        lifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
            LogUtil.i(TAG, "dataPointObserver: $it")
            LogDataInterfaceManager.getInstances()
                .trackJsonNewValue(
                    Gson().toJson(it),
                    if (it.nodeType == 0) LogDataUtil.NODE_TYPE_ADD_OPERATION else it.nodeType
                )
            LogDataInterfaceManager.getInstances().accOffNotify()
        }
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                LogUtil.i(TAG, "onCreate: $source")
                LiveEventBus.get(KEY_DATA_POINT, DataPoint::class.java).observeForever(dataPointObserver)
            }

            Lifecycle.Event.ON_DESTROY -> {
                LogUtil.i(TAG, "onDestroy: $source")
                LiveEventBus.get(KEY_DATA_POINT, DataPoint::class.java).removeObserver(dataPointObserver)
            }

            else -> {}
        }
    }


    companion object {
        const val TAG = "DataPointReportLifeCycle"
        const val KEY_DATA_POINT = "key_data_point"
    }

}