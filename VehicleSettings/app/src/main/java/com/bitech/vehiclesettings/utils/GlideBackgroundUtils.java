package com.bitech.vehiclesettings.utils;

import android.content.Context;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestManager;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;

public class GlideBackgroundUtils {
    // 带生命周期管理的加载方法
    public static void setBackgroundSafely(RequestManager glide, ImageView view, int resourceId) {
        glide.load(resourceId)
                .apply(new RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                        .skipMemoryCache(false))
                .into(view);
    }

    // 清理资源的方法
    public static void clear(Context context, ImageView view) {
        Glide.with(context).clear(view);
    }
}
