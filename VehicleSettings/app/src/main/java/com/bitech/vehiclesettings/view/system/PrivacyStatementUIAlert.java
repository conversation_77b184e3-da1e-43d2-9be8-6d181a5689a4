package com.bitech.vehiclesettings.view.system;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.text.Html;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.adapter.PrivacyStatementAdapter;
import com.bitech.vehiclesettings.bean.PrivacyStatementBean;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSPrivacyStatementBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.common.PdfDetailsUIAlert;
import com.bitech.vehiclesettings.view.common.WebDetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.List;

public class PrivacyStatementUIAlert extends BaseDialog {
    private static final String TAG = PrivacyStatementUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;
    public static boolean isShow = false;

    public PrivacyStatementUIAlert(@NonNull Context context) {
        super(context);
    }

    public PrivacyStatementUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PrivacyStatementUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static PrivacyStatementUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        PrivacyStatementUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSPrivacyStatementBinding binding;


        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        public PrivacyStatementUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public PrivacyStatementUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PrivacyStatementUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSPrivacyStatementBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 964;
            window.setAttributes(layoutParams);

            // 初始化内容
            initItem();

            return dialog;
        }

        private boolean isDialogShowing = false; // 类成员变量

        @SuppressLint("RtlHardcoded")
        private void initItem() {
            List<PrivacyStatementBean> privacyStatementList = onProgressChangedListener.getPrivacyStatementList();
            PrivacyStatementAdapter adapter = new PrivacyStatementAdapter(context, privacyStatementList);
            adapter.setItemWidth(936);
            adapter.setItemHeight(160);
//            adapter.setTextSizePx(com.bitech.base.R.dimen.font_30px);
            Log.d(TAG, "initItem: " + adapter.getItemCount());

            adapter.setOnItemClickListener((item, position) -> {
                if (!isDialogShowing) {
                    isDialogShowing = true;

                    PdfDetailsUIAlert.Builder detailsUIAlert = new PdfDetailsUIAlert.Builder(context);
                    Dialog dialog = detailsUIAlert.create(item.getDialogTitle(), item.getDialogContent(), 1912, 1080);

                    dialog.setOnDismissListener(d -> {
                        isDialogShowing = false; // 对话框关闭时重置标志位
                        this.dialog.show();
                    });

//                    detailsUIAlert.setPadding(100);
                    detailsUIAlert.setScrollable(true);
                    dialog.show(); // 最后再显示
                }
            });

            binding.rvPermissions.setLayoutManager(new LinearLayoutManager(context));
            binding.rvPermissions.setAdapter(adapter);
        }


        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        List<PrivacyStatementBean> getPrivacyStatementList();
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }
}
