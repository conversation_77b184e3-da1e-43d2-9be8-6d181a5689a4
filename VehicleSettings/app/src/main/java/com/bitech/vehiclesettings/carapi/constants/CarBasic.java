package com.bitech.vehiclesettings.carapi.constants;

public class CarBasic {

    /**
     * 外后视镜自动折叠
     */
    public static class DVD_SET_AutoFoldSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int AUTO_FOLD_MODE = 0x1;
        public static final int NOT_AUTO_FOLD_MODE = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 外后视镜自动折叠
     */
    public static class AutoFoldSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int AUTO_FOLD_MODE = 0x1;
        public static final int NOT_AUTO_FOLD_MODE = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 倒车时后视镜自动调节设置.
     */
    public static class DVD_Set_ReverseExtMirrorSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ONLY_RIGHT_SIDE = 0x2;
        public static final int ONLY_LEFT_SIDE = 0x3;
        public static final int BOTH_SIDE = 0x4;
        public static final int NOT_USED = 0x5;
    }

    /**
     * 倒车时后视镜自动调节回调.
     */
    public static class RvsExtMirrFbSts {
        public static final int OFF = 0x0;
        public static final int ONLY_RIGHT_SIDE = 0x1;
        public static final int ONLY_LEFT_SIDE = 0x2;
        public static final int BOTH_SIDE = 0x3;
    }

    /**
     * 设防提示
     */
    public static class DVD_SET_RemoteLockFeedback {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LAMP_AND_SPEAKER = 0x1;
        public static final int ONLY_LAMP = 0x2;
        public static final int ONLY_SPEAKER_RESERVED = 0x3;
        /**
         * 0x4-0x7 不使用
         */
        public static final int NOT_USED = 0x7;
    }

    /**
     * 设防提示
     */
    public static class RemoteLockFeedbackSts {
        public static final int LAMP_AND_SPEAKER = 0x0;
        public static final int ONLY_LAMP = 0x1;
        public static final int ONLY_SPEAKER_RESERVED = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 无线充电
     */
    public static class DVD_SET_CWC_WorkingSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int INVALID = 0x3;
    }

    /**
     * 无线充电
     */
    public static class CWC_workingSts {
        public static final int CWC_OFF = 0x0;
        public static final int CWC_ON = 0x1;
    }

    /**
     * 无线充电充电状态
     */
    public static class CWC_ChargingSts {
        public static final int NO_CHARGING = 0x0;
        public static final int CHARGING = 0x1;
        public static final int CHARGING_FINISH = 0x2;
        public static final int CHARGING_FAULT = 0x3;
    }

    /**
     * 手机遗忘提醒
     */
    public static class DVD_SET_CWC_PhoneForgottenFunSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int INVALID = 0x3;
    }

    /**
     * 手机遗忘提醒开关状态
     */
    public static class CWC_PhoneForgotten_ON_OFF_Sts {
        public static final int FUNCTION_OFF = 0x0;
        public static final int FUNCTION_ON = 0x1;
    }

    /**
     * 手机遗忘提醒
     */
    public static class CWC_PhoneForgottenMsg {
        public static final int NOT_FORGOTTEN = 0x0;
        public static final int FORGOTTEN = 0x1;
    }

    /**
     * 启停开关
     */
    public static class IHU_ISS_Switch {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 启停开关
     */
    public static class SSMStatus {
        public static final int RESET_NOT_IMPLEMENTED = 0x0;
        public static final int STANDBY = 0x1;
        public static final int STOPPED = 0x2;
        public static final int STARTER_RESTART = 0x3;
        public static final int ENGINE_RESTART = 0x4;
        public static final int OPERATION = 0x5;
        public static final int AUTO_STOPPING = 0x6;
    }

    /**
     * 钥匙状态
     */
    public static class BCM_4_KeySts {
        public static final int OFF = 0x0;
        public static final int ACC = 0x1;
        public static final int ON = 0x2;
        public static final int CRANK_ON = 0x3;
    }

    /**
     * 天窗信号
     */
    public static class SRFOperateSts {
        public static final int STARTUP = 0x0;
        public static final int TILT = 0x1;
        public static final int CLOSE = 0x2;
        public static final int OPENING = 0x3;
        public static final int STOP = 0x4;
        public static final int CLOSING = 0x5;
        public static final int OPEN = 0x6;
        public static final int COMFORT = 0x7;
    }

    /**
     * 仪表背光等级设置
     */
    public static class IHU_SET_ICMBrightnessLevel {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LEVEL1 = 0x1;
        public static final int LEVEL2 = 0x2;
        public static final int LEVEL3 = 0x3;
        public static final int LEVEL4 = 0x4;
        public static final int LEVEL5 = 0x5;
        public static final int LEVEL6 = 0x6;
        public static final int LEVEL7 = 0x7;
        public static final int LEVEL8 = 0x8;
        public static final int LEVEL9 = 0x9;
        public static final int LEVEL10 = 0xA;
    }

    /**
     * 仪表背光等级回调
     */
    public static class ICM_BrightnessLevel {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LEVEL1 = 0x1;
        public static final int LEVEL2 = 0x2;
        public static final int LEVEL3 = 0x3;
        public static final int LEVEL4 = 0x4;
        public static final int LEVEL5 = 0x5;
        public static final int LEVEL6 = 0x6;
        public static final int LEVEL7 = 0x7;
        public static final int LEVEL8 = 0x8;
        public static final int LEVEL9 = 0x9;
        public static final int LEVEL10 = 0xA;
    }

    /**
     * 仪表背光等级回调
     */
    public static class VehicleSpeedVSOSigValid {
        public static final int VAlID = 0x0;
        public static final int NOT_VALID = 0x1;
    }

    /**
     * ILLU状态.
     */
    public static class DayNightModeLight {
        public static final int CLOSE = 0x0;
        public static final int OPEN = 0x1;
    }

    /**
     * 小灯信号状态.
     */
    public static class BCM_4_ParkLightSts {
        public static final int OFF = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 光线传感器状态
     */
    public static class LightDetectedSts {
        public static final int DARK = 0x0;
        public static final int BRIGHT = 0x1;
    }

    /**
     * 电动尾门开关设置
     */
    public static class DVD_TrunkCmd {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OPEN = 0x1;
        public static final int CLOSE = 0x2;
        public static final int NOT_USED = 0x0;
    }

    /**
     * 电动尾门开关回调
     */
    public static class BCM_4_TrunkSts {
        public static final int CLOSE = 0x0;
        public static final int OPEN = 0x1;
    }

    /**
     * 天窗设置
     */
    public static class IHU_SRFCmd {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OPEN = 0x1;
        public static final int CLOSE = 0x2;
        public static final int TILT = 0x3;
    }

    /**
     * 天窗状态
     */
    public static class SRF_OperateSts {
        public static final int STARTUP = 0x0;
        public static final int TILT = 0x1;
        public static final int CLOSE = 0x2;
        public static final int OPENING = 0x3;
        public static final int STOP = 0x4;
        public static final int CLOSING = 0x5;
        public static final int OPEN = 0x6;
        public static final int COMFORT_OPEN = 0x7;
    }

    /**
     * 全景天窗百分比位置
     */
    public static class SRF_PositionSts {
        public static final int STARTUP = 0x0;
        public static final int PERSENT0 = 0x1;
        public static final int PERSENT10 = 0x2;
        public static final int PERSENT20 = 0x3;
        public static final int PERSENT30 = 0x4;
        public static final int PERSENT40 = 0x5;
        public static final int PERSENT50 = 0x6;
        public static final int PERSENT60 = 0x7;
        public static final int PERSENT70 = 0x8;
        public static final int PERSENT80 = 0x9;
        public static final int PERSENT90 = 0xA;
        public static final int PERSENT100 = 0xB;
        public static final int TILT = 0xC;
    }

    /**
     * 全景天窗百分比设置
     */
    public static class IHU_PSRFCmd {
        public static final int NOT_ACITIVE = 0x0;
        public static final int PERSENT0 = 0x1;
        public static final int PERSENT10 = 0x2;
        public static final int PERSENT20 = 0x3;
        public static final int PERSENT30 = 0x4;
        public static final int PERSENT40 = 0x5;
        public static final int PERSENT50 = 0x6;
        public static final int PERSENT60 = 0x7;
        public static final int PERSENT70 = 0x8;
        public static final int PERSENT80 = 0x9;
        public static final int PERSENT90 = 0xA;
        public static final int PERSENT100 = 0xB;
        public static final int TILT = 0xC;
    }


    /**
     * 车窗设置
     */
    public static class IHU_WindowCmd {
        public static final int NOT_ACTIVE = 0x00;
        public static final int FD_WINDOW_OPEN = 0x01;
        public static final int FD_WINDOW_CLOSE = 0x02;
        public static final int FP_WINDOW_OPEN = 0x03;
        public static final int FP_WINDOW_CLOSE = 0x04;
        public static final int RL_WINDOW_OPEN = 0x05;
        public static final int RL_WINDOW_CLOSE = 0x06;
        public static final int RR_WINDOW_OPEN = 0x07;
        public static final int RR_WINDOW_CLOSE = 0x08;
        public static final int FRONT_WINDOWS_OPEN = 0x09;
        public static final int FRONT_WINDOWS_CLOSE = 0x0a;
        public static final int REAR_WINDOWS_OPEN = 0x0b;
        public static final int REAR_WINDOWS_CLOSE = 0x0c;
        public static final int ALL_WINDOWS_OPEN = 0x0d;
        public static final int ALL_WINDOWS_CLOSE = 0x0e;
        public static final int ALL_WINDOWS_COMFORT = 0x0f;
    }

    /**
     * 车窗位置
     */
    public static class WindowPosition {
        public static final int FD = 0x00;
        public static final int FP = 0x01;
        public static final int RL = 0x02;
        public static final int RR = 0x03;
    }

    /**
     * 车窗状态
     */
    public static class Window_Sts {
        public static final int STARTUP = 0x0;
        public static final int COMFORT = 0x1;
        public static final int CLOSE = 0x2;
        public static final int OPEN = 0x3;
        public static final int STOP = 0x4;
        public static final int CLOSING = 0x5;
        public static final int OPENING = 0x6;
    }

    /**
     * 主题设置
     */
    public static class DVD_SetThemeType {
        public static final int NOT_ACTIVE = 0x00;
        public static final int THEME1 = 0x03;
        public static final int THEME2 = 0x01;
        public static final int THEME3 = 0x02;
    }

    /**
     * 12/24小时制设置
     */
    public static class Set_Time24H12H {
        public static final int NOT_ACTIVE = 0x00;
        public static final int TIME_24H = 0x01;
        public static final int TIME_12H = 0x02;
    }

    /**
     * 主题设置
     */
    public static class ScreenOverTemperatureSts {
        public static final int NORMAL = 0x00;
        public static final int OVER_TEMPERATURE = 0x01;
    }

    /**
     *
     */
    public static class CtpKeyVoiceRemindSts {
        public static final int NOT_ACTIVE = 0x00;
        public static final int ACTIVE = 0x01;
    }

    /**
     * 交通拥堵辅助/集成式巡航辅助
     */
    public static class TJA_ICA_ON_OFF {
        public static final int NOT_ACTIVE = 0x0;
        public static final int TJA_ICA_OFF = 0x1;
        public static final int TJA_ICA_ON = 0x2;
    }

    /**
     * 交通拥堵辅助/集成式巡航辅助(回调)
     */
    public static class TJA_ICA_ON_OFF_sts {
        public static final int TJA_ICA_OFF = 0x0;
        public static final int TJA_ICA_ON = 0x1;
    }

    /**
     * 自适应巡航开关
     */
    public static class TimeGapLastSetReq {
        public static final int TIME_GAP_OFF = 0x0;
        public static final int TIME_GAP_ON = 0x1;
    }

    /**
     * 自适应巡航开关(回调)
     */
    public static class TimeGapLastSet_DVD {
        public static final int TIME_GAP_OFF = 0x0;
        public static final int TIME_GAP_ON = 0x1;
    }

    /**
     * 自适应巡航设置
     */
    public static class TimeGapSet1Req {
        public static final int NOT_ACTIVE = 0x0;
        public static final int TIME_GAP_0 = 0x1;
        public static final int TIME_GAP_1 = 0x2;
        public static final int TIME_GAP_2 = 0x3;
    }

    /**
     * 自适应巡航设置(回调)
     */
    public static class TimeGapSet_DVD {
        public static final int TIME_GAP_0 = 0x0;
        public static final int TIME_GAP_1 = 0x1;
        public static final int TIME_GAP_2 = 0x2;
    }

    /**
     * 手机蓝牙无钥匙进入(写)
     */
    public static class DVD_SET_BLTKey_PESts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 手机蓝牙无钥匙进入(回调)
     */
    public static class PEPS_BLTKey_PESts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 距离报警系统(写)
     */
    public static class DistanceWarning_on_off {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 距离报警系统(回调)
     */
    public static class DistanceWarning_on_off_sts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 自动紧急制动系统 T1E
     */
    public static class AEB_ON_OFF {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 自动紧急制动系统 T1E
     */
    public static class AEB_ON_OFF_sts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * DMS报警开关（写）
     */
    public static class IHU_12_DMSSwtSet {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * DMS报警开关（回调）
     */
    public static class DMS_1_AlrmSwtSt {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * 主驾座椅便捷进出（写）
     */
    public static class DVD_Set_EasyEntrySts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 主驾座椅便捷进出（回调）
     */
    public static class EasyEntryFeedbackSts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 位置记忆与钥匙绑定（写）
     */
    public static class DVD_SetSCURelateKeyOrFaceID {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int ON = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 位置记忆与钥匙绑定（回调）
     */
    public static class SCURelateKeyOrFaceIDFeedbackSts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 智能钥匙ID（回调）
     */
    public static class IDInformation {
        public static final int Transmitter_1 = 0x1;
        public static final int Transmitter_2 = 0x2;
        public static final int Transmitter_3 = 0x3;
        public static final int Transmitter_4 = 0x4;
    }

    /**
     * 选择您想要的座椅/后视镜位置（写）
     */
    public static class Seat_ExtMirror_PosRecall_ID {
        public static final int NOT_ACTIVE = 0x0;
        public static final int seat_ExtMirror_Pos0_Recall_ID1 = 0x1;
        public static final int seat_ExtMirror_Pos1_Recall_ID1 = 0x2;
        public static final int seat_ExtMirror_Pos2_Recall_ID1 = 0x3;
        public static final int seat_ExtMirror_Pos0_Recall_ID2 = 0x4;
        public static final int seat_ExtMirror_Pos1_Recall_ID2 = 0x5;
        public static final int seat_ExtMirror_Pos2_Recall_ID2 = 0x6;
        public static final int seat_ExtMirror_Pos0_Recall_ID3 = 0x7;
        public static final int seat_ExtMirror_Pos1_Recall_ID3 = 0x8;
        public static final int seat_ExtMirror_Pos2_Recall_ID3 = 0x9;
        public static final int seat_ExtMirror_Pos0_Recall_ID4 = 0xA;
        public static final int seat_ExtMirror_Pos1_Recall_ID4 = 0xB;
        public static final int seat_ExtMirror_Pos2_Recall_ID4 = 0xC;
        public static final int seat_ExtMirror_Pos0_Recall_ID5 = 0xD;
        public static final int seat_ExtMirror_Pos1_Recall_ID5 = 0xE;
        public static final int seat_ExtMirror_Pos2_Recall_ID5 = 0xF;
    }

    /**
     * 选择您想要的座椅/后视镜位置（回调）
     */
    public static class Seat_ExtMirror_MemoryEnable {
        public static final int DISABLE = 0x0;
        public static final int ENABLE = 0x1;
    }

    /**
     * 请选择保持当前位置到（写）
     */
    public static class Seat_ExtMirror_PosStoreReq_ID {
        public static final int NOT_ACTIVE = 0x0;
        public static final int seat_ExtMirror_Pos0_Recall_ID1 = 0x1;
        public static final int seat_ExtMirror_Pos1_Recall_ID1 = 0x2;
        public static final int seat_ExtMirror_Pos2_Recall_ID1 = 0x3;
        public static final int seat_ExtMirror_Pos0_Recall_ID2 = 0x4;
        public static final int seat_ExtMirror_Pos1_Recall_ID2 = 0x5;
        public static final int seat_ExtMirror_Pos2_Recall_ID2 = 0x6;
        public static final int seat_ExtMirror_Pos0_Recall_ID3 = 0x7;
        public static final int seat_ExtMirror_Pos1_Recall_ID3 = 0x8;
        public static final int seat_ExtMirror_Pos2_Recall_ID3 = 0x9;
        public static final int seat_ExtMirror_Pos0_Recall_ID4 = 0xA;
        public static final int seat_ExtMirror_Pos1_Recall_ID4 = 0xB;
        public static final int seat_ExtMirror_Pos2_Recall_ID4 = 0xC;
        public static final int seat_ExtMirror_Pos0_Recall_ID5 = 0xD;
        public static final int seat_ExtMirror_Pos1_Recall_ID5 = 0xE;
        public static final int seat_ExtMirror_Pos2_Recall_ID5 = 0xF;
    }

    /**
     * 请选择保持当前位置到（回调）
     */
    public static class Seat_ExtMirror_MemoryReq {
        public static final int NO_REQUEST = 0x0;
        public static final int REQUEST = 0x1;
    }

    /**
     * 特殊驻车模式（写）
     */
    public static class IHU_SpeParkSet {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 特殊驻车模式（回调）
     */
    public static class TCU_SpeParkSts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 中控锁 设置
     */
    public static class IHU_26_CenterLockSwt {
        public static final int NOT_ACTIVE = 0x0;
        public static final int UNLOCK = 0x1;
        public static final int LOCK = 0x2;
        public static final int RESERVED = 0x2;
    }

    /**
     * 中控锁 回调
     */
    public static class BCM_4_DriverDoorLockSts {
        public static final int LOCK = 0x0;
        public static final int UNLOCK = 0x1;
    }

    /**
     * 车窗锁 设置
     */
    public static class IHU_26_WinInhbSwt {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }

    /**
     * 车窗锁 回调
     */
    public static class WindowInhibitSts {
        public static final int PERMIT = 0x0;
        public static final int INHIBIT = 0x1;
    }

    /**
     * 自动落锁
     */
    public static class DVD_SET_AutoLockSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int AUTO_LOCK_MODE = 0x1;
        public static final int NOT_AUTO_LOCK_MODE = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 自动落锁
     */
    public static class AutoLockSts {
        public static final int NOT_AUTO_LOCK_MODE = 0x0;
        public static final int AUTO_LOCK_MODE = 0x1;
    }

    /**
     * 车内儿童检测 设置
     */
    public static class IHU_CPDSwitchSet {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int NOT_USED = 0x3;
    }

    /**
     * 车内儿童检测 回调
     */
    public static class CIR_1_EnaSts {
        public static final int ENABLE = 0x0;
        public static final int DISABLE = 0x1;
        public static final int RESERVED = 0x2;
        public static final int IGNORE = 0x3;
    }

    /**
     * 雨刮器灵敏度 设置
     */
    public static class IHU_26_WiprSnvty {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LEVEL1 = 0x1;
        public static final int LEVEL2 = 0x2;
        public static final int LEVEL3 = 0x3;
        public static final int LEVEL4 = 0x4;
        public static final int RESERVED = 0x5;
    }

    /**
     * 雨刮器灵敏度 回调
     */
    public static class BCM_22_WiprSnvtySetFb {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LEVEL1 = 0x1;
        public static final int LEVEL2 = 0x2;
        public static final int LEVEL3 = 0x3;
        public static final int LEVEL4 = 0x4;
        public static final int RESERVED = 0x5;
    }

    /**
     * 雨刮维修模式设置.
     */
    public static class IHU_WiprSrvModSet {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 雨刮维修模式回调.
     */
    public static class BCM_WiprSrvModSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ACTIVE = 0x1;
    }


    /**
     * 自动雨刮状态.
     */
    public static class BCM_WiprAutoSts {
        public static final int NOT_ACTIVE = 0x1;
        public static final int ACTIVE = 0x0;
    }

    /**
     * 整车语言/Language
     */
    public static class LanguageSet {
        public static final int NOT_ACTIVE = 0x0;
        public static final int CHINESE = 0x1;
        public static final int ENGLISH = 0x2;
        public static final int RUSSIAN = 0x3;
        public static final int ARABIC = 0x4;
        public static final int PORTUGUESE = 0x5;
        public static final int FARSI = 0x6;
        public static final int SPANISH = 0x7;
        public static final int ITALIAN = 0x8;
        public static final int TURKISH = 0x9;
        public static final int UKRAINIAN = 0xa;
        public static final int THAI = 0x0b;
        public static final int INDONESIAN = 0x0c;
        public static final int GERMAN = 0xd;
        public static final int FRENCH = 0xe;
        public static final int DUTCH = 0xf;
        public static final int KAZAKH = 0x19;
        /**
         * 0xb-0x1f 不使用
         */
        public static final int NOT_USED = 0x1f;
    }

    /**
     * 制动俯仰状态设置
     */
    public static class Set_CSTFunctionSts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int INVALID = 0x3;
    }

    /**
     * 制动俯仰状态回调
     */
    public static class CST_Status {
        public static final int CLOSE = 0x0;
        public static final int STANDBY = 0x1;
        public static final int OPEN = 0x2;
        public static final int FAILURE = 0x3;
    }

    /**
     * 制动俯仰灵敏度设置
     */
    public static class CST_SensitivityReq {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LOW = 0x1;
        public static final int HIGH = 0x2;
        public static final int INVALID = 0x3;
    }

    /**
     * 制动俯灵敏度回调
     */
    public static class CST_SensitivitySts {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LOW = 0x1;
        public static final int HIGH = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * 制动感觉关联驾驶模式
     */
    public static class IHU_AssociateDriveModeSet {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * 制动感觉关联驾驶模式回调
     */
    public static class IPB_AssociateWithDriverModeSts {
        public static final int OFF = 0x0;
        public static final int ON = 0x1;
    }

    /**
     * 制动感觉模式设置
     */
    public static class BrakeFeelModeSetReq {
        public static final int NOT_ACTIVE = 0x0;
        public static final int COMFORT = 0x1;
        public static final int SPOT = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * 制动感觉模式回调
     */
    public static class BrakeFeelSts {
        public static final int COMFORT = 0x0;
        public static final int SPORT = 0x1;
        public static final int RESERVED_1 = 0x2;
        public static final int RESERVED = 0x3;
    }

    /**
     * 疲劳驾驶提醒设置.
     */
    public static class Reserved_FatigureDrivingTime {
        public static final int NOT_ACTIVE = 0x0;
        public static final int CANCEL = 0x1;
        public static final int ONE_HOUR = 0x2;
        public static final int ONE_PORT_FIVE_HOUR = 0x3;
        public static final int TWO_HOUR = 0x4;
        public static final int TWO_PORT_FIVE_HOUR = 0x5;
        public static final int THREE_HOUR = 0x6;
        public static final int THREE_PORT_FIVE_HOUR = 0x7;
        public static final int FOUR_HOUR = 0x8;
        public static final int NO_USED = 0x9;
    }

    /**
     * 疲劳驾驶警告.
     */
    public static class FatigureDrivingWaring {
        public static final int NOT_Warning = 0x0;
        public static final int Warning = 0x1;
    }

    /**
     * 疲劳驾驶提醒回调.
     */
    public static class FatigureDrivingTime {
       /* public static final int NOT_ACTIVE = 0x0;
        public static final int ONE_HOUR = 0x1;
        public static final int ONE_PORT_FIVE_HOUR = 0x2;
        public static final int TWO_HOUR = 0x3;
        public static final int TWO_PORT_FIVE_HOUR = 0x4;
        public static final int THREE_HOUR = 0x5;
        public static final int THREE_PORT_FIVE_HOUR = 0x6;
        public static final int FOUR_HOUR = 0x7;
        public static final int RESERVED = 0x8;
        public static final int RESERVED_1 = 0x9;*/

        public static final int NOT_ACTIVE = 0x0;
        public static final int CANCEL = 0x1;
        public static final int ONE_HOUR = 0x2;
        public static final int ONE_PORT_FIVE_HOUR = 0x3;
        public static final int TWO_HOUR = 0x4;
        public static final int TWO_PORT_FIVE_HOUR = 0x5;
        public static final int THREE_HOUR = 0x6;
        public static final int THREE_PORT_FIVE_HOUR = 0x7;
        public static final int FOUR_HOUR = 0x8;
        public static final int NO_USED = 0x9;
    }

    /**
     * 遮阳帘控制
     */
    public static class IHU_PSRFRCmd {
        public static final int NOT_ACTIVE = 0x0;
        public static final int OPEN_0 = 0x1;
        public static final int OPEN_10 = 0x2;
        public static final int OPEN_20 = 0x3;
        public static final int OPEN_30 = 0x4;
        public static final int OPEN_40 = 0x5;
        public static final int OPEN_50 = 0x6;
        public static final int OPEN_60 = 0x7;
        public static final int OPEN_70 = 0x8;
        public static final int OPEN_80 = 0x9;
        public static final int OPEN_90 = 0xA;
        public static final int OPEN_100 = 0xB;
    }

    /**
     * 遮阳帘控制回调
     */
    public static class BCM_22_Rool_PositionSts {
        public static final int STARTUP = 0x0;
        public static final int OPEN_0 = 0x1;
        public static final int OPEN_10 = 0x2;
        public static final int OPEN_20 = 0x3;
        public static final int OPEN_30 = 0x4;
        public static final int OPEN_40 = 0x5;
        public static final int OPEN_50 = 0x6;
        public static final int OPEN_60 = 0x7;
        public static final int OPEN_70 = 0x8;
        public static final int OPEN_80 = 0x9;
        public static final int OPEN_90 = 0xA;
        public static final int OPEN_100 = 0xB;
        public static final int TILT = 0xC;
    }

    /**
     * 后尾门开关回调
     */
    public static class PLG_OperateSts {
        public static final int STOP = 0x0;
        public static final int OPEN = 0x1;
        public static final int CLOSE = 0x2;
        public static final int OPENING = 0x3;
        public static final int CLOSING = 0x4;
    }

    /**
     * 后尾门开启高度回调
     */
    public static class PLG_Set_MaxPosition {
        public static final int DEFAULT = 100;
    }

    public static class IHU_AutHldSet {
        public static final int NOT_ACTIVE = 0x0;
        public static final int ON = 0x1;
        public static final int OFF = 0x2;
    }

    /**
     * 胎压检测系统故障状态
     */
    public static class ITPMS_SYS_MAUL_STS {
        public static final int NO_ERROR = 0x0;
        public static final int ERROR = 0x1;
    }

    /**
     * 胎压检测系统欠压报警
     */
    public static class ITPMS_WARN_STS {
        public static final int NO_WARNING = 0x0;
        public static final int WARNING = 0x1;
    }

    /**
     * 胎压检测系统欠压报警值
     */
    public static class ITPMS_WARN_VALUE {
        public static final int NO_WARNING = 0x0;//正常
        public static final int WARNING = 0x1;//胎压不足
        public static final int INCOMPATIBLE = 0x2;//轮胎不兼容
        public static final int UNKNOWN = 0x3;//忽略
        public static final int CRITICAL_PUNCTURE_WARNING = 0x4;//严重穿刺报警
        public static final int GENERAL_CRITICAL_PUNCTURE_WARNING = 0x5;//忽略
    }

    /*
     * 胎压重置是否可用信号.
     */
    public static class ITPMS_ResetValid {
        public static final int VALID = 0x0;
        public static final int INVALID = 0x1;
    }

    /**
     * 请求胎压重置信号.
     */
    public static class ITPMS_ResetRequest {
        public static final int REQUEST = 0x0;
        public static final int NO_REQUEST = 0x1;
    }

    /**
     * 胎压重置中状态反馈信号.
     */
    public static class ITPMS_ResetProgressStatus {
        public static final int NORMAL = 0x0;
        public static final int SUCCESS = 0x1;
        public static final int FAILED = 0x2;
    }

    /**
     * 胎压故障状态信号.
     */
    public static class iTPMS_SysMaulfunctionSts {
        public static final int NOT_ERROR = 0x0;
        public static final int ERROR = 0x1;
    }

    /**
     * 引擎工作状态.
     */
    public static class EngineSts {
        public static final int NOT_RUN = 0x0;
        public static final int RUN = 0x1;
    }

    /**
     * 车门状态包括四个车门，以及前仓盖
     */
    public static class DoorSts {
        public static final int CLOSE = 0x0;
        public static final int OPEN = 0x1;
    }

    /**
     * 普通充（放）电枪状态
     */
    public static class OBC_CC_ConnectSts {
        public static final int NOT_CONNECTED = 0;
        public static final int CHARGE_CONNECTED = 1;
        public static final int V2L = 2;//放电
        public static final int ERROR = 3;
        public static final int PARTIAL_CONNECTED = 4;
    }

    /**
     * 快充电枪状态
     */
    public static class BMSH_CC2_ConnectSts {
        public static final int NOT_CONNECTED = 0;
        public static final int CONNECTED = 1;
        public static final int PARTIAL_CONNECTED = 2;
        public static final int INVALID = 3;
    }

    /**
     *充电状态（慢充）
     */
    public static class BMSH_PackChargingThermalSt {
        public static final int NONE = 0;
        public static final int HEATING = 1;//电池加热中
        public static final int COOLING_AND_CHARGING = 2;//边冷却边充电
        public static final int CHARGEING = 3;//充电中
        public static final int CHARGE_FINISH = 4;//充电完成
    }

    public static class BMSH_PackchgSTS {
        public static final int NO_CHARGING = 0;
        public static final int PAKING_CHARGING = 1;//充电中
        public static final int CHARGE_COMPLETE = 2;//充电完成
        public static final int CHARGE_ABORT = 3;//充电中止
        public static final int THERMAL_CHARGING = 4;//充电加热中
    }

    public static class V2L_FUNCTION_Sts {
        public static final int OFF = 0;
        public static final int ON = 1;
    }

    public static class Signal_Sts {
        public static final int NORMAL = 0;
        public static final int LOSS = 1;
    }
    /**
     * 车窗锁状态
     */
    public static class Window_Lock {
        public static final int PERMIT = 0;
        public static final int INHIBIT = 1;
    }
    /**
     * 遮阳帘状态
     */
    public static class Sun_Shade_Sts {
        public static final int STARTUP = 0;
        public static final int RESERVED = 1;
        public static final int CLOSE = 2;
        public static final int OPENING = 3;
        public static final int STOP = 4;
        public static final int CLOSING = 5;
        public static final int OPEN = 6;
        public static final int HALF_OPEN = 7;
    }

    /**
     * 能量流（EV）
     */
    public static class VCU_EnergyFlowDis {
        public static final int NONE = 0;
        public static final int DISCHARGE = 1;
        public static final int CHARGE = 2;
        public static final int DISCHARGE_REGENERATE = 3;
        public static final int REGENERATE = 4;
    }
}
