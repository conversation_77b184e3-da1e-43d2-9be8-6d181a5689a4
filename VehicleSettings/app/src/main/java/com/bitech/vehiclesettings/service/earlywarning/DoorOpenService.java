package com.bitech.vehiclesettings.service.earlywarning;

import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.bean.atmosphere.AmbLigBean;
import com.bitech.vehiclesettings.carapi.constants.CarWarn;
import com.bitech.vehiclesettings.service.atmosphere.EffectDiapatcher;
import com.bitech.vehiclesettings.utils.BeanToStringUtil;

/***
 * 预警接口
 */
public class DoorOpenService {
    private static final String TAG = DoorOpenService.class.getSimpleName();
    private static volatile DoorOpenService instance;
    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);
    private LightInBean lInBean;

    private DoorOpenService() {
        lInBean = carServer.getLightInData();
    }

    public static DoorOpenService getInstance() {
        if (instance == null) {
            synchronized (DoorOpenService.class) {
                if (instance == null) {
                    instance = new DoorOpenService();
                }
            }
        }
        return instance;
    }

    public void fLWarn(int sign) {
        if (sign == CarWarn.DoorSts.CLOSE) {
            EffectDiapatcher.getInstance().resumeTriggered();
            return;
        }
        int brightnessIdx = lInBean.getFront().getFrontBrightness();
        int colorIdx = lInBean.getFront().getFrontColor();
        AmbLigBean ambLigBean = new AmbLigBean().setFront();
        ambLigBean.setAmbLigColorAdj(CarLight.AmbLigColorAdj.Invalid);
        ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
        carServer.setLightAmbLightCan(ambLigBean);
        Log.d(TAG, "[门开预警]fLWarn: " + BeanToStringUtil.beanToString(ambLigBean));
    }

    public void fRWarn(int sign) {
        if (sign == CarWarn.DoorSts.CLOSE) {
            EffectDiapatcher.getInstance().resumeTriggered();
            return;
        }
        int brightnessIdx = lInBean.getFront().getFrontBrightness();
        int colorIdx = lInBean.getFront().getFrontColor();
        AmbLigBean ambLigBean = new AmbLigBean().setFront();
        ambLigBean.setAmbLigColorAdj(CarLight.AmbLigColorAdj.Invalid);
        ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
        carServer.setLightAmbLightCan(ambLigBean);
        Log.d(TAG, "[门开预警]fRWarn: " + BeanToStringUtil.beanToString(ambLigBean));
    }

    public void rLWarn(int sign) {
        if (sign == CarWarn.DoorSts.CLOSE) {
            EffectDiapatcher.getInstance().resumeTriggered();
            return;
        }
        int brightnessIdx = lInBean.getFront().getFrontBrightness();
        int colorIdx = lInBean.getFront().getFrontColor();
        AmbLigBean ambLigBean = new AmbLigBean().setRear();
        ambLigBean.setAmbLigColorAdj(CarLight.AmbLigColorAdj.Invalid);
        ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
        carServer.setLightAmbLightCan(ambLigBean);
        Log.d(TAG, "[门开预警]rLWarn: " + BeanToStringUtil.beanToString(ambLigBean));
    }

    public void rRWarn(int sign) {
        if (sign == CarWarn.DoorSts.CLOSE) {
            EffectDiapatcher.getInstance().resumeTriggered();
            return;
        }
        int brightnessIdx = lInBean.getFront().getFrontBrightness();
        int colorIdx = lInBean.getFront().getFrontColor();
        AmbLigBean ambLigBean = new AmbLigBean().setRear();
        if (sign == CarWarn.DoorSts.CLOSE) {
            ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
            ambLigBean.setAmbLigColorAdj(colorIdx);
        } else {
            ambLigBean.setAmbLigBriAdj(brightnessIdx);
            ambLigBean.setAmbLigColorAdj(colorIdx);
        }
        ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
        carServer.setLightAmbLightCan(ambLigBean);
        Log.d(TAG, "[门开预警]rRWarn: " + BeanToStringUtil.beanToString(ambLigBean));
    }
}
