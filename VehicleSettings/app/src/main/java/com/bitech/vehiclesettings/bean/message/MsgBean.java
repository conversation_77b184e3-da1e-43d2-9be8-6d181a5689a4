package com.bitech.vehiclesettings.bean.message;

import java.util.HashMap;
import java.util.Map;

/**
 * 消息Bean.
 */
public class MsgBean {
    Map<String, Object> extension;
    Boolean relative = false;
    Long time;
    Boolean valid = true;
    Object value = 0;

    public MsgBean(Builder builder) {
        this.value = builder.value;
        this.time = builder.time;
    }


    public MsgBean() {
        if (extension == null) {
            extension = new HashMap<>();
        }
        time = System.currentTimeMillis();
    }

    public Map<String, Object> getExtension() {
        return extension;
    }

    public void setExtension(Map<String, Object> extension) {
        this.extension = extension;
    }

    public Boolean getRelative() {
        return relative;
    }

    public void setRelative(Boolean relative) {
        this.relative = relative;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public static class Builder {

        private long time;

        private int value = 0;
        private Extension extension;
        private Boolean relative = false;

        private Boolean valid = true;

        public Builder extension(Extension extension) {
            this.extension = extension;
            return this;
        }

        public Builder relative(Boolean relative) {
            this.relative = relative;
            return this;
        }

        public Builder valid(Boolean valid) {
            this.valid = valid;
            return this;
        }

        public Builder timestamp(long timestamp) {
            this.time = timestamp;
            return this;
        }

        public Builder value(int value) {
            this.value = value;
            return this;
        }

        public MsgBean build() {
            return new MsgBean(this);
        }
    }
}
