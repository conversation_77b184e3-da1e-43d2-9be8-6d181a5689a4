package com.bitech.vehiclesettings.carapi.constants

class Car3DModel {
    object CarControlId {
        const val LEFT_FRONT_DOOR = 101
        const val RIGHT_FRONT_DOOR = 102
        const val LEFT_REAR_DOOR = 103
        const val RIGHT_REAR_DOOR = 104
        const val HOOD = 105
        const val SUNROOF = 106
        const val TRUNK_LID = 107
        const val FUEL_CAP = 108
        const val CHARGING_PORT_COVER = 109
        const val LEFT_FRONT_WINDOW = 110
        const val RIGHT_FRONT_WINDOW = 111
        const val LEFT_REAR_WINDOW = 112
        const val RIGHT_REAR_WINDOW = 113
        const val ALL_WINDOW = 114
        const val L_MIRROR = 115
        const val R_MIRROR = 116
    }

    object CarControlName {
        const val LEFT_FRONT_DOOR = "LeftFrontDoor"
        const val RIGHT_FRONT_DOOR = "RightFrontDoor"
        const val LEFT_REAR_DOOR = "LeftRearDoor"
        const val RIGHT_REAR_DOOR = "RightRearDoor"
        const val HOOD = "Hood"
        const val SUNROOF = "Sunroof"
        const val TRUNK_LID = "TrunkLid"
        const val FUEL_CAP = "FuelCap"
        const val CHARGING_PORT_COVER = "ChargingPortCover"
        const val LEFT_FRONT_WINDOW = "LeftFrontWindow"
        const val RIGHT_FRONT_WINDOW = "RightFrontWindow"
        const val LEFT_REAR_WINDOW = "LeftRearWindow"
        const val RIGHT_REAR_WINDOW = "RightRearWindow"
        const val ALL_WINDOW = "AllWindow"
        const val L_MIRROR = "LMirror"
        const val R_MIRROR = "RMirror"
    }

    object CustomState {
        const val CLOSE = 0
        const val OPEN = 1
    }

    object WindowState {
        const val CLOSE = 0
        const val OPEN = 1
        const val BREATHABLE = 2
    }

    object RearMirrorState {
        const val NOT_FOLD = 0
        const val FOLD = 1
    }

    object DriveMode {
        const val DRIVE_MODE_ID = 401
        const val DRIVE_MODE_NAME = "DriveMode"
        const val EV = 1
        const val ECO = 2
        const val COMFORT = 3
        const val GT = 4
        const val SNOW = 5
        const val CUSTOM = 6
    }

    object CameraStatus {
        const val CAMERA_STATUS_ID = 0
        const val CAMERA_STATUS_NAME = "CameraStatus"
        const val MAIN = 1  /*默认角度*/
        const val TRUNK = 2 /*后备箱*/
        const val HOOD = 3  /*引擎盖*/
        const val DOOR = 4  /*车门*/
        const val SKYLIGHT = 5  /*遮阳帘*/
        const val SUN_ROOFS = 6 /*天窗*/
        const val REAR_MIRROR = 7   /*后视镜*/
        const val WINDOW = 8    /*车窗*/
        const val REAR_WING = 9 /*尾翼*/
        const val REFITTED = 10 /*改装空间*/
        const val CHARGE = 11   /*充电*/
        const val DISCHARGE = 12    /*放电*/
        const val WALLPAPER = 13    /*壁纸切换*/
        const val AC = 14   /*空调*/
        const val SEAT_1 = 15   /*4座*/
        const val SEAT_2 = 16   /*副座*/
        const val DRIVE = 17    /*驾驶*/
        const val CONTROL = 18  /*车控*/
        const val LIGHT_1 = 19  /*舱外大灯*/
        const val LIGHT_2 = 20  /*舱内第一排灯*/
        const val LIGHT_3 = 21  /*舱内第二排灯*/
    }

    /**
     * 车设页面切换
     */
    object ControlModelState {
        const val CONTROL_MODEL_STATE_ID = 3001
        const val CONTROL_MODEL_STATE_NAME = "ControlModelState"
        const val NONE = 0  // 默认缺省值
        const val LAUNCH = 1    // 切换到首页
        const val CONTROL = 2   // 切换到车设页面
    }

    /**
     * 充放电模式
     */
    object ChargeMode {
        const val CHARGE_MODE_ID = 701
        const val CHARGE_MODE_NAME = "ChargeMode"
        const val NONE = 0  // 默认（结束充电或放电）
        const val CHARGING = 1  // 充电
        const val DISCHARGING = 2   // 放电
    }

    /**
     * 电池电量
     */
    object BatteryPercent {
        const val BATTERY_STATE_ID = 702
        const val BATTERY_STATE_NAME = "BatteryState"
        const val PERCENT = "Percent"
    }

    /**
     * 充电功率、电流、电压
     */
    object ChargePower {
        const val CHARGE_STATE_ID = 703
        const val CHARGE_STATE_NAME = "ChargeState"
        const val POWER = "Power"
        const val CURRENT = "Current"
        const val VOLTAGE = "Voltage"
    }

    /**
     * 放电最大功率、当前功率
     */
    object DischargePower {
        const val DISCHARGE_STATE_ID = 704
        const val DISCHARGE_STATE_NAME = "DischargeState"
        const val MAX_POWER_NAME = "MaxPower"
        const val CURRENT_POWER = "CurrentPower"
    }

    /**
     * 充电剩余时间
     */
    object ChargeRemainTime {
        const val CHARGE_REMAIN_TIME_ID = 705
        const val CHARGE_REMAIN_TIME_NAME = "ChargeRemainTime"
    }

    /**
     * 3d壁纸
     */
    object WallpaperState {
        const val WALLPAPER_STATE_ID = 801
        const val WALLPAPER_STATE_NAME = "WallpaperState"
        const val MODEL = 0
        const val CAT = 1
        const val FIVE_SENSES = 2
        const val MUSIC = 3
    }

    /**
     * 音乐壁纸
     */
    object MusicWallpaper {
        const val MUSIC_WALLPAPER_ID = 601
        const val MUSIC_WALLPAPER_NAME = "MusicWallpaper"
        const val LOUDNESS = "Loudness"
        const val FREQUENCY = "Frequency"
        const val COLOR1R = "Color1R"
        const val COLOR1G = "Color1G"
        const val COLOR1B = "Color1B"
        const val COLOR1A = "Color1A"
        const val COLOR2R = "Color2R"
        const val COLOR2G = "Color2G"
        const val COLOR2B = "Color2B"
        const val COLOR2A = "Color2A"
    }

    /**
     * 车模渲染设置
     */
    object RenderState {
        const val RENDER_STATE_ID = 4001
        const val RENDER_STATE_NAME = "RenderState"
        const val DISABLE = 0
        const val ENABLE = 1
    }

    /**
     * 时光壁纸
     */
    object WeatherTime {
        const val WEATHER_TIME_ID = 501
        const val WEATHER_NAME = "Weather"
        const val TIME_NAME = "Time"
        const val WEATHER_1 = 1
        const val WEATHER_2 = 2
        const val WEATHER_3 = 3
        const val WEATHER_4 = 4
        const val TIME_1 = 1
        const val TIME_2 = 2
        const val TIME_3 = 3
        const val TIME_4 = 4
    }
}