package com.bitech.vehiclesettings.view.condition;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertConditionRoadRescueBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class RoadRescueCallUIAlert extends BaseDialog {
    private static final String TAG = RoadRescueCallUIAlert.class.getSimpleName();
    private static RoadRescueCallUIAlert.onProgressChangedListener onProgressChangedListener;


    public RoadRescueCallUIAlert(@NonNull Context context) {
        super(context);
    }

    public RoadRescueCallUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected RoadRescueCallUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static RoadRescueCallUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(RoadRescueCallUIAlert.onProgressChangedListener onProgressChangedListener) {
        RoadRescueCallUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private RoadRescueCallUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertConditionRoadRescueBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private RoadRescueCallUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public RoadRescueCallUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public RoadRescueCallUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new RoadRescueCallUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertConditionRoadRescueBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 502;
            window.setAttributes(layoutParams);
            setListener();
            return dialog;
        }

        private void setListener() {
            binding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // TODO 跳转道路救援
                    onProgressChangedListener.intentToRoadRescue();
                    dialog.dismiss();
                }
            });

            binding.tvCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void intentToRoadRescue();
    }
}
