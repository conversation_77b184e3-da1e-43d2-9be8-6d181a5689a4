package com.bitech.vehiclesettings.view.newenergy;

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import com.bitech.platformlib.interfaces.newenergy.INewEnergyListener
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy
import com.bitech.vehiclesettings.databinding.DialogCommonOptionsBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 通用的多选项弹窗
 */
class CommonOptionsDialog(
    context: Context, private val dialogType: Int, private var optionValue: Int = 0
) : BaseDialog(context), View.OnClickListener {
    private val dialogCoroutineScope = CoroutineScope(Dispatchers.Main + Job())

    //按钮点击回调接口
    private var optionsDialogCallback: OnOptionsDialogCallback? = null
    private lateinit var binding: DialogCommonOptionsBinding

    // 新能源管理对象
    private var carNewEnergyManager = NewEnergyViewModel.newEnergyManager

    // 属性监听回调
    private val carPropertyCallback: INewEnergyListener by lazy {
        object : INewEnergyListener {
            override fun onEnergyRecoveryMode(value: Int) {
                if (dialogType == TYPE_ENERGY_RECOVERY_LEVEL) {
                    binding.selectedIndex = value.coerceIn(
                        CarNewEnergy.EnergyRecoverySts.LOW_LEVEL,
                        CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL
                    ) - 2
                    optionValue = value.coerceIn(
                        CarNewEnergy.EnergyRecoverySts.LOW_LEVEL,
                        CarNewEnergy.EnergyRecoverySts.HIGH_LEVEL
                    ) - 2
                }
            }

            override fun onMileageDisplay(value: Int) {
                if (dialogType == TYPE_MILEAGE_DISPLAY) {
                    binding.selectedIndex = if (value == 2) 0 else if (value == 0) 2 else value
                }
            }

            override fun onPureElectricDisplay(value: Int) {
                if (dialogType == TYPE_PURE_ELECTRIC_DISPLAY) {
                    binding.selectedIndex = value
                }
            }

            override fun onRangeConditionDisplay(value: Int) {
                if (dialogType == TYPE_RANGE_CONDITION_DISPLAY) {
                    binding.selectedIndex =
                        if (value == 0 || value == 3) 1 else if (value == 1) 2 else 0
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate: ")
        binding =
            DataBindingUtil.inflate(layoutInflater, R.layout.dialog_common_options, null, false)
        setContentView(binding.root)
        initView()
    }

    private fun initView() {
        // 设置对话框窗口属性
        val attributes = window?.attributes
        attributes?.width = 1176
        attributes?.height = 400
        window?.attributes = attributes
        // 对话框按钮监听
        binding.tvOption1.setOnClickListener(this)
        binding.tvOption2.setOnClickListener(this)
        binding.tvOption3.setOnClickListener(this)

        when (dialogType) {
//            能量回收强度反馈状态
//                0x0:Not Active
//                0x1:Off
//                0x2:Low
//                0x3:Middle
//                0x4:High
            TYPE_ENERGY_RECOVERY_LEVEL -> {
                binding.dialogTitle = context.getString(R.string.ne_energy_recovery_level)
                binding.option1Text = context.getString(R.string.settings_down)
                binding.option2Text = context.getString(R.string.settings_medium)
                binding.option3Text = context.getString(R.string.settings_high)
                binding.selectedIndex = optionValue - 2
            }

            TYPE_MILEAGE_DISPLAY -> {
//                0：总里程
//                1：HEV
//                2：EV
                binding.dialogTitle = context.getString(R.string.ne_mileage_display)
                binding.option1Text = context.getString(R.string.ne_pure_electric_mileage)
                binding.option2Text = context.getString(R.string.ne_fuel_mileage)
                binding.option3Text = context.getString(R.string.ne_total_mileage)
                binding.selectedIndex =
                    if (optionValue == 2) 0 else if (optionValue == 0) 2 else optionValue
            }

            TYPE_PURE_ELECTRIC_DISPLAY -> {
//                0：电量百分比
//                1：电续航里程
                binding.dialogTitle = context.getString(R.string.ne_pure_electric_display)
                binding.option1Text = context.getString(R.string.ne_percentage_of_charge)
                binding.option2Text = context.getString(R.string.ne_electric_range)
                binding.selectedIndex = optionValue
            }

            TYPE_RANGE_CONDITION_DISPLAY -> {
//                0x0:Default
//                0x1:Dynamics
//                0x2:CLTC
//                0x3:WLTC 默认WLTC
                binding.dialogTitle = context.getString(R.string.ne_range_condition_display)
                binding.option1Text = "CLTC"
                binding.option2Text = "WLTC"
                binding.option3Text = context.getString(R.string.ne_dynamic)
                binding.selectedIndex =
                    if (optionValue == 0 || optionValue == 3) 1 else if (optionValue == 1) 2 else 0
            }

            else -> {
                binding.selectedIndex = optionValue
            }
        }

    }

    override fun onStart() {
        super.onStart()
        LogUtil.i(TAG, "onStart")
        isShowingDialog = dialogType;
        carNewEnergyManager.addCallback(TAG, carPropertyCallback)

    }

    override fun onStop() {
        super.onStop()
        LogUtil.d(TAG, "onStop")
        isShowingDialog = 0;
        dialogCoroutineScope.cancel()
        carNewEnergyManager.removeCallback(TAG)
    }

    override fun cancel() {
        super.cancel()
        LogUtil.d(TAG, "cancel")
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss")
        super.dismiss()
    }

    override fun onClick(view: View) {
        val preIndex = optionValue

        when (view.id) {
            R.id.tvOption1 -> {
                LogUtil.d(TAG, "onClick : tvOption1")
                binding.selectedIndex = 0
                optionsDialogCallback?.onClickOptionIndex(0)

            }

            R.id.tvOption2 -> {
                LogUtil.d(TAG, "onClick : tvOption1")
                binding.selectedIndex = 1
                optionsDialogCallback?.onClickOptionIndex(1)

            }

            R.id.tvOption3 -> {
                LogUtil.d(TAG, "onClick : tvOption1")
                binding.selectedIndex = 2
                optionsDialogCallback?.onClickOptionIndex(2)

            }

            else -> {
                LogUtil.d(TAG, "onClick : else")
            }
        }
        if (dialogType == TYPE_ENERGY_RECOVERY_LEVEL) {
            dialogCoroutineScope.launch {
                delay(2000)
                if (preIndex == optionValue) {
                    binding.selectedIndex = preIndex - 2
                    LogUtil.d(TAG, "preIndex: $preIndex optionValue: $optionValue")
                }
            }
        }
    }

    fun setDialogClickCallback(callback: OnOptionsDialogCallback) {
        optionsDialogCallback = callback
    }

    interface OnOptionsDialogCallback {
        fun onClickOptionIndex(index: Int)
    }

    companion object {

        const val TAG = "CommonOptionsDialog"

        //能量回收强度
        const val TYPE_ENERGY_RECOVERY_LEVEL = 1

        //行驶里程显示
        const val TYPE_MILEAGE_DISPLAY = 2

        //纯电续航显示
        const val TYPE_PURE_ELECTRIC_DISPLAY = 3

        //续航工况显示
        const val TYPE_RANGE_CONDITION_DISPLAY = 4

        //当前正在显示的弹窗
        var isShowingDialog = 0
    }

}