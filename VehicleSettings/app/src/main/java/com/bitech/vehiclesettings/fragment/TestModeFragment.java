package com.bitech.vehiclesettings.fragment;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import com.bitech.vehiclesettings.databinding.FragmentTestModeBinding;


public class TestModeFragment extends BaseFragment<FragmentTestModeBinding> {


    @Override
    protected FragmentTestModeBinding getLayoutResId(LayoutInflater inflater, ViewGroup container) {
        binding = FragmentTestModeBinding.inflate(inflater, container, false);
        return binding;
    }

    @Override
    protected void initView() {

    }


    @Override
    protected void setListener() {

    }

    @Override
    protected void initObserve() {

    }

    @Override
    protected void initData() {

    }
}