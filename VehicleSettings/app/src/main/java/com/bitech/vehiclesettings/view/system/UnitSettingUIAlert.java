package com.bitech.vehiclesettings.view.system;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSInstrumentUnitBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSUnitSettingBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSVerifyInfoBinding;
import com.bitech.vehiclesettings.presenter.system.SystemPresenterListener;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.Locale;

public class UnitSettingUIAlert extends BaseDialog {
    private static final String TAG = UnitSettingUIAlert.class.getSimpleName();
    private static UnitSettingUIAlert.OnProgressChangedListener onProgressChangedListener;

    public UnitSettingUIAlert(@NonNull Context context) {
        super(context);
    }

    public UnitSettingUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected UnitSettingUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static UnitSettingUIAlert.OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(UnitSettingUIAlert.OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = true;
        private DialogAlertSUnitSettingBinding binding;
        private String phoneNumber;
        private boolean isBlueOpen = false;
        private UnitSettingUIAlert dialog;
        public Builder(Context context) {
            this.context = context;
        }
        public UnitSettingUIAlert.Builder setCancelable(boolean isCancelable) {
            this.isCancelable = isCancelable;
            return this;
        }

        public UnitSettingUIAlert.Builder setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
            return this;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public UnitSettingUIAlert create() {
            dialog = new UnitSettingUIAlert(context, R.style.Dialog);
            binding = DialogAlertSUnitSettingBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1128; // 或者使用具体的像素值
                layoutParams.height = 508;
                window.setAttributes(layoutParams);
            }
            setupListeners();
            initData();
            return dialog;
        }

        private void initData() {
            int state = onProgressChangedListener.getUnitSetting();
            binding.tvUnitSettingKm.setSelected(state == 0 ? true : false);
            binding.tvUnitSettingMi.setSelected(state == 1 ? true : false);
        }

        private void setupListeners() {
            binding.tvUnitSettingKm.setOnClickListener(v -> {
                onProgressChangedListener.setUnitSetting(0);
                binding.tvUnitSettingMi.setSelected(false);
                binding.tvUnitSettingKm.setSelected(true);
            });
            binding.tvUnitSettingMi.setOnClickListener(v -> {
                onProgressChangedListener.setUnitSetting(1);
                binding.tvUnitSettingMi.setSelected(true);
                binding.tvUnitSettingKm.setSelected(false);
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void dismiss() {
        unregisterReceiver(getContext());
        super.dismiss();
    }

    private void unregisterReceiver(Context context) {
        // Implementation for unregistering receivers
    }

    public interface OnProgressChangedListener {
        int getUnitSetting();
        void setUnitSetting(int state);
    }
}
