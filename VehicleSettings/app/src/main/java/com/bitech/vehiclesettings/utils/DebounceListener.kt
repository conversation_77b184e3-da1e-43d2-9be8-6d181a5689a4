package com.bitech.vehiclesettings.utils

import android.content.res.Resources
import android.os.SystemClock
import android.util.Log
import android.view.View

/**
 * 点击防抖
 */
abstract class DebounceListener protected constructor(
    viewName: String? = "",
    interval: Long = 500L
) : View.OnClickListener {

    private var lastClickTime: Long = 0
    private var mInterval = interval
    private var mViewName = viewName

    override fun onClick(v: View) {
        if (mViewName == null) {
            try {
                mViewName = v.context.resources.getResourceEntryName(v.id)
            } catch (_: Resources.NotFoundException) {

            }
        }
        val currentTime = SystemClock.uptimeMillis()
        if (currentTime - lastClickTime > mInterval) {
            Log.i("DebounceListener", "$mViewName isClicked")
            onDebounceClick(v)
            lastClickTime = currentTime
        } else {
            Log.i("DebounceListener", "$mViewName click too fast!")
        }
    }

    /**
     * debounce click listener
     */
    protected abstract fun onDebounceClick(v: View)
}