package com.bitech.vehiclesettings.view.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.ContextThemeWrapper
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogConfirmTwoBtnBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst

/**
 * @ClassName: ConfirmDialog
 * 
 * @Date:  2024/1/22 16:37
 * @Description: 自定义按钮确认弹窗.
 **/
class ConfirmDialog(context: Context) :
    Dialog(context, R.style.dialog), View.OnClickListener {

    private lateinit var binding: DialogConfirmTwoBtnBinding
    private lateinit var confirmDialogClickCallback: OnConfirmDialogClickCallback
    private lateinit var message: String
    private var confirmText: String = context.getString(R.string.dialog_confirm_text)
    private var cancelText: String = context.getString(R.string.str_cancel)

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        val themeId = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue)
        val themedContext = ContextThemeWrapper(context, themeId)
        binding = DialogConfirmTwoBtnBinding.inflate(
            LayoutInflater.from(themedContext)
        )
        setContentView(binding.root)
        // 初始化视图
        initView()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        // 设置对话框窗口属性
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_DISPLAY_OVERLAY
        attributes?.windowAnimations = 0
        window?.attributes = attributes
        // 外部点击可关闭
        setCanceledOnTouchOutside(true)
        // 对话框按钮监听
        binding.dialogConfirmBtn.setOnClickListener(this)
        binding.dialogCancelBtn.setOnClickListener(this)
        binding.dialogTipsTv.text = message
        binding.dialogConfirmBtn.text = confirmText
        binding.dialogCancelBtn.text = cancelText
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    /**
     * 设置标题警告提示语.
     *
     * @param tips 提示信息
     */
    fun setTips(tips: String) {
        message = tips
    }

    /**
     * 设置确认按钮显示文言.
     *
     * @param tips
     */
    fun setConfirmBtnText(tips: String) {
        confirmText = tips
    }

    /**
     * 设置取消按钮显示文言.
     *
     * @param tips
     */
    fun setCancelBtnText(text: String) {
        cancelText = text
        if (::binding.isInitialized) {
            binding.dialogCancelBtn.text = text
        }
    }

    /**
     * dialog 按钮点击事件.
     *
     * @param view View
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.dialog_confirm_btn -> {
                LogUtil.d(TAG, "onClick : confirm!")
                confirmDialogClickCallback.onConfirmClick()
                dismiss()
            }

            R.id.dialog_cancel_btn -> {
                LogUtil.d(TAG, "onClick : cancel!")
                confirmDialogClickCallback.onCancelClick()
                dismiss()
            }

            else -> {
                // TODO:
            }
        }
    }

    /**
     * 设置确认按钮点击事件监听.
     *
     * @param callback
     */
    fun setDialogClickCallback(callback: OnConfirmDialogClickCallback) {
        confirmDialogClickCallback = callback
    }

    /**
     * @ClassName: OnConfirmDialogClickCallback
     * 
     * @Date:  2024/1/22 17:43
     * @Description: 确认按钮点击事件回调.
     **/
    interface OnConfirmDialogClickCallback {
        /**
         * 确认按钮被点击.
         *
         */
        fun onConfirmClick()

        /**
         * 取消按钮被点击.
         *
         */
        fun onCancelClick()
    }

    companion object {
        // 日志标志位
        private const val TAG = "ConfirmDialog"
    }
}
