package com.bitech.vehiclesettings.view.system;

import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.adapter.AccessRecordItemAdapter;
import com.bitech.vehiclesettings.adapter.AccessRecordMenuAdapter;
import com.bitech.vehiclesettings.bean.RecordItemBean;
import com.bitech.vehiclesettings.bean.RecordMenuBean;
import com.bitech.vehiclesettings.databinding.DialogAlertSAccessRecordBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSGoEngineModeBinding;
import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class GotoEngineModeUIAlert extends BaseDialog {
    private static final String TAG = GotoEngineModeUIAlert.class.getSimpleName();


    public GotoEngineModeUIAlert(@NonNull Context context) {
        super(context);
    }

    public GotoEngineModeUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected GotoEngineModeUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public interface OnDialogResultListener {
        void onDataReceived(boolean b);
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSGoEngineModeBinding binding;
        private OnDialogResultListener resultListener;
        private static final int[] CONSTANTS = {
                213518, 658035, 235657, 567534, 647825,
                234700, 127347, 648924, 733782, 553456
        };

        public Builder setOnDialogResultListener(OnDialogResultListener listener) {
            this.resultListener = listener;
            return this;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private GotoEngineModeUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }

        public GotoEngineModeUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public GotoEngineModeUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new GotoEngineModeUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSGoEngineModeBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1200;
            layoutParams.height = 500;
            window.setAttributes(layoutParams);

            binding.tvConfirm.setOnClickListener(v -> {
                if (resultListener != null) {
                    String result = binding.etInput.getText().toString();
                    Log.d(TAG, "输入的密码: " + result);
                    resultListener.onDataReceived(generatePassword().equals(result));
                }
            });
            binding.tvContent.setOnClickListener(v -> {
                SystemPresenter.getInstance().gotoEngineMode();
            });

            binding.tvCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });

            // 模态
            dialog.setCanceledOnTouchOutside(false);
            return dialog;
        }

        public static String generatePassword() {
            ;
            int x = Integer.parseInt(
                    new SimpleDateFormat("yyMMdd").format(new Date()));
            Log.d(TAG, "当前日期x: " + x);
            int lastNum = x % 10;
            int constant = CONSTANTS[lastNum];
            Log.d(TAG, "匹配常量constant: " + constant);
            int key = Math.abs(x + constant);
            String keyStr = String.format("%06d", key);
            String res = keyStr.substring(keyStr.length() - 6);
            Log.d(TAG, "生成的密码: " + res);
            // TODO 去除log
            return res; // 取最后6位
        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

}
