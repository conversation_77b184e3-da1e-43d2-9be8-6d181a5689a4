package com.bitech.vehiclesettings.presenter.light.manager;

import static com.bitech.vehiclesettings.MyApplication.getContext;

import android.provider.Settings;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.bean.atmosphere.AmbLigBean;
import com.bitech.platformlib.constants.CarLight;
import com.bitech.platformlib.manager.LightManager;

import com.bitech.platformlib.utils.AtmoLightConst;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.PrefsConst;

public class LightInManager {
    private static final String TAG = LightInManager.class.getSimpleName();
    LightManager carServer = (com.bitech.platformlib.manager.LightManager) BitechCar.getInstance().getServiceManager(getContext(), BitechCar.CAR_LIGHT_MANAGER);

    // 单例实现
    private static volatile LightInManager instance;

    public static LightInManager getInstance() {
        if (instance == null) {
            synchronized (LightInManager.class) {
                if (instance == null) {
                    instance = new LightInManager();

                }
            }
        }
        return instance;
    }

    /**
     * 氛围灯开关
     *
     * @param open 开关
     */
    public void atmosphereSwitch(int open, LightInBean lInBean) {
        if (lInBean != null) {
            Log.d(TAG, "[氛围灯开关]... ");
            effectSwitch(open, lInBean);
        }
    }


    public void effectSwitch(int open, LightInBean lInBean) {
        if (lInBean != null) {
            switch (lInBean.getLampEffect()) {
                case 0:
                    // 静态
                    Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_0);
                    break;
                case 1:
                    // 呼吸
                    Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_1);
                    break;
                case 2:
                    // 渐变
                    Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_2);
                    break;
                case 3:
                    // 音乐律动
                    Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_3);
                    // adb shell settings get global vehiclesetting_rhythm_and_cadence.
                    break;
                default:
                    Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, CommonConst.AtmosphereEffect.EFFECT_0);
                    break;
            }

        }
        OpenorClose(open);
    }

    public void OpenorClose(int open) {

        switch (open) {
            case CommonConst.AtmosphereSwitch.CLOSE:
                Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_LIGHT_SW, CommonConst.AtmosphereSwitch.CLOSE);
                break;
            case CommonConst.AtmosphereSwitch.OPEN:
                Settings.Global.putInt(getContext().getContentResolver(), PrefsConst.GlobalValue.L_LIGHT_SW, CommonConst.AtmosphereSwitch.OPEN);
                break;

        }
    }

    /**
     * 氛围灯调节
     *
     * @param briAdj   亮度
     * @param colorAdj 颜色
     */
    public void atmosphereAdj(int briAdj, int colorAdj) {
        new Thread(() -> {
            AmbLigBean ambLigBean = getFrontOrRear(CommonConst.LIGHT_SW_ALL);
            ambLigBean.setAmbLigBriAdj(briAdj);
            ambLigBean.setAmbLigColorAdj(colorAdj);
            carServer.setLightAmbLightCan(ambLigBean);
            memoryAmbLighValue(ambLigBean.getValue(), ambLigBean.getValue());
            Log.d(TAG, "[atmosphere adj]... ");
        }).start();

    }

    /**
     * 前排氛围灯调节
     *
     * @param briAdj   亮度
     * @param colorAdj 颜色
     */
    public void atmosphereFrontAdj(int briAdj, int colorAdj) {
        new Thread(() -> {
            AmbLigBean ambLigBean = getFrontOrRear(CommonConst.LIGHT_SW_FRONT);
            ambLigBean.setAmbLigBriAdj(briAdj);
            ambLigBean.setAmbLigColorAdj(colorAdj);
            carServer.setLightAmbLightCan(ambLigBean);
            memoryAmbLighFrontValue(ambLigBean.getValue());
            Log.d(TAG, "[atmosphereFrontAdj adj]... ");
        }).start();

    }

    /**
     * 后排氛围灯调节
     *
     * @param briAdj   亮度
     * @param colorAdj 颜色
     */
    public void atmosphereRearAdj(int briAdj, int colorAdj) {
        new Thread(() -> {
            AmbLigBean ambLigBean = getFrontOrRear(CommonConst.LIGHT_SW_REAR);
            ambLigBean.setAmbLigBriAdj(briAdj);
            ambLigBean.setAmbLigColorAdj(colorAdj);
            carServer.setLightAmbLightCan(ambLigBean);
            memoryAmbLighRearValue(ambLigBean.getValue());
            Log.d(TAG, "[atmosphereRearAdj adj]... ");
        }).start();

    }


    private AmbLigBean getFrontOrRear(int pos) {
        AmbLigBean ambLigBean = new AmbLigBean();
        if (CommonConst.LIGHT_SW_FRONT == pos) {
            // 2,4,5,7,8
            ambLigBean.setFront();

        } else if (CommonConst.LIGHT_SW_REAR == pos) {
            // 9
            ambLigBean.setRear();
        } else {
            ambLigBean.setAll();
        }
        return ambLigBean;
    }

    private void saveCoustomSw(int open, LightInBean lInBean) {
        if (lInBean != null) {
            lInBean.setLightSw(open);

        }
    }

    private void themeSwitch(int open, LightInBean lInBean) {
        AmbLigBean ambLigBean = getFrontOrRear(CommonConst.LIGHT_SW_ALL);
        int brightPos = lInBean.getBrightPos();
        int colorLin = lInBean.getSingleColorLin();
        if (lInBean.getColorPos() >= CommonConst.COLOR_POS_MAX) {
            switch (open) {
                case CommonConst.AtmosphereSwitch.CLOSE:
                    ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
                    ambLigBean.setAmbLigColorAdj(colorLin);
                    carServer.setLightAmbLightCan(ambLigBean);
                    break;
                case CommonConst.AtmosphereSwitch.OPEN:
                    ambLigBean.setAmbLigBriAdj(brightPos);
                    ambLigBean.setAmbLigColorAdj(colorLin);
                    carServer.setLightAmbLightCan(ambLigBean);

                    break;
            }
            memoryAmbLighValue(ambLigBean.getValue(), ambLigBean.getValue());
        } else {
            // 多色
            switch (open) {
                case CommonConst.AtmosphereSwitch.CLOSE:
                    ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
                    ambLigBean.setAmbLigColorAdj(colorLin);
                    carServer.setLightAmbLightCan(ambLigBean);
                    memoryAmbLighValue(ambLigBean.getValue(), ambLigBean.getValue());
                    break;
                case CommonConst.AtmosphereSwitch.OPEN:
                    AmbLigBean ambLigBeanFront = getFrontOrRear(CommonConst.LIGHT_SW_FRONT);
                    ambLigBeanFront.setAmbLigBriAdj(brightPos);
                    colorLin = lInBean.getMutiFrontColorLin();
                    ambLigBeanFront.setAmbLigColorAdj(colorLin);
                    carServer.setLightAmbLightCan(ambLigBeanFront);

                    AmbLigBean ambLigBeanRear = getFrontOrRear(CommonConst.LIGHT_SW_REAR);
                    ambLigBeanRear.setAmbLigBriAdj(brightPos);
                    colorLin = lInBean.getMutiRearColorLin();
                    ambLigBeanRear.setAmbLigColorAdj(colorLin);
                    carServer.setLightAmbLightCan(ambLigBeanRear);
                    memoryAmbLighValue(ambLigBeanFront.getValue(), ambLigBeanRear.getValue());
                    break;
            }
        }
        Log.d(TAG, "[主题开关]亮度: " + "brightnessIdx" + ",颜色：" + colorLin + ",值：" + ambLigBean.getValue() + ",亮度：" + brightPos);
    }


    private void frontSwitch(int open, LightInBean lInBean) {
        AmbLigBean ambLigBean = getFrontOrRear(CommonConst.LIGHT_SW_FRONT);
        int brightnessIdx = lInBean.getFront().getFrontBrightness();
        int colorIdx = lInBean.getFront().getFrontColor();
        switch (open) {
            case CommonConst.AtmosphereSwitch.CLOSE:
                ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
                ambLigBean.setAmbLigColorAdj(colorIdx);
                break;
            case CommonConst.AtmosphereSwitch.OPEN:
                ambLigBean.setAmbLigBriAdj(brightnessIdx);
                ambLigBean.setAmbLigColorAdj(colorIdx);
                break;
        }
        carServer.setLightAmbLightCan(ambLigBean);
        memoryAmbLighFrontValue(ambLigBean.getValue());
        Log.d(TAG, "[前排开关]亮度: " + "brightnessIdx" + ",颜色：" + colorIdx + ",值：" + ambLigBean.getValue());
    }

    private void rearSwitch(int open, LightInBean lInBean) {
        AmbLigBean ambLigBean = getFrontOrRear(CommonConst.LIGHT_SW_REAR);
        int brightnessIdx = lInBean.getFront().getFrontBrightness();
        int colorIdx = lInBean.getSingleColorLin();
        switch (open) {
            case CommonConst.AtmosphereSwitch.CLOSE:
                ambLigBean.setAmbLigBriAdj(CarLight.AmbLigBriAdj.LEVEL_0);
                ambLigBean.setAmbLigColorAdj(colorIdx);
                break;
            case CommonConst.AtmosphereSwitch.OPEN:
                ambLigBean.setAmbLigBriAdj(brightnessIdx);
                ambLigBean.setAmbLigColorAdj(colorIdx);
                break;
        }
        carServer.setLightAmbLightCan(ambLigBean);
        memoryAmbLighRearValue(ambLigBean.getValue());
        Log.d(TAG, "[后排开关]亮度: " + "brightnessIdx" + ",颜色：" + colorIdx + ",值：" + ambLigBean.getValue());
    }

    private void memoryAmbLighValue(long front, long rear) {
        Settings.Global.putLong(getContext().getContentResolver(), AtmoLightConst.GlobalValue.L_INTELLIGENT_WELCOME_FRONT, front);
        Settings.Global.putLong(getContext().getContentResolver(), AtmoLightConst.GlobalValue.L_INTELLIGENT_WELCOME_REAR, rear);
        carServer.saveAtmosphereLight(front, rear);
    }

    private void memoryAmbLighFrontValue(long front) {
        Settings.Global.putLong(getContext().getContentResolver(), AtmoLightConst.GlobalValue.L_INTELLIGENT_WELCOME_FRONT, front);
        long rear = Settings.Global.getLong(getContext().getContentResolver(), AtmoLightConst.GlobalValue.L_INTELLIGENT_WELCOME_REAR, -1l);
        carServer.saveAtmosphereLight(front, rear);
    }

    private void memoryAmbLighRearValue(long rear) {
        long front = Settings.Global.getLong(getContext().getContentResolver(), AtmoLightConst.GlobalValue.L_INTELLIGENT_WELCOME_FRONT, -1l);
        Settings.Global.putLong(getContext().getContentResolver(), AtmoLightConst.GlobalValue.L_INTELLIGENT_WELCOME_REAR, rear);
        carServer.saveAtmosphereLight(front, rear);
    }
}
