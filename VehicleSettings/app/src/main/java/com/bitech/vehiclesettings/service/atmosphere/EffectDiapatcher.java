package com.bitech.vehiclesettings.service.atmosphere;

import android.provider.Settings;
import android.util.Log;

import com.bitech.platformlib.BitechCar;
import com.bitech.platformlib.bean.LightInBean;
import com.bitech.platformlib.manager.LightManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.ambientlightsdk.core.Ambientlightsdk;
import com.bitech.vehiclesettings.ambientlightsdk.core.MusicUtils;
import com.bitech.vehiclesettings.service.earlywarning.WarnDispatcher;
import com.bitech.vehiclesettings.utils.CommonConst;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.OnOffEvent;

import de.greenrobot.event.EventBus;

public class EffectDiapatcher {
    private static final String TAG = EffectDiapatcher.class.getSimpleName();
    private GradientEffectService gradualChangeService;
    private BreathingEffectService breathingEffectService;
    private Ambientlightsdk ambientlightsdk;
    private static volatile EffectDiapatcher instance;
    LightManager carServer = (LightManager) BitechCar.getInstance().getServiceManager(MyApplication.getContext(), BitechCar.CAR_LIGHT_MANAGER);

    private EffectDiapatcher() {
        if (ambientlightsdk == null) {
            ambientlightsdk = new Ambientlightsdk();
        }
        MusicUtils.setCallback(AmbientLightHelper.getInstance());
    }

    public static EffectDiapatcher getInstance() {
        if (instance == null) {
            synchronized (EffectDiapatcher.class) {
                if (instance == null) {
                    instance = new EffectDiapatcher();
                }
            }
        }
        return instance;
    }


    public void cheryRhythmStart() {
        stopBreath();
        stopGradualChange();
        ambientlightsdk.initSDK();
        ambientlightsdk.startSDK();
    }

    public void gradualStart() {
        stopBreath();
        stopCheryRhythm();
        if (gradualChangeService == null) {
            gradualChangeService = new GradientEffectService();
        }
        gradualChangeService.start();

    }

    public void breathingStart() {
        // 呼吸
        stopGradualChange();
        stopCheryRhythm();
        if (breathingEffectService == null) {
            breathingEffectService = new BreathingEffectService();
        }
        breathingEffectService.start();
    }

    public void closeAll(int sw) {
        stopSendEffect();
        // 关闭氛围灯
        StaticEffectService.getInstance().setStatic(sw);
    }

    public void stopSendEffect() {
        stopBreath();
        stopGradualChange();
        stopCheryRhythm();
    }

    public void stopGradualChange() {
        if (gradualChangeService != null) {
            gradualChangeService.stop();
            gradualChangeService = null;
        }
    }

    public void stopCheryRhythm() {
        if (ambientlightsdk != null) {
            try {
                ambientlightsdk.closeSDK();
                ambientlightsdk.forceDestroy();
            } catch (Exception e) {
                Log.d(TAG, "cheryRhythmStop: " + e.getMessage());
            }

        }

    }

    public void stopBreath() {
        if (breathingEffectService != null) {
            breathingEffectService.stop();
            breathingEffectService = null;
        }
    }

    public void triggered() {
        stopSendEffect();

    }

    public void resumeTriggered() {
        handleEffectChange();
    }

    /**
     * 灯光效果设置 1静态，2呼吸，3渐变，4音乐律动
     */
    public void handleEffectChange() {
        if (WarnDispatcher.getPriority() > 0) {
            Log.d(TAG, "handleEffectChange: " + WarnDispatcher.getPriority());
            return;
        }
        try {
            int sw = Settings.Global.getInt(MyApplication.getContext().getContentResolver(), PrefsConst.GlobalValue.L_LIGHT_SW, -1);
            int effect = Settings.Global.getInt(MyApplication.getContext().getContentResolver(), PrefsConst.GlobalValue.L_RHYTHM_CADENC, -1);
            if (sw == CommonConst.AtmosphereSwitch.OPEN) {
                // 氛围灯开处理
                openEffect(effect, sw);
                Log.d(TAG, "handleEffectSettingChange 氛围灯开启: " + sw + ", effect:" + effect);
            } else {
                // 获取数据
                LightInBean bean = carServer.getLightInData();
                if (bean.getThemeMode() == CommonConst.TAB_0) {
                    this.closeAll(sw);
                    Log.d(TAG, "handleEffectSettingChange 氛围灯关闭: " + sw + ", effect:" + effect + ",主题模式：" + bean.getThemeMode());
                } else if ((bean.getFront().getFront() != CommonConst.OPEN) && (bean.getRear().getRear() != CommonConst.OPEN)) {
                    this.closeAll(sw);
                    Log.d(TAG, "handleEffectSettingChange 氛围灯关闭: " + sw + ", effect:" + effect + ",自定义模式前排：" + bean.getFront().getFront() + ",后排：" + bean.getRear().getRear());
                } else {
                    openEffect(effect, sw);
                    Log.d(TAG, "handleEffectSettingChange 氛围灯关闭 部分关闭: " + sw + ", effect:" + effect + ",自定义模式前排：" + bean.getFront().getFront() + ",后排：" + bean.getRear().getRear());
                }
            }
            StaticEffectService.getInstance().setOnOff(sw);
            EventBus.getDefault().post(new OnOffEvent(sw));
            Log.d("氛围灯SW:" + sw + ",VSettingsObserver", "Setting sw:" + sw + " ,音乐律动: " + effect);
        } catch (Exception e) {
            Log.e(TAG, "Error reading setting: " + e.getMessage());
        }
    }

    private void openEffect(int value, int sw) {
        if (value == CommonConst.AtmosphereEffect.EFFECT_3) {
            this.cheryRhythmStart();
        } else if (value == CommonConst.AtmosphereEffect.EFFECT_2) {
            this.gradualStart();
        } else if (value == CommonConst.AtmosphereEffect.EFFECT_1) {
            this.breathingStart();
        } else {
            this.closeAll(sw);
            Log.d(TAG, "onForceDestroy");
        }
    }
}
