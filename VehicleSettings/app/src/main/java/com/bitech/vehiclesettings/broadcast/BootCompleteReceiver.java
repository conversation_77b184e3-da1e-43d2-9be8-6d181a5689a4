package com.bitech.vehiclesettings.broadcast;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bitech.vehiclesettings.service.display.DisplayService;
import com.bitech.vehiclesettings.service.VehicleService;
import com.bitech.vehiclesettings.service.VoiceControlService;
import com.bitech.vehiclesettings.service.sound.VoiceService;
import com.bitech.vehiclesettings.service.system.SystemService;
import com.bitech.vehiclesettings.utils.CommonConst;

public class BootCompleteReceiver extends BroadcastReceiver {
    private static final String TAG = BootCompleteReceiver.class.getSimpleName();

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.d(TAG, "onReceive--action : " + action);
        switch (action) {
            case CommonConst.BOOT_COMPLETED_ACTION:
                // 执行自启动操作
                startService(context);
                break;
        }
    }

    private void startService(Context context) {
        Intent startIntent = new Intent(context, VehicleService.class);
        context.startForegroundService(startIntent);
        Intent startIntentVoice = new Intent(context, VoiceControlService.class);
        context.startForegroundService(startIntentVoice);
        Intent startIntentDisplay = new Intent(context, DisplayService.class);
        context.startForegroundService(startIntentDisplay);
        Intent startIntentSystem = new Intent(context, SystemService.class);
        context.startForegroundService(startIntentSystem);
        Intent startIntentSound = new Intent(context, VoiceService.class);
        context.startForegroundService(startIntentSound);
    }
}
