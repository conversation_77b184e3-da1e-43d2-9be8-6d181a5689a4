package com.bitech.vehiclesettings.view.system;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.adapter.AccessRecordItemAdapter;
import com.bitech.vehiclesettings.adapter.AccessRecordMenuAdapter;
import com.bitech.vehiclesettings.bean.RecordItemBean;
import com.bitech.vehiclesettings.bean.RecordMenuBean;
import com.bitech.vehiclesettings.databinding.DialogAlertSAccessRecordBinding;
import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class AccessRecordUIAlert extends BaseDialog {
    private static final String TAG = AccessRecordUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public AccessRecordUIAlert(@NonNull Context context) {
        super(context);
    }

    public AccessRecordUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected AccessRecordUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        AccessRecordUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSAccessRecordBinding binding;

        AccessRecordItemAdapter recordAdapter;

        SystemPresenter presenter;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        public AccessRecordUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }

        public AccessRecordUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public AccessRecordUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new AccessRecordUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSAccessRecordBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1912;
            layoutParams.height = 960;
            window.setAttributes(layoutParams);

            presenter = new SystemPresenter(context);

            // 初始化菜单 & 记录列表
            initMenu();
            initRecordList();

            return dialog;
        }

        private void initMenu() {
            List<RecordMenuBean> menuItems = Arrays.asList(
                    new RecordMenuBean(R.mipmap.ic_camera, R.mipmap.ic_camera_selected,  "摄像头权限", 1000),
                    new RecordMenuBean(R.mipmap.ic_microphone, R.mipmap.ic_microphone_selected,  "麦克风权限", 1000),
                    new RecordMenuBean(R.mipmap.ic_position, R.mipmap.ic_position_selected,  "位置权限", 1000));
            // 设置访问次数
            for (int i = 0; i < menuItems.size(); i++) {
                menuItems.get(i).setTimes(presenter.getRecordTimes(i));
            }
            AccessRecordMenuAdapter menuAdapter = new AccessRecordMenuAdapter(menuItems, position -> {
                updateRecordList(position); // 切换访问记录
            });

            binding.rvMenu.setLayoutManager(new LinearLayoutManager(context));
            binding.rvMenu.setAdapter(menuAdapter);
        }

        private void initRecordList() {
            recordAdapter = new AccessRecordItemAdapter(new ArrayList<>());
            binding.rvRecord.setLayoutManager(new LinearLayoutManager(context));
            binding.rvRecord.setAdapter(recordAdapter);
            updateRecordList(0);
        }

        private void updateRecordList(int position) {
            List<RecordItemBean> newRecords = presenter.getRecordsForPermission(position);
            Collections.sort(newRecords);
            recordAdapter.updateData(newRecords);
        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface onProgressChangedListener {

        void openPermissionUI();
    }
}
