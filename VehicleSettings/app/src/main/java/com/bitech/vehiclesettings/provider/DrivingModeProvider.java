package com.bitech.vehiclesettings.provider;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bitech.platformlib.manager.DrivingManager;
import com.bitech.vehiclesettings.MyApplication;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.utils.EToast;

import java.util.Objects;

public class DrivingModeProvider extends ContentProvider {

    public static final String ACTION = "com.chery.carsettings.provider.drivemode";
    public static final Uri DRIVING_MODE_URI = Uri.parse("content://" + ACTION);
    public static final String TAG = DrivingModeProvider.class.getSimpleName();
    public static final String SET_DRIVING_MODE = "setDriveMode";
    public static final String GET_DRIVING_MODE = "getDriveMode";
    public static final String KEY_MODE = "mode";
    public static final int ECO = 0;
    public static final int ENERGY_SAVING_HYBRID = 1;
    public static final int NORMAL = 2;
    public static final int SPORT = 3;
    public static final int SNOW = 4;
    public static final int INDIVIDUAL = 5;

    DrivingManager drivingManager = DrivingManager.getInstance();

    private int currentDriveMode = 0; // 默认模式

    private Handler mainHandler = new Handler(Looper.getMainLooper());

    @Override
    public boolean onCreate() {
        return true;
    }

    @Override
    public String getType(Uri uri) {
        return null;
    }

    @Override
    public Uri insert(Uri uri, ContentValues values) {
        return null;
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection,
                        String[] selectionArgs, String sortOrder) {
        return null;
    }

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        return 0;
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection,
                      String[] selectionArgs) {
        return 0;
    }

    @Override
    public Bundle call(String method, String arg, Bundle extras) {
        switch (method) {
            case SET_DRIVING_MODE:
                int anInt = extras.getInt(KEY_MODE);
                int powerMode = drivingManager.getPowerMode();
                if(powerMode != CarDriving.FLZCU_9_PowerMode.ON){
                    EToast.showToast(MyApplication.getContext(), MyApplication.getContext().getString(R.string.str_driving_power_mode_on), 0, false);
                }
                return setDriveMode(anInt);
            case GET_DRIVING_MODE:
                return getDriveMode();
            default:
                return null;
        }
    }

    private Bundle setDriveMode(int index) {
        Log.d(TAG, "Drive mode set to: " + index);
        switch (index) {
            case ECO:
                drivingManager.setDrivingModeSet(CarDriving.VCC_1_DriveMode.ECO);
                break;
            case ENERGY_SAVING_HYBRID:
                drivingManager.setDrivingModeSet(CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID);
                break;
            case NORMAL:
                drivingManager.setDrivingModeSet(CarDriving.VCC_1_DriveMode.NORMAL);
                break;
            case SPORT:
                drivingManager.setDrivingModeSet(CarDriving.VCC_1_DriveMode.SPORT);
                break;
            case SNOW:
                drivingManager.setDrivingModeSet(CarDriving.VCC_1_DriveMode.SNOW);
                break;
            case INDIVIDUAL:
                drivingManager.setDrivingModeSet(CarDriving.VCC_1_DriveMode.INDIVIDUAL);
                break;

        }
        return null;
    }

    private Bundle getDriveMode() {
        Bundle result = new Bundle();

        currentDriveMode = drivingManager.getDriveMode();
        switch (currentDriveMode) {
            case CarDriving.VCC_1_DriveMode.ECO:
                result.putInt(KEY_MODE, ECO);
                break;
            case CarDriving.VCC_1_DriveMode.ENERGY_SAVING_HYBRID:
                result.putInt(KEY_MODE, ENERGY_SAVING_HYBRID);
                break;
            case CarDriving.VCC_1_DriveMode.NORMAL:
                result.putInt(KEY_MODE, NORMAL);
                break;
            case CarDriving.VCC_1_DriveMode.SPORT:
                result.putInt(KEY_MODE, SPORT);
                break;
            case CarDriving.VCC_1_DriveMode.SNOW:
                result.putInt(KEY_MODE, SNOW);
                break;
            case CarDriving.VCC_1_DriveMode.INDIVIDUAL:
                result.putInt(KEY_MODE, INDIVIDUAL);
                break;
            default:
                result.putInt(KEY_MODE, ECO);
        }
        return result;
    }
}