package com.bitech.vehiclesettings.utils;

import android.content.Context;
import android.media.AudioAttributes;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.iflytek.autofly.tts.ttsproxy.ITtsClientListener;
import com.iflytek.autofly.ttsaidl.ttsproxy.ITtsRemoteStatusListener;
import com.iflytek.autofly.ttsaidl.ttsproxy.TtsResultCode;
import com.iflytek.autofly.ttsaidl.ttsproxy.TtsServiceAgent;

import java.lang.ref.WeakReference;

/**
 * TTS语音合成工具类
 */
public class TtsHelper {
    private static final String TAG = "TtsHelper";
    private static final String TARGET_PACKAGE_NAME = "com.iflytek.cutefly.speechclient.hmi";
    private static final int DEFAULT_USAGE = AudioAttributes.USAGE_ASSISTANT;

    private static volatile TtsHelper instance;

    private final TtsServiceAgent ttsAgent = TtsServiceAgent.getInstance();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private boolean isInitialized = false;

    private TtsHelper() {}

    public static TtsHelper getInstance() {
        if (instance == null) {
            synchronized (TtsHelper.class) {
                if (instance == null) {
                    instance = new TtsHelper();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化TTS服务
     */
    public void init(@NonNull Context context, @Nullable TtsInitListener listener) {
        if (isInitialized) {
            notifyInitSuccess(listener);
            return;
        }

        Context appContext = context.getApplicationContext();
        int result = ttsAgent.init(new InternalInitListener(listener),
                appContext, DEFAULT_USAGE, false, TARGET_PACKAGE_NAME);

        if (result == TtsResultCode.SUCCESS) {
            isInitialized = true;
        } else {
            notifyInitFailed(listener, result);
        }
    }

    /**
     * 开始播报
     */
    public boolean speak(@NonNull String text, @Nullable TtsPlayListener listener) {
        if (!isInitialized) return false;
        return ttsAgent.startSpeak(text, new InternalPlayListener(listener)) == TtsResultCode.SUCCESS;
    }

    /**
     * 停止播报
     */
    public boolean stop() {
        if (!isInitialized) return false;
        try {
            return ttsAgent.stopSpeak() == TtsResultCode.SUCCESS;
        } catch (RemoteException e) {
            Log.e(TAG, "stopSpeak error", e);
            return false;
        }
    }

    /**
     * 暂停播报
     */
    public boolean pause() {
        if (!isInitialized) return false;
        try {
            return ttsAgent.pauseSpeak() == TtsResultCode.SUCCESS;
        } catch (RemoteException e) {
            Log.e(TAG, "pauseSpeak error", e);
            return false;
        }
    }

    /**
     * 恢复播报
     */
    public boolean resume() {
        if (!isInitialized) return false;
        try {
            return ttsAgent.resumeSpeak() == TtsResultCode.SUCCESS;
        } catch (RemoteException e) {
            Log.e(TAG, "resumeSpeak error", e);
            return false;
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        if (!isInitialized) return;
        isInitialized = !ttsAgent.releaseService();
    }

    // ===== 内部监听器封装 =====
    private class InternalInitListener implements ITtsClientListener {
        private final WeakReference<TtsInitListener> weakListener;

        InternalInitListener(@Nullable TtsInitListener listener) {
            this.weakListener = new WeakReference<>(listener);
        }

        @Override
        public void onTtsInited(boolean success, int errId) {
            if (success) {
                isInitialized = true;
                notifyInitSuccess(weakListener.get());
            } else {
                isInitialized = false;
                notifyInitFailed(weakListener.get(), errId);
            }
        }
    }

    private class InternalPlayListener implements ITtsRemoteStatusListener {
        private final WeakReference<TtsPlayListener> weakListener;

        InternalPlayListener(@Nullable TtsPlayListener listener) {
            this.weakListener = new WeakReference<>(listener);
        }

        @Override
        public void onPlayBegin() {
            runOnMain(() -> {
                TtsPlayListener listener = weakListener.get();
                if (listener != null) listener.onPlayBegin();
            });
        }

        @Override
        public void onPlayCompleted() {
            runOnMain(() -> {
                TtsPlayListener listener = weakListener.get();
                if (listener != null) listener.onPlayCompleted();
            });
        }

        @Override
        public void onPlayInterrupted() {
            runOnMain(() -> {
                TtsPlayListener listener = weakListener.get();
                if (listener != null) listener.onPlayInterrupted();
            });
        }

        @Override
        public void onProgressReturn(int i, int i1) {

        }

        @Override
        public void onPlayText(String s) {

        }

        @Override
        public void onPlayPause() {

        }

        @Override
        public void onPlayResume() {

        }
    }

    // ====== 主线程回调工具 ======
    private void runOnMain(Runnable task) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            task.run();
        } else {
            mainHandler.post(task);
        }
    }

    private void notifyInitSuccess(@Nullable TtsInitListener listener) {
        runOnMain(() -> {
            if (listener != null) listener.onInitSuccess();
        });
    }

    private void notifyInitFailed(@Nullable TtsInitListener listener, int errorCode) {
        runOnMain(() -> {
            if (listener != null) listener.onInitFailed(errorCode);
        });
    }

    // ====== 接口定义 ======
    public interface TtsInitListener {
        void onInitSuccess();
        void onInitFailed(int errorCode);
    }

    public interface TtsPlayListener {
        void onPlayBegin();
        void onPlayCompleted();
        void onPlayInterrupted();
    }
}
