package com.bitech.vehiclesettings.view.widget

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.Toast
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.databinding.ToastSettingsTipsBinding

/**
 * @Description: Setting自定义Toast.
 **/
object SettingsToast {

    private var lastToast: Toast? = null

    fun showToast(contentId: Int) {
        showToast(MyApplication.getContext(), MyApplication.getContext().getString(contentId))
    }

    fun showToast(message: String) {
        show(MyApplication.getContext(), message, false)
    }

    fun showToast(context: Context, contentId: Int) {
        showToast(context, context.getString(contentId))
    }

    fun showToast(context: Context, message: String) {
        show(context, message, false)
    }

    fun showShortToast(context: Context, contentId: Int) {
        showShortToast(context, context.getString(contentId))
    }

    fun showShortToast(context: Context, message: String) {
        show(context, message, true)
    }

    fun show(context: Context, message: String, shortToast: Boolean) {
        val inflater = LayoutInflater.from(context)
        val binding: ToastSettingsTipsBinding = ToastSettingsTipsBinding.inflate(inflater)
        binding.tvToastText.text = message

        lastToast?.cancel()
        val toast = Toast(context)
        toast.view = binding.root
//        toast.setGravity(Gravity.TOP, 0, -100)
        toast.setGravity(Gravity.TOP, 0, 0)
        toast.duration = if (shortToast) Toast.LENGTH_SHORT else Toast.LENGTH_LONG
//        AnimationHelper.startToastAnimation(binding.toastCommonBg)
        toast.show()
        lastToast = toast
    }

}