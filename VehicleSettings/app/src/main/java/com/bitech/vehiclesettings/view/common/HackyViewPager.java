package com.bitech.vehiclesettings.view.common;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.OverScroller;

import androidx.viewpager.widget.ViewPager;

public class HackyViewPager extends ViewPager {
    private OnScrollListener mOnScrollListener;

    public HackyViewPager(Context context) {
        super(context);
    }

    public HackyViewPager(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (mOnScrollListener != null) {
            mOnScrollListener.onScroll(l, t);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return false; // 禁止滑动
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        return false; // 禁止拦截触摸事件
    }

    public void setOnScrollListener(OnScrollListener listener) {
        this.mOnScrollListener = listener;
    }

    public interface OnScrollListener {
        void onScroll(int scrollX, int scrollY);
    }

    public class MyScroller extends OverScroller {
        public MyScroller(Context context) {
            super(context);
        }

        @Override
        public void startScroll(int startX, int startY, int dx, int dy, int duration) {
            super.startScroll(startX, startY, dx, dy, 0);
        }
    }
}