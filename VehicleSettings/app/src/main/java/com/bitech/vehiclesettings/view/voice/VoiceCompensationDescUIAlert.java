package com.bitech.vehiclesettings.view.voice;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundVoiceCompensationBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertSoundVoiceCompensationDescBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class VoiceCompensationDescUIAlert extends BaseDialog {
    private static final String TAG = VoiceCompensationDescUIAlert.class.getSimpleName();


    public VoiceCompensationDescUIAlert(Context context) {
        super(context);
    }

    public VoiceCompensationDescUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected VoiceCompensationDescUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private VoiceCompensationDescUIAlert dialog = null;
        private View layout;
        private DialogAlertSoundVoiceCompensationDescBinding binding;
        public Builder(Context context) {
            this.context = context;
        }


        public VoiceCompensationDescUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public VoiceCompensationDescUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new VoiceCompensationDescUIAlert(context,R.style.Dialog);
            binding = DialogAlertSoundVoiceCompensationDescBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);

            return dialog;
        }

        public void dismiss() {
            if (dialog != null) {
                dialog.dismiss();
            }
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

}
