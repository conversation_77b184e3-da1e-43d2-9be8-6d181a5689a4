package com.bitech.vehiclesettings.view.light;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.ScrollView;

import com.bitech.vehiclesettings.R;

/**
 * 项目名称：ColorPickerView
 * 类描述：
 * 创建人：liuchanggui
 * 创建时间：2025/6/3
 */
public class ColorPickerView extends FrameLayout {
    private final String TAG = "ColorPickerView";
    private ImageView imgColorRang;//颜色选择盘
    private ImageView imgPicker;//颜色选择器
    private RelativeLayout rl_root;
    private ScrollView scrollView;

    private Bitmap bitmap;//颜色选择盘图片
    private onColorChangedListener colorChangedListener;//颜色变换监听
    private int lastPixel, lastPositionX;
    private int startX;

    public ColorPickerView(Context context) {
        super(context);
    }

    public ColorPickerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
        initTouchListener();
    }


    private void initView(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.color_picker, this);
        rl_root = view.findViewById(R.id.rl_root);
        imgColorRang = view.findViewById(R.id.img_color_rang);
        imgPicker = view.findViewById(R.id.img_picker);
        bitmap = Bitmap.createBitmap(552, 96, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        imgColorRang.getDrawable().setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        imgColorRang.getDrawable().draw(canvas);
//        bitmap = ((BitmapDrawable) imgColorRang.getDrawable()).getBitmap();//获取圆盘图片
    }

    public void setScrollView(ScrollView scrollView) {
        this.scrollView = scrollView;
    }

    public void setImgPickerPosition(int xPosition) {
        imgPicker.layout(xPosition, imgPicker.getTop(), xPosition + 4, imgPicker.getBottom());
//        if (imgPicker != null) {
//            rl_root.removeView(imgPicker);
//        }
//        imgPicker = new ImageView(getContext());
//        imgPicker.setLayoutParams(new RelativeLayout.LayoutParams(4, RelativeLayout.LayoutParams.MATCH_PARENT));
//        this.startX = xPosition;
//        imgPicker.setX(xPosition);
//        rl_root.addView(imgPicker);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initTouchListener() {
        rl_root.setOnTouchListener((v, event) -> {
            if (scrollView != null) {
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    scrollView.requestDisallowInterceptTouchEvent(false);
                    colorChangedListener.stopColorChanged(Color.red(lastPixel), Color.green(lastPixel), Color.blue(lastPixel), lastPositionX);
                } else {
                    scrollView.requestDisallowInterceptTouchEvent(true);
                }
            }
            float xInView = event.getX();
            float yInView = event.getY();
            Log.d(TAG, "xInView: " + xInView + ",yInView: " + yInView + ",left: " + imgColorRang.getLeft() + ",top: " + imgColorRang.getTop() + ",right: " + imgColorRang.getRight() + ",bottom: " + imgColorRang.getBottom());
            //触摸点与圆盘圆心距离
            if (colorChangedListener != null) {
                if (colorChangedListener.stopPick()) {
                    return false;
                }
            }
            //在选色图片内则进行读取颜色等操作
            if ((xInView >= 2 && xInView <= (imgColorRang.getRight() - 2))) {
                //选色位置指示，若设置了则移动到点取的位置
                if (imgPicker != null) {
                    int xInWindow = (int) event.getX() - startX;
                    int left = xInWindow - imgPicker.getWidth() / 2;
                    int right = left + imgPicker.getWidth();
                    imgPicker.layout(left, imgPicker.getTop(), right, imgPicker.getBottom());
                }
                //读取颜色
                if ((event.getX() > 0 && event.getX() / 2 < bitmap.getWidth()) && (event.getY() > 0 && event.getY() / 2 < bitmap.getHeight())) {
                    int pixel = bitmap.getPixel((int) event.getX(), bitmap.getHeight() / 2);   //获取选择像素
                    lastPixel = pixel;
                    lastPositionX = (int) xInView - 2;
                    Log.d(TAG, "initTouchListener: red=" + Color.red(pixel) + ",green=" + Color.green(pixel) + ",blue=" + Color.blue(pixel));
                    Log.d(TAG, "initTouchListener: " + String.format("#%02X%02X%02X", Color.red(pixel), Color.green(pixel), Color.blue(pixel)));
                    if (colorChangedListener != null) {
                        if (event.getAction() == MotionEvent.ACTION_UP) {
                            colorChangedListener.stopColorChanged(Color.red(pixel), Color.green(pixel), Color.blue(pixel), lastPositionX);
                        } else {
                            colorChangedListener.colorChanged(Color.red(pixel), Color.green(pixel), Color.blue(pixel), lastPositionX);
                        }
                    }
                }
            } else if (xInView > (imgColorRang.getRight() - 2)) {
                int xInWindow = (int) event.getX() - startX;
                int left = xInWindow - imgPicker.getWidth() / 2;
                int right = left + imgPicker.getWidth();
                imgPicker.layout(imgColorRang.getRight() - 4, imgPicker.getTop(), imgColorRang.getRight(), imgPicker.getBottom());
                if (colorChangedListener != null) {
                    int pixel = Color.parseColor("#FF2B45");

                    if (event.getAction() == MotionEvent.ACTION_UP) {
                        colorChangedListener.stopColorChanged(Color.red(pixel), Color.green(pixel), Color.blue(pixel), lastPositionX);
                    } else {
                        colorChangedListener.colorChanged(Color.red(pixel), Color.green(pixel), Color.blue(pixel), lastPositionX);
                    }
                }
            } else if (xInView < 2) {
                imgPicker.layout(0, imgPicker.getTop(), 4, imgPicker.getBottom());
                if (colorChangedListener != null) {
                    int pixel = Color.parseColor("#FF0000");
                    if (event.getAction() == MotionEvent.ACTION_UP) {
                        colorChangedListener.stopColorChanged(Color.red(pixel), Color.green(pixel), Color.blue(pixel), lastPositionX);
                    } else {
                        colorChangedListener.colorChanged(Color.red(pixel), Color.green(pixel), Color.blue(pixel), lastPositionX);
                    }
                }
            }
            return true;
        });
    }

    public void setColorChangedListener(onColorChangedListener colorChangedListener) {
        this.colorChangedListener = colorChangedListener;
    }

    /**
     * 颜色变换监听接口
     */
    public interface onColorChangedListener {
        void colorChanged(int red, int green, int blue, int positionX);

        void stopColorChanged(int red, int green, int blue, int positionX);

        boolean stopPick();
    }

}