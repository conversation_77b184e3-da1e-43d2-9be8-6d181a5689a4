package com.bitech.vehiclesettings.view.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.ToastSettingsDmTipsBinding
import com.bitech.vehiclesettings.manager.CarConfigInfoManager
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: SettingsDmBtnToast
 * 
 * @Date:  2024/3/30 13:25
 * @Description: Setting dm自定义Toast.
 **/
@SuppressLint("InflateParams")
class SettingsDmBtnToast(context: Context) {
    // window对象
    private var windowManager: WindowManager? = null

    // window属性对象
    private val layoutParams: WindowManager.LayoutParams

    // 提示文言
    private var tips: String? = null
    private var toastView: ToastSettingsDmTipsBinding
    private var confirmClickCallback: OnConfirmClickCallback? = null

    init {
        toastView = ToastSettingsDmTipsBinding
                .bind(LayoutInflater.from(context).inflate(R.layout.toast_settings_dm_tips, null))
        // 初始化windowManager对象
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        // 设置窗口参数
        layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_STATUS_BAR_PANEL,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                    or WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        )
        // 设置Toast位置
        layoutParams.token = toastView.root.windowToken
        layoutParams.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
        layoutParams.y = 200
        toastView.toastOkBtn.setOnClickListener {
            // 确认按钮回调
            confirmClickCallback?.onConfirmClick()
            // 弹窗消失
            onDismiss(false)
        }
    }

    /**
     * 显示Toast提示.
     *
     * @param message 消息
     */
    fun showToast(message: String) {
        Handler(Looper.getMainLooper()).post {
            tips = message
            toastView.toastTipsTv.text = message
            // 添加视图
            windowManager?.addView(toastView.root, layoutParams)
            // 10s自动消失
            Handler(Looper.getMainLooper()).postDelayed({
                onDismiss(true)
            }, 10000)
        }
    }

    /**
     * 弹窗消失.
     *
     * @param isCallDismiss 是否回调dismiss
     */
    fun onDismiss(isCallDismiss: Boolean = false) {
        LogUtil.i(TAG, "onDismiss : isCallDismiss = $isCallDismiss")
        if (toastView.root.windowToken != null) {
            if (isCallDismiss) {
                confirmClickCallback?.onDismiss()
            }
            windowManager?.removeView(toastView.root)
            confirmClickCallback = null
            windowManager = null
        }
    }

    /**
     * 设置确认按钮点击事件监听.
     *
     * @param callback
     */
    fun setClickCallback(callback: OnConfirmClickCallback) {
        confirmClickCallback = callback
    }

    interface OnConfirmClickCallback {
        /**
         * 确认按钮被点击.
         *
         */
        fun onConfirmClick()

        /**
         * 确认按钮被点击.
         *
         */
        fun onDismiss()
    }

    companion object {
        // 日志标志位
        private const val TAG = "SettingsDmBtnToast"
    }
}
