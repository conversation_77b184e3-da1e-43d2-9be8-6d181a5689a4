package com.bitech.vehiclesettings.fragment;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewbinding.ViewBinding;

import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.presenter.system.SystemPresenter;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;

import java.lang.ref.WeakReference;

import xyz.iyer.cloudposlib.zprogresshud.ZProgressHUD;

public abstract class BaseFragment<T extends ViewBinding> extends Fragment {

    private static final String TAG = "BaseFragment";
    protected Activity mContext;
//    protected View rootView;
    private ZProgressHUD zp;
    protected T binding;
    private Animation anim;

    // 窗口计数器
    private static int activeActivities = 0;
    private boolean mIsFragmentVisibleFirst = true;

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        Log.d(TAG, "onAttach context= " + context);
        if (context instanceof Activity) {
            mContext = new WeakReference<>((MainActivity) context).get();
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        Log.d(TAG, "onDetach");
        mContext = null;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        activeActivities++;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = getLayoutResId(inflater, container);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView();
        setListener();
        // 初始化viewModel订阅
        initObserve();
        // 初始化数据
        if (mIsFragmentVisibleFirst) {
            mIsFragmentVisibleFirst = false;
            initData();
        }
    }

    /**
     * 加载布局文件
     *
     * @return int
     */
    protected abstract T getLayoutResId(LayoutInflater inflater, ViewGroup container);

    protected abstract void initView();

    protected abstract void setListener();

    protected abstract void initObserve();

    protected abstract void initData();

    protected void loadPageAnim(View view, int currentPosition, int position) {
        if (view == null) {
            return;
        }
        if (anim != null) {
            anim.cancel();
            anim = null;
        }
        view.clearAnimation();
        if (mContext == null) {
            return;
        }
        if (anim == null) {
            Log.d("页面切换动效", currentPosition + " " + position);
//            anim = AnimationUtils.loadAnimation(mContext, position - currentPosition > 0 ?
//                    com.bitech.base.R.anim.slide_in_up : com.bitech.base.R.anim.slide_in_down);
            anim = AnimationUtils.loadAnimation(mContext, com.bitech.base.R.anim.fade_in);
        }


        view.startAnimation(anim);

    }

    protected void showProgress(String message) {
        hideProgress();
        zp = new ZProgressHUD(getActivity());
        if (!TextUtils.isEmpty(message)) {
            zp.setMessage(message);
        } else {
            zp.setMessage("加载中");
        }
        zp.show();
    }

    protected void hideProgress() {
        if (zp != null && zp.isShowing()) {
            zp.dismiss();
        }
    }

    protected void onBackPressedSupport() {
        if (mContext == null) {
            return;
        }
        mContext.onBackPressed();
        mContext.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (binding != null) {
            binding = null;
        }
    }

    @Override
    public void onDestroy() {
        mContext = null;
        super.onDestroy();
        if (--activeActivities == 0) {
            updatePermissionStatus(); // 重置授权状态
        }
    }

    private static void updatePermissionStatus() {
        if (SystemPresenter.isThisTimePermissionCamera) {
            Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_CAMERA, 0);
        }
        if (SystemPresenter.isThisTimePermissionMicrophone) {
            Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_MICROPHONE, 0);
        }
        if (SystemPresenter.isThisTimePermissionLocation) {
            Prefs.put(PrefsConst.SYSTEM_PERMISSION_APP_LOCATION, 0);
        }
    }


}
