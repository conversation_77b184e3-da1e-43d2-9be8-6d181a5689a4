package com.bitech.vehiclesettings.carapi.constants;

import com.bitech.vehiclesettings.utils.PrefsConst;

public class CarDisplay {
    public static final float FONT_SIZE_LITTLE = 1.0f;
    public static final float FONT_SIZE_STANDARD = 1.15f;
    public static final float FONT_SIZE_LARGE = 1.3f;
    public static final int DAY = 0;
    public static final int NIGHT = 1;

    public static final int AUTO = 2;

    public static final int DEFAULT_FP = PrefsConst.FALSE;
    public static final float DEFAULT_FONT_SIZE = 1.0f;

    public static class Lyrics {
        public static final int SHOW = 1;
        public static final int HIDE = 0;
        public static final int DEFAULT = 0;
    }
    public static final int DEFAULT_VIDEO_LIMIT = 1;
    public static final int DEFAULT_ZKP_BRIGHTNESS_DAY = 8;
    public static final int DEFAULT_ZKP_BRIGHTNESS_NIGHT = 2;
    public static final int DEFAULT_AUTO = 1;
    public static final int DEFAULT_YBP_BRIGHTNESS = 8;
    public static final int BRIGHT_LIMIT = 1;
}
