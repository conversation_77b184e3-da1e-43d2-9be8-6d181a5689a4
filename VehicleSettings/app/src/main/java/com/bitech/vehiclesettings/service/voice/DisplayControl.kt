package com.bitech.vehiclesettings.service.voice

import android.app.UiModeManager
import android.content.Context
import android.net.Uri
import android.util.Log
import com.bitech.platformlib.manager.DisplayManager
import com.bitech.platformlib.manager.NewEnergyManager
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarDisplay
import com.bitech.vehiclesettings.carapi.constants.CarDriving
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.fragment.DisplayFragment
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter
import com.bitech.vehiclesettings.presenter.display.WallpaperPresenter
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.DialogNavigationUtils
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import com.bitech.vehiclesettings.view.display.CleanUIAlert
import com.bitech.vehiclesettings.view.display.WallpaperUIAlert
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDVRRespondID
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P

class DisplayControl(private var context: Context) {
    private val TAG = "DisplayControl"
    private val mDisplayManager: DisplayManager = DisplayManager.getInstance()
    private var mContext: Context = context
    private var cleanUIAlert: CleanUIAlert? = CleanUIAlert.getInstance(context)
    private val mNewEnergyManager : NewEnergyManager = NewEnergyManager.getInstance()
    private val mDisplayPresenter: DisplayPresenter = DisplayPresenter.getInstance()


    private fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id")
    }

    //特殊提示语id
    private fun sendResultCode(respondId: String, mValue: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        param.value = mValue

        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
        Log.d("sendResultCode", "sendResultCode: zhc6whu:返回提示语id" + param.respondId)
    }

    @JvmField
    val BASE_URI: Uri = Uri.Builder().scheme("content")
        .authority(Constant.AUTHORITY)
        .build()

    fun showPrevImageWallpaper(context: Context) {
        context.contentResolver.call(
            BASE_URI,
            Constant.METHOD_SHOW_PREV_IMAGE_WALLPAPER,
            null,
            null
        )
    }

    fun showNextImageWallpaper(context: Context) {
        context.contentResolver.call(
            BASE_URI,
            Constant.METHOD_SHOW_NEXT_IMAGE_WALLPAPER,
            null,
            null
        )
    }

    //设置特定的主题
    fun setSpecificTheme(value: String) {
        // theme=【0=风景，1=黑色，2=田园，3=白色，4=黑夜，5=科技，6=白天，7=城市，8=时尚，9=人物，10=赛车，11=盗梦，12=轻奢，13=智慧，14=复古，15=经典，16=极简，17=运动】
        // position=【0=中控屏，1=仪表屏，2=副驾屏，3=二排左屏，4=二排右屏，5=二排吸顶屏，6=二排扶手屏，7=二排左扶手屏，8=二排右扶手屏，9=三排左屏，10=三排右屏，11=三排吸顶屏】
        TODO()
    }

    //设置桌面壁纸
    //讯飞传入参数 0：场景化壁纸 1：静态壁纸
    fun setSpecificmode(value: String) {
        val wallpaper = WallpaperPresenter.getWallpaperState()
        if (value.toInt() == 0) {
            if (wallpaper == CarSettingConstant.WALLPAPER_TYPE_3D) {
                sendResultCode(VDVRRespondID.set_wallpaper_2, "场景化壁纸")
            } else {
                if (!WallpaperUIAlert.isShow) {
                    DialogNavigationUtils.launchMainActivity(
                        mContext,
                        MainActivity.MainTabIndex.DISPLAY,
                        DisplayFragment.WallpaperType,
                        CommonConst.DIALOG_OPEN
                    )
                }
                sendResultCode(VDVRRespondID.set_wallpaper_1)
            }
        } else {
            if (wallpaper == CarSettingConstant.WALLPAPER_TYPE_3D) {
                sendResultCode(VDVRRespondID.close_wallpaper_2, "静态壁纸")
            } else {
                if (!WallpaperUIAlert.isShow) {
                    DialogNavigationUtils.launchMainActivity(
                        mContext,
                        MainActivity.MainTabIndex.DISPLAY,
                        DisplayFragment.WallpaperType,
                        CommonConst.DIALOG_OPEN
                    )
                }
                sendResultCode(VDVRRespondID.close_wallpaper_1)
            }
        }
    }

    // 关闭壁纸界面
    fun closeSpecificmode(value: String) {
        // todo
        val wallpaper = WallpaperPresenter.getWallpaperState()
        if (value.toInt() == 0) {
            if (wallpaper == CarSettingConstant.WALLPAPER_TYPE_3D) {
                sendResultCode(VDVRRespondID.close_wallpaper_2, "场景化壁纸")
            } else {
                if (!WallpaperUIAlert.isShow) {
                    DialogNavigationUtils.launchMainActivity(
                        mContext,
                        MainActivity.MainTabIndex.DISPLAY,
                        DisplayFragment.WallpaperType,
                        CommonConst.DIALOG_OPEN
                    )
                }
                sendResultCode(VDVRRespondID.set_wallpaper_1)
            }
        } else {
            if (wallpaper == CarSettingConstant.WALLPAPER_TYPE_3D) {
                sendResultCode(VDVRRespondID.close_wallpaper_2, "静态壁纸")
            } else {
                if (!WallpaperUIAlert.isShow) {
                    DialogNavigationUtils.launchMainActivity(
                        mContext,
                        MainActivity.MainTabIndex.DISPLAY,
                        DisplayFragment.WallpaperType,
                        CommonConst.DIALOG_OPEN
                    )
                }
                sendResultCode(VDVRRespondID.close_wallpaper_1)
            }
        }
    }
    // 切换壁纸--无指定值
    fun switchWallpaper(flag: Boolean){
        val wallpaper = WallpaperPresenter.getWallpaperState()
        val sum_wallpaper = WallpaperPresenter.getPRSETWallpapersSelectedSize()+ WallpaperPresenter.getGalleryWallpapersSelectedSize()
        if(flag){
            if(wallpaper == CarSettingConstant.WALLPAPER_TYPE_3D){
                sendResultCode(VDVRRespondID.switch_wallpaper_new_2)
            }else{
                if(sum_wallpaper > 1){
                    showNextImageWallpaper(mContext);
                    sendResultCode(VDVRRespondID.switch_wallpaper_new_3)
                }else if (sum_wallpaper == 1){
                    sendResultCode(VDVRRespondID.switch_wallpaper_new_1)
                }
            }
        }
    }
    // 切换壁纸--上一张
    fun switchPrevWallpaper(flag: Boolean) {
        val wallpaper = WallpaperPresenter.getWallpaperState()
        val sum_wallpaper = WallpaperPresenter.getPRSETWallpapersSelectedSize()
        + WallpaperPresenter.getGalleryWallpapersSelectedSize()
        if(flag){
            if(wallpaper == CarSettingConstant.WALLPAPER_TYPE_3D){
                sendResultCode(VDVRRespondID.switch_wallpaper_NEXT_new_2)
            }else{
                if(sum_wallpaper > 1){
                    showNextImageWallpaper(mContext)
                    sendResultCode(VDVRRespondID.switch_wallpaper_NEXT_new_3)
                }else if (sum_wallpaper == 1){
                    sendResultCode(VDVRRespondID.switch_wallpaper_NEXT_new_1)
                }
            }
        }
    }

    // 摄像头功能设置
    // flag = 0 时为关闭摄像头，flag = 1 时为打开摄像头
    fun setControlCamera(flag: Boolean) {
        var DMS_status = Prefs.get(PrefsConst.D_DMS_OPEN_STATUS, CarDriving.DMS_OPEN_Status.OFF)
        var Private_staus = Prefs.get(
            PrefsConst.D_DMS_PRIVACY_AGREEMENT,
            CarDriving.DMS_Privacy_Agreement.NOT_AGREE
        );

        if (flag) {
            if (DMS_status == CarSettingConstant.CAMERA_OFF) {
                // 隐私条例状态
                if (Private_staus == 0) {
                    sendResultCode(VDVRRespondID.Turn_on_in_car_camera_1)
                } else {
                    sendResultCode(VDVRRespondID.Turn_on_in_car_camera_2)
                }
            } else {
                sendResultCode(VDVRRespondID.Turn_on_in_car_camera_3)
            }
        } else {
            if (DMS_status == CarSettingConstant.CAMERA_ON) {
                sendResultCode(VDVRRespondID.close_on_in_car_camera_1)
            } else {
                sendResultCode(VDVRRespondID.close_dms_camera_2)
            }
        }
    }
    // 电量、剩余电量查询
    fun setElectricityQuery(){
        //TODO
        val mileage = mNewEnergyManager.getEnduranceElectricCurrentMode()
        val battery = mNewEnergyManager.getBatteryLevel()
        if(battery > CarSettingConstant.LOW_BATTERY){
            sendResultCode(VDVRRespondID.vehicleInfo_1,"${battery}-${mileage}")
        }else if(battery < CarSettingConstant.LOW_BATTERY && mileage < CarSettingConstant.LOW_MILEAGE){
            sendResultCode(VDVRRespondID.vehicleInfo_1,"${battery}-${mileage}")
        }
    }
    // 续航查询
    fun queryRange(){
        val mileage = mNewEnergyManager.getEnduranceElectricCurrentMode()
        sendResultCode(VDVRRespondID.vehicleInfo_4,mileage.toString())
    }

    /**
     * 切换显示模式
     */
    fun setDisplayMode() {
        val disPlayPresenter = DisplayPresenter.getInstance()
        val globalValue = Prefs.getGlobalValue(PrefsConst.DISPLAY_MODE, CarDisplay.AUTO)
        when (globalValue) {
            CarDisplay.DAY -> {
                disPlayPresenter.setDisplayMode(CarDisplay.DAY, false)
                sendResultCode(VDVRRespondID.switch_screen_display_mode_1, "浅色模式")
            }

            CarDisplay.NIGHT -> {
                disPlayPresenter.setDisplayMode(CarDisplay.NIGHT, false)
                sendResultCode(VDVRRespondID.switch_screen_display_mode_1, "深色模式")
            }

            CarDisplay.AUTO -> {
                disPlayPresenter.setDisplayMode(CarDisplay.AUTO, false)
                sendResultCode(VDVRRespondID.switch_screen_display_mode_1, "自动模式")
            }
        }
    }

    /**
     * 屏幕清洁开关
     * param:flag true:开启 false:关闭
     */
    fun setScreenClean(flag: Boolean) {
        if (flag) {
            if (cleanUIAlert?.isShowing != true) {
                cleanUIAlert = CleanUIAlert.getInstance(context)
                cleanUIAlert?.show()
                Log.d(TAG, "ScreenClean");
                sendResultCode(VDVRRespondID.open_screen_cleaning_1)
            } else {
                sendResultCode(VDVRRespondID.open_screen_cleaning_2)
            }
        } else {
            if (cleanUIAlert?.isShowing == true) {
                cleanUIAlert?.dismiss()
                sendResultCode(VDVRRespondID.close_screen_cleaning_1)
            } else {
                sendResultCode(VDVRRespondID.close_screen_cleaning_2)
            }
        }
    }

    /**
     * 分屏开关
     * param:flag true:开启 false:关闭
     */
    fun setSplitScreenMode(flag: Boolean) {
        val globalValue = mDisplayPresenter.getFp()
        val uri = Uri.parse(CarSettingConstant.SPLIT_SCREEN_URL)
        val resultBundle =
            context.contentResolver.call(uri, CarSettingConstant.SPLIT_SCREEN_STATUS, null, null)
        val state = resultBundle?.getBoolean(CarSettingConstant.SPLIT_SCREEN_STATUS_RESULT, false)
        if (flag) {
            if (state == true) {
                sendResultCode(VDVRRespondID.OPEN_SPLIT_SCREEN_3)
            } else if (globalValue == CarSettingConstant.SPLIT_SCREEN_ON) {
                sendResultCode(VDVRRespondID.OPEN_SPLIT_SCREEN_2)
            } else if (globalValue == CarSettingConstant.SPLIT_SCREEN_OFF) {
                mDisplayPresenter.setFp(CarSettingConstant.SPLIT_SCREEN_ON)
                sendResultCode(VDVRRespondID.OPEN_SPLIT_SCREEN_1)
            }
        } else {
            if (state == true) {
                mDisplayPresenter.setFp(CarSettingConstant.SPLIT_SCREEN_OFF)
                sendResultCode(VDVRRespondID.CLOSE_SPLIT_SCREEN_3)
            } else if (globalValue == CarSettingConstant.SPLIT_SCREEN_OFF) {
                sendResultCode(VDVRRespondID.CLOSE_SPLIT_SCREEN_2)
            } else {
                mDisplayPresenter.setFp(CarSettingConstant.SPLIT_SCREEN_OFF)
                sendResultCode(VDVRRespondID.CLOSE_SPLIT_SCREEN_1)
            }
        }
    }

    /**
     * 屏幕亮度调到具体百分比
     * @param value 0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
     */
    //屏幕亮度调到具体百分比,0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
    fun setScreenBrightnessPercentage(value: String) {
        val parts = value.split("-")
        val param1 =
            parts[0].toIntOrNull() ?: throw IllegalArgumentException("第一个参数必须为整数")
        val param2Str = parts[1]
        if (param2Str.contains(".")) {
            // 是小数：发送特定响应码
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_8)
            return // 终止后续逻辑
        }
        val param2 =
            param2Str.toIntOrNull() ?: throw IllegalArgumentException("第二个参数必须为整数")
        //调节中控屏亮度
        if (param1 == Constant.SCREEN_ZKP_CONTROL_CENTER) {
            val brightness = mDisplayPresenter.getZKPBrightness()
            // 判断参数范围并设置屏幕亮度,范围为1-10,10%-100%
            if (10 <= param2 && param2 <= 100 && param2 / 10 != brightness) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(param2 / 10)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_1)
            } else if (param2 > 100) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(10)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            } else if (param2 < 10) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(1)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            } else {
                sendResultCode(
                    VDVRRespondID.adjust_screen_brightness_to_number_9,
                    param2.toString() + "%"
                )
            }
        }
        //调节仪表盘亮度
        else if (param1 == Constant.SCREEN_YBP_CONTROL_CENTER) {
            val brightness = mDisplayPresenter.getYBPBrightness()
            // 判断参数范围并设置屏幕亮度,范围为1-10,10%-100%
            if (10 <= param2 && param2 <= 100 && param2 / 10 != brightness) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPBrightness(param2 / 10)
                Log.d(TAG, "ScreenBrightnessPercentage");
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_1)
            } else if (param2 > 100) {
                // 设置屏幕亮度为最高
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPBrightness(10)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            } else if (param2 < 10) {
                // 设置屏幕亮度为最低
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPBrightness(1)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            } else {
                sendResultCode(
                    VDVRRespondID.adjust_screen_brightness_to_number_9,
                    param2.toString() + "%"
                )
            }
        }
        //调节无指定屏、全车亮度
        else if (param1 == Constant.SCREEN_ALL_CONTROL_CENTER || param1 == Constant.SCREEN_UNSPECIFIED_CONTROL_CENTER) {
            val brightness1 = mDisplayPresenter.getZKPBrightness()
            val brightness2 = mDisplayPresenter.getYBPBrightness()
            if (CarSettingConstant.SCREEN_BRIGHTNESS_MIN <= param2 && param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX && param2 / 10 != brightness1 || param2 / 10 != brightness2) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = param2 / 10
                mDisplayPresenter.setZKPBrightness(param2 / 10)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_1)
            } else if (param2 > 100) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MAX
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MAX)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            } else if (param2 < 10) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MIN
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MIN)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            } else {
                sendResultCode(
                    VDVRRespondID.adjust_screen_brightness_to_number_9,
                    param2.toString() + "%"
                )
            }
        }
        //无此屏幕、后排屏
        else if (param1 == Constant.SCREEN_NO_CONTROL_CENTER || param1 == Constant.SCREEN_HP_CONTROL_CENTER) {
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
        }
    }

    /**
     * 屏幕亮度调到具体数值
     * @param value 0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
     */
    fun setScreenBrightness(value: String) {
        val parts = value.split("-")
        val param1 =
            parts[0].toIntOrNull() ?: throw IllegalArgumentException("第一个参数必须为整数")
        val param2Str = parts[1]
        if (param2Str.contains(".")) {
            // 是小数：发送特定响应码
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_8)
            return // 终止后续逻辑
        }
        val param2 =
            param2Str.toIntOrNull() ?: throw IllegalArgumentException("第二个参数必须为整数")
        //调节中控屏亮度
        if (param1 == Constant.SCREEN_ZKP_CONTROL_CENTER) {
            val brightness = mDisplayPresenter.getZKPBrightness()
            if (param2 != brightness && param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX && param2 >= CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(param2)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_1)
            } else if (param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MAX)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            } else if (param2 < CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MIN)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            } else {
                sendResultCode(
                    VDVRRespondID.adjust_screen_brightness_to_number_9,
                    param2.toString()
                )
            }
        }
        //调节仪表屏亮度
        else if (param1 == Constant.SCREEN_YBP_CONTROL_CENTER) {
            val brightness = mDisplayPresenter.getYBPBrightness()
            if (param2 != brightness && param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX && param2 >= CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPBrightness(param2)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_1)
            } else if (param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MAX
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            } else if (param2 < CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MIN
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            } else {
                sendResultCode(
                    VDVRRespondID.adjust_screen_brightness_to_number_9,
                    param2.toString()
                )
            }
        }
        //调节无指定屏幕、全车亮度
        else if (param1 == Constant.SCREEN_ALL_CONTROL_CENTER || param1 == Constant.SCREEN_UNSPECIFIED_CONTROL_CENTER) {
            val brightness1 = mDisplayPresenter.getZKPBrightness()
            val brightness2 = mDisplayPresenter.getYBPBrightness()
            if (param2 != brightness1 || param2 != brightness2 && param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX && param2 >= CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(param2)
                mDisplayPresenter.ybpBrightness = param2
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_1)
            } else if (param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MAX)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MAX
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            } else if (param2 < CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MIN)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MIN
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            } else {
                sendResultCode(
                    VDVRRespondID.adjust_screen_brightness_to_number_9,
                    param2.toString()
                )
            }
        }
        //无此屏幕、后排屏幕亮度
        else if (param1 == Constant.SCREEN_NO_CONTROL_CENTER || param1 == Constant.SCREEN_HP_CONTROL_CENTER) {
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
        }
    }

    /**
     * 屏幕亮度挡位调节
     * @param value 0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
     */
    fun setScreenBrightnessLevel(value: String) {
        val parts = value.split("-").takeIf { it.size == 2 } ?: run {
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
            return
        }

        // 解析参数
        val mode = parts[0].toIntOrNull() ?: run {
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
            return
        }

        // 检查操作数格式
        if (parts[1].contains(".")) return

        val operation = parts[1].toIntOrNull() ?: run {
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
            return
        }

        // 准备屏幕配置
        val screenConfigs = when (mode) {
            Constant.SCREEN_ZKP_CONTROL_CENTER -> listOf(
                ScreenConfig(
                    { mDisplayPresenter.getZKPBrightness() },
                    { mDisplayPresenter.setZKPBrightness(it) },
                    { mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF) }
                )
            )

            Constant.SCREEN_YBP_CONTROL_CENTER -> listOf(
                ScreenConfig(
                    { mDisplayPresenter.getYBPBrightness() },
                    { mDisplayPresenter.setYBPBrightness(it) },
                    { mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF) }
                )
            )

            Constant.SCREEN_UNSPECIFIED_CONTROL_CENTER,
            Constant.SCREEN_ALL_CONTROL_CENTER -> listOf(
                ScreenConfig(
                    { mDisplayPresenter.getZKPBrightness() },
                    { mDisplayPresenter.setZKPBrightness(it) },
                    { mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF) }
                ),
                ScreenConfig(
                    { mDisplayPresenter.getYBPBrightness() },
                    { mDisplayPresenter.setYBPBrightness(it) },
                    { mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF) }
                )
            )

            else -> {
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
                return
            }
        }

        // 处理亮度调整
        screenConfigs.forEach { config ->
            handleBrightness(config, operation)
        }
    }

    private data class ScreenConfig(
        val getter: () -> Int?,
        val setter: (Int) -> Unit,
        val setAuto: (Int) -> Unit
    )

    /**
     * 处理单个屏幕的亮度调整
     *
     * @param config 屏幕配置信息
     * @param operation 亮度调整操作指令
     */
    private fun handleBrightness(config: ScreenConfig, operation: Int) {
        val current = config.getter() ?: run {
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
            return
        }

        val uiModeManager = context.getSystemService(Context.UI_MODE_SERVICE) as? UiModeManager
        val isNightMode = uiModeManager?.nightMode == UiModeManager.MODE_NIGHT_YES

        val targetValue = calculateTargetValue(operation, current, isNightMode) ?: return

        when {
            operation == Constant.FONT_SIZE_BIG && current >= CarSettingConstant.SCREEN_BRIGHTNESS_MAX -> {
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            }

            operation == Constant.FONT_SIZE_SMALL && current <= CarSettingConstant.SCREEN_BRIGHTNESS_MIN -> {
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            }

            targetValue != current -> {
                config.setter(targetValue)
                config.setAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                sendAdjustSuccessCode(operation)
            }

            else -> {
                sendCurrentLevelCode(operation)
            }
        }
    }

    /**
     * 计算目标亮度值
     *
     * @param operation 亮度调整操作指令
     * @param current 当前亮度值
     * @param isNightMode 是否为夜间模式
     * @return 目标亮度值，如果操作无效返回null
     */
    private fun calculateTargetValue(operation: Int, current: Int, isNightMode: Boolean): Int? {
        return when (operation) {
            Constant.FONT_SIZE_BIG ->
                (current + 2).coerceAtMost(CarSettingConstant.SCREEN_BRIGHTNESS_MAX)

            Constant.FONT_SIZE_SMALL ->
                (current - 2).coerceAtLeast(CarSettingConstant.SCREEN_BRIGHTNESS_MIN)

            Constant.FONT_SIZE_MAX, Constant.FONT_SIZE_HIGH ->
                CarSettingConstant.SCREEN_BRIGHTNESS_MAX

            Constant.FONT_SIZE_MIN, Constant.FONT_SIZE_LOW ->
                CarSettingConstant.SCREEN_BRIGHTNESS_MIN

            Constant.FONT_SIZE_MIDDLE ->
                if (isNightMode) 2 else 8

            else -> null
        }
    }

    /**
     * 发送亮度调整成功响应码
     *
     * @param operation 亮度调整操作指令
     */
    private fun sendAdjustSuccessCode(operation: Int) {
        val resultCode = when (operation) {
            Constant.FONT_SIZE_BIG,
            Constant.FONT_SIZE_MAX,
            Constant.FONT_SIZE_HIGH -> VDVRRespondID.adjust_screen_brightness_to_number_3

            Constant.FONT_SIZE_SMALL,
            Constant.FONT_SIZE_MIN,
            Constant.FONT_SIZE_LOW -> VDVRRespondID.adjust_screen_brightness_to_number_4

            else -> VDVRRespondID.adjust_screen_brightness_to_number_3
        }
        sendResultCode(resultCode)
    }

    /**
     * 发送当前亮度状态响应码
     *
     * @param operation 亮度调整操作指令
     */
    private fun sendCurrentLevelCode(operation: Int) {
        when (operation) {
            Constant.FONT_SIZE_MAX ->
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_max_2)

            Constant.FONT_SIZE_MIN ->
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_min_2)

            Constant.FONT_SIZE_HIGH ->
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_2, "高")

            Constant.FONT_SIZE_MIDDLE ->
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_2, "中")

            Constant.FONT_SIZE_LOW ->
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_2, "低")
        }
    }
    /**
     * 设置屏幕亮度为自动调节
     * @param value 0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
     */
    fun setScreenBrightnessAuto(value: String) {
        if (value.toInt() == Constant.SCREEN_ZKP_CONTROL_CENTER) {
            val signal = mDisplayPresenter.getZKPAuto()
            if (signal == CarSettingConstant.SCREEN_AUTO_OFF) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_ON)
                sendResultCode(VDVRRespondID.open_screen_brightness_auto_1)
            } else {
                sendResultCode(VDVRRespondID.open_screen_brightness_auto_2)
            }
        } else if (value.toInt() == Constant.SCREEN_YBP_CONTROL_CENTER) {
            val signal = mDisplayPresenter.getYBPAuto()
            if (signal == CarSettingConstant.SCREEN_AUTO_OFF) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_ON)
                sendResultCode(VDVRRespondID.open_screen_brightness_auto_1)
            } else {
                sendResultCode(VDVRRespondID.open_screen_brightness_auto_2)
            }
        } else if (value.toInt() == Constant.SCREEN_ALL_CONTROL_CENTER || value.toInt() == Constant.SCREEN_UNSPECIFIED_CONTROL_CENTER) {
            val signal1 = mDisplayPresenter.getZKPAuto()
            val signal2 = mDisplayPresenter.getYBPAuto()
            if (signal1 == CarSettingConstant.SCREEN_AUTO_OFF || signal2 == CarSettingConstant.SCREEN_AUTO_OFF) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_ON)
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_ON)
                sendResultCode(VDVRRespondID.open_screen_brightness_auto_1)
            } else {
                sendResultCode(VDVRRespondID.open_screen_brightness_auto_2)
            }
        } else if (value.toInt() == Constant.SCREEN_NO_CONTROL_CENTER || value.toInt() == Constant.SCREEN_HP_CONTROL_CENTER) {
            sendResultCode(VDVRRespondID.open_screen_brightness_auto_3)
        }
    }

    /**
     * 关闭屏幕亮度自动调节
     * @param value 0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
     */
    fun setClosedScreenBrightnessAuto(value: String) {
        if (value.toInt() == Constant.SCREEN_ZKP_CONTROL_CENTER) {
            val signal = mDisplayPresenter.getZKPAuto()
            if (signal != CarSettingConstant.SCREEN_AUTO_OFF) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                sendResultCode(VDVRRespondID.close_screen_brightness_auto_1)
            } else {
                sendResultCode(VDVRRespondID.close_screen_brightness_auto_2)
            }
        } else if (value.toInt() == Constant.SCREEN_YBP_CONTROL_CENTER) {
            val signal = mDisplayPresenter.getYBPAuto()
            if (signal != CarSettingConstant.SCREEN_AUTO_OFF) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                sendResultCode(VDVRRespondID.close_screen_brightness_auto_1)
            } else {
                sendResultCode(VDVRRespondID.close_screen_brightness_auto_2)
            }
        } else if (value.toInt() == Constant.SCREEN_ALL_CONTROL_CENTER || value.toInt() == Constant.SCREEN_UNSPECIFIED_CONTROL_CENTER) {
            val signal = mDisplayPresenter.getZKPAuto()
            val signal2 = mDisplayPresenter.getYBPAuto()
            if (signal != CarSettingConstant.SCREEN_AUTO_OFF || signal2 != CarSettingConstant.SCREEN_AUTO_OFF) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                sendResultCode(VDVRRespondID.close_screen_brightness_auto_1)
            } else {
                sendResultCode(VDVRRespondID.close_screen_brightness_auto_2)
            }
        } else if (value.toInt() == Constant.SCREEN_NO_CONTROL_CENTER || value.toInt() == Constant.SCREEN_HP_CONTROL_CENTER) {
            sendResultCode(VDVRRespondID.close_screen_brightness_auto_3)
        }
    }

    /**
     * 屏幕亮度调大数值
     * @param value 0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
     */
    fun setScreenValueUp(value: String) {
        val parts = value.split("-")
        val param1 =
            parts[0].toIntOrNull() ?: throw IllegalArgumentException("第一个参数必须为整数")
        val param2Str = parts[1]
        if (param2Str.contains(".")) {
            // 是小数：发送特定响应码
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_8)
            return // 终止后续逻辑
        }
        val param2 =
            param2Str.toIntOrNull() ?: throw IllegalArgumentException("第二个参数必须为整数")
        //调节中控屏亮度
        if (param1 == Constant.SCREEN_ZKP_CONTROL_CENTER) {
            val signal = mDisplayPresenter.getZKPBrightness()
            if (signal + param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(signal + param2)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_4)
            }
            if (signal + param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MAX)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            }
        }
        //调节仪表屏幕亮度
        else if (param1 == Constant.SCREEN_YBP_CONTROL_CENTER) {
            val signal = mDisplayPresenter.getYBPBrightness()
            if (signal + param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = signal + param2
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_4)
            }
            if (signal + param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MAX
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            }
        }
        //调节非指定屏幕、全车亮度
        else if (param1 == Constant.SCREEN_ALL_CONTROL_CENTER || param1 == Constant.SCREEN_UNSPECIFIED_CONTROL_CENTER) {
            val signal1 = mDisplayPresenter.getZKPBrightness()
            val signal2 = mDisplayPresenter.getYBPBrightness()
            if (signal1 + param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX && signal2 + param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(signal1 + param2)
                mDisplayPresenter.ybpBrightness = signal2 + param2
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_3)
            } else if (signal1 + param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX && signal2 + param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(signal1 + param2)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MAX
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_3)
            } else if (signal1 + param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX && signal2 + param2 <= CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MAX)
                mDisplayPresenter.ybpBrightness = signal2 + param2
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_3)
            } else if (signal1 + param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX && signal2 + param2 > CarSettingConstant.SCREEN_BRIGHTNESS_MAX) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MAX)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MAX
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_5)
            }

        }
        //无此屏幕、后排屏
        else if (param1 == Constant.SCREEN_HP_CONTROL_CENTER || param1 == Constant.SCREEN_NO_CONTROL_CENTER) {
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
        }
    }


    /**
     * 屏幕亮度调低数值
     * @param value 0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
     */
    fun setScreenValueDown(value: String) {
        val parts = value.split("-")
        val param1 =
            parts[0].toIntOrNull() ?: throw IllegalArgumentException("第一个参数必须为整数")
        val param2Str = parts[1]
        if (param2Str.contains(".")) {
            // 是小数：发送特定响应码
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_8)
            return // 终止后续逻辑
        }
        val param2 =
            param2Str.toIntOrNull() ?: throw IllegalArgumentException("第二个参数必须为整数")
        //调节中控屏亮度
        if (param1 == Constant.SCREEN_ZKP_CONTROL_CENTER) {
            val state = mDisplayPresenter.getZKPBrightness()
            if (state - param2 >= CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(state - param2)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_4)
            } else {
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MIN)
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            }
        }
        //调节仪表屏幕亮度
        else if (param1 == Constant.SCREEN_YBP_CONTROL_CENTER) {
            val state = mDisplayPresenter.getYBPBrightness()
            if (state - param2 >= CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = state - param2
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_4)
            } else {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MIN
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            }
        }
        //调节非指定屏幕、全车亮度
        else if (param1 == Constant.SCREEN_ALL_CONTROL_CENTER || param1 == Constant.SCREEN_UNSPECIFIED_CONTROL_CENTER) {
            val state1 = mDisplayPresenter.getZKPBrightness()
            val state2 = mDisplayPresenter.getYBPBrightness()
            if (state1 - param2 >= CarSettingConstant.SCREEN_BRIGHTNESS_MIN && state2 - param2 >= CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(state1 - param2)
                mDisplayPresenter.ybpBrightness = state2 - param2
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_4)
            } else if (state1 - param2 >= 1 && state2 - param2 < 1) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(state1 - param2)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MIN
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_4)
            } else if (state1 - param2 < CarSettingConstant.SCREEN_BRIGHTNESS_MIN && state2 - param2 >= CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MIN)
                mDisplayPresenter.ybpBrightness = state2 - param2
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_4)
            } else if (state1 - param2 < CarSettingConstant.SCREEN_BRIGHTNESS_MIN && state2 - param2 < CarSettingConstant.SCREEN_BRIGHTNESS_MIN) {
                mDisplayPresenter.setYBPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPAuto(CarSettingConstant.SCREEN_AUTO_OFF)
                mDisplayPresenter.setZKPBrightness(CarSettingConstant.SCREEN_BRIGHTNESS_MIN)
                mDisplayPresenter.ybpBrightness = CarSettingConstant.SCREEN_BRIGHTNESS_MIN
                sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_6)
            }

        }
        //无此屏幕、后排屏
        else if (param1 == Constant.SCREEN_HP_CONTROL_CENTER || param2 == Constant.SCREEN_NO_CONTROL_CENTER) {
            sendResultCode(VDVRRespondID.adjust_screen_brightness_to_number_7)
        }
    }

    /**
     * 设置系统颜色，指定模式
     * @param value 0=蓝色，1=橙色, 2=红色, 3=绿色, 4=青色, 5=紫色
     */
    fun setSystemColorSpecify(value: String) {
        val color = mDisplayManager.systemColor
        when (value.toInt()) {
            Constant.COLOR_BLUE -> {
                if (color == CarSettingConstant.COLOR_BLUE) {
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_2, "蓝色")
                } else {
                    mDisplayManager.systemColor = CarSettingConstant.COLOR_BLUE
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_1, "蓝色")
                }
            }

            Constant.COLOR_ORANGE -> {
                if (color == CarSettingConstant.COLOR_ORANGE) {
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_2, "橙色")
                } else {
                    mDisplayManager.systemColor = CarSettingConstant.COLOR_ORANGE
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_1, "橙色")
                }
            }

            Constant.COLOR_RED -> {
                if (color == CarSettingConstant.COLOR_RED) {
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_2, "红色")
                } else {
                    mDisplayManager.systemColor = CarSettingConstant.COLOR_RED
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_1, "红色")
                }
            }

            Constant.COLOR_GREEN -> {
                if (color == CarSettingConstant.COLOR_GREEN) {
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_2, "绿色")
                } else {
                    mDisplayManager.systemColor = CarSettingConstant.COLOR_GREEN
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_1, "绿色")
                }
            }

            Constant.COLOR_CYAN -> {
                if (color == CarSettingConstant.COLOR_CYAN) {
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_2, "青色")
                } else {
                    mDisplayManager.systemColor = CarSettingConstant.COLOR_CYAN
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_1, "青色")
                }
            }

            Constant.COLOR_PURPLE -> {
                if (color == CarSettingConstant.COLOR_PURPLE) {
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_2, "紫色")
                } else {
                    mDisplayManager.systemColor = CarSettingConstant.COLOR_PURPLE
                    sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_1, "紫色")
                }
            }
        }
    }

    /**
     * 设置系统颜色，无指定模式
     */
    fun setSystemColorNoSpecify(flag: Boolean) {
        val color = mDisplayManager.systemColor
        when (color) {
            CarSettingConstant.COLOR_BLUE -> {
                mDisplayManager.systemColor = CarSettingConstant.COLOR_PURPLE
                sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_3, "紫色")
            }

            CarSettingConstant.COLOR_PURPLE -> {
                mDisplayManager.systemColor = CarSettingConstant.COLOR_CYAN
                sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_3, "青色")
            }

            CarSettingConstant.COLOR_CYAN -> {
                mDisplayManager.systemColor = CarSettingConstant.COLOR_GREEN
                sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_3, "绿色")
            }

            CarSettingConstant.COLOR_GREEN -> {
                mDisplayManager.systemColor = CarSettingConstant.COLOR_ORANGE
                sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_3, "橙色")
            }

            CarSettingConstant.COLOR_ORANGE -> {
                mDisplayManager.systemColor = CarSettingConstant.COLOR_RED
                sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_3, "红色")
            }

            CarSettingConstant.COLOR_RED -> {
                mDisplayManager.systemColor = CarSettingConstant.COLOR_BLUE
                sendResultCode(VDVRRespondID.Set_system_color_and_specify_mode_3, "蓝色")
            }
        }
    }

    /**
     * 屏幕蓝光过滤功能开关
     * @param flag true=打开，false=关闭
     */
    fun setScreenBlueClean(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_blue_ray_filtering_1)
        }
        else {
            sendResultCode(VDVRRespondID.close_blue_ray_filtering_1)
        }
    }

    /**
     * 设置蓝光过滤强度等级
     */
    fun setBlueLightFilterLevel(value: String) {
        sendResultCode(VDVRRespondID.adjust_blue_ray_filtering_to_gear_1)
    }

    /**
     * 屏幕导航光效开关
     * @param flag true=打开，false=关闭
     */
    fun setScreenNavigationLight(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_screen_navigation_lighting_effect_1)
        }
        else {
            sendResultCode(VDVRRespondID.close_screen_navigation_lighting_effect_1)
        }
    }

    /**
     * 跨屏具体内容切换
     */
    fun setScreenCheck(){
        sendResultCode(VDVRRespondID.cross_screen_content_switch_with_info_1)
    }

    /**
     * 跨屏具体内容移动
     */
    fun setScreenCheckMove(value: String) {
        sendResultCode(VDVRRespondID.cross_screen_content_move_with_info_1)
    }

    /**
     * 跨屏具体内容交换
     */
    fun setScreenCheckExchange(value: String) {
        sendResultCode(VDVRRespondID.cross_screen_content_exchange_with_info_1)
    }

    /**
     * 屏幕内容移动
     */
    fun screenValueMove(value: String) {
        sendResultCode(VDVRRespondID.screen_content_move_with_info_1)
    }

    /**
     * 屏幕内容切换
     */
    fun screenValueCheck(value: String) {
        sendResultCode(VDVRRespondID.past_screen_content_1)
    }

    /**
     * 屏幕内容换成指定个数
     */
    fun screenValueNumber(value: String) {
        sendResultCode(VDVRRespondID.set_screen_content_to_number_1)
    }

    /**
     * 切换主题，无指定值
     */
    fun switchThemes() {
        sendResultCode(VDVRRespondID.switch_theme_1)
    }

    /**
     * 主题类别开关
     * @param flag true=打开，false=关闭
     */
    fun openThemeCategory(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_theme_type_1)
        } else {
            sendResultCode(VDVRRespondID.close_theme_type_1)
        }
    }

    /**
     * 移除主题
     */
    fun removeTheme(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.delete_theme_1)
        }
    }

    /**
     * 下载主题
     */
    fun downloadTheme(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.download_theme_1)
        }
    }

    /**
     * 主题自动切换功能开关
     * @param flag true=打开，false=关闭
     */
    fun openThemeAutoSwitch(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.enable_automatic_theme_switching_1)
        } else {
            sendResultCode(VDVRRespondID.close_automatic_theme_switching_1)
        }
    }

    /**
     * 切换摄像头，无指定值
     */
    fun setCamera(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.switch_camera_1)
        }
    }

    /**
     * 打开车门控制界面
     * @param flag true:打开
     */
    fun openDoorControl(flag: Boolean) {
        if(flag){
        sendResultCode(VDVRRespondID.open_the_door_control_interface_3)}
        else{
            sendResultCode(VDVRRespondID.close_the_door_control_interface_3)
        }
    }

    /**
     * 打开悬架设置界面
     */
    fun openSuspensionControl(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_suspension_settings_interface_3)
    }

    /**
     * 打开 打开数字交互信号灯设置界面
     */
    fun digitSignalLightControl(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_digital_interactive_signal_light_settings_interface_1)
    }

    /**
     * 打开开门预警界面
     */
    fun openDoorWarning(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_door_opening_warning_interface_1)
    }

    /**
     * 打开加速踏板防误踩界面
     */
    fun openAcceleratorPedalAnti(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_acceleration_pedal_mis_step_prevention_interface_1)
    }

    /**
     * 打开紧急转向辅助界面
     */
    fun openEmergencyTurnAssist(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_emergency_steering_assistance_interface_1)
    }

    /**
     * 打开限速偏移界面
     */
    fun openSpeedOffset(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_speed_limit_deviation_interface_1)
    }

    /**
     * 打开电动手套箱密码软开关
     */
    fun openElectricGloveBoxPasswordSoftSwitch(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_electric_glove_box_password_soft_switch_3)
    }

    /**
     * 打开安全设置界面
     */
    fun openSecuritySettings(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_safety_settings_interface_3)
    }

    /**
     * 电动管柱调节界面
     */
    fun electricColumn(flag: Boolean) {
        sendResultCode(VDVRRespondID.electric_pipe_column_adjustment_interface_1)
    }

    /**
     * 打开智能钥匙界面
     */
    fun openIntelligentKey(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_smart_key_interface_3)
    }

    /**
     * 打开环境舒适界面
     */
    fun openEnvironmentComfort(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_environment_comfort_interface_3)
    }

    /**
     * 打开车辆状态显示界面
     */
    fun openVehicleStatusDisplay(flag: Boolean) {
        sendResultCode(VDVRRespondID.open_the_vehicle_status_display_interface_3)
    }

    /**
     * 星动吧台界面开关
     */
    fun openStarDanceTable(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_the_Xingdong_Bar_interface_3)
        } else {
            sendResultCode(VDVRRespondID.close_the_Xingdong_Bar_interface_3)
        }
    }

    /**
     * 系统调试模式开关
     */
    fun openSystemDebugMode(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_system_debugging_mode_1)
        } else {
            sendResultCode(VDVRRespondID.close_system_debugging_mode_1)
        }
    }

    /**
     * 自动时区开关
     * @param flag true:打开 false:关闭
     */
    fun openAutoTimeZone(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_automatic_time_zone_1)
        } else {
            sendResultCode(VDVRRespondID.close_automatic_time_zone_1)
        }
    }

    /**
     * 系统时区设置为指定区域
     */
    fun setSystemTimeZone(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_time_zone_1)
        }
    }

    /**
     * 设置温度单位
     */
    fun setTemperatureUnit(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_temperature_unit_1)
        }
    }

    /**
     * 设置压强单位
     */
    fun setPressureUnit(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_pressure_unit_1)
        }
    }

    /**
     * 设置电耗单位
     */
    fun setEnergyUnit(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_electricity_consumption_unit_1)
        }
    }

    /**
     * 设置功率显示单位
     */
    fun setPowerUnit(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_power_display_unit_1)
        }
    }

    /**
     * 设置距离显示单位
     */
    fun setDistanceUnit(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_distance_display_unit_1)
        }
    }

    /**
     * 倒计时设置
     */
    fun setCountDown(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_countdown_time_1)
        }
    }

    /**
     * 暂停、继续、删除倒计时
     */
    fun controlCountDown(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.pause_countdown_time_1)
        }
    }


    /**
     * 将指定业务的默认软件设置成指定app
     */
    fun setDefaultApp(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.set_default_app_1)
        }
    }

    /**
     * 屏幕应用图标缩放
     */
    fun setScreenAppZoom(value: String) {
        sendResultCode(VDVRRespondID.screen_module_zoom_out_1)
    }

    /**
     * 弹窗开关
     */
    fun openSystemAlert(flag: Boolean) {
        if (flag) {
            sendResultCode(VDVRRespondID.open_pop_up_1)
        } else {
            sendResultCode(VDVRRespondID.close_pop_up_1)
        }
    }

    /**
     * 按键亮度调高具体数值
     */
    fun screenValueUp(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.raise_button_brightness_by_number_1)
        }
    }

    /**
     * 按键亮度调低具体数值
     */
    fun screenValueDown(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.lower_button_brightness_by_number_1)
        }
    }

    /**
     * 仪表电量显示类型设置为xxx
     */
    fun screenValueRegulate(value: String) {
        if (value != "") {
            sendResultCode(VDVRRespondID.instrument_power_display_settings_3)
        }
    }

    /**
     * 打开or关闭仪表显示歌词
     */
    fun openOrcloseShowLyrics(flag: Boolean){
        if (flag){
            if (DisplayPresenter.getShowLyrics() == CarDisplay.Lyrics.HIDE) {
                DisplayPresenter.setShowLyrics(CarDisplay.Lyrics.SHOW)
                sendResultCode(VDVRRespondID.open_instrument_display_lyrics_2)
            }else{
                sendResultCode(VDVRRespondID.open_instrument_display_lyrics_1)
            }
        }else{
            if (DisplayPresenter.getShowLyrics() == CarDisplay.Lyrics.SHOW) {
                DisplayPresenter.setShowLyrics(CarDisplay.Lyrics.HIDE)
                sendResultCode(VDVRRespondID.close_instrument_display_lyrics_1)
            }else{
                sendResultCode(VDVRRespondID.close_instrument_display_lyrics_2)
            }
        }
    }

    /**
     * 设置字体大小
     * @param value 1:小号字体 2:标准(中号)字体 3:大号字体
     */
    fun voiceSetFontSize(value: String){
        when (value.toInt()) {
            //小号字体
            Constant.AR_PARAM_LITTLE -> {
                if (mDisplayPresenter.fontSize == CarSettingConstant.FONT_SIZE_STANDARD) {
                    mDisplayPresenter.setFontSize(CarSettingConstant.FONT_SIZE_LITTLE)
                    sendResultCode(VDVRRespondID.set_adjust_fonts_1,"小号字体")
                } else if (mDisplayPresenter.fontSize == CarSettingConstant.FONT_SIZE_LARGE) {
                    mDisplayPresenter.setFontSize(CarSettingConstant.FONT_SIZE_LITTLE)
                    sendResultCode(VDVRRespondID.set_adjust_fonts_1,"小号字体")
                }else{
                    sendResultCode(VDVRRespondID.set_adjust_fonts_2,"小号字体")
                }
            }
            //标准(中号)字体
            Constant.AR_PARAM_STANDARD -> {
                if (mDisplayPresenter.fontSize == CarSettingConstant.FONT_SIZE_LITTLE) {
                    mDisplayPresenter.setFontSize(CarSettingConstant.FONT_SIZE_STANDARD)
                    sendResultCode(VDVRRespondID.set_adjust_fonts_1,"标准字体")
                } else if (mDisplayPresenter.fontSize == CarSettingConstant.FONT_SIZE_LARGE) {
                    mDisplayPresenter.setFontSize(CarSettingConstant.FONT_SIZE_STANDARD)
                    sendResultCode(VDVRRespondID.set_adjust_fonts_1,"标准字体")
                }else{
                    sendResultCode(VDVRRespondID.set_adjust_fonts_2,"标准字体")
                }
            }
            //大号字体
            Constant.AR_PARAM_LARGE -> {
                if (mDisplayPresenter.fontSize == CarSettingConstant.FONT_SIZE_LITTLE) {
                    mDisplayPresenter.setFontSize(CarSettingConstant.FONT_SIZE_LARGE)
                    sendResultCode(VDVRRespondID.set_adjust_fonts_1,"大号字体")
                } else if (mDisplayPresenter.fontSize == CarSettingConstant.FONT_SIZE_STANDARD) {
                    mDisplayPresenter.setFontSize(CarSettingConstant.FONT_SIZE_LARGE)
                    sendResultCode(VDVRRespondID.set_adjust_fonts_1,"大号字体")
                }else{
                    sendResultCode(VDVRRespondID.set_adjust_fonts_2,"大号字体")
                }
            }
        }
    }

    object Constant {
        //讯飞传入参数
        //0为中控屏，1为仪表屏幕，2为后排屏幕，3为非指定屏，4为全车，99为无此屏幕
        const val SCREEN_ZKP_CONTROL_CENTER: Int = 0
        const val SCREEN_YBP_CONTROL_CENTER: Int = 1
        const val SCREEN_HP_CONTROL_CENTER: Int = 2
        const val SCREEN_UNSPECIFIED_CONTROL_CENTER: Int = 3
        const val SCREEN_ALL_CONTROL_CENTER: Int = 4
        const val SCREEN_NO_CONTROL_CENTER: Int = 99

        //0=大一点，1=小一点，2=最大，3=最小,4=高，5=中，6=低
        const val FONT_SIZE_BIG: Int = 0
        const val FONT_SIZE_SMALL: Int = 1
        const val FONT_SIZE_MAX: Int = 2
        const val FONT_SIZE_MIN: Int = 3
        const val FONT_SIZE_HIGH: Int = 4
        const val FONT_SIZE_MIDDLE: Int = 5
        const val FONT_SIZE_LOW: Int = 6

        //0=蓝色，1=橙色, 2=红色, 3=绿色, 4=青色, 5=紫色
        const val COLOR_BLUE: Int = 0
        const val COLOR_ORANGE: Int = 1
        const val COLOR_RED: Int = 2
        const val COLOR_GREEN: Int = 3
        const val COLOR_CYAN: Int = 4
        const val COLOR_PURPLE: Int = 5
        const val AR_PARAM_LITTLE: Int = 0
        const val AR_PARAM_STANDARD: Int = 1
        const val AR_PARAM_LARGE: Int = 2


        const val AUTHORITY: String = "com.android.launcher3.wallpaperprovider"
        const val METHOD_SHOW_PREV_IMAGE_WALLPAPER = "showPrevImageWallpaper"
        const val METHOD_SHOW_NEXT_IMAGE_WALLPAPER = "showNextImageWallpaper"
    }
}
