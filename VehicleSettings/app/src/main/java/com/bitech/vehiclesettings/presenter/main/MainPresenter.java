package com.bitech.vehiclesettings.presenter.main;

import android.content.Context;
import android.util.Log;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.activity.MainActivity;
import com.bitech.vehiclesettings.databinding.ActivityMainBinding;

import org.libpag.PAGFile;
import org.libpag.PAGView;

public class MainPresenter implements MainPresenterListener {
    private Context mContext;

    public MainPresenter(Context context) {
        mContext = context;
    }

    private String[] dayPagsOn = {"day/vehicle_control_on.pag", "day/light_on.pag", "day/energy_on.pag", "day/drive_on.pag", "day/vehicle_condition_on.pag",
            "day/display_on.pag", "day/connect_on.pag", "day/sound_on.pag", "day/intelligent_assistant_on.pag", "day/system_on.pag"};

    private String[] nightPagsOn = {"night/vehicle_control_on.pag", "night/light_on.pag", "night/energy_on.pag", "night/drive_on.pag", "night/vehicle_condition_on.pag",
            "night/display_on.pag", "night/connect_on.pag", "night/sound_on.pag", "night/intelligent_assistant_on.pag", "night/system_on.pag"};

    private String[] dayPagsOff = {"day/vehicle_control_off.pag", "day/light_off.pag", "day/energy_off.pag", "day/drive_off.pag", "day/vehicle_condition_off.pag",
            "day/display_off.pag", "day/connect_off.pag", "day/sound_off.pag", "day/intelligent_assistant_off.pag", "day/system_off.pag"};

    private String[] nightPagsOff = {"night/vehicle_control_off.pag", "night/light_off.pag", "night/energy_off.pag", "night/drive_off.pag", "night/vehicle_condition_off.pag",
            "night/display_off.pag", "night/connect_off.pag", "night/sound_off.pag", "night/intelligent_assistant_off.pag", "night/system_off.pag"};

    public void menuHandleIntent(ActivityMainBinding binding, int targetTab, int targetDialog, int operation) {
        Log.d("getTagValue", "targetDialog: " + targetDialog + " targetTab: " + targetTab + " operation: " + operation);
        if ((binding != null) && (targetTab != -1)) {
            switch (targetTab) {
                case MainActivity.MainTabIndex.QUICK_CONTROL:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.QUICK_CONTROL) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.QUICK_CONTROL], binding.pvQuickControl);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.QUICK_CONTROL);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.QUICK_CONTROL, false);
                        } else {
                            // todo 已打开返回提示
                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;
                case MainActivity.MainTabIndex.LIGHT:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.LIGHT) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.LIGHT], binding.pvLight);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.LIGHT);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.LIGHT, false);

                        } else {
                            // todo 已打开返回提示

                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;
                case MainActivity.MainTabIndex.NEW_ENERGY:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.NEW_ENERGY) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.NEW_ENERGY], binding.pvNewEnergy);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.NEW_ENERGY);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.NEW_ENERGY, false);
                        } else {
                            // todo 已打开返回提示
                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;

                case MainActivity.MainTabIndex.DRIVE:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.DRIVE) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.DRIVE], binding.pvDrive);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.DRIVE);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.DRIVE, false);
                        } else {
                            // todo 已打开返回提示
                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;

                case MainActivity.MainTabIndex.CONDITION:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.CONDITION) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.CONDITION], binding.pvCondition);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.CONDITION);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.CONDITION, false);
                        } else {
                            // todo 已打开返回提示
                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;

                case MainActivity.MainTabIndex.DISPLAY:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.DISPLAY) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.DISPLAY], binding.pvDisplay);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.DISPLAY);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.DISPLAY, false);
                        } else {
                            // todo 已打开返回提示
                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;


                case MainActivity.MainTabIndex.CONNECT:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.CONNECT) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.CONNECT], binding.pvConnect);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.CONNECT);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.CONNECT, false);
                        } else {
                            // todo 已打开返回提示
                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;

                case MainActivity.MainTabIndex.VOICE:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.VOICE) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.VOICE], binding.pvVoice);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.VOICE);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.VOICE, false);
                            //  打开车速弹框
//                            VoiceCompensationUIAlert.Builder voiceCompensationUIAlert;
//                            if (voiceCompensationUIAlert == null) {
//                                voiceCompensationUIAlert = new VoiceCompensationUIAlert.Builder(mContext, presenter, viewModel, voiceHandler);
//                            }
//                            voiceCompensationUIAlert.create().show();
                        } else {
                            // todo 已打开返回提示
                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;
//                case MainActivity.MainTabIndex.SMART_RECOGNITION:
//                    if (operation == 1) {
//                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.SMART_RECOGNITION) {
//                            // 未打开时，打开当前页
//                            playPagView(getPagsOn()[MainActivity.MainTabIndex.SMART_RECOGNITION], binding.pvSmartRecognition);
//                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.SMART_RECOGNITION);
//                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.SMART_RECOGNITION, false);
//                        } else {
//                            // todo 已打开返回提示
//                        }
//                    } else {
//                        // 关闭应用
//                        closeVehicle();
//                    }
//                    break;
                case MainActivity.MainTabIndex.SYSTEM:
                    if (operation == 1) {
                        if (binding.viewPager.getCurrentItem() != MainActivity.MainTabIndex.SYSTEM) {
                            // 未打开时，打开当前页
                            playPagView(getPagsOn()[MainActivity.MainTabIndex.SYSTEM], binding.pvSystem);
                            binding.setTabSelectedIndex(MainActivity.MainTabIndex.SYSTEM);
                            binding.viewPager.setCurrentItem(MainActivity.MainTabIndex.SYSTEM, false);
                        } else {
                            // todo 已打开返回提示
                        }
                    } else {
                        // 关闭应用
                        closeVehicle();
                    }
                    break;
            }
        }

    }

    private void closeVehicle(){

    }
    public void playPagView(String assets, PAGView pagView) {
        PAGFile pagFile = PAGFile.Load(mContext.getAssets(), assets);
        pagView.setComposition(pagFile);
        pagView.setRepeatCount(1);
        pagView.play();
    }

    public String[] getPagsOn() {
        return Util.isNight(mContext) ? nightPagsOn : dayPagsOn;
    }

    public String[] getPagsOff() {
        return Util.isNight(mContext) ? nightPagsOff : dayPagsOff;
    }
}
