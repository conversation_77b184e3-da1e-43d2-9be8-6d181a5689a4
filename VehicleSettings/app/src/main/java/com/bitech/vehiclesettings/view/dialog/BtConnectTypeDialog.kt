package com.bitech.vehiclesettings.view.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.constraintlayout.widget.ConstraintLayout
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.bean.BtDeviceBean
import com.bitech.vehiclesettings.databinding.DialogBtConnectTypeBinding
import com.bitech.vehiclesettings.manager.CarConfigInfoManager
import com.bitech.vehiclesettings.manager.CarBtManager
import com.bitech.vehiclesettings.utils.Contacts
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: BtConnectTypeDialog
 * 
 * @Date:  2024/6/6 9:41
 * @Description: 蓝牙连接类型弹窗.
 **/
class BtConnectTypeDialog(context: Context, private val deviceBean: BtDeviceBean) :
    Dialog(context, R.style.dialog), View.OnClickListener {

    // 蓝牙配对码弹窗视图
    private lateinit var binding: DialogBtConnectTypeBinding

    // 页面弹窗点击事件callback
    private var confirmDialogClickCallback: OnConfirmDialogClickCallback? = null

    /**
     * 生命周期-创建.
     *
     * @param savedInstanceState 状态保存对象
     */
    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        binding =
            DialogBtConnectTypeBinding
                .bind(layoutInflater.inflate(R.layout.dialog_bt_connect_type, null))
        setContentView(binding.root)
        // 初始化视图
        initView()
        // 初始化数据
        initData()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        LogUtil.d(TAG, "initView : ")
        // 设置对话框窗口属性
        val attributes = window?.attributes
        // 设置弹窗类型-系统弹窗
        attributes?.type = WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG
        attributes?.windowAnimations = 0
        window?.attributes = attributes
        // 外部点击可关闭
        setCanceledOnTouchOutside(true)
        // 页面布局初始化
        refreshLayout(deviceBean)
        // 页面监听设置
        binding.apply {
            // 对话框按钮监听
            dialogConfirmBtn.setOnClickListener(this@BtConnectTypeDialog)
            dialogCancelBtn.setOnClickListener(this@BtConnectTypeDialog)
            // 连接类型按钮监听
            settingsBtTypeAaCpIb.setOnClickListener(this@BtConnectTypeDialog)
            settingsBtTypePhoneIb.setOnClickListener(this@BtConnectTypeDialog)
            settingsBtTypeMusicIb.setOnClickListener(this@BtConnectTypeDialog)
        }
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    /**
     * 初始化页面数据.
     *
     */
    private fun initData() {
        LogUtil.d(TAG, "initData : ")
        binding.apply {
            // 设置连接类型标题
            dialogTitleTv.text = context
                .getString(R.string.bt_connected_type_text, deviceBean.device.name)
            // 默认选中蓝牙电话和音乐连接类型
            settingsBtTypePhoneIb.isSelected = true
            settingsBtTypeMusicIb.isSelected = true
        }
    }

    /**
     * 刷新电话的布局.
     *
     */
    fun refreshLayout(deviceBean: BtDeviceBean) {
        LogUtil.d(TAG, "refreshLayout : ")
        // AA或CP图标初始化
        /*if (deviceBean.isSupportWirelessAA) {
            // 支持AA,则显示AA图标
            binding.settingsBtTypeAaCpIb.visibility = View.VISIBLE
            // 设置AA图标
            binding.settingsBtTypeAaCpIb
                .setImageResource(R.drawable.image_button_bt_android_auto_112)
        } else if (deviceBean.isSupportWirelessCP) {
            // 支持CP,则显示CP图标
            binding.settingsBtTypeAaCpIb.visibility = View.VISIBLE
            // 设置CP图标
            binding.settingsBtTypeAaCpIb
                .setImageResource(R.drawable.image_button_bt_carplay_112)
        } else {
            // 都不支持，则隐藏AA和CP图标
            binding.settingsBtTypeAaCpIb.visibility = View.GONE
        }*/
        //新版ue无需显示AA或CP图标
        binding.settingsBtTypeAaCpIb.visibility = View.GONE
        // 蓝牙电话图标位置初始化
        val phoneLayoutParams = binding.settingsBtTypePhoneIb.layoutParams
                as ConstraintLayout.LayoutParams
        if (!deviceBean.isSupportWirelessCP && !deviceBean.isSupportWirelessAA) {
            // 都不支持,则电话图标显示在左侧
            phoneLayoutParams.endToEnd = ConstraintLayout.LayoutParams.UNSET
        } else {
            // 存在一个支持，则电话图标显示在中间
            phoneLayoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
        }
        //新版ue无需显示AA或CP图标,电话图标显示在左侧
        phoneLayoutParams.endToEnd = ConstraintLayout.LayoutParams.UNSET
        // 重设布局
        binding.settingsBtTypePhoneIb.layoutParams = phoneLayoutParams
        // 重新布局
        binding.settingsBtTypePhoneIb.requestLayout()
    }

    /**
     * dialog 按钮点击事件.
     *
     * @param view View
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.settings_bt_type_aa_cp_ib -> {
                LogUtil.d(TAG, "onClick : aa or cp is click!")
                // AA或CP图标被点击,则设置AA/CP/蓝牙电话/蓝牙音乐选中与否
                setAaOrCpIconSelected()
            }

            R.id.settings_bt_type_phone_ib -> {
                LogUtil.d(TAG, "onClick : phone is click!")
                // 设置蓝牙电话选中与否
                setPhoneIconSelected()
            }

            R.id.settings_bt_type_music_ib -> {
                LogUtil.d(TAG, "onClick : music is click!")
                // 设置蓝牙音乐选中与否
                setMusicIconSelected()
            }

            R.id.dialog_confirm_btn -> {
                LogUtil.d(TAG, "onClick : confirm is click!")
                confirmDialogClickCallback?.onConfirmClick(deviceBean, getConnectType())
                dismiss()
            }

            R.id.dialog_cancel_btn -> {
                LogUtil.d(TAG, "onClick : cancel is click!")
                confirmDialogClickCallback?.onCancelClick()
                dismiss()
            }
        }
    }

    /**
     * 设置AA或CP图标选中与否.
     *
     */
    private fun setAaOrCpIconSelected() {
        LogUtil.d(TAG, "setAaOrCpIconSelected : ")
        if (binding.settingsBtTypeAaCpIb.isSelected) {
            // 当前AA或CP选中,图标被点击后,设置为未选中
            binding.settingsBtTypeAaCpIb.isSelected = false
        } else {
            // 当前AA或CP未选中,图标被点击后,设置为选中
            binding.settingsBtTypeAaCpIb.isSelected = true
            if (deviceBean.isSupportWirelessAA) {
                // 如果支持AA,选中时则设置蓝牙电话也选中,蓝牙音乐取消选中
                binding.settingsBtTypePhoneIb.isSelected = true
                binding.settingsBtTypeMusicIb.isSelected = false
            }
            if (deviceBean.isSupportWirelessCP) {
                // 如果支持CP,选中时则设置蓝牙电话和音乐都取消选中
                binding.settingsBtTypePhoneIb.isSelected = false
                binding.settingsBtTypeMusicIb.isSelected = false
            }
        }
    }

    /**
     * 设置蓝牙电话图标选中与否.
     *
     */
    private fun setPhoneIconSelected() {
        LogUtil.d(TAG, "setPhoneIconSelected : ")
        if (binding.settingsBtTypePhoneIb.isSelected) {
            // 当前蓝牙电话选中,点击后,设置蓝牙电话未选中
            binding.settingsBtTypePhoneIb.isSelected = false
        } else {
            // 当前蓝牙电话未选中,点击后,设置蓝牙电话选中
            binding.settingsBtTypePhoneIb.isSelected = true
            if (deviceBean.isSupportWirelessCP) {
                // 当前设备支持CP,则取消CP选中
                binding.settingsBtTypeAaCpIb.isSelected = false
            }
        }
    }

    /**
     * 设置蓝牙音乐图标选中与否.
     *
     */
    private fun setMusicIconSelected() {
        LogUtil.d(TAG, "setMusicIconSelected : ")
        if (binding.settingsBtTypeMusicIb.isSelected) {
            // 当前蓝牙音乐选中,点击后,设置蓝牙音乐未选中
            binding.settingsBtTypeMusicIb.isSelected = false
        } else {
            // 当前蓝牙音乐未选中,点击后,设置音乐电话选中
            binding.settingsBtTypeMusicIb.isSelected = true
            // 取消CP或AA的选中
            binding.settingsBtTypeAaCpIb.isSelected = false
        }
    }

    /**
     * 获取连接类型.
     *
     * @return 连接类型
     */
    private fun getConnectType(): Int {
        var type = Contacts.BT_CONNECT_ALL
        if (binding.settingsBtTypeAaCpIb.isSelected) {
            type = if (deviceBean.isSupportWirelessCP) {
                if (CarBtManager.instance.hasConnectDeviceAa()) {
                    // 当前存在AA连接,则从AA切换CP
                    Contacts.BT_CONNECT_AA_TO_CP
                } else {
                    // 支持CP，连接CP
                    Contacts.BT_CONNECT_CP
                }
            } else {
                if (CarBtManager.instance.hasConnectDeviceCp()) {
                    // 当前存在CP连接,则从CP切换AA
                    Contacts.BT_CONNECT_CP_TO_AA
                } else {
                    // 支持AA，连接AA
                    Contacts.BT_CONNECT_AA
                }
            }
        } else {
            if (binding.settingsBtTypePhoneIb.isSelected
                && binding.settingsBtTypeMusicIb.isSelected
            ) {
                // 连接蓝牙电话和音乐
                type = Contacts.BT_CONNECT_ALL
            } else if (binding.settingsBtTypePhoneIb.isSelected) {
                // 仅连接蓝牙电话
                type = Contacts.BT_CONNECT_HFP
            } else if (binding.settingsBtTypeMusicIb.isSelected) {
                // 仅连接蓝牙音乐
                type = Contacts.BT_CONNECT_A2DP
            }
        }
        return type
    }

    /**
     * 设置确认按钮点击事件监听.
     *
     * @param callback
     */
    fun setDialogClickCallback(callback: OnConfirmDialogClickCallback) {
        confirmDialogClickCallback = callback
    }

    /**
     * @ClassName: OnConfirmDialogClickCallback
     * 
     * @Date:  2024/6/6 10:10
     * @Description: 确认按钮点击事件回调.
     **/
    interface OnConfirmDialogClickCallback {
        /**
         * 确认按钮被点击.
         *
         * @param deviceBean 连接的蓝牙对象
         * @param connectType 连接类型
         */
        fun onConfirmClick(deviceBean: BtDeviceBean, connectType: Int)

        /**
         * 取消按钮被点击.
         *
         */
        fun onCancelClick()
    }

    companion object {
        // 日志标志位
        private const val TAG = "BtConnectTypeDialog"
    }
}
