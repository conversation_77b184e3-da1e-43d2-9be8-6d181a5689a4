package com.bitech.vehiclesettings.view.newenergy

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.util.TypedValue
import android.view.ContextThemeWrapper
import android.view.WindowManager
import android.widget.SeekBar
import com.bitech.platformlib.interfaces.newenergy.INewEnergyListener
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy
import com.bitech.vehiclesettings.databinding.DialogChargeSocBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.Prefs
import com.bitech.vehiclesettings.utils.PrefsConst
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel

/**
 * 放电下限/充电上限
 */
class ChargeSOCDialog(
    context: Context,
    private val isCharging: Boolean,
    private var mCurrentSOC: Int = 30,
    private val chargeStopSOC: Int = 100,
    private val dischargeStopSOC: Int = 30,
    private val dischargeImmediately: Boolean = false
    //需传入系统色ResID 对应的系统级弹窗layout需要启用系统色应用
) : BaseDialog(
    ContextThemeWrapper(
        context,
        Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue)
    )
) {

    companion object {
        // 日志标志位
        private const val TAG = "ChargeSOCDialog"
        const val CHARGE_SOC_START = 80         // 充电起始基准值（80%）
        const val CHARGE_SOC_END = 100          // 充电结束最大值（100%）
        const val RECOMMEND_CHARGE_STOP = 90    // 推荐充电阈值（90%）
        const val DISCHARGE_SOC_START = 30      // 放电起始基准值（30%）
        const val DISCHARGE_SOC_END = 100       // 放电结束最大值（100%）
    }


    private lateinit var binding: DialogChargeSocBinding
    private var titleText: String = context.getString(
        if (isCharging) R.string.ne_charge_stop_soc else if (dischargeImmediately) R.string.ne_whether_enable_discharge else R.string.ne_discharge_stop_soc
    )

    // 新能源管理对象
    private var carNewEnergyManager = NewEnergyViewModel.newEnergyManager

    // 属性监听回调
    private val carPropertyCallback: INewEnergyListener by lazy {
        object : INewEnergyListener {

            override fun onBatteryLevel(value: Int) {
                LogUtil.d(TAG, "onBatteryLevel= $value")
                // 当前电量SOC值反馈
                mCurrentSOC = value
                binding.currentSOC = mCurrentSOC
                //放电下限不能超过当前电量
                val progress = binding.progress ?: 0
                if (!isCharging && progress + DISCHARGE_SOC_START > mCurrentSOC) {
                    updateProgressUI(mCurrentSOC - DISCHARGE_SOC_START)
                }
            }

            override fun onChargeSocThreshold(value: Int) {
                LogUtil.d(TAG, "onChargeSocThreshold= $value")
                if (isCharging) {
                    updateProgressUI(value - CHARGE_SOC_START)
                }
            }

//            override fun onDischargeSocLowThreshold(value: Int) {
//                LogUtil.d(TAG, "onDischargeSocLowThreshold= $value")
//                //放电下限不能超过当前电量
//                if (!isCharging) {
//                    var temp = value - 30
//                    if (value > mCurrentSOC) {
//                        temp = mCurrentSOC - 30
//                    }
//                    updateProgressUI(temp)
//                }
//            }
        }
    }

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : isCharging = $isCharging")
        binding = DialogChargeSocBinding.bind(
            layoutInflater.inflate(R.layout.dialog_charge_soc, null)
        )

        // 绑定自定义dialog视图
        setContentView(binding.root)
        // 初始化页面视图
        initView()
        // 初始化页面监听
        intiListener()
        // 初始化页面数据
        initData()
    }

    /**
     * 初始化页面视图.
     *
     */
    private fun initView() {
        LogUtil.d(TAG, "initView : ")
        val attributes = window?.attributes
        attributes?.type =
            if (dischargeImmediately) WindowManager.LayoutParams.TYPE_SYSTEM_ALERT else WindowManager.LayoutParams.TYPE_APPLICATION_ATTACHED_DIALOG
        attributes?.width = 1176
        attributes?.height = 800
        window?.attributes = attributes
        binding.apply {
            tvTitle.text = titleText
        }
        val typedValue = TypedValue()
        context.theme.resolveAttribute(R.attr.appPrimaryColor, typedValue, true)
        val appPrimaryColor = typedValue.data
        binding.primaryColor = appPrimaryColor
    }

    fun setTitle(title: String) {
        titleText = title
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        carNewEnergyManager.removeCallback(TAG)
        super.dismiss()
    }

    /**
     * 初始化页面监听.
     *
     */
    private fun intiListener() {
        LogUtil.d(TAG, "intiListener : ")
        //注册监听
        carNewEnergyManager.addCallback(TAG, carPropertyCallback)

        binding.apply {
            btnCancel.setOnClickListener { dismiss() }
            btnConfirm.setOnClickListener { view ->
                val progress = sbChargeValue.progress
                LogUtil.d(
                    TAG,
                    "confirm click : progress = $progress , currentSOC = $mCurrentSOC"
                )
                if (isCharging) {
                    carNewEnergyManager.chargeSocThreshold = progress + CHARGE_SOC_START
                    dismiss()
                } else {
                    carNewEnergyManager.setDischargeSocThreshold(
                        MyApplication.getContext(),
                        progress + DISCHARGE_SOC_START
                    )
                    //是否立即开启外放电
                    if (dischargeImmediately) {
                        view.postDelayed({
                            carNewEnergyManager.setDischargeSwitch(CarNewEnergy.V2LSwitchSts.ON)
                        }, 500)
                    }
                    dismiss()
                }
            }

            sbChargeValue.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?, progress: Int, fromUser: Boolean
                ) {
                    LogUtil.d(
                        TAG, "onProgressChanged : progress = $progress ， fromUser = $fromUser"
                    )
                    if (fromUser) {
                        var temp = progress
                        val targetSoc =
                            if (isCharging) progress + CHARGE_SOC_START else progress + DISCHARGE_SOC_START
                        if (isCharging) {
                            //充电上限不能低于当前电量
                            if (targetSoc < mCurrentSOC) {
                                temp = mCurrentSOC - CHARGE_SOC_START
                            }

                        } else {
                            //放电下限不能超过当前电量
                            if (targetSoc > mCurrentSOC) {
                                temp = mCurrentSOC - DISCHARGE_SOC_START
                            }
                        }
                        updateProgressUI(temp)
                    }
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {}

                override fun onStopTrackingTouch(seekBar: SeekBar?) {}
            })
        }
    }

    /**
     * 初始化页面数据.
     *
     */
    private fun initData() {
        LogUtil.d(TAG, "initData : ")
        binding.charging = isCharging
        //获取当前SOC
        binding.currentSOC = mCurrentSOC

        //默认指示器位置
        val progress = if (isCharging) {
            if (chargeStopSOC > CHARGE_SOC_END) CHARGE_SOC_END - CHARGE_SOC_START else if (chargeStopSOC > CHARGE_SOC_START) chargeStopSOC - CHARGE_SOC_START else 0

        } else {
            //从30%开始
            val value = dischargeStopSOC - CHARGE_SOC_START
            if (dischargeStopSOC > mCurrentSOC) mCurrentSOC - CHARGE_SOC_START else value
        }
        //刷新指示器位置
        updateProgressUI(progress)
    }

    private fun updateProgressUI(progress: Int) {
        binding.progress =
            if (isCharging) progress.coerceIn(
                0,
                CHARGE_SOC_END - CHARGE_SOC_START
            ) else progress.coerceIn(0, DISCHARGE_SOC_END - DISCHARGE_SOC_START)
    }

}