package com.bitech.vehiclesettings.carapi.constants;

import com.bitech.vehiclesettings.R;

public class CarVoice {
    public static class Media {
        public static final int DEFAULT = 12;
        public static final int MAX = 31;
        public static final int MIN = 0;
    }

    public static class Phone {
        public static final int DEFAULT = 12;
        public static final int MAX = 25;
        public static final int MIN = 0;
    }

    public static class Navi {
        public static final int DEFAULT = 5;
        public static final int MAX = 10;
        public static final int MIN = 0;
    }

    public static class VR {
        public static final int DEFAULT = 5;
        public static final int MAX = 10;
        public static final int MIN = 0;
    }

    /**
     * 声场
     * AMPSoundFocus
     */
    public static class EQ {
        public static final int NOT_ACTIVE = 0x0;
        /**
         * 自定义
         */
        public static final int CUSTOM = 0x1;

        public static final int DEFAULT_POSITION_X = 0x0;
        public static final int DEFAULT_POSITION_Y = 0x0;


        /**
         * 全车均衡
         */
        public static final int ALL = 0x2;
        /**
         * 默认
         */
        public static final int DEFAULT = 0x2;
        /**
         * 主驾均衡
         */
        public static final int DRIVER = 0x3;
        /**
         * 前排均衡
         */
        public static final int SLEEP = 0x4;
        /**
         * 后排均衡
         */
        public static final int VIP = 0x5;
        public static final int RESERVED = 0x7;

        /**
         * 无效值
         */
        public static final int INVALID = -1;

        public static int signalReverse(int signal) {
            switch (signal) {
                case CUSTOM:
                case ALL:
                case DRIVER:
                case SLEEP:
                case VIP:
                    return signal;
                default:
                    return INVALID;
            }
        }
    }

    /**
     * 环绕音
     * SurndFnOnOff
     */
    public static class SurroundSound {
        public static final int NOT_ACTIVE = 0x0;
        /**
         * 开
         */
        public static final int ON = 0x1;
        /**
         * 关
         */
        public static final int OFF = 0x2;
        /**
         * 默认
         */
        public static final int DEFAULT = 0x2;
        public static final int RESERVED = 0x3;
        public static final int INVALID = -1;

        public static int signalReverse(int signal) {
            switch (signal) {
                case ON:
                case OFF:
                    return signal;
                default:
                    return INVALID;
            }
        }
    }

    /**
     * 虚拟现场
     * VirtualSbwfrOnOff_2
     */
    public static class VirtualScene {
        public static final int NOT_ACTIVE = 0x0;
        /**
         * 风云HIFI
         */
        public static final int HIFI = 0x3;
        /**
         * 默认
         */
        public static final int DEFAULT = 0x3;
        /**
         * 演唱会
         */
        public static final int VOCAL = 0x1;
        /**
         * 音乐厅
         */
        public static final int HALL = 0x2;
        /**
         * 体育馆
         */
        public static final int STADIUM = 0x4;
        public static final int INVALID = -1;

        public static int signalReverse(int signal) {
            switch (signal) {
                case HIFI:
                case VOCAL:
                case HALL:
                case STADIUM:
                    return signal;
                default:
                    return INVALID;
            }
        }

        public static int virtualSceneText(int signal) {
            switch (signal) {
                case HIFI:
                    return R.string.str_sound_effect_item1;
                case STADIUM:
                    return R.string.str_sound_effect_item2;
                case VOCAL:
                    return R.string.str_sound_effect_item3;
                case HALL:
                    return R.string.str_sound_effect_item4;
            }
            return R.string.str_sound_effect_item1;
        }
    }

    /**
     * 虚拟现场UI
     */
    public static class VirtualSceneUI {
        /**
         * 风云HIFI
         */
        public static final int HIFI = 0;
        /**
         * 默认
         */
        public static final int DEFAULT = 0;
        /**
         * 演唱会
         */
        public static final int VOCAL = 2;
        /**
         * 音乐厅
         */
        public static final int HALL = 3;
        /**
         * 体育馆
         */
        public static final int STADIUM = 1;
    }

    /**
     * 头枕扬声器
     * STATATAT_Headrest_Mode
     */
    public static class HeadRest {
        public static final int NOT_ACTIVE = 0x0;
        /**
         * 共享
         */
        public static final int SHARE = 0x1;

        /**
         * 默认
         */
        public static final int DEFAULT = 0x1;

        /**
         * 驾享
         */
        public static final int DRIVING = 0x2;
        /**
         * 私享
         */
        public static final int PRIVATE = 0x3;
    }

    /**
     * 均衡调节
     */
    public static class Equalization {
        public static final int NOT_ACTIVE = 0x0;
        public static final int LOW_7 = 0x1;
        public static final int LOW_6 = 0x2;
        public static final int LOW_5 = 0x3;
        public static final int LOW_4 = 0x4;
        public static final int LOW_3 = 0x5;
        public static final int LOW_2 = 0x6;
        public static final int LOW_1 = 0x7;
        public static final int LOW_0 = 0x8;
        public static final int UPP_1 = 0x9;
        public static final int UPP_2 = 0xA;
        public static final int UPP_3 = 0xB;
        public static final int UPP_4 = 0xC;
        public static final int UPP_5 = 0xD;
        public static final int UPP_6 = 0xE;
        public static final int UPP_7 = 0xF;
        public static final int DEFAULT = 0x8;

        public static int signalReverse(int signal) {
            return signal - 1;
        }

        public static int signal2UI(int signal) {
            return signal + 1;
        }
    }

    /**
     * 随速补偿
     * Compensation
     */
    public static class Compensation {
        public static final int GAIN = 30720;
        public static final int INCREASE = 256;
        public static final int NOT_ACTIVE = 0x0;
        public static final int OFF = 0x1;
        public static final int LOW = 0x2;
        public static final int MIDDLE = 0x3;
        public static final int HIGH = 0x4;
        public static final int DEFAULT = 0x3;
        public static final int RESERVED = 0x5;
        public static final int INVALID = -1;

        public static int signalReverse(int signal) {
            switch (signal) {
                case OFF:
                case LOW:
                case MIDDLE:
                case HIGH:
                    return signal;
                case RESERVED:
                    return INVALID;
                default:
                    return INVALID;
            }
        }

        public static abstract class BaseCompensation {
            protected final int[][] gainRanges;
            protected final int maxGain;

            public BaseCompensation(int[][] gainRanges, int maxGain) {
                this.gainRanges = gainRanges;
                this.maxGain = maxGain;
            }

            /**
             * 根据速度值查找对应的增益值
             *
             * @param speed 速度值
             * @return 增益值
             */
            public int conversion(int speed) {
                int left = 0, right = gainRanges.length - 1;
                while (left <= right) {
                    int mid = (left + right) / 2;
                    if (speed >= gainRanges[mid][0] && speed <= gainRanges[mid][1]) {
                        return mid; // 返回对应的增益索引
                    } else if (speed < gainRanges[mid][0]) {
                        right = mid - 1;
                    } else {
                        left = mid + 1;
                    }
                }
                return maxGain; // 默认返回最大增益
            }
        }

        /**
         * 随速补偿_低
         */
        public static class MediaLow extends BaseCompensation {
            private static final int[][] GAIN_RANGES = {
                    {0, 6}, {6, 30}, {30, 69}, {69, 108},
                    {108, 147}, {147, 186}, {186, 225}, {225, Integer.MAX_VALUE}
            };
            private static final int MAX_GAIN = Gain.GAIN_7;

            public static final MediaLow INSTANCE = new MediaLow(); // 单例

            private MediaLow() {
                super(GAIN_RANGES, MAX_GAIN);
            }
        }

        /**
         * 随速补偿_中
         */
        public static class MediaMiddle extends BaseCompensation {
            private static final int[][] GAIN_RANGES = {
                    {0, 14}, {14, 17}, {17, 32}, {32, 47}, {47, 62},
                    {62, 77}, {77, 92}, {92, 107}, {107, 122}, {122, 137},
                    {137, 152}, {152, 167}, {167, 182}, {182, 197},
                    {197, 212}, {212, Integer.MAX_VALUE}
            };
            private static final int MAX_GAIN = Gain.GAIN_15;

            public static final MediaMiddle INSTANCE = new MediaMiddle(); // 单例

            private MediaMiddle() {
                super(GAIN_RANGES, MAX_GAIN);
            }
        }

        /**
         * 随速补偿_高
         */
        public static class MediaHigh extends BaseCompensation {
            private static final int[][] GAIN_RANGES = {
                    {0, 14}, {14, 15}, {15, 24}, {24, 33}, {33, 42},
                    {42, 51}, {51, 60}, {60, 69}, {69, 78}, {78, 87},
                    {87, 96}, {96, 105}, {105, 114}, {114, 123},
                    {123, 132}, {132, Integer.MAX_VALUE}
            };
            private static final int MAX_GAIN = Gain.GAIN_7;

            public static final MediaHigh INSTANCE = new MediaHigh(); // 单例

            private MediaHigh() {
                super(GAIN_RANGES, MAX_GAIN);
            }
        }

        public static class Gain {
            public static final int GAIN_0 = 0;
            public static final int GAIN_1 = 1;
            public static final int GAIN_2 = 2;
            public static final int GAIN_3 = 3;
            public static final int GAIN_4 = 4;
            public static final int GAIN_5 = 5;
            public static final int GAIN_6 = 6;
            public static final int GAIN_7 = 7;
            public static final int GAIN_8 = 8;
            public static final int GAIN_9 = 9;
            public static final int GAIN_10 = 10;
            public static final int GAIN_11 = 11;
            public static final int GAIN_12 = 12;
            public static final int GAIN_13 = 13;
            public static final int GAIN_14 = 14;
            public static final int GAIN_15 = 15;
        }
    }

    public static class CompensationUI {
        public static final int OFF = 0;
        public static final int LOW = 1;
        public static final int MIDDLE = 2;
        public static final int HIGH = 3;

        public static int signalReverse(int signal) {
            switch (signal) {
                case Compensation.OFF:
                    return OFF;
                case Compensation.LOW:
                    return LOW;
                case Compensation.MIDDLE:
                    return MIDDLE;
                case Compensation.HIGH:
                    return HIGH;
            }
            return Compensation.OFF;
        }
    }

    /**
     * AVAS车外低速模拟音
     */
    public static class AVAS {
        /**
         * 开
         */
        public static final int ON = 0x1;
        /**
         * 关
         */
        public static final int OFF = 0x0;
        /**
         * 默认
         */
        public static final int DEFAULT = 0x1;
    }

    /**
     * AVAS车外低速模拟音
     */
    public static class AVAS_ICU {
        /**
         * 开
         */
        public static final int ON = 0x0;
        /**
         * 关
         */
        public static final int OFF = 0x1;
        /**
         * 默认
         */
        public static final int DEFAULT = 0x0;
    }

    /**
     * 外放模式
     */
    public static class ExternalMode {
        public static final int IN = 0;
        public static final int OUT = 1;
        public static final int IN_OUT = 2;
        public static final int DEFAULT = 0;

    }

    /**
     * 导航压低媒体音
     */
    public static class LowerMediaTone {
        public static final int OFF = 0;
        public static final int ON = 1;
        public static final int DEFAULT = 1;
    }

    /**
     * 来电播报
     */
    public static class CallBroadcast {
        public static final int OFF = 0;
        public static final int ON = 1;
        public static final int DEFAULT = 1;
    }

    /**
     * 报警音类型
     */
    public static class AlarmType {
        /**
         * 国风
         */
        public static final int NATIONAL = 1;
        /**
         * 科技
         */
        public static final int TECHNOLOGY = 0;
        /**
         * 新潮
         */
        public static final int SMART = 2;
        public static final int DEFAULT = 0;

        // 转UI
        public static int reverseUI(int value) {
            return switch (value) {
                case NATIONAL -> 0;
                case TECHNOLOGY -> 1;
                case SMART -> 2;
                default -> DEFAULT;
            };
        }
    }

    /**
     * 按键音
     */
    public static class ButtonSound {
        public static final int OFF = 0;
        public static final int ON = 1;
        public static final int DEFAULT = 1;
    }

    /**
     * 试听音屏蔽焦点
     */
    public static class AudioFocus {
        public static final int USAGE_VOICE_COMMUNICATION = 2;
        public static final int USAGE_HICAR_PHONE = 4003;
        public static final int USAGE_CARPLAY_PHONE = 3002;
        public static final int USAGE_GEAR_R = 1001;
    }
}
