package com.bitech.vehiclesettings.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.bean.AppPermissionBean;
import com.bitech.vehiclesettings.bean.PermissionAppBean;
import com.bitech.vehiclesettings.bean.RecordItemBean;

import java.util.List;

public class PermissionAppAdapter extends RecyclerView.Adapter<PermissionAppAdapter.ViewHolder> {
    private final List<AppPermissionBean> permissionList;
    private OnItemClickListener listener;

    public interface OnItemClickListener {
        /**
         * 条目被点击时调用
         * @param position 条目位置
         * @param bean 当前条目数据
         */
        void onItemClick(int position, AppPermissionBean bean);
    }

    public PermissionAppAdapter(List<AppPermissionBean> permissionList, OnItemClickListener listener) {
        this.permissionList = permissionList;
        this.listener = listener;
    }

    public void updateData(List<AppPermissionBean> newList) {
        permissionList.clear();
        permissionList.addAll(newList);
        notifyDataSetChanged();
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_permission_app, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AppPermissionBean bean = permissionList.get(position);
        holder.appName.setText(bean.getAppName());
        holder.permissionType.setText(bean.getLevelLabel());

        // 根据 position 奇偶决定布局方向（修正 spanIndex == -1 问题）
        int direction = (position % 2 == 0) ? View.LAYOUT_DIRECTION_RTL : View.LAYOUT_DIRECTION_LTR;
        holder.itemView.setLayoutDirection(direction);

        // 🔥 添加Log，验证设置结果
        String directionStr = (direction == View.LAYOUT_DIRECTION_RTL) ? "RTL(右对齐)" : "LTR(左对齐)";
        Log.d("PermissionAppAdapter", "onBindViewHolder: position=" + position + ", 设置方向=" + directionStr);

        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onItemClick(position, bean);
            }
        });
    }



    @Override
    public int getItemCount() {
        return permissionList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        RelativeLayout container;
        TextView appName;
        TextView permissionType;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            container = itemView.findViewById(R.id.rl_app);
            appName = itemView.findViewById(R.id.tv_app_name);
            permissionType = itemView.findViewById(R.id.tv_permission_type);
        }
    }
}

