package com.bitech.vehiclesettings.view.display;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.Switch;
import android.widget.Toast;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class VideoLimitUIAlert extends BaseDialog {
    private static final String TAG = VideoLimitUIAlert.class.getSimpleName();

    Switch swVideoLimit;

    public VideoLimitUIAlert(Context context) {
        super(context);
    }

    public VideoLimitUIAlert(Context context, int theme, Switch swVideoLimit) {
        super(context, theme);
        this.swVideoLimit = swVideoLimit;
    }

    protected VideoLimitUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static class Builder {
        public interface OnDialogClickListener {
            void onConfirm();

            void onCancel();
        }

        private final Context context;
        private boolean isCan = true;
        private VideoLimitUIAlert dialog = null;
        private Switch swVideoLimit;
        private View layout;
        private OnDialogClickListener onDialogClickListener;

        public Builder(Context context, Switch swVideoLimit) {
            this.context = context;
            this.swVideoLimit = swVideoLimit;
        }


        public VideoLimitUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public VideoLimitUIAlert.Builder setOnDialogClickListener(VideoLimitUIAlert.Builder.OnDialogClickListener listener) {
            this.onDialogClickListener = listener;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public VideoLimitUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new VideoLimitUIAlert(context, R.style.Dialog, swVideoLimit);
            layout = View.inflate(context, R.layout.dialog_alert_display_video_limit, null);

            Button confirmBtn = layout.findViewById(R.id.btn_confirm);
            confirmBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onDialogClickListener != null) {
                        onDialogClickListener.onConfirm();
                    }
                    dialog.dismiss();
                }
            });
            Button cancelBtn = layout.findViewById(R.id.btn_cancel);
            cancelBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onDialogClickListener != null) {
                        onDialogClickListener.onCancel();
                    }
                    dialog.dismiss();
                }
            });
            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128; // 或者使用具体的像素值
            window.setAttributes(layoutParams);

            return dialog;
        }
    }


    @Override
    public void cancel() {
        super.cancel();
        swVideoLimit.setChecked(true);
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }


}
