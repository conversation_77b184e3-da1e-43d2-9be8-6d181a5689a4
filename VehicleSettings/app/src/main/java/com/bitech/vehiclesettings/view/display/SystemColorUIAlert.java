package com.bitech.vehiclesettings.view.display;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.adapter.SystemColorButtonAdapter;
import com.bitech.vehiclesettings.databinding.DialogAlertDisplaySystemColorBinding;
import com.bitech.vehiclesettings.presenter.display.DisplayPresenter;
import com.bitech.vehiclesettings.utils.SystemColorUtil;

import java.util.Arrays;
import java.util.List;

public class SystemColorUIAlert extends Dialog {
    private static final String TAG = SystemColorUIAlert.class.getSimpleName();
    private static SystemColorUIAlert.onProgressChangedListener onProgressChangedListener;

    public SystemColorUIAlert(Context context) {
        super(context);
    }

    public SystemColorUIAlert(Context context, int theme) {
        super(context, theme);
    }

    protected SystemColorUIAlert(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(SystemColorUIAlert.onProgressChangedListener onProgressChangedListener) {
        SystemColorUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private SystemColorUIAlert dialog = null;
        private View layout;
        DisplayPresenter displayPresenter;
        private RecyclerView recyclerView;
        private final List<Integer> colors = Arrays.asList(
                R.color.system_color_blue, R.color.system_color_purple, R.color.system_color_cyan,
                R.color.system_color_green, R.color.system_color_orange, R.color.system_color_red
        );
        private int selectedColor = R.color.system_color_blue;
        private DialogAlertDisplaySystemColorBinding  binding;
        private SystemColorButtonAdapter adapter = null;
        public Builder(Context context, DisplayPresenter displayPresenter) {
            this.context = context;
            this.displayPresenter = displayPresenter;
        }

        public SystemColorUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public SystemColorUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new SystemColorUIAlert(context,R.style.Dialog);
            layout = View.inflate(context, R.layout.dialog_alert_display_system_color, null);

            layout.findViewById(R.id.btn_confirm).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    changeSystemColor(selectedColor);
                    dialog.cancel();
                }
            });

            recyclerView = layout.findViewById(R.id.recyclerView);
            recyclerView.setLayoutManager(new LinearLayoutManager(layout.getContext(), LinearLayoutManager.HORIZONTAL, false));

            binding = DialogAlertDisplaySystemColorBinding.inflate(LayoutInflater.from(context));

            adapter = new SystemColorButtonAdapter(colors, selectedColor -> {
                // 处理颜色选择事件
                this.selectedColor = selectedColor;
            });
            recyclerView.setOverScrollMode(View.OVER_SCROLL_NEVER);


            recyclerView.setAdapter(adapter);

            dialog.setCancelable(isCan);
            dialog.setContentView(layout);
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1912; // 或者使用具体的像素值
            window.setAttributes(layoutParams);

            initSelected();

            return dialog;
        }

        private void initSelected() {
            int currentTheme = onProgressChangedListener.getSystemColor();
            if (adapter != null) {
                adapter.setSelectedIndex(SystemColorUtil.SystemColorStyleToIndex(currentTheme));
            }
        }

        public void changeSystemColor(int selectedColor) {
            onProgressChangedListener.setSystemColor(selectedColor);
        }
    }


    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface onProgressChangedListener {
        int getSystemColor();
        void setSystemColor(int selectedColor);
    }

}
