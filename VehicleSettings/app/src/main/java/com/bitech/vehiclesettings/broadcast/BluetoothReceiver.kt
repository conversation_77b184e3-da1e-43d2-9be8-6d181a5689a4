package com.bitech.vehiclesettings.broadcast

import android.bluetooth.BluetoothA2dpSink
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothHeadsetClient
import android.bluetooth.BluetoothMapClient
import android.bluetooth.BluetoothPbapClient
import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: BluetoothReceiver
 * 
 * @Date:  2024/1/24 9:56
 * @Description: 蓝牙相关广播接收器.
 **/
class BluetoothReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        LogUtil.d(TAG, "onReceive : action = $action")
        when (action) {
            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                // 获取蓝牙状态
                val state = intent
                    .getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.STATE_OFF)
                LogUtil.d(
                    TAG, "onReceive : blue state = $state " +
                            ", btStateListener size = ${btStateListenerMap?.size}"
                )
                // 监听回调
                for (listener in btStateListenerMap!!.values) {
                    listener.onBluetoothStateChanged(state)
                }
            }

            BluetoothAdapter.ACTION_SCAN_MODE_CHANGED -> {
                val scanMode = intent.getIntExtra(
                    BluetoothAdapter.EXTRA_SCAN_MODE,
                    BluetoothAdapter.SCAN_MODE_CONNECTABLE
                )
                LogUtil.d(TAG, "onReceive : scanMode = $scanMode")
                // 监听回调
                for (listener in btStateListenerMap!!.values) {
                    listener.onBluetoothFoundStateChanged(scanMode)
                }
            }

            BluetoothDevice.ACTION_FOUND -> {
                val device: BluetoothDevice? =
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                val deviceRssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE)
                LogUtil.d(TAG, "onReceive: bt name = ${device?.name} , rssi = $deviceRssi")
                if (device != null) {
                    // 监听回调
                    for (listener in btStateListenerMap!!.values) {
                        listener.onBtScanDevice(device, deviceRssi)
                    }
                }
            }

            BluetoothAdapter.ACTION_DISCOVERY_STARTED -> {
                LogUtil.d(TAG, "onReceive: start scan!")
                // 监听回调
                for (listener in btStateListenerMap!!.values) {
                    listener.onStartScan()
                }
            }

            BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                LogUtil.d(TAG, "onReceive: stop scan!")
                // 监听回调
                for (listener in btStateListenerMap!!.values) {
                    listener.onStopScan()
                }
            }

            BluetoothDevice.ACTION_BOND_STATE_CHANGED -> {
                val device: BluetoothDevice? =
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                val bondState: Int = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, -1)
                if (device != null && bondState != -1) {
                    LogUtil.d(TAG, "onReceive: device = ${device.name} , bondState = $bondState")
                    // 监听回调
                    for (listener in btStateListenerMap!!.values) {
                        listener.onPairedState(bondState, device)
                    }
                }
            }

            BluetoothDevice.ACTION_PAIRING_REQUEST -> {
                val device: BluetoothDevice? =
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                val type = intent.getIntExtra(
                    BluetoothDevice.EXTRA_PAIRING_VARIANT,
                    BluetoothDevice.ERROR
                )
                LogUtil.d(TAG, "onReceive : paired device = ${device?.name} , type = $type ")
                if (device != null && type == BluetoothDevice.PAIRING_VARIANT_PASSKEY_CONFIRMATION) {
                    val pairingKey = intent.getIntExtra(
                        BluetoothDevice.EXTRA_PAIRING_KEY,
                        BluetoothDevice.ERROR
                    )
                    // 格式化蓝牙配对码
                    val pairStringKey = String.format(CODE_FORMAT, pairingKey)
                    LogUtil.d(TAG, "onReceive : paired pairingKey= $pairStringKey")
                    // 监听回调
                    for (listener in btStateListenerMap!!.values) {
                        listener.onPairedCode(device, pairStringKey)
                    }
                }
            }

            BluetoothHeadsetClient.ACTION_CONNECTION_STATE_CHANGED -> {
                // HFP连接设备
                val device: BluetoothDevice? =
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                // HFP之前的连接状态
                val preHfpConnState = intent.getIntExtra(
                    BluetoothProfile.EXTRA_PREVIOUS_STATE,
                    BluetoothProfile.STATE_DISCONNECTED
                )
                // HFP现在的连接状态
                val hfpConnState = intent.getIntExtra(
                    BluetoothProfile.EXTRA_STATE,
                    BluetoothProfile.STATE_DISCONNECTED
                )
                LogUtil.d(
                    TAG,
                    "onReceive : device = ${device?.name} , preHfpConnState = $preHfpConnState , hfpConnState = $hfpConnState"
                )
                if (device != null) {
                    // 监听回调
                    for (listener in btStateListenerMap!!.values) {
                        listener.onBtHfpConnectState(hfpConnState, preHfpConnState, device)
                    }
                }
            }

            BluetoothA2dpSink.ACTION_CONNECTION_STATE_CHANGED -> {
                // A2DP连接设备
                val device: BluetoothDevice? =
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                // A2dp之前的连接状态
                val preA2dpConnState = intent.getIntExtra(
                    BluetoothProfile.EXTRA_PREVIOUS_STATE,
                    BluetoothProfile.STATE_DISCONNECTED
                )
                // A2dp现在的连接状态
                val a2dpConnState = intent.getIntExtra(
                    BluetoothProfile.EXTRA_STATE,
                    BluetoothProfile.STATE_DISCONNECTED
                )
                LogUtil.d(
                    TAG,
                    "onReceive : device = ${device?.name} , preA2dpConnState = $preA2dpConnState , a2dpConnState = $a2dpConnState"
                )
                if (device != null) {
                    // 监听回调
                    for (listener in btStateListenerMap!!.values) {
                        listener.onBtA2dpConnectState(a2dpConnState, preA2dpConnState, device)
                    }
                }
            }

            BluetoothPbapClient.ACTION_CONNECTION_STATE_CHANGED, BluetoothMapClient.ACTION_CONNECTION_STATE_CHANGED -> {
                // PBAP和MAP
                val device: BluetoothDevice? =
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                // Pbap和MAP之前的连接状态
                val prePbapConnState = intent.getIntExtra(
                    BluetoothProfile.EXTRA_PREVIOUS_STATE,
                    BluetoothProfile.STATE_DISCONNECTED
                )
                // Pbap和MAP现在的连接状态
                val pbapConnState = intent.getIntExtra(
                    BluetoothProfile.EXTRA_STATE,
                    BluetoothProfile.STATE_DISCONNECTED
                )
                LogUtil.d(
                    TAG,
                    "onReceive : device = ${device?.name} , prePbapConnState = $prePbapConnState , pbapConnState = $pbapConnState"
                )
                if (device != null) {
                    // 监听回调
                    for (listener in btStateListenerMap!!.values) {
                        listener.onBtPbapAndMapConnectState(pbapConnState, prePbapConnState, device)
                    }
                }
            }

            BluetoothAdapter.ACTION_LOCAL_NAME_CHANGED -> {
                // 蓝牙名称发生改变
                val bluetoothName = intent.getStringExtra(BluetoothAdapter.EXTRA_LOCAL_NAME)
                LogUtil.d(TAG, "onReceive : bt name = $bluetoothName")
                if (bluetoothName != null) {
                    // 监听回调
                    for (listener in btStateListenerMap!!.values) {
                        listener.onBtNameChanged(bluetoothName)
                    }
                }
            }

            BluetoothDevice.ACTION_ACL_DISCONNECTED -> {
                val device: BluetoothDevice? =
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                // ACL链路断开广播
                //TODO 定制接口
//                val errorCode = intent.getIntExtra(BluetoothDevice.EXTRA_ERROR_CODE, -1)
                val errorCode = 0
                LogUtil.i(TAG, "onReceive : device = ${device?.name} , errorCode = $errorCode")
                if (device != null) {
                    // 监听回调
                    for (listener in btStateListenerMap!!.values) {
                        listener.onBtACLDisconnected(device, errorCode)
                    }
                }
            }

            else -> {
                // TODO:
            }
        }
    }

    /**
     * 注册蓝牙状态监听.
     *
     * @param strClassName 类名
     * @param btStateListener 蓝牙状态监听
     */
    fun registerBtStateListener(strClassName: String, btStateListener: BluetoothStateListener) {
        LogUtil.d(
            TAG, "registerBtStateListener : " +
                    "strClassName = $strClassName , " +
                    "btStateListener = $btStateListener"
        )
        // 添加监听
        btStateListenerMap?.set(strClassName, btStateListener)
    }

    /**
     * 注销蓝牙状态监听.
     *
     * @param strClassName 类名
     */
    fun unregisterBtStateListener(strClassName: String?) {
        LogUtil.d(TAG, "unregisterBtStateListener : strClassName = $strClassName")
        // 移除监听
        btStateListenerMap?.remove(strClassName)
    }

    /**
     * @ClassName: BluetoothStateListener
     * 
     * @Date:  2024/1/24 10:09
     * @Description: 蓝牙状态监听.
     **/
    interface BluetoothStateListener {
        /**
         * 蓝牙名称发生改变时回调.
         *
         * @param btName
         */
        fun onBtNameChanged(btName: String)

        /**
         * 蓝牙状态发生改变时回调.
         *
         * @param state 蓝牙状态
         */
        fun onBluetoothStateChanged(state: Int)

        /**
         * 蓝牙可被发现状态改变时回调.
         *
         * @param foundState 蓝牙可被发现状态
         */
        fun onBluetoothFoundStateChanged(foundState: Int)

        /**
         * 发现蓝牙时回调.
         *
         * @param device 蓝牙设备对象
         * @param deviceRssi 蓝牙设备的信号强度
         */
        fun onBtScanDevice(device: BluetoothDevice, deviceRssi: Short)

        /**
         * 开始扫描时回调.
         *
         */
        fun onStartScan()

        /**
         * 停止扫描.
         *
         */
        fun onStopScan()

        /**
         * 配对状态改变时回调.
         *
         * @param pairedState 当前配对状态
         * @param device 配对设备
         */
        fun onPairedState(pairedState: Int, device: BluetoothDevice)

        /**
         * 蓝牙配对校验.
         *
         * @param device 设备
         * @param pairingKey 蓝牙配对码
         */
        fun onPairedCode(device: BluetoothDevice, pairingKey: String)

        /**
         * 蓝牙电话连接过程改变时回调.
         *
         * @param state
         * @param preState
         * @param device
         */
        fun onBtHfpConnectState(state: Int, preState: Int, device: BluetoothDevice)

        /**
         * 蓝牙音乐连接过程改变时回调.
         *
         * @param state
         * @param preState
         * @param device
         */
        fun onBtA2dpConnectState(state: Int, preState: Int, device: BluetoothDevice)

        /**
         * 蓝牙电话本，信息访问连接过程改变时回调.
         *
         * @param state
         * @param preState
         * @param device
         */
        fun onBtPbapAndMapConnectState(state: Int, preState: Int, device: BluetoothDevice)

        /**
         * 当蓝牙ACL链路断开时回调.
         *
         * @param device 断开的设备
         * @param reason 原因
         */
        fun onBtACLDisconnected(device: BluetoothDevice, reason: Int)
    }

    companion object {
        // 日志标志位
        private const val TAG = "BluetoothReceiver"

        // 蓝牙配对码格式
        private const val CODE_FORMAT = "%06d"

        // 蓝牙信息监听
        private var btStateListenerMap: MutableMap<String, BluetoothStateListener>? = HashMap()
    }
}
