package com.bitech.vehiclesettings.view.carsetting;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertCChildLockBinding;
import com.bitech.vehiclesettings.databinding.DialogAlertCMoreSettingBinding;
import com.bitech.vehiclesettings.utils.CommonUtils;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class MoreSettingUIAlert extends BaseDialog {
    private static final String TAG = MoreSettingUIAlert.class.getSimpleName();
    private static MoreSettingUIAlert.onProgressChangedListener onProgressChangedListener;

    public MoreSettingUIAlert(@NonNull Context context) {
        super(context);
    }

    public MoreSettingUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected MoreSettingUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static MoreSettingUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(MoreSettingUIAlert.onProgressChangedListener onProgressChangedListener) {
        MoreSettingUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertCMoreSettingBinding binding;

        String title, unit;
        int iconId, type;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private MoreSettingUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder(Context context, String title, String unit, int iconId, int type) {
            this.context = context;
            this.title = title;
            this.unit = unit;
            this.iconId = iconId;
            this.type = type;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }


        /**
         * Create the custom dialog
         */
        public MoreSettingUIAlert create() {
            if (dialog == null)
                dialog = new MoreSettingUIAlert(context,
                        R.style.Dialog);
            binding = DialogAlertCMoreSettingBinding.inflate(LayoutInflater.from(context));
            dialog.setCancelable(isCan);
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1128;
            layoutParams.height = 800;
            window.setAttributes(layoutParams);

            // 初始化开关
            initSwitch();
            // 初始化进度条
            initSeekBar();
            // 设置文本
            setText();
            // 设置icon
            setIcon();
            // 设置seekBar
            onProgressChangedListener.updateSeekBarUI(type, binding.sbSeekbar, binding.tvSeekbarProgress);
            // 设置seekbar监听
            setSeekbarListener();
            // 设置开关监听
            setSwitchListener();

            // 设置关闭监听
            dialog.setOnCancelListener(dialog -> {
                binding.swSwitch.setOnCheckedChangeListener(null);
            });

            return dialog;
        }

        private void initSeekBar() {
            onProgressChangedListener.initSeekbar(binding.sbSeekbar, binding.tvSeekbarProgress, type);
        }

        private void initSwitch() {
            onProgressChangedListener.initSwitch(binding.swSwitch, binding.sbSeekbar, type);
            setSeekBarEnable(binding.swSwitch.isChecked());
        }

        @SuppressLint("UseCompatLoadingForDrawables")
        private void setSeekBarEnable(boolean isEnable) {
            binding.sbSeekbar.setEnabled(isEnable);

            // 获取当前progress drawable
            Drawable progressDrawable = binding.sbSeekbar.getProgressDrawable();

            if (progressDrawable instanceof LayerDrawable) {
                LayerDrawable layerDrawable = (LayerDrawable) progressDrawable;

                // 获取progress层
                Drawable progress = layerDrawable.findDrawableByLayerId(android.R.id.progress);

                if (progress != null) {
                    if (isEnable) {
                        progress.clearColorFilter(); // 恢复默认颜色
                    } else {
                        progress.setColorFilter(
                                ContextCompat.getColor(context, R.color.gray),
                                PorterDuff.Mode.SRC_IN
                        );
                    }
                }
            }
        }

        private void setSeekbarListener() {
            binding.sbSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    onProgressChangedListener.setOnSeekBarListener(type, seekBar, progress, fromUser);
                }

                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {

                }

                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {

                }
            });
        }

        private void setSwitchListener() {
            binding.swSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                onProgressChangedListener.setOnSwitchListener(type, isChecked);
                setSeekBarEnable(isChecked);
            });
        }

        public DialogAlertCMoreSettingBinding getBinding() {
            return binding;
        }

        private void setIcon() {
            binding.ivSeekbarIcon.setImageResource(iconId);
        }

        private void setText() {
            binding.tvTitle.setText(title);
            binding.tvSwitchText.setText(title);
            binding.tvSeekbarText.setText(unit);
        }

        public void updateSwitch(int status) {
            binding.swSwitch.setChecked(CommonUtils.IntToBool(status));
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void initSwitch(@SuppressLint("UseSwitchCompatOrMaterialCode") Switch switchView, SeekBar seekBar, int type);

        void updateSeekBarUI(int type, SeekBar seekBar, TextView textView);

        void setOnSeekBarListener(int type, SeekBar seekBar, int progress, boolean fromUser);

        void setOnSwitchListener(int type, boolean isChecked);

        void initSeekbar(SeekBar seekBar, TextView textView, int type);
    }
}
