package com.bitech.vehiclesettings.view.system;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSPowerConsumptionUnitBinding;
import com.bitech.vehiclesettings.view.common.SegmentedPickerView;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class PowerConsumptionUnitUIAlert extends BaseDialog {
    private static final String TAG = PowerConsumptionUnitUIAlert.class.getSimpleName();
    private static PowerConsumptionUnitUIAlert.OnProgressChangedListener onProgressChangedListener;
    public static boolean isShow = false;

    public PowerConsumptionUnitUIAlert(@NonNull Context context) {
        super(context);
    }

    public PowerConsumptionUnitUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PowerConsumptionUnitUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static PowerConsumptionUnitUIAlert.OnProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(PowerConsumptionUnitUIAlert.OnProgressChangedListener listener) {
        onProgressChangedListener = listener;
    }

    public static class Builder {
        private final Context context;
        private boolean isCancelable = true;
        private DialogAlertSPowerConsumptionUnitBinding binding;
        private String phoneNumber;
        private boolean isBlueOpen = false;
        public PowerConsumptionUnitUIAlert dialog;

        public Builder(Context context) {
            this.context = context;
        }

        public PowerConsumptionUnitUIAlert.Builder setCancelable(boolean isCancelable) {
            this.isCancelable = isCancelable;
            return this;
        }

        public PowerConsumptionUnitUIAlert.Builder setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
            return this;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public PowerConsumptionUnitUIAlert create() {
            dialog = new PowerConsumptionUnitUIAlert(context, R.style.Dialog);
            binding = DialogAlertSPowerConsumptionUnitBinding.inflate(LayoutInflater.from(context));

            dialog.setCancelable(isCancelable);
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1128; // 或者使用具体的像素值
                layoutParams.height = 508;
                window.setAttributes(layoutParams);
            }
            initPicker();
            return dialog;
        }

        private void initPicker() {
            binding.spvPicker.setItems(R.string.str_power_consumption_unit_Wh, R.string.str_power_consumption_unit_kWh);
            binding.spvPicker.setOnItemSelectedListener(new SegmentedPickerView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(int index, String text) {
                    onProgressChangedListener.onSwitch(index);
                }

                @Override
                public void onItemClicked(int index, String text) {

                }
            });
            binding.spvPicker.setSelectedIndex(onProgressChangedListener.getPowerConsumptionUnit(), false);
        }

        public void updatePowerConsumptionUnitUI(int powerConsumptionUnit) {
            if (dialog != null) {
                binding.spvPicker.setSelectedIndex(powerConsumptionUnit, true);
            }
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

    public interface OnProgressChangedListener {
        int getPowerConsumptionUnit();

        void setPowerConsumptionUnit(int state);

        void onSwitch(int index);
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isShow = false;
    }
}
