package com.bitech.vehiclesettings.utils

import android.util.Log

/**
 * @ClassName: LogUtil
 * 
 * @Date:  2024/1/18 11:16
 * @Description: 日志工具类.
 **/
object LogUtil {

    private const val TAG = "VS_"

    // 日志等级
    private const val VERBOSE = 1
    private const val DEBUG = 2
    private const val INFO = 3
    private const val WARN = 4
    private const val ERROR = 5
    private var level = VERBOSE

    /**
     * V级log(最详细的日志等级，可以输出一些非常详细的调试信息，比如函数的输入参数、返回值等).
     *
     * @param tag 标签
     * @param msg log信息
     */
    fun v(tag: String, msg: String) {
        if (level <= VERBOSE) {
            Log.v(TAG + tag, msg)
        }
    }

    /**
     * D级log(用于输出调试信息，可以输出一些较为详细的调试信息).
     *
     * @param tag 标签
     * @param msg log信息
     */
    fun d(tag: String, msg: String) {
        if (level <= DEBUG) {
            Log.d(TAG + tag, msg)
        }
    }

    /**
     * I级log(用于记录一些重要的应用程序运行信息，例如应用程序启动、网络请求等事件).
     *
     * @param tag 标签
     * @param msg log信息
     */
    fun i(tag: String, msg: String) {
        if (level <= INFO) {
            Log.i(TAG + tag, msg)
        }
    }

    /**
     * W级log(用于记录一些警告信息，例如未处理的异常、低电量等警告信息).
     *
     * @param tag 标签
     * @param msg log信息
     */
    fun w(tag: String, msg: String) {
        if (level <= WARN) {
            Log.w(TAG + tag, msg)
        }
    }

    /**
     * E级log(用于记录一些严重错误信息，例如应用程序崩溃、无法连接网络等错误信息).
     *
     * @param tag 标签
     * @param msg log信息
     */
    fun e(tag: String, msg: String) {
        if (level <= ERROR) {
            Log.e(TAG + tag, msg)
        }
    }
}
