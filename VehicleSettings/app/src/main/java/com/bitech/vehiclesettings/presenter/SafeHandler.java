package com.bitech.vehiclesettings.presenter;


import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import com.bitech.vehiclesettings.utils.MessageConst;

import java.lang.ref.WeakReference;

public class SafeHandler {
    private static final String TAG = SafeHandler.class.getSimpleName();
    private final Handler internalHandler;
    private final WeakReference<SafeHandlerCallback> callbackRef;
    private final Object lock = new Object();

    public SafeHandler(SafeHandlerCallback callback) {
        this(callback, Looper.getMainLooper());
    }

    public SafeHandler(SafeHandlerCallback callback, Looper looper) {
        this.callbackRef = new WeakReference<>(callback);
        this.internalHandler = new Handler(looper) {
            @Override
            public void handleMessage(Message msg) {
                synchronized (lock) {
                    SafeHandlerCallback callback = callbackRef.get();
                    if (callback != null && callback.isActive()) {
                        callback.handleSafeMessage(msg);
                    }
                }
            }
        };
    }

    public void sendMessageDelayed(int what) {
        sendMessageDelayed(what, MessageConst.MSG_FEEDBACK_DELAY);
    }

    public void sendMessage(int what) {
        Message msg = internalHandler.obtainMessage(what);
        internalHandler.removeMessages(what);
        internalHandler.sendMessage(msg);
    }

    public void sendMessage(int what, Bundle bundle) {
        Message msg = internalHandler.obtainMessage(what);
        msg.setData(bundle);
        internalHandler.removeMessages(what);
        internalHandler.sendMessage(msg);
    }

    public void sendMessageDelayed65(int what) {
        sendMessageDelayed(what, MessageConst.MSG_FEEDBACK_DELAY_65);
    }

    // 发送消息（带生命周期检查）
    public void sendMessageDelayed(int msgId, long delayMillis) {
        Log.d(TAG, "发送延迟消息 ID:" + msgId + " 延迟:" + delayMillis);
        synchronized (lock) {
            performSafetyCheck(() -> {
                Message msg = internalHandler.obtainMessage(msgId);
                internalHandler.removeMessages(msgId);
                internalHandler.sendMessageDelayed(msg, delayMillis);
            });
        }
    }

    private void performSafetyCheck(Runnable action) {
        if (callbackRef.get() != null && callbackRef.get().isActive()) {
            action.run();
        }
    }

    // 执行任务（带双重检查）
    public void postDelayed(Runnable task, long delay) {
        synchronized (lock) {
            if (callbackRef.get() != null && callbackRef.get().isActive()) {
                internalHandler.postDelayed(() -> {
                    synchronized (lock) {
                        if (callbackRef.get() != null && callbackRef.get().isActive()) {
                            task.run();
                        }
                    }
                }, delay);
            }
        }
    }

    // 清理资源
    public void dispose() {
        synchronized (lock) {
            Log.d(TAG, "执行资源清理");
            internalHandler.removeCallbacksAndMessages(null);
            callbackRef.clear();
        }
    }

    // 按消息ID移除延时任务/消息
    public void removeMessages(int what) {
        synchronized (lock) {
            Log.d(TAG, "移除消息 ID:" + what);
            internalHandler.removeMessages(what);
        }
    }

    // 移除所有消息和任务（但不清理callback）
    public void removeAllMessages() {
        synchronized (lock) {
            Log.d(TAG, "移除所有消息和任务");
            internalHandler.removeCallbacksAndMessages(null);
        }
    }
}
