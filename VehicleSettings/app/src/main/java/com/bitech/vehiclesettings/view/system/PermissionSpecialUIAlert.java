package com.bitech.vehiclesettings.view.system;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionSpecialBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class PermissionSpecialUIAlert extends BaseDialog {
    private static final String TAG = PermissionSpecialUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;
    private boolean isNormalDismiss = true;


    public PermissionSpecialUIAlert(@NonNull Context context) {
        super(context);
    }

    public PermissionSpecialUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PermissionSpecialUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static PermissionSpecialUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        PermissionSpecialUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSPermissionSpecialBinding binding;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        public PermissionSpecialUIAlert dialog = null;
        private View layout;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public PermissionSpecialUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PermissionSpecialUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSPermissionSpecialBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 528;
            window.setAttributes(layoutParams);

            initSwitch();

            initTips();

            return dialog;
        }

        private void initTips() {

        }

        private void initSwitch() {
            binding.swAnalysis.setOnClickListener(buttonView -> {
                onProgressChangedListener.isSwitch(binding.swAnalysis.isChecked());
                if (binding.swAnalysis.isChecked()) {
                    dialog.isNormalDismiss = false;
                    onProgressChangedListener.openCancel(binding);
//                    binding.swAnalysis.setChecked(false);
                } else {
                    binding.swAnalysis.setChecked(true);
                }
            });
            binding.swAnalysis.setChecked(onProgressChangedListener.getSwitch() == 1);
        }

        /**
         * 获取bind
         * @return
         */
        public DialogAlertSPermissionSpecialBinding getBinding() {
            return binding;
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
        if (isNormalDismiss) {
            PermissionUIAlert.Builder permissionUIAlert = new PermissionUIAlert.Builder(this.getContext());
            permissionUIAlert.create().show();
        }
        isNormalDismiss = true;
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void isSwitch(boolean isChecked);

        int getSwitch();

        void openCancel(DialogAlertSPermissionSpecialBinding binding);
    }
}
