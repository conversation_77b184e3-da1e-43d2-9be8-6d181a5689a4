package com.bitech.vehiclesettings.view.negative;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.platformlib.manager.QuickManager;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarQuickControl;
import com.bitech.vehiclesettings.databinding.DialogAlertSunShadeBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class SunShadeUIAlert extends BaseDialog {

    public SunShadeUIAlert(@NonNull Context context) {
        super(context);
    }

    public SunShadeUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected SunShadeUIAlert(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private SunShadeUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSunShadeBinding binding;
        private QuickManager quickManager = QuickManager.getInstance();

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private SunShadeUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public SunShadeUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public SunShadeUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new SunShadeUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSunShadeBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 520;
            layoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
            window.setAttributes(layoutParams);

            initButton();
            return dialog;
        }

        private void initButton() {
            binding.tvCarSunshadeFront.setOnClickListener(v -> {
                binding.llToolsSubSunshadeAll.setVisibility(View.GONE);
                binding.llToolsSubSunshadeFront.setVisibility(View.VISIBLE);
            });
            binding.tvCarSunshadeRear.setOnClickListener(v -> {
                binding.llToolsSubSunshadeAll.setVisibility(View.GONE);
                binding.llToolsSubSunshadeRear.setVisibility(View.VISIBLE);
            });


            // 前排全开
            binding.tvCarSunshadeFrontOpen.setOnClickListener(v -> {
                quickManager.setSunshadeFront(CarQuickControl.SetFrontSunshadeSts.AUTO_OPEN);
            });
            // 前排全关
            binding.tvCarSunshadeFrontClose.setOnClickListener(v -> {
                quickManager.setSunshadeFront(CarQuickControl.SetFrontSunshadeSts.AUTO_CLOSE);
            });
            // 后排全开
            binding.tvCarSunshadeRearOpen.setOnClickListener(v -> {
                quickManager.setSunshadeRear(CarQuickControl.SetFrontSunshadeSts.AUTO_OPEN);
            });
            // 后排全关
            binding.tvCarSunshadeRearClose.setOnClickListener(v -> {
                quickManager.setSunshadeRear(CarQuickControl.SetFrontSunshadeSts.AUTO_CLOSE);
            });

        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }
}
