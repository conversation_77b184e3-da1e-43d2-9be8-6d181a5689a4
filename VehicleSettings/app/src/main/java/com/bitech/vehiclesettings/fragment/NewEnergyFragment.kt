package com.bitech.vehiclesettings.fragment

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.drawable.AnimationDrawable
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.graphics.drawable.toDrawable
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.lifecycleScope
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.base.kt.BaseFragment
import com.bitech.vehiclesettings.bean.TargetDialogInfo
import com.bitech.vehiclesettings.carapi.constants.CarNewEnergy
import com.bitech.vehiclesettings.databinding.FragmentNewEnergyBinding
import com.bitech.vehiclesettings.utils.LogUtil
import com.bitech.vehiclesettings.utils.getIdName
import com.bitech.vehiclesettings.view.newenergy.BookChargeDialog
import com.bitech.vehiclesettings.view.newenergy.BookTravelDialog
import com.bitech.vehiclesettings.view.newenergy.ChargeSOCDialog
import com.bitech.vehiclesettings.view.newenergy.ComDetailTipDialog
import com.bitech.vehiclesettings.view.newenergy.CommonOptionsDialog
import com.bitech.vehiclesettings.view.widget.SettingsToast
import com.bitech.vehiclesettings.view.widget.onOptionsListener
import com.bitech.vehiclesettings.viewmodel.MainActViewModel
import com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * @Description: 新能源页面Fragment.
 **/
class NewEnergyFragment : BaseFragment<FragmentNewEnergyBinding, NewEnergyViewModel>(),
    View.OnClickListener, CoroutineScope by MainScope() {

    private var isAnimLoaded = false
    private val mAnimLoadCount = AtomicInteger(0)

    //动画-充放电扫光
    private var batteryChargingAnim: AnimationDrawable? = null
    private var batteryDischargingAnim: AnimationDrawable? = null

    //动画-左右车轮
    private var tireLeftForwardAnim: AnimationDrawable? = null
    private var tireRightForwardAnim: AnimationDrawable? = null

    //动画-管道左前-绿色
    private var pipeLeftFrontGreenAnim: AnimationDrawable? = null

    //动画-前发电机-绿色（管道蓝色）
    private var frontMotorGreenPipeBlueAnim: AnimationDrawable? = null

    //动画-前发电机-绿色（管道灰色）
    private var frontMotorGreenPipeGrayAnim: AnimationDrawable? = null

    //动画-前发电机-绿色（管道绿色）
    private var frontMotorGreenPipeGreenAnim: AnimationDrawable? = null

    //动画-前发电机-蓝色（管道蓝色）
    private var frontMotorBluePipeBlueAnim: AnimationDrawable? = null

    //动画-后发电机-绿色
    private var backMotorGreenAnim: AnimationDrawable? = null

    //动画-后发电机-蓝色
    private var backMotorBlueAnim: AnimationDrawable? = null

    //动画-发动机管道上下-红色
    private var engPipeUpDownRedAnim: AnimationDrawable? = null

    //动画-管道中前-蓝色
    private var pipeMiddleFrontBlueAnim: AnimationDrawable? = null

    //动画-管道中前-绿色
    private var pipeMiddleFrontGreenAnim: AnimationDrawable? = null

    //动画-管道右后-蓝色
    private var pipeRightBackBlueAnim: AnimationDrawable? = null

    //动画-管道右后-绿色
    private var pipeRightBackGreenAnim: AnimationDrawable? = null

    private var mainActViewModel: MainActViewModel? = null

    private var optionsDialog: CommonOptionsDialog? = null

    /**
     * 视图绑定.
     *
     * @return getLayoutId
     */
    override fun getLayoutId(container: ViewGroup?): FragmentNewEnergyBinding {
        return FragmentNewEnergyBinding.bind(
            layoutInflater.inflate(
                R.layout.fragment_new_energy,
                container,
                false
            )
        )
    }

    /**
     * viewModel绑定。
     *
     * @return BtViewModel
     */
    override fun getViewModel(): Class<NewEnergyViewModel> {
        return NewEnergyViewModel::class.java
    }

    override fun bindViewModel(
        viewBinding: FragmentNewEnergyBinding,
        viewModel: NewEnergyViewModel
    ) {
        viewBinding.apply {
            newEnergyViewModel = viewModel
            lifecycleOwner = viewLifecycleOwner
        }
    }

    /**
     * 初始化视图，onViewCreated()中调用.
     *
     */
    override fun initView() {
        // 初始化视图功能项可视化
        initViewVisible()
    }

    /**
     * 初始化各类监听，onViewCreated()中调用.
     *
     */
    @SuppressLint("ClickableViewAccessibility")
    override fun intiListener() {
        LogUtil.d(TAG, "intiListener : ")
        // 页面监听设置
        viewBinding?.apply {
            covChargingLimit.setOnClickListener(this@NewEnergyFragment)
            covDischargingLimit.setOnClickListener(this@NewEnergyFragment)
            covEnergyRecoveryLevel.setOnClickListener(this@NewEnergyFragment)
            covMileageDisplay.setOnClickListener(this@NewEnergyFragment)
            covPureElectricDisplay.setOnClickListener(this@NewEnergyFragment)
            covRangeConditionDisplay.setOnClickListener(this@NewEnergyFragment)
//            covEnergyCurve.setOnClickListener(this@NewEnergyFragment)
            covDisclaimers.setOnClickListener(this@NewEnergyFragment)
            covReservationCharging.setOnClickListener(this@NewEnergyFragment)
            covBookTrip.setOnClickListener(this@NewEnergyFragment)
            btnNeFunction.setOnClickListener(this@NewEnergyFragment)
            covParkingPowerGeneration.setOnClickListener(this@NewEnergyFragment)
            //预约充电
            covReservationCharging.onOptionsListener {
                onTipsIconClick = { view ->
                    // 提示图标点击事件
                    ComDetailTipDialog(requireContext()).apply {
                        setTips(
                            getString(R.string.ne_reservation_charging),
                            getString(R.string.ne_reservation_charging_tips)
                        )
                    }.show()
                }
                onOptionSwitchChange = { buttonView, isChecked ->
                    viewModel.setBookChargeSwitch(isChecked)
                }
            }

            //对外放电开关
            covDischarge.onOptionsListener {
                onTipsIconClick = { view ->
                    // 提示图标点击事件
                    ComDetailTipDialog(requireContext()).apply {
                        setTips(
                            getString(R.string.ne_discharge),
                            getString(R.string.ne_discharge_tips)
                        )
                    }.show()
                }
                onOptionSwitchChange = { buttonView, isChecked ->
                    var toastResId = 0
                    if (isChecked) {
                        // 检查放电枪是否连接
                        if (viewModel.slowGunStatusLiveData.value != CarNewEnergy.SlowGunConnectSts.V2L) {
                            toastResId = R.string.ne_insert_discharge_gun_tips

                            // 检查当前电量SOC是否小于等于 30
                        } else if (viewModel.currentSOCLiveData.value?.let { it <= 30 } == true) {
                            toastResId = R.string.ne_discharge_error_low_battery

                            // 检查当前电量SOC是否小于放电下限值
                        } else if (viewModel.currentSOCLiveData.value?.let {
                                it <= (viewModel.dischargeStopSOCLiveData.value ?: 30)
                            } == true) {
                            toastResId = R.string.ne_discharge_limit_value_error
                        }
                    }
                    if (toastResId == 0) {
                        viewModel.setDischargeSwitchStatus(isChecked)
                    } else {
                        //不能打开放电开关
                        viewBinding?.covDischarge?.setSwitchOpen(false)
                        SettingsToast.showToast(toastResId)
                    }
                }
            }

            //驻车发电
            covParkingPowerGeneration.onOptionsListener {
                onOptionSwitchChange = { buttonView, isChecked ->
                    //当车辆模式处于纯电优先/极致纯电模式时，弹出Toast提示
                    viewModel.setForceEvChargeMode(isChecked)
                }
            }

            //预约出行
            covBookTrip.onOptionsListener {
                onOptionSwitchChange = { buttonView, isChecked ->
                    viewModel.setBookTravelStatus(isChecked)
                }
            }
        }
    }

    /**
     * 初始化MutableLiveData数据订阅,onCreate()中调用.
     *
     */
    override fun initObserve() {
        //MainActivity的ViewModel
        mainActViewModel = ViewModelProvider(requireActivity()).get(MainActViewModel::class.java)
        processTargetDialogEvent(mainActViewModel!!.targetDialogLiveEvent.value)
        mainActViewModel?.targetDialogLiveEvent!!.observe(
            viewLifecycleOwner,
            this::processTargetDialogEvent
        )
        LogUtil.d(TAG, "initObserve : ${hashCode()}")
        viewModel.apply {

            // 能量流状态
            energyFlowStatusLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "能量流状态 energyFlowStatusLiveData observe : $it")
                updateEnergyFlowAnim()
            }

            // 慢充枪连接状态/放电枪连接状态
            slowGunStatusLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "慢充枪/放电枪连接状态 slowGunStatusLiveData observe : $it")
                updateChargeTextAndButtonUI()
            }

            // 快充枪连接状态
            fastGunStatusLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "快充枪连接状态 fastGunStatusLiveData observe : $it")
                updateChargeTextAndButtonUI()
            }

            // 充电状态
            chargingStatusLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "充电状态 chargingStatusLiveData observe : $it")
                updateChargeAndDischargeAnim()
                updateChargeTextAndButtonUI()
            }

            // 充电电流
            bmsPackCurrentDisLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "BMS电流 chargingPowerLiveData observe : $it")
                updateChargePowerUI()
            }

            // 充电电压
            bmsPackVoltageDisLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "BMS电压 chargingPowerLiveData observe : $it")
                updateChargePowerUI()
            }

            // 放电功率
            dischargingPowerLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "放电功率 dischargingPowerLiveData observe : $it")
                updateDischargePowerUI()
            }

            // 最大放电功率
            dischargingPowerLimitLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "最大放电功率 dischargingPowerLimitLiveData observe : $it")
                updateDischargePowerUI()
            }

            // 剩余充电时间
            leftChargingTimeLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "剩余充电时间 leftChargingTimeLiveData observe : $it")
                updateLeftChargeTimeUI(it)
            }

            // 当前电量SOC
            currentSOCLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "当前电量SOC currentSOCLiveData observe : $it")
            }

            // 对外放电功能状态
            dischargeStatusLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "对外放电功能开启状态 dischargeStatusLiveData observe : $it")
                updateChargeAndDischargeAnim()
                updateChargeTextAndButtonUI()
            }

            //电子锁闭锁
            electronicLockStatusLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "电子锁闭锁 electronicLockStatusLiveData observe : $it")
                updateChargeTextAndButtonUI()
            }

            //档位状态
            gearStatusLiveData.distinctUntilChanged().observe(viewLifecycleOwner) {
                LogUtil.d(TAG, "档位 gearStatusLiveData observe : $it")
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        if (isAnimLoaded) {
            return
        }
        isAnimLoaded = true
        initAnimation()
    }

    /**
     * 初始化各类数据，onStart()中调用.
     *
     */
    override fun initData() {
        LogUtil.d(TAG, "initData : ${hashCode()}")
        viewModel.initData()
    }

    /**
     * 移除MutableLiveData数据订阅
     *
     */
    override fun removeObserve() {
        LogUtil.d(TAG, "removeObserve : ${hashCode()}")

    }

    /**
     * 初始化页面功能可视化.
     *
     */
    private fun initViewVisible() {
        LogUtil.d(TAG, "initViewVisible : ${hashCode()}")

    }

    /**
     * 页面点击事件监听.
     *
     * @param view View
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.covChargingLimit -> {
                LogUtil.d(TAG, "onClick : covChargingLimit")
                showChargeSOCDialog(true)
            }

            R.id.covDischargingLimit -> {
                LogUtil.d(TAG, "onClick : covDischargingLimit")
                showChargeSOCDialog(false)
            }

            R.id.covEnergyRecoveryLevel -> {
                LogUtil.d(TAG, "onClick: covEnergyRecoveryLevel")
                optionsDialog = CommonOptionsDialog(
                    requireContext(),
                    CommonOptionsDialog.TYPE_ENERGY_RECOVERY_LEVEL,
                    viewModel.energyRecoveryLiveData.value ?: 0
                )
                optionsDialog?.setDialogClickCallback(object :
                    CommonOptionsDialog.OnOptionsDialogCallback {
                    override fun onClickOptionIndex(index: Int) {
                        LogUtil.d(TAG, "onClickOptionIndex option index: $index")
                        viewModel.setEnergyRecoveryType(index)
                    }
                })
                optionsDialog?.show()
            }

            R.id.covMileageDisplay -> {
                LogUtil.d(TAG, "onClick: covMileageDisplay")
                optionsDialog = CommonOptionsDialog(
                    requireContext(),
                    CommonOptionsDialog.TYPE_MILEAGE_DISPLAY,
                    viewModel.mileageDisplayLiveData.value ?: 0
                )
                optionsDialog?.setDialogClickCallback(object :
                    CommonOptionsDialog.OnOptionsDialogCallback {
                    override fun onClickOptionIndex(index: Int) {
                        LogUtil.d(TAG, "onClickOptionIndex option index: $index")
                        viewModel.setMileageDisplay(index)
                    }
                })
                optionsDialog?.show()
            }

            R.id.covPureElectricDisplay -> {
                LogUtil.d(TAG, "onClick: covPureElectricDisplay")
                optionsDialog = CommonOptionsDialog(
                    requireContext(),
                    CommonOptionsDialog.TYPE_PURE_ELECTRIC_DISPLAY,
                    viewModel.pureElectricDisplayLiveData.value ?: 0
                )
                optionsDialog?.setDialogClickCallback(object :
                    CommonOptionsDialog.OnOptionsDialogCallback {
                    override fun onClickOptionIndex(index: Int) {
                        LogUtil.d(TAG, "onClickOptionIndex option index: $index")
                        viewModel.setPureElectricDisplay(index)
                    }
                })
                optionsDialog?.show()
            }

            R.id.covRangeConditionDisplay -> {
                LogUtil.d(TAG, "onClick: covRangeConditionDisplay")
                optionsDialog = CommonOptionsDialog(
                    requireContext(),
                    CommonOptionsDialog.TYPE_RANGE_CONDITION_DISPLAY,
                    viewModel.rangeConditionDisplayLiveData.value ?: 0
                )
                optionsDialog?.setDialogClickCallback(object :
                    CommonOptionsDialog.OnOptionsDialogCallback {
                    override fun onClickOptionIndex(index: Int) {
                        LogUtil.d(TAG, "onClickOptionIndex option index: $index")
                        viewModel.setRangeConditionDisplay(index)
                    }
                })
                optionsDialog?.show()
            }

//            R.id.covEnergyCurve -> {
//                LogUtil.d(TAG, "onClick: covEnergyCurve")
//                EnergyCurveDialog(requireContext()).show()
//            }

            R.id.covDisclaimers -> {
                LogUtil.d(TAG, "onClick: covDisclaimers")
                ComDetailTipDialog(requireContext()).apply {
                    setTips(
                        getString(R.string.ne_disclaimers),
                        getString(R.string.ne_disclaimers_tips)
                    )
                    setDialogWidthHeight(1992, 992, 0)
                }.show()
            }

            R.id.covReservationCharging -> {
                LogUtil.d(
                    TAG,
                    "onClick: covReservationCharging= ${viewModel.bookChargeSwitchLiveData.value}"
                )
                //判断开关是否打开
                if (viewModel.bookChargeSwitchLiveData.value == false) {
                    SettingsToast.showToast(R.string.ne_open_reservation_charging_switch)
                    return
                }
                val bookChargeDialog = BookChargeDialog(
                    requireContext(), viewModel.bookChargeDuration.get(),
                    viewModel.bookChargeHour.get(), viewModel.bookChargeMinutes.get()
                ).apply {
                    setTitle(getString(R.string.ne_reservation_charging_time))
                }
                bookChargeDialog.setDialogClickCallback(object :
                    BookChargeDialog.OnBookChargeCallback {
                    override fun onConfirmBookCharge(duration: Int, hour: Int, minute: Int) {
                        viewModel.setBookChargeDurHourMin(duration, hour, minute)
                    }
                })
                bookChargeDialog.show()
            }

            R.id.covBookTrip -> {
                LogUtil.d(TAG, "onClick: covBookTrip= ${viewModel.bookTravelLiveData.value}")
                if (viewModel.bookTravelLiveData.value == false) {
                    SettingsToast.showToast(R.string.ne_open_book_charge_switch)
                    return
                }
                val bookTravelDialog = BookTravelDialog(
                    requireContext(), viewModel.bookTravelWeek.get(),
                    viewModel.bookTravelHour.get(), viewModel.bookTravelMinutes.get()
                ).apply {
                    setTitle(getString(R.string.ne_book_trip))
                }
                bookTravelDialog.setDialogClickCallback(object :
                    BookTravelDialog.OnBookTravelCallback {
                    override fun onConfirmBookTravel(week: Int, hour: Int, minute: Int) {
                        viewModel.setBookTravelWeekHourMin(week, hour, minute)
                    }
                })
                bookTravelDialog.show()
            }

            R.id.btnNeFunction -> {
                //解锁/停止充电
                LogUtil.i(TAG, "btnNeFunction click")
                //充电中-停止充电
                val chargingSts = viewModel.chargingStatusLiveData.value
                //放电中-解锁
                val disChargingSts = viewModel.dischargeStatusLiveData.value
                //快慢充电枪插入、放电枪插入-解锁
                val fastGunStatus = viewModel.fastGunStatusLiveData.value
                val slowGunStatus = viewModel.slowGunStatusLiveData.value
                LogUtil.i(
                    TAG,
                    "btnNeFunction : chargingSts = $chargingSts , disChargingSts = $disChargingSts , " +
                            "fastGunStatus = $fastGunStatus, slowGunStatus = $slowGunStatus"
                )
                //正在充电
                if (chargingSts == CarNewEnergy.FastChargingSts.PARKING_CHARGE) {
                    viewModel.setStopCharge()
                } else {
                    viewModel.setElectricLockUnlock()
                }
            }

            else -> {
                LogUtil.d(TAG, "onClick : other view is click!")
            }
        }
    }

    /**
     * 更新充放电文字和操作按钮文字
     */
    private fun updateChargeTextAndButtonUI() {
        //充电状态
        val charging =
            viewModel.chargingStatusLiveData.value == CarNewEnergy.FastChargingSts.PARKING_CHARGE
        //充电完成
        val chargeComplete =
            viewModel.chargingStatusLiveData.value == CarNewEnergy.FastChargingSts.CHARGE_COMPLETE
        //充电结束
        val chargeEnd =
            viewModel.chargingStatusLiveData.value == CarNewEnergy.FastChargingSts.NO_CHARGE
        //放电状态
        val disCharging = viewModel.dischargeStatusLiveData.value == true
        //快慢充电枪插入
        val fastGunConnect = viewModel.fastGunStatusLiveData.value == true
        val slowGunStatus = viewModel.slowGunStatusLiveData.value
        val slowChargeGunConnect = slowGunStatus == CarNewEnergy.SlowGunConnectSts.CHARGE_CONNECTED
        val dischargeGunConnect = slowGunStatus == CarNewEnergy.SlowGunConnectSts.V2L
        val electronicLocked = viewModel.electronicLockStatusLiveData.value == true
        LogUtil.i(
            TAG,
            "updateChargeTextAndButtonUI : charging = $charging , disCharging = $disCharging , " +
                    "fastGunConnect = $fastGunConnect, slowChargeGunConnect = $slowChargeGunConnect," +
                    " dischargeGunConnect = $dischargeGunConnect"
        )
        //充电中-停止充电
        if (charging && (fastGunConnect || slowChargeGunConnect)) {
            viewModel.dischargeBtnEnable.set(false)
            viewModel.functionButtonVisible.set(true)
            viewModel.textFunctionButton.set(getString(R.string.ne_stop_charging))
            viewModel.textChargeType.set(getString(R.string.ne_charging))
        } else if (fastGunConnect || slowChargeGunConnect) {
            //快慢充电枪插入-解锁
            viewModel.dischargeBtnEnable.set(false)
            //电子锁状态
            viewModel.functionButtonVisible.set(electronicLocked)
            viewModel.textFunctionButton.set(getString(R.string.ne_unlock))
            //充电完成、充电结束
            if (chargeComplete) {
                viewModel.textChargeType.set(getString(R.string.ne_charging_complete))
            } else if (chargeEnd) {
                viewModel.textChargeType.set(getString(R.string.ne_charging_end))
            } else {
                viewModel.textChargeType.set(
                    if (fastGunConnect)
                        getString(R.string.ne_fast_charging_connected) else getString(R.string.ne_slow_charging_connected)
                )
            }
        } else if (disCharging && dischargeGunConnect) {
            //放电中-解锁
            viewModel.dischargeBtnEnable.set(true)
            //电子锁状态
            viewModel.functionButtonVisible.set(electronicLocked)
            viewModel.textFunctionButton.set(getString(R.string.ne_unlock))
            viewModel.textChargeType.set(getString(R.string.ne_discharging))
        } else if (dischargeGunConnect) {
            //放电枪插入-解锁
            viewModel.dischargeBtnEnable.set(true)
            //电子锁状态
            viewModel.functionButtonVisible.set(electronicLocked)
            viewModel.textFunctionButton.set(getString(R.string.ne_unlock))
            //从放电中->放电完成
            if (viewModel.textChargeType.get().equals(getString(R.string.ne_discharging))) {
                viewModel.textChargeType.set(getString(R.string.ne_discharging_complete))
            } else {
                viewModel.textChargeType.set(getString(R.string.ne_discharging_connected))
            }

        } else {
            viewModel.textChargeType.set("")
            viewModel.dischargeBtnEnable.set(true)
            viewModel.functionButtonVisible.set(false)
        }

    }

    /**
     * 更新充电剩余时间
     * 信号单位为分钟，需要除以60计算，商是⼩时数，余数为分钟数，当信号⼩于60 min时，仅显
     * ⽰“预计剩余xx min”；ICC需要⾃⼰转换成h显⽰，收到1显⽰“充电即将完成”提⽰，接收到最
     * ⼤数值预估剩余充满时间显⽰“---”，等收到值⾮最⼤正常计算显⽰
     */
    private fun updateLeftChargeTimeUI(minute: Int) {
        val hour = minute / 60
        val miu = minute % 60
        LogUtil.i(TAG, "updateLeftChargeTimeUI : minute = $minute , hour = $hour , miu = $miu")
        val chargingSts = viewModel.chargingStatusLiveData.value
        if (chargingSts == CarNewEnergy.FastChargingSts.PARKING_CHARGE) {
            viewModel.textRemainingTime.set(
                if (minute <= 0) {
                    ERROR_VALUE_TEXT
                } else if (hour < 1) {
                    if (miu == 1) "充电即将完成" else "$miu min"
                } else {
                    if (hour > 24) ERROR_VALUE_TEXT else "$hour h $miu min"
                }
            )
        }
    }

    /**
     * 更新充电功率
     */
    private fun updateChargePowerUI() {
        //放电状态
        val disCharging = viewModel.dischargeStatusLiveData.value == true
        if (disCharging) {
            return
        }
        val bmsCurrentRaw = viewModel.bmsPackCurrentDisLiveData.value ?: 0f
        val bmsVoltageRaw = viewModel.bmsPackVoltageDisLiveData.value ?: 0f
        //计算功率
        val power = getPowerText(bmsCurrentRaw, bmsVoltageRaw)
        LogUtil.i(
            TAG,
            "updateChargePowerUI power= $power bmsCurrentRaw= $bmsCurrentRaw bmsVoltageRaw= $bmsVoltageRaw"
        )
        val bmsCurrentText = getBmsCurrentText(bmsCurrentRaw)
        val bmsVoltageText = getBmsVoltageText(bmsVoltageRaw)

        val colorSpan = ForegroundColorSpan(resources.getColor(R.color.text_color_2))
        viewModel.tvChargePower.set(SpannableString("功率：" + power + "KW").apply {
            setSpan(colorSpan, 0, 3, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
        })
        viewModel.tvElectricCurrent.set(SpannableString("电流：" + bmsCurrentText + "A").apply {
            setSpan(colorSpan, 0, 3, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
        })
        viewModel.tvVoltage.set(SpannableString("电压：" + bmsVoltageText + "V").apply {
            setSpan(colorSpan, 0, 3, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
        })
    }

    /**
     * 更新放电功率
     */
    private fun updateDischargePowerUI() {
        //放电状态
        val disChargingSts = viewModel.dischargeStatusLiveData.value == true
        //放电枪连接
        val slowGunStatus = viewModel.slowGunStatusLiveData.value
        if (slowGunStatus == CarNewEnergy.SlowGunConnectSts.V2L || disChargingSts
        ) {
            val colorSpan = ForegroundColorSpan(resources.getColor(R.color.text_color_2))
            val dischargingPower = viewModel.dischargingPowerLiveData.value ?: 0f
            val dischargingPowerLimit = viewModel.dischargingPowerLimitLiveData.value ?: 0f
            //无效值FF处理
            val dischargingPowerText = getDischargingPowerText(dischargingPower)
            val dischargingPowerLimitText = getDischargingPowerMaxText(dischargingPowerLimit)
            viewModel.tvDischargePowerLimit.set(SpannableString("最大功率：" + dischargingPowerLimitText + "KW").apply {
                setSpan(colorSpan, 0, 4, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
            })
            viewModel.tvChargePower.set(SpannableString("当前功率：" + dischargingPowerText + "KW").apply {
                setSpan(colorSpan, 0, 4, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
            })
        }
    }

    /**
     * 显示充电或放电停止SOC弹窗.
     * @param isCharging true充电，false放电
     */
    private fun showChargeSOCDialog(isCharging: Boolean) {
        val chargeSOCDialog = ChargeSOCDialog(
            requireContext(), isCharging,
            viewModel.currentSOCLiveData.value ?: 0,
            viewModel.chargeStopSOCLiveData.value ?: 0,
            viewModel.dischargeStopSOCLiveData.value ?: 0
        )
        chargeSOCDialog.show()
    }

    private fun initAnimation() {
        LogUtil.d(TAG, "initAnimation")
        //充放电扫光
        batteryChargingAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 58, "ic_battery_charge_scan_")
        }
        batteryDischargingAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 58, "ic_battery_discharge_scan_")
        }
        //动画-左车轮
        tireLeftForwardAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 19, "ic_left_tire_")
        }
        //动画-右车轮
        tireRightForwardAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 19, "ic_right_tire_")
        }
        //动画-管道左前-绿色
        pipeLeftFrontGreenAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 27, "ic_pipe_left_front_green_")
        }
        //动画-前发电机-绿色（管道蓝色）
        frontMotorGreenPipeBlueAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 30, "ic_front_motor_green_pipe_blue_")
        }
        //动画-前发电机-绿色（管道绿色）
        frontMotorGreenPipeGreenAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 30, "ic_front_motor_green_pipe_green_")
        }
        //动画-前发电机-绿色（管道灰色）
        frontMotorGreenPipeGrayAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 30, "ic_front_motor_green_")
        }
        //动画-前发电机-蓝色（管道蓝色）
        frontMotorBluePipeBlueAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 30, "ic_front_motor_blue_")
        }
        //动画-后发电机-绿色
        backMotorGreenAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 30, "ic_back_motor_green_")
        }
        //动画-后发电机-蓝色
        backMotorBlueAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 30, "ic_back_motor_blue_")
        }
        //动画-发动机管道上下-红色
        engPipeUpDownRedAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 45, "ic_eng_pipe_up_down_red_")
        }
        //动画-管道中前-蓝色
        pipeMiddleFrontBlueAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 27, "ic_pipe_middle_front_blue_")
        }
        //动画-管道中前-绿色
        pipeMiddleFrontGreenAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 27, "ic_pipe_middle_front_green_")
        }
        //动画-管道右后-蓝色
        pipeRightBackBlueAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 22, "ic_pipe_right_back_blue_")
        }
        //动画-管道右后-绿色
        pipeRightBackGreenAnim = AnimationDrawable().apply {
            doAnimationInitJob(this, 22, "ic_pipe_right_back_green_")
        }

    }

    /**
     * 刷新充放电动画
     *
     */
    private fun updateChargeAndDischargeAnim() {
        val charging =
            viewModel.chargingStatusLiveData.value == CarNewEnergy.FastChargingSts.PARKING_CHARGE
        val disCharging = viewModel.dischargeStatusLiveData.value == true

        LogUtil.i(
            TAG,
            "updateChargeAndDischarge: chargingStatus = $charging disChargingStatus = $disCharging"
        )

        if (charging) {
            startAnimation(viewBinding?.ivBatteryChargeScan, batteryChargingAnim)
        } else {
            stopAnimation(viewBinding?.ivBatteryChargeScan)
        }

        if (disCharging) {
            startAnimation(viewBinding?.ivBatteryDischargeScan, batteryDischargingAnim)
        } else {
            stopAnimation(viewBinding?.ivBatteryDischargeScan)
        }
    }

    private fun updateEnergyFlowAnim() {
        when (viewModel.energyFlowStatusLiveData.value) {
            CarNewEnergy.HcuEnergyFlowSts.ST02_2WD_EV -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorBluePipeBlueAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST04_2WD_ExtendRangeDisCharge -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorBluePipeBlueAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontBlueAnim)
                startAnimation(viewBinding?.ivPipeLeftFrontAnim, pipeLeftFrontGreenAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST05_2WD_ExtendRangeCharge -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorGreenPipeBlueAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontGreenAnim)
                startAnimation(viewBinding?.ivPipeLeftFrontAnim, pipeLeftFrontGreenAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST08_2WD_Parallel -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorBluePipeBlueAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontBlueAnim)
                startAnimation(viewBinding?.ivEngPipeUpDownRedAnim, engPipeUpDownRedAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST0A_Engine -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivEngPipeUpDownRedAnim, engPipeUpDownRedAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorBlueAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST0B_Running_Generate -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivEngPipeUpDownRedAnim, engPipeUpDownRedAnim)
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorGreenPipeGrayAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontGreenAnim)
                startAnimation(viewBinding?.ivPipeLeftFrontAnim, pipeLeftFrontGreenAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorBlueAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST0C_Parking_Generate -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorGreenPipeGrayAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontGreenAnim)
                startAnimation(viewBinding?.ivPipeLeftFrontAnim, pipeLeftFrontGreenAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST0D_2WD_ReGenerateBrake -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorGreenPipeGreenAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontGreenAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST10_DecelerateCharge -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorGreenPipeGreenAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontGreenAnim)
                startAnimation(viewBinding?.ivPipeLeftFrontAnim, pipeLeftFrontGreenAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorGreenAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackGreenAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST03_4WD_EV -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorBluePipeBlueAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontBlueAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorBlueAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST06_4WD_ExtendRangeDisCharge -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorBluePipeBlueAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontBlueAnim)
                startAnimation(viewBinding?.ivPipeLeftFrontAnim, pipeLeftFrontGreenAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorBlueAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST07_4WD_ExtendRangeCharge -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorGreenPipeBlueAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontGreenAnim)
                startAnimation(viewBinding?.ivPipeLeftFrontAnim, pipeLeftFrontGreenAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorBlueAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST09_4WD_Parallel -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorBluePipeBlueAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontBlueAnim)
                startAnimation(viewBinding?.ivEngPipeUpDownRedAnim, engPipeUpDownRedAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorBlueAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST0E_4WD_ReGenerateBrake -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorGreenPipeGreenAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontGreenAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorGreenAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackGreenAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST11_4WD_EVByRear -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorBlueAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST12_4WD_ExtendRangeChargeByRear,
            CarNewEnergy.HcuEnergyFlowSts.ST13_4WD_ExtendRangeDisChargeByRear -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivFrontMotorAnim, frontMotorGreenPipeGrayAnim)
                startAnimation(viewBinding?.ivPipeMiddleFrontAnim, pipeMiddleFrontGreenAnim)
                startAnimation(viewBinding?.ivPipeLeftFrontAnim, pipeLeftFrontGreenAnim)
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorBlueAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackBlueAnim)
            }

            CarNewEnergy.HcuEnergyFlowSts.ST14_4WD_ReGenerateBrakeByRear -> {
                startTireAnimation()
                startAnimation(viewBinding?.ivBackMotorAnim, backMotorGreenAnim)
                startAnimation(viewBinding?.ivPipeRightBackAnim, pipeRightBackGreenAnim)
            }

            else -> {

            }
        }
    }

    /**
     * init动画资源
     */
    private fun doAnimationInitJob(
        animationDrawable: AnimationDrawable?,
        maxFrame: Int,
        animSrcName: String,
        inSampleSize: Int = 1,
        animDuration: Int = ANIMATION_DURATION,
        delayTimeMillis: Long = ANIMATION_LOAD_DELAY,
    ) {
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
            LogUtil.d(
                TAG,
                "initAnimation InitJob start: $animSrcName , delayTime = $delayTimeMillis"
            )
            if (delayTimeMillis > 0) {
                delay(delayTimeMillis)
            }
            for (i in 0..maxFrame) {
                context?.run {
                    //跳帧
                    if (i % 2 == 0) {
                        val resId = MyApplication.getContext().resources.getIdentifier(
                            "$animSrcName$i",
                            "mipmap",
                            MyApplication.getContext().packageName
                        )
                        val options = BitmapFactory.Options()
                        options.inSampleSize = inSampleSize.coerceAtLeast(1)
                        try {
                            val bitmap: Bitmap =
                                BitmapFactory.decodeResource(
                                    MyApplication.getContext().resources,
                                    resId, options
                                )
                            val bitmapDrawable =
                                bitmap.toDrawable(MyApplication.getContext().resources)
                            animationDrawable?.addFrame(bitmapDrawable, animDuration)
                        } catch (e: Exception) {
                            LogUtil.e(TAG, "initAnimation InitJob error: $e")
                        }
                    }
                } ?: return@launch
            }
            mAnimLoadCount.incrementAndGet()
            LogUtil.d(TAG, "initAnimation InitJob end: $animSrcName  NO.${mAnimLoadCount.get()}")
            if (mAnimLoadCount.get() == ANIMATION_FILE_COUNT) {
                LogUtil.d(TAG, "initAnimation InitJob total: $ANIMATION_FILE_COUNT")
                withContext(Dispatchers.Main) {
                    updateChargeAndDischargeAnim()
                    updateEnergyFlowAnim()
                }
            }
        }
    }

    private fun startTireAnimation() {
        startAnimation(viewBinding?.ivTireLeftAnim, tireLeftForwardAnim)
        startAnimation(viewBinding?.ivTireRightAnim, tireRightForwardAnim)
    }

    private fun startAnimation(imageView: ImageView?, animationDrawable: AnimationDrawable?) {
        LogUtil.d(TAG, "startAnimation")
        viewLifecycleOwner.lifecycleScope.launch {
            delay(ANIMATION_PLAY_DELAY)
            withContext(Dispatchers.Main) {
                if (animationDrawable != null) {
                    imageView?.setImageDrawable(animationDrawable)
                }
                val batteryAnimation = imageView?.drawable
                if (batteryAnimation is AnimationDrawable) {
                    LogUtil.d(TAG, "startAnimation: ${imageView.getIdName()}")
                    batteryAnimation.start()
                }
            }
        }
    }

    private fun stopAnimation(imageView: ImageView?) {
        LogUtil.d(TAG, "stopAnimation")
        val batteryAnimation = imageView?.drawable
        if (batteryAnimation is AnimationDrawable) {
            LogUtil.d(TAG, "stopAnimation: ${imageView.getIdName()}")
            batteryAnimation.stop()
        }
    }

    private fun releaseAnimation() {
        LogUtil.d(TAG, "releaseAnimation ")
        recycleAnimationDrawable(batteryChargingAnim)
        batteryChargingAnim = null
        viewBinding?.ivBatteryChargeScan?.setImageBitmap(null)
        viewBinding?.ivBatteryChargeScan?.setImageDrawable(null)

        recycleAnimationDrawable(batteryDischargingAnim)
        batteryDischargingAnim = null
        viewBinding?.ivBatteryDischargeScan?.setImageBitmap(null)
        viewBinding?.ivBatteryDischargeScan?.setImageDrawable(null)

        recycleAnimationDrawable(tireLeftForwardAnim)
        tireLeftForwardAnim = null
        viewBinding?.ivTireLeftAnim?.setImageBitmap(null)
        viewBinding?.ivTireLeftAnim?.setImageDrawable(null)

        recycleAnimationDrawable(tireRightForwardAnim)
        tireRightForwardAnim = null
        viewBinding?.ivTireRightAnim?.setImageBitmap(null)
        viewBinding?.ivTireRightAnim?.setImageDrawable(null)

        recycleAnimationDrawable(pipeLeftFrontGreenAnim)
        pipeLeftFrontGreenAnim = null
        viewBinding?.ivPipeLeftFrontAnim?.setImageBitmap(null)
        viewBinding?.ivPipeLeftFrontAnim?.setImageDrawable(null)

        recycleAnimationDrawable(frontMotorGreenPipeBlueAnim)
        frontMotorGreenPipeBlueAnim = null

        recycleAnimationDrawable(frontMotorGreenPipeGreenAnim)
        frontMotorGreenPipeGreenAnim = null

        recycleAnimationDrawable(frontMotorGreenPipeGrayAnim)
        frontMotorGreenPipeGrayAnim = null

        recycleAnimationDrawable(frontMotorBluePipeBlueAnim)
        frontMotorBluePipeBlueAnim = null
        viewBinding?.ivFrontMotorAnim?.setImageBitmap(null)
        viewBinding?.ivFrontMotorAnim?.setImageDrawable(null)

        recycleAnimationDrawable(backMotorGreenAnim)
        backMotorGreenAnim = null

        recycleAnimationDrawable(backMotorBlueAnim)
        backMotorBlueAnim = null
        viewBinding?.ivBackMotorAnim?.setImageBitmap(null)
        viewBinding?.ivBackMotorAnim?.setImageDrawable(null)

        recycleAnimationDrawable(engPipeUpDownRedAnim)
        engPipeUpDownRedAnim = null
        viewBinding?.ivEngPipeUpDownRedAnim?.setImageBitmap(null)
        viewBinding?.ivEngPipeUpDownRedAnim?.setImageDrawable(null)

        recycleAnimationDrawable(pipeMiddleFrontBlueAnim)
        pipeMiddleFrontBlueAnim = null

        recycleAnimationDrawable(pipeMiddleFrontGreenAnim)
        pipeMiddleFrontGreenAnim = null
        viewBinding?.ivPipeMiddleFrontAnim?.setImageBitmap(null)
        viewBinding?.ivPipeMiddleFrontAnim?.setImageDrawable(null)

        recycleAnimationDrawable(pipeRightBackBlueAnim)
        pipeRightBackBlueAnim = null

        recycleAnimationDrawable(pipeRightBackGreenAnim)
        pipeRightBackGreenAnim = null
        viewBinding?.ivPipeRightBackAnim?.setImageBitmap(null)
        viewBinding?.ivPipeRightBackAnim?.setImageDrawable(null)

        viewBinding?.clNeCarModel?.background = null
        viewBinding?.ivCarWdChassis?.setImageDrawable(null)
        viewBinding?.ivCarWdChassisShadow?.setImageDrawable(null)
        viewBinding?.ivTireLeft?.setImageDrawable(null)
        viewBinding?.ivTireRight?.setImageDrawable(null)
        viewBinding?.ivBatteryPercent?.setImageDrawable(null)
        viewBinding?.ivEngineImage?.setImageDrawable(null)

        isAnimLoaded = false
        mAnimLoadCount.set(0)
    }

    private fun recycleAnimationDrawable(animationDrawables: AnimationDrawable?) {
        if (animationDrawables != null) {
            animationDrawables.stop()
            for (i in 0 until animationDrawables.numberOfFrames) {
                val frame: Drawable = animationDrawables.getFrame(i)
                if (frame is BitmapDrawable) {
                    frame.bitmap.recycle()
                }
                frame.callback = null
            }
            animationDrawables.callback = null
        }
    }

    override fun onDestroyView() {
        releaseAnimation()
        cancel()
        super.onDestroyView()
    }

    /**
     * 处理跳转弹窗事件
     */
    private fun processTargetDialogEvent(targetDialog: TargetDialogInfo?) {
        Log.d(TAG, "processTargetDialogEvent targetDialog= $targetDialog")
        if (targetDialog == null) {
            return
        }
        //具体Tab索引
        if (targetDialog.targetTab == MainActivity.MainTabIndex.NEW_ENERGY) {
            when (targetDialog.targetDialog) {
                ENDURANCE_INTERFACE -> {
                    if (targetDialog.operation == 1) {
                        optionsDialog = CommonOptionsDialog(
                            requireContext(),
                            CommonOptionsDialog.TYPE_RANGE_CONDITION_DISPLAY,
                            viewModel.rangeConditionDisplayLiveData.value ?: 0
                        )
                        optionsDialog?.show()
                    } else {
                        optionsDialog?.dismiss()
                    }
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        viewModel.sendDataPointData()
    }

    override fun onStop() {
        if (optionsDialog?.isShowing == true) {
            optionsDialog?.dismiss()
        }
        super.onStop()
    }

    companion object {
        // 日志标志位
        private const val TAG = "NewEnergyFragment"

        private const val ERROR_VALUE_TEXT = "---"

        // 动画一帧的持续时间ms
        private const val ANIMATION_DURATION = 50

        private const val ANIMATION_LOAD_DELAY = 0L

        private const val ANIMATION_PLAY_DELAY = 800L

        private const val ANIMATION_FILE_COUNT = 16

        const val ENDURANCE_INTERFACE = 10

        fun getPowerText(bmsCurrentRaw: Float, bmsVoltageRaw: Float): String {
            //功率电流电压需要四舍五入取整、取绝对值
            val bmsCurrent = abs(bmsCurrentRaw).roundToInt()
            val bmsVoltage = abs(bmsVoltageRaw).roundToInt()
            //计算功率
            val power =
                if (bmsCurrentRaw * bmsVoltageRaw == 0f || bmsCurrentRaw == Float.MIN_VALUE || bmsVoltageRaw == Float.MIN_VALUE)
                    ERROR_VALUE_TEXT else (bmsCurrent * bmsVoltage + 1000 / 2) / 1000
            return power.toString()
        }

        fun getBmsCurrentText(bmsCurrentRaw: Float): String {
            val bmsCurrent = abs(bmsCurrentRaw).roundToInt()
            return if (bmsCurrentRaw == 0f || bmsCurrentRaw == Float.MIN_VALUE) ERROR_VALUE_TEXT else bmsCurrent.toString()
        }

        fun getBmsVoltageText(bmsVoltageRaw: Float): String {
            val bmsVoltage = abs(bmsVoltageRaw).roundToInt()
            return if (bmsVoltageRaw == 0f || bmsVoltageRaw == Float.MIN_VALUE) ERROR_VALUE_TEXT else bmsVoltage.toString()
        }

        fun getDischargingPowerText(dischargingPower: Float): String {
            return if (dischargingPower == 0f || dischargingPower >= 25.5f
                || dischargingPower == Float.MIN_VALUE
            ) ERROR_VALUE_TEXT else dischargingPower.roundToInt().toString()
        }

        fun getDischargingPowerMaxText(dischargingPowerLimit: Float): String {
            return if (dischargingPowerLimit == 0f || dischargingPowerLimit == Float.MIN_VALUE)
                ERROR_VALUE_TEXT else dischargingPowerLimit.toString()
        }
    }
}