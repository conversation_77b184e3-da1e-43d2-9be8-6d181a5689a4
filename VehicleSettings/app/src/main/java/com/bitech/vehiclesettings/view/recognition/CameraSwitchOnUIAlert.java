package com.bitech.vehiclesettings.view.recognition;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bitech.base.utils.Util;
import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.carapi.constants.CarDriving;
import com.bitech.vehiclesettings.databinding.DialogAlertRCameraSwitchOnBinding;
import com.bitech.vehiclesettings.utils.BindingUtil;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectUtils;
import com.bitech.vehiclesettings.utils.Prefs;
import com.bitech.vehiclesettings.utils.PrefsConst;
import com.bitech.vehiclesettings.view.common.DetailsUIAlert;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.iflytek.autofly.icvp.sdk.ICVPCallBack;
import com.iflytek.autofly.icvp.sdk.ICVPManager;
import com.iflytek.autofly.icvp.sdk.common.ICVPCameraConstants;
import com.iflytek.autofly.icvp.sdk.inter.ICVPOnAllCameraStateListener;

public class CameraSwitchOnUIAlert extends BaseDialog {
    private static final String TAG = CameraSwitchOnUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public CameraSwitchOnUIAlert(@NonNull Context context) {
        super(context);
    }

    public CameraSwitchOnUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected CameraSwitchOnUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        CameraSwitchOnUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        private boolean cameraConfirmFlag = false;
        private boolean cameraCancelFlag = false;
        protected DialogAlertRCameraSwitchOnBinding binding;

        private PrivacyPolicyUIAlert.Builder privacyPolicyUIAlert;
        private DetailsUIAlert.Builder detailUIAlert;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private CameraSwitchOnUIAlert dialog = null;

        public Builder(Context context) {
            this.context = context;
        }


        public Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public CameraSwitchOnUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new CameraSwitchOnUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertRCameraSwitchOnBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1176;
            layoutParams.height = 672;
            window.setAttributes(layoutParams);

            // 设置文字link效果
            clickTextLink();
            // 设置按钮效果
            // 设置确定按钮效果
            clickConfirmCamera();
            // 初始化确定按钮
            setConfirmCamera();
            // 设置取消按钮效果
            clickCancelCamera();
            // 设置radio实现checkbox效果
            clickRadioAgree();

            return dialog;
        }

        // 摄像头开启接口模拟
        private boolean openConfirmCamera() {
            return Math.random() < 0.99;
        }

        // 设置文字链接点击效果
        private void clickTextLink() {
            String fullText = context.getString(R.string.str_recognition_camera_2_3);
            // 1. 创建 SpannableString
            SpannableString spannableString = new SpannableString(fullText);
            // 2. 定义要设置为链接的文本范围
            final String linkText = context.getString(R.string.str_recognition_camera_3);
            int start = fullText.indexOf(linkText);
            int end = start + linkText.length();
            // 3. 创建 ClickableSpan
            ClickableSpan clickableSpan = new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    openTipsDialog(context.getString(R.string.str_recognition_privacy), context.getString(R.string.str_recognition_privacy_1), 1584, 1000);
                    detailUIAlert.setGravity(Gravity.LEFT);
                    detailUIAlert.setTextSize(com.bitech.base.R.dimen.font_24px);
                    detailUIAlert.setPadding(120);
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    // 自定义链接样式
                    int theme = Prefs.get(PrefsConst.SYSTEM_COLOR, R.style.OverlayThemeBlue);
                    if (theme == R.style.OverlayThemeBlue) {
                        ds.setColor(context.getColor(R.color.system_color_blue));
                    } else if (theme == R.style.OverlayThemePurple) {
                        ds.setColor(context.getColor(R.color.system_color_purple));
                    } else if (theme == R.style.OverlayThemeCyan) {
                        ds.setColor(context.getColor(R.color.system_color_cyan));
                    } else if (theme == R.style.OverlayThemeGreen) {
                        ds.setColor(context.getColor(R.color.system_color_green));
                    } else if (theme == R.style.OverlayThemeOrange) {
                        ds.setColor(context.getColor(R.color.system_color_orange));
                    } else if (theme == R.style.OverlayThemeRed) {
                        ds.setColor(context.getColor(R.color.system_color_red));
                    }
                    ds.setUnderlineText(false);     // 移除下划线（可选）
                }
            };
            // 4. 将 ClickableSpan 应用到指定文本范围
            spannableString.setSpan(
                    clickableSpan,
                    start,
                    end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            );

            // 5. 设置 TextView
            binding.tvAgreeText.setText(spannableString);
            binding.tvAgreeText.setMovementMethod(LinkMovementMethod.getInstance()); // 必须调用
            // 移除点击后的背景高亮
            binding.tvAgreeText.setHighlightColor(Color.TRANSPARENT);
        }

        private void openTipsDialog(String title, String content, int width, int height) {
            if (detailUIAlert != null && detailUIAlert.isShowing()) {
                return;
            }
            if (detailUIAlert == null) {
                detailUIAlert = new DetailsUIAlert.Builder(context);
            }
            // 注册子弹窗
            if (dialog != null) {
                dialog.registerChildDialog();
            }
            Dialog subDialog = detailUIAlert.create(title, content, width, height);
            subDialog.show();

            // 设置关闭监听器以注销子弹窗
            subDialog.setOnDismissListener(dialog -> {
                if (this.dialog != null) {
                    this.dialog.unregisterChildDialog();
                }
            });
        }

        // 设置按钮点击效果
        private void clickRadioAgree() {
            binding.rbAgree.setOnClickListener(this::clickEffects);
            // 初始化
            int hasAgree = Prefs.get(PrefsConst.D_DMS_PRIVACY_AGREEMENT, CarDriving.DMS_Privacy_Agreement.NOT_AGREE);
            binding.rbAgree.setChecked(hasAgree == CarDriving.DMS_Privacy_Agreement.AGREE);
            cameraConfirmFlag = hasAgree == CarDriving.DMS_Privacy_Agreement.AGREE;
            setConfirmCamera();
        }

        private void clickEffects(View v) {
            Integer tag = (Integer) binding.rgAgree.getTag();
            int hasAgree = Prefs.get(PrefsConst.D_DMS_PRIVACY_AGREEMENT, CarDriving.DMS_Privacy_Agreement.NOT_AGREE);
            if (tag == null) {
                if (hasAgree == CarDriving.DMS_Privacy_Agreement.NOT_AGREE) {
                    //第一次选中点击
                    Log.d(TAG, "第一次选中点击");
                    binding.rgAgree.clearCheck();
                    binding.rgAgree.check(v.getId());
                    binding.rgAgree.setTag(0);
                    cameraConfirmFlag = true;
                    Prefs.put(PrefsConst.D_DMS_PRIVACY_AGREEMENT, CarDriving.DMS_Privacy_Agreement.AGREE);
                } else {
                    //第一次取消选中点击
                    Log.d(TAG, "第一次取消选中点击");
                    binding.rgAgree.clearCheck();
                    binding.rgAgree.setTag(null);
                    cameraConfirmFlag = false;
                    Prefs.put(PrefsConst.D_DMS_PRIVACY_AGREEMENT, CarDriving.DMS_Privacy_Agreement.NOT_AGREE);
                }
            } else {
                //是否是上一次选中点击的,如果是说明是重复点击，取消选中
                if (tag == 0) {
                    Log.d(TAG, "取消选中");
                    binding.rgAgree.clearCheck();
                    binding.rgAgree.setTag(null);
                    cameraConfirmFlag = false;
                    Prefs.put(PrefsConst.D_DMS_PRIVACY_AGREEMENT, CarDriving.DMS_Privacy_Agreement.NOT_AGREE);
                } else {
                    Log.d(TAG, "选中");
                    binding.rgAgree.check(v.getId());
                    binding.rgAgree.setTag(0);
                    cameraConfirmFlag = true;
                    Prefs.put(PrefsConst.D_DMS_PRIVACY_AGREEMENT, CarDriving.DMS_Privacy_Agreement.AGREE);
                }
            }
            setConfirmCamera();
        }

        // 设置确定按钮点击逻辑
        private void clickConfirmCamera() {
            binding.tvCameraConfirm.setOnClickListener(v -> {
                // 设置确定按钮不可按
                lockedBtn(true);
                EToast.showToast(context, "摄像头开启中，请稍后", 0, false);
                // 打开摄像头
                onProgressChangedListener.onDataReceived("open");
            });
        }

        public void openSuccess() {
            EToast.showToast(context, "摄像头开启成功", 0, false);
            dialog.dismiss();
            // 设置确定按钮可按
            lockedBtn(false);
        }

        public void openFail() {
            EToast.showToast(context, "摄像头开启失败，请检查后重试", 0, false);
            dialog.dismiss();
            // 设置确定按钮可按
            lockedBtn(false);
        }

        // 设置按钮锁
        private void lockedBtn(boolean flag) {
            if (flag) {
                cameraConfirmFlag = false;
                cameraCancelFlag = false;
            } else {
                cameraConfirmFlag = true;
                cameraCancelFlag = true;
            }
            setConfirmCamera();
            setCancelCamera();
        }

        // 更改确定按钮状态
        private void setConfirmCamera() {
            binding.tvCameraConfirm.setClickable(cameraConfirmFlag);
            if (!cameraConfirmFlag) {
                GrayEffectUtils.applyGrayEffect(binding.tvCameraConfirm);
            } else {
                GrayEffectUtils.removeGrayEffect(binding.tvCameraConfirm);
            }
//            binding.tvCameraConfirm.setBackgroundResource(cameraConfirmFlag ? R.drawable.selector_bg_open : R.drawable.selector_bg_disabled);
        }

        // 更改取消按钮状态
        private void setCancelCamera() {
            binding.tvCameraCancel.setClickable(cameraCancelFlag);
            if (!cameraCancelFlag) {
                GrayEffectUtils.applyGrayEffect(binding.tvCameraCancel);
            } else {
                GrayEffectUtils.removeGrayEffect(binding.tvCameraCancel);
            }
//            binding.tvCameraCancel.setBackgroundResource(cameraCancelFlag ? R.drawable.selector_bg_cancel : R.drawable.selector_bg_cancel_disabled);
        }

        // 设置取消按钮点击逻辑
        private void clickCancelCamera() {
            binding.tvCameraCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onDataReceived(String data);
    }
}
