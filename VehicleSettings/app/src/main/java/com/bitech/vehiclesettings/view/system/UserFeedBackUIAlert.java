package com.bitech.vehiclesettings.view.system;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.CountDownTimer;
import android.os.IBinder;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSystemUserFeedbackBinding;
import com.bitech.vehiclesettings.utils.EToast;
import com.bitech.vehiclesettings.utils.GrayEffectHelper;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;
import com.lion.os.sdk.accountInfo.AccountInfoManager;
import com.lion.os.sdk.accountInfo.bean.FeedbackResponse;
import com.lion.os.sdk.accountInfo.listener.FeedbackSubmitListener;

import org.json.JSONException;
import org.json.JSONObject;
import org.libpag.PAGFile;

public class UserFeedBackUIAlert extends BaseDialog {
    private static final String TAG = UserFeedBackUIAlert.class.getSimpleName();

    protected DialogAlertSystemUserFeedbackBinding binding;
    private CountDownTimer animationTimer;
    private boolean isPagPlaying = false;
    private OnProgressChangedListener onProgressChangedListener;

    public UserFeedBackUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    public interface OnProgressChangedListener {
        void onAnimationStateChanged(boolean isPlaying);
    }

    public void setOnProgressChangedListener(OnProgressChangedListener listener) {
        this.onProgressChangedListener = listener;
    }

    @Override
    protected void onCreate(android.os.Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void dismiss() {
        cancelTimer();
        if (binding != null && binding.pagAnimation != null) {
            binding.pagAnimation.freeCache();
        }
        VoiceInputService.listener = null;
        super.dismiss();
    }

    private void cancelTimer() {
        if (animationTimer != null) {
            animationTimer.cancel();
            animationTimer = null;
        }
    }

    public static class Builder {
        private static final long MAX_ANIMATION_DURATION = 15000;

        private final Context context;
        private boolean isCancelable = true;
        private OnProgressChangedListener progressListener;
        private UserFeedBackUIAlert dialog;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCancelable(boolean isCancelable) {
            this.isCancelable = isCancelable;
            return this;
        }

        public Builder setOnProgressChangedListener(OnProgressChangedListener listener) {
            this.progressListener = listener;
            return this;
        }

        public UserFeedBackUIAlert create() {
            dialog = new UserFeedBackUIAlert(context, R.style.Dialog);
            dialog.setCancelable(isCancelable);

            DialogAlertSystemUserFeedbackBinding binding = DialogAlertSystemUserFeedbackBinding.inflate(
                    LayoutInflater.from(context));
            dialog.binding = binding;
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            if (window != null) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.width = 1584;
                layoutParams.height = 786;
                window.setAttributes(layoutParams);
            }

            initViews(binding);
            return dialog;
        }

        private void initViews(DialogAlertSystemUserFeedbackBinding binding) {
            // 初始化按钮状态
            GrayEffectHelper.setEnable(binding.btnConfirm, false);
            GrayEffectHelper.setGrayScale(binding.btnConfirm, 0.39f);

            // 设置按钮点击事件
            binding.btnCancel.setOnClickListener(v -> dialog.dismiss());
            binding.btnConfirm.setOnClickListener(v -> {
                AccountInfoManager.getInstance().submitFeedback(binding.editText.getText().toString(), new FeedbackSubmitListener() {

                    @Override
                    public void onSubmitSuccess(FeedbackResponse feedbackResponse) {
                        EToast.showToast(context, "已提交，感谢反馈", 1000, false);
                    }

                    @Override
                    public void onFailed(String s, String s1) {
                        Log.e(TAG, "onFailed: " + s + " " + s1);
                        EToast.showToast(context, s1, 1000, false);
                    }
                });
                dialog.dismiss();
            });

            // 文本变化监听
            binding.editText.addTextChangedListener(new TextWatcher() {
                @Override public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
                @Override public void onTextChanged(CharSequence s, int start, int before, int count) {
                    boolean hasText = s.length() > 0;
                    GrayEffectHelper.setEnable(binding.btnConfirm, hasText);
                    GrayEffectHelper.setGrayScale(binding.btnConfirm, hasText ? 1f : 0.39f);
                    // 更新字符统计 TextView
                    int length = s.length();
                    binding.charCounter.setText(length + "/200");
                }
                @Override public void afterTextChanged(Editable s) {}
            });

            // 语音输入监听
            VoiceInputService.listener = text -> binding.editText.post(() -> {
                Editable editable = binding.editText.getText();
                if (editable != null && text != null && !editable.toString().endsWith(text)) {
                    editable.append(text);
                    binding.editText.setSelection(editable.length());
                }
            });

            // 设置动画切换
            setupAnimationToggle(binding);
        }

        private void setupAnimationToggle(DialogAlertSystemUserFeedbackBinding binding) {
            binding.ivVoiceNormal.setOnClickListener(v -> toggleAnimation(binding));
            binding.pagAnimation.setOnClickListener(v -> toggleAnimation(binding));
        }

        private void toggleAnimation(DialogAlertSystemUserFeedbackBinding binding) {
            if (dialog.isPagPlaying) {
                stopPagAnimation(binding);
                binding.tvUserbackTip.setText(context.getString(R.string.str_user_feedback_tip_no_play));
            } else {
                playPagAnimation(binding);
                binding.tvUserbackTip.setText(context.getString(R.string.str_user_feedback_tip_is_playing));
            }

            if (progressListener != null) {
                progressListener.onAnimationStateChanged(dialog.isPagPlaying);
            }
        }

        private void playPagAnimation(DialogAlertSystemUserFeedbackBinding binding) {
            try {
                dialog.cancelTimer();
                binding.ivVoiceNormal.setVisibility(View.GONE);
                binding.pagAnimation.setVisibility(View.VISIBLE);

                String pagPath = isNightMode() ? "night/user_feedback.pag" : "day/user_feedback.pag";
                PAGFile pagFile = PAGFile.Load(context.getAssets(), pagPath);
                binding.pagAnimation.setComposition(pagFile);
                binding.pagAnimation.setRepeatCount(0);
                binding.pagAnimation.play();

                dialog.isPagPlaying = true;
                startVoiceScene();
            } catch (Exception e) {
                Log.e(TAG, "播放PAG动画失败", e);
                resetToStaticImage(binding);
            }
        }

        private void stopPagAnimation(DialogAlertSystemUserFeedbackBinding binding) {
            dialog.cancelTimer();
            if (binding.pagAnimation != null) {
                binding.pagAnimation.stop();
            }
            resetToStaticImage(binding);
            exitVoiceScene();
        }

        private void resetToStaticImage(DialogAlertSystemUserFeedbackBinding binding) {
            binding.pagAnimation.setVisibility(View.GONE);
            binding.ivVoiceNormal.setVisibility(View.VISIBLE);
            dialog.isPagPlaying = false;
        }

        private boolean isNightMode() {
            int nightModeFlags = context.getResources().getConfiguration().uiMode
                    & android.content.res.Configuration.UI_MODE_NIGHT_MASK;
            return nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES;
        }

        private void startVoiceScene() {
            try {
                Intent intent = new Intent();
                intent.setComponent(new ComponentName("com.iflytek.cutefly.speechclient.hmi",
                        "com.iflytek.auto.speechclient.sdk.SpeechClientService"));
                intent.putExtra("StartFrom", "Tucao");
                intent.putExtra("Operation", "enter");
                context.startService(intent);

                dialog.animationTimer = new CountDownTimer(MAX_ANIMATION_DURATION, 1000) {
                    @Override public void onTick(long millisUntilFinished) {}
                    @Override public void onFinish() {
                        exitVoiceScene();
                        stopPagAnimation(dialog.binding);
                    }
                }.start();
            } catch (Exception e) {
                Log.e(TAG, "启动语音场景失败", e);
            }
        }

        private void exitVoiceScene() {
            try {
                Intent intent = new Intent();
                intent.setComponent(new ComponentName("com.iflytek.cutefly.speechclient.hmi",
                        "com.iflytek.auto.speechclient.sdk.SpeechClientService"));
                intent.putExtra("StartFrom", "Tucao");
                intent.putExtra("Operation", "outer");
                context.startService(intent);
            } catch (Exception e) {
                Log.e(TAG, "退出语音场景失败", e);
            }
        }
    }

    public static class VoiceInputService extends Service {
        public static OnVoiceTextReceivedListener listener;

        public interface OnVoiceTextReceivedListener {
            void onVoiceText(String text);
        }

        @Override
        public int onStartCommand(Intent intent, int flags, int startId) {
            if (intent != null && "com.chery.vehicle.VoiceAction".equals(intent.getAction())) {
                String jsonStr = intent.getStringExtra("value");
                try {
                    JSONObject json = new JSONObject(jsonStr);
                    if ("voice_pgs".equals(json.optString("dt"))) {
                        String text = json.optString("pt");
                        if (listener != null && text != null && !text.isEmpty()) {
                            listener.onVoiceText(text);
                        }
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "解析语音数据失败", e);
                }
            }
            return START_NOT_STICKY;
        }

        @Override
        public IBinder onBind(Intent intent) {
            return null;
        }
    }

    public static class VoiceActionReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("com.bitech.vehiclesettings.userfeedback.VoiceAction".equals(intent.getAction())) {
                String text = intent.getStringExtra("text");
                String psg = intent.getStringExtra("psg");
                String resultText = text != null ? text : (psg != null ? psg : "");

                if (!TextUtils.isEmpty(resultText) && VoiceInputService.listener != null) {
                    VoiceInputService.listener.onVoiceText(resultText);
                }
            }
        }
    }
}