package com.bitech.vehiclesettings.view.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R
import com.bitech.vehiclesettings.databinding.DialogConfirmTitleTwoBtnBinding
import com.bitech.vehiclesettings.manager.CarConfigInfoManager
import com.bitech.vehiclesettings.view.dialog.BtPairedCodeDialog
import com.bitech.vehiclesettings.utils.LogUtil

/**
 * @ClassName: ConfirmTitleDialog
 *
 * @Date:  2024/3/21 18:53
 * @Description: 自定义退出确认提示弹窗.
 **/
class ConfirmTitleDialog(context: Context) :
    Dialog(context, R.style.dialog), View.OnClickListener {

    private lateinit var binding: DialogConfirmTitleTwoBtnBinding
    private lateinit var confirmDialogClickCallback: OnConfirmDialogClickCallback
    private lateinit var message: String
    private lateinit var title: String
    private var confirmText: String = context.getString(R.string.dialog_confirm_text)

    @SuppressLint("InflateParams")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d(TAG, "onCreate : ")
        binding = DialogConfirmTitleTwoBtnBinding
                .bind(layoutInflater.inflate(R.layout.dialog_confirm_title_two_btn, null))
        setContentView(binding.root)
        // 初始化视图
        initView()
    }

    /**
     * 初始化dialog视图.
     *
     */
    private fun initView() {
        // 设置对话框窗口属性
        val attributes = window?.attributes
        attributes?.type = WindowManager.LayoutParams.TYPE_DISPLAY_OVERLAY
        attributes?.windowAnimations = 0
        window?.attributes = attributes
        // 外部点击可关闭
        setCanceledOnTouchOutside(true)
        // 对话框按钮监听
        binding.dialogConfirmBtn.setOnClickListener(this)
        binding.dialogCancelBtn.setOnClickListener(this)
        binding.dialogTitleTv.text = title
        binding.dialogTipsTv.text = message
        binding.dialogConfirmBtn.text = confirmText
    }

    override fun cancel() {
        LogUtil.d(TAG, "cancel :")
        super.cancel()
    }

    override fun dismiss() {
        LogUtil.d(TAG, "dismiss :")
        super.dismiss()
    }

    /**
     * 设置标题警告提示语.
     *
     * @param titleString 标题
     * @param tips 提示信息
     */
    fun setTips(titleString: String, tips: String) {
        title = titleString
        message = tips
    }

    /**
     * 设置确认按钮显示文言.
     *
     * @param tips
     */
    fun setConfirmBtnText(tips: String) {
        confirmText = tips
    }

    /**
     * dialog 按钮点击事件.
     *
     * @param view View
     */
    override fun onClick(view: View) {
        when (view.id) {
            R.id.dialog_confirm_btn -> {
                LogUtil.d(TAG, "onClick : confirm!")
                confirmDialogClickCallback.onConfirmClick()
                dismiss()
            }

            R.id.dialog_cancel_btn -> {
                LogUtil.d(TAG, "onClick : cancel!")
                confirmDialogClickCallback.onCancelClick()
                dismiss()
            }

            else -> {
                // TODO:
            }
        }
    }

    /**
     * 设置确认按钮点击事件监听.
     *
     * @param callback
     */
    fun setDialogClickCallback(callback: OnConfirmDialogClickCallback) {
        confirmDialogClickCallback = callback
    }

    /**
     * @ClassName: OnConfirmDialogClickCallback
     *
     * @Date:  2024/1/22 17:43
     * @Description: 确认按钮点击事件回调.
     **/
    interface OnConfirmDialogClickCallback {
        /**
         * 确认按钮被点击.
         *
         */
        fun onConfirmClick()

        /**
         * 取消按钮被点击.
         *
         */
        fun onCancelClick()
    }

    companion object {
        // 日志标志位
        private const val TAG = "ConfirmDialog"
    }
}
