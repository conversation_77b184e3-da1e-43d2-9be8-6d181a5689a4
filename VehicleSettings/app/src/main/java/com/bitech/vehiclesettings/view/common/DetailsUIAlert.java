package com.bitech.vehiclesettings.view.common;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ActivityChooserView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertRDetailsBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class DetailsUIAlert extends BaseDialog {
    private static final String TAG = DetailsUIAlert.class.getSimpleName();
    private static onProgressChangedListener onProgressChangedListener;


    public DetailsUIAlert(@NonNull Context context) {
        super(context);
    }

    public DetailsUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected DetailsUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(onProgressChangedListener onProgressChangedListener) {
        DetailsUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private OnDialogResultListener listener;

    public static class Builder {
        private final Context context;
        private boolean isCan = true;
        protected DialogAlertRDetailsBinding binding;
        private boolean isBlueOpen = false;
        private DetailsUIAlert dialog = null;
        private View layout;
        WindowManager.LayoutParams layoutParams;

        public Builder(Context context) {
            this.context = context;
        }

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        public DetailsUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        public void setOnDialogResultListener(OnDialogResultListener listener) {
            dialog.listener = listener;
        }

        public DetailsUIAlert create() {
            return create("标题", "正文", 1128, 428);
        }

        public DetailsUIAlert create(String title, String content, int width, int height) {
            return create(title, content, width, height, Gravity.CENTER);
        }

        public DetailsUIAlert create(String title, String content, int width, int height, int gravity) {
            return create(title, content, width, height, gravity, com.bitech.base.R.dimen.font_28px, com.bitech.base.R.dimen.font_28px);
        }

        public DetailsUIAlert create(String title, String content, int width, int height, int gravity, int textSizeId) {
            return create(title, content, width, height, gravity, textSizeId, com.bitech.base.R.dimen.font_28px);
        }
        public DetailsUIAlert create(String title, String content, int width, int height, int gravity, int textSizeId, int titleSizeId) {
            if (dialog == null)
                dialog = new DetailsUIAlert(context, R.style.Dialog);
            dialog.setCancelable(isCan);

            binding = DialogAlertRDetailsBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());

            Window window = dialog.getWindow();
            layoutParams = window.getAttributes();
            setSize(width, height);
            window.setAttributes(layoutParams);
            // 设置显示文本
            setText(title, content);
            setGravity(gravity);
            // 设置文本大小
            setTextSize(textSizeId);
            // 设置标题大小
            setTitleSize(titleSizeId);
            // 默认正文不可滚动
            setScrollable(false);
            return dialog;
        }

        public void setPadding(int size) {
            binding.scrollView.setPadding(size, 0, size, 0);
        }

        public void setTextSize(int id) {
            binding.tvContent.setTextSize(context.getResources().getDimension(id));
        }

        public void setTitleSize(int id) {
            binding.tvTitle.setTextSize(context.getResources().getDimension(id));
        }

        public void setScrollable(boolean flag) {
            binding.scrollView.setScrollable(flag);
        }

        public void setGravity(int gravity) {
            binding.tvContent.setGravity(gravity);
        }

        public void setSize(int width, int height) {
            layoutParams.width = width;
            layoutParams.height = height;
        }

        public void setText(String title, String content) {
            binding.tvTitle.setText(title);
            binding.tvContent.setText(content);
        }

        public boolean isShowing() {
            return dialog != null && dialog.isShowing();
        }

        public DetailsUIAlert getDialog() {
            return dialog;
        }
    }

    @Override
    public void cancel() {
        //unregisterReceiver(this.getContext());
        super.cancel();
    }

    @Override
    public void dismiss() {
        unregisterReceiver(this.getContext());
        super.dismiss();
    }

    /**
     * 反注册广播取消蓝牙的配对
     *
     * @param context
     */
    public void unregisterReceiver(Context context) {

    }

    public interface onProgressChangedListener {
        void onSwitch(boolean flag);
    }
}
