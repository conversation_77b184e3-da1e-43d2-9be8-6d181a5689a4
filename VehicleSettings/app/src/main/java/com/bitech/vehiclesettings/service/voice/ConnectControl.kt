package com.bitech.vehiclesettings.service.voice

import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.wifi.WifiManager
import android.util.Log
import com.bitech.platformlib.BitechCar
import com.bitech.platformlib.manager.ConnectManager
import com.bitech.vehiclesettings.MyApplication
import com.bitech.vehiclesettings.activity.MainActivity
import com.bitech.vehiclesettings.carapi.constants.CarSettingConstant
import com.bitech.vehiclesettings.fragment.ConnectFragment
import com.bitech.vehiclesettings.manager.CarBtManager
import com.bitech.vehiclesettings.manager.CarWifiManager
import com.bitech.vehiclesettings.presenter.voice.VoicePresenter
import com.bitech.vehiclesettings.utils.CommonConst
import com.bitech.vehiclesettings.utils.DialogNavigationUtils
import com.bitech.vehiclesettings.view.connect.WifiFragment
import com.bitech.vehiclesettings.viewmodel.BtViewModel
import com.bitech.vehiclesettings.viewmodel.WifiViewModel
import com.chery.ivi.vdb.client.VDBus
import com.chery.ivi.vdb.event.base.VDKey
import com.chery.ivi.vdb.event.id.phonelink.VDEventPhoneLink
import com.chery.ivi.vdb.event.id.vr.VDEventVR
import com.chery.ivi.vdb.event.id.vr.VDValueVR
import com.chery.ivi.vdb.event.id.vr.bean.VDP2P


class ConnectControl {
    private var mBluetoothAdapter: BluetoothAdapter? = null
    private var carWifiManager = CarWifiManager.instance
    private var carBtManager = CarBtManager.instance
    private var btViewModel: BtViewModel? = null
    private var wifiViewModel: WifiViewModel? = null
    var manager: ConnectManager =
        BitechCar.getInstance().getServiceManager(BitechCar.CAR_CONNECT_MANAGER) as ConnectManager
    private val mVoicePresenter: VoicePresenter = VoicePresenter.getInstance()
    //单回复提示语id
    fun sendResultCode(respondId: String) {
        val param = VDP2P()
        param.semanticType = VDValueVR.VRSemanticKey.VR_CONTROL_RESPONSE
        param.respondId = respondId
        val event = VDP2P.createEvent(VDEventVR.VR_P2P_INTERFACE, param)
        VDBus.getDefault().set(event)
    }
    /**
     * 设置蓝牙
     * @param flag true为打开蓝牙，false为关闭蓝牙
     */
    fun setBluetooth(flag: Boolean) {
        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        //如果value的值为字符"true"，则打开蓝牙
        if (flag) {
            if(!mBluetoothAdapter?.isEnabled!!){
                mBluetoothAdapter?.enable()
                sendResultCode("open_bluetooth_1")
            }else{
                sendResultCode("open_bluetooth_2")
            }
        } else {
            if(mBluetoothAdapter?.isEnabled == true){
                mBluetoothAdapter?.disable()
                sendResultCode("close_bluetooth_1")
            }else{
                sendResultCode("close_bluetooth_2")
            }
        }
    }
    /**
     * 设置蓝牙可被发现状态.
     *
     * @param btFoundStatus 可被发现状态.
     * @return Boolean
     */
    fun setBtFoundStatus(btFoundStatus: Boolean) {
        // 创建意图以请求蓝牙可见性
        if (mBluetoothAdapter?.getScanMode() == BluetoothAdapter.SCAN_MODE_CONNECTABLE_DISCOVERABLE) {
            // 当前蓝牙可见
            if (btFoundStatus){
                sendResultCode("open_bluetooth_founded_switch_2_1")
            }else{
                val discoverableIntent = Intent(BluetoothAdapter.ACTION_REQUEST_DISCOVERABLE).apply {
                    // 设置可见性持续时间（秒），最大值为3600秒（1小时）
                    putExtra(BluetoothAdapter.EXTRA_DISCOVERABLE_DURATION, 0) // 关闭
                    // 检查是否为非Activity上下文，如果是则添加FLAG_ACTIVITY_NEW_TASK标志
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                // 启动活动请求可见性
                MyApplication.getContext().startActivity(discoverableIntent)
                sendResultCode("close_bluetooth_founded_switch_1_1")
            }
        } else {
            // 当前蓝牙不可见
            if (btFoundStatus){
                val discoverableIntent = Intent(BluetoothAdapter.ACTION_REQUEST_DISCOVERABLE).apply {
                    // 设置可见性持续时间（秒），最大值为3600秒（1小时）
                    putExtra(BluetoothAdapter.EXTRA_DISCOVERABLE_DURATION, 300) // 5分钟
                    // 检查是否为非Activity上下文，如果是则添加FLAG_ACTIVITY_NEW_TASK标志
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                // 启动活动请求可见性
                MyApplication.getContext().startActivity(discoverableIntent)
                sendResultCode("open_bluetooth_founded_switch_1_1")
            }else{
                sendResultCode("close_bluetooth_founded_switch_2_1")
            }
        }
    }

    /**
     * 跳转蓝牙设置界面
     */
    fun openBluetoothDialog(flag: Boolean){
        // 对 offsetX 进行相应处理
        val intentBluetooth = Intent(
            MyApplication.getContext(),
            MainActivity::class.java
        )
        // 传递目标Fragment的索引，ConnectFragment对应MainTabIndex.CONNECT(8)
        intentBluetooth.putExtra("target_dialog", 0)
        if (flag){
            btViewModel=BtViewModel()
            btViewModel?.setBluetoothStatus(true)
            //连接蓝牙指令
            intentBluetooth.putExtra("operation",1)
        }else{
            //断开蓝牙指令
            // 检查是否有已连接的蓝牙设备
            val list =carBtManager.getConnectedDevices()
            if (list.size>0) {
                disconnectBluetoothDevices()
                sendResultCode("disconnect_bluetooth_2")
            } else {
                sendResultCode("disconnect_bluetooth_1")
            }

        }
        // 如果在非Activity环境中启动，需要添加NEW_TASK标志
        intentBluetooth.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        MyApplication.getContext().startActivity(intentBluetooth)
    }
    private fun disconnectBluetoothDevices() {
        //获取当前已连接蓝牙设备
        val list =carBtManager.getConnectedDevices()
        //断开列表中的所有设备的蓝牙连接
        for (device in list) {
            carBtManager.disconnectedDevice(device)
        }
    }

    /**
     * 蓝牙扫描
     */
    fun startScanBtDevice(){
        val intentBluetooth = Intent(
            MyApplication.getContext(),
            MainActivity::class.java
        )

        // 传递目标Fragment的索引，ConnectFragment对应MainTabIndex.CONNECT(8)
        intentBluetooth.putExtra("target_dialog", 0)
        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        // 如果在非Activity环境中启动，需要添加NEW_TASK标志
        intentBluetooth.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        MyApplication.getContext().startActivity(intentBluetooth)
        //当前蓝牙是否打开
        if (!mBluetoothAdapter?.isEnabled!!) {
            //打开蓝牙
            mBluetoothAdapter?.enable()
        }
        btViewModel=BtViewModel()
        btViewModel!!.startScanBtDevice(isAutoScan = true, isHand = true)
        sendResultCode("search_bluetooth_device_1")
    }
    /**
     * 设置wifi
     * @param flag
     */
    fun setWifi(flag: Boolean) {
        //判断当前wifi是否已经打开
        wifiViewModel=WifiViewModel()
        if (wifiViewModel?.getWifiState() == true) {
            if (flag) {
                sendResultCode("open_wifi_2")
            } else {
                wifiViewModel?.setWifiState(false)
                sendResultCode("close_wifi_1")
            }
        }else{
            if (flag) {
                wifiViewModel?.setWifiState(true) // 打开WiFi
                sendResultCode("open_wifi_1")
            } else {
                sendResultCode("close_wifi_2")
            }
        }
    }

    /**
     * 设置热点开关
     * @param flag ture 打开热点,false 关闭热点
     */
    fun setHotspot(flag: Boolean) {
        val state = carWifiManager.getHotspotState()
        val event = VDBus.getDefault().getOnce(VDEventPhoneLink.PHONE_STATE) // 失败了会返回null
        if (event != null) {
            val bundle = event.payload
            val mode = bundle.getInt(VDKey.STATUS)
            val type = bundle.getInt(VDKey.TYPE)
            if (state) {
                //热点已打开
                if (flag) {
                    //打开热点
                    sendResultCode("open_hotspot_2")
                } else {
                    //关闭热点
                    val resultCode = if (mode > 0) {
                        when (type) {
                            CarSettingConstant.CARPLAY -> "close_hotspot_3"   // Carplay
                            CarSettingConstant.CARLINK -> "close_hotspot_4"   // Carlink
                            CarSettingConstant.HICAR -> "close_hotspot_5"   // Hicar
                            else -> "close_hotspot_1" // 其他连接类型（保留默认，与原逻辑一致）
                        }
                    } else {
                        "close_hotspot_1" // 无连接状态
                    }
                    carWifiManager.closeWifiHotspot()
                    sendResultCode(resultCode)
                }
            } else {
                //热点已关闭
                if (flag) {
                    carWifiManager.openWifiHotspot()
                    sendResultCode("open_hotspot_1")
                } else {
                    sendResultCode("close_hotspot_2")
                }
            }
        }
    }

    /**
     * 移动网络设置
     */
    fun set5G(flag:Boolean) {
        val state = manager.fiveGNetwork
        if (state == 0){
            //未打开
            if (flag){
                //打开5G开关
                manager.fiveGNetwork = 1
                sendResultCode("open_mobile_data_1")
            }else{
                sendResultCode("close_mobile_data_1")
            }
        }else{
            if (flag){
                sendResultCode("open_mobile_data_2")
            }else{
                manager.fiveGNetwork = 0
                sendResultCode("close_mobile_data_2")
            }
        }
    }

    /**
     * 设置网络通知开关
     */
    fun setNetWorkNotification(flag:Boolean){
        val state = carWifiManager.getNetWorkNotification()
        if(state){
            //网络通知已打开
            if(flag){
                //打开网络通知
                sendResultCode("open_network_notification_2")
            }else{
                //关闭网络通知
                carWifiManager.setNetWorkNotification(false)
                sendResultCode("close_network_notification_1")
            }
        }else{
            //网络通知热点已关闭
            if(flag){
                carWifiManager.setNetWorkNotification(true)
                sendResultCode("open_network_notification_1")
            }else{
                sendResultCode("close_network_notification_2")
            }
        }
    }

    /**
     * 设置设备名称
     */
    fun setDeviceName(flag: Boolean){
        val intent = Intent(MyApplication.getContext(), MainActivity::class.java).apply {
            putExtra("target_dialog", 3) // 添加标识
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        MyApplication.getContext().startActivity(intent)
        sendResultCode("Modif_name_of_the_vehicle_equipment_1")
    }
    //设置热点密码
    fun setHotspotPassword(){
        Log.d("zhc6whu", "setHotspotPassword:打开热点列表 ")
        //获取当前是否打开了热点界面
        wifiViewModel=WifiViewModel()
        if (wifiViewModel?.getHotspotState()==true){
            val intentHotspot = Intent(
                MyApplication.getContext(),
                MainActivity::class.java
            )
            // 传递目标Fragment的索引，ConnectFragment对应MainTabIndex.CONNECT(8)
            intentHotspot.putExtra("target_dialog", 2)
            intentHotspot.putExtra("operation", 1)
            // 如果在非Activity环境中启动，需要添加NEW_TASK标志
            intentHotspot.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            MyApplication.getContext().startActivity(intentHotspot)
        }else{
            sendResultCode("set_hotspot_password_3")
        }
    }

    /**
     * 打开热点界面
     */
    fun openHotspot(flag: Boolean){
        if (flag){
            val intentHotspot = Intent(
                MyApplication.getContext(),
                MainActivity::class.java
            )
            intentHotspot.putExtra("target_dialog", 2)
            intentHotspot.putExtra("operation", 0)
            // 如果在非Activity环境中启动，需要添加NEW_TASK标志
            intentHotspot.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            MyApplication.getContext().startActivity(intentHotspot)
        }else{
            //关闭热点界面
            val intentHotspot = Intent(
                MyApplication.getContext(),
                MainActivity::class.java
            )
            intentHotspot.putExtra("target_dialog", 4)
            // 如果在非Activity环境中启动，需要添加NEW_TASK标志
            intentHotspot.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            MyApplication.getContext().startActivity(intentHotspot)
            //检查是否已有HotspotDialogFragment实例正在显示
            sendResultCode("Open_the_hotspot_list_3")
        }
    }

    /**
     * 打开WiFi界面
     */
    fun connectWifi(flag: Boolean){
        if (flag){
            // 对 offsetX 进行相应处理
            val intentWifi = Intent(
                MyApplication.getContext(),
                MainActivity::class.java
            )
            // 传递目标Fragment的索引，ConnectFragment对应MainTabIndex.CONNECT(8)
            intentWifi.putExtra("target_dialog", 1)
            // 如果在非Activity环境中启动，需要添加NEW_TASK标志
            intentWifi.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            MyApplication.getContext().startActivity(intentWifi)
            sendResultCode("connect_wifi_1")
        }else{
            val connectivityManager =
                MyApplication.getContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val networkInfo = connectivityManager.activeNetworkInfo
            if (networkInfo != null && networkInfo.isConnected && networkInfo.type == ConnectivityManager.TYPE_WIFI){
                // 断开当前连接的Wi-Fi
                val wifiManager =
                    MyApplication.getContext().applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
                wifiManager.disconnect()
                sendResultCode("disconnect_wifi_2")
            }else{
                sendResultCode("disconnect_wifi_1")
            }
        }
    }

    /**
     * 打开连接界面
     */
    fun setConnectPage(isOpen: Boolean) {
        if (isOpen) {
            //判断当前是否在连接界面
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.CONNECT) {
                sendResultCode("open_the_connection_settings_interface_2")
            } else {
                DialogNavigationUtils.launchMainActivity(
                    MyApplication.getContext(),
                    MainActivity.MainTabIndex.CONNECT,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode("open_the_connection_settings_interface_1")
            }
        } else {
            //判断当前是否在连接界面
            if (MainActivity.getCurrentMainTabIndex() == MainActivity.MainTabIndex.CONNECT) {
                DialogNavigationUtils.launchMainActivity(
                    MyApplication.getContext(),
                    MainActivity.MainTabIndex.CONNECT,
                    CommonConst.INVALID_DIALOG,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode("close_the_connection_settings_interface_2")
            } else {
                sendResultCode("close_the_connection_settings_interface_1")
            }
        }
    }

    /**
     * 显示WiFi连接界面
     */
    fun setConnectWifiPage(isOpen: Boolean) {
        if (isOpen) {
            if (WifiFragment.isShow) {
                sendResultCode("open_the_wifi_connection_interface_2")
            } else {
                DialogNavigationUtils.launchMainActivity(
                    MyApplication.getContext(),
                    MainActivity.MainTabIndex.CONNECT,
                    ConnectFragment.WIFI_FRAGMENT,
                    CommonConst.DIALOG_OPEN
                )
                sendResultCode("open_the_wifi_connection_interface_1")
            }
        } else {
            if (WifiFragment.isShow) {
                DialogNavigationUtils.launchMainActivity(
                    MyApplication.getContext(),
                    MainActivity.MainTabIndex.CONNECT,
                    ConnectFragment.WIFI_FRAGMENT,
                    CommonConst.DIALOG_CLOSE
                )
                sendResultCode("close_the_wifi_connection_interface_2")
            } else {
                sendResultCode("close_the_wifi_connection_interface_1")
            }
        }
    }

    /**
     * 查看蓝牙历史连接设备
     */
    fun viewBtHistory(){
        // 对 offsetX 进行相应处理
        val intentBluetooth = Intent(
            MyApplication.getContext(),
            MainActivity::class.java
        )

        // 传递目标Fragment的索引，ConnectFragment对应MainTabIndex.CONNECT(8)
        intentBluetooth.putExtra("target_dialog", 0)

        // 如果在非Activity环境中启动，需要添加NEW_TASK标志
        intentBluetooth.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        MyApplication.getContext().startActivity(intentBluetooth)
        sendResultCode("check_bluetooth_history_connect_device_1")
    }

    /**
     * 修改蓝牙名称
     */
    fun changeBtName(){
        val intent = Intent(MyApplication.getContext(), MainActivity::class.java).apply {
            putExtra("target_dialog", 3) // 添加标识
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        MyApplication.getContext().startActivity(intent)
        sendResultCode("Modif_name_of_the_vehicle_equipment_1")
    }

    /**
     * 删除配对蓝牙设备
     */
    fun deleteBtDevice(){
        // 对 offsetX 进行相应处理
        val intentBluetooth = Intent(
            MyApplication.getContext(),
            MainActivity::class.java
        )

        // 传递目标Fragment的索引，ConnectFragment对应MainTabIndex.CONNECT(8)
        intentBluetooth.putExtra("target_dialog", 0)
        //删除指令
        intentBluetooth.putExtra("operation", 0)

        // 如果在非Activity环境中启动，需要添加NEW_TASK标志
        intentBluetooth.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        MyApplication.getContext().startActivity(intentBluetooth)
    }

    /**
     * 打开or关闭来电播报
     */
    fun setCallNotification(flag: Boolean){
        val status = mVoicePresenter.getCallBroadcast()
        if (flag){//打开
            if (status == 0){
                mVoicePresenter.setCallBroadcast(1)
                sendResultCode("open_call_broadcast_1")
            }else{
                sendResultCode("open_call_broadcast_2")
            }
        }else{//关闭
            if (status == 1){
                mVoicePresenter.setCallBroadcast(0)
                sendResultCode("close_call_broadcast_1")
            }else{
                sendResultCode("close_call_broadcast_2")
            }
        }
    }
}