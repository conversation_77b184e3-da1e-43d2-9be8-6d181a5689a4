package com.bitech.vehiclesettings.view.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.PixelFormat
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import com.bitech.vehiclesettings.R

class GlobalWifiDialog(context: Context) : Dialog(context, R.style.globalDialog) {

    private var windowManager: WindowManager? = null
    private var params: WindowManager.LayoutParams? = null
    private var isShowing = false
    private var dialogView: View? = null

    init {
        // 初始化WindowManager
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        // 初始化窗口参数
        params = WindowManager.LayoutParams().apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT
            format = PixelFormat.TRANSLUCENT
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
            gravity = Gravity.CENTER

            // 根据Android版本设置不同的type
            type = 2020
        }

        // 设置Dialog的属性
        setCanceledOnTouchOutside(true)
        setCancelable(true)
    }

    override fun show() {
        try {
            if (!isShowing) {
                dialogView = window?.decorView
                dialogView?.setOnTouchListener { v, event ->
                    if (event?.action == MotionEvent.ACTION_DOWN) {
                        val dialogWidth = dialogView?.measuredWidth ?: 0
                        val dialogHeight = dialogView?.measuredHeight ?: 0
                        val x = event.x
                        val y = event.y

                        // 判断点击是否在对话框外部
                        if (x < 0 || x > dialogWidth || y < 0 || y > dialogHeight) {
                            dismiss()
                            return@setOnTouchListener true
                        }
                    }
                    false
                }

                windowManager?.addView(dialogView, params)
                isShowing = true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun dismiss() {
        try {
            if (isShowing) {
                windowManager?.removeViewImmediate(dialogView)
                isShowing = false
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置对话框是否全屏显示
     */
    fun setFullScreen(fullScreen: Boolean) {
        params?.width = if (fullScreen)
            WindowManager.LayoutParams.MATCH_PARENT
        else
            WindowManager.LayoutParams.WRAP_CONTENT

        params?.height = if (fullScreen)
            WindowManager.LayoutParams.MATCH_PARENT
        else
            WindowManager.LayoutParams.WRAP_CONTENT
    }

    /**
     * 设置对话框位置
     */
    fun setGravity(gravity: Int) {
        params?.gravity = gravity
    }

    /**
     * 设置背景是否透明
     */
    fun setBackgroundTransparent(transparent: Boolean) {
        if (transparent) {
            params?.flags = params?.flags?.and(WindowManager.LayoutParams.FLAG_DIM_BEHIND.inv())
        } else {
            params?.flags = params?.flags?.or(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            params?.dimAmount = 0.5f
        }
    }
}
