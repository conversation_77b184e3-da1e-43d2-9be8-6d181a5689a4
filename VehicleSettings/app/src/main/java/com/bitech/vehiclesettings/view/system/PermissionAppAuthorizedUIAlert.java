package com.bitech.vehiclesettings.view.system;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bitech.vehiclesettings.R;
import com.bitech.vehiclesettings.databinding.DialogAlertSPermissionAppAuthoirzedBinding;
import com.bitech.vehiclesettings.view.dialog.BaseDialog;

public class PermissionAppAuthorizedUIAlert extends BaseDialog {
    private static final String TAG = PermissionAppAuthorizedUIAlert.class.getSimpleName();
    private static PermissionAppAuthorizedUIAlert.onProgressChangedListener onProgressChangedListener;
    private int position;

    public PermissionAppAuthorizedUIAlert(@NonNull Context context) {
        super(context);
    }

    public PermissionAppAuthorizedUIAlert(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected PermissionAppAuthorizedUIAlert(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public static PermissionAppAuthorizedUIAlert.onProgressChangedListener getOnProgressChangedListener() {
        return onProgressChangedListener;
    }

    public static void setOnProgressChangedListener(PermissionAppAuthorizedUIAlert.onProgressChangedListener onProgressChangedListener) {
        PermissionAppAuthorizedUIAlert.onProgressChangedListener = onProgressChangedListener;
    }

    public interface OnDialogResultListener {
        void onDataReceived(String data);
    }

    private PermissionAppAuthorizedUIAlert.OnDialogResultListener listener;

    public static class Builder {

        private final Context context;
        private boolean isCan = true;
        protected DialogAlertSPermissionAppAuthoirzedBinding binding;

        int position;
        String title, content;

        public boolean isBlueOpen() {
            return isBlueOpen;
        }

        public void setBlueOpen(boolean blueOpen) {
            isBlueOpen = blueOpen;
        }

        private boolean isBlueOpen = false;
        private PermissionAppAuthorizedUIAlert dialog = null;
        private View layout;

        public Builder(Context context, int position, String title, String content) {
            this.context = context;
            this.position = position;
            this.title = title;
            this.content = content;
        }


        public PermissionAppAuthorizedUIAlert.Builder setCancelable(boolean isCan) {
            this.isCan = isCan;
            return this;
        }

        /**
         * Create the custom dialog
         */
        public PermissionAppAuthorizedUIAlert create() {
            // instantiate the dialog with the custom Theme
            if (dialog == null)
                dialog = new PermissionAppAuthorizedUIAlert(context,
                        R.style.Dialog);
            dialog.setCancelable(isCan);
            // 设置dialog的bind
            binding = DialogAlertSPermissionAppAuthoirzedBinding.inflate(LayoutInflater.from(context));
            dialog.setContentView(binding.getRoot());
            // 获取对话框的Window对象
            Window window = dialog.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = 1584;
            layoutParams.height = 608;
            window.setAttributes(layoutParams);

            // 设置文本
            setText();
            // 初始化按钮
            initButton();

            dialog.position = position;

            return dialog;
        }

        private void  initButton() {
            binding.tvOpen12Month.setOnClickListener(v -> {
                if (onProgressChangedListener != null) {
                    onProgressChangedListener.setPermissionTime(position, 0);
                    onProgressChangedListener.updatePermissionList(position);
                }
                dialog.dismiss();
            });
            binding.tvOpenThisTime.setOnClickListener(v -> {
                if (onProgressChangedListener != null) {
                    onProgressChangedListener.setPermissionTime(position, 1);
                    onProgressChangedListener.updatePermissionList(position);
                }
                dialog.dismiss();
            });
            binding.tvCancel.setOnClickListener(v -> {
                dialog.dismiss();
            });
        }

        private void setText() {
            binding.tvTitle.setText(title);
            binding.tvContent.setText(content);
        }

    }

    @Override
    public void cancel() {
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
        onProgressChangedListener.openPermissionDialog(position);
    }

    public interface onProgressChangedListener {
        void setPermissionTime(int position, int status);
        void updatePermissionList(int position);

        void openPermissionDialog(int position);
    }
}
