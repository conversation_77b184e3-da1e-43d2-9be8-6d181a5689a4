package com.bitech.vehiclesettings.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.bitech.vehiclesettings.bean.WifiHotspotBean

/**
 * @ClassName: WifiHotspotDao
 * 
 * @Date:  2024/2/6 14:02
 * @Description: this is a WifiHotspotDao class.
 **/
@Dao
interface WifiHotspotDao {

    /**
     * 从数据库表中查询出所有的热点已连接设备.
     *
     * @return
     */
    @Query("SELECT * FROM wifiHotspotDevices")
    fun getAllHotspotDevices(): MutableList<WifiHotspotBean>?

    /**
     * 将热点已连接设备插入数据库表.
     *
     * @param wifiHotspotBean 热点已连接设备.
     */
    @Insert(onConflict = OnConflictStrategy.IGNORE)   // 或者 REPLACE
    fun insertHotspotDevice(wifiHotspotBean: WifiHotspotBean)

    /**
     * 将热点已连接设备从数据库表中移除.
     *
     * @param wifiHotspotBean 热点已连接设备.
     */
    @Delete
    fun deleteHotspotDevice(wifiHotspotBean: WifiHotspotBean)

    /**
     * 清除热点已连接列表中的所有设备数据.
     *
     */
    @Query("DELETE FROM wifiHotspotDevices")
    fun deleteAllHotspotDevice()

    @Query("SELECT * FROM wifiHotspotDevices WHERE hotspotMac = :mac")
    suspend fun getDeviceByMac(mac: String): WifiHotspotBean?
}
