<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.bitech.vehiclesettings"
    android:sharedUserId="android.uid.system">
    <!--车模-->
    <queries>
        <package android:name="com.bitech.wallpaper3D" />
        <package android:name="com.iflytek.autofly.icvpservice.pro" />
    </queries>
    <uses-permission android:name="android.permission.READ_WALLPAPER_INTERNAL" />
    <!-- 文件读取权限  Android6.0 以后需要动态获取  10.0之后对文件的处理更复杂了 -->
    <uses-permission android:name="android.car.permission.CAR_CONTROL_AUDIO_SETTINGS" />
    <uses-permission android:name="android.car.permission.CAR_CONTROL_AUDIO_VOLUME" />

    <uses-permission android:name="android.permission.GRANT_RUNTIME_PERMISSIONS" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="android.permission.REVOKE_RUNTIME_PERMISSIONS" />
    <uses-permission android:name="android.permission.MANAGE_APP_OPS_MODES" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!--权限设置-->
    <uses-permission android:name="android.permission.MANAGE_DEVICE_ADMINS" />


    <!--蓝牙模块-->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!--    wifi模块-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />
    <uses-permission android:name="android.permission.NETWORK_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!--    读取设备信息-->
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.READ_GLOBAL_SETTINGS" />

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_INTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_INTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- services权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.STATUS_BAR" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />


    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.SET_TIME" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
    <uses-permission android:name="android.permission.INTERNAL_SYSTEM_WINDOW" />
    <uses-permission android:name="android.permission.NETWORK_SETUP_WIZARD" />
    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
    <uses-permission android:name="android.permission.ACCESS_INSTANT_APPS" />
    <uses-permission android:name="android.permission.NETWORK_STACK" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.car.permission.CAR_CONTROL_AUDIO_VOLUME" />
    <uses-permission android:name="android.car.permission.CAR_CONTROL_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.BIND_QUICK_SETTINGS_TILE" />

    <!--    恢复出厂设置-->
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.MASTER_CLEAR" />
    <uses-permission android:name="android.permission.ACCESS_SURFACE_FLINGER" />

    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
    <!--发送广播-->
    <protected-broadcast android:name="com.bitech.vehiclesettings.ACTION_TIME_FORMAT_CHANGED" />
    <protected-broadcast android:name="com.bitech.vehicleSetting.FACTORY_RESET" />
    <protected-broadcast android:name="com.chery.systemui.action.WIRELESS_CHARGE_DIALOG_SHOW" />
    <protected-broadcast android:name="com.chery.systemui.action.WIRELESS_CHARGE_DIALOG_DISMISS" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:exported="true"
        android:icon="@mipmap/icon_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/icon_launcher"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".activity.SplashActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustNothing|stateHidden">

        </activity>

        <activity
            android:name=".activity.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Transparent"
            android:windowSoftInputMode="adjustNothing|stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <provider
            android:name=".provider.VehicleSliceProvider"
            android:authorities="com.chery.carsettings.provider.slice.panel.headlamp;com.chery.phev.provider.slice.panel.passerby_protect_sound.switch;com.chery.carsettings.provider.slice.panel.clean_screen.function;com.chery.carsettings.provider.slice.panel.power.function;com.chery.carsettings.provider.slice.panel.brightness.seekbar;com.chery.carsettings.provider.slice.panel.sound.seekbar;com.chery.carsettings.provider.slice.panel.oil.switch;com.chery.carsettings.provider.slice.panel.lock_screen.function;com.chery.carsettings.provider.slice.panel.epb.switch;com.chery.carsettings.provider.slice.panel.esp.switch;com.chery.carsettings.provider.slice.panel.hdc.switch;com.chery.carsettings.provider.slice.panel.auto_hold.switch;com.chery.phev.provider.slice.panel.book_charge.switch;com.chery.carsettings.provider.slice.panel.wiper_level.function;com.chery.carsettings.provider.slice.panel.central_locking.switch;com.chery.carsettings.provider.slice.panel.mirror_fold;com.chery.carsettings.provider.slice.panel.mirror_adjust.function;com.chery.carsettings.provider.slice.panel.sunshade.function;com.chery.carsettings.provider.slice.panel.tailgate.function;com.chery.carsettings.provider.slice.panel.window_lock.switch;com.chery.carsettings.provider.slice.panel.child_lock.switch;com.chery.carsettings.provider.slice.panel.battery_life.switch;com.chery.carsettings.provider.slice.panel.light_mode.switch;com.chery.carsettings.provider.broadcast.tether;com.chery.carsettings.provider.slice.panel.senior_sound.function;com.chery.carsettings.provider.slice.panel.display_mode.switch;com.chery.carsettings.provider.slice.panel.window_mode.switch;com.chery.carsettings.provider.slice.panel.parking_radar.switch"
            android:exported="true"
            android:grantUriPermissions="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.app.slice.category.SLICE" />

                <data
                    android:host="beginnerguide.mega.com"
                    android:pathPrefix="/"
                    android:scheme="http" />
            </intent-filter>
        </provider>

        <provider
            android:name=".provider.ScreensaverProvider"
            android:authorities="com.bitech.screensaver.provider"
            android:exported="false" />

        <receiver
            android:name=".broadcast.EngModeReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.engmode.bitech.ResetSystem" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.bitech.vehiclesettings.service.VehicleService"
            android:exported="true" />

        <service
            android:name="com.bitech.vehiclesettings.service.system.SystemService"
            android:exported="true" />

        <service
            android:name="com.bitech.vehiclesettings.service.sound.VoiceService"
            android:exported="true" />

        <service
            android:name=".service.VoiceControlService"
            android:exported="true" />

        <!-- 监听开关机广播 -->
        <receiver
            android:name="com.bitech.vehiclesettings.broadcast.BootCompleteReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".broadcast.TimeChangeReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.chery.systemui.action.TIME_FORMAT" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIME_TICK" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".view.negative.VoiceBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.chery.systemui.dock.volume_dialog_show" />
                <action android:name="com.mega.hvac.action_hvac_state" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".broadcast.SliceReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="SliceReceiver.broadcast.PowerOff.click" />
                <action android:name="SliceReceiver.broadcast.light" />
                <action android:name="SliceReceiver.broadcast.voice" />
                <action android:name="SliceReceiver.broadcast.clean_screen" />
                <action android:name="SliceReceiver.broadcast.unlock_fuel_port" />
                <action android:name="SliceReceiver.broadcast.lock_screen" />
                <action android:name="SliceReceiver.broadcast.epb" />
                <action android:name="SliceReceiver.broadcast.esp" />
                <action android:name="SliceReceiver.broadcast.hdc" />
                <action android:name="SliceReceiver.broadcast.auto_hold" />
                <action android:name="SliceReceiver.broadcast.avas" />
                <action android:name="SliceReceiver.broadcast.book_charge" />
                <action android:name="SliceReceiver.broadcast.wiper_level" />
                <action android:name="SliceReceiver.broadcast.central_locking" />
                <action android:name="SliceReceiver.broadcast.mirror_fold" />
                <action android:name="SliceReceiver.broadcast.mirror_adjust" />
                <action android:name="SliceReceiver.broadcast.sentry" />
                <action android:name="SliceReceiver.broadcast.sunshade" />
                <action android:name="SliceReceiver.broadcast.tailgate" />
                <action android:name="SliceReceiver.broadcast.window_mode_open" />
                <action android:name="SliceReceiver.broadcast.window_mode_air" />
                <action android:name="SliceReceiver.broadcast.window_mode_close" />
                <action android:name="SliceReceiver.broadcast.child_lock" />
                <action android:name="SliceReceiver.broadcast.battery_life" />
                <action android:name="SliceReceiver.broadcast.light_mode" />
                <action android:name="SliceReceiver.broadcast.broadcast" />
                <action android:name="SliceReceiver.broadcast.display_mode_day" />
                <action android:name="SliceReceiver.broadcast.display_mode_night" />
                <action android:name="SliceReceiver.broadcast.display_mode_auto" />
                <action android:name="SliceReceiver.broadcast.turnoff" />
                <action android:name="SliceReceiver.broadcast.window_mode" />
                <action android:name="SliceReceiver.broadcast.parking_radar" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".widget.NewEnergyWidget"
            android:exported="false"
            android:label="@string/ne_new_energy">

            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.intent.action.WidgetProvider" />
                <action android:name="com.bitech.vehiclesettings.widget.search_charging_stations" />
                <action android:name="com.bitech.vehiclesettings.widget.new_energy_widget_refresh" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/new_energy_widget_info" />
        </receiver>

        <receiver
            android:name=".widget.TirePressureWidget"
            android:exported="false"
            android:label="@string/str_condition_tire_pressure">

            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.intent.action.WidgetProvider" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/tire_pressure_widget_info" />
        </receiver>

        <receiver
            android:name=".view.system.UserFeedBackUIAlert$VoiceActionReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.bitech.vehiclesettings.userfeedback.VoiceAction" />
            </intent-filter>
        </receiver>

        <provider
            android:name=".contentprovider.NewEnergyContentProvider"
            android:authorities="com.bitech.vehiclesettings.contentprovider.new_energy"
            android:enabled="true"
            android:exported="false" />

        <provider
            android:name=".provider.DrivingModeProvider"
            android:authorities="com.chery.carsettings.provider.drivemode"
            android:enabled="true"
            android:exported="false" />

        <provider
            android:name=".provider.PermissionProvider"
            android:authorities="com.chery.carsettings.provider.permission"
            android:enabled="true"
            android:exported="true" />
    </application>
</manifest>