<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="SkinTextView">
        <attr name="textType" format="integer" />
    </declare-styleable>

    <declare-styleable name="CommonOptionView">
        <attr name="enable" format="boolean" />
        <attr name="haveSwitch" format="boolean" />
        <attr name="haveTips" format="boolean" />
        <attr name="haveMore" format="boolean" />
        <attr name="swChecked" format="boolean" />
        <attr name="textTitle" format="string" />
        <attr name="textContent" format="string" />
    </declare-styleable>

    <declare-styleable name="SegmentedPickerView">
        <!-- 可自定义的选择器宽度和高度，单位为 dimension -->
        <attr name="pickerWidth" format="dimension" />
        <attr name="pickerHeight" format="dimension" />
        <attr name="pickerFontSize" format="dimension" />
        <attr name="marginSize" format="dimension" />
        <attr name="imageWidth" format="dimension" />
        <attr name="imageHeight" format="dimension" />
        <attr name="textImageSpacing" format="dimension" />
    </declare-styleable>

    <declare-styleable name="SlideToConfirmView">
        <attr name="text" format="string" />
        <attr name="textSize" format="dimension" />
        <attr name="textColor" format="color" />
        <attr name="backgroundColor" format="color" />
        <attr name="progressColor" format="color" />
        <attr name="thumbImage" format="reference" />
        <attr name="successThumbImage" format="reference" />
        <attr name="successText" format="string" />
        <attr name="successTextColor" format="color" />
        <attr name="cornerRadius" format="dimension" />
        <attr name="thumbWidth" format="dimension" />
        <attr name="thumbHeight" format="dimension" />
        <attr name="thumbMargin" format="dimension" />
        <attr name="progressPadding" format="dimension" />
    </declare-styleable>

    <declare-styleable name="ImageSeekBarView">
        <attr name="iconSrc" format="reference" />
        <attr name="iconWidth" format="dimension" />
        <attr name="iconHeight" format="dimension" />
        <attr name="iconMarginStart" format="dimension" />
        <attr name="min" format="integer" />
        <attr name="max" format="integer" />
        <attr name="progress" format="integer" />
        <attr name="seekBarWidth" format="dimension" />
        <attr name="seekBarHeight" format="dimension" />
        <attr name="radius" format="dimension" />
    </declare-styleable>

    <!-- 遮蔽效果 -->
    <declare-styleable name="maskProgressBar">
        <attr name="max1" format="float" />
        <attr name="progress2" format="float" />
        <attr name="start_angle" format="float" />
        <attr name="progress_background" format="reference" />
        <attr name="progress_content" format="reference" />
        <attr name="anim_time" format="float" />
    </declare-styleable>

    <!--fragment-->
    <declare-styleable name="BounceScrollView">
        <attr name="bgAnimate" format="boolean" />
        <attr name="scrollThreshold" format="dimension|integer" />
        <attr name="snapEnabled" format="boolean" />
    </declare-styleable>

    <!--车辆控制UI-->
    <declare-styleable name="TopDrawableTextView">
        <!-- Drawable 位置偏移 -->
        <attr name="drawableOffsetX" format="dimension" />
        <attr name="drawableOffsetY" format="dimension" />
        <!-- 文本位置偏移 -->
        <attr name="textOffsetX" format="dimension" />
        <attr name="textOffsetY" format="dimension" />
        <!-- Drawable 与文本间距 -->
        <attr name="customDrawablePadding" format="dimension" />
    </declare-styleable>

    <!--渐变图片-->
    <declare-styleable name="FadeBackgroundImageView">
        <attr name="fadeEnabled" format="boolean" />
        <attr name="fadeThreshold" format="dimension" />
        <attr name="initialAlpha" format="float" />
        <attr name="reverseFade" format="boolean" />
    </declare-styleable>

    <attr name="maskProgressStyle" format="reference" />
    <!--系统色-->
    <attr name="colorPrimaryDynamic" format="color|reference" />
    <attr name="colorPrimary" format="color" />

    <attr name="appPrimaryColor" format="color" />

    <!--自定义switch-->
    <declare-styleable name="UniversalSwitch">
        <attr name="android:id" />
        <attr name="android:layout_width" />
        <attr name="android:layout_height" />
        <attr name="android:layout_gravity" />
        <attr name="android:layout_marginLeft" />
        <attr name="android:background" />
        <attr name="android:checked" />
        <attr name="android:switchMinWidth" />
        <attr name="android:thumb" />
        <attr name="android:track" />
    </declare-styleable>
</resources>