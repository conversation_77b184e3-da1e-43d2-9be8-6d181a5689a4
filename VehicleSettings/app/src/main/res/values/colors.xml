<?xml version="1.0" encoding="utf-8"?>
<resources>

    <color name="main_bg_color">#E8EDF4</color>
    <color name="transparent">#00000000</color>
    <color name="common_black">#FF000000</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorRed">#E24F4F</color>
    <color name="colorRed_s">#EB8F8F</color>
    <color name="colorLight_80">#B3E66129</color>

    <color name="blue">#3a76ef</color>
    <color name="blue_s">#4D3A77F0</color>
    <color name="gray">#4DE5EEFF</color>
    <color name="gray_s">#B3E5EEFF</color>
    <color name="gray_switch">#E6E8EB</color>
    <color name="fragrance_type_bg">#1FE5EEFF</color>
    <color name="appBackColor">#F4F3F3</color>
    <color name="textColor">#333333</color>
    <color name="color_21aa4c">#21aa4c</color>
    <color name="quick_selected">#fafcff</color>
    <color name="gesture_navigation">#fafcff</color>
    <color name="gesture_navigation2">#121519</color>
    <color name="gallery_card">#1FFAFCFF</color>
    <color name="voice_alert_text">#949DA9</color>
    <color name="voice_reset">#1F121519</color>
    <color name="wifi_unselect">#CCCED1</color>
    <color name="select_border">#CCCED1</color>

    <!--进度条未选中-->
    <color name="progress_unselect">#14121519</color>
    <!--白色-->
    <color name="light_white">#FFFAFCFF</color>
    <color name="white">#FFFFFF</color>
    <color name="white_transparent_30">#4DFFFFFF</color>
    <color name="white_transparent_40">#66FFFFFF</color>
    <color name="white_transparent_60">#99FFFFFF</color>
    <color name="sys_reset_white">#FFFFFF</color>

    <color name="clean_white">#FFFFFF</color>
    <color name="black_12">#4D000000</color>
    <color name="black_60">#99000000</color>
    <color name="black">#000000</color>
    <color name="white_s">#FFFFFF</color>
    <color name="black_transparent_30">#4D000000</color>
    <color name="black_transparent_40">#66000000</color>
    <color name="black_transparent_60">#99000000</color>
    <!--黑色半透明-->
    <!--全透明-->
    <color name="color_seekbar_transparent_10">#19000000</color>
    <color name="color_transparent_10">#1F000000</color>
    <color name="half_transparent">#3f000000</color>
    <!--橙色-->
    <!--橘红色-->

    <color name="color_switch_white">#FFFAFCFF</color>
    <color name="color_white">#F4F6FA</color>
    <color name="color_spv">#14121519</color>
    <color name="color_time">#99121519</color>
    <color name="color_white_voice_expand">#14121519</color>
    <color name="color_white_10">#06121519</color>
    <color name="color_white_12">#14121519</color>
    <color name="color_white_50">#80FFFFFF</color>
    <color name="color_white_60">#99FFFFFF</color>
    <color name="color_white_80">#B3FFFFFF</color>
    <color name="color_white_end">#EFF2F6</color>
    <color name="color_black">#121519</color>
    <color name="color_transparent_100">#FF000000</color>
    <color name="color_transparent_80">#CD000000</color>
    <color name="color_transparent_60">#99000000</color>
    <color name="color_transparent_40">#66000000</color>
    <color name="color_transparent_30">#4D000000</color>
    <color name="color_transparent_20">#33000000</color>
    <color name="color_transparent_8">#14000000</color>

    <color name="color_white_transparent_70">#B3FFFFFF</color>
    <color name="color_white_transparent_60">#99FFFFFF</color>
    <color name="color_white_transparent_50">#80FFFFFF</color>
    <color name="color_black_transparent_50">#80000000</color>
    <color name="ss">#FE210A</color>

    <!--氛围灯-->
    <color name="color_1">#FF0700</color>
    <color name="color_2">#FF5A03</color>
    <color name="color_3">#FFEB05</color>
    <color name="color_4">#00FF05</color>
    <color name="color_5">#06FBFF</color>
    <color name="color_6">#0524FF</color>
    <color name="color_7">#AD29FF</color>
    <color name="color_8">#AD29FF</color>
    <color name="color_9">#AD29FF</color>
    <color name="color_10">#AD29FF</color>
    <color name="color_11">#AD29FF</color>
    <color name="color_12">#EFF2F6</color>
    <color name="color_13">#F6F8FA</color>

    <!--系统色-->
    <color name="system_color_blue">#3a76ef</color>
    <color name="system_color_purple">#7758a2</color>
    <color name="system_color_cyan">#57a2a2</color>
    <color name="system_color_green">#85a359</color>
    <color name="system_color_orange">#bd9c5c</color>
    <color name="system_color_red">#a3575e</color>

    <!-- 默认颜色 -->
<!--    <color name="color_primary_default">#3a76ef</color> &lt;!&ndash; 使用blue作为默认 &ndash;&gt;-->

    <!-- 动态颜色占位符 -->
    <color name="dynamic_primary">?attr/colorPrimaryDynamic</color>

    <color name="color_00000000">#00000000</color>
    <color name="color_ECEFF4">#ECEFF4</color>
    <color name="color_747578">#747578</color>
    <color name="color_5E6062">#5E6062</color>
    <color name="color_17191E">#17191E</color>
    <color name="color_D3DBE3">#D3DBE3</color>
    <color name="color_F7F8F9">#F7F8F9</color>
    <color name="color_3292F5">#3292F5</color>
    <color name="color_E5EAEF">#E5EAEF</color>
    <color name="color_8E8F98">#8E8F98</color>
    <color name="color_e24f4f">#E24F4F</color>
    <color name="color_2B7ACE">#2B7ACE</color>
    <color name="color_17d58c">#17D58C</color>
    <color name="color_121519">#121519</color>
    <color name="color_121519_60">#99121519</color>
    <color name="gesture_navigation3">#99121519</color>
    <color name="color_121519_12">#1F121519</color>

    <color name="color_btn_disable">#b9cdf4</color>
    <color name="color_radio_gray">#bfc2c5</color>

    <color name="text_color_1">#121519</color>
    <color name="text_color_2">#99121519</color>
    <color name="text_color_error">#E24F4F</color>
    <color name="bg_solid_color_1">#FFFFFF</color>
    <color name="bg_seekbar_progress_green">#00B23A</color>
    <color name="bg_seekbar_progress_1">#14000000</color>
    <color name="bg_seekbar_progress_2">#1E121519</color>
    <color name="bg_warning_start">#FF0000</color>
    <color name="bg_warning_end">#4D1C27</color>
    <color name="chart_line_green">#ff21aa4c</color>
    <color name="chart_line_blue">#ff0091ff</color>

    <color name="bg_seekbar_confirm">#21aa4c</color>
    <color name="bg_seekbar_cancel">#de5f0d</color>

    <color name="color_dialog_content">#99000000</color>
    <color name="color_dialog_border_bg">#EFF2F6</color>
    <color name="color_dialog_border_voice">#E9ECF1</color>
    <color name="color_transparent_1">#4DE5EEFF</color>
    <color name="color_dialog_cancel_n">#FFFFFF</color>
    <color name="color_toast">#FFFFFF</color>
    <color name="card_bg_dark">#1FFAFCFF</color>
    <color name="color_dark_60">#99FAFCFF</color>
    <color name="color_detail_60">#99121519</color>

    <color name="color_dividing_line">#33727272</color>
    <color name="color_wallpaper_gray_text">#99121519</color>
    <color name="color_chirld_lock_line">#979797</color>
    <color name="color_driving_personal">#14121519</color>
    <color name="color_condition_warning">#F78F04</color>
    <color name="color_quick_n">#1FFAFCFF</color>

    <color name="color_bluetooth_connection_code">#D8DBE0</color>

    <color name="background_layer">#E9ECF1</color>
    <color name="warning_text">#121519</color>

    <!--充放电状态灵动岛-->
    <color name="charging_bar_bg_start_color">#17A617</color>
    <color name="charging_bar_bg_end_color">#21C721</color>
    <color name="discharging_bar_bg_start_color">#219CD3</color>
    <color name="discharging_bar_bg_end_color">#27BBFC</color>
    <color name="text_status_bar_charging">#121519</color>
    <color name="status_bar_charging_background">#B7BABE</color>
    <color name="status_bar_discharging_background">#A0A3A8</color>
</resources>
