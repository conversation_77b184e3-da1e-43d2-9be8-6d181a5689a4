<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--车辆中心dialog主题样式-->
    <style name="dialog" parent="@android:style/Theme.Dialog">
        <!-- 有无边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--无标题 -->
        <item name="android:windowNoTitle">true</item>
    </style>

    <!--车辆中心dialog主题样式-->
    <style name="globalDialog" parent="@android:style/Theme.Dialog">
        <!-- 有无边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--无标题 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <!--车辆中心radiobutton 选中文字样式-->
    <style name="settings_radiobutton_tv_style">
        <item name="android:textColor">@color/color_ECEFF4</item>
        <item name="android:textSize">@dimen/sp_34</item>
<!--        <item name="android:fontFamily">@font/roboto_medium</item>-->
        <item name="android:includeFontPadding">false</item>
    </style>

    <!--车辆中心radiobutton 未选中文字样式-->
    <style name="settings_radiobutton_no_tv_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_34</item>
<!--        <item name="android:fontFamily">@font/roboto_medium</item>-->
        <item name="android:includeFontPadding">false</item>
    </style>

    <!--车辆中心开关文字说明样式-->
    <style name="settings_switch_tv_style">
        <item name="android:gravity">center|left</item>
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/sp_24</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
        <item name="android:ellipsize">end</item>
        <item name="android:padding">@dimen/dp_10</item>
    </style>

    <!--车辆中心开关文字说明样式-->
    <style name="settings_switch_tv_disable_style">
        <item name="android:gravity">center|left</item>
        <item name="android:textColor">@color/color_D3DBE3</item>
        <item name="android:textSize">@dimen/sp_36</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
        <item name="android:ellipsize">end</item>
        <item name="android:padding">@dimen/dp_10</item>
    </style>

    <!--————————————————————————————————— 车辆中心标准文言主题 ——————————————————————————————————————-->
    <!--文言主题格式-->
    <style name="settings_text_48_regular_eceff4_style">
        <item name="android:textColor">@color/color_ECEFF4</item>
        <item name="android:textSize">@dimen/sp_48</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_48_medium_17191e_style">
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/sp_48</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_40_regular_17191e_style">
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/sp_36</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_40_regular_747578_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_40</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_40_medium_17191e_style">
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/sp_40</item>
<!--        <item name="android:fontFamily">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_40_medium_eceff4_style">
        <item name="android:textColor">@color/color_ECEFF4</item>
        <item name="android:textSize">@dimen/sp_40</item>
<!--        <item name="android:fontFamily">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_40_medium_f7f8f9_style">
        <item name="android:textColor">@color/color_F7F8F9</item>
        <item name="android:textSize">@dimen/sp_40</item>
<!--        <item name="android:fontFamily">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_40_medium_747578_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_40</item>
<!--        <item name="android:fontFamily">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_36_regular_17191e_style">
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">@dimen/sp_24</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_36_regular_3292F5_style">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/sp_24</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>
    <!--文言主题格式-->
    <style name="settings_text_19sp_regular_3292F5_style">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/sp_19</item>
        <!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <style name="settings_text_19sp_regular_AAABAF_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_19</item>
        <!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_36_regular_d3dbe3_style">
        <item name="android:textColor">@color/color_D3DBE3</item>
        <item name="android:textSize">@dimen/sp_36</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_36_medium_17191e_style">
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/sp_36</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_36_medium_d3dbe3_style">
        <item name="android:textColor">@color/color_D3DBE3</item>
        <item name="android:textSize">@dimen/sp_36</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_36_medium_747578_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_36</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_36_medium_eceff4_style">
        <item name="android:textColor">@color/color_ECEFF4</item>
        <item name="android:textSize">@dimen/sp_36</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_34_regular_17191e_style">
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/dp_34</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_34_regular_d3dbe3_style">
        <item name="android:textColor">@color/color_D3DBE3</item>
        <item name="android:textSize">@dimen/dp_34</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_34_medium_17191e_style">
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/sp_34</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_34_regular_747578_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_34</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_34_regular_f7f8f9_style">
        <item name="android:textColor">@color/color_F7F8F9</item>
        <item name="android:textSize">@dimen/sp_34</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_32_regular_17191e_style">
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/sp_32</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_32_regular_d3dbe3_style">
        <item name="android:textColor">@color/color_D3DBE3</item>
        <item name="android:textSize">@dimen/sp_32</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_32_regular_747578_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_32</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_32_regular_disable_style">
        <item name="android:textColor">@color/color_D3DBE3</item>
        <item name="android:textSize">@dimen/sp_32</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_32_medium_747578_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_32</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_32_medium_e5eaef_style">
        <item name="android:textColor">@color/color_E5EAEF</item>
        <item name="android:textSize">@dimen/sp_32</item>
<!--        <item name="android:font">@font/roboto_medium</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_32_regular_3292f5_style">
        <item name="android:textColor">@color/color_3292F5</item>
        <item name="android:textSize">@dimen/sp_30</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_30_regular_747578_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_30</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_30_regular_8e8f98_style">
        <item name="android:textColor">@color/color_8E8F98</item>
        <item name="android:textSize">@dimen/sp_30</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_30_regular_d3dbe3_style">
        <item name="android:textColor">@color/color_D3DBE3</item>
        <item name="android:textSize">@dimen/sp_30</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_30_regular_17191e_style">
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textSize">@dimen/sp_30</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言主题格式-->
    <style name="settings_text_30_regular_e24f3f_style">
        <item name="android:textColor">@color/color_e24f4f</item>
        <item name="android:textSize">@dimen/sp_30</item>
<!--        <item name="android:font">@font/cn_regular</item>-->
    </style>

    <!--文言格式-->
    <style name="settings_text_40_medium_style">
        <item name="android:textSize">@dimen/sp_40</item>
<!--        <item name="android:fontFamily">@font/roboto_medium</item>-->
    </style>

    <!--车辆中心内容滚动条格式-->
    <style name="settings_scroll_bar_style">
        <item name="android:scrollbars">vertical</item>
<!--        <item name="android:scrollbarThumbVertical">@drawable/scroll_bar_thumb_bg</item>-->
    </style>

    <!--车辆中心内容滚动条格式2-->
    <style name="settings_scroll_bar_style2">
        <item name="android:scrollbars">vertical</item>
<!--        <item name="android:scrollbarThumbVertical">@drawable/scroll_bar_thumb_bg_2</item>-->
    </style>

    <!--车辆中心内容区域卡片格式-->
    <style name="settings_content_card_style">
<!--        <item name="android:background">@drawable/card_bg</item>-->
        <item name="android:padding">@dimen/dp_0</item>
    </style>

    <!--车辆中心图标格式 48x48-->
    <style name="settings_icon_48x48_style">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
    </style>

    <!--——————————————————————————————————— 车辆中心开关主题 ———————————————————————————————————————-->
    <!--车辆中心开关设置样式-->
    <style name="settings_switch_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:clickable">true</item>
        <item name="android:showText">false</item>
<!--        <item name="android:track">@drawable/switch_track_bg</item>-->
<!--        <item name="android:thumb">@drawable/switch_thumb_bg</item>-->
    </style>

    <!--车辆中心车速音量选项组背景格式-->
    <style name="settings_sound_speed_button_group_style">
<!--        <item name="android:background">@mipmap/switch_bg_912</item>-->
    </style>

    <!--车辆中心报警音类型选项按钮样式-->
    <style name="settings_sound_warning_type_style">
        <item name="android:button">@null</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textAppearance">@style/settings_radiobutton_tv_style</item>
<!--        <item name="android:background">@drawable/tab_button_bg_299_72</item>-->
    </style>

    <!--车辆中心dialog背景样式格式-->
    <style name="dialog_background_984_520_style">
<!--        <item name="android:background">@drawable/_9_popup_bg_984_520</item>-->
    </style>

    <!--车辆中心新能源调节进度条样式-->
    <style name="settings_new_energy_progress_style" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:thumbOffset">@dimen/px_4</item>
        <!-- 设置进度条轨道不分割，滑块直接在轨道上滑动 -->
        <item name="android:splitTrack">false</item>
<!--        <item name="android:progressDrawable">@drawable/seekbar_progress_two_level</item>-->
        <item name="android:thumb">@mipmap/ic_sound_slider</item>
    </style>

    <!--车辆中心弹窗确认按钮背景样式543_98-->
    <style name="dialog_btn_confirm_534_98_style">
<!--        <item name="android:layout_width">@dimen/dp_534</item>-->
        <item name="android:layout_height">@dimen/dp_98</item>
<!--        <item name="android:background">@drawable/dialog_btn_confirm_534_98_bg</item>-->
        <item name="android:gravity">center</item>
        <item name="android:padding">@dimen/dp_0</item>
        <item name="android:textColor">@color/color_ECEFF4</item>
        <item name="android:textAppearance">@style/settings_text_40_medium_style</item>
    </style>

    <!--车辆中心弹窗关闭按钮背景样式543_98-->
    <style name="dialog_btn_cancel_534_98_style">
<!--        <item name="android:layout_width">@dimen/dp_534</item>-->
        <item name="android:layout_height">@dimen/dp_98</item>
<!--        <item name="android:background">@drawable/dialog_btn_cancel_534_98_bg</item>-->
        <item name="android:gravity">center</item>
        <item name="android:padding">@dimen/dp_0</item>
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textAppearance">@style/settings_text_40_medium_style</item>
    </style>

    <!--车辆中心dialog背景样式格式-->
    <style name="dialog_background_1248_1054_style">
<!--        <item name="android:background">@mipmap/popup_bg_1248_1054</item>-->
    </style>

    <!--车辆中心图标格式 96x96-->
    <style name="settings_icon_96x96_style">
        <item name="android:layout_width">@dimen/dp_96</item>
        <item name="android:layout_height">@dimen/dp_96</item>
    </style>
    <!--车辆中心弹窗确认按钮文字背景样式-->
    <style name="dialog_btn_confirm_402_92_style">
        <item name="android:layout_width">267dp</item>
        <item name="android:layout_height">67dp</item>
        <item name="android:background">@drawable/dialog_btn_bg_selected</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">@dimen/dp_0</item>
        <item name="android:textColor">@color/color_F7F8F9</item>
        <item name="android:textAppearance">@style/settings_text_40_medium_style</item>
    </style>
    <!--车辆中心弹窗取消按钮文字背景样式-->
    <style name="dialog_btn_cancel_402_92_style">
        <item name="android:layout_width">267dp</item>
        <item name="android:layout_height">67dp</item>
        <item name="android:background">@drawable/dialog_btn_bg_unselected</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">@dimen/dp_0</item>
        <item name="android:textColor">@color/color_17191E</item>
        <item name="android:textAppearance">@style/settings_text_40_medium_style</item>
    </style>
    <!--车辆中心名称编辑框文言格式-->
    <style name="settings_edit_text_style">
        <item name="android:textColor">@color/color_747578</item>
        <item name="android:textSize">@dimen/sp_32</item>
    </style>
    <!--车辆中心弹窗确认按钮文字背景样式-->
    <style name="dialog_btn_confirm_223_72_style">
        <item name="android:background">@drawable/switch_btn_223_72</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/color_F7F8F9</item>
        <item name="android:textAppearance">@style/settings_text_40_medium_style</item>
    </style>
    <!--车辆中心toast背景样式格式-->
    <style name="toast_background_style">
        <item name="android:background">@drawable/toast_bg_shape</item>
        <item name="android:padding">@dimen/dp_0</item>
    </style>
</resources>
