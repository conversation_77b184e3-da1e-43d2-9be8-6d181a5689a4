<resources>

    <!-- 完全透明主题 -->
    <style name="TransparentTheme" parent="AppTheme">
        <!-- 基本窗口设置 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowDisablePreview">true</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <!--Android 5.x开始需要把颜色设置透明，否则导航栏会呈现系统默认的浅灰色-->
        <item name="android:statusBarColor">@color/main_bg_color</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:navigationBarColor">@color/main_bg_color</item>
        <!--Android 5.x开始需要把颜色设置透明，否则导航栏会呈现系统默认的浅灰色-->
    </style>

    <!--系统色叠加主题-->
    <style name="OverlayThemeBlue" parent="ThemeOverlay.MaterialComponents.Light">
        <item name="appPrimaryColor">@color/system_color_blue</item>
    </style>

    <style name="OverlayThemePurple" parent="ThemeOverlay.MaterialComponents.Light">
        <item name="appPrimaryColor">@color/system_color_purple</item>
    </style>

    <style name="OverlayThemeCyan" parent="ThemeOverlay.MaterialComponents.Light">
        <item name="appPrimaryColor">@color/system_color_cyan</item>
    </style>

    <style name="OverlayThemeGreen" parent="ThemeOverlay.MaterialComponents.Light">
        <item name="appPrimaryColor">@color/system_color_green</item>
    </style>

    <style name="OverlayThemeOrange" parent="ThemeOverlay.MaterialComponents.Light">
        <item name="appPrimaryColor">@color/system_color_orange</item>
    </style>

    <style name="OverlayThemeRed" parent="ThemeOverlay.MaterialComponents.Light">
        <item name="appPrimaryColor">@color/system_color_red</item>
    </style>

    <style name="AppTheme.Dialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@null</item>  <!--除边框 -->
    </style>

    <style name="BaseDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@color/appBackColor</item>
        <!--        <item name="android:windowBackground">@android:color/transparent</item>-->
        <!--        <item name="android:windowFrame">@null</item>-->
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:textColor">@color/textColor</item>
    </style>

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="Transparent" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>

    <style name="scroll_bar">
        <item name="android:scrollbarStyle">outsideOverlay</item>
        <item name="android:scrollbarThumbVertical">@drawable/common_scrollbar_thumb</item>
        <item name="android:scrollbars">vertical</item>
        <item name="android:overScrollMode">never</item>

    </style>
    <!--车辆中心图标格式 112x112-->
    <style name="settings_icon_112x112_style">
        <item name="android:layout_width">112dp</item>
        <item name="android:layout_height">112dp</item>
    </style>

    <style name="fullscreen" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <!--Android 5.x开始需要把颜色设置透明，否则导航栏会呈现系统默认的浅灰色-->
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="GlobalDialogTheme" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>

</resources>
