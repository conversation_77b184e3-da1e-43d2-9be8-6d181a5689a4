<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <layer-list>
            <!-- 圆形背景（主题主色） -->
            <item>
                <shape android:shape="oval">
                    <solid android:color="?attr/appPrimaryColor" />
                </shape>
            </item>
            <!-- 勾选图标（白色） -->
            <item
                android:drawable="@drawable/ic_check_white"
                android:gravity="center" />
        </layer-list>
    </item>

    <!-- 未选中状态 -->
    <item android:state_selected="false">
        <shape android:shape="oval">
            <solid android:color="@color/wifi_unselect" />
            <stroke
                android:width="2dp"
                android:color="@color/select_border" />
        </shape>
    </item>
</selector>
