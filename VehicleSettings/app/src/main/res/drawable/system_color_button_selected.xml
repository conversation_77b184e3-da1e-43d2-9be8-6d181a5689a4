<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 外层：边框（按钮颜色） -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="8dp"/>
            <solid android:color="@android:color/transparent"/>
            <stroke android:width="2dp" android:color="?attr/colorAccent"/>
        </shape>
    </item>

    <!-- 中层：留白区域 -->
    <item android:top="4dp" android:bottom="4dp" android:left="4dp" android:right="4dp">
        <shape android:shape="rectangle">
            <corners android:radius="6dp"/>
            <solid android:color="?attr/colorAccent"/> <!-- 留白颜色 -->
        </shape>
    </item>

    <!-- 内层：按钮主体颜色（动态设置） -->
    <item android:top="8dp" android:bottom="8dp" android:left="8dp" android:right="8dp">
        <shape android:shape="rectangle">
            <corners android:radius="4dp"/>
            <solid android:color="?attr/colorPrimary"/> <!-- 按钮本身颜色 -->
        </shape>
    </item>
</layer-list>
