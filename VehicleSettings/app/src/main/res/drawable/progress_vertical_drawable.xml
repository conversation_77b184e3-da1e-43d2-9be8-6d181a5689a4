<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:id="@android:id/background">
        <shape>
            <solid android:color="#E5E5E5" />
            <!--下面是设置4个角的圆角-->
            <corners
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp"
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp" />
        </shape>
    </item>
    <item android:id="@android:id/progress">
        <scale
            android:scaleWidth="0%"
            android:scaleHeight="100%"
            android:scaleGravity="bottom">
            <shape>
                <!--这里是设置填充颜色和方向-->
                <gradient
                    android:angle="270"
                    android:endColor="#FC6589"
                    android:centerColor="#FC6589"
                    android:startColor="#B5FF9C"
                    android:type="linear" />
                <!--下面是设置4个角的圆角-->
                <corners
                    android:bottomLeftRadius="0dp"
                    android:bottomRightRadius="0dp"
                    android:topLeftRadius="0dp"
                    android:topRightRadius="0dp" />
            </shape>
        </scale>
    </item>
</layer-list>