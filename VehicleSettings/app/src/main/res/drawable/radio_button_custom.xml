<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="true">
        <shape>
            <solid android:color="@color/blue" /> <!-- 选中时的按钮颜色 -->
            <size
                android:width="24dp"
                android:height="24dp" /> <!-- 按钮大小 -->
            <corners android:radius="12dp" /> <!-- 按钮圆角 -->
        </shape>
    </item>
    <item android:state_checked="false">
        <shape>
            <stroke
                android:width="2dp"
                android:color="#CCCCCC" /> <!-- 未选中时的边框颜色 -->
            <size
                android:width="24dp"
                android:height="24dp" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>