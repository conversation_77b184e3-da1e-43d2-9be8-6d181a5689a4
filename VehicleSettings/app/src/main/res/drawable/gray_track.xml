<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:height="34.67dp">
        <shape
            android:shape="rectangle" >
            <!-- 高度  此处设置宽度无效-->
            <!-- 圆角弧度 12 -->
            <corners android:radius="8dp"/>

            <!-- 变化率 定义从左到右的颜色不变 -->
            <gradient
                android:endColor="@color/black_12"
                android:startColor="@color/black_12" />
        </shape>
    </item>
</layer-list>