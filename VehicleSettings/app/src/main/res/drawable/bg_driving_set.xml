<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 底层：图片 -->
    <item>
        <bitmap
            android:src="@drawable/bg_driving"
            android:gravity="fill" />
    </item>

    <!-- 上层：半透明遮罩 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#00ffffff" />
        </shape>
    </item>

    <!-- 上层：边框 -->
    <item>
        <shape android:shape="rectangle">
<!--            <stroke
                android:width="2dp"
                android:color="#FF0000" /> &lt;!&ndash; 红色边框 &ndash;&gt;-->
<!--            <corners android:radius="8dp" /> &lt;!&ndash; 圆角 &ndash;&gt;-->
        </shape>
    </item>
</layer-list>