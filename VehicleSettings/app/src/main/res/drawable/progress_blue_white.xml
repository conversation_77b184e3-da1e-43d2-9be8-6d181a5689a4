<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="8dp" />
            <solid android:color="@color/progress_unselect" />
        </shape>
        <bitmap
            android:gravity="center_vertical"
            android:tileMode="repeat" />
    </item>

    <item android:id="@android:id/progress">
        <scale android:scaleWidth="100%">
            <shape>
                <corners
                    android:bottomLeftRadius="8dp"
                    android:bottomRightRadius="2.66dp"
                    android:topLeftRadius="8dp"
                    android:topRightRadius="2.66dp" />
                <solid android:color="?attr/appPrimaryColor"></solid>
            </shape>
        </scale>
        <bitmap
            android:gravity="center_vertical"
            android:tileMode="repeat" />
    </item>

</layer-list>