<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:height="37.33dp">
        <shape
            android:shape="rectangle" >
            <!-- 高度  此处设置宽度无效-->
            <!-- 圆角弧度 15 -->
            <corners android:radius="10.66dp"/>

            <!-- 变化率 定义从左到右的颜色不变 -->
            <gradient
                android:endColor="@color/gray_switch"
                android:startColor="@color/gray_switch" />
        </shape>
    </item>
</layer-list>