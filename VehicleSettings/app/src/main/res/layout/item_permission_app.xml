<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginBottom="8dp">

        <RelativeLayout
            android:id="@+id/rl_app"
            android:layout_width="554.67dp"
            android:layout_height="100dp"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/shape_bg_white">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="32dp"
                android:layout_marginRight="32dp">

                <TextView
                    android:id="@+id/tv_app_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:text="应用名称"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_permission_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:text="仅在使用中允许"
                    android:textColor="@color/black_transparent_40"
                    android:textSize="@dimen/font_36px" />

            </RelativeLayout>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginTop="57.34dp"
                android:layout_marginRight="10.67dp"
                android:src="@mipmap/ic_small_marker" />

        </RelativeLayout>

</LinearLayout>
