<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_system_permission_app_authorized_item"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <TextView
                android:id="@+id/tv_sub_title"
                android:layout_width="554.67dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:fontFamily="DreamHanSansCN"
                android:singleLine="false"
                android:text="@string/str_system_permission_camera_app_authorized_item"
                android:textColor="@color/dialog_content_color"
                android:textSize="@dimen/font_30px" />


            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="66.67dp"
                android:layout_gravity="center"
                android:layout_marginTop="10dp"
                android:background="@drawable/shape_bg_white"
                android:visibility="gone">


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_system_permission_app_authorized_item_1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <RadioButton
                    android:id="@+id/rb_each_use"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/dp_32"
                    android:layout_marginRight="@dimen/dp_21.33"
                    android:background="@drawable/radio_custom"
                    android:button="@drawable/radio_custom" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="66.67dp"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:background="@drawable/shape_bg_white">


                <LinearLayout
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="@dimen/weight_1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_32"
                        android:text="@string/str_system_permission_app_authorized_item_2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <RadioButton
                    android:id="@+id/rb_use_period_only"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/dp_32"
                    android:layout_marginRight="@dimen/dp_21.33"
                    android:background="@drawable/radio_custom"
                    android:button="@drawable/radio_custom" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="66.67dp"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:background="@drawable/shape_bg_white">


                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="@dimen/weight_1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_system_permission_app_authorized_item_3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <RadioButton
                    android:id="@+id/rb_disabled"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/dp_32"
                    android:layout_marginRight="@dimen/dp_21.33"
                    android:foreground="@drawable/radio_custom" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>