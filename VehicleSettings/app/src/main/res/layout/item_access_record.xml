<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginBottom="8dp">

    <LinearLayout
        android:id="@+id/rl_menu"
        android:layout_width="match_parent"
        android:layout_height="133.33dp"
        android:background="@drawable/shape_bg_white"
        android:layout_marginBottom="16dp">

        <ImageView
            android:id="@+id/iv_record"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginLeft="32dp"
            android:layout_gravity="center_vertical"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="23.33dp"
            android:layout_marginTop="36.67dp"
            android:layout_marginRight="32dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_record_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_confirm2"
                android:textColor="@color/black"
                android:textSize="@dimen/font_34px" />

            <TextView
                android:id="@+id/tv_record_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_confirm2"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_30px" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
