<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="334.67dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="120dp"
        android:layout_marginEnd="120dp"
        android:layout_marginTop="90.67dp"
        android:fontFamily="HarmonyOS_Sans_SC"
        android:gravity="center"
        android:text="@string/str_condition_maintenance_reset_confirm"
        android:textColor="@color/black"
        android:textSize="@dimen/font_40px"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginEnd="80dp"
        android:layout_marginTop="98.67dp"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/selector_bg_red"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_confirm2"
            android:textColor="#ffffff"
            android:textSize="@dimen/font_36px" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="32dp"
            android:background="@drawable/selector_bg_cancel"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_cancel"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
    </LinearLayout>
</LinearLayout>