<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/border_bg_dialog">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/dialog_background_984_520_style"
        android:layout_width="784dp"
        android:layout_height="533dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.bitech.vehiclesettings.view.widget.ScanScrollView
            android:id="@+id/dialog_title_ssv"
            style="@style/settings_scroll_bar_style2"
            android:layout_width="@dimen/dp_0"
            android:layout_height="40dp"
            android:layout_marginStart="85dp"
            android:layout_marginTop="33dp"
            android:layout_marginEnd="@dimen/dp_77"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/dialog_title_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/bt_paired_code_text"
                    android:textColor="@color/black"
                    android:textSize="@dimen/sp_32"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.bitech.vehiclesettings.view.widget.ScanScrollView>

        <TextView
            android:id="@+id/settings_bt_paired_code_one_tv"
            style="@style/settings_text_48_medium_17191e_style"
            android:layout_width="53dp"
            android:layout_height="60dp"
            android:layout_marginStart="149dp"
            android:layout_marginTop="172dp"
            android:background="@drawable/shape_bg_bluetooth_code"
            android:gravity="center"
            android:textAlignment="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/settings_bt_paired_code_two_tv"
            style="@style/settings_text_48_medium_17191e_style"
            android:layout_width="53dp"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/dp_28"
            android:background="@drawable/shape_bg_bluetooth_code"
            android:gravity="center"
            android:textAlignment="center"
            app:layout_constraintBottom_toBottomOf="@id/settings_bt_paired_code_one_tv"
            app:layout_constraintStart_toEndOf="@id/settings_bt_paired_code_one_tv"
            app:layout_constraintTop_toTopOf="@id/settings_bt_paired_code_one_tv" />

        <TextView
            android:id="@+id/settings_bt_paired_code_three_tv"
            style="@style/settings_text_48_medium_17191e_style"
            android:layout_width="53dp"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/dp_28"
            android:background="@drawable/shape_bg_bluetooth_code"
            android:gravity="center"
            android:textAlignment="center"
            app:layout_constraintBottom_toBottomOf="@id/settings_bt_paired_code_one_tv"
            app:layout_constraintStart_toEndOf="@id/settings_bt_paired_code_two_tv"
            app:layout_constraintTop_toTopOf="@id/settings_bt_paired_code_one_tv" />

        <TextView
            android:id="@+id/settings_bt_paired_code_four_tv"
            style="@style/settings_text_48_medium_17191e_style"
            android:layout_width="53dp"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/dp_28"
            android:background="@drawable/shape_bg_bluetooth_code"
            android:gravity="center"
            android:textAlignment="center"
            app:layout_constraintBottom_toBottomOf="@id/settings_bt_paired_code_one_tv"
            app:layout_constraintStart_toEndOf="@id/settings_bt_paired_code_three_tv"
            app:layout_constraintTop_toTopOf="@id/settings_bt_paired_code_one_tv" />

        <TextView
            android:id="@+id/settings_bt_paired_code_five_tv"
            style="@style/settings_text_48_medium_17191e_style"
            android:layout_width="53dp"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/dp_28"
            android:background="@drawable/shape_bg_bluetooth_code"
            android:gravity="center"
            android:textAlignment="center"
            app:layout_constraintBottom_toBottomOf="@id/settings_bt_paired_code_one_tv"
            app:layout_constraintStart_toEndOf="@id/settings_bt_paired_code_four_tv"
            app:layout_constraintTop_toTopOf="@id/settings_bt_paired_code_one_tv" />

        <TextView
            android:id="@+id/settings_bt_paired_code_six_tv"
            style="@style/settings_text_48_medium_17191e_style"
            android:layout_width="53dp"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/dp_28"
            android:background="@drawable/shape_bg_bluetooth_code"
            android:gravity="center"
            android:textAlignment="center"
            app:layout_constraintBottom_toBottomOf="@id/settings_bt_paired_code_one_tv"
            app:layout_constraintStart_toEndOf="@id/settings_bt_paired_code_five_tv"
            app:layout_constraintTop_toTopOf="@id/settings_bt_paired_code_one_tv" />

        <com.bitech.vehiclesettings.view.widget.ScanScrollView
            android:id="@+id/dialog_tips_ssv"
            style="@style/settings_scroll_bar_style2"
            android:layout_width="@dimen/dp_0"
            android:layout_height="40dp"
            android:layout_marginTop="107dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/bt_paired_code_tips"
                    android:textColor="@color/black"
                    android:textSize="@dimen/sp_24"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.bitech.vehiclesettings.view.widget.ScanScrollView>

        <!--        勾选框-->
        <CheckBox
            android:id="@+id/bt_preferences_checkbox"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="85dp"
            android:layout_marginTop="@dimen/dp_274"
            android:button="@drawable/shape_bt_preferences_checkbox"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="517dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="149dp"
            android:layout_marginTop="280dp"
            android:text="@string/bt_preferences"
            android:textColor="@color/black_transparent_40"
            android:textSize="21sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/dialog_ok_btn"
            style="@style/dialog_btn_confirm_402_92_style"
            android:layout_marginStart="@dimen/dp_60"
            android:layout_marginBottom="@dimen/dp_60"
            android:background="@drawable/shape_bg_blue"
            android:text="@string/dialog_confirm_text"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/dialog_cancel_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_60" />

        <Button
            android:id="@+id/dialog_cancel_btn"
            style="@style/dialog_btn_cancel_402_92_style"
            android:layout_marginStart="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_60"
            android:layout_marginBottom="@dimen/dp_60"
            android:background="@drawable/shape_bg_white"
            android:text="@string/dialog_cancel_text"
            android:textColor="@color/color_transparent_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/dialog_ok_btn" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
