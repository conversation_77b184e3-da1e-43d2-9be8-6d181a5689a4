<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".fragment.NewEnergyFragment">

    <data>

        <variable
            name="lightViewModel"
            type="com.bitech.vehiclesettings.viewmodel.LightInViewModel" />

        <import type="android.view.View" />

        <import type="com.bitech.platformlib.constants.CarLight.SW" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_light"
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_light_car_1"
                android:layout_width="match_parent"
                android:layout_height="149.33dp"
                android:layout_marginTop="73dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_2"
                android:layout_width="951.34dp"
                android:layout_height="220.87dp"
                android:layout_marginLeft="575.33dp"
                android:layout_marginTop="125.33dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_3"
                android:layout_width="230.67dp"
                android:layout_height="139.33dp"
                android:layout_marginLeft="476.33dp"
                android:layout_marginTop="268dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_4"
                android:layout_width="230.67dp"
                android:layout_height="139.33dp"
                android:layout_marginLeft="1385.33dp"
                android:layout_marginTop="268dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear1"
                android:layout_width="19.27dp"
                android:layout_height="20dp"
                android:layout_marginLeft="962.17dp"
                android:layout_marginTop="180.33dp"
                android:scaleType="centerCrop"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear2"
                android:layout_width="15.67dp"
                android:layout_height="20dp"
                android:layout_marginLeft="1101.67dp"
                android:layout_marginTop="180.33dp"
                android:scaleType="centerCrop"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear3"
                android:layout_width="133.94dp"
                android:layout_height="50.67dp"
                android:layout_marginLeft="970dp"
                android:layout_marginTop="238.67dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear4"
                android:layout_width="290dp"
                android:layout_height="15.33dp"
                android:layout_marginLeft="656.67dp"
                android:layout_marginTop="276.67dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear5"
                android:layout_width="290dp"
                android:layout_height="15.33dp"
                android:layout_marginLeft="1140.67dp"
                android:layout_marginTop="276.67dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear6"
                android:layout_width="586dp"
                android:layout_height="297.33dp"
                android:layout_marginTop="260dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear7"
                android:layout_width="218.67dp"
                android:layout_height="150.67dp"
                android:layout_marginLeft="1488dp"
                android:layout_marginTop="260dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear8"
                android:layout_width="649.33dp"
                android:layout_height="251.33dp"
                android:layout_marginTop="316dp"
                android:visibility="invisible"/>

            <ImageView
                android:id="@+id/iv_light_car_rear9"
                android:layout_width="276dp"
                android:layout_height="141.33dp"
                android:layout_marginLeft="1452dp"
                android:layout_marginTop="322dp"
                android:visibility="invisible"/>
        </FrameLayout>

        <RelativeLayout
            android:id="@+id/rl_top"
            android:layout_width="match_parent"
            android:layout_height="416dp"
            android:layout_marginLeft="@dimen/dp_left"
            android:layout_marginTop="24.67dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_light_auto_top"
                android:layout_width="176dp"
                android:layout_height="53.33dp"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="524.67dp"
                android:background="@drawable/selector_bg_quick_12"
                android:drawableLeft="@drawable/selector_bg_light_auto_top"
                android:drawablePadding="@dimen/dp_10"
                android:fontFamily="HarmonyOS_Sans_SC"
                android:gravity="center_vertical"
                android:paddingLeft="24dp"
                android:text="@string/str_automatic_toplight"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_32px" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_light_car_end"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dp_left"
            android:layout_marginTop="336dp"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_light">
            <!--氛围灯-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:fontFamily="AppleSystemUIFont"
                android:text="@string/str_light_atmosphere"
                android:textColor="@color/black_60"
                android:textSize="@dimen/font_36px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:layout_marginTop="18dp">

                <TextView
                    android:id="@+id/tv_light_sw"
                    android:layout_width="82.67dp"
                    android:layout_height="match_parent"
                    android:background="@drawable/selector_bg_light"
                    android:drawableTop="@drawable/selector_bg_light_zks"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:paddingTop="18dp" />

                <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                    android:id="@+id/spv_light_atmosphere"
                    android:layout_width="333.33dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="32dp"
                    app:pickerFontSize="@dimen/font_36px"
                    app:pickerHeight="64dp" />
                <!--亮度调节-->
                <LinearLayout
                    android:layout_width="736dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="32dp"
                    android:background="@drawable/shape_bg_white_12">

                    <ImageView
                        android:id="@+id/iv_dashboard"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="16dp"
                        android:src="@mipmap/ic_light" />

                    <SeekBar
                        android:id="@+id/sb_light_brightness_adjust"
                        android:layout_width="560dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="8dp"
                        android:layout_toRightOf="@id/iv_dashboard"
                        android:background="@color/transparent"
                        android:importantForAccessibility="no"
                        android:max="100"
                        android:maxHeight="40dp"
                        android:minHeight="40dp"
                        android:padding="0dp"
                        android:paddingStart="0dp"
                        android:paddingEnd="0dp"
                        android:progress="2"
                        android:progressDrawable="@drawable/progress_blue_white"
                        android:splitTrack="false"
                        android:thumb="@null"
                        android:thumbOffset="0dp" />

                    <TextView
                        android:id="@+id/tv_brightness_auto"
                        android:layout_width="80dp"
                        android:layout_height="45.33dp"
                        android:layout_alignParentLeft="true"
                        android:layout_gravity="center"
                        android:layout_marginLeft="16dp"
                        android:background="@drawable/selector_bg_quick_12"
                        android:gravity="center"
                        android:selected="@{lightViewModel.autoBright == SW.ON}"
                        android:text="@string/str_light_auto"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>

            </LinearLayout>
            <!-- 推荐颜色 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_recommend_color"
                android:layout_width="1216dp"
                android:layout_height="74.67dp"
                android:layout_marginTop="26.67dp"
                android:fadingEdgeLength="180dp"
                android:requiresFadingEdge="horizontal" />

            <LinearLayout
                android:id="@+id/ll_light_custom"
                android:layout_width="match_parent"
                android:layout_height="74.67dp"
                android:layout_marginTop="26.67dp">
                <!-- 前排 后排 -->
                <FrameLayout
                    android:id="@+id/fl_light_custom"
                    android:layout_width="338.67dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center"
                    android:background="@drawable/shape_bg_white_12">

                    <View
                        android:id="@+id/view_light_area"
                        android:layout_width="169.33dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/shape_bg_open_n" />

                    <LinearLayout
                        android:id="@+id/ll_light_area"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1">

                            <TextView
                                android:id="@+id/tv_light_area_font"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:fontFamily="AppleSystemUIFont"
                                android:gravity="center"
                                android:text="@string/str_light_area_front"
                                android:textColor="@color/selector_text_color"
                                android:textSize="@dimen/font_36px" />

                            <View
                                android:id="@+id/view_light_area_font"
                                android:layout_width="8dp"
                                android:layout_height="21.33dp"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="30dp"
                                android:background="@drawable/shape_bg_light_indicator" />
                            <!--
                            <ImageView
                                android:id="@+id/iv_right_corner"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="right|bottom"
                                android:layout_marginEnd="@dimen/px_8"
                                android:layout_marginBottom="@dimen/px_8"
                                android:src="@mipmap/ic_right_corner" />
                             -->
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1">

                            <TextView
                                android:id="@+id/tv_light_area_rear"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:fontFamily="DreamHanSansCN"
                                android:gravity="center"
                                android:text="@string/str_light_area_rear"
                                android:textColor="@color/selector_text_color"
                                android:textSize="@dimen/font_36px" />

                            <View
                                android:id="@+id/view_light_area_rear"
                                android:layout_width="8dp"
                                android:layout_height="21.33dp"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="30dp"
                                android:background="@drawable/shape_bg_light_indicator" />
                        </FrameLayout>
                    </LinearLayout>
                </FrameLayout>

                <TextView
                    android:id="@+id/tv_light_sync"
                    android:layout_width="93.33dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="16dp"
                    android:background="@drawable/selector_bg_quick_12"
                    android:gravity="center"
                    android:text="@string/str_light_sync"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />
                <!--            <SeekBar-->
                <!--                android:id="@+id/sb_iv_color"-->
                <!--                android:layout_width="368dp"-->
                <!--                android:layout_height="64dp"-->
                <!--                android:layout_gravity="center"-->
                <!--                android:layout_marginLeft="32dp"-->
                <!--                android:background="@mipmap/ic_bar_color"-->
                <!--                android:importantForAccessibility="no"-->
                <!--                android:max="100"-->
                <!--                android:maxHeight="64dp"-->
                <!--                android:minWidth="368dp"-->
                <!--                android:minHeight="54.33dp"-->
                <!--                android:progress="50"-->
                <!--                android:progressDrawable="@mipmap/ic_bar_color"-->
                <!--                android:thumb="@mipmap/ic_bar_color_sliding" />-->
                <com.bitech.vehiclesettings.view.light.ColorPickerView
                    android:id="@+id/light_color_picker"
                    android:layout_width="368dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="32dp" />

                <LinearLayout
                    android:id="@+id/ll_custom_color"
                    android:layout_width="352dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="16dp"
                    android:background="@drawable/shape_bg_white_12"
                    android:gravity="center_vertical">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/dp_12">

                        <TextView
                            android:id="@+id/tv_custom_color_0"
                            android:layout_width="@dimen/dp_40"
                            android:layout_height="@dimen/dp_40"
                            android:layout_gravity="center"
                            android:background="@drawable/add_bg"
                            android:gravity="center" />

                        <View
                            android:id="@+id/view_mask_0"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/shape_bg_custom_color_s"
                            android:visibility="gone" />
                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/dp_8">

                        <TextView
                            android:id="@+id/tv_custom_color_1"
                            android:layout_width="@dimen/dp_40"
                            android:layout_height="@dimen/dp_40"
                            android:layout_gravity="center"
                            android:background="@drawable/add_bg"
                            android:gravity="center" />

                        <View
                            android:id="@+id/view_mask_1"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/shape_bg_custom_color_s"
                            android:visibility="gone" />
                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/dp_8">

                        <TextView
                            android:id="@+id/tv_custom_color_2"
                            android:layout_width="@dimen/dp_40"
                            android:layout_height="@dimen/dp_40"
                            android:layout_gravity="center"
                            android:background="@drawable/add_bg"
                            android:gravity="center" />

                        <View
                            android:id="@+id/view_mask_2"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/shape_bg_custom_color_s"
                            android:visibility="gone" />
                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/dp_8">

                        <TextView
                            android:id="@+id/tv_custom_color_3"
                            android:layout_width="@dimen/dp_40"
                            android:layout_height="@dimen/dp_40"
                            android:layout_gravity="center"
                            android:background="@drawable/add_bg"
                            android:gravity="center" />

                        <View
                            android:id="@+id/view_mask_3"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/shape_bg_custom_color_s"
                            android:visibility="gone" />
                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/dp_8">

                        <TextView
                            android:id="@+id/tv_custom_color_4"
                            android:layout_width="@dimen/dp_40"
                            android:layout_height="@dimen/dp_40"
                            android:layout_gravity="center"
                            android:background="@drawable/add_bg"
                            android:gravity="center" />

                        <View
                            android:id="@+id/view_mask_4"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/shape_bg_custom_color_s"
                            android:visibility="gone" />
                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/dp_8">

                        <TextView
                            android:id="@+id/tv_custom_color_5"
                            android:layout_width="@dimen/dp_40"
                            android:layout_height="@dimen/dp_40"
                            android:layout_gravity="center"
                            android:background="@drawable/add_bg"
                            android:gravity="center" />

                        <View
                            android:id="@+id/view_mask_5"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/shape_bg_custom_color_s"
                            android:visibility="gone" />
                    </FrameLayout>
                </LinearLayout>

            </LinearLayout>
            <!-- 车灯效果 -->
            <TextView
                android:id="@+id/tt_light_effect"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="26.67dp"
                android:text="@string/str_light_effect"
                android:textColor="@color/black_60"
                android:textSize="@dimen/font_36px" />
            <!--氛围灯-->
            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/spv_light_effect"
                android:layout_width="1216dp"
                android:layout_height="64dp"
                android:layout_marginTop="18dp"
                app:pickerFontSize="@dimen/font_36px"
                app:pickerHeight="64dp" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>