<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="checked"
            type="Boolean" />
    </data>

    <LinearLayout
        android:layout_width="@dimen/px_120"
        android:layout_height="@dimen/px_68"
        android:layout_marginEnd="@dimen/px_48"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvText"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/app_name"
            android:textColor="@{checked?@color/white:@color/text_color_1}"
            android:textSize="@dimen/pxt_36"
            app:drawable_radius="@{@dimen/px_16}"
            app:drawable_solidColor="@{checked?@color/blue:@color/bg_solid_color_1}"
            tools:text="@string/app_name" />
    </LinearLayout>
</layout>

