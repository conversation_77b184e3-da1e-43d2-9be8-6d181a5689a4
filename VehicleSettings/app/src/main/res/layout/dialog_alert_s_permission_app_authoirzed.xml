<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_recognition_camera"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="30.33dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:singleLine="false"
                android:text="@string/str_recognition_camera_1"
                android:textColor="@color/dialog_content_color"
                android:textSize="@dimen/font_40px" />
            
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="50.67dp"
                android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_open_12_month"
                android:layout_width="277.33dp"
                android:layout_height="64dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/selector_bg_open"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_open_12_month"
                android:textColor="@color/white"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:id="@+id/tv_open_this_time"
                android:layout_width="277.33dp"
                android:layout_height="64dp"
                android:layout_marginLeft="32dp"
                android:layout_marginRight="32dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/selector_bg_cancel"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_open_this_time"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="277.33dp"
                android:layout_height="64dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/selector_bg_cancel"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_cancel"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>