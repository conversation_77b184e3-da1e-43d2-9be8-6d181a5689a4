<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="charging"
            type="Boolean" />

        <variable
            name="progress"
            type="Integer" />

        <variable
            name="currentSOC"
            type="Integer" />

        <variable
            name="primaryColor"
            type="Integer" />

        <import type="android.view.View" />

        <import type="com.bitech.vehiclesettings.view.newenergy.ChargeSOCDialog" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/border_bg_dialog"
        android:paddingHorizontal="@dimen/px_120"
        android:paddingTop="@dimen/px_53"
        android:paddingBottom="@dimen/px_68">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_48"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/ne_discharging_limit" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_47"
            android:text="@{`当前电量：`+ currentSOC + `%`}"
            android:textColor="@color/text_color_2"
            android:textSize="@dimen/pxt_36"
            android:visibility="@{charging ? View.INVISIBLE : View.VISIBLE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            tools:text="@string/ne_discharging_limit" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clSeekbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_264"
            android:layout_marginTop="@dimen/px_117"
            android:padding="@dimen/px_48"
            app:drawable_radius="@{@dimen/px_24}"
            app:drawable_solidColor="@{@color/bg_solid_color_1}"
            app:layout_constraintTop_toBottomOf="@id/tvTitle">


            <SeekBar
                android:id="@+id/sbChargeValue"
                android:layout_width="match_parent"
                android:layout_height="@dimen/px_96"
                android:background="@null"
                android:max="@{charging?ChargeSOCDialog.CHARGE_SOC_END - ChargeSOCDialog.CHARGE_SOC_START:ChargeSOCDialog.DISCHARGE_SOC_END - ChargeSOCDialog.DISCHARGE_SOC_START}"
                android:paddingStart="0dp"
                android:paddingEnd="0dp"
                android:progress="@{progress}"
                android:progressDrawable="@{charging?@drawable/seekbar_progress_charge_1:@drawable/seekbar_progress_charge_2}"
                android:secondaryProgress="@{charging?0:currentSOC-ChargeSOCDialog.DISCHARGE_SOC_START}"
                android:splitTrack="false"
                android:thumb="@null"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvValueStart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_24"
                android:text="@{charging?`80%`:`30%`}"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_36"
                app:layout_constraintStart_toStartOf="@id/sbChargeValue"
                app:layout_constraintTop_toBottomOf="@id/sbChargeValue" />

            <TextView
                android:id="@+id/tvValueEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="100%"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_36"
                app:layout_constraintEnd_toEndOf="@id/sbChargeValue"
                app:layout_constraintTop_toTopOf="@id/tvValueStart" />

            <ImageView
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48"
                android:layout_marginStart="@dimen/px_32"
                android:src="@drawable/ic_charge_soc"
                app:layout_constraintBottom_toBottomOf="@id/sbChargeValue"
                app:layout_constraintStart_toStartOf="@id/sbChargeValue"
                app:layout_constraintTop_toTopOf="@id/sbChargeValue" />

            <TextView
                android:id="@+id/tvValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/px_32"
                android:text="@{charging?progress+ ChargeSOCDialog.CHARGE_SOC_START + `%`:progress+ChargeSOCDialog.DISCHARGE_SOC_START + `%`}"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_36"
                app:layout_constraintBottom_toBottomOf="@id/sbChargeValue"
                app:layout_constraintEnd_toEndOf="@id/sbChargeValue"
                app:layout_constraintTop_toTopOf="@id/sbChargeValue" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_24"
            android:textColor="@color/text_color_error"
            android:textSize="@dimen/pxt_36"
            app:currentSoc="@{currentSOC}"
            app:isCharging="@{charging}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clSeekbar"
            app:progress="@{progress}" />

        <Button
            android:id="@+id/btnConfirm"
            android:layout_width="@dimen/px_444"
            android:layout_height="@dimen/px_96"
            android:backgroundTint="@{primaryColor}"
            android:text="@string/dialog_ok_text"
            android:textColor="@color/white"
            android:textSize="@dimen/pxt_36"
            app:currentSoc="@{currentSOC}"
            app:drawable_radius="@{@dimen/px_16}"
            app:isCharging="@{charging}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:progress="@{progress}" />

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="@dimen/px_444"
            android:layout_height="@dimen/px_96"
            android:text="@string/dialog_cancel_text"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_36"
            app:drawable_radius="@{@dimen/px_16}"
            app:drawable_solidColor="@{@color/bg_solid_color_1}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>