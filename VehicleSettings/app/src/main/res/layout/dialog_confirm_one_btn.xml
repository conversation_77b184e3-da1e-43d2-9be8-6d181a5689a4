<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/border_bg_dialog">
    
    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/dialog_background_984_520_style"
        android:layout_width="784dp"
        android:layout_height="wrap_content"
        android:maxHeight="1154dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        
        <com.bitech.vehiclesettings.view.widget.ScanScrollView
            android:id="@+id/dialog_title_ssv"
            style="@style/settings_scroll_bar_style2"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_54"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_77"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_17">

                <TextView
                    android:id="@+id/dialog_title_tv"
                    android:includeFontPadding="false"
                    android:paddingBottom="12dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textSize="24sp"
                    android:textColor="@color/black"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
        
        </com.bitech.vehiclesettings.view.widget.ScanScrollView>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/dialog_tips_cl"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            app:layout_constraintHeight_max="828dp"
            app:layout_constraintHeight_min="194dp"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="@dimen/dp_51"
            android:layout_marginEnd="@dimen/dp_77"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dialog_title_ssv"
            >
            <com.bitech.vehiclesettings.view.widget.ScanScrollView
                android:id="@+id/dialog_tips_ssv"
                style="@style/settings_scroll_bar_style2"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">


                <TextView
                    android:id="@+id/dialog_tips_tv"
                    android:textSize="24sp"
                    android:textColor="@color/black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_17"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </com.bitech.vehiclesettings.view.widget.ScanScrollView>

            <TextView
                android:id="@+id/dialog_tips_tv2"
                style="@style/settings_text_34_regular_747578_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--<ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/img_popup_text_mask_bg_796"
            app:layout_constraintBottom_toBottomOf="@id/dialog_tips_ssv"
            app:layout_constraintEnd_toEndOf="@id/dialog_tips_ssv"
            app:layout_constraintStart_toStartOf="@id/dialog_tips_ssv"
            />-->

        <Button
            android:id="@+id/dialog_confirm_know_btn"
            style="@style/dialog_btn_confirm_402_92_style"
            android:layout_marginTop="@dimen/dp_47"
            android:layout_marginBottom="@dimen/dp_60"
            android:text="@string/dialog_i_know_text"
            android:background="@drawable/shape_bg_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dialog_tips_cl" />
    
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
