<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_tips_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_rear_mirror_ad_title"
                android:textColor="@color/black"
                android:textSize="@dimen/font_48px" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="33.33dp">

                <TextView
                    android:id="@+id/tv_tips_text"
                    android:layout_width="581.33dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="0dp"

                    android:background="@drawable/selector_bg_blue_type"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/color_transparent_40"
                    android:textSize="@dimen/font_36px" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>