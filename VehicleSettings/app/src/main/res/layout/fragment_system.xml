<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/scrollView"
    style="@style/scroll_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_left"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="66.67dp"
            android:gravity="center_vertical">
            <TextView
                android:id="@+id/vClick2EngineMode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/str_system_about"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="261.34dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">
            <!-- 左侧 TextView 组合 -->
            <LinearLayout
                android:id="@+id/ll_version_msg"
                android:layout_width="592dp"
                android:layout_height="261.34dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:background="@mipmap/ic_system_about"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="25.33dp"
                    android:gravity="left"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_about_01"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_60px" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_about_02"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_60px" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="57.33dp">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_software_version"
                        android:textColor="@color/color_transparent_60"
                        android:textSize="@dimen/font_28px" />

                    <TextView
                        android:id="@+id/tv_system_software_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:text="@string/str_system_software_version_content"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="5.33dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_hardware_version"
                        android:textColor="@color/color_transparent_60"
                        android:textSize="@dimen/font_28px" />

                    <TextView
                        android:id="@+id/tv_system_hardware_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:text="@string/str_system_hardware_version_content"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="32dp"
                android:orientation="vertical">
                <!-- 右侧第一行 -->
                <RelativeLayout
                    android:id="@+id/rl_device_msg"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_system_device_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:id="@+id/tvDeviceMsg"
                            android:layout_width="300dp"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:text="@string/str_system_device_info_content"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="84dp"
                        android:layout_marginRight="6dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>

                <!-- 右侧第二行 -->
                <RelativeLayout
                    android:id="@+id/rl_storage_msg"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:layout_marginTop="21.33dp"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_system_storage_space"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/str_system_storage_space_content"
                                android:textColor="@color/color_transparent_40"
                                android:textSize="@dimen/font_28px" />
                            <TextView
                                android:id="@+id/tv_storage_msg"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="10dp"
                                android:text="@string/str_system_storage_space_content"
                                android:textColor="@color/color_transparent_40"
                                android:textSize="@dimen/font_28px" />
                        </LinearLayout>


                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">
            <!--分析与改进-->
            <LinearLayout
                android:visibility="gone"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_analysis"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <RelativeLayout
                    android:id="@+id/rl_analysis"
                    android:layout_width="458.67dp"
                    android:layout_height="133.33dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_system_analysis_and_improve"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:id="@+id/iv_analysis_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="33.33dp"
                        android:src="@mipmap/ic_small_tip" />

                </RelativeLayout>
            </LinearLayout>
            <!--恢复出厂设置-->
            <RelativeLayout
                android:layout_width="592dp"
                android:layout_height="120dp">

                <!-- 原有的 RelativeLayout -->
                <RelativeLayout
                    android:id="@+id/rl_restore_setting"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_system_factory_data_reset"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="84dp"
                        android:layout_marginRight="6dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>

                <View
                    android:id="@+id/vw_restore_setting"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#00000000"
                    android:visibility="gone"/>

            </RelativeLayout>
        </LinearLayout>
        <!--踏板控制-->
        <TextView
            android:id="@+id/tv_test"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="39.33dp"
            android:text="@string/str_system_privacy"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_permission"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_authority_manage"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_privacy_policy"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_permission_privacy_statement_title"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_basic_privacy"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_basic_privacy_agreement"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_service_privacy"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_service_privacy_agreement"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_service_privacy_is_agree"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_service_privacy_agreement_agree"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_real_time_data"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_real_time_data"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_diagnostic_data"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_diagnostic_data"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_privacy_statement"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_privacy_statement"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>
        <!--通用-->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="39.33dp"
            android:text="@string/str_system_general"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_gesture_navigation"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_gesture_navigation"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_language_setting"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_language_setting"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_datetime_setting"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_date_time_setting"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_instrument_fuel_unit"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_meter_consumption_unit"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_tire_pressure_unit"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_tire_pressure_unit"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_power_consumption_unit"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_power_unit"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="23.33dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:visibility="gone"
                android:id="@+id/rl_unit_setting"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_unit_setting"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:visibility="gone"
                android:id="@+id/rl_temperature_setting"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_temperature_unit"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_user_feedback"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_system_user_feedback"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <!--        <LinearLayout-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="133.33dp"-->
        <!--            android:layout_marginTop="23.33dp"-->
        <!--            android:orientation="horizontal">-->
        <!--            <View-->
        <!--                android:layout_width="0dp"-->
        <!--                android:layout_height="match_parent"-->
        <!--                android:layout_weight="1" />-->
        <!--            <View-->
        <!--                android:id="@+id/vClick2EngineMode"-->
        <!--                android:layout_width="100dp"-->
        <!--                android:layout_height="match_parent" />-->
        <!--        </LinearLayout>-->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="29.33dp"
            android:orientation="horizontal" />
    </LinearLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>