<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="28.89dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_light_flowing_panel"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <ImageView
                android:layout_width="842.67dp"
                android:layout_height="306.67dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="45.33dp"
                android:background="@mipmap/ic_l_flow_panel"
                android:gravity="center" />

            <LinearLayout
                android:layout_width="842.67dp"
                android:layout_height="73.33dp"
                android:layout_marginLeft="85.33dp"
                android:layout_marginTop="64dp"
                android:background="@drawable/shape_bg_white">
                <!-- 关闭 -->
                <TextView
                    android:id="@+id/tv_light_flowing_1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5.33dp"
                    android:layout_weight="1"
                    android:background="@drawable/selector_bg_blue_type"
                    android:fontFamily="AppleSystemUIFont"
                    android:gravity="center"
                    android:text="@string/str_light_flowing_item1"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_light_flowing_2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5.33dp"
                    android:layout_weight="1"
                    android:background="@drawable/selector_bg_blue_type"
                    android:fontFamily="AppleSystemUIFont"
                    android:gravity="center"
                    android:text="@string/str_light_flowing_item2"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_light_flowing_3"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5.33dp"
                    android:layout_weight="1"
                    android:background="@drawable/selector_bg_blue_type"
                    android:fontFamily="AppleSystemUIFont"
                    android:gravity="center"
                    android:text="@string/str_light_flowing_item3"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_light_flowing_4"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5.33dp"
                    android:layout_weight="1"
                    android:background="@drawable/selector_bg_blue_type"
                    android:fontFamily="AppleSystemUIFont"
                    android:gravity="center"
                    android:text="@string/str_light_flowing_item4"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>