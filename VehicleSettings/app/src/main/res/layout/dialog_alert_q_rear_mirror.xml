<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1328dp"
    android:layout_height="614.67dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">


    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="33.33dp"
        android:fontFamily="DreamHanSansCN"
        android:text="@string/str_rearmirror_title"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="645.33dp"
            android:layout_height="match_parent"
            android:layout_marginTop="56.67dp"
            android:layout_marginLeft="80dp"
            android:layout_marginRight="33.33dp"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_condition_rear_mirror"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_switch_rear_mirror_title"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="21.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_condition_heating_rear_mirror"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_switch_heating_rear_mirror_title"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:layout_marginLeft="32dp"
                android:layout_marginBottom="18dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_rear_mirror_ad_title"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            
            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/spv_rear_mirror_adjust"
                android:layout_width="645.33dp"
                android:layout_height="64dp"
                app:pickerFontSize="@dimen/font_36px"
                app:pickerHeight="64dp"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center">

                <ImageView
                    android:layout_width="492dp"
                    android:layout_height="350.33dp"
                    android:layout_gravity="center"
                    android:src="@mipmap/ic_rear_mirror_adjust" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="105dp"
                    android:layout_marginTop="150dp"
                    android:text="@string/str_rearmirror_outside"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="195dp"
                    android:layout_marginTop="150dp"
                    android:text="@string/str_rearmirror_inside"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="150dp"
                    android:layout_marginTop="95dp"
                    android:text="@string/str_rearmirror_up"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="150dp"
                    android:layout_marginTop="185dp"
                    android:text="@string/str_rearmirror_down"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="280dp"
                    android:layout_marginTop="150dp"
                    android:text="@string/str_rearmirror_outside"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="370dp"
                    android:layout_marginTop="150dp"
                    android:text="@string/str_rearmirror_inside"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="325dp"
                    android:layout_marginTop="95dp"
                    android:text="@string/str_rearmirror_up"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="325dp"
                    android:layout_marginTop="185dp"
                    android:text="@string/str_rearmirror_down"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_simply_text"
                android:layout_width="wrap_content"
                android:layout_height="33.33dp"
                android:layout_gravity="center"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_rearmirror_adjust_prmpt"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_36px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_simply_text"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="36.66dp"
                android:layout_marginBottom="45.33dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_simply_confirm"
                    android:layout_width="221.33dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/selector_bg_open"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_save"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_simply_cancel"
                    android:layout_width="221.33dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="26.66dp"
                    android:background="@drawable/selector_bg_cancel"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_recover"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>