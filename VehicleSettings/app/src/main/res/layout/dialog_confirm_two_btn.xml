<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:layout_width="784dp"
    android:layout_height="335dp">
    
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="784dp"
        android:layout_height="335dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        
        <com.bitech.vehiclesettings.view.widget.ScanScrollView
            android:id="@+id/dialog_tips_ssv"
            style="@style/settings_scroll_bar_style2"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="70dp"
            android:layout_marginEnd="@dimen/dp_77"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="31dp">
                
                <TextView
                    android:id="@+id/dialog_tips_tv"
                    android:includeFontPadding="false"
                    android:paddingBottom="12dp"
                    android:lineHeight="24dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textSize="24sp"
                    android:textColor="@color/black"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
        
        </com.bitech.vehiclesettings.view.widget.ScanScrollView>
        
        <Button
            android:id="@+id/dialog_confirm_btn"
            android:layout_width="267dp"
            android:layout_height="67dp"
            android:background="@drawable/shape_bg_blue"
            android:layout_marginStart="@dimen/dp_60"
            android:layout_marginTop="80dp"
            android:layout_marginEnd="@dimen/dp_60"
            android:layout_marginBottom="@dimen/dp_60"
            android:text="@string/dialog_confirm_text"
            android:ellipsize="end"
            android:maxLines="1"
            android:textSize="@dimen/sp_24"
            android:textColor="@color/white"
            app:layout_constraintEnd_toStartOf="@id/dialog_cancel_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dialog_tips_ssv" />
        
        <Button
            android:id="@+id/dialog_cancel_btn"
            android:layout_width="267dp"
            android:layout_height="67dp"
            android:background="@drawable/shape_bg_white"
            android:layout_marginEnd="@dimen/dp_60"
            android:text="@string/dialog_cancel_text"
            android:textSize="@dimen/sp_24"
            android:textColor="@color/black"
            app:layout_constraintBottom_toBottomOf="@id/dialog_confirm_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/dialog_confirm_btn"
            app:layout_constraintTop_toTopOf="@id/dialog_confirm_btn" />
    
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
