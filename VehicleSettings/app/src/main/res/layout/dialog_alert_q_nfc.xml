<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_nfc_title"
                android:textColor="@color/black"
                android:textSize="@dimen/font_48px" />

            <RelativeLayout
                android:id="@+id/rl_nfc_item1"
                android:layout_width="581.33dp"
                android:layout_height="106.67dp"
                android:layout_marginLeft="85.33dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/selector_bg_quick">

                <ImageView
                    android:id="@+id/sw_condition_nfc"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:src="@drawable/selector_q_nfc" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="128dp"
                    android:text="@string/str_nfc_item1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:id="@+id/sw_condition_edit"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="32dp"
                    android:src="@drawable/selector_q_edit" />
            </RelativeLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>