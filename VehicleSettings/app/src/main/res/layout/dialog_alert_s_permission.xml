<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="101.33dp">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="30.33dp"
                    android:fontFamily="DreamHanSansCN"
                    android:text="@string/str_system_permission"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_48px" />

                <ImageView
                    android:id="@+id/iv_access_record"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="37.33dp"
                    android:layout_marginRight="85.33dp"
                    android:src="@mipmap/ic_time" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:gravity="center"
                android:orientation="horizontal">

                <RelativeLayout
                    android:id="@+id/rl_position"
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_system_permission_3"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="90.67dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_camera"
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_system_permission_1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="90.67dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:layout_marginTop="21.33dp"
                android:gravity="center"
                android:orientation="horizontal">

                <RelativeLayout
                    android:id="@+id/rl_microphone"
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_system_permission_2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="90.67dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_special"
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_system_permission_special"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="90.67dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>