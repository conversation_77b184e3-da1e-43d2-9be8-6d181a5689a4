<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="500dp"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="624dp"
                android:layout_height="101.33dp"
                android:layout_gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="DreamHanSansCN"
                    android:text="@string/str_carsetting_high"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_48px" />

                <ImageView
                    android:id="@+id/iv_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="80dp"
                    android:src="@mipmap/ic_small_tip" />

            </RelativeLayout>

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="624dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:singleLine="false"
                android:text="@string/str_carsetting_high_content"
                android:textColor="@color/color_detail_60"
                android:textSize="@dimen/font_36px" />

<!--            <RelativeLayout-->
<!--                android:layout_width="624dp"-->
<!--                android:layout_height="158.67dp"-->
<!--                android:layout_marginTop="18dp"-->
<!--                android:layout_gravity="center"-->
<!--                android:background="@drawable/shape_bg_white">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    android:layout_marginLeft="32dp"-->
<!--                    android:layout_marginTop="32dp"-->
<!--                    android:text="@string/str_carsetting_high"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="@dimen/font_36px" />-->

<!--                <TextView-->
<!--                    android:id="@+id/iv_hud_roate_top"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_marginTop="32dp"-->
<!--                    android:layout_marginRight="32dp"-->
<!--                    android:text="90%"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="@dimen/font_36px" />-->

<!--                <ImageView-->
<!--                    android:id="@+id/iv_hud_high"-->
<!--                    android:layout_width="32dp"-->
<!--                    android:layout_height="32dp"-->
<!--                    android:layout_marginLeft="32dp"-->
<!--                    android:layout_marginTop="94.67dp"-->
<!--                    android:src="@mipmap/ic_rear_high" />-->

<!--                <SeekBar-->
<!--                    android:id="@+id/sb_iv_hud_roate"-->
<!--                    android:layout_width="432dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    android:layout_marginLeft="90.67dp"-->
<!--                    android:layout_marginTop="94.67dp"-->
<!--                    android:importantForAccessibility="no"-->
<!--                    android:background="@color/transparent"-->
<!--                    android:max="100"-->
<!--                    android:maxHeight="13.33dp"-->
<!--                    android:minWidth="522.66dp"-->
<!--                    android:minHeight="13.33dp"-->
<!--                    android:progress="50"-->
<!--                    android:progressDrawable="@drawable/seekbar_progress"-->
<!--                    android:splitTrack="false"-->
<!--                    android:thumb="@mipmap/ic_sound_slider" />-->
<!--            </RelativeLayout>-->

            <RelativeLayout
                android:layout_width="624dp"
                android:layout_height="158.67dp"
                android:layout_marginTop="18dp"
                android:layout_gravity="center"
                android:background="@drawable/shape_bg_white">

                <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                    android:id="@+id/isb_iv_hud_roate"
                    android:layout_width="560dp"
                    android:layout_height="64dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="32dp"
                    app:max="50"
                    app:progress="45"
                    app:seekBarWidth="560dp"
                    app:seekBarHeight='64dp'>

                    <LinearLayout
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:gravity="center">
                        <ImageView
                            android:id="@+id/iv_hud_high"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@mipmap/ic_q_hud_high" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/iv_hud_roate_top"
                        android:layout_width="94.67dp"
                        android:layout_height="64dp"
                        android:layout_gravity="right"
                        android:gravity="center"
                        android:text="90%"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </com.bitech.vehiclesettings.view.common.ImageSeekBarView>
                
                <RelativeLayout
                    android:layout_width="560dp"
                    android:layout_height="30.67dp"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentLeft="true"
                        android:gravity="center"
                        android:text="50%"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:gravity="center"
                        android:text="100%"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </RelativeLayout>

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="624dp"
                android:layout_height="165.33dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_camera_confirm"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:layout_alignParentLeft="true"
                    android:background="@drawable/selector_bg_open"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_confirm2"
                    android:textColor="#ffffff"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_camera_cancel"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:layout_alignParentRight="true"
                    android:background="@drawable/selector_bg_cancel"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_cancel"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </RelativeLayout>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>