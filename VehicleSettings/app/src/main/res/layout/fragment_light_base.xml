<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_light"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_light_tab"
        android:layout_width="@dimen/dp_width"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_left"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_light_in"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="32dp"
            android:layout_marginTop="24.67dp"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_light_in"
            android:textColor="@color/black"
            android:textSize="@dimen/font_42px" />

        <ImageView
            android:id="@+id/iv_light_in"
            android:layout_width="0dp"
            android:layout_height="4dp"
            android:layout_alignParentLeft="true"
            android:layout_alignLeft="@id/tv_light_in"
            android:layout_alignRight="@id/tv_light_in"
            android:layout_marginLeft="32dp"
            android:layout_marginTop="68.67dp"
            android:background="@drawable/shape_bg_blue" />

        <TextView
            android:id="@+id/tv_light_out"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="210.67dp"
            android:layout_marginTop="24.67dp"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_light_out"
            android:textColor="@color/black"
            android:textSize="@dimen/font_42px" />

        <ImageView
            android:id="@+id/iv_light_out"
            android:layout_width="112dp"
            android:layout_height="4dp"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="210.67dp"
            android:layout_marginTop="68.67dp"
            android:layout_alignLeft="@id/tv_light_out"
            android:layout_alignRight="@id/tv_light_out"
            android:background="@drawable/shape_bg_blue" />

    </RelativeLayout>

    <com.bitech.vehiclesettings.view.common.HackyViewPager
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="0dp"
        android:overScrollMode="never"
        android:paddingBottom="80dp"
        app:layout_constraintTop_toBottomOf="@id/rl_light_tab" />

</androidx.constraintlayout.widget.ConstraintLayout>

