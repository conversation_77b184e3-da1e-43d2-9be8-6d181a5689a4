<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    style="@style/scroll_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_light"
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_left"
        android:orientation="vertical"
        android:paddingBottom="80.33dp">

        <LinearLayout
            android:id="@+id/ll_light_car_end"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="341.33dp"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:text="@string/str_light_control"
                android:textColor="@color/black_60"
                android:textSize="@dimen/font_36px" />
            <!--    车控 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:layout_marginTop="23.33dp"
                android:orientation="horizontal">

                <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                    android:id="@+id/spv_light_control"
                    android:layout_width="1016dp"
                    android:layout_height="match_parent"
                    app:pickerFontSize="@dimen/font_36px"
                    app:pickerHeight="64dp" />

                <TextView
                    android:id="@+id/tv_light_rear_fog"
                    android:layout_width="168dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="32dp"
                    android:background="@drawable/selector_bg_quick_12"
                    android:drawableLeft="@drawable/selector_light_rear_forg"
                    android:drawablePadding="@dimen/dp_10"
                    android:fontFamily="HarmonyOS_Sans_SC"
                    android:gravity="center_vertical"
                    android:paddingLeft="24dp"
                    android:text="@string/str_rear_forg"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_32px" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_lamp_control_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/str_lamp_auto_open_tip"
                android:textColor="@color/black_60"
                android:textSize="@dimen/font_32px" />
            <!--大灯高度调节-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="34dp"
                android:text="@string/str_lamp_hight_settings"
                android:textColor="@color/black_60"
                android:textSize="@dimen/font_36px" />

            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/spv_light_high"
                android:layout_width="1216dp"
                android:layout_height="64dp"
                android:layout_marginTop="18dp"
                app:pickerFontSize="@dimen/font_36px"
                app:pickerHeight="64dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center_vertical"
                    android:background="@mipmap/ic_headlightdown" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="110.67dp"
                    android:text="@string/str_light_high_3"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="292dp"
                    android:text="@string/str_light_high_2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="292dp"
                    android:text="@string/str_light_high_1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="292dp"
                    android:text="@string/str_light_high_0"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="110.67dp"
                    android:background="@mipmap/ic_headlightup" />

            </LinearLayout>
            <!--大灯延时关闭-->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:text="@string/str_lamp_delay_settings"
                android:textColor="@color/black_60"
                android:textSize="@dimen/font_36px" />

            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/spv_lamp_delay"
                android:layout_width="1216dp"
                android:layout_height="64dp"
                android:layout_marginTop="18dp"
                app:pickerFontSize="@dimen/font_36px"
                app:pickerHeight="64dp" />

            <!--车灯设置-->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="39.33dp"
                android:text="@string/str_lamp_settings"
                android:textColor="@color/black_60"
                android:textSize="@dimen/font_36px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:layout_marginTop="18dp"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_approaching_welcome"
                        android:layout_width="53.33dp"
                        android:layout_height="34.67dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="53.33dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="117.33dp"
                        android:text="@string/str_welcoming_guests"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_high_low_switch"
                        android:layout_width="53.33dp"
                        android:layout_height="34.67dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="53.33dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="117.33dp"
                        android:text="@string/str_intelligent_distance_switching"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />


                </RelativeLayout>

            </LinearLayout>
            <!--str_headlight_delay-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:layout_marginTop="21.33dp"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_intelligent_welcome"
                        android:layout_width="53.33dp"
                        android:layout_height="34.67dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="53.33dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="117.33dp"
                        android:text="@string/str_intelligent_welcome_light"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </RelativeLayout>


            </LinearLayout>


        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>
