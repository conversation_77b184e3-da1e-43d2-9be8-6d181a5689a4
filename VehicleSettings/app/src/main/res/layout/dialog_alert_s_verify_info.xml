<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="516dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="624dp"
        android:layout_height="101.33dp"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:text="@string/str_system_factory_data_reset_title"
        android:textColor="@color/black"
        android:textSize="@dimen/font_42px" />

    <!--    手机号-->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:text="@string/str_system_factory_data_reset_title_2"
        android:textColor="@color/black_transparent_60"
        android:textSize="@dimen/font_36px" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginTop="21.33dp"
        android:layout_marginStart="80dp"
        android:layout_marginEnd="80dp"
        android:background="@drawable/shape_bg_black_8">

        <!-- 占满整个布局的 EditText -->
        <EditText
            android:id="@+id/dialog_phone_input_et"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:paddingLeft="32dp"
            android:paddingRight="60dp"
            android:hint="@string/str_system_factory_data_reset_phone"
            android:textColorHint="@color/black_transparent_60"
            android:textSize="@dimen/font_36px"
            android:textColor="@color/black"
            android:inputType="number"
            android:maxLength="11"/>

        <!-- 覆盖在 EditText 上的清除按钮 -->
        <ImageView
            android:id="@+id/iv_phone_clear"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="32dp"
            android:src="@mipmap/ic_s_reset_cancel"
            android:visibility="gone"
            android:clickable="true"
            android:focusable="true"/>

    </FrameLayout>

    <!--验证码    -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginTop="21.33dp"
        android:layout_marginStart="80dp"
        android:layout_marginEnd="80dp"
        android:gravity="left"
        android:background="@drawable/shape_bg_black_8">
        <FrameLayout
            android:layout_width="401.33dp"
            android:layout_height="80dp">
            <!-- 占满整个布局的 EditText -->
            <EditText
                android:id="@+id/dialog_verify_code_input_et"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingLeft="32dp"
                android:paddingRight="60dp"
                android:hint="@string/str_system_factory_data_reset_verify_code"
                android:textColorHint="@color/black_transparent_60"
                android:textSize="@dimen/font_36px"
                android:textColor="@color/black"
                android:inputType="number"
                android:maxLength="6"/>
        </FrameLayout>
        <ImageView
            android:id="@+id/iv_verify_code_line"
            android:layout_width="0.67dp"
            android:layout_height="32dp"
            android:layout_marginTop="24dp"
            android:visibility="gone"
            android:background="@mipmap/ic_sys_reset_line"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:gravity="center">
            <TextView
                android:id="@+id/tv_get_verify_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:gravity="center"
                android:text="@string/str_system_factory_data_reset_get_verify_code"
                android:textColor="@color/blue"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="66.67dp"
        android:layout_below="@id/tv_simply_text"
        android:layout_marginStart="80dp"
        android:layout_marginTop="53.33dp"
        android:layout_marginEnd="80dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:gravity="center"
            android:background="@drawable/selector_bg_red">
            <ImageView
                android:id="@+id/iv_reset_confirm"
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_30"
                android:visibility="gone"
                android:background="@mipmap/ic_loading_d"/>
            <TextView
                android:layout_marginStart="@dimen/dp_16"
                android:id="@+id/tv_sys_reset_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/str_recover_factory"
                android:textColor="@color/sys_reset_white"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_sys_reset_cancel"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="32dp"
            android:background="@drawable/selector_bg_cancel"
            android:fontFamily="DreamHanSansCN"
            android:gravity="center"
            android:text="@string/str_cancel"
            android:textColor="@color/selector_text_color"
            android:textSize="@dimen/font_36px" />

    </LinearLayout>
</LinearLayout>