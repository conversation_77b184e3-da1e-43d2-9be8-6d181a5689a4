<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".fragment.NewEnergyFragment">

    <data>

        <variable
            name="newEnergyViewModel"
            type="com.bitech.vehiclesettings.viewmodel.NewEnergyViewModel" />

        <import type="android.view.View" />

        <import type="com.bitech.vehiclesettings.carapi.constants.CarNewEnergy.BatteryStatus" />

        <import type="com.bitech.vehiclesettings.carapi.constants.CarNewEnergy.GearStatus" />
    </data>

    <com.bitech.vehiclesettings.view.common.BounceScrollView
        android:id="@+id/svNeGroup"
        style="@style/scroll_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dp_left">

            <!--    车模-->
            <FrameLayout
                android:id="@+id/clNeCarModel"
                android:layout_width="@dimen/px_1260"
                android:layout_height="@dimen/px_720"
                android:layout_marginStart="@dimen/px_240"
                android:background="@mipmap/ic_car_model_transparent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/ivTireRight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_280"
                    android:layout_marginTop="@dimen/px_187"
                    android:src="@mipmap/ic_right_tire" />

                <!--                动画-右车轮-->
                <ImageView
                    android:id="@+id/ivTireRightAnim"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_280"
                    android:layout_marginTop="@dimen/px_187"
                    app:carWheelForwardAnimVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <!--                两驱/四驱底盘投影-->
                <ImageView
                    android:id="@+id/ivCarWdChassisShadow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_142"
                    android:layout_marginTop="@dimen/px_219"
                    android:src="@mipmap/ic_two_wd_chassis_shadow" />

                <!--                两驱/四驱底盘-->
                <ImageView
                    android:id="@+id/ivCarWdChassis"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_176"
                    android:layout_marginTop="@dimen/px_272"
                    android:src="@mipmap/ic_four_wd_chassis" />

                <ImageView
                    android:id="@+id/ivTireLeft"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_224"
                    android:layout_marginTop="@dimen/px_385"
                    android:src="@mipmap/ic_left_tire" />

                <!--                动画-左车轮-->
                <ImageView
                    android:id="@+id/ivTireLeftAnim"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_224"
                    android:layout_marginTop="@dimen/px_385"
                    app:carWheelForwardAnimVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <!--                电池电量图片-->
                <ImageView
                    android:id="@+id/ivBatteryPercent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_404"
                    android:layout_marginTop="@dimen/px_247"
                    app:batteryPercent="@{newEnergyViewModel.currentSOCLiveData}" />

                <!--                动画-充电电池扫光-->
                <ImageView
                    android:id="@+id/ivBatteryChargeScan"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_455"
                    android:layout_marginTop="@dimen/px_307"
                    android:visibility="@{newEnergyViewModel.chargingStatusLiveData==1?View.VISIBLE:View.GONE}" />

                <!--                动画-放电电池扫光-->
                <ImageView
                    android:id="@+id/ivBatteryDischargeScan"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_455"
                    android:layout_marginTop="@dimen/px_307"
                    android:visibility="@{newEnergyViewModel.dischargeStatusLiveData?View.VISIBLE:View.GONE}" />

                <!--                动画-管道左前-->
                <ImageView
                    android:id="@+id/ivPipeLeftFrontAnim"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_238"
                    android:layout_marginTop="@dimen/px_350"
                    app:pipeLeftFrontAnimVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <!--                发动机图片-->
                <ImageView
                    android:id="@+id/ivEngineImage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_182"
                    android:layout_marginTop="@dimen/px_303"
                    android:src="@mipmap/ic_eng_red"
                    app:engineImageVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <!--                动画-前发电机-->
                <ImageView
                    android:id="@+id/ivFrontMotorAnim"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_272"
                    android:layout_marginTop="@dimen/px_271"
                    app:frontMotorAnimVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <!--                动画-后发电机-->
                <ImageView
                    android:id="@+id/ivBackMotorAnim"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_833"
                    android:layout_marginTop="@dimen/px_272"
                    app:backMotorVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <!--                动画-发动机管道上下-红色-->
                <ImageView
                    android:id="@+id/ivEngPipeUpDownRedAnim"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_223"
                    android:layout_marginTop="@dimen/px_273"
                    app:engPipeUpDownRedAnimVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <!--                动画-管道中前-->
                <ImageView
                    android:id="@+id/ivPipeMiddleFrontAnim"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_363"
                    android:layout_marginTop="@dimen/px_350"
                    app:frontMotorAnimVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <!--                动画-管道右后-->
                <ImageView
                    android:id="@+id/ivPipeRightBackAnim"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_770"
                    android:layout_marginTop="@dimen/px_350"
                    app:backMotorVisible="@{newEnergyViewModel.energyFlowStatusLiveData}" />

                <TextView
                    android:id="@+id/tvBatteryLevel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginTop="@dimen/px_180"
                    android:layout_marginEnd="@dimen/px_585"
                    android:text="@{String.valueOf(newEnergyViewModel.currentSOCLiveData)}"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_100"
                    tools:text="35" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginTop="@dimen/px_242"
                    android:layout_marginEnd="@dimen/px_550"
                    android:text="%"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_36" />

            </FrameLayout>

            <!--    充放电类型、时间、解锁/结束充电-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clNeChargeInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvNeChargeType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_166"
                    android:drawablePadding="@dimen/px_10"
                    android:gravity="center_vertical"
                    android:text="@{newEnergyViewModel.textChargeType}"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_44"
                    app:fastCharging="@{newEnergyViewModel.batteryStatusLiveData==BatteryStatus.DC_CHARGE_MODE?true:false}"
                    app:iconCharge="@{newEnergyViewModel.textChargeType}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvNeRemainingTimeDesc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_48"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:gravity="center_vertical"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:text="@string/ne_charging_remainder"
                    android:textColor="@color/text_color_2"
                    android:textSize="@dimen/pxt_32"
                    android:visibility="@{newEnergyViewModel.chargingStatusLiveData==1?View.VISIBLE:View.INVISIBLE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvNeChargeType" />

                <TextView
                    android:id="@+id/tvNeRemainingTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/px_22"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:gravity="center_vertical"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:text="@{newEnergyViewModel.textRemainingTime}"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_48"
                    android:visibility="@{newEnergyViewModel.chargingStatusLiveData==1?View.VISIBLE:View.INVISIBLE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvNeRemainingTimeDesc" />

                <Button
                    android:id="@+id/btnNeFunction"
                    android:layout_width="@dimen/px_290"
                    android:layout_height="@dimen/px_100"
                    android:layout_marginTop="@dimen/px_208"
                    android:outlineProvider="none"
                    android:text="@{newEnergyViewModel.textFunctionButton}"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_36"
                    android:visibility="@{newEnergyViewModel.functionButtonVisible?View.VISIBLE:View.INVISIBLE}"
                    app:drawable_radius="@{@dimen/px_16}"
                    app:drawable_solidColor="@{@color/bg_solid_color_1}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvNeChargeType" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--    功率、电流、电压-->
            <LinearLayout
                android:id="@+id/clNeChargePowerInfo"
                android:layout_width="0dp"
                android:layout_height="@dimen/px_42"
                android:layout_marginTop="@dimen/px_606"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="@id/clNeCarModel"
                app:layout_constraintStart_toStartOf="@id/clNeCarModel"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvNeDischargePowerLimit"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingStart="@dimen/dp_0"
                    android:paddingEnd="@dimen/px_48"
                    android:text="@{newEnergyViewModel.tvDischargePowerLimit}"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_30"
                    android:visibility="@{newEnergyViewModel.dischargeStatusLiveData?View.VISIBLE:View.GONE}" />

                <TextView
                    android:id="@+id/tvNeChargePower"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@{newEnergyViewModel.tvChargePower}"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_30"
                    android:visibility="@{newEnergyViewModel.chargingStatusLiveData==1 || newEnergyViewModel.dischargeStatusLiveData?View.VISIBLE:View.GONE}" />

                <TextView
                    android:id="@+id/tvNeElectricCurrent"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/px_48"
                    android:text="@{newEnergyViewModel.tvElectricCurrent}"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_30"
                    android:visibility="@{newEnergyViewModel.chargingStatusLiveData==1?View.VISIBLE:View.GONE}" />

                <TextView
                    android:id="@+id/tvNeVoltage"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/px_48"
                    android:text="@{newEnergyViewModel.tvVoltage}"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/pxt_30"
                    android:visibility="@{newEnergyViewModel.chargingStatusLiveData==1?View.VISIBLE:View.GONE}" />
            </LinearLayout>

            <!--            充放电管理-->
            <TextView
                android:id="@+id/tvNeChargePowerDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_663"
                android:text="@string/ne_charge_management"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_36"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covReservationCharging"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_35"
                app:layout_constraintStart_toStartOf="@id/tvNeChargePowerDesc"
                app:layout_constraintTop_toBottomOf="@id/tvNeChargePowerDesc"
                app:ovSwChecked="@{newEnergyViewModel.bookChargeSwitchLiveData}"
                app:ovTextContent="@{newEnergyViewModel.textBookCharge}"
                app:textTitle="@string/ne_reservation_charging" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covChargingLimit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/px_48"
                app:haveSwitch="false"
                app:haveTips="false"
                app:layout_constraintStart_toEndOf="@id/covReservationCharging"
                app:layout_constraintTop_toTopOf="@id/covReservationCharging"
                app:ovTextContent="@{`最大充电量：` + newEnergyViewModel.chargeStopSOCLiveData + `%`}"
                app:textTitle="@string/ne_charging_limit" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covDischarge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_35"
                app:enable="@{newEnergyViewModel.dischargeBtnEnable}"
                app:haveMore="false"
                app:layout_constraintStart_toStartOf="@id/covReservationCharging"
                app:layout_constraintTop_toBottomOf="@id/covReservationCharging"
                app:ovSwChecked="@{newEnergyViewModel.dischargeStatusLiveData}"
                app:ovTextContent="@{newEnergyViewModel.dischargeStatusLiveData?@string/ne_discharging:@string/ne_start_discharging}"
                app:textTitle="@string/ne_discharge" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covDischargingLimit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/px_48"
                app:haveSwitch="false"
                app:haveTips="false"
                app:layout_constraintStart_toEndOf="@id/covReservationCharging"
                app:layout_constraintTop_toTopOf="@id/covDischarge"
                app:ovTextContent="@{`截止放电量：`+ newEnergyViewModel.dischargeStopSOCLiveData + `%`}"
                app:textTitle="@string/ne_discharging_limit" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covParkingPowerGeneration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_35"
                app:enable="@{newEnergyViewModel.gearStatusLiveData==GearStatus.GEAR_P?true:false}"
                app:haveMore="false"
                app:haveTips="false"
                app:layout_constraintStart_toStartOf="@id/covReservationCharging"
                app:layout_constraintTop_toBottomOf="@id/covDischarge"
                app:ovSwChecked="@{newEnergyViewModel.forceChargeModeLiveData}"
                app:textTitle="@string/ne_parking_power_generation" />

            <!--            能量管理-->
            <TextView
                android:id="@+id/tvNeEnergyManagement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_87"
                android:text="@string/ne_energy_management"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_36"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/covParkingPowerGeneration" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covEnergyRecoveryLevel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_35"
                app:haveSwitch="false"
                app:haveTips="false"
                app:layout_constraintStart_toStartOf="@id/tvNeEnergyManagement"
                app:layout_constraintTop_toBottomOf="@id/tvNeEnergyManagement"
                app:textTitle="@string/ne_energy_recovery_level" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covMileageDisplay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/px_48"
                app:haveSwitch="false"
                app:haveTips="false"
                app:layout_constraintStart_toEndOf="@id/covEnergyRecoveryLevel"
                app:layout_constraintTop_toTopOf="@id/covEnergyRecoveryLevel"
                app:textTitle="@string/ne_mileage_display" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covPureElectricDisplay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_35"
                app:haveSwitch="false"
                app:haveTips="false"
                app:layout_constraintStart_toStartOf="@id/tvNeEnergyManagement"
                app:layout_constraintTop_toBottomOf="@id/covEnergyRecoveryLevel"
                app:textTitle="@string/ne_pure_electric_display" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covRangeConditionDisplay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/px_48"
                app:haveSwitch="false"
                app:haveTips="false"
                app:layout_constraintStart_toEndOf="@id/covEnergyRecoveryLevel"
                app:layout_constraintTop_toTopOf="@id/covPureElectricDisplay"
                app:textTitle="@string/ne_range_condition_display" />

            <!--            更多-->
            <TextView
                android:id="@+id/tvMore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_87"
                android:text="@string/str_more_title"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_36"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/covPureElectricDisplay" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covBookTrip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_35"
                app:haveTips="false"
                app:layout_constraintStart_toStartOf="@id/tvMore"
                app:layout_constraintTop_toBottomOf="@id/tvMore"
                app:ovSwChecked="@{newEnergyViewModel.bookTravelLiveData}"
                app:ovTextContent="@{newEnergyViewModel.textBookTravel}"
                app:textTitle="@string/ne_book_trip" />

            <com.bitech.vehiclesettings.view.widget.CommonOptionView
                android:id="@+id/covDisclaimers"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/px_48"
                app:haveSwitch="false"
                app:haveTips="false"
                app:layout_constraintStart_toEndOf="@id/covBookTrip"
                app:layout_constraintTop_toTopOf="@id/covBookTrip"
                app:textTitle="@string/ne_disclaimers" />

            <!--            <com.bitech.vehiclesettings.view.widget.CommonOptionView-->
            <!--                android:id="@+id/covEnergyCurve"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginStart="@dimen/px_48"-->
            <!--                app:haveSwitch="false"-->
            <!--                app:haveTips="false"-->
            <!--                app:layout_constraintStart_toEndOf="@id/covBookTrip"-->
            <!--                app:layout_constraintTop_toTopOf="@id/covBookTrip"-->
            <!--                app:textTitle="@string/ne_energy_curve" />-->

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.bitech.vehiclesettings.view.common.BounceScrollView>
</layout>