<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:cardCornerRadius="@dimen/statusbar_charging_info_radius">

    <View
        android:id="@+id/ivBatteryBackground"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/ivBatteryStatus"
        android:layout_width="@dimen/iv_battery_status_width"
        android:layout_height="@dimen/iv_battery_status_height"
        android:layout_gravity="start|center_vertical"
        android:layout_marginStart="@dimen/iv_battery_status_margin_start" />

    <TextView
        android:id="@+id/tvBatteryLevel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start|center_vertical"
        android:layout_marginStart="@dimen/tv_battery_status_level_margin_start"
        android:gravity="center_vertical"
        android:textColor="@color/text_status_bar_charging"
        android:textSize="@dimen/tv_battery_status_level_text_size" />

    <TextView
        android:id="@+id/tvBatteryStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|center_vertical"
        android:layout_marginEnd="@dimen/px_32"
        android:textColor="@color/text_status_bar_charging"
        android:textSize="@dimen/tv_battery_status_text_size" />

</androidx.cardview.widget.CardView>