<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="365.33dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="101.33dp"
        android:layout_marginStart="80dp"
        android:gravity="center">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:fontFamily="DreamHanSansCN"
            android:text="@string/str_wireless_charging"
            android:textColor="@color/black"
            android:textSize="@dimen/font_42px" />
    </LinearLayout>
        <LinearLayout
            android:layout_width="625dp"
            android:layout_height="214.33dp"
            android:layout_marginStart="80dp"
            android:orientation="vertical"
            android:gravity="center_horizontal">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_bg_white"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="106.67dp">

                    <Switch
                        android:id="@+id/sw_front"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="false"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="106.67dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/str_car_sunshade_front"
                                android:textColor="@color/black"
                                android:textSize="@dimen/font_36px" />

                            <TextView
                                android:id="@+id/tv_front_tip"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/str_toggle_close"
                                android:textColor="@color/color_transparent_40"
                                android:textSize="@dimen/font_28px" />

                        </LinearLayout>
                    </RelativeLayout>
                </LinearLayout>
                <View
                    android:layout_width="517.33dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="32dp"
                    android:background="@color/color_transparent_20"/>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="106.67dp">
                    <LinearLayout
                        android:id="@+id/ll_forget_reminder"
                        android:layout_width="match_parent"
                        android:layout_height="133.33dp">
                        <Switch
                            android:id="@+id/sw_forget_reminder"
                            android:layout_width="64dp"
                            android:layout_height="37.33dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:background="@color/transparent"
                            android:checked="false"
                            android:switchMinWidth="64dp"
                            android:thumb="@drawable/thumb"
                            android:track="@drawable/track" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_forget_reminder"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />
                    </LinearLayout>
                    <ImageView
                        android:id="@+id/iv_forget_reminder_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="33.33dp"
                        android:src="@mipmap/ic_small_tip" />
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
</LinearLayout>