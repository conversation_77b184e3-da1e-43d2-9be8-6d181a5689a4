<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="80dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_system_permission_special"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <!--分析与改进-->
            <LinearLayout
                android:layout_width="624dp"
                android:layout_height="106.67dp"
                android:layout_gravity="center"
                android:layout_marginTop="10.67dp"
                android:background="@drawable/shape_bg_white">

                <com.bitech.vehiclesettings.view.common.NoToggleSwitch
                    android:id="@+id/sw_analysis"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track"
                    app:track="@drawable/track"/>

                <RelativeLayout
                    android:id="@+id/rl_analysis"
                    android:layout_width="528dp"
                    android:layout_height="106.67dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_system_analysis_and_improve"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:id="@+id/iv_analysis_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="33.33dp"
                        android:src="@mipmap/ic_small_tip" />

                </RelativeLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="624dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="21.33dp"
                android:fontFamily="DreamHanSansCN"
                android:gravity="left"
                android:singleLine="false"
                android:text="@string/str_system_permission_special_content"
                android:textColor="@color/dialog_content_color"
                android:textSize="@dimen/font_36px" />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>