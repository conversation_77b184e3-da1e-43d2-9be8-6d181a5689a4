<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="101.33dp"
                android:layout_gravity="center"
                android:gravity="center">
                <TextView
                    android:id="@+id/title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="DreamHanSansCN"
                    android:text="@string/str_carsetting_lock_2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_48px" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="581.33dp"
                android:layout_height="226.67dp"
                android:layout_gravity="center"
                android:background="@drawable/shape_bg_white"
                android:orientation="vertical">


                <LinearLayout
                    android:layout_width="581.33dp"
                    android:layout_height="106.66dp">

                    <Switch
                        android:id="@+id/sw_child_lock_left"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="false"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_carsetting_child_lock"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:id="@+id/tv_child_lock_left"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_carsetting_child_lock_1"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginLeft="32dp"
                    android:layout_marginRight="32dp"
                    android:background="@color/color_chirld_lock_line" />

                <LinearLayout
                    android:layout_width="581.33dp"
                    android:layout_height="106.66dp">

                    <Switch
                        android:id="@+id/sw_child_lock_right"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="false"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_carsetting_child_lock_2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:id="@+id/tv_child_lock_right"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_carsetting_child_lock_3"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />

                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_gravity="center"
                android:layout_width="581.33dp"
                android:layout_height="106.66dp"
                android:layout_marginTop="21.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_rear_screen_control"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_carsetting_rear_sreen_control"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                    <TextView
                        android:id="@+id/tv_rear_screen_control"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_carsetting_rear_sreen_control_close"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>