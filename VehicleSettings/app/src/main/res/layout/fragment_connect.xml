<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    style="@style/scroll_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintTop_toBottomOf="parent">

    <LinearLayout
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_left"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/ll_bluetooth"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_bg_white"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <Switch
                        android:id="@+id/sw_blue"
                        android:layout_width="64dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="30dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_blue"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="8dp" />

                        <TextView
                            android:id="@+id/tv_blue_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_blue_desc"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />

                    </LinearLayout>
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="13.33dp"
                    android:layout_marginBottom="13.33dp"
                    android:src="@mipmap/ic_small_marker"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/ll_wifi"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <Switch
                        android:id="@+id/sw_wifi"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="30dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_wifi"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="8dp" />

                        <TextView
                            android:id="@+id/tv_wifi_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_wifi_desc"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="13.33dp"
                    android:layout_marginBottom="13.33dp"
                    android:src="@mipmap/ic_small_marker"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/ll_Hotpot"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_bg_white"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <Switch
                        android:id="@+id/sw_hotpot"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />


                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="30dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_hot"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="8dp" />

                        <TextView
                            android:id="@+id/tv_hotpot_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_hot_desc"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="13.33dp"
                    android:layout_marginBottom="13.33dp"
                    android:src="@mipmap/ic_small_marker"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <Switch
                        android:id="@+id/sw_5G"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="30dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_5G"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="8dp" />

                        <TextView
                            android:id="@+id/tv_fiveG_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_5G_desc"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_bg_white">
                <LinearLayout
                    android:id="@+id/ll_wireless_charging"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:orientation="horizontal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <LinearLayout
                        android:layout_height="wrap_content"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="30dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_wxcd"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <Space
                            android:layout_width="wrap_content"
                            android:layout_height="8dp" />

                        <TextView
                            android:id="@+id/tv_wxcd_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_wireless_charging_no"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/iv_wireless_remind"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|end"
                        android:layout_marginEnd="33.33dp"
                        android:src="@mipmap/ic_small_tip" />
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="13.33dp"
                    android:layout_marginBottom="13.33dp"
                    android:src="@mipmap/ic_small_marker"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </LinearLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>
