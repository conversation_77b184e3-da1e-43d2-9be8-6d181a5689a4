<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="368dp"
    android:layout_height="206.67dp"
    android:layout_marginBottom="21.33dp"
    app:cardCornerRadius="16dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <!-- 背景图片 -->
        <ImageView
            android:id="@+id/ivBackground"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/ivDel"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentEnd="true"
            android:layout_margin="16dp"
            android:src="@mipmap/ic_del_wallpaper"
            android:visibility="gone"/>


        <ImageView
            android:id="@+id/ivAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="67.33dp"
            android:visibility="gone"
            android:src="@mipmap/ic_add_wallpaper" />

        <TextView
            android:id="@+id/tvAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_wrapper_to_gallery"
            android:textSize="@dimen/font_36px"
            android:layout_centerHorizontal="true"
            android:visibility="gone"
            android:layout_marginTop="114dp"/>

        <!-- 右下角选择框 -->
        <ImageView
            android:id="@+id/ivCheckbox"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_margin="16dp"
            android:src="@mipmap/ic_checkbox_false_wallpaper" />
    </RelativeLayout>
</androidx.cardview.widget.CardView>