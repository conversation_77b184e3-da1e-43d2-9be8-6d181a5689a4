<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/border_bg_dialog"
        android:orientation="vertical">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="33.33dp"
                    android:fontFamily="DreamHanSansCN"
                    android:text="@string/str_wifi"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_48px" />

                <RelativeLayout
                    android:layout_width="581.33dp"
                    android:layout_height="106.33dp"
                    android:layout_marginLeft="85.33dp"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_condition_wifi"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_alignParentLeft="true"
                        android:layout_marginLeft="32dp"
                        android:layout_marginTop="37.33dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_marginLeft="128dp"
                        android:layout_marginTop="35.33dp"
                        android:text="@string/str_wifi"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_wifi_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_marginLeft="128dp"
                        android:layout_marginTop="74dp"
                        android:textColor="@color/color_transparent_40"
                        android:text="@string/str_wifi_desc"
                        android:textSize="@dimen/font_28px" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_used_title"
                    android:layout_width="581.33dp"
                    android:layout_height="60dp"
                    android:layout_marginStart="85.33dp"
                    android:layout_marginTop="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="13.33dp"
                        android:text="@string/str_connected"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_28px" />
                </RelativeLayout>
                <!--已连接-->
                <RelativeLayout
                    android:id="@+id/rl_connected_wifi"
                    android:layout_width="581.33dp"
                    android:layout_height="106.67dp"
                    android:layout_marginLeft="85.33dp"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/shape_bg_white"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/iv_connected_wifi"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_alignParentLeft="true"
                        android:layout_marginLeft="32dp"
                        android:layout_marginTop="37.33dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:src="@mipmap/ic_wifi"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <TextView
                        android:id="@+id/tv_item_connected_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_marginLeft="96dp"
                        android:layout_marginTop="22dp"
                        android:text="@string/str_wifi"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_item_connected_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_marginLeft="96dp"
                        android:layout_marginTop="60.66dp"
                        android:textColor="@color/color_transparent_40"
                        android:text="@string/str_wifi_desc"
                        android:textSize="@dimen/font_28px" />

                    <ImageView
                        android:id="@+id/iv_del"
                        android:layout_width="25.33dp"
                        android:layout_height="29.33dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="35.33dp"
                        android:src="@drawable/selector_del_device" />
                </RelativeLayout>
                <!--wifi已配对设备-->
                <LinearLayout
                    android:id="@+id/ll_used_wifi"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

                <RelativeLayout
                    android:id="@+id/rl_scan_title"
                    android:layout_width="581.33dp"
                    android:layout_height="60dp"
                    android:layout_marginLeft="85.33dp"
                    android:layout_marginTop="0dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="13.33dp"
                        android:text="@string/str_unconnect"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_28px" />

                    <ImageView
                        android:id="@+id/iv_refesh"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="13.33dp"
                        android:src="@mipmap/ic_refresh" />

                </RelativeLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_scan_wifi"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentLeft="true"
                    android:overScrollMode="never" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:orientation="horizontal" />
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</layout>