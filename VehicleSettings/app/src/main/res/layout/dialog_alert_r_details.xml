<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_recognition_camera"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <com.bitech.vehiclesettings.view.common.ToggleScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="30.33dp"
                android:paddingStart="30dp"
                android:paddingEnd="30dp"
                android:scrollbars="none"
                android:layout_gravity="center">

                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="DreamHanSansCN"
                    android:singleLine="false"
                    android:text="@string/str_recognition_camera_1"
                    android:textColor="@color/dialog_content_color"
                    android:textSize="@dimen/font_40px" />

            </com.bitech.vehiclesettings.view.common.ToggleScrollView>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>