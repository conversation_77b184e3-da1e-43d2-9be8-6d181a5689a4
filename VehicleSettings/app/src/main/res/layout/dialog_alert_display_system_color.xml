<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1274.67dp"
    android:layout_height="720dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="33.33dp"
        android:text="@string/str_screen_system_color"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <ImageView
            android:layout_width="346.86dp"
            android:layout_height="195.33dp"
            android:src="@mipmap/ic_systemcolor_left" />

        <ImageView
            android:layout_width="346.86dp"
            android:layout_height="195.33dp"
            android:layout_marginHorizontal="32dp"
            android:src="@mipmap/ic_systemcolor_middle" />

        <ImageView
            android:layout_width="346.86dp"
            android:layout_height="195.33dp"
            android:src="@mipmap/ic_systemcolor_right" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="98.67dp"
        android:layout_marginTop="48dp"
        android:text="@string/str_screen_choose_color"
        android:textColor="@color/black"
        android:textSize="@dimen/font_32px" />
    <!-- 选择颜色 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp">
        <View
            android:layout_width="1.33dp"
            android:layout_height="56.67dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="258.67dp"
            android:background="@android:color/darker_gray"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="60dp"/>

    </FrameLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="53.33dp">

        <Button
            android:id="@+id/btn_confirm"
            style="?android:borderlessButtonStyle"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:background="@drawable/button_blue"
            android:text="@string/str_screen_system_color_confirm"
            android:textColor="@color/white"
            android:textSize="@dimen/font_36px" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="44dp" />
</LinearLayout>