<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    style="@style/scroll_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintTop_toBottomOf="parent">

    <LinearLayout
        android:layout_width="1216dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_left"
        android:orientation="vertical">
        <View
            android:layout_width="wrap_content"
            android:layout_height="14dp"/>
        <!--壁纸、仪表主题-->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <!--壁纸-->
            <FrameLayout
                android:id="@+id/llWallpaper"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_bg_white"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="592dp"
                    android:layout_height="240dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/display_wp_btn" />

                <TextView
                    android:layout_width="592dp"
                    android:layout_height="80dp"
                    android:layout_gravity="bottom"
                    android:gravity="center"
                    android:text="@string/str_wrapper"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_36px" />

            </FrameLayout>

            <!--仪表主题-->
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:background="@drawable/shape_bg_white"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="592dp"
                    android:layout_height="240dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/display_topic_btn" />

                <TextView
                    android:layout_width="592dp"
                    android:layout_height="80dp"
                    android:layout_gravity="bottom"
                    android:gravity="center"
                    android:text="@string/str_topic"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </FrameLayout>
        </LinearLayout>

        <!--息屏保护、系统色-->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="21.33dp"
            android:orientation="horizontal">
            <!--息屏保护-->
            <RelativeLayout
                android:id="@+id/rlProtect"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_screen_protect"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="80.67dp"
                    android:layout_marginEnd="9.33dp"
                    android:layout_marginBottom="9.33dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
            <!--系统色-->
            <RelativeLayout
                android:id="@+id/rlSystemColor"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_screen_system_color"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="80.67dp"
                    android:layout_marginEnd="9.33dp"
                    android:layout_marginBottom="9.33dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <!--显示模式-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="39.33dp"
            android:text="@string/str_display_mode"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <com.bitech.vehiclesettings.view.common.SegmentedPickerView
            android:id="@+id/spvDayNight"
            android:layout_width="1216dp"
            android:layout_height="64dp"
            android:background="@color/color_spv"
            app:pickerFontSize="@dimen/font_36px"
            android:layout_marginTop="18dp" />

        <!--亮度-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="39.33dp"
            android:text="@string/str_display_ld"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
        <!--中控屏、仪表盘-->
        <LinearLayout
            android:layout_width="1216dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:orientation="horizontal">
            <!--中控屏-->
            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="32dp"
                        android:layout_marginTop="15.33dp"
                        android:text="@string/str_display_zk"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_34px" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                        android:id="@+id/sb_zkp"
                        android:layout_width="420dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="33.33dp"
                        android:layout_marginTop="15.33dp"
                        app:iconSrc="@mipmap/ic_sun_d"
                        app:max="10"
                        app:seekBarWidth="420dp" />
                    <TextView
                        android:id="@+id/btnZKPAUTO"
                        android:layout_width="86.67dp"
                        android:layout_height="40dp"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="21.33dp"
                        android:background="@drawable/selector_bg_quick_12"
                        android:gravity="center"
                        android:text="@string/str_auto"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>

            </LinearLayout>
            <!--仪表盘-->
            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginStart="32dp"
                android:background="@drawable/shape_bg_white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="32dp"
                        android:layout_marginTop="15.33dp"
                        android:text="@string/str_dashboard"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_34px" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                        android:id="@+id/sb_ybp"
                        android:layout_width="420dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="33.33dp"
                        android:layout_marginTop="15.33dp"
                        app:iconSrc="@mipmap/ic_sun_d"
                        app:max="10"
                        app:seekBarWidth="420dp" />

                    <TextView
                        android:id="@+id/btnYBPAUTO"
                        android:layout_width="86.67dp"
                        android:layout_height="40dp"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="21.33dp"
                        android:background="@drawable/selector_bg_quick_12"
                        android:gravity="center"
                        android:text="@string/str_auto"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
        <!--吸顶屏-->
        <LinearLayout
            android:layout_width="592dp"
            android:layout_height="120dp"
            android:layout_marginTop="32dp"
            android:background="@drawable/shape_bg_white"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="32dp"
                    android:layout_marginTop="15.33dp"
                    android:text="@string/str_display_ceiling"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_34px" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                    android:id="@+id/sb_xdp"
                    android:layout_width="420dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="33.33dp"
                    android:layout_marginTop="15.33dp"
                    app:iconSrc="@mipmap/ic_sun_d"
                    app:max="10"
                    app:seekBarWidth="420dp" />

                <Button
                    android:id="@+id/btnXDPAUTO"
                    style="?android:borderlessButtonStyle"
                    android:layout_width="86.67dp"
                    android:layout_height="40dp"
                    android:layout_gravity="bottom"
                    android:layout_marginStart="21.33dp"
                    android:background="@drawable/button_blue"
                    android:text="@string/str_auto"
                    android:textColor="@color/color_white"
                    android:textSize="@dimen/font_28px" />
            </LinearLayout>
        </LinearLayout>

        <!--字体大小-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:text="@string/str_display_font_size"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <com.bitech.vehiclesettings.view.common.SegmentedPickerView
            android:id="@+id/spvFontSize"
            android:layout_width="1216dp"
            app:pickerFontSize="@dimen/font_36px"
            android:layout_height="64dp"
            android:layout_marginTop="15.33dp" />

        <!--高级显示设置-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:text="@string/str_display_senior"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
        <!--仪表显示歌词-->
        <LinearLayout
            android:layout_width="592dp"
            android:layout_height="120dp"
            android:layout_marginTop="15.33dp"
            android:background="@drawable/shape_bg_white">

            <com.bitech.vehiclesettings.view.widget.UniversalSwitch
                android:id="@+id/swShowLyrics"
                android:layout_width="64dp"
                android:layout_height="37.33dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="32dp"
                android:background="@color/transparent"
                android:checked="true"
                android:switchMinWidth="64dp"
                android:thumb="@drawable/thumb"
                android:track="@drawable/track" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_yb"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_yb_desc"
                    android:textColor="@color/color_transparent_40"
                    android:textSize="@dimen/font_28px" />
            </LinearLayout>
        </LinearLayout>
        <!--更多-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:text="@string/str_more_title"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
        <!--分屏、视频限制-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="15.33dp"
            android:orientation="horizontal">
            <!--分屏-->
            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/swFp"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_fp"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_fp_desc"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>
            </LinearLayout>
            <!--视频限制-->
            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginStart="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/swVideoLimit"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_spxz"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_spxz_desc"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <!--视频限制、屏幕清洁-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="21.33dp"
            android:orientation="horizontal">

            <!--屏幕清洁-->
            <RelativeLayout
                android:id="@+id/rlClean"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_qjpm"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="80.67dp"
                    android:layout_marginEnd="9.33dp"
                    android:layout_marginBottom="9.33dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
            <!--恢复默认值-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="135.02dp"
                android:layout_marginStart="32dp"
                android:orientation="horizontal">

                <RelativeLayout
                    android:id="@+id/rlReset"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_bg_white">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_recover_default"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_marginTop="80.67dp"
                        android:layout_marginEnd="9.33dp"
                        android:layout_marginBottom="9.33dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>
            </LinearLayout>

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="29.33dp"
            android:orientation="horizontal" />
    </LinearLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>
