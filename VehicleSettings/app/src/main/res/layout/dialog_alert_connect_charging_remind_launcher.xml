<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="33.33dp"
        android:fontFamily="DreamHanSansCN"
        android:text="@string/str_warm_reminder_for_charging"
        android:textColor="@color/selector_text_color"
        android:textSize="@dimen/font_42px" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="624dp"
            android:layout_height="424dp"
            android:layout_marginStart="80dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:gravity="left"
                android:singleLine="false"
                android:text="@string/str_warm_reminder_for_charging_content"
                android:textColor="@color/dialog_content_color"
                android:textSize="@dimen/font_36px" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="66.67dp"
                android:gravity="center"
                android:layout_marginTop="20dp">
                <CheckBox
                    android:id="@+id/rb_no_reminder"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:background="@drawable/radio_btn_selector"
                    android:foreground="@null"
                    android:button="@null"
                    android:layout_width="32dp"
                    android:layout_height="32dp" />
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:text="@string/str_warm_reminder_for_charging_no_reminder_launcher"
                        android:textColor="@color/dialog_content_color"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="30.33dp"
                android:layout_marginBottom="45.33dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_confirm"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/selector_bg_open"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_confirm2"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_36px"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>