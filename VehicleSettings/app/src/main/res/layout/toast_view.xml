<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    android:id="@+id/rl_toast"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:background="@drawable/bg_toast"
        android:id="@+id/rl_toast_bg"
        android:layout_width="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_height="80dp"
        android:minWidth="490.66dp"
        android:layout_centerHorizontal="true">
        <TextView
            android:layout_gravity="center"
            android:id="@+id/toast_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="50dp"
            android:layout_marginRight="50dp"
            android:paddingHorizontal="6dp"
            android:gravity="center"
            tools:text="123"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px"/>
    </LinearLayout>

</RelativeLayout>
