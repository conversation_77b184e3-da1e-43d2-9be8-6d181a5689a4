<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/px_1584"
    android:layout_height="@dimen/px_1049"
    android:orientation="vertical"
    android:background="@drawable/border_bg_dialog">

    <RelativeLayout
        android:layout_width="@dimen/px_1344"
        android:layout_height="@dimen/px_152"
        android:layout_gravity="center_horizontal">
        <TextView
            android:id="@+id/tv_title1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="@string/tv_wash_car_title"
            android:textSize="42px"
            android:layout_centerInParent="true" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_normal_description"
        android:layout_width="@dimen/px_1344"
        android:layout_height="@dimen/px_138"
        android:layout_marginTop="@dimen/px_24"
        android:layout_marginLeft="@dimen/px_120"
        android:text="@string/tv_normal_description"
        android:textColor="@color/color_transparent_60"
        android:textSize="36px"
        android:layout_marginBottom="@dimen/px_10"/>

    <TextView
        android:id="@+id/tv_normal_condition"
        android:layout_width="@dimen/px_1344"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/px_120"
        android:text="@string/tv_normal_condition"
        android:textColor="@color/color_transparent_60"
        android:textSize="36px"
        android:layout_marginBottom="@dimen/px_5"/>

    <LinearLayout
        android:layout_width="@dimen/px_1344"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/px_120"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/px_10">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <RelativeLayout
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48">

                <ImageView
                    android:id="@+id/iv_1_icon"
                    android:layout_width="@dimen/px_48"
                    android:layout_height="@dimen/px_48"
                    android:src="@drawable/icon_set_cicle"/>

                <TextView
                    android:id="@+id/iv_1_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1"
                    android:textColor="@color/white"
                    android:textSize="32px"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>
            <TextView
                android:id="@+id/tv_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tv_1"
                android:textColor="@color/black"
                android:textSize="32px"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48">

                <ImageView
                    android:id="@+id/tv_2_icon"
                    android:layout_width="@dimen/px_48"
                    android:layout_height="@dimen/px_48"
                    android:src="@drawable/icon_set_cicle"/>

                <TextView
                    android:id="@+id/iv_2_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2"
                    android:textColor="@color/white"
                    android:textSize="32px"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>
            <TextView
                android:id="@+id/tv_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tv_2"
                android:textColor="@color/black"
                android:textSize="32px"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="@dimen/px_1344"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/px_120"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/px_20">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48">

                <ImageView
                    android:id="@+id/tv_3_icon"
                    android:layout_width="@dimen/px_48"
                    android:layout_height="@dimen/px_48"
                    android:src="@drawable/icon_set_cicle"/>

                <TextView
                    android:id="@+id/iv_3_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3"
                    android:textColor="@color/white"
                    android:textSize="32px"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>
            <TextView
                android:id="@+id/tv_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tv_3"
                android:textColor="@color/black"
                android:textSize="32px"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48">

                <ImageView
                    android:id="@+id/tv_4_icon"
                    android:layout_width="@dimen/px_48"
                    android:layout_height="@dimen/px_48"
                    android:src="@drawable/icon_set_cicle"/>

                <TextView
                    android:id="@+id/iv_4_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4"
                    android:textColor="@color/white"
                    android:textSize="32px"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>

            <TextView
                android:id="@+id/tv_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tv_4"
                android:textColor="@color/black"
                android:textSize="32px"/>
        </LinearLayout>
    </LinearLayout>
    <!-- 分割线View -->
    <View
        android:layout_width="@dimen/px_1344"
        android:layout_height="@dimen/px_1"
        android:layout_marginTop="@dimen/px_48"
        android:layout_marginLeft="@dimen/px_120"
        android:background="#979797"
        android:alpha="0.4"/>

    <TextView
        android:id="@+id/tv_conveyor_description"
        android:layout_width="@dimen/px_1344"
        android:layout_height="@dimen/px_138"
        android:layout_marginLeft="@dimen/px_120"
        android:layout_marginTop="@dimen/px_48"
        android:text="@string/tv_conveyor_description"
        android:textColor="@color/color_transparent_60"
        android:textSize="36px"
        android:layout_marginBottom="@dimen/px_10"/>

    <TextView
        android:id="@+id/tv_conveyor_condition"
        android:layout_width="@dimen/px_1344"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/px_120"
        android:text="@string/tv_conveyor_condition"
        android:textColor="@color/color_transparent_60"
        android:textSize="36px"
        android:layout_marginBottom="@dimen/px_5"/>

    <LinearLayout
        android:layout_width="@dimen/px_1344"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/px_120"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/px_10">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48">

                <ImageView
                    android:id="@+id/iv_5_icon"
                    android:layout_width="@dimen/px_48"
                    android:layout_height="@dimen/px_48"
                    android:src="@drawable/icon_set_cicle"/>

                <TextView
                    android:id="@+id/iv_5_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1"
                    android:textColor="@color/white"
                    android:textSize="32px"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>
            <TextView
                android:id="@+id/tv_5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tv_5"
                android:textColor="@color/black"
                android:textSize="32px"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48">

                <ImageView
                    android:id="@+id/tv_6_icon"
                    android:layout_width="@dimen/px_48"
                    android:layout_height="@dimen/px_48"
                    android:src="@drawable/icon_set_cicle"/>

                <TextView
                    android:id="@+id/iv_6_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2"
                    android:textColor="@color/white"
                    android:textSize="32px"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>

            <TextView
                android:id="@+id/tv_6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tv_6"
                android:textColor="@color/black"
                android:textSize="32px" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="@dimen/px_1344"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/px_120"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48">

                <ImageView
                    android:id="@+id/tv_7_icon"
                    android:layout_width="@dimen/px_48"
                    android:layout_height="@dimen/px_48"
                    android:src="@drawable/icon_set_cicle"/>

                <TextView
                    android:id="@+id/iv_7_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3"
                    android:textColor="@color/white"
                    android:textSize="32px"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>
            <TextView
                android:id="@+id/tv_7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tv_7"
                android:textColor="@color/black"
                android:textSize="32px"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="@dimen/px_48"
                android:layout_height="@dimen/px_48">

                <ImageView
                    android:id="@+id/tv_8_icon"
                    android:layout_width="@dimen/px_48"
                    android:layout_height="@dimen/px_48"
                    android:src="@drawable/icon_set_cicle"/>

                <TextView
                    android:id="@+id/iv_8_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4"
                    android:textColor="@color/white"
                    android:textSize="32px"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>

            <TextView
                android:id="@+id/tv_8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tv_8"
                android:textColor="@color/black"
                android:textSize="32px"/>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>