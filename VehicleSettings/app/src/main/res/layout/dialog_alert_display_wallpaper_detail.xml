<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="533.33dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/image_foreground"
        android:layout_width="500dp"
        android:layout_height="265.33dp"
        android:layout_marginTop="56dp"
        android:layout_gravity="center_horizontal"
        android:src="@mipmap/display_wp_detail_bg2" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="36dp"
        android:text="@string/str_wrapper_detail_desc"
        android:textColor="@color/color_detail_60"
        android:textSize="@dimen/font_36px" />

    <Button
        android:id="@+id/btn_confirm"
        style="?android:borderlessButtonStyle"
        android:layout_width="624dp"
        android:layout_height="66.67dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="50.67dp"
        android:background="@drawable/button_blue"
        android:text="@string/str_confirm2"
        android:textColor="@color/white"
        android:textSize="@dimen/font_36px" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="44dp" />
</LinearLayout>