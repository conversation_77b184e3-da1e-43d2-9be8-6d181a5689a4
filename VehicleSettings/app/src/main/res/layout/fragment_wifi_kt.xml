<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.widget.ScanScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/settings_wifi_ssv"
    style="@style/settings_scroll_bar_style"
    android:layout_width="784dp"
    android:layout_height="533dp"
    android:background="@drawable/border_bg_dialog"
    tools:context=".view.connect.WifiFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="784dp"
        android:layout_height="533dp"
        android:layout_marginEnd="@dimen/dp_23">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_wifi_cl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_blue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_wifi"
                android:textColor="@color/black"
                android:textSize="@dimen/font_48px"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_wifi_switch_cl"
                android:layout_width="624dp"
                android:layout_height="107dp"
                android:layout_marginTop="107dp"
                android:layout_marginStart="80dp"
                android:background="@drawable/shape_bg_white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <Switch
                    android:id="@+id/settings_wifi_sw"
                    style="@style/settings_switch_style"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="39dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />

                <TextView
                    android:id="@+id/settings_wifi_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="128dp"
                    android:layout_marginTop="38dp"
                    android:text="@string/str_wifi"
                    android:textColor="@color/black"
                    android:textSize="@dimen/sp_24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--已连接Wifi文字-->
            <TextView
                android:id="@+id/settings_wifi_connected_tv"
                style="@style/settings_text_30_regular_747578_style"
                android:layout_width="550dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="80dp"
                android:layout_marginTop="32dp"
                android:gravity="center|left"
                android:text="@string/wifi_connected"
                android:textColor="@color/black"
                android:textSize="21sp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/settings_wifi_switch_cl" />
            <!--已连接Wifi列表-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_wifi_connected_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="76dp"
                android:visibility="gone"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_wifi_switch_cl">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/settings_connected_wifi_rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="80dp"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/settings_wifi_use_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="80dp"
                android:layout_marginBottom="16dp"
                android:gravity="center|left"
                android:text="@string/wifi_can_use"
                android:textColor="@color/black"
                android:textSize="21sp"
                app:layout_constraintBottom_toTopOf="@+id/settings_can_use_wifi_cl"
                app:layout_constraintStart_toStartOf="parent" />

            <ProgressBar
                android:id="@+id/settings_wifi_scan_pb"
                style="@style/settings_icon_48x48_style"
                android:layout_marginStart="8dp"
                android:layout_marginTop="28dp"
                android:layout_marginBottom="16dp"
                android:indeterminate="true"
                android:indeterminateDrawable="@drawable/progress_bar_bt_scan_bg"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/settings_can_use_wifi_cl"
                app:layout_constraintStart_toEndOf="@+id/settings_wifi_use_tv"
                app:layout_constraintTop_toBottomOf="@+id/settings_wifi_connected_cl" />

            <ImageView
                android:id="@+id/settings_wifi_start_scan_iv"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="80dp"
                android:layout_marginBottom="16dp"
                android:src="@drawable/icon_list_refresh_48"
                app:layout_constraintBottom_toTopOf="@id/settings_can_use_wifi_cl"
                app:layout_constraintEnd_toEndOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_can_use_wifi_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="76dp"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_wifi_connected_cl">

                <TextView
                    android:id="@+id/settings_no_scan_wifi_tv"
                    style="@style/settings_text_32_regular_17191e_style"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="@string/wifi_not_found_can_use"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/settings_can_use_wifi_rv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="80dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_network_notification_cl"
                android:layout_width="624dp"
                android:layout_height="106.66dp"
                android:layout_marginStart="80dp"
                android:background="@drawable/shape_bg_white"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_can_use_wifi_cl">

                <Switch
                    android:id="@+id/settings_network_notification_sw"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_marginStart="35dp"
                    android:layout_marginTop="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />

                <TextView
                    android:id="@+id/setting_network_notification_tv"
                    android:layout_width="738dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="36dp"
                    android:layout_marginTop="22dp"
                    android:text="@string/wifi_network_notification_switcher"
                    android:textColor="@color/black"
                    android:textSize="@dimen/sp_24"
                    app:layout_constraintStart_toEndOf="@id/settings_network_notification_sw"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/setting_network_notification_tips_tv"
                    android:layout_width="738dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="36dp"
                    android:layout_marginBottom="@dimen/dp_32"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical"
                    android:text="@string/wifi_network_notification_tip"
                    android:textColor="@color/black_transparent_40"
                    android:textSize="@dimen/sp_24"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/settings_network_notification_sw"
                    app:layout_constraintTop_toBottomOf="@id/setting_network_notification_tv" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_setting_wifi_add"
                android:layout_width="624dp"
                android:layout_height="106.66dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_bg_white"
                android:layout_marginStart="80dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_network_notification_cl">

                <TextView
                    android:id="@+id/settings_wifi_add"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="32dp"
                    android:text="@string/wifi_add"
                    android:textColor="@color/black"
                    android:textSize="@dimen/sp_24"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_bias="0.511"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="13.33dp"
                    android:layout_marginBottom="13.33dp"
                    android:src="@mipmap/ic_small_marker"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_setting_wifi_add" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.bitech.vehiclesettings.view.widget.ScanScrollView>
