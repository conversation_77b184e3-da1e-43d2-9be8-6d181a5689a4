<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="380dp"
    android:minWidth="752dp"
    android:minHeight="530.67dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="101.34dp"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:gravity="center">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/str_park_power_2"
            android:textColor="@color/black"
            android:textSize="@dimen/font_42px" />
    </LinearLayout>


    <TextView
        android:layout_width="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="@string/str_park_power_desc_2"
        android:textColor="@color/black_transparent_60"
        android:textSize="@dimen/font_36px"
        />
    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:layout_marginTop="50.66dp">

        <Button
            android:id="@+id/btn_confirm"
            style="?android:borderlessButtonStyle"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:background="@drawable/button_blue_n"
            android:text="@string/str_confirm_exit"
            android:textColor="@color/white"
            android:textSize="@dimen/font_36px" />

        <Button
            android:id="@+id/btn_cancel"
            style="?android:borderlessButtonStyle"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_marginStart="32dp"
            android:background="@drawable/button_white"
            android:text="@string/str_cancel"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
    </LinearLayout>
</LinearLayout>