<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/border_bg_dialog">
    
    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/dialog_background_984_520_style"
        android:layout_width="984dp"
        android:layout_height="520dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        
        <com.bitech.vehiclesettings.view.widget.ScanScrollView
            style="@style/settings_scroll_bar_style2"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_96"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_77"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_17"
                android:minHeight="@dimen/dp_96">
                
                <TextView
                    android:id="@+id/dialog_title_tv"
                    style="@style/settings_text_40_medium_17191e_style"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
        
        </com.bitech.vehiclesettings.view.widget.ScanScrollView>
        
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="576dp"
            android:layout_height="112dp"
            android:layout_marginStart="204dp"
            android:layout_marginTop="176dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            
            <ImageButton
                android:id="@+id/settings_bt_type_aa_cp_ib"
                style="@style/settings_icon_112x112_style"
                android:background="@color/color_00000000"
                android:src="@drawable/image_button_bt_android_auto_112"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            
            <ImageButton
                android:id="@+id/settings_bt_type_phone_ib"
                style="@style/settings_icon_112x112_style"
                android:background="@color/color_00000000"
                android:src="@drawable/image_button_bt_phone_112"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            
            <ImageButton
                android:id="@+id/settings_bt_type_music_ib"
                style="@style/settings_icon_112x112_style"
                android:background="@color/color_00000000"
                android:src="@drawable/image_button_bt_music_112"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        
        </androidx.constraintlayout.widget.ConstraintLayout>
        
        <Button
            android:id="@+id/dialog_confirm_btn"
            style="@style/dialog_btn_confirm_402_92_style"
            android:layout_marginStart="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_60"
            android:layout_marginBottom="@dimen/dp_60"
            android:text="@string/dialog_confirm_text"
            android:background="@drawable/shape_bg_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/dialog_cancel_btn"
            app:layout_constraintStart_toStartOf="parent" />
        
        <Button
            android:id="@+id/dialog_cancel_btn"
            style="@style/dialog_btn_cancel_402_92_style"
            android:layout_marginEnd="@dimen/dp_60"
            android:text="@string/dialog_cancel_text"
            android:background="@drawable/shape_bg_white"
            app:layout_constraintBottom_toBottomOf="@id/dialog_confirm_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/dialog_confirm_btn"
            app:layout_constraintTop_toTopOf="@id/dialog_confirm_btn" />
    
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
