<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="429.33dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="101.33dp"
        android:layout_marginStart="80dp"
        android:orientation="horizontal"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_42px"
            android:textColor="@color/black"
            android:text="@string/str_condition_repair_check"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_wiper_repair_mode"
        android:layout_width="624dp"
        android:layout_height="120dp"
        android:layout_marginTop="16dp"
        android:layout_marginStart="80dp"
        android:background="@drawable/shape_bg_white">

        <!-- 用 FrameLayout 包裹 Switch 和透明 View -->
        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical">

            <!-- Switch 组件 -->
            <Switch
                android:id="@+id/sw_wiper_repair"
                android:layout_width="64dp"
                android:layout_height="37.33dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="32dp"
                android:background="@color/transparent"
                android:switchMinWidth="64dp"
                android:thumb="@drawable/thumb"
                android:track="@drawable/track" />
            <!-- 透明 View，仅覆盖 Switch -->
            <View
                android:visibility="gone"
                android:id="@+id/view_switch_overlay"
                android:layout_width="64dp"
                android:layout_height="37.33dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="32dp"
                android:background="@android:color/transparent"
                android:clickable="true" />
        </FrameLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="32dp"
                android:text="@string/str_condition_wiper_repair_mode"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/rl_sun_shade_curtain_repair_mode"
        android:layout_marginTop="21.33dp"
        android:layout_width="624dp"
        android:layout_height="120dp"
        android:layout_marginLeft="80dp"
        android:background="@drawable/shape_bg_white">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="32dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_sun_shade_curtain_repair_mode"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_sun_shade_curtain_repair_mode_content"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_28px" />

        </LinearLayout>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="84dp"
            android:layout_marginRight="6dp"
            android:src="@mipmap/ic_small_marker" />

    </RelativeLayout>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="44dp" />
</LinearLayout>