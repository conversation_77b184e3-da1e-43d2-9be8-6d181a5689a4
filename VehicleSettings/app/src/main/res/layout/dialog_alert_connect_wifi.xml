<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="33.33dp"
        android:layout_gravity="center"
        android:fontFamily="DreamHanSansCN"
        android:text="@string/str_wifi"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px" />

    <Space
        android:layout_width="wrap_content"
        android:layout_height="33dp"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">
            <LinearLayout
                android:id="@+id/ll_bluetooth"
                android:layout_width="581.33dp"
                android:layout_height="106.66dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/wifi_switch"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|left"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_wifi"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>
            </LinearLayout>
            <Space
                android:layout_width="wrap_content"
                android:layout_height="16dp"/>

            <LinearLayout
                android:id="@+id/Bluetooth_list"
                android:layout_width="581.33dp"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="visible">
                <!--                下面为已配对设备布局-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp">

                    <TextView
                        android:id="@+id/textView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="13dp"
                        android:text="@string/str_wifi_desc2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_32px"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/connected_wifi_list"
                    android:layout_width="581.33dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1" />

                <LinearLayout
                    android:id="@+id/Bluetooth_devices_list"
                    android:layout_width="581.33dp"
                    android:layout_height="60dp"
                    android:orientation="vertical"
                    android:visibility="visible">
                    <!--                下面为已配对设备布局-->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView
                            android:id="@+id/textView2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="13dp"
                            android:text="@string/str_wifi_desc3"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_32px"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/ic_refresh"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_gravity="center_vertical"
                            android:src="@mipmap/ic_refresh"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>


                </LinearLayout>
                <!--可用设备展示列表-->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/available_wifi_list"
                    android:layout_width="581.33dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>