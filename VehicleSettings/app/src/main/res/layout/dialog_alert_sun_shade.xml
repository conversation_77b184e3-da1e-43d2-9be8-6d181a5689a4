<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="101.33dp"
                android:layout_gravity="center"
                android:gravity="center">
                <TextView
                    android:id="@+id/title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="DreamHanSansCN"
                    android:text="@string/str_SUNSHADE"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_48px" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_tools_sub_sunshade"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="30dp"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/ll_tools_sub_sunshade_all"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/tv_car_sunshade_front"
                        android:layout_width="198px"
                        android:layout_height="198px"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_front"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_front"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_car_sunshade_rear"
                        android:layout_width="198px"
                        android:layout_height="198px"
                        android:layout_marginLeft="32dp"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_rear"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_tools_sub_sunshade_front"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_car_sunshade_front_open"
                        android:layout_width="198px"
                        android:layout_height="198px"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_front"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_open"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_car_sunshade_front_close"
                        android:layout_width="198px"
                        android:layout_height="198px"
                        android:layout_marginLeft="32dp"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_close"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_tools_sub_sunshade_rear"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_car_sunshade_rear_open"
                        android:layout_width="198px"
                        android:layout_height="198px"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_front"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_open"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_car_sunshade_rear_close"
                        android:layout_width="198px"
                        android:layout_height="198px"
                        android:layout_marginLeft="32dp"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_close"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>