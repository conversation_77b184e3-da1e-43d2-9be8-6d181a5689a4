<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="hasCancel"
            type="boolean" />

        <variable
            name="contentText"
            type="String" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/border_bg_dialog"
        android:paddingHorizontal="@dimen/px_120"
        android:paddingTop="@dimen/px_137"
        android:paddingBottom="@dimen/px_76">

        <TextView
            android:id="@+id/tvContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_36"
            android:text="@{contentText}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/ne_whether_charing_immediately" />

        <Button
            android:id="@+id/btnConfirm"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_96"
            android:layout_marginEnd="@dimen/px_48"
            app:layout_goneMarginEnd="0dp"
            android:text="@string/dialog_ok_text"
            android:textColor="@color/white"
            android:textSize="@dimen/pxt_36"
            app:drawable_radius="@{@dimen/px_16}"
            app:drawable_solidColor="@{@color/blue}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btnCancel"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="@dimen/px_444"
            android:layout_height="@dimen/px_96"
            android:text="@string/dialog_cancel_text"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_36"
            android:visibility="@{hasCancel? View.VISIBLE : View.GONE}"
            app:drawable_radius="@{@dimen/px_16}"
            app:drawable_solidColor="@{@color/bg_solid_color_1}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>