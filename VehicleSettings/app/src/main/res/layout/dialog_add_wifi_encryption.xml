<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="752dp"
    android:layout_height="533dp"
    android:background="@drawable/border_bg_dialog">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="33dp"
        android:text="@string/wifi_encryption_type"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_32"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="581dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="85dp"
        android:layout_marginTop="100dp"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <LinearLayout
            android:layout_width="581dp"
            android:layout_height="80dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/shape_bg_white">

            <TextView
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_weight="@dimen/weight_1"
                android:text="@string/wifi_add_wifi_encryption_2_textview"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_24" />

            <ImageView
                android:id="@+id/wifi_add_wifi_encryption_2"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_40"
                android:layout_marginEnd="@dimen/dp_32"
                android:background="@drawable/circular_check_selector"
                android:clickable="true"
                android:focusable="true"
                />


        </LinearLayout>

        <LinearLayout
            android:layout_width="581dp"
            android:layout_height="80dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/shape_bg_white">


            <TextView
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_weight="@dimen/weight_1"
                android:text="@string/wifi_add_wifi_encryption_3_textview"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_24" />

            <ImageView
                android:id="@+id/wifi_add_wifi_encryption_3"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_40"
                android:layout_marginEnd="@dimen/dp_32"
                android:background="@drawable/circular_check_selector"
                android:clickable="true"
                android:focusable="true"
                />

        </LinearLayout>

        <LinearLayout
            android:layout_width="581dp"
            android:layout_height="80dp"
            android:background="@drawable/shape_bg_white">


            <TextView
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_weight="@dimen/weight_1"
                android:text="@string/wifi_add_wifi_encryption_4_textview"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_24" />

            <ImageView
                android:id="@+id/wifi_add_wifi_encryption_4"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_40"
                android:layout_marginEnd="@dimen/dp_32"
                android:background="@drawable/circular_check_selector"
                android:clickable="true"
                android:focusable="true"
                />
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
