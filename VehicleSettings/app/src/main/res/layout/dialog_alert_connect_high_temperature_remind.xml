<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="33.33dp"
        android:fontFamily="DreamHanSansCN"
        android:text="@string/str_warm_reminder_for_high_temperature"
        android:textColor="@color/selector_text_color"
        android:textSize="@dimen/font_48px" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="424dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="1104dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:singleLine="false"
                android:text="@string/str_warm_reminder_for_high_temperature_content"
                android:textColor="@color/dialog_content_color"
                android:textSize="@dimen/font_36px" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="85.33dp"
                android:layout_marginTop="30.33dp"
                android:layout_marginRight="85.33dp"
                android:layout_marginBottom="45.33dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_confirm"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/selector_bg_open"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_confirm2"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>