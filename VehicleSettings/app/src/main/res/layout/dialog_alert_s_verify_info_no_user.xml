<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="365.33dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="624dp"
        android:layout_height="101.33dp"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:text="@string/str_system_factory_data_reset_title"
        android:textColor="@color/black"
        android:textSize="@dimen/font_42px" />
    <TextView
        android:layout_marginStart="80dp"
        android:layout_marginTop="16dp"
        android:layout_width="624dp"
        android:layout_height="wrap_content"
        android:textSize="@dimen/font_36px"
        android:textColor="@color/black_transparent_60"
        android:text="@string/str_system_reset_data_no_user"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="66.67dp"
        android:layout_below="@id/tv_simply_text"
        android:layout_marginStart="80dp"
        android:layout_marginTop="53.33dp"
        android:layout_marginEnd="80dp"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/ll_reset_confirm"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:gravity="center"
            android:background="@drawable/selector_bg_red">
            <ImageView
                android:id="@+id/iv_reset_confirm"
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_30"
                android:visibility="gone"
                android:background="@mipmap/ic_loading_d"/>
            <TextView
                android:layout_marginStart="@dimen/dp_16"
                android:id="@+id/tv_sys_reset_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/str_recover_factory"
                android:textColor="@color/sys_reset_white"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_sys_reset_cancel"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="32dp"
            android:background="@drawable/selector_bg_cancel"
            android:fontFamily="DreamHanSansCN"
            android:gravity="center"
            android:text="@string/str_cancel"
            android:textColor="@color/selector_text_color"
            android:textSize="@dimen/font_36px" />

    </LinearLayout>
</LinearLayout>