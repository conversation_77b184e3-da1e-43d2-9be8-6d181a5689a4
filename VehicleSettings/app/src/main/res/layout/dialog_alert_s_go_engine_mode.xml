<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="85.33dp"
        android:layout_marginTop="32.66dp"
        android:layout_marginEnd="85.33dp"
        android:fontFamily="HarmonyOS_Sans_SC"
        android:gravity="center"
        android:text="enter password"
        android:textColor="@color/color_dialog_content"
        android:textSize="@dimen/font_40px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="85.33dp"
        android:layout_marginTop="32dp"
        app:cardCornerRadius="15dp"
        android:layout_marginEnd="85.33dp">

        <EditText
            android:id="@+id/etInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#7C7C7C"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center_vertical"
            android:inputType="text"
            android:padding="16dp"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_content" />

    </androidx.cardview.widget.CardView>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="85.33dp"
        android:layout_marginTop="53.33dp"
        android:layout_marginEnd="85.33dp"
        android:layout_marginBottom="45.33dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/tv_content">

        <TextView
            android:id="@+id/tvConfirm"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/selector_bg_open"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_confirm2"
            android:textColor="#ffffff"
            android:textSize="@dimen/font_36px" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="50.66dp"
            android:background="@drawable/selector_bg_cancel"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_cancel"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

    </LinearLayout>
</LinearLayout>