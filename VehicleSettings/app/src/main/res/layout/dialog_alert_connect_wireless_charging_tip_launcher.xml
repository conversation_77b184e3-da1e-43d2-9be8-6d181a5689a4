<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="356dp"
    android:background="@drawable/border_bg_dialog"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="642dp"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingRight="@dimen/px_48">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_100"
            android:gravity="center"
            android:orientation="vertical">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:textColor="@color/black"
                android:gravity="center"
                android:textSize="@dimen/font_42px"
                android:text="@string/str_warm_reminder_for_charging"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <TextView
                android:id="@+id/tv_charge_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_warm_reminder_for_charging_content_launcher"
                android:textSize="@dimen/font_36px"
                android:textColor="@color/dialog_content_color"
                />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>