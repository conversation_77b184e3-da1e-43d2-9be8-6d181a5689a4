<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/px_832"
    android:layout_height="@dimen/px_200"
    android:background="@drawable/shape_bg_white">

    <Switch
        android:id="@+id/swOption"
        android:layout_width="@dimen/px_96"
        android:layout_height="@dimen/px_56"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/px_48"
        android:background="@color/transparent"
        android:thumb="@drawable/thumb"
        android:track="@drawable/track"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/px_48"
        android:layout_marginBottom="@dimen/px_11"
        android:gravity="center_vertical"
        android:lineHeight="@dimen/px_46"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="@dimen/pxt_36"
        app:layout_constraintBottom_toTopOf="@id/tvContent"
        app:layout_constraintStart_toEndOf="@id/swOption"
        app:layout_goneMarginBottom="@dimen/px_75"
        tools:text="@string/ne_reservation_charging" />

    <TextView
        android:id="@+id/tvContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/px_52"
        android:gravity="center_vertical"
        android:lineHeight="@dimen/px_36"
        android:maxLines="1"
        android:singleLine="true"
        android:text="@string/str_blue_desc"
        android:textColor="@color/color_transparent_40"
        android:textSize="@dimen/pxt_28"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        tools:text="@string/ne_reservation_charging" />

    <ImageView
        android:id="@+id/ivTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/px_50"
        android:src="@mipmap/ic_small_tip"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivMarkerMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/px_20"
        android:layout_marginBottom="@dimen/px_20"
        android:src="@mipmap/ic_small_marker"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>