<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>


    </data>

    <com.bitech.vehiclesettings.view.common.BounceScrollView
        android:id="@+id/scrollView"
        style="@style/scroll_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="parent">

        <LinearLayout
            android:layout_width="@dimen/dp_width"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dp_left"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="416dp" />

            <LinearLayout
                android:id="@+id/ll_car_mode_title"
                android:layout_width="match_parent"
                android:layout_height="66.67dp"
                android:layout_marginTop="57.67dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_test"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-2dp"
                    android:gravity="center"
                    android:text="@string/str_driving_car_mode"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:id="@+id/iv_car_mode_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="18dp"
                    android:src="@mipmap/ic_small_tip" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/rl_car_mode"
                android:layout_width="match_parent"
                android:layout_height="120dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:id="@+id/ll_drive_mode_ev"
                        android:layout_width="176dp"
                        android:layout_height="120dp"
                        android:background="@mipmap/img_set_drivemode_ev"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:layout_marginTop="49.33dp"
                            android:text="@string/str_driving_car_mode_1"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:text="EV"
                            android:textColor="@color/white_transparent_60"
                            android:textSize="@dimen/font_24px" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_drive_mode_eco"
                        android:layout_width="176dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/shape_bg_white"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:layout_marginTop="49.33dp"
                            android:text="@string/str_driving_car_mode_2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:text="ECO"
                            android:textColor="@color/black_transparent_60"
                            android:textSize="@dimen/font_24px" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_drive_mode_comfort"
                        android:layout_width="176dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/shape_bg_white"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:layout_marginTop="49.33dp"
                            android:text="@string/str_driving_car_mode_3"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:text="COMFORT"
                            android:textColor="@color/black_transparent_60"
                            android:textSize="@dimen/font_24px" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_drive_mode_gt"
                        android:layout_width="176dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/shape_bg_white"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:layout_marginTop="49.33dp"
                            android:text="@string/str_driving_car_mode_4"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:text="GT"
                            android:textColor="@color/black_transparent_60"
                            android:textSize="@dimen/font_24px" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_drive_mode_snow"
                        android:layout_width="176dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/shape_bg_white"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:layout_marginTop="49.33dp"
                            android:text="@string/str_driving_car_mode_5"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:text="SNOW"
                            android:textColor="@color/black_transparent_60"
                            android:textSize="@dimen/font_24px" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_drive_mode_custom"
                        android:layout_width="176dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/shape_bg_white"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:layout_marginTop="49.33dp"
                            android:text="@string/str_driving_car_mode_6"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18.67dp"
                            android:text="CUSTOM"
                            android:textColor="@color/black_transparent_60"
                            android:textSize="@dimen/font_24px" />
                    </LinearLayout>
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="77.33dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />
            </RelativeLayout>


            <LinearLayout
                android:id="@+id/ll_person_tip"
                android:layout_width="match_parent"
                android:layout_height="60dp">

                <TextView
                    android:id="@+id/tv_personalized_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="16dp"
                    android:text="@string/str_driving_car_mode_6_tip"
                    android:textColor="@color/color_transparent_60"
                    android:textSize="@dimen/font_34px" />
            </LinearLayout>

            <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/fl_extreme_pure_electric"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/ll_extreme_pure_electric"
                        android:layout_width="592dp"
                        android:layout_height="120dp"
                        android:background="@drawable/shape_bg_white">

                        <Switch
                            android:id="@+id/sw_extreme_pure_electric"
                            android:layout_width="64dp"
                            android:layout_height="37.33dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:background="@color/transparent"
                            android:switchMinWidth="64dp"
                            android:thumb="@drawable/thumb"
                            android:track="@drawable/track" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="120dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginLeft="32dp"
                                android:text="@string/str_driving_car_mode_7"
                                android:textColor="@color/black"
                                android:textSize="@dimen/font_36px" />
                        </RelativeLayout>
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/iv_extreme_pure_electric"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignRight="@id/ll_extreme_pure_electric"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="33.33dp"
                        android:src="@mipmap/ic_small_tip" />
                </RelativeLayout>

                <View
                    android:id="@+id/vw_extreme_pure_electric"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@android:color/transparent"
                    android:clickable="true"
                    android:elevation="10dp"
                    android:focusable="true"
                    android:visibility="gone" />
            </FrameLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="26dp"
                android:text="@string/str_driving_car_pedal_control"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="18dp"
                android:orientation="horizontal">

                <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/ll_comfort_braking"
                        android:layout_width="592dp"
                        android:layout_height="120dp"
                        android:background="@drawable/shape_bg_white"
                        android:clickable="false"
                        android:focusable="false">

                        <Switch
                            android:id="@+id/sw_comfort_braking"
                            android:layout_width="64dp"
                            android:layout_height="37.33dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:background="@color/transparent"
                            android:switchMinWidth="64dp"
                            android:thumb="@drawable/thumb"
                            android:track="@drawable/track" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="120dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginLeft="32dp"
                                android:text="@string/str_driving_car_pedal_control_1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/font_36px" />

                            <ImageView
                                android:id="@+id/iv_comfort_braking"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentRight="true"
                                android:layout_centerVertical="true"
                                android:layout_marginRight="33.33dp"
                                android:src="@mipmap/ic_small_tip" />

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentRight="true"
                                android:layout_marginTop="84dp"
                                android:layout_marginRight="6dp"
                                android:src="@mipmap/ic_small_marker" />
                        </RelativeLayout>
                    </LinearLayout>

                    <View
                        android:id="@+id/vw_comfort"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@android:color/transparent"
                        android:clickable="true"
                        android:elevation="10dp"
                        android:focusable="true"
                        android:visibility="gone" />  <!-- 确保覆盖其他视图 -->
                </FrameLayout>

                <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    android:layout_marginLeft="32dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/llAutoParking"
                        android:layout_width="592dp"
                        android:layout_height="120dp"
                        android:background="@drawable/shape_bg_white">

                        <Switch
                            android:id="@+id/sw_automatic_parking"
                            android:layout_width="64dp"
                            android:layout_height="37.33dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:background="@color/transparent"
                            android:switchMinWidth="64dp"
                            android:thumb="@drawable/thumb"
                            android:track="@drawable/track" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_driving_car_pedal_control_2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />
                    </LinearLayout>

                    <View
                        android:id="@+id/vw_auto_parking"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@android:color/transparent"
                        android:clickable="true"
                        android:elevation="10dp"
                        android:focusable="true"
                        android:visibility="gone" />
                </FrameLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="21.33dp"
                android:orientation="horizontal">

                <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/ll_parking_brake"
                        android:layout_width="592dp"
                        android:layout_height="120dp"
                        android:background="@drawable/shape_bg_white">

                        <Switch
                            android:id="@+id/sw_parking_brake"
                            android:layout_width="64dp"
                            android:layout_height="37.33dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:background="@color/transparent"
                            android:checked="false"
                            android:switchMinWidth="64dp"
                            android:thumb="@drawable/thumb"
                            android:track="@drawable/track" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_driving_car_pedal_control_3"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />
                    </LinearLayout>

                    <View
                        android:id="@+id/vw_parking_brake"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@android:color/transparent"
                        android:clickable="true"
                        android:elevation="10dp"
                        android:focusable="true"
                        android:visibility="gone" />
                </FrameLayout>
            </LinearLayout>
            <!--场景辅助-->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="39.33dp"
                android:text="@string/str_driving_car_scene_assist"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="21.33dp"
                android:orientation="horizontal">

                <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/ll_steep_slope_descent"
                        android:layout_width="592dp"
                        android:layout_height="120dp"
                        android:background="@drawable/shape_bg_white">

                        <Switch
                            android:id="@+id/sw_steep_slope_descent"
                            android:layout_width="64dp"
                            android:layout_height="37.33dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:background="@color/transparent"
                            android:switchMinWidth="64dp"
                            android:thumb="@drawable/thumb"
                            android:track="@drawable/track" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_driving_car_scene_assist_2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />
                    </LinearLayout>

                    <View
                        android:id="@+id/vw_hdc"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@android:color/transparent"
                        android:clickable="true"
                        android:elevation="10dp"
                        android:focusable="true"
                        android:visibility="gone" />
                </FrameLayout>
                <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    android:layout_width="wrap_content"
                    android:layout_marginLeft="32dp"
                    android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/ll_intelligent_suspension_preview"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_intelligent_suspension_preview"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="120dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_driving_car_scene_assist_3"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />
                    </RelativeLayout>
                </LinearLayout>
                <View
                    android:id="@+id/vw_intelligent_suspension_preview"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@android:color/transparent"
                    android:clickable="true"
                    android:elevation="10dp"
                    android:focusable="true"
                    android:visibility="gone" />
            </FrameLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="23.33dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ll_traction_mode"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_traction_mode"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="120dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_driving_car_scene_assist_4"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_traction_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />
                    </RelativeLayout>
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/rl_lock_tips"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_driving_car_scene_assist_5"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="84dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="23.33dp"
                android:orientation="horizontal">

                <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/ll_body_stability_control"
                        android:layout_width="592dp"
                        android:layout_height="120dp"
                        android:background="@drawable/shape_bg_white">

                        <Switch
                            android:id="@+id/sw_body_stability_control"
                            android:layout_width="64dp"
                            android:layout_height="37.33dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="32dp"
                            android:background="@color/transparent"
                            android:switchMinWidth="64dp"
                            android:thumb="@drawable/thumb"
                            android:track="@drawable/track" />

                        <RelativeLayout
                            android:layout_width="592dp"
                            android:layout_height="120dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginLeft="32dp"
                                android:text="@string/str_driving_car_scene_assist_1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/font_36px" />
                        </RelativeLayout>
                    </LinearLayout>

                    <View
                        android:id="@+id/vw_body_stability_control"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@android:color/transparent"
                        android:clickable="true"
                        android:elevation="10dp"
                        android:focusable="true"
                        android:visibility="gone" />
                </FrameLayout>

                <RelativeLayout
                    android:id="@+id/rlWashCar"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="32dp"
                            android:text="@string/tv_wash_car_title"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="32dp"
                            android:text="@string/tv_wash_car_title_content"
                            android:textColor="@color/color_transparent_40"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="84dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="29.33dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </com.bitech.vehiclesettings.view.common.BounceScrollView>
</layout>
