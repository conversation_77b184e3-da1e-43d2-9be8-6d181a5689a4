<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="752dp"
    android:layout_height="810.67dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="33.33dp"
            android:fontFamily="DreamHanSansCN"
            android:text="@string/str_system_date_time_settings"
            android:textColor="@color/selector_text_color"
            android:textSize="@dimen/font_48px" />

        <TextView
            android:id="@+id/tvTimeDisplay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="90dp"
            android:layout_marginTop="32dp"
            android:fontFamily="DreamHanSansCN"
            android:text="@string/str_system_time_display"
            android:textColor="@color/selector_text_color"
            android:textSize="@dimen/font_32px" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="80dp">
            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/spvTimeDisplay"
                android:layout_width="624dp"
                android:layout_height="64dp"
                android:layout_marginTop="16dp"
                app:marginSize="0dp" />

            <RelativeLayout
                android:layout_width="624dp"
                android:layout_height="106.67dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/swAutoCalibration"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="128dp"
                    android:text="@string/str_system_auto_calibration"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rlManuallyCalibrate"
                android:layout_width="624dp"
                android:layout_height="106.67dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:id="@+id/tvManuallyCalibrate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_system_manually_calibrate"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="64dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>



        <!--  <RelativeLayout
              android:id="@+id/rlTimeZone"
              android:layout_width="581.33dp"
              android:layout_height="106.67dp"
              android:layout_marginStart="85.33dp"
              android:layout_marginTop="16dp"
              android:background="@drawable/shape_bg_white">

              <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:layout_centerVertical="true"
                  android:layout_marginLeft="32dp"
                  android:text="@string/str_system_zone_setting"
                  android:textColor="@color/black"
                  android:textSize="@dimen/font_36px" />

              <ImageView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:layout_alignParentRight="true"
                  android:layout_marginTop="64dp"
                  android:layout_marginRight="10.67dp"
                  android:src="@mipmap/ic_small_marker" />
          </RelativeLayout>-->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45.33dp" />
    </LinearLayout>
</LinearLayout>
