<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="1328dp"
    android:layout_height="802.67dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <!-- 标题 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="101.33dp">

        <TextView
            android:layout_width="1104dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="85.33dp"
            android:gravity="center_horizontal"
            android:text="@string/str_wrapper"
            android:textColor="@color/black"
            android:textSize="@dimen/font_48px" />

        <ImageView
            android:id="@+id/iv_wallpaper_detail"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:layout_gravity="center"
            android:layout_marginStart="13.33dp"
            android:layout_marginEnd="40dp"
            android:src="@mipmap/ic_small_tip" />
    </LinearLayout>

    <!-- 壁纸模式 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:text="@string/str_wrapper_model"
        android:textColor="@color/color_wallpaper_gray_text"
        android:textSize="@dimen/font_32px" />

    <com.bitech.vehiclesettings.view.common.SegmentedPickerView
        android:id="@+id/spvWallpaper"
        android:layout_width="1168dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="33.33dp"
        android:background="@drawable/shape_bg_white" />

    <ScrollView
        android:id="@+id/sv_view"
        style="@style/scroll_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:orientation="vertical">


            <!-- 壁纸推荐 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="80dp"
                    android:text="@string/str_wrapper_recommend"
                    android:textColor="@color/color_wallpaper_gray_text"
                    android:textSize="@dimen/font_32px" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvStaticEdit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="80dp"
                    android:gravity="end"
                    android:text="@string/str_edit"
                    android:textColor="@color/blue"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
            <!-- 场景化壁纸 -->
            <FrameLayout
                android:id="@+id/fm_scene"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvScene"
                        android:layout_width="1200dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="80dp"
                        android:layout_marginTop="14.67dp" />
                </LinearLayout>
            </FrameLayout>
            <!-- 静态壁纸  -->
            <FrameLayout
                android:id="@+id/fmStatic"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvStatic"
                        android:layout_width="1200dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="80dp"
                        android:layout_marginTop="14.67dp"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never" />

                    <!-- 展开 -->
                    <LinearLayout
                        android:id="@+id/ll_wp_more"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="80dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_wp_more"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_wrapper_more"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_34px" />

                        <ImageView
                            android:id="@+id/iv_wp_more"
                            android:layout_width="21.33dp"
                            android:layout_height="21.33dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="13.33dp"
                            android:src="@mipmap/ic_expand" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="80dp"
                        android:layout_marginTop="47.33dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_wrapper_my"
                            android:textColor="@color/color_wallpaper_gray_text"
                            android:textSize="@dimen/font_36px" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tvCustomEdit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="80dp"
                            android:gravity="end"
                            android:text="@string/str_edit"
                            android:textColor="@color/blue"
                            android:textSize="@dimen/font_36px" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvCustom"
                        android:layout_width="1200dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="80dp"
                        android:layout_marginTop="14.67dp"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never" />
                </LinearLayout>

            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="29.33dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>