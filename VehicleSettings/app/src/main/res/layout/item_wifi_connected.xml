<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="624dp"
    android:layout_height="107dp"
    android:background="@drawable/shape_bg_blue">
    
    <TextView
        android:id="@+id/wifi_name_tv"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_24"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textSize="@dimen/sp_24"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@id/wifi_connected_state_tv"
        app:layout_constraintEnd_toStartOf="@id/wifi_delete_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    
    <TextView
        android:id="@+id/wifi_connected_state_tv"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:text="@string/wifi_connected_tips"
        android:textSize="19sp"
        android:textColor="@color/color_white_transparent_50"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/wifi_name_tv"
        app:layout_constraintStart_toStartOf="@id/wifi_name_tv"
        app:layout_constraintTop_toBottomOf="@id/wifi_name_tv" />
    
    <ImageView
        android:id="@+id/wifi_delete_iv"
        style="@style/settings_icon_48x48_style"
        android:layout_marginEnd="@dimen/dp_36"
        android:src="@drawable/icon_list_del_n_48"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    
    <ProgressBar
        android:id="@+id/wifi_loading_pb"
        style="@style/settings_icon_48x48_style"
        android:layout_marginEnd="@dimen/dp_36"
        android:indeterminate="true"
        android:indeterminateDrawable="@drawable/progress_bar_wifi_scan_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
