<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="380dp"
    android:minWidth="752dp"
    android:minHeight="530.67dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="101.34dp"
        android:layout_gravity="center"
        android:gravity="center">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/str_system_device_name"
            android:textColor="@color/black"
            android:textSize="@dimen/font_42px" />
    </LinearLayout>

<!--    设备信息-->
    <FrameLayout
        android:layout_width="624dp"
        android:layout_height="80dp"
        android:layout_gravity="center"
        android:background="@drawable/shape_bg_black_8">
        <!-- 覆盖在 EditText 上的清除按钮 -->
        <EditText
            android:id="@+id/dialog_device_info_et"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:paddingLeft="32dp"
            android:paddingRight="60dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <ImageView
            android:id="@+id/iv_clear"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="32dp"
            android:src="@mipmap/ic_s_reset_cancel"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:visibility="gone"
            android:clickable="true"
            android:focusable="true"/>

    </FrameLayout>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="94.66dp"
        android:text="@string/str_system_device_info_tip"
        android:textColor="@color/black_transparent_60"
        android:textSize="@dimen/font_32px"
        />
    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:layout_marginTop="50.66dp">

        <Button
            android:id="@+id/btn_confirm"
            style="?android:borderlessButtonStyle"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:background="@drawable/button_blue"
            android:text="@string/str_confirm2"
            android:textColor="@color/white"
            android:textSize="@dimen/font_36px" />

        <Button
            android:id="@+id/btn_cancel"
            style="?android:borderlessButtonStyle"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_marginStart="32dp"
            android:background="@drawable/button_white"
            android:text="@string/str_cancel"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
    </LinearLayout>
</LinearLayout>