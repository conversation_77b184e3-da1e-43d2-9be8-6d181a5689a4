<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="752dp"
    android:layout_height="810.67dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_Language_settings"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/svpLanguage"
                android:layout_width="624dp"
                android:layout_height="64dp"
                app:marginSize="0dp"
                android:layout_marginTop="53.33dp"
                android:layout_gravity="center_horizontal" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="45.33dp" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>
