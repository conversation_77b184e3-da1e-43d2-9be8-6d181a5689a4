<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/px_1176"
    android:layout_height="@dimen/px_792"
    android:background="@drawable/border_bg_dialog">

    <!-- 标题和信息图标 -->
    <RelativeLayout
        android:layout_width="@dimen/px_936"
        android:layout_height="@dimen/px_152"
        android:layout_marginLeft="@dimen/px_120">

        <TextView
            android:id="@+id/tv_title2"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_54"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/tv_wash_car_title"
            android:textColor="@color/black"
            android:textSize="42px"/>

        <ImageView
            android:id="@+id/iv_info"
            android:layout_width="@dimen/px_50"
            android:layout_height="@dimen/px_50"
            android:background="@drawable/icon_tipps"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"/>
    </RelativeLayout>

    <!-- 常规洗车选项 -->
    <LinearLayout
        android:id="@+id/ll_regular_car_wash"
        android:layout_width="@dimen/px_936"
        android:layout_height="@dimen/px_180"
        android:background="@drawable/shape_bg_white"
        android:padding="@dimen/px_45"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/px_152"
        android:layout_marginStart="@dimen/px_120">
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_regular_car_wash"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/regular_car_wash"
                android:textColor="@color/black"
                android:textSize="36px"/>

            <TextView
                android:id="@+id/tv_regular_car_wash_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/regular_car_wash_desc"
                android:textColor="@color/color_transparent_60"
                android:textSize="28px"/>
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_check_regular"
            android:layout_width="@dimen/px_96"
            android:layout_height="@dimen/px_96"
            android:background="@drawable/icon_set_choose"
            android:visibility="visible"/>
    </LinearLayout>

    <!-- 传送带洗车选项 -->
    <LinearLayout
        android:id="@+id/ll_conveyor_car_wash"
        android:layout_width="@dimen/px_936"
        android:layout_height="@dimen/px_180"
        android:background="@drawable/shape_bg_white"
        android:padding="@dimen/px_45"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/px_364"
        android:layout_marginStart="@dimen/px_120">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_conveyor_car_wash"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/conveyor_car_wash"
                android:textColor="@color/black"
                android:textSize="36px"/>

            <TextView
                android:id="@+id/tv_conveyor_car_wash_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/conveyor_car_wash_desc"
                android:textColor="@color/color_transparent_60"
                android:textSize="28px"/>
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_check_conveyor"
            android:layout_width="@dimen/px_96"
            android:layout_height="@dimen/px_96"
            android:src="@drawable/icon_set_choose"
            android:visibility="gone"/>
    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="@dimen/px_936"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/px_620"
        android:layout_marginStart="@dimen/px_120">

        <FrameLayout
            android:layout_width="@dimen/px_444"
            android:layout_height="@dimen/px_96">

            <Button
                android:id="@+id/btn_start_car_wash"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/start_car_wash"
                android:textSize="36px"
                android:textColor="@color/white"
                android:background="@drawable/shape_bg_blue" />

            <!-- 覆盖在 Button 上方的 View -->
            <View
                android:id="@+id/btn_confirm_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:foreground="@null" />  <!-- 半透明红色 -->
        </FrameLayout>

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="@dimen/px_444"
            android:layout_height="@dimen/px_96"
            android:layout_marginStart="@dimen/px_48"
            android:text="@string/cancel"
            android:textSize="36px"
            android:textColor="@color/black"
            android:background="@drawable/selector_bg_cancel"/>
    </LinearLayout>

</FrameLayout>