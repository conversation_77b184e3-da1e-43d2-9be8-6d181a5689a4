<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="tabSelectedIndex"
            type="Integer" />

        <import type="com.bitech.vehiclesettings.activity.MainActivity.MainTabIndex" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent"
        android:paddingBottom="85.33dp">

        <!--        <ImageView-->
        <!--            android:id="@+id/iv_model"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="match_parent" /> -->
        <com.bitech.vehiclesettings.view.common.FadeBackgroundImageView
            android:id="@+id/iv_model"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="invisible"
            app:fadeEnabled="true"
            app:fadeThreshold="200dp"
            app:initialAlpha="1.0" />

        <View
            android:id="@+id/maskView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent" />

        <com.bitech.vehiclesettings.view.common.HackyViewPager
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:paddingTop="80dp"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="85.33dp"
            android:layout_marginTop="104.67dp"
            android:paddingBottom="24.67dp"
            android:text="@string/str_setting"
            android:textColor="@color/black"
            android:textSize="@dimen/font_42px"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.bitech.vehiclesettings.view.common.BounceScrollView
            android:id="@+id/scrollView"
            android:layout_width="426.66dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="26.67dp"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:bgAnimate="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_setting"
            app:scrollThreshold="@dimen/quick_control_model_height">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/dp_32"
                android:orientation="vertical"
                android:paddingBottom="220dp">

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp">

                    <TextView
                        android:id="@+id/tv_quick_control"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.QUICK_CONTROL}"
                        android:text="@string/m_car_control"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_quick_control"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp"
                    android:layout_marginTop="@dimen/dp_16">

                    <TextView
                        android:id="@+id/tv_light"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.LIGHT}"
                        android:text="@string/m_light"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_light"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp"
                    android:layout_marginTop="@dimen/dp_16">

                    <TextView
                        android:id="@+id/tv_new_energy"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.NEW_ENERGY}"
                        android:text="@string/m_new_energy"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_new_energy"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp"
                    android:layout_marginTop="@dimen/dp_16">

                    <TextView
                        android:id="@+id/tv_drive"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.DRIVE}"
                        android:text="@string/m_drive"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_drive"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp"
                    android:layout_marginTop="@dimen/dp_16">

                    <TextView
                        android:id="@+id/tv_condition"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.CONDITION}"
                        android:text="@string/m_condition"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_condition"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp"
                    android:layout_marginTop="@dimen/dp_16">

                    <TextView
                        android:id="@+id/tv_display"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.DISPLAY}"
                        android:text="@string/m_display"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_display"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp"
                    android:layout_marginTop="@dimen/dp_16">

                    <TextView
                        android:id="@+id/tv_connect"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.CONNECT}"
                        android:text="@string/m_connect"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_connect"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp"
                    android:layout_marginTop="@dimen/dp_16">

                    <TextView
                        android:id="@+id/tv_voice"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.VOICE}"
                        android:text="@string/m_voice"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_voice"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>

                <!--                <FrameLayout-->
                <!--                    android:visibility="gone"-->
                <!--                    android:layout_width="362.66dp"-->
                <!--                    android:layout_height="90.66dp"-->
                <!--                    android:layout_marginTop="@dimen/dp_16">-->

                <!--                    <TextView-->
                <!--                        android:id="@+id/tv_smart_recognition"-->
                <!--                        android:layout_width="match_parent"-->
                <!--                        android:layout_height="match_parent"-->
                <!--                        android:background="@drawable/selector_menu"-->
                <!--                        android:gravity="center_vertical"-->
                <!--                        android:paddingStart="109.33dp"-->
                <!--                        android:selected="@{tabSelectedIndex == MainTabIndex.SMART_RECOGNITION}"-->
                <!--                        android:text="@string/m_smart_recognition"-->
                <!--                        android:textColor="@color/selector_function_tab"-->
                <!--                        android:textSize="@dimen/font_36px" />-->

                <!--                    <org.libpag.PAGView-->
                <!--                        android:id="@+id/pv_smart_recognition"-->
                <!--                        android:layout_width="32dp"-->
                <!--                        android:layout_height="32dp"-->
                <!--                        android:layout_gravity="center_vertical"-->
                <!--                        android:layout_marginStart="53.33dp" />-->
                <!--                </FrameLayout>-->

                <FrameLayout
                    android:layout_width="362.66dp"
                    android:layout_height="90.66dp"
                    android:layout_marginTop="@dimen/dp_16">

                    <TextView
                        android:id="@+id/tv_system"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_menu"
                        android:gravity="center_vertical"
                        android:paddingStart="109.33dp"
                        android:selected="@{tabSelectedIndex == MainTabIndex.SYSTEM}"
                        android:text="@string/m_system"
                        android:textColor="@color/selector_function_tab"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_system"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="53.33dp" />
                </FrameLayout>
            </LinearLayout>
        </com.bitech.vehiclesettings.view.common.BounceScrollView>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>