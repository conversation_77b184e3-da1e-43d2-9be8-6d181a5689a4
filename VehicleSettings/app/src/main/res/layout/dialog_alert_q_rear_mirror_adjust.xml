<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="33.33dp"
        android:fontFamily="DreamHanSansCN"
        android:text="@string/str_rear_mirror_ad_title"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:layout_width="581.33dp"
        android:layout_height="77.33dp"
        android:layout_marginTop="86.67dp"
        android:background="@drawable/shape_bg_white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <View
            android:id="@+id/view_rear_mirror_adjust"
            android:layout_width="138.66dp"
            android:layout_height="match_parent"
            android:layout_margin="5.33dp"
            android:background="@drawable/shape_bg_open_n" />

        <LinearLayout
            android:id="@+id/ll_rear_mirror_adjust"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_rear_mirror_adjust_1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="5.33dp"
                android:layout_weight="1"
                android:background="@drawable/selector_bg_blue_type"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_rear_mirror_ad_1"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:id="@+id/tv_rear_mirror_adjust_2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="5.33dp"
                android:layout_weight="1"
                android:background="@drawable/selector_bg_blue_type"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_rear_mirror_ad_2"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:id="@+id/tv_rear_mirror_adjust_3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="5.33dp"
                android:layout_weight="1"
                android:background="@drawable/selector_bg_blue_type"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_rear_mirror_ad_3"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:id="@+id/tv_rear_mirror_adjust_4"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="5.33dp"
                android:layout_weight="1"
                android:background="@drawable/selector_bg_blue_type"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_rear_mirror_ad_4"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_36px" />

        </LinearLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>