<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_624"
    android:layout_height="@dimen/dp_107"
    android:layout_marginBottom="@dimen/dp_16"
    android:background="@drawable/shape_bg_white">

    <TextView
        android:id="@+id/settings_no_scan_wifi_tv"
        style="@style/settings_text_36_regular_17191e_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="@dimen/maxLines_1"
        android:text="@string/wifi_not_found_can_use_wife"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
