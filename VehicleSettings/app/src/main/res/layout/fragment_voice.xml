<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    style="@style/scroll_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintTop_toBottomOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_light"
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_left"
        android:orientation="vertical"
        android:paddingTop="14dp"
        android:paddingBottom="48.33dp">

        <FrameLayout
            android:id="@+id/fl_sound_effect"
            android:layout_width="592dp"
            android:layout_height="256dp"
            android:background="@drawable/shape_bg_white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="centerCrop"
                android:src="@mipmap/ic_voice_adj" />

            <LinearLayout
                android:layout_width="190.67dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="32dp"
                    android:layout_marginTop="32dp"
                    android:text="@string/str_sound_effect_adjustment"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tvEffect"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="32dp"
                    android:layout_marginTop="8dp"
                    android:text="@string/str_sound_effect_item1"
                    android:textColor="@color/black_transparent_30"
                    android:textSize="@dimen/font_28px" />
            </LinearLayout>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="556dp"
                android:layout_marginTop="220dp"
                android:src="@mipmap/ic_small_marker" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_headrest_speaker"
            android:layout_width="592dp"
            android:layout_height="256dp"
            android:layout_marginStart="@dimen/dp_32"
            android:background="@drawable/shape_bg_white"
            app:layout_constraintStart_toEndOf="@id/fl_sound_effect"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="182.67dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/ic_voice_headrest" />

            <LinearLayout
                android:layout_width="190.67dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="32dp"
                    android:layout_marginTop="32dp"
                    android:text="@string/str_headrest_speaker"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tvHeadrest"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="32dp"
                    android:layout_marginTop="8dp"
                    android:text="@string/str_headrest_jx"
                    android:textColor="@color/black_transparent_30"
                    android:textSize="@dimen/font_28px" />
            </LinearLayout>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="556dp"
                android:layout_marginTop="220dp"
                android:src="@mipmap/ic_small_marker" />
        </FrameLayout>

        <!--音量-->
        <TextView
            android:id="@+id/tv_volume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_sound_adjust_title"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/fl_sound_effect" />
        <!--导航-->
        <LinearLayout
            android:id="@+id/ll_navi"
            android:layout_width="592dp"
            android:layout_height="120dp"
            android:layout_marginTop="24.67dp"
            android:background="@drawable/shape_bg_white"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_volume">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="15.33dp"
                android:text="@string/str_navigation"
                android:textColor="@color/black"
                android:textSize="@dimen/font_34px" />

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbNavi"
                android:layout_width="528dp"
                android:layout_height="40dp"
                android:layout_marginStart="33.33dp"
                android:layout_marginTop="15.33dp"
                app:iconSrc="@mipmap/ic_sound_navigation"
                app:max="10"
                app:progress="0"
                app:seekBarWidth="528dp" />

        </LinearLayout>
        <!--语音-->
        <LinearLayout
            android:layout_width="592dp"
            android:layout_height="120dp"
            android:layout_marginStart="32dp"
            android:layout_marginTop="24.67dp"
            android:background="@drawable/shape_bg_white"
            android:orientation="vertical"
            app:layout_constraintStart_toEndOf="@id/ll_navi"
            app:layout_constraintTop_toBottomOf="@id/tv_volume">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="15.33dp"
                android:text="@string/str_voice"
                android:textColor="@color/black"
                android:textSize="@dimen/font_34px" />

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbVR"
                android:layout_width="528dp"
                android:layout_height="40dp"
                android:layout_marginStart="33.33dp"
                android:layout_marginTop="15.33dp"
                app:iconSrc="@mipmap/ic_sound_voice"
                app:max="10"
                app:progress="0"
                app:seekBarWidth="528dp" />
        </LinearLayout>
        <!--媒体-->
        <LinearLayout
            android:id="@+id/ll_media"
            android:layout_width="592dp"
            android:layout_height="120dp"
            android:layout_marginTop="24.67dp"
            android:background="@drawable/shape_bg_white"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_navi">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="15.33dp"
                android:text="@string/str_media"
                android:textColor="@color/black"
                android:textSize="@dimen/font_34px" />

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbMedia"
                android:layout_width="528dp"
                android:layout_height="40dp"
                android:layout_marginStart="33.33dp"
                android:layout_marginTop="15.33dp"
                app:iconSrc="@mipmap/ic_sound_media"
                app:max="31"
                app:progress="0"
                app:seekBarWidth="528dp" />

        </LinearLayout>
        <!--电话-->
        <LinearLayout
            android:id="@+id/ll_phone"
            android:layout_width="592dp"
            android:layout_height="120dp"
            android:layout_marginStart="32dp"
            android:layout_marginTop="24.67dp"
            android:background="@drawable/shape_bg_white"
            android:orientation="vertical"
            app:layout_constraintStart_toEndOf="@id/ll_media"
            app:layout_constraintTop_toBottomOf="@id/ll_navi">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="15.33dp"
                android:text="@string/str_phone"
                android:textColor="@color/black"
                android:textSize="@dimen/font_34px" />

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbPhone"
                android:layout_width="528dp"
                android:layout_height="40dp"
                android:layout_marginStart="33.33dp"
                android:layout_marginTop="15.33dp"
                app:iconSrc="@mipmap/ic_sound_phone"
                app:max="25"
                app:progress="0"
                app:seekBarWidth="528dp" />

        </LinearLayout>

        <!--报警-->
        <LinearLayout
            android:id="@+id/ll_alarm"
            android:layout_width="592dp"
            android:layout_height="120dp"
            android:layout_marginTop="24.67dp"
            android:background="@drawable/shape_bg_white"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_media">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="15.33dp"
                android:text="@string/str_alarm_sound"
                android:textColor="@color/black"
                android:textSize="@dimen/font_34px" />

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbAlarm"
                android:layout_width="528dp"
                android:layout_height="40dp"
                android:layout_marginStart="33.33dp"
                android:layout_marginTop="15.33dp"
                app:iconSrc="@mipmap/ic_sound_alarm"
                app:max="10"
                app:progress="0"
                app:seekBarWidth="528dp" />

            <!--            <FrameLayout-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginStart="33.33dp"-->
            <!--                android:layout_marginTop="15.33dp">-->


            <!--                <SeekBar-->
            <!--                    android:layout_width="528dp"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:max="10"-->
            <!--                    android:maxHeight="40dp"-->
            <!--                    android:minHeight="40dp"-->
            <!--                    android:padding="0dp"-->
            <!--                    android:paddingStart="0dp"-->
            <!--                    android:paddingEnd="0dp"-->
            <!--                    android:progress="0"-->
            <!--                    android:progressDrawable="@drawable/progress_blue_white"-->
            <!--                    android:thumb="@null"-->
            <!--                    android:thumbOffset="0dp" />-->
            <!--            </FrameLayout>-->
        </LinearLayout>
        <!-- 展开 -->

        <LinearLayout
            android:id="@+id/ll_sd_more"
            android:layout_width="592dp"
            android:layout_height="53.33dp"
            android:layout_marginTop="32dp"
            android:background="@drawable/shape_bg_voice"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_alarm">

            <TextView
                android:id="@+id/tv_sd_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="253.33dp"
                android:layout_marginTop="12.67dp"
                android:text="@string/str_wrapper_less"
                android:textColor="@color/black"
                android:textSize="@dimen/font_32px" />

            <ImageView
                android:id="@+id/ivSdMore"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginTop="15dp"
                android:layout_marginStart="10.67dp"
                android:src="@mipmap/ic_retract" />
        </LinearLayout>

        <!--提示音-->
        <TextView
            android:id="@+id/tv_prompt_sound"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_sound_tip"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_sd_more" />
        <!--来电播报、车外低速模拟音-->
        <LinearLayout
            android:id="@+id/ll_call"
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_prompt_sound">
            <!--来电播报-->
            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/swCallBroadcast"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_call_broadcast"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>
            <!--车外低速模拟音-->
            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginStart="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/swLowSpeedAnalog"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_low_speed_analog"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="352dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_low_speed_analog_desc"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <!--更多-->
        <TextView
            android:id="@+id/tv_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_more_title"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_call" />
        <!--随速补偿、报警音类型-->
        <LinearLayout
            android:id="@+id/ll_voice_compensation"
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_more">
            <!--随速补偿-->
            <RelativeLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <RelativeLayout
                    android:id="@+id/rl_voice_compensation"
                    android:layout_width="490dp"
                    android:layout_height="match_parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_voice_compensation"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_voice_compensation_desc"
                    android:layout_width="100dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true">

                    <ImageView
                        android:id="@+id/iv_voice_compensation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="32dp"
                        android:src="@mipmap/ic_small_tip" />

                    <ImageView
                        android:id="@+id/ivVoiceCompensationDesc"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_marginTop="80.67dp"
                        android:layout_marginEnd="9.33dp"
                        android:layout_marginBottom="9.33dp"
                        android:src="@mipmap/ic_small_marker" />
                </RelativeLayout>


            </RelativeLayout>
            <!--报警音类型-->
            <RelativeLayout
                android:id="@+id/rl_sound_alarm_type"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_alarm_sound_type"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="80.67dp"
                    android:layout_marginEnd="9.33dp"
                    android:layout_marginBottom="9.33dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <!--外放模式、导航时压低媒体音-->
        <LinearLayout
            android:id="@+id/ll_voice_external_mode"
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="21.33dp"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_voice_compensation">
            <!--外放模式-->
            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/swVoiceExternalMode"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_voice_external_mode"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_voice_external_mode_desc"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>
            </LinearLayout>
            <!--导航时压低媒体音-->
            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/swVoiceLowerMediaTone"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_voice_lower_media_tone"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="352dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_voice_lower_media_tone_desc"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_button_sound"
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="21.33dp"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_voice_external_mode">

            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/swButtonSound"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_button_sound"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>
            <!--恢复默认值-->
            <RelativeLayout
                android:id="@+id/rbReset"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginStart="32dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_recover_default"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="80.67dp"
                    android:layout_marginEnd="9.33dp"
                    android:layout_marginBottom="9.33dp"
                    android:src="@mipmap/ic_small_marker" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>
