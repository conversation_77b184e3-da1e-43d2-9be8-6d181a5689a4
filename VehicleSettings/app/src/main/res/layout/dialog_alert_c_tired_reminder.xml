<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="101.33dp"
            android:layout_gravity="center"
            android:gravity="center">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_driving_car_scene_assist_5"
                android:textColor="@color/black"
                android:textSize="@dimen/font_48px" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:background="@drawable/shape_bg_white">

            <!--            <Switch
                android:id="@+id/sw_driver_state"
                android:layout_width="64dp"
                android:layout_height="37.33dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="32dp"
                android:background="@color/transparent"
                android:checked="false"
                android:switchMinWidth="64dp"
                android:thumb="@drawable/thumb"
                android:track="@drawable/track" />-->

            <com.bitech.vehiclesettings.view.common.NoToggleSwitch
                android:id="@+id/sw_driver_state"
                android:layout_width="64dp"
                android:layout_height="37.33dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="32dp"
                android:background="@color/transparent"
                android:switchMinWidth="64dp"
                android:thumb="@drawable/thumb"
                app:track="@drawable/track" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="32dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_driving_driver_state"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp">

            <TextView
                android:id="@+id/tv_agree_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="6dp"

                android:background="@drawable/selector_bg_blue_type"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"

                android:text="@string/str_driving_tips"

                android:textColor="@color/color_dialog_content"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:id="@+id/tv_agree_link"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:autoLink="all"
                android:background="@drawable/selector_bg_blue_type"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_driving_tips_link"
                android:textColor="?attr/appPrimaryColor"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">
            <!--疲劳检测-->
            <LinearLayout
                android:id="@+id/ll_fatigue_detection"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_fatigue_detection"
                    android:layout_width="64dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <RelativeLayout
                    android:layout_width="341.33dp"
                    android:layout_height="120dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_recognition_safe_1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:id="@+id/iv_fatigue_detection_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="33.33dp"
                        android:src="@mipmap/ic_small_tip" />

                </RelativeLayout>
            </LinearLayout>
            <!--视线分心提醒-->
            <LinearLayout
                android:id="@+id/ll_distraction"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:layout_marginLeft="21.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_distraction"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <RelativeLayout
                    android:layout_width="341.33dp"
                    android:layout_height="120dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_recognition_safe_2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:id="@+id/iv_distraction_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="33.33dp"
                        android:src="@mipmap/ic_small_tip" />

                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
        <!--主驾打电话提醒-->
        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginTop="21.33dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_call_reminder"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_call"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <RelativeLayout
                    android:layout_width="341.33dp"
                    android:layout_height="120dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_recognition_safe_3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:id="@+id/iv_call_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="33.33dp"
                        android:src="@mipmap/ic_small_tip" />

                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>