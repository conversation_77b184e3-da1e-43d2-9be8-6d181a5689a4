<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="365.33dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="90.67dp"
            android:layout_marginHorizontal="80dp"
            android:text="@string/str_voice_reset"
            android:gravity="start"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="98.67dp">

            <Button
                android:id="@+id/btn_confirm"
                style="?android:borderlessButtonStyle"
                android:layout_width="296dp"
                android:layout_height="64dp"
                android:background="@drawable/selector_bg_red"
                android:text="@string/str_display_confirm_reset"
                android:textColor="@color/white"
                android:textSize="@dimen/font_36px" />

            <Button
                android:id="@+id/btn_cancel"
                style="?android:borderlessButtonStyle"
                android:layout_width="296dp"
                android:layout_height="64dp"
                android:layout_marginStart="50.67dp"
                android:background="@drawable/button_white"
                android:text="@string/str_cancel"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="44dp" />
</LinearLayout>