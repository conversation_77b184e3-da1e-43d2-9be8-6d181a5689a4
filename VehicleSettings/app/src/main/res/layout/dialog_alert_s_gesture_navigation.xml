<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="1274.67dp"
    android:layout_height="780dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="35.33dp"
        android:text="@string/str_gesture_navigation"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginTop="32dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">
        <!--left-->
        <ScrollView
            android:id="@+id/scrollView"
            style="@style/scroll_bar"
            android:layout_width="match_parent"
            android:layout_height="501.33dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llBack"
                    android:layout_width="554.67dp"
                    android:layout_height="106.67dp"
                    android:background="@drawable/selector_bg_blue">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvBack1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/font_48px"
                            android:text="@string/str_gesture_back"
                            android:textColor="@drawable/text_gesture_navigation"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:id="@+id/tvBack2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/font_48px"
                            android:layout_marginTop="5.33dp"
                            android:text="@string/str_gesture_back_desc"
                            android:textColor="@drawable/text_gesture_navigation_2"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llControl"
                    android:layout_width="554.67dp"
                    android:layout_height="106.67dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvControl1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/font_48px"
                            android:text="@string/str_gesture_control"
                            android:textColor="@drawable/text_gesture_navigation"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:id="@+id/tvControl2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/font_48px"
                            android:layout_marginTop="5.33dp"
                            android:text="@string/str_gesture_control_desc"
                            android:textColor="@drawable/text_gesture_navigation_2"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llQuick"
                    android:layout_width="554.67dp"
                    android:layout_height="106.67dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvQuick1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/font_48px"
                            android:text="@string/str_gesture_quick"
                            android:textColor="@drawable/text_gesture_navigation"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:id="@+id/tvQuick2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/font_48px"
                            android:layout_marginTop="5.33dp"
                            android:text="@string/str_gesture_quick_desc"
                            android:textColor="@drawable/text_gesture_navigation_2"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llScroll"
                    android:layout_width="554.67dp"
                    android:layout_height="106.67dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvScroll1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/font_48px"
                            android:text="@string/str_gesture_scroll"
                            android:textColor="@drawable/text_gesture_navigation"
                            android:textSize="@dimen/font_36px" />

                        <TextView
                            android:id="@+id/tvScroll2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/font_48px"
                            android:layout_marginTop="5.33dp"
                            android:text="@string/str_gesture_scroll_desc"
                            android:textColor="@drawable/text_gesture_navigation_2"
                            android:textSize="@dimen/font_28px" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>


        </ScrollView>

        <VideoView
            android:id="@+id/vv"
            android:layout_width="581.33dp"
            android:layout_height="490.67dp"
            android:layout_marginStart="586.67dp"/>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>