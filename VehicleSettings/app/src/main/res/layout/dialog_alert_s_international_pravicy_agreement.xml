<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="50dp"
            android:paddingEnd="50dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_system_international_privacy_statement_title"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <TextView
                android:id="@+id/tv_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:textColor="@color/black"
                android:textSize="@dimen/font_34px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_marginTop="30.33dp"
                android:layout_height="wrap_content">

                <com.bitech.vehiclesettings.view.common.NoToggleSwitch
                    android:id="@+id/sw_activity_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="DreamHanSansCN"
                    android:thumb="@drawable/thumb"
                    app:track="@drawable/track"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:text="@string/str_system_international_privacy_statement_activity_data"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_32px"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_marginTop="30.33dp"
                android:layout_height="wrap_content">

                <com.bitech.vehiclesettings.view.common.NoToggleSwitch
                    android:id="@+id/sw_setting_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="DreamHanSansCN"
                    android:thumb="@drawable/thumb"
                    app:track="@drawable/track"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:text="@string/str_system_international_privacy_statement_setting_data"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_32px"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_marginTop="30.33dp"
                android:layout_height="wrap_content">

                <com.bitech.vehiclesettings.view.common.NoToggleSwitch
                    android:id="@+id/sw_browse_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="DreamHanSansCN"
                    android:thumb="@drawable/thumb"
                    app:track="@drawable/track"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:text="@string/str_system_international_privacy_statement_browse_data"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_32px"/>

            </LinearLayout>

            <TextView
                android:id="@+id/tv_acknowledge"
                android:layout_width="266.67dp"
                android:layout_height="66.67dp"
                android:layout_marginTop="33.33dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/selector_bg_open"
                android:fontFamily="DreamHanSansCN"
                android:gravity="center"
                android:text="@string/str_system_international_privacy_statement_acknowledge"
                android:textColor="@color/white"
                android:textSize="@dimen/font_36px" />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>