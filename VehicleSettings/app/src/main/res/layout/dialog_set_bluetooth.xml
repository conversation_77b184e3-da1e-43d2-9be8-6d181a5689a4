<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="752dp"
    android:layout_height="533dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/border_bg_dialog">

    <TextView
        android:id="@+id/tv_bluetooth_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="33dp"
        android:text="@string/str_blue"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_32"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_bluetooth_preferences"
        android:layout_width="581dp"
        android:layout_height="107dp"
        android:layout_marginTop="107dp"
        android:background="@drawable/shape_bg_white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <Switch
            android:id="@+id/setting_bt_preferences_sw"
            style="@style/settings_switch_style"
            android:layout_width="64dp"
            android:layout_height="37.33dp"
            android:layout_marginStart="32dp"
            android:layout_marginTop="35dp"
            android:background="@color/transparent"
            android:checked="false"
            android:switchMinWidth="64dp"
            android:thumb="@drawable/thumb"
            android:track="@drawable/track"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

        <TextView
            android:id="@+id/setting_bt_preferences"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="128dp"
            android:layout_marginTop="22dp"
            android:text="@string/bt_preferences_device"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/setting_bt_preferences_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="128dp"
            android:layout_marginTop="61dp"
            android:ellipsize="end"
            android:gravity="center|right"
            android:maxLength="32"
            android:maxLines="1"
            android:text="@string/bt_preferences_tips"
            android:textColor="@color/black_transparent_40"
            android:textSize="@dimen/sp_19"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_bluetooth_media"
        android:layout_width="581dp"
        android:layout_height="131dp"
        android:layout_marginTop="229dp"
        android:background="@drawable/shape_bg_white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <Switch
            android:id="@+id/setting_bt_media_sw"
            style="@style/settings_switch_style"
            android:layout_width="64dp"
            android:layout_height="37.33dp"
            android:layout_marginStart="32dp"
            android:layout_marginTop="35dp"
            android:background="@color/transparent"
            android:checked="false"
            android:switchMinWidth="64dp"
            android:thumb="@drawable/thumb"
            android:track="@drawable/track"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

        <TextView
            android:id="@+id/setting_bt_media"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="128dp"
            android:layout_marginTop="22dp"
            android:text="@string/bt_media_tv"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/setting_bt_media_tips"
            android:layout_width="361dp"
            android:layout_height="60dp"
            android:layout_marginStart="128dp"
            android:layout_marginTop="61dp"
            android:ellipsize="end"
            android:maxLength="32"
            android:text="@string/bt_media_tips"
            android:textColor="@color/black_transparent_40"
            android:textSize="@dimen/sp_19"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/setting_bt_media_img"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@mipmap/setting_bt_media_img"
            android:layout_marginStart="517dp"
            android:layout_marginTop="50dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <Button
        android:id="@+id/btn_disconnect"
        android:layout_width="267dp"
        android:layout_height="67dp"
        android:background="@drawable/shape_bg_blue"
        android:text="取消配对"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_24"
        android:layout_marginStart="243dp"
        android:layout_marginBottom="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>