<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginBottom="20dp"
    android:background="@drawable/shape_bg_white">

    <!--保养提示-->
    <LinearLayout
        android:id="@+id/rl_maintain_tips"
        android:layout_width="match_parent"
        android:layout_height="103.33dp"
        android:background="@drawable/shape_bg_white">

        <com.bitech.vehiclesettings.view.common.NoToggleSwitch
            android:id="@+id/sw_switch"
            android:layout_width="64dp"
            android:layout_height="37.33dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="32dp"
            android:background="@color/transparent"
            android:checked="true"
            android:switchMinWidth="64dp"
            android:thumb="@drawable/thumb"
            app:track="@drawable/track" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="32dp"
            android:orientation="vertical">
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_carsetting_maintain_2"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_carsetting_maintain_3"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_28px" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>
