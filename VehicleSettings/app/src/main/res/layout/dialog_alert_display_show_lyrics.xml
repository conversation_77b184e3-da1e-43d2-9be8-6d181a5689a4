<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="752dp"
    android:layout_height="305.33dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="35.33dp"
        android:text="@string/str_yb"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="32.67dp"
        android:text="@string/str_yb_warning"
        android:textColor="@color/black_transparent_40"
        android:textSize="@dimen/font_40px" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="52dp">

        <Button
            android:id="@+id/btn_confirm"
            style="?android:borderlessButtonStyle"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:background="@drawable/button_blue"
            android:text="@string/str_confirm2"
            android:textColor="@color/white"
            android:textSize="@dimen/font_36px" />

        <Button
            android:id="@+id/btn_cancel"
            style="?android:borderlessButtonStyle"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:layout_marginStart="50.67dp"
            android:background="@drawable/button_white"
            android:text="@string/str_cancel"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="44dp" />
</LinearLayout>