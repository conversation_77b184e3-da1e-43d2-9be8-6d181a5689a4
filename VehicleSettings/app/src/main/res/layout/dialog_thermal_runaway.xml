<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/warning_outlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/energy_alarm_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/background_layer"
        android:layout_width="@dimen/thermal_runaway_background_layer_width"
        android:layout_height="@dimen/thermal_runaway_background_layer_height"
        android:background="@drawable/bg_layer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/warning_title1"
        android:layout_width="@dimen/thermal_runaway_warning_title1_width"
        android:layout_height="@dimen/thermal_runaway_warning_title1_height"
        android:layout_marginStart="@dimen/thermal_runaway_warning_title1_margin_start"
        android:layout_marginEnd="@dimen/thermal_runaway_warning_title1_margin_end"
        android:gravity="center"
        android:text="@string/warning_title1"
        android:textColor="@color/warning_text"
        android:textSize="@dimen/thermal_runaway_warning_title1_text_size"
        app:layout_constraintEnd_toEndOf="@id/background_layer"
        app:layout_constraintStart_toStartOf="@id/background_layer"
        app:layout_constraintTop_toTopOf="@id/background_layer" />

    <ImageView
        android:id="@+id/car_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@mipmap/energy_alarm_battery"
        app:layout_constraintBottom_toBottomOf="@id/background_layer"
        app:layout_constraintEnd_toEndOf="@id/background_layer"
        app:layout_constraintStart_toStartOf="@id/background_layer"
        app:layout_constraintTop_toTopOf="@id/background_layer" />

    <ImageView
        android:id="@+id/warning_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/energy_alarm"
        android:layout_marginBottom="@dimen/px_50"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/warning_title2"
        android:layout_width="@dimen/thermal_runaway_warning_title2_width"
        android:layout_height="@dimen/thermal_runaway_warning_title2_height"
        android:layout_marginStart="@dimen/thermal_runaway_warning_title2_margin_start"
        android:layout_marginTop="@dimen/thermal_runaway_warning_title2_margin_top"
        android:layout_marginEnd="@dimen/thermal_runaway_warning_title2_margin_end"
        android:layout_marginBottom="@dimen/thermal_runaway_warning_title2_margin_bottom"
        android:gravity="center"
        android:text="@string/warning_title2"
        android:textColor="@color/warning_text"
        android:textSize="@dimen/thermal_runaway_warning_title2_text_size"
        app:layout_constraintBottom_toBottomOf="@id/background_layer"
        app:layout_constraintEnd_toEndOf="@id/background_layer"
        app:layout_constraintStart_toStartOf="@id/background_layer"
        app:layout_constraintTop_toTopOf="@id/background_layer" />

</androidx.constraintlayout.widget.ConstraintLayout>
