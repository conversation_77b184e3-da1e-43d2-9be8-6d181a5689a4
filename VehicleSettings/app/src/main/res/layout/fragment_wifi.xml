<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.widget.ScanScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/settings_wifi_ssv"
    style="@style/settings_scroll_bar_style"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".view.connect.WifiFragment">
    
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp_23">
        
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_wifi_cl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@id/settings_hotspot_cl"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_wifi_switch_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_32"
                android:minHeight="120dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">
                
                <Switch
                    android:id="@+id/settings_wifi_sw"
                    style="@style/settings_switch_style"
                    android:layout_marginStart="@dimen/dp_36"
                    android:layout_marginTop="@dimen/dp_35"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />
                
                <TextView
                    android:id="@+id/settings_wifi_tv"
                    style="@style/settings_switch_tv_style"
                    android:layout_width="550dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_24"
                    android:text="@string/wifi_switcher"
                    app:layout_constraintBottom_toBottomOf="@id/settings_wifi_sw"
                    app:layout_constraintStart_toEndOf="@id/settings_wifi_sw"
                    app:layout_constraintTop_toTopOf="@id/settings_wifi_sw" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
            
            <TextView
                android:id="@+id/settings_wifi_connected_tv"
                style="@style/settings_text_30_regular_747578_style"
                android:layout_width="550dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_36"
                android:layout_marginBottom="@dimen/dp_24"
                android:gravity="center|left"
                android:text="@string/wifi_connected"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/settings_wifi_connected_cl"
                app:layout_constraintStart_toStartOf="parent" />
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_wifi_connected_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="114dp"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/settings_wifi_switch_cl">
                
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/settings_connected_wifi_rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
            
            <TextView
                android:id="@+id/settings_wifi_use_tv"
                style="@style/settings_text_30_regular_747578_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_36"
                android:layout_marginBottom="@dimen/dp_9"
                android:gravity="center|left"
                android:text="@string/wifi_can_use"
                app:layout_constraintBottom_toBottomOf="@id/settings_wifi_start_scan_iv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/settings_wifi_start_scan_iv" />
            
            <ProgressBar
                android:id="@+id/settings_wifi_scan_pb"
                style="@style/settings_icon_48x48_style"
                android:layout_marginStart="@dimen/dp_14"
                android:indeterminate="true"
                android:indeterminateDrawable="@drawable/progress_bar_bt_scan_bg"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/settings_wifi_start_scan_iv"
                app:layout_constraintStart_toEndOf="@id/settings_wifi_use_tv"
                app:layout_constraintTop_toTopOf="@id/settings_wifi_start_scan_iv" />
            
            <ImageView
                android:id="@+id/settings_wifi_start_scan_iv"
                style="@style/settings_icon_48x48_style"
                android:layout_marginEnd="@dimen/dp_36"
                android:layout_marginBottom="@dimen/dp_15"
                android:src="@drawable/icon_list_refresh_48"
                app:layout_constraintBottom_toTopOf="@id/settings_can_use_wifi_cl"
                app:layout_constraintEnd_toEndOf="parent" />
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_can_use_wifi_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="480dp"
                android:layout_marginTop="114dp"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_wifi_connected_cl">
                
                <TextView
                    android:id="@+id/settings_no_scan_wifi_tv"
                    style="@style/settings_text_32_regular_17191e_style"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="@string/wifi_not_found_can_use"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/settings_can_use_wifi_rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_network_notification_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_48"
                android:minHeight="166dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_can_use_wifi_cl">
                
                <Switch
                    android:id="@+id/settings_network_notification_sw"
                    style="@style/settings_switch_style"
                    android:layout_marginStart="@dimen/dp_36"
                    android:layout_marginTop="@dimen/dp_6"
                    app:layout_constraintBottom_toBottomOf="@id/setting_network_notification_tv"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/setting_network_notification_tv"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />
                
                <TextView
                    android:id="@+id/setting_network_notification_tv"
                    style="@style/settings_switch_tv_style"
                    android:layout_width="738dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_42"
                    android:text="@string/wifi_network_notification_switcher"
                    app:layout_constraintStart_toEndOf="@id/settings_network_notification_sw"
                    app:layout_constraintTop_toTopOf="parent" />
                
                <TextView
                    android:id="@+id/setting_network_notification_tips_tv"
                    style="@style/settings_text_30_regular_747578_style"
                    android:layout_width="738dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:layout_marginBottom="@dimen/dp_32"
                    android:gravity="center_vertical"
                    android:text="@string/wifi_network_notification_tip"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/setting_network_notification_tv"
                    app:layout_constraintTop_toBottomOf="@id/setting_network_notification_tv" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
        
        </androidx.constraintlayout.widget.ConstraintLayout>
        
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_hotspot_cl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_wifi_hot_spot_switch_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_32"
                android:minHeight="120dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">
                
                <Switch
                    android:id="@+id/settings_hotspot_sw"
                    style="@style/settings_switch_style"
                    android:layout_marginStart="@dimen/dp_36"
                    android:layout_marginTop="@dimen/dp_35"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />
                
                <TextView
                    android:id="@+id/settings_hotspot_tv"
                    style="@style/settings_switch_tv_style"
                    android:layout_width="550dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_24"
                    android:text="@string/wifi_hotspot_switcher"
                    app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_sw"
                    app:layout_constraintStart_toEndOf="@id/settings_hotspot_sw"
                    app:layout_constraintTop_toTopOf="@id/settings_hotspot_sw" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_hotspot_edit_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="240dp"
                android:layout_marginTop="@dimen/dp_48"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_wifi_hot_spot_switch_cl">
                
                <TextView
                    android:id="@+id/settings_hotspot_name_tv"
                    style="@style/settings_text_36_regular_17191e_style"
                    android:layout_width="550dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_36"
                    android:gravity="center|left"
                    android:text="@string/wifi_hot_spot_name"
                    app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_edit_ib"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/settings_hotspot_edit_ib" />
                
                <EditText
                    android:id="@+id/settings_hotspot_name_et"
                    style="@style/settings_edit_text_style"
                    android:layout_width="350dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_24"
                    android:background="@android:color/transparent"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center|right"
                    android:imeOptions="actionDone"
                    android:inputType="textNoSuggestions"
                    android:maxLength="32"
                    android:maxLines="1"
                    android:text="@string/wifi_hotspot_name"
                    android:textCursorDrawable="@drawable/edit_text_cursor_drawable"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_edit_ib"
                    app:layout_constraintEnd_toStartOf="@id/settings_hotspot_edit_ib"
                    app:layout_constraintTop_toTopOf="@id/settings_hotspot_edit_ib" />
                
                <TextView
                    android:id="@+id/settings_hotspot_name_et_tv"
                    style="@style/settings_edit_text_style"
                    android:layout_width="350dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_24"
                    android:ellipsize="end"
                    android:gravity="center|right"
                    android:maxLines="1"
                    android:text="@string/wifi_hotspot_name"
                    app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_edit_ib"
                    app:layout_constraintEnd_toStartOf="@id/settings_hotspot_edit_ib"
                    app:layout_constraintTop_toTopOf="@id/settings_hotspot_edit_ib" />
                
                <ImageButton
                    android:id="@+id/settings_hotspot_edit_ib"
                    style="@style/settings_icon_48x48_style"
                    android:layout_marginEnd="@dimen/dp_36"
                    android:background="@color/color_00000000"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/image_button_edit_icon"
                    app:layout_constraintBottom_toTopOf="@id/settings_hotspot_password_edit_ib"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                
                <ImageButton
                    android:id="@+id/settings_hotspot_clear_ib"
                    style="@style/settings_icon_48x48_style"
                    android:layout_marginEnd="@dimen/dp_36"
                    android:background="@color/color_00000000"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/image_button_clear_icon"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_edit_ib"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/settings_hotspot_edit_ib" />
                
                <TextView
                    android:id="@+id/settings_hotspot_password_tv"
                    style="@style/settings_text_36_regular_17191e_style"
                    android:layout_width="550dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_36"
                    android:gravity="center|left"
                    android:text="@string/wifi_hot_spot_password"
                    app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_password_edit_ib"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/settings_hotspot_password_edit_ib" />
                
                <EditText
                    android:id="@+id/settings_hotspot_password_et"
                    style="@style/settings_edit_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_24"
                    android:background="@android:color/transparent"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center|right"
                    android:imeOptions="actionDone"
                    android:inputType="textVisiblePassword"
                    android:maxLength="32"
                    android:maxLines="1"
                    android:text="@string/wifi_hotspot_password"
                    android:textCursorDrawable="@drawable/edit_text_cursor_drawable"
                    app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_password_edit_ib"
                    app:layout_constraintEnd_toStartOf="@id/settings_hotspot_password_edit_ib"
                    app:layout_constraintTop_toTopOf="@+id/settings_hotspot_password_edit_ib"
                    tools:ignore="TextFields" />
                
                <ImageButton
                    android:id="@+id/settings_hotspot_password_edit_ib"
                    style="@style/settings_icon_48x48_style"
                    android:layout_marginEnd="@dimen/dp_36"
                    android:background="@color/color_00000000"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/image_button_edit_icon"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/settings_hotspot_edit_ib" />
                
                <ImageButton
                    android:id="@+id/settings_hotspot_password_clear_ib"
                    style="@style/settings_icon_48x48_style"
                    android:layout_marginEnd="@dimen/dp_36"
                    android:background="@color/color_00000000"
                    android:padding="@dimen/dp_3"
                    android:src="@drawable/image_button_clear_icon"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_password_edit_ib"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/settings_hotspot_password_edit_ib" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
            
            <TextView
                android:id="@+id/settings_hotspot_connected_tv"
                style="@style/settings_text_30_regular_747578_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_36"
                android:gravity="center|left"
                android:text="@string/wifi_hotspot_connected"
                app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_count_tv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/settings_hotspot_count_tv" />
            
            <TextView
                android:id="@+id/settings_hotspot_count_tv"
                style="@style/settings_text_30_regular_17191e_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_47"
                android:layout_marginBottom="@dimen/dp_24"
                android:gravity="center|right"
                android:text="@string/wifi_hotspot_connected_count"
                app:layout_constraintBottom_toTopOf="@id/settings_hotspot_connected_cl"
                app:layout_constraintEnd_toEndOf="parent" />
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settings_hotspot_connected_cl"
                style="@style/settings_content_card_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="114dp"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settings_hotspot_edit_cl">
                
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/settings_connected_hotspot_rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
        
        </androidx.constraintlayout.widget.ConstraintLayout>
    
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.bitech.vehiclesettings.view.widget.ScanScrollView>
