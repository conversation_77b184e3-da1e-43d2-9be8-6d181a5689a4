<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="28.89dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_light_zone_control"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <ImageView
                android:layout_width="842.67dp"
                android:layout_height="306.67dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="45.33dp"
                android:background="@mipmap/ic_l_zone_control" />

            <LinearLayout
                android:layout_width="842.67dp"
                android:layout_height="80dp"
                android:layout_marginLeft="85.33dp"
                android:layout_marginTop="64dp">
                <!-- 前围 -->
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"

                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_cowl_item1"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="128dp"
                        android:text="@string/str_light_zone_item1"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                </RelativeLayout>
                <!-- 前围 -->
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:layout_marginLeft="21.33dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_cowl_item2"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="128dp"
                        android:text="@string/str_light_zone_item2"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                </RelativeLayout>

                <!-- 后围 -->
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:layout_marginLeft="21.33dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_cowl_item3"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="128dp"
                        android:text="@string/str_light_zone_item3"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>