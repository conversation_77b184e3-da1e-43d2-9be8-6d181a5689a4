<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_recognition_camera"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <com.bitech.vehiclesettings.view.common.ToggleScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="330dp"
                android:layout_marginTop="30.33dp"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:layout_gravity="center">

                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="DreamHanSansCN"
                    android:singleLine="false"
                    android:text="@string/str_recognition_camera_1"
                    android:textColor="@color/dialog_content_color"
                    android:textSize="@dimen/font_30px" />

            </com.bitech.vehiclesettings.view.common.ToggleScrollView>

            <View
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_marginTop="10dp"
                android:background="@color/color_transparent_20" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_marginTop="33.33dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:fontFamily="DreamHanSansCN"
                android:singleLine="false"
                android:text="@string/str_recognition_camera_1"
                android:textColor="@color/dialog_content_color"
                android:textSize="@dimen/font_32px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="85.33dp"
                android:layout_marginTop="53.33dp"
                android:layout_marginRight="85.33dp"
                android:layout_marginBottom="45.33dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_agree"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/selector_bg_open"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_system_permission_service_privacy_statement_agree"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_refuse"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="200dp"
                    android:background="@drawable/selector_bg_cancel"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_system_permission_service_privacy_statement_refuse"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_32px" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>