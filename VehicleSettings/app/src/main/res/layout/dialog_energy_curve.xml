<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="tabSelectedIndex"
            type="Integer" />

        <variable
            name="fuelSelectedIndex"
            type="Integer" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/border_bg_dialog">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_152"
            android:gravity="center"
            android:text="@string/ne_energy_curve"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_48"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clCurveText"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_128"
            android:layout_marginHorizontal="@dimen/px_80"
            app:layout_constraintTop_toBottomOf="@id/tvTitle">

            <TextView
                android:id="@+id/tvElectricCurve"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_72"
                android:gravity="center"
                android:text="@string/ne_electric_curve"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvFuelCurve"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/px_72"
                android:layout_marginStart="@dimen/px_100"
                android:gravity="center"
                android:text="@string/ne_fuel_curve"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvElectricCurve"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/viewUnderline1"
                android:layout_width="@dimen/px_168"
                android:layout_height="@dimen/px_6"
                android:layout_marginBottom="@dimen/px_18"
                android:background="@color/blue"
                android:visibility="@{tabSelectedIndex==0?View.VISIBLE:View.INVISIBLE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <View
                android:id="@+id/viewUnderline2"
                android:layout_width="@dimen/px_168"
                android:layout_height="@dimen/px_6"
                android:layout_marginBottom="@dimen/px_18"
                android:background="@color/blue"
                android:visibility="@{tabSelectedIndex==1?View.VISIBLE:View.INVISIBLE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvFuelCurve" />

            <TextView
                android:id="@+id/tvCurveValueDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_10"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_32"
                app:layoutMarginRight="@{tabSelectedIndex==0?@dimen/px_98:@dimen/px_40}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="@string/ne_recently_50km" />

            <TextView
                android:id="@+id/tvCurveValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/px_10"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvCurveValueDesc"
                tools:text="8.5 L/100km" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvEnergyUnit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/px_137"
            android:layout_marginTop="@dimen/px_24"
            android:text="@{tabSelectedIndex==0?`kW•h/100km`:`L/100km`}"
            android:textColor="@color/text_color_2"
            android:textSize="@dimen/pxt_36"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clCurveText" />

        <com.github.mikephil.charting.charts.LineChart
            android:id="@+id/lineChartFuel"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_606"
            android:layout_marginHorizontal="@dimen/px_119"
            android:visibility="@{tabSelectedIndex==1?View.VISIBLE:View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/tvEnergyUnit" />

        <com.github.mikephil.charting.charts.LineChart
            android:id="@+id/lineChartElectric"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_606"
            android:layout_marginHorizontal="@dimen/px_119"
            android:visibility="@{tabSelectedIndex==0?View.VISIBLE:View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/tvEnergyUnit" />

        <LinearLayout
            android:id="@+id/llFuelType"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_80"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/px_60"
            android:background="@drawable/shape_bg_white"
            android:gravity="center_vertical"
            android:visibility="@{tabSelectedIndex==1?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/tvFuel10km"
                android:layout_width="@dimen/px_174"
                android:layout_height="match_parent"
                android:background="@drawable/selector_bg_blue_type"
                android:gravity="center"
                android:selected="@{fuelSelectedIndex == 0}"
                android:text="10km"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/pxt_32" />

            <TextView
                android:id="@+id/tvFuel25km"
                android:layout_width="@dimen/px_174"
                android:layout_height="match_parent"
                android:background="@drawable/selector_bg_blue_type"
                android:gravity="center"
                android:selected="@{fuelSelectedIndex == 1}"
                android:text="25km"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/pxt_32" />

            <TextView
                android:id="@+id/tvFuel50km"
                android:layout_width="@dimen/px_174"
                android:layout_height="match_parent"
                android:background="@drawable/selector_bg_blue_type"
                android:gravity="center"
                android:selected="@{fuelSelectedIndex == 2}"
                android:text="50km"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/pxt_32" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>