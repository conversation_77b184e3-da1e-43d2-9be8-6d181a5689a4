<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="465.33dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="101.33dp"
        android:layout_marginStart="80dp"
        android:orientation="horizontal"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_42px"
            android:textColor="@color/black"
            android:text="@string/str_condition_next_maintenance"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:orientation="horizontal"
        android:gravity="center">
        <TextView
            android:id="@+id/tv_next_maintenance_km"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_36px"
            android:textColor="@color/black_transparent_60"
            android:text="@string/str_condition_next_maintenance"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_36px"
            android:textColor="@color/black_transparent_60"
            android:text=" ｜ "/>
        <TextView
            android:id="@+id/tv_next_maintenance_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_36px"
            android:textColor="@color/black_transparent_60"
            android:text="@string/str_condition_next_maintenance"/>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/rl_maintenance_reset"
        android:layout_width="624dp"
        android:layout_height="120dp"
        android:layout_marginTop="21.33dp"
        android:layout_marginStart="80dp"
        android:background="@drawable/shape_bg_white">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="32dp"
            android:orientation="vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_condition_next_maintenance_standard"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="84dp"
            android:layout_marginRight="6dp"
            android:src="@mipmap/ic_small_marker" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_maintenance_remind"
        android:layout_width="624dp"
        android:layout_height="120dp"
        android:layout_marginTop="21.33dp"
        android:layout_marginStart="80dp"
        android:background="@drawable/shape_bg_white">

        <Switch
            android:id="@+id/sw_maintenance_remind"
            android:layout_width="64dp"
            android:layout_height="37.33dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="32dp"
            android:background="@color/transparent"
            android:checked="true"
            android:switchMinWidth="64dp"
            android:thumb="@drawable/thumb"
            android:track="@drawable/track" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="32dp"
                android:text="@string/str_condition_next_maintenance_reminder"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="44dp" />
</LinearLayout>