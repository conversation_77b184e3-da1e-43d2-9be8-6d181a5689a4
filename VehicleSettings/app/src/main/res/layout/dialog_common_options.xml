<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="selectedIndex"
            type="Integer" />

        <variable
            name="dialogTitle"
            type="String" />

        <variable
            name="option1Text"
            type="String" />

        <variable
            name="option2Text"
            type="String" />

        <variable
            name="option3Text"
            type="String" />

        <import type="android.view.View" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/border_bg_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/px_120">

        <TextView
            android:id="@+id/tvDialogTitle"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_152"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:text="@{dialogTitle}"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_42"
            tools:text="@string/ne_range_condition_display" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_96"
            android:layout_marginTop="@dimen/px_24"
            android:background="@drawable/shape_bg_white_12"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvOption1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/selector_bg_blue_type"
                android:gravity="center"
                android:selected="@{selectedIndex == 0}"
                android:text="@{option1Text}"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/pxt_36" />

            <TextView
                android:id="@+id/tvOption2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/selector_bg_blue_type"
                android:gravity="center"
                android:selected="@{selectedIndex == 1}"
                android:text="@{option2Text}"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/pxt_36" />

            <TextView
                android:id="@+id/tvOption3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/selector_bg_blue_type"
                android:gravity="center"
                android:selected="@{selectedIndex == 2}"
                android:text="@{option3Text}"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/pxt_36"
                android:visibility="@{option3Text==null ? View.GONE : View.VISIBLE}" />

        </LinearLayout>
    </LinearLayout>
</layout>