<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/shape_bg_white"
    android:layout_width="624dp"
    android:layout_height="107dp"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="10dp">
    
    <TextView
        android:id="@+id/wifi_hotspot_device_name_tv"
        android:layout_width="500dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_36"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="24sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

