<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="624dp"
    android:layout_height="107dp"
    android:layout_marginBottom="16dp"
    android:layout_marginStart="80dp"
    android:background="@drawable/shape_bg_white">
    
    <ImageView
        android:id="@+id/bt_icon_iv"
        style="@style/settings_icon_48x48_style"
        android:layout_marginStart="32dp"
        android:src="@mipmap/lanya_n_dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    
    <TextView
        android:id="@+id/bt_name_tv"
        android:layout_width="550dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:textSize="24sp"
        android:textColor="@color/black"
        android:maxLines="1"
        app:layout_constraintBottom_toTopOf="@id/bt_carplay_tips_tv"
        app:layout_constraintStart_toEndOf="@id/bt_icon_iv"
        app:layout_constraintTop_toTopOf="parent" />
    
    <TextView
        android:id="@+id/bt_carplay_tips_tv"
        style="@style/settings_text_30_regular_747578_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:text="@string/bt_device_support_wireless_carplay"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/bt_name_tv"
        app:layout_constraintTop_toBottomOf="@id/bt_name_tv" />
    
    <ProgressBar
        android:id="@+id/bt_paired_loading_pb"
        style="@style/settings_icon_48x48_style"
        android:layout_marginEnd="@dimen/dp_36"
        android:indeterminate="true"
        android:indeterminateDrawable="@drawable/progress_bar_wifi_scan_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
