<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="581dp"
    android:layout_height="107dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:layout_marginBottom="16dp"
    android:background="@drawable/shape_bg_white">

    <Space
        android:layout_width="32dp"
        android:layout_height="1dp"/>

    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@mipmap/lanya_n_dark" />

    <Space
        android:layout_width="32dp"
        android:layout_height="1dp"/>

    <TextView
        android:id="@+id/device_name"
        android:layout_width="249dp"
        android:layout_height="31dp"
        android:textSize="@dimen/font_34px" />

    <!--    <TextView-->
    <!--        android:id="@+id/device_address"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:textSize="14sp"/>-->

</LinearLayout>