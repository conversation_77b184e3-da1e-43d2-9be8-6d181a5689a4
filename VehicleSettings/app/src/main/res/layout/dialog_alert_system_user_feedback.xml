<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1032dp"
    android:layout_height="524dp"
    android:background="@drawable/border_bg_dialog">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 原有内容保持不变 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="101.33dp"
            android:layout_marginStart="80dp"
            android:layout_marginEnd="80dp"
            android:gravity="center">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_user_feedback"
                android:textColor="@color/black"
                android:textSize="@dimen/font_42px" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="189.33dp"
            android:layout_marginStart="80dp"
            android:layout_marginEnd="80dp"
            android:layout_marginTop="46.67dp"
            android:orientation="vertical"
            android:background="@drawable/shape_bg_driving_personal"
            android:gravity="center_horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingStart="20dp"
                android:paddingEnd="20dp">

                <EditText
                    android:id="@+id/editText"
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="top|start"
                    android:maxLines="10"
                    android:maxLength="200"
                    android:background="@null"
                    android:inputType="textMultiLine"
                    android:textColor="@color/black"
                    android:textSize="24sp"
                    android:hint="请描述您的问题..."
                    android:textColorHint="@color/black_transparent_30" />

                <TextView
                    android:id="@+id/charCounter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_alignParentEnd="true"
                    android:text="0/200"
                    android:textSize="26sp"
                    android:textColor="@color/black_transparent_30"
                    android:layout_marginEnd="36dp"
                    android:layout_marginBottom="24dp"/>
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="50.67dp"
            android:layout_marginStart="80dp"
            android:layout_marginEnd="80dp">

            <Button
                android:id="@+id/btn_confirm"
                style="?android:borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="64dp"
                android:background="@drawable/button_blue"
                android:text="@string/str_submit"
                android:textColor="@color/white"
                android:textSize="@dimen/font_36px" />

            <Button
                android:id="@+id/btn_cancel"
                style="?android:borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="64dp"
                android:layout_marginStart="32dp"
                android:background="@drawable/button_white"
                android:text="@string/str_cancel"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>
    </LinearLayout>
    <!-- 关闭按钮（固定在右上角） -->
    <!-- 在原有 RelativeLayout 中添加 -->
    <FrameLayout
        android:layout_width="140dp"
        android:layout_height="140dp"
        android:layout_marginStart="80dp">

        <!-- 默认显示的静态图片 -->
        <ImageView
            android:id="@+id/iv_voice_normal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/icon_sound_recordings_listening_back"
            android:contentDescription="语音输入" />
        <!-- PAG动画（初始隐藏） -->
        <org.libpag.PAGView
            android:id="@+id/pag_animation"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />
    </FrameLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="90.67dp"
        android:gravity="center">
        <TextView
            android:id="@+id/tv_userback_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="点击按钮或唤醒语音后开始语音反馈"
            android:textSize="@dimen/font_36px"
            android:textColor="@color/black_transparent_30"/>
    </LinearLayout>
</RelativeLayout>