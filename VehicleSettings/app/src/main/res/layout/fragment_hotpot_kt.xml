<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.widget.ScanScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/settings_wifi_ssv"
    style="@style/settings_scroll_bar_style"
    android:layout_width="784dp"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    tools:context=".view.connect.WifiFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/settings_hotspot_cl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_hotpot_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="33dp"
            android:fontFamily="DreamHanSansCN"
            android:text="@string/str_hot"
            android:textColor="@color/black"
            android:textSize="@dimen/font_48px"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_wifi_switch_cl"
            android:layout_width="624dp"
            android:layout_height="107dp"
            android:layout_marginTop="107dp"
            android:background="@drawable/shape_bg_white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <Switch
                android:id="@+id/settings_hotspot_sw"
                style="@style/settings_switch_style"
                android:layout_width="wrap_content"
                android:layout_height="35dp"
                android:layout_marginStart="32dp"
                android:layout_marginTop="36dp"
                android:background="@color/transparent"
                android:checked="true"
                android:switchMinWidth="64dp"
                android:thumb="@drawable/thumb"
                android:track="@drawable/track"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/settings_hotspot_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="128dp"
                android:layout_marginTop="22dp"
                android:text="@string/wifi_hotspot_switcher"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_24"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/settings_hotspot_name_et_tv"
                android:layout_width="350dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center|left"
                android:maxLines="1"
                android:textSize="19sp"
                android:textColor="@color/color_transparent_40"
                android:layout_marginStart="128dp"
                android:layout_marginTop="61dp"
                android:text="@string/wifi_hotspot_name"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_hotspot_edit_cl"
            style="@style/settings_content_card_style"
            android:layout_width="624dp"
            android:layout_height="80dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/shape_bg_white"
            app:layout_constraintEnd_toEndOf="parent"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/settings_wifi_switch_cl">

            <EditText
                android:id="@+id/settings_hotspot_name_et"
                style="@style/settings_edit_text_style"
                android:layout_width="350dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_24"
                android:background="@android:color/transparent"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:gravity="center|right"
                android:imeOptions="actionDone"
                android:inputType="textNoSuggestions"
                android:maxLength="32"
                android:maxLines="1"
                android:text="@string/wifi_hotspot_name"
                android:textCursorDrawable="@drawable/edit_text_cursor_drawable"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_password_edit_ib"
                app:layout_constraintEnd_toStartOf="@id/settings_hotspot_password_edit_ib"
                app:layout_constraintTop_toTopOf="@id/settings_hotspot_password_edit_ib" />
            />

            <TextView
                android:id="@+id/settings_hotspot_password_tv"
                style="@style/settings_text_36_regular_17191e_style"
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:gravity="center|left"
                android:text="@string/wifi_hot_spot_password"
                android:layout_marginTop="@dimen/dp_26"
                android:layout_marginStart="@dimen/dp_32"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/settings_hotspot_password_et"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:gravity="center|right"
                android:imeOptions="actionDone"
                android:inputType="textVisiblePassword"
                android:maxLength="32"
                android:maxLines="1"
                android:text="@string/wifi_hotspot_password"
                android:textColor="@color/color_transparent_40"
                android:textSize="24sp"
                android:layout_marginTop="25dp"
                android:layout_marginEnd="96dp"
                android:textCursorDrawable="@drawable/edit_text_cursor_drawable"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="TextFields" />

            <ImageButton
                android:id="@+id/settings_hotspot_password_edit_ib"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@color/color_00000000"
                android:layout_marginTop="27dp"
                android:layout_marginEnd="35dp"
                android:src="@mipmap/image_button_edit_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/settings_hotspot_connected_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="80dp"
            android:textSize="24sp"
            android:gravity="center|left"
            android:text="@string/wifi_hotspot_connected"
            android:textColor="@color/color_transparent_60"
            app:layout_constraintBottom_toBottomOf="@id/settings_hotspot_count_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/settings_hotspot_count_tv" />

        <TextView
            android:id="@+id/settings_hotspot_count_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="80dp"
            android:layout_marginBottom="@dimen/dp_24"
            android:textSize="24sp"
            android:gravity="center|right"
            android:text="@string/wifi_hotspot_connected_count"
            android:textColor="@color/color_transparent_60"
            app:layout_constraintBottom_toTopOf="@id/settings_hotspot_connected_cl"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_hotspot_connected_cl"
            style="@style/settings_content_card_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="88dp"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/settings_hotspot_edit_cl">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/settings_connected_hotspot_rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="80dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_wifi_compatible_cl"
            android:layout_width="624dp"
            android:layout_height="107dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/shape_bg_white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/settings_hotspot_connected_cl">

            <Switch
                android:id="@+id/settings_hotspot_compatible_sw"
                style="@style/settings_switch_style"
                android:layout_width="64dp"
                android:layout_height="37.33dp"
                android:layout_marginLeft="32dp"
                android:layout_marginTop="39dp"
                android:background="@color/transparent"
                android:checked="false"
                android:switchMinWidth="64dp"
                android:thumb="@drawable/thumb"
                android:track="@drawable/track"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/settings_hotspot_compatible_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="128dp"
                android:layout_marginTop="22dp"
                android:text="2.4GHz"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_24"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/settings_hotspot_compatible_tv2"
                android:layout_width="350dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center|left"
                android:maxLines="1"
                android:textSize="19sp"
                android:textColor="@color/color_transparent_40"
                android:layout_marginStart="128dp"
                android:layout_marginTop="61dp"
                android:text="@string/hotspot_connect_type"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/img_hotspot_detail"
                android:layout_width="42px"
                android:layout_height="42px"
                android:layout_marginStart="843px"
                android:layout_marginTop="59px"
                android:src="@mipmap/hotspot_detail"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.bitech.vehiclesettings.view.widget.ScanScrollView>