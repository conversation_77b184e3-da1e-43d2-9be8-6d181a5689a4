<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    style="@style/scroll_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_left"
        android:layout_marginTop="14dp"
        android:orientation="vertical">
        <!--后尾门开启高度-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="166.67dp"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="554.67dp"
                android:layout_height="match_parent"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="32dp"
                    android:text="@string/str_carsetting_high"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/iv_hud_roate_top"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="32dp"
                    android:layout_marginRight="32dp"
                    android:text="90%"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:id="@+id/iv_hud_high"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="94.67dp"
                    android:src="@mipmap/ic_rear_high" />

                <SeekBar
                    android:id="@+id/sb_iv_hud_roate"
                    android:layout_width="432dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="90.67dp"
                    android:layout_marginTop="94.67dp"
                    android:importantForAccessibility="no"
                    android:background="@color/transparent"
                    android:max="100"
                    android:maxHeight="13.33dp"
                    android:minWidth="522.66dp"
                    android:minHeight="13.33dp"
                    android:progress="50"
                    android:progressDrawable="@drawable/seekbar_progress"
                    android:splitTrack="false"
                    android:thumb="@mipmap/ic_sound_slider" />
            </RelativeLayout>
        </LinearLayout>
        <!--雨刮灵敏度-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_carsetting_wiper_sens"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <com.bitech.vehiclesettings.view.common.SegmentedPickerView
            android:id="@+id/segmentedPickerView"
            android:layout_width="match_parent"
            android:layout_height="77.33dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="24.67dp"
            app:pickerFontSize="@dimen/font_36px"
            app:pickerHeight="77.33dp" />

        <!--门窗-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_carsetting_door_window"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_auto_window"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_door_window_1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <!--车锁、设防提示、儿童锁-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_carsetting_lock"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">
            <!--设防提示-->
            <RelativeLayout
                android:id="@+id/rl_lock_tips"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_carsetting_lock_1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
            <!--儿童锁-->
            <RelativeLayout
                android:id="@+id/rl_child_lock"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_carsetting_lock_2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>
        <!--自动落锁、驻车自动解锁-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">
            <!--自动落锁-->
            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_auto_lock"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_lock_3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>

            <!--驻车自动解锁-->
            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_auto_unlock"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_lock_4"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <!--保养-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_carsetting_maintain"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">
            <!--保养提示-->
            <LinearLayout
                android:id="@+id/rl_maintain_tips"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_maintain_tips"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_maintain_1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>
            <!--保养里程复位-->
            <RelativeLayout
                android:id="@+id/rl_maintain_reset"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_carsetting_maintain_2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_maintain_content"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_carsetting_maintain_3"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <!--安全-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_carsetting_safe"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <RelativeLayout
            android:id="@+id/rl_safe"
            android:layout_width="554.67dp"
            android:layout_height="133.33dp"
            android:layout_marginTop="32dp"
            android:background="@drawable/shape_bg_white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="32dp"
                android:text="@string/str_carsetting_safe_1"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />

            <ImageView
                android:id="@+id/iv_safe_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="33.33dp"
                android:src="@mipmap/ic_small_tip" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginTop="90.67dp"
                android:layout_marginRight="10.67dp"
                android:src="@mipmap/ic_small_marker" />

        </RelativeLayout>

        <!--更多-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_more_title"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
        <!--雨刮维修模式、超速报警-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">
            <!--雨刮维修模式-->
            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_wiper_repair"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_more_1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>
            </LinearLayout>
            <!--超速报警-->
            <LinearLayout
                android:id="@+id/ll_overspeed"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_overspeed"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <!--设防提示-->
                <RelativeLayout
                    android:layout_width="458.67dp"
                    android:layout_height="133.33dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_more_2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="90.67dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>

            </LinearLayout>
        </LinearLayout>

        <!--疲劳驾驶提醒-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_fatugue_drive"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_fatigue_drive"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <RelativeLayout
                    android:layout_width="458.67dp"
                    android:layout_height="133.33dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_more_3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="90.67dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="29.33dp"
            android:orientation="horizontal" />
    </LinearLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>