<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1328dp"
    android:layout_height="726dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="101.33dp"
        android:layout_marginStart="80dp"
        android:orientation="horizontal"
        android:gravity="center">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/str_condition_energy_consumption_list"
            android:textColor="@color/black"
            android:textSize="@dimen/font_48px"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="1168dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:orientation="horizontal">
        <LinearLayout
            android:layout_width="584dp"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="584dp"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:textSize="@dimen/font_60px"
                    android:textColor="@color/black"
                    android:text="1024"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="km"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="584dp"
                android:layout_marginTop="21.33dp"
                android:layout_height="wrap_content"
                android:gravity="center">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_36px"
                    android:textColor="@color/black"
                    android:text="总里程"/>
            </LinearLayout>
        </LinearLayout>
        <ImageView
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:src="@mipmap/ic_condition_line_set"/>
        <LinearLayout
            android:layout_width="584dp"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="584dp"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:textSize="@dimen/font_60px"
                    android:textColor="@color/black"
                    android:text="1624"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="km"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="584dp"
                android:layout_marginTop="21.33dp"
                android:layout_height="wrap_content"
                android:gravity="center">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_36px"
                    android:textColor="@color/black"
                    android:text="总续航里程"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:paddingLeft="15dp"
                    android:paddingEnd="15dp"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:text="CLTC"
                    android:background="@drawable/bg_rounded_corner_blue_left_down_and_right_up"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="1168dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginTop="39.33dp"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="最近50公里"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="1168dp"
        android:layout_height="145.33dp"
        android:layout_marginTop="18dp"
        android:layout_marginStart="80dp"
        android:background="@drawable/shape_bg_white">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="27.33dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/font_60px"
                    android:textColor="@color/black"
                    android:text="12.8"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="kW·h/100km"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="10.67dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="平均电耗"/>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="27.33dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/font_60px"
                    android:textColor="@color/black"
                    android:text="8.5"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="L/100km"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="10.67dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="平均油耗"/>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="27.33dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/font_60px"
                    android:textColor="@color/black"
                    android:text="8.5"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="kW·h/100km"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="10.67dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="平均能耗"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="1168dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginTop="39dp"
        android:orientation="horizontal">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="自清零后"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="清零"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_36px"
            android:layout_gravity="end"
            />
    </LinearLayout>
    <LinearLayout
        android:layout_width="1168dp"
        android:layout_height="145.33dp"
        android:layout_marginTop="18dp"
        android:layout_marginStart="80dp"
        android:background="@drawable/shape_bg_white">
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="27.33dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/font_60px"
                    android:textColor="@color/black"
                    android:text="30.4"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="kW·h"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="10.67dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="外接充电电量"/>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="27.33dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/font_60px"
                    android:textColor="@color/black"
                    android:text="1009.5"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_28px"
                    android:text="L"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="10.67dp"
                android:gravity="center_horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="@dimen/font_28px"
                    android:textColor="@color/black"
                    android:text="燃油消耗量"/>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="44dp" />
</LinearLayout>