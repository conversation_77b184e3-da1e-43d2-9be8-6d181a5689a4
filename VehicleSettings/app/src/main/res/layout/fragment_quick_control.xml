<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    style="@style/scroll_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_left"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_my_glview"
            android:layout_width="1216dp"
            android:layout_height="500dp"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:scaleType="center">


        </LinearLayout>

        <FrameLayout
            android:id="@+id/fl_main_tool"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="8dp">

            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="none">

                <LinearLayout
                    android:id="@+id/ll_tools"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <FrameLayout
                        android:id="@+id/fl_center_lock"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_bg_quick">

                        <TextView
                            android:id="@+id/tv_center_lock_desc"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fontFamily="DreamHanSansCN"
                            android:gravity="bottom"
                            android:paddingStart="16dp"
                            android:paddingBottom="@dimen/dp_16"
                            android:text="@string/str_center_lock"
                            android:textColor="@color/selector_quick_text_color"
                            android:textSize="@dimen/font_36px" />

                        <org.libpag.PAGView
                            android:id="@+id/pv_center_lock"
                            android:layout_width="@dimen/dp_30"
                            android:layout_height="@dimen/dp_30"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_16" />
                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/fl_tail_lock"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/selector_bg_quick">

                        <TextView
                            android:id="@+id/tv_tail_lock_desc"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fontFamily="DreamHanSansCN"
                            android:gravity="bottom"
                            android:paddingStart="16dp"
                            android:paddingBottom="@dimen/dp_16"
                            android:text="@string/str_tail_lock"
                            android:textColor="@color/selector_quick_text_color"
                            android:textSize="@dimen/font_36px" />

                        <org.libpag.PAGView
                            android:id="@+id/pv_tail_lock"
                            android:layout_width="@dimen/dp_30"
                            android:layout_height="@dimen/dp_30"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_16" />
                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/fl_rear_mirror"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/selector_bg_quick">

                        <TextView
                            android:id="@+id/tv_rear_mirror_desc"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fontFamily="DreamHanSansCN"
                            android:gravity="bottom"
                            android:paddingStart="16dp"
                            android:paddingBottom="@dimen/dp_16"
                            android:text="@string/str_rear_mirror_lock"
                            android:textColor="@color/selector_quick_text_color"
                            android:textSize="@dimen/font_36px" />

                        <org.libpag.PAGView
                            android:id="@+id/pv_rear_mirror"
                            android:layout_width="@dimen/dp_30"
                            android:layout_height="@dimen/dp_30"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_16" />
                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/fl_window_desc"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/selector_bg_quick">

                        <TextView
                            android:id="@+id/tv_window_desc"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fontFamily="DreamHanSansCN"
                            android:gravity="bottom"
                            android:paddingStart="16dp"
                            android:paddingBottom="@dimen/dp_16"
                            android:text="@string/str_car_window"
                            android:textColor="@color/selector_quick_text_color"
                            android:textSize="@dimen/font_36px" />

                        <org.libpag.PAGView
                            android:id="@+id/pv_window_desc"
                            android:layout_width="@dimen/dp_30"
                            android:layout_height="@dimen/dp_30"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_16" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:layout_marginLeft="132dp"
                            android:layout_marginBottom="12dp"
                            android:src="@mipmap/ic_small_marker" />
                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/fl_window_lock"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="32dp"
                        android:background="@drawable/selector_bg_quick">

                        <TextView
                            android:id="@+id/tv_window_lock_desc"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fontFamily="DreamHanSansCN"
                            android:gravity="bottom"
                            android:paddingStart="16dp"
                            android:paddingBottom="@dimen/dp_16"
                            android:text="@string/str_window_lock"
                            android:textColor="@color/selector_quick_text_color"
                            android:textSize="@dimen/font_36px" />

                        <org.libpag.PAGView
                            android:id="@+id/pv_window_lock"
                            android:layout_width="@dimen/dp_30"
                            android:layout_height="@dimen/dp_30"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_16" />
                    </FrameLayout>

                    <RelativeLayout
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="32dp">
                        <!-- 遮阳帘 -->
                        <com.bitech.vehiclesettings.view.common.TopDrawableTextView
                            android:id="@+id/tv_sunshade_desc"
                            android:layout_width="176dp"
                            android:layout_height="match_parent"

                            android:background="@drawable/selector_bg_quick"
                            android:drawableTop="@drawable/selector_bg_quick_sunshade"
                            android:drawablePadding="8dp"
                            android:fontFamily="DreamHanSansCN"
                            android:text="@string/str_car_sunshade"
                            android:textColor="@color/selector_text_color"
                            android:textSize="@dimen/font_36px"
                            app:drawableOffsetX="17.33dp"
                            app:drawableOffsetY="17.33dp"
                            app:textOffsetX="16dp"
                            app:textOffsetY="73.33dp" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_alignParentBottom="true"
                            android:layout_marginRight="12dp"
                            android:layout_marginBottom="12dp"
                            android:src="@mipmap/ic_small_marker" />
                    </RelativeLayout>
                    <!-- 电动尾翼 -->
                    <RelativeLayout
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="32dp"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/tv_autotail_desc"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/selector_bg_white"
                            android:drawableTop="@drawable/selector_bg_quick_autotail"
                            android:fontFamily="DreamHanSansCN"
                            android:gravity="center"
                            android:paddingTop="22dp"
                            android:text="@string/str_car_autotail"
                            android:textColor="@color/selector_text_color"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_alignParentBottom="true"
                            android:layout_marginRight="12dp"
                            android:layout_marginBottom="12dp"
                            android:src="@mipmap/ic_small_marker" />
                    </RelativeLayout>


                    <TextView
                        android:id="@+id/tv_skylight_lock_desc"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="32dp"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_tc"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_skylight_lock"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px"
                        android:visibility="gone" />
                </LinearLayout>
            </HorizontalScrollView>
            <!--车窗子菜单-->
            <LinearLayout
                android:id="@+id/ll_tools_sub_win"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="invisible">

                <FrameLayout
                    android:id="@+id/fl_car_window_reback"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="32dp"
                    android:background="@drawable/selector_bg_quick">

                    <TextView
                        android:id="@+id/tv_car_window_reback"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="bottom"
                        android:paddingStart="16dp"
                        android:paddingBottom="@dimen/dp_16"
                        android:text="@string/str_car_window"
                        android:textColor="@color/selector_quick_text_color"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_car_window_reback"
                        android:layout_width="@dimen/dp_30"
                        android:layout_height="@dimen/dp_30"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16" />

                </FrameLayout>

                <LinearLayout
                    android:id="@+id/tv_car_window_reback_arrow"
                    android:layout_width="152dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/ic_q_retract_arrow" />
                </LinearLayout>

                <FrameLayout
                    android:id="@+id/fl_car_window_lower"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="32dp"
                    android:background="@drawable/selector_bg_quick">

                    <TextView
                        android:id="@+id/tv_car_window_lower"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="bottom"
                        android:paddingStart="16dp"
                        android:paddingBottom="@dimen/dp_16"
                        android:text="@string/str_car_window_lower"
                        android:textColor="@color/selector_quick_text_color"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_car_window_lower"
                        android:layout_width="@dimen/dp_30"
                        android:layout_height="@dimen/dp_30"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16" />

                </FrameLayout>

                <FrameLayout
                    android:id="@+id/fl_car_window_breathable"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="32dp"
                    android:background="@drawable/selector_bg_quick">

                    <TextView
                        android:id="@+id/tv_car_window_breathable"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="bottom"
                        android:paddingStart="16dp"
                        android:paddingBottom="@dimen/dp_16"
                        android:text="@string/str_car_window_breathable"
                        android:textColor="@color/selector_quick_text_color"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_car_window_breathable"
                        android:layout_width="@dimen/dp_30"
                        android:layout_height="@dimen/dp_30"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16" />

                </FrameLayout>

                <FrameLayout
                    android:id="@+id/fl_car_window_raise"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="32dp"
                    android:background="@drawable/selector_bg_quick">

                    <TextView
                        android:id="@+id/tv_car_window_raise"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="bottom"
                        android:paddingStart="16dp"
                        android:paddingBottom="@dimen/dp_16"
                        android:text="@string/str_car_window_raise"
                        android:textColor="@color/selector_quick_text_color"
                        android:textSize="@dimen/font_36px" />

                    <org.libpag.PAGView
                        android:id="@+id/pv_car_window_raise"
                        android:layout_width="@dimen/dp_30"
                        android:layout_height="@dimen/dp_30"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16" />

                </FrameLayout>

            </LinearLayout>

            <!--遮阳帘子菜单-->
            <LinearLayout
                android:id="@+id/ll_tools_sub_sunshade"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="invisible">

                <TextView
                    android:id="@+id/tv_car_sunshade_reback"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="146dp"
                    android:background="@drawable/selector_bg_quick"
                    android:drawableTop="@drawable/selector_bg_quick_sunshade"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:paddingTop="22dp"
                    android:text="@string/str_car_sunshade"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <LinearLayout
                    android:id="@+id/tv_car_sunshade_reback_arrow"
                    android:layout_width="152dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/ic_q_retract_arrow" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_tools_sub_sunshade_all"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/tv_car_sunshade_front"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_front"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_front"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_car_sunshade_rear"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="32dp"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_rear"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_tools_sub_sunshade_front"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:visibility="invisible">

                    <TextView
                        android:id="@+id/tv_car_sunshade_front_open"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_front"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_open"
                        android:textColor="@color/selector_quick_text_color"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_car_sunshade_front_close"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="32dp"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_close"
                        android:textColor="@color/selector_quick_text_color"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_tools_sub_sunshade_rear"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:visibility="invisible">

                    <TextView
                        android:id="@+id/tv_car_sunshade_rear_open"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_front"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_open"
                        android:textColor="@color/selector_quick_text_color"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:id="@+id/tv_car_sunshade_rear_close"
                        android:layout_width="176dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="32dp"
                        android:background="@drawable/selector_bg_quick"
                        android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                        android:fontFamily="DreamHanSansCN"
                        android:gravity="center"
                        android:paddingTop="22dp"
                        android:text="@string/str_car_sunshade_close"
                        android:textColor="@color/selector_quick_text_color"
                        android:textSize="@dimen/font_36px" />

                </LinearLayout>

            </LinearLayout>

            <!-- 电动尾翼子菜单 -->
            <LinearLayout
                android:id="@+id/ll_tools_sub_autotail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:visibility="invisible">

                <TextView
                    android:id="@+id/tv_car_autotail_reback"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="48.67dp"
                    android:background="@drawable/selector_bg_quick"
                    android:drawableTop="@drawable/selector_bg_quick_autotail"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:paddingTop="22dp"
                    android:text="@string/str_car_autotail"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <LinearLayout
                    android:id="@+id/tv_car_autotail_reback_arrow"
                    android:layout_width="152dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/ic_q_retract_arrow" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_car_autotail_close"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:background="@drawable/selector_bg_quick"
                    android:drawableTop="@drawable/selector_bg_quick_sunshade_front"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:paddingTop="22dp"
                    android:text="@string/str_car_autotail_close"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_car_autotail_1"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/selector_bg_quick"
                    android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:paddingTop="22dp"
                    android:text="@string/str_car_autotail_1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_car_autotail_2"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/selector_bg_quick"
                    android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:paddingTop="22dp"
                    android:text="@string/str_car_autotail_2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_car_autotail_auto"
                    android:layout_width="176dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/selector_bg_quick"
                    android:drawableTop="@drawable/selector_bg_quick_sunshade_rear"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:paddingTop="22dp"
                    android:text="@string/str_car_autotail_auto"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
        </FrameLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="57.67dp"
            android:text="@string/str_smart_key_title"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_close_unlock"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_switch_key_title"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_condition_tail"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_switch_tail_title"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_auto_window"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_door_window_1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_lockcar_sunroof_shade"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_switch_lock_recycle"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">
            <!--设防提示-->
            <RelativeLayout
                android:id="@+id/rl_lock_tips"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_carsetting_lock_1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
            <!--儿童锁-->
            <RelativeLayout
                android:id="@+id/rl_child_lock"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_carsetting_lock_2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>
        <!--自动落锁、驻车自动解锁-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">
            <!--自动落锁-->
            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_auto_lock"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_lock_3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>

            <!--驻车自动解锁-->
            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_auto_unlock"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_lock_4"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <!--3d车模控制-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">
            <!--3d车模控制-->
            <RelativeLayout
                android:id="@+id/rl_3d_model"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_rear_mirror_title"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="135.02dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_rear_mirror"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_rear_mirror_adjust"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white"
                android:visibility="gone">

                <Switch
                    android:id="@+id/sw_condition_rear_mirror"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_switch_rear_mirror_title"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="135.02dp"
            android:layout_marginTop="32dp"
            android:orientation="horizontal"
            android:visibility="gone">

            <RelativeLayout
                android:id="@+id/rl_rear_mirror_adjust"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_rear_mirror_auto"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_condition_heating_rear_mirror"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_switch_heating_rear_mirror_title"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="56.67dp"
            android:text="@string/str_more_title"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="135.02dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_steering_whell_custom"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <TextView

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_Steering_wheel_custom"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_condition_seat_writing"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_switch_seat_writing_title"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="135.02dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_hud_roate"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <TextView

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_carsetting_high"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_wiper_sens"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <TextView

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_carsetting_wiper_sens"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="135.02dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_safe"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_carsetting_safe_1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:id="@+id/iv_safe_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="33.33dp"
                    android:src="@mipmap/ic_small_tip" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_vehicle_power_off"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:id="@+id/tv_vehicle_power_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_vehicle_power_off"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="90.67dp"
                    android:layout_marginRight="10.67dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="135.02dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                <com.bitech.vehiclesettings.view.common.NoToggleSwitch
                    android:id="@+id/sw_refuel_small_door"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    app:track="@drawable/track"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_refuel_small_door"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="29.33dp"
            android:orientation="horizontal" />
    </LinearLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>
