<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/border_bg_dialog"
        android:paddingHorizontal="@dimen/px_120">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_152"
            android:gravity="center"
            android:text="@string/ne_energy_consumption_list"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_42"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--    自补能后-->
        <TextView
            android:id="@+id/tvAfterSupplementEnergy"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_100"
            android:layout_marginTop="@dimen/px_104"
            android:gravity="center_vertical|start"
            android:text="@string/ne_after_supplement_energy"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_36"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAfterCharging"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_160"
            app:drawable_radius="@{@dimen/px_24}"
            app:drawable_solidColor="@{@color/color_white_transparent_70}"
            app:layout_constraintTop_toBottomOf="@id/tvAfterSupplementEnergy">

            <TextView
                android:id="@+id/tvAfterChargingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/px_24"
                android:paddingVertical="@dimen/px_8"
                android:text="@string/ne_after_charging"
                android:textColor="@color/light_white"
                android:textSize="@dimen/pxt_24"
                app:drawable_radiusLT="@{@dimen/px_24}"
                app:drawable_radiusRB="@{@dimen/px_24}"
                app:drawable_solidColor="@{@color/color_21aa4c}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTotalMileage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_17"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvTotalMileageText"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="358 km" />

            <TextView
                android:id="@+id/tvTotalMileageText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/px_17"
                android:text="@string/ne_total_mileage_traveled"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.167"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tvAvgPowerConsumption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvAvgPowerConsumptionText"
                app:layout_constraintTop_toTopOf="@id/tvTotalMileage"
                tools:text="8.8 kW·h/100km" />

            <TextView
                android:id="@+id/tvAvgPowerConsumptionText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_average_power_consumption"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="@id/tvTotalMileageText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.46"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tvAverageSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvAverageSpeedText"
                app:layout_constraintTop_toTopOf="@id/tvTotalMileage"
                tools:text="85 km/h" />

            <TextView
                android:id="@+id/tvAverageSpeedText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_average_speed"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="@id/tvTotalMileageText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.83"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAfterRefueling"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_160"
            android:layout_marginTop="@dimen/px_24"
            app:drawable_radius="@{@dimen/px_24}"
            app:drawable_solidColor="@{@color/color_white_transparent_70}"
            app:layout_constraintTop_toBottomOf="@id/clAfterCharging">

            <TextView
                android:id="@+id/tvAfterRefuelingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_left_corner_marker_blue"
                android:paddingHorizontal="@dimen/px_24"
                android:paddingVertical="@dimen/px_8"
                android:text="@string/ne_after_refueling"
                android:textColor="@color/light_white"
                android:textSize="@dimen/pxt_24"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTotalMileageRefueling"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/px_17"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvTotalMileageTextRefueling"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="658 km" />

            <TextView
                android:id="@+id/tvTotalMileageTextRefueling"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/px_17"
                android:text="@string/ne_total_mileage_traveled"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.167"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tvAverageSpeedRefueling"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvAverageSpeedTextRefueling"
                app:layout_constraintTop_toTopOf="@id/tvTotalMileageRefueling"
                tools:text="115 km/h" />

            <TextView
                android:id="@+id/tvAverageSpeedTextRefueling"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_average_speed"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="@id/tvTotalMileageTextRefueling"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.46"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--    自启动后-->
        <TextView
            android:id="@+id/tvAfterStartup"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_100"
            android:gravity="start|center_vertical"
            android:text="@string/ne_after_startup"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_36"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clAfterRefueling" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAfterStartup"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_160"
            android:paddingVertical="@dimen/px_17"
            app:drawable_radius="@{@dimen/px_24}"
            app:drawable_solidColor="@{@color/color_white_transparent_70}"
            app:layout_constraintTop_toBottomOf="@id/tvAfterStartup">

            <TextView
                android:id="@+id/tvTotalMileageStartup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvTotalMileageTextStartup"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="210 km" />

            <TextView
                android:id="@+id/tvTotalMileageTextStartup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_total_mileage_traveled"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.167"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tvAvgPowerConsumptionStartup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvAvgPowerConsumptionTextStartup"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="11.2 kW·h/100km" />

            <TextView
                android:id="@+id/tvAvgPowerConsumptionTextStartup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_average_power_consumption"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.46"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tvAverageSpeedStartup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvAverageSpeedTextStartup"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="96 km/h" />

            <TextView
                android:id="@+id/tvAverageSpeedTextStartup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_average_speed"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.83"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--    自复位后-->
        <TextView
            android:id="@+id/tvAfterResetting"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_100"
            android:gravity="start|center_vertical"
            android:text="@string/ne_after_resetting"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_36"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clAfterStartup" />

        <TextView
            android:id="@+id/neResetting"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_100"
            android:background="@drawable/ripple_effect"
            android:gravity="start|center_vertical"
            android:text="@string/ne_resetting"
            android:textColor="@color/blue"
            android:textSize="@dimen/pxt_32"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvAfterResetting" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_160"
            android:paddingVertical="@dimen/px_17"
            app:drawable_radius="@{@dimen/px_24}"
            app:drawable_solidColor="@{@color/color_white_transparent_70}"
            app:layout_constraintTop_toBottomOf="@id/tvAfterResetting">

            <TextView
                android:id="@+id/tvTotalMileageResetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvTotalMileageTextResetting"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="124 km" />

            <TextView
                android:id="@+id/tvTotalMileageTextResetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_total_mileage_traveled"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.167"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tvAvgPowerConsumptionResetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvAvgPowerConsumptionTextResetting"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="9.5 kW·h/100km" />

            <TextView
                android:id="@+id/tvAvgPowerConsumptionTextResetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_average_power_consumption"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.46"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tvAverageSpeedResetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_60"
                app:layout_constraintStart_toStartOf="@id/tvAverageSpeedTextResetting"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="88 km/h" />

            <TextView
                android:id="@+id/tvAverageSpeedTextResetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_average_speed"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.83"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>