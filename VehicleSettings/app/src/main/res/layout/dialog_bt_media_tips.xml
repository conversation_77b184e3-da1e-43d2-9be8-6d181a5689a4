<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="752dp"
    android:layout_height="285dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog">

    <TextView
        android:layout_width="581dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/bt_media_tv"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="33dp"
        android:layout_marginStart="85dp"/>

    <TextView
        android:layout_width="581dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="85dp"
        android:text="开启后，连接蓝牙时播放手机上的声音，包含媒体、通知、系统提示音；关闭后，只在打开蓝牙音乐或桌面媒体卡切换至蓝牙音乐时，手机媒体声音通过车机播放。"
        android:textColor="@color/black_transparent_40"
        android:textSize="26sp"
        android:layout_marginTop="107dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>