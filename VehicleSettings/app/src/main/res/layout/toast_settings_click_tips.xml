<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toast_click_tips_cl"
        style="@style/toast_background_style"
        android:layout_width="wrap_content"
        android:layout_height="180dp"
        android:minWidth="569dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        
        <TextView
            android:id="@+id/toast_click_tips_tv"
            style="@style/settings_text_34_regular_17191e_style"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_80"
            android:layout_marginBottom="@dimen/dp_40"
            android:gravity="center"
            android:maxLines="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
