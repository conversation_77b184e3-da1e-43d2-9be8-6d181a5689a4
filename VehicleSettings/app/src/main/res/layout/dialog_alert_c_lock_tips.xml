<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_carsetting_lock_tips_dg"
                android:textColor="@color/black"
                android:textSize="@dimen/font_48px" />

        <LinearLayout
            android:layout_width="576dp"
            android:layout_height="77.34dp"
            android:layout_gravity="center"
            android:layout_marginTop="86.67dp">
            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/spv_picker"
                android:layout_width="576dp"
                android:layout_height="77.34dp"/>
        </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>