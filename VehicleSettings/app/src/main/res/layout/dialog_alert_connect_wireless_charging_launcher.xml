<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="442.67dp"
    android:layout_height="256dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="442.67dp"
        android:layout_height="256dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingLeft="@dimen/px_48"
        android:paddingTop="@dimen/px_28"
        android:paddingRight="@dimen/px_48">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_64"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <Switch
                    android:id="@+id/sw_front"
                    android:layout_width="@dimen/dp_53"
                    android:layout_height="@dimen/dp_35"
                    android:layout_gravity="center_vertical"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_32"
                    android:layout_marginTop="-3dp"
                    android:gravity="center_vertical"
                    android:text="@string/str_wireless_charging"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_38" />
                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />
                <ImageView
                    android:id="@+id/iv_charge_tips"
                    android:layout_width="@dimen/dp_64"
                    android:layout_height="@dimen/dp_64"
                    android:layout_marginRight="-12dp"
                    android:background="@drawable/icon_dark_96_tip"
                    />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_height="106.67dp"
            android:orientation="horizontal"
            android:background="@drawable/shape_bg_white"
            android:gravity="center_vertical">
            <TextView
                android:id="@+id/tv_charge_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_wireless_charging_error_launcher"
                android:textSize="@dimen/font_36px"
                android:textColor="@color/black"
                android:layout_marginLeft="@dimen/dp_32"
                />
            <View
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"/>
            <ImageView
                android:id="@+id/iv_charge_status"
                android:layout_width="@dimen/dp_85"
                android:layout_height="@dimen/dp_85"
                android:layout_marginRight="21.33dp"
                android:background="@drawable/comba_img_charge_d"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>