<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="564px"
    android:layout_height="224px"
    android:background="@drawable/bg_widget"
    android:orientation="vertical"
    android:paddingTop="26px"
    android:paddingBottom="18px">

    <LinearLayout
        android:layout_width="281px"
        android:layout_height="88px"
        android:layout_alignParentStart="true"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvPureElectricEndurance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/light_white"
            android:textSize="@dimen/pxt_36" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="纯电续航"
            android:textColor="@color/color_dark_60"
            android:textSize="@dimen/pxt_24" />

    </LinearLayout>

    <TextView
        android:layout_width="2px"
        android:layout_height="40px"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="24px"
        android:background="#33FAFCFF" />

    <LinearLayout
        android:layout_width="281px"
        android:layout_height="88px"
        android:layout_alignParentEnd="true"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvRemainingCapacity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/light_white"
            android:textSize="@dimen/pxt_36" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="剩余电量"
            android:textColor="@color/color_dark_60"
            android:textSize="@dimen/pxt_24" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvSearchCharingStation"
        android:layout_width="500px"
        android:layout_height="68px"
        android:layout_alignParentBottom="true"
        android:layout_centerInParent="true"
        android:layout_marginHorizontal="32px"
        android:background="@drawable/bg_widget_btn"
        android:gravity="center"
        android:text="@string/ne_search_charging_stations"
        android:textColor="#CCFAFCFF"
        android:textSize="18sp"
        android:textStyle="bold" />

</RelativeLayout>