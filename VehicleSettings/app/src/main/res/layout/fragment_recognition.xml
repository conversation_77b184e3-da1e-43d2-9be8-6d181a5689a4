<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    style="@style/scroll_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:layout_marginTop="14dp"
        android:layout_marginLeft="@dimen/dp_left"
        android:orientation="vertical">

        <!--舱内摄像头-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="133.33dp"
            android:layout_marginTop="24.67dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_camera"
                android:layout_width="554.67dp"
                android:layout_height="133.33dp"
                android:background="@drawable/shape_bg_white">

                    <com.bitech.vehiclesettings.view.common.NoToggleSwitch
                        android:id="@+id/sw_camera"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        app:track="@drawable/track" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_recognition_camera"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_recognition"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!--安全驾驶检测-->
            <TextView
                android:id="@+id/tv_safe_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="56.67dp"
                android:text="@string/str_recognition_safe"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
            <!--疲劳检测、视线分心提醒-->
            <LinearLayout
                android:id="@+id/ll_fatigue_detection_and_distraction"
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:layout_marginTop="24.67dp"
                android:orientation="horizontal">
                <!--疲劳检测-->
                <LinearLayout
                    android:id="@+id/ll_fatigue_detection"
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_fatigue_detection"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_safe_1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_fatigue_detection_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>
                <!--视线分心提醒-->
                <LinearLayout
                    android:id="@+id/ll_distraction"
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_distraction"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_safe_2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_distraction_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>
            <!--主驾打电话提醒-->
            <LinearLayout
                android:id="@+id/ll_call_and_drink"
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:layout_marginTop="24.67dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_call"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_safe_3"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_call_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_drink"
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_drink"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_safe_4"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_drink_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

            <!--智能车控-->
            <TextView
                android:id="@+id/tv_recognition_vehicle_control"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="56.67dp"
                android:text="@string/str_recognition_vehicle_control"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
            <!--智能座椅加热、智能座椅通风-->
            <LinearLayout
                android:id="@+id/ll_seat_heat_and_seat_ventilation"
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:layout_marginTop="24.67dp"
                android:orientation="horizontal">
                <!--智能座椅加热-->
                <LinearLayout
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_seat_heat"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_vehicle_control_1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_seat_heat_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>
                <!--智能座椅通风-->
                <LinearLayout
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_seat_ventilation"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_vehicle_control_2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_seat_ventilation_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>
            <!--视线解锁屏保-->
            <LinearLayout
                android:id="@+id/ll_sight_unlock"
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:layout_marginTop="24.67dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_sight_unlock"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_vehicle_control_3"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_sight_unlock_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

            <!--主动关怀-->
            <TextView
                android:id="@+id/tv_recognition_care"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="56.67dp"
                android:text="@string/str_recognition_care"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
            <!--个性化问候、抽烟关怀-->
            <LinearLayout
                android:id="@+id/ll_greet_and_smoke"
                android:layout_width="match_parent"
                android:layout_height="133.33dp"
                android:layout_marginTop="24.67dp"
                android:orientation="horizontal">
                <!--个性化问候-->
                <LinearLayout
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_greet"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_care_1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_greet_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>
                <!--抽烟关怀-->
                <LinearLayout
                    android:id="@+id/ll_smoke"
                    android:layout_width="554.67dp"
                    android:layout_height="133.33dp"
                    android:layout_marginLeft="32dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/sw_smoke"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <RelativeLayout
                        android:layout_width="458.67dp"
                        android:layout_height="133.33dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_recognition_care_2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <ImageView
                            android:id="@+id/iv_smoke_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="33.33dp"
                            android:src="@mipmap/ic_small_tip" />

                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="29.33dp"
            android:orientation="horizontal" />
    </LinearLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>