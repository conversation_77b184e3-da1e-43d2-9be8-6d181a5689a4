<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="368dp"
    android:layout_height="206.67dp"
    android:layout_marginBottom="21.33dp"
    app:cardCornerRadius="16dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 背景图片 -->
        <ImageView
            android:id="@+id/iv_background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@mipmap/display_wp_3d" />

        <!-- 左上角标签 -->
        <TextView
            android:id="@+id/tv_tag"
            android:layout_width="100dp"
            android:layout_height="42.67dp"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:background="@mipmap/display_left_top_label"
            android:gravity="center"
            android:padding="6dp"
            android:text="@string/str_wrapper_3d"
            android:textColor="@android:color/white"
            android:textSize="@dimen/font_20px" />

        <!-- 右下角选择框 -->
        <ImageView
            android:id="@+id/iv_checkbox"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_margin="16dp"
            android:src="@mipmap/icon_set_light_choose_no" />
    </RelativeLayout>
</androidx.cardview.widget.CardView>