<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="440dp"
    android:layout_height="501dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <Switch
        android:id="@+id/sw_front"
        android:layout_width="@dimen/dp_53"
        android:layout_height="@dimen/dp_35"
        android:layout_gravity="center_vertical"
        android:background="@color/transparent"
        android:checked="false"
        android:thumb="@drawable/thumb"
        android:track="@drawable/track"
        android:layout_marginTop="32dp"
        android:layout_marginStart="32dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="109dp"
        android:layout_marginTop="33dp"
        android:gravity="center_vertical"
        android:text="@string/str_blue"
        android:textColor="@color/black"
        android:textSize="25sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_charge_tips"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginTop="19dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/icon_dark_96_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--    未打开蓝牙状态-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bt_close"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:layout_marginTop="35dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView4">

        <ImageView
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:src="@mipmap/icon_launcher_bt_close"
            android:layout_marginTop="60dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/blue_launcher_close_tips"
            android:textColor="@color/black"
            android:textSize="21sp"
            android:layout_marginTop="250dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>