<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="752dp"
    android:layout_height="533dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog">

    <TextView
        android:id="@+id/textView3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="33dp"
        android:text="@string/wifi_add_wifi"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_32"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.402">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="581dp"
            android:layout_height="80dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_bg_white">

            <EditText
                android:id="@+id/et_wifi_name"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_marginStart="32dp"
                android:background="@android:color/transparent"
                android:hint="名称"
                android:textColor="@color/black_transparent_40"
                android:textColorHint="@color/black_transparent_40"
                android:textSize="24sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ic_wifi_name_clear"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginEnd="30dp"
                android:src="@mipmap/img_btn_clear"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="581dp"
            android:layout_height="80dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_bg_white">

            <EditText
                android:id="@+id/et_wifi_password"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_marginStart="32dp"
                android:background="@android:color/transparent"
                android:hint="密码"
                android:inputType="textPassword"
                android:textColor="@color/black_transparent_40"
                android:textColorHint="@color/black_transparent_40"
                android:textSize="24sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ic_wifi_hide"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginEnd="30dp"
                android:src="@mipmap/img_closeeyes"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_encryption"
            android:layout_width="581dp"
            android:layout_height="90dp"
            android:background="@drawable/shape_bg_white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="16dp"
                android:text="@string/wifi_encryption_type"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_24"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/wifi_encryption_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="50dp"
                android:text="WPA"
                android:textColor="@color/black_transparent_40"
                android:textSize="@dimen/sp_19"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="13dp"
                android:layout_marginBottom="13dp"
                android:src="@mipmap/ic_small_marker"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <Button
        android:id="@+id/btn_connect"
        android:layout_width="267dp"
        android:layout_height="67dp"
        android:background="@drawable/shape_bg_blue"
        android:text="@string/wifi_join"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_24"
        android:layout_marginStart="84dp"
        android:layout_marginBottom="45dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btn_cancel"
        android:layout_width="267dp"
        android:layout_height="67dp"
        android:background="@drawable/shape_bg_white"
        android:text="@string/str_cancel"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_24"
        android:layout_marginEnd="84dp"
        android:layout_marginBottom="45dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>