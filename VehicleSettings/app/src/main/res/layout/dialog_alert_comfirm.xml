<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="34.66dp"
        android:fontFamily="HarmonyOS_Sans_SC"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="85.33dp"
        android:layout_marginTop="32.66dp"
        android:layout_marginEnd="85.33dp"
        android:fontFamily="HarmonyOS_Sans_SC"
        android:gravity="center"
        android:textColor="@color/color_dialog_content"
        android:textSize="@dimen/font_40px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="85.33dp"
        android:layout_marginTop="53.33dp"
        android:layout_marginEnd="85.33dp"
        android:layout_marginBottom="45.33dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/tv_content">

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/selector_bg_open"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_confirm2"
            android:textColor="#ffffff"
            android:textSize="@dimen/font_36px" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="50.66dp"
            android:background="@drawable/selector_bg_cancel"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_cancel"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>