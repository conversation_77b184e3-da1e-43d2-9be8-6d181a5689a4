<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="101.33dp"
            android:layout_gravity="center"
            android:gravity="center">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_3d_model"
                android:textColor="@color/black"
                android:textSize="@dimen/font_48px" />
        </LinearLayout>
        <!--车门控制-->
        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/str_3d_model_car_door"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_car_door_fl"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_door_fl"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_door_fl"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_car_door_fr"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:layout_marginLeft="24dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_door_fr"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_door_fr"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_car_door_rl"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_door_rl"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_door_rl"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_car_door_rr"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:layout_marginLeft="24dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_door_rr"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_door_rr"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
        </LinearLayout>
        <!--车窗控制-->
        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/str_3d_model_car_window"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_car_window_fl"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_window_fl"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_window_fl"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_car_window_fr"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:layout_marginLeft="24dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_window_fr"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_window_fr"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_car_window_rl"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_window_rl"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_window_rl"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_car_window_rr"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:layout_marginLeft="24dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_window_rr"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_window_rr"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="896dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_car_hood"
                android:layout_width="437.33dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_car_hood"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="false"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="309.33dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_3d_model_car_hood"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>