<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="620dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="33.33dp"
        android:text="@string/str_headrest_speaker"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px" />

    <FrameLayout
        android:layout_width="698.67dp"
        android:layout_height="357.33dp"
        android:layout_marginHorizontal="42.67dp"
        android:layout_marginTop="33.33dp">

        <ImageView
            android:id="@+id/iv_background"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@mipmap/ic_sound_headrest_speaker_sx" />
        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginBottom="16dp"
            android:text="@string/str_headrest_gx_desc"
            android:textColor="@color/black_transparent_40"
            android:textSize="@dimen/font_34px" />
    </FrameLayout>


    <com.bitech.vehiclesettings.view.common.SegmentedPickerView
        android:id="@+id/spvHeadrest"
        android:layout_marginTop="16dp"
        android:layout_width="624dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:background="@drawable/shape_bg_white" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="50.67dp" />
</LinearLayout>