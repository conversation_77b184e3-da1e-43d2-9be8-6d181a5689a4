<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="33.33dp"
            android:fontFamily="DreamHanSansCN"
            android:text="@string/str_system_permission_4"
            android:textColor="@color/selector_text_color"
            android:textSize="@dimen/font_48px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginTop="33.33dp"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="100dp"
                android:background="@drawable/shape_bg_white">

                    <com.bitech.vehiclesettings.view.common.NoToggleSwitch
                        android:id="@+id/sw_permission"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track"
                        app:track="@drawable/track"/>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_switch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_carsetting_door_window_1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:visibility="invisible"
                android:layout_width="554.67dp"
                android:layout_height="100dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="1104dp"
            android:layout_height="93.33dp"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_permission_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="4dp"
                android:textColor="@color/dialog_content_color"
                android:textSize="@dimen/font_30px"
                android:autoLink="web"
                android:fontFamily="DreamHanSansCN"
                android:breakStrategy="simple"
                />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="20dp">

            <TextView
                android:id="@+id/tv_permission_app"
                android:layout_width="1104dp"
                android:layout_height="wrap_content"

                android:background="@drawable/selector_bg_blue_type"
                android:fontFamily="DreamHanSansCN"

                android:text="已允许N个应⽤获取此设备的位置。"

                android:textColor="@color/dialog_content_color"
                android:textSize="@dimen/font_30px" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:scrollbars="none"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>