<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1274.67dp"
    android:layout_height="780dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="35.33dp"
        android:text="@string/str_sound_effect_adjustment"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <!--left-->
        <ScrollView
            android:id="@+id/sv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50.66dp"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="592dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="80dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="19.33dp"
                    android:text="@string/str_sound_adv"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_32px" />

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">


                    <com.bitech.vehiclesettings.view.voice.MyRadioGroup
                        android:id="@+id/mg"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="13.33dp">

                            <RadioButton
                                android:id="@+id/rbAllCar"
                                android:layout_width="288dp"
                                android:layout_height="64dp"
                                android:layout_marginEnd="13.33dp"
                                android:background="@drawable/selector_radio_button"
                                android:button="@null"
                                android:gravity="center"
                                android:text="@string/str_sound_adv_all_car"
                                android:textColor="@drawable/radio_display_text_selector"
                                android:textSize="@dimen/font_36px" />

                            <RadioButton
                                android:id="@+id/rbDriver"
                                android:layout_width="288dp"
                                android:layout_height="64dp"
                                android:background="@drawable/selector_radio_button"
                                android:button="@null"
                                android:gravity="center"
                                android:text="@string/str_sound_adv_driver"
                                android:textColor="@drawable/radio_display_text_selector"
                                android:textSize="@dimen/font_36px" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="13.33dp">

                            <RadioButton
                                android:id="@+id/rbFront"
                                android:layout_width="288dp"
                                android:layout_height="64dp"
                                android:layout_marginEnd="13.33dp"
                                android:background="@drawable/selector_radio_button"
                                android:button="@null"
                                android:gravity="center"
                                android:text="@string/str_sound_adv_front"
                                android:textColor="@drawable/radio_display_text_selector"
                                android:textSize="@dimen/font_36px" />

                            <RadioButton
                                android:id="@+id/rbRear"
                                android:layout_width="288dp"
                                android:layout_height="64dp"
                                android:background="@drawable/selector_radio_button"
                                android:button="@null"
                                android:gravity="center"
                                android:text="@string/str_sound_adv_rear"
                                android:textColor="@drawable/radio_display_text_selector"
                                android:textSize="@dimen/font_36px" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <!--                        <RadioButton-->
                            <!--                            android:id="@+id/rbAuto"-->
                            <!--                            android:layout_width="270.67dp"-->
                            <!--                            android:layout_height="66.67dp"-->
                            <!--                            android:layout_marginEnd="13.33dp"-->
                            <!--                            android:background="@drawable/selector_radio_button"-->
                            <!--                            android:button="@null"-->
                            <!--                            android:gravity="center"-->
                            <!--                            android:text="@string/str_sound_adv_auto"-->
                            <!--                            android:textColor="@drawable/radio_display_text_selector"-->
                            <!--                            android:textSize="@dimen/font_36px" />-->

                            <RadioButton
                                android:id="@+id/rbCustom"
                                android:layout_width="288dp"
                                android:layout_height="64dp"
                                android:background="@drawable/selector_radio_button"
                                android:button="@null"
                                android:gravity="center"
                                android:text="@string/str_sound_adv_custom"
                                android:textColor="@drawable/radio_display_text_selector"
                                android:textSize="@dimen/font_36px" />
                        </LinearLayout>
                    </com.bitech.vehiclesettings.view.voice.MyRadioGroup>

                </FrameLayout>

                <LinearLayout
                    android:layout_width="592dp"
                    android:layout_height="106.67dp"
                    android:layout_marginTop="25.33dp"
                    android:background="@drawable/shape_bg_white">

                    <Switch
                        android:id="@+id/swSurroundSound"
                        android:layout_width="64dp"
                        android:layout_height="37.33dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:background="@color/transparent"
                        android:checked="true"
                        android:switchMinWidth="64dp"
                        android:thumb="@drawable/thumb"
                        android:track="@drawable/track" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="32dp"
                            android:text="@string/str_sound_adv_item7"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />
                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32.67dp"
                    android:layout_marginBottom="19.33dp"
                    android:text="@string/str_sound_adv_virtual_live"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_32px" />

                <FrameLayout
                    android:id="@+id/llSPV"
                    android:layout_width="592dp"
                    android:layout_height="64dp"
                    android:layout_marginTop="14.67dp">

                    <!-- 子控件 SegmentedPickerView -->
                    <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                        android:id="@+id/spvVirtual"
                        android:layout_width="592dp"
                        android:layout_height="64dp"
                        android:background="@drawable/shape_bg_white" />

                    <!-- 新增：透明点击遮罩，负责父逻辑 -->
                    <View
                        android:id="@+id/touchBlocker"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@android:color/transparent" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="33.33dp"
                    android:layout_marginBottom="14.67dp"
                    android:text="@string/str_sound_equalizer"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_32px" />


                <RelativeLayout
                    android:id="@+id/rl_sound_equalizer_desc"
                    android:layout_width="592dp"
                    android:layout_height="133.33dp"
                    android:background="@drawable/shape_bg_white">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_sound_equalizer_desc"
                        android:textColor="@color/selector_text_color"
                        android:textSize="@dimen/font_36px" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="90.67dp"
                        android:layout_marginRight="10.67dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="32dp" />
            </LinearLayout>
        </ScrollView>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="22.66dp"
            android:layout_marginTop="64.67dp"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <View
                    android:id="@+id/hotArea"
                    android:layout_width="397.33dp"
                    android:layout_height="471.33dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="30dp" />

                <ImageView
                    android:id="@+id/ivBackground"
                    android:layout_width="559.33dp"
                    android:layout_height="593.2dp"
                    android:src="@mipmap/ic_sound_effect_adjustment" />

                <ImageView
                    android:id="@+id/ivAudio"
                    android:layout_width="559.33dp"
                    android:layout_height="593.2dp" />

                <ImageView
                    android:id="@+id/ivCustom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:src="@mipmap/ic_sound_headrest_speaker_center"
                    android:visibility="visible" />

                <!--                <TextView-->
                <!--                    android:id="@+id/tvCoordinates"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_gravity="center_horizontal|bottom"-->
                <!--                    android:layout_marginBottom="100dp"-->
                <!--                    android:text="坐标: (0,0)"-->
                <!--                    android:textColor="#000"-->
                <!--                    android:textSize="20sp" />-->

                <Button
                    android:id="@+id/btnBFReset"
                    style="?android:borderlessButtonStyle"
                    android:layout_width="181.33dp"
                    android:layout_height="66.67dp"
                    android:layout_gravity="center_horizontal|bottom"
                    android:layout_marginTop="48dp"
                    android:background="@drawable/button_voice_reset"
                    android:text="@string/str_reset"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </FrameLayout>
        </LinearLayout>
    </LinearLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="101.33dp" />
</LinearLayout>