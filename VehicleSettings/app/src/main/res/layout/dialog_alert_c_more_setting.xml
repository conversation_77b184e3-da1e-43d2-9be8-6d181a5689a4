<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="33.33dp"
            android:fontFamily="DreamHanSansCN"
            android:textColor="@color/selector_text_color"
            android:textSize="@dimen/font_48px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="106.67dp"
            android:layout_marginTop="32dp"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="554.67dp"
                android:layout_height="match_parent"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_switch"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <RelativeLayout
                    android:layout_width="458.67dp"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/tv_switch_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="32dp"
                        android:text="@string/str_carsetting_more_3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="166.67dp"
            android:layout_marginTop="32dp"
            android:gravity="center"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="554.67dp"
                android:layout_height="match_parent"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:id="@+id/tv_seekbar_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="32dp"
                    android:text="@string/str_carsetting_high"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_seekbar_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="32dp"
                    android:layout_marginRight="32dp"
                    android:text="90%"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <ImageView
                    android:id="@+id/iv_seekbar_icon"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="94.67dp"
                    android:src="@mipmap/ic_rear_high" />

                <SeekBar
                    android:id="@+id/sb_seekbar"
                    android:layout_width="432dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="90.67dp"
                    android:layout_marginTop="94.67dp"
                    android:importantForAccessibility="no"
                    android:max="100"
                    android:maxHeight="13.33dp"
                    android:minWidth="522.66dp"
                    android:minHeight="13.33dp"
                    android:progress="50"
                    android:progressDrawable="@drawable/seekbar_progress"
                    android:thumb="@mipmap/ic_sound_slider" />
            </RelativeLayout>
        </LinearLayout>

    </LinearLayout>
</LinearLayout>