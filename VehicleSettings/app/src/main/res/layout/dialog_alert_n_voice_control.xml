<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="816px"
    android:layout_height="680px"
    android:background="@drawable/border_voice_dialog"
    android:minWidth="752dp"
    android:minHeight="530.67dp"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbNavi"
                android:layout_width="720px"
                android:layout_height="128px"
                android:layout_marginTop="48px"
                app:iconHeight="45px"
                app:iconMarginStart="54px"
                app:iconSrc="@mipmap/ic_sound_navigation_dock"
                app:iconWidth="45px"
                app:max="10"
                app:radius="16px"
                app:seekBarHeight="128px"
                app:seekBarWidth="720px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="132px"
                android:layout_marginTop="22px"
                android:text="@string/str_navigation"
                android:textColor="@color/voice_alert_text"
                android:textSize="24sp" />

        </FrameLayout>

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbVR"
                android:layout_width="720px"
                android:layout_height="128px"
                android:layout_marginTop="24px"
                app:iconHeight="45px"
                app:iconMarginStart="54px"
                app:iconSrc="@mipmap/ic_sound_voice_dock"
                app:iconWidth="45px"
                app:max="10"
                app:radius="16px"
                app:seekBarHeight="128px"
                app:seekBarWidth="720px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="132px"
                android:layout_marginTop="10px"
                android:text="@string/str_voice"
                android:textColor="@color/voice_alert_text"
                android:textSize="24sp" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbMedia"
                android:layout_width="720px"
                android:layout_height="128px"
                android:layout_marginTop="24px"
                app:iconHeight="45px"
                app:iconMarginStart="54px"
                app:iconSrc="@mipmap/ic_sound_media_dock"
                app:iconWidth="45px"
                app:max="31"
                app:radius="16px"
                app:seekBarHeight="128px"
                app:seekBarWidth="720px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="132px"
                android:layout_marginTop="10px"
                android:text="@string/str_media"
                android:textColor="@color/voice_alert_text"
                android:textSize="24sp" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                android:id="@+id/sbPhone"
                android:layout_width="720px"
                android:layout_height="128px"
                android:layout_marginTop="24px"
                app:iconHeight="45px"
                app:iconMarginStart="54px"
                app:iconSrc="@mipmap/ic_sound_phone_dock"
                app:iconWidth="45px"
                app:max="25"
                app:radius="16px"
                app:seekBarHeight="128px"
                app:seekBarWidth="720px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="132px"
                android:layout_marginTop="10px"
                android:text="@string/str_phone"
                android:textColor="@color/voice_alert_text"
                android:textSize="24sp" />
        </FrameLayout>

    </LinearLayout>

</LinearLayout>