<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="624dp"
    android:layout_height="107dp"
    android:layout_marginBottom="16dp"
    android:background="@drawable/shape_bg_white">

    <ImageView
        android:id="@+id/wifi_signal_strength_iv"
        android:src="@mipmap/ic_wifi"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="32dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/wifi_name_tv"
        style="@style/settings_text_36_regular_17191e_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_24"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/wifi_signal_strength_iv"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/wifi_lock"
        android:src="@mipmap/ic_wifi_lock"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginEnd="@dimen/dp_36"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/wifi_delete"
        android:src="@mipmap/icon_set_wifi_delete"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginEnd="@dimen/dp_36"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/wifi_loading_pb"
        style="@style/settings_icon_48x48_style"
        android:layout_marginEnd="@dimen/dp_72"
        android:indeterminate="true"
        android:indeterminateDrawable="@drawable/progress_bar_wifi_scan_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
