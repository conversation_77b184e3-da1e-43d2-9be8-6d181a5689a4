<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_recognition_camera"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <!-- 第一个选项 -->
            <LinearLayout
                android:layout_width="@dimen/dp_624"
                android:layout_height="@dimen/dp_106.67"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="@dimen/weight_1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_32"
                        android:text="@string/str_system_permission_app_authorized_main_1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>

                <ImageView
                    android:id="@+id/rb_open_12_month"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/dp_32"
                    android:layout_marginRight="@dimen/dp_21.33"
                    android:src="@mipmap/icon_set_light_choose_no"/>
            </LinearLayout>

            <!-- 第二个选项 -->
            <LinearLayout
                android:layout_width="@dimen/dp_624"
                android:layout_height="@dimen/dp_106.67"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/shape_bg_white">


                <LinearLayout
                    android:layout_width="@dimen/dp_0"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="@dimen/weight_1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_32"
                        android:text="@string/str_system_permission_app_authorized_main_2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>

                <ImageView
                    android:id="@+id/rb_open_this_time"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/dp_32"
                    android:layout_marginRight="@dimen/dp_21.33"
                    android:src="@mipmap/icon_set_light_choose_no" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>