<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_system_permission_privacy_statement_title"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="53.33dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="33.33dp"
                android:gravity="center"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_data"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingStart="30dp"
                    android:paddingEnd="30dp"
                    android:layout_gravity="center"
                    android:overScrollMode="never"
                    android:scrollbars="vertical"
                    />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>