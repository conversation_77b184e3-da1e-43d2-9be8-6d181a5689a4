<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="ltr">

    <ImageView
        android:id="@+id/img_color_rang"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:src="@drawable/color_slidingbar"
        android:visibility="invisible" />

    <ImageView
        android:id="@+id/img_color_rang_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:src="@mipmap/color_slidingbar_mask"
        android:visibility="visible" />

    <ImageView
        android:id="@+id/img_picker"
        android:layout_width="2.66dp"
        android:layout_height="match_parent"
        android:background="@color/white" />
</RelativeLayout>