<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="20dp"
    android:background="@drawable/shape_bg_white">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="32dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_permission_text"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textColor="@color/black"
            android:textSize="@dimen/font_34px" />
    </LinearLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="10.67dp"
        android:layout_marginRight="10.67dp"
        android:src="@mipmap/ic_small_marker" />
</RelativeLayout>
