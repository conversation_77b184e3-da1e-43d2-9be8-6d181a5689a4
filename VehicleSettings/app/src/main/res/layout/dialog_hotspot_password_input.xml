<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.widget.ScanScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/settings_wifi_ssv"
    style="@style/settings_scroll_bar_style"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/border_bg_dialog"
    tools:context=".view.connect.WifiFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="784dp"
        android:layout_height="533dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/dialog_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="@dimen/dp_94"
            android:gravity="center"
            android:maxLength="32"
            android:textSize="@dimen/sp_32"
            android:textColor="@color/black"
            android:text="@string/wifi_hotspot_password_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/dialog_tips_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="@dimen/dp_94"
            android:gravity="center"
            android:maxLength="32"
            android:text="@string/dialog_tips_tv"
            android:textColor="@color/black_transparent_40"
            android:textSize="21sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/constraintLayout" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="581dp"
            android:layout_height="80dp"
            android:layout_marginTop="101dp"
            android:layout_marginBottom="180dp"
            android:background="@drawable/shape_bg_black_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <EditText
                android:id="@+id/dialog_hotspot_password_input_et"
                android:layout_width="520dp"
                android:layout_height="match_parent"
                android:background="@android:color/transparent"
                android:focusable="true"
                android:textSize="24sp"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:hint="@string/wifi_connected_hint_text"
                android:textColor="@color/black"
                android:textColorHint="@color/color_transparent_40"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.491"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0" />

            <ImageView
                android:id="@+id/dialog_wifi_icon_iv"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginEnd="33dp"
                android:src="@drawable/icon_set_light_delete"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <Button
            android:id="@+id/dialog_hotspot_connected_btn"
            android:layout_width="267dp"
            android:layout_height="67dp"
            android:layout_marginStart="80dp"
            android:layout_marginBottom="50dp"
            android:background="@drawable/shape_bg_blue_s"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/dialog_hotspot_cancel_btn"
            android:layout_width="267dp"
            android:layout_height="67dp"
            android:layout_marginBottom="50dp"
            android:layout_marginEnd="80dp"
            android:background="@drawable/shape_bg_white"
            android:text="@string/dialog_cancel_text"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.bitech.vehiclesettings.view.widget.ScanScrollView>