<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="1056dp"
    android:layout_height="530.67dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="33.33dp"
        android:fontFamily="DreamHanSansCN"
        android:text="@string/str_system_manually_calibrate"
        android:textColor="@color/selector_text_color"
        android:textSize="@dimen/font_48px" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp">

                <FrameLayout
                    android:layout_width="432dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="80dp">

                    <Button
                        style="?android:borderlessButtonStyle"
                        android:layout_width="432dp"
                        android:layout_height="64dp"
                        android:background="@drawable/button_card" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="134.67dp"
                        android:text="年"
                        android:textColor="@color/color_time"
                        android:textSize="@dimen/font_24px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="256.67dp"
                        android:text="月"
                        android:textColor="@color/color_time"
                        android:textSize="@dimen/font_24px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="372dp"
                        android:text="日"
                        android:textColor="@color/color_time"
                        android:textSize="@dimen/font_24px" />
                </FrameLayout>

                <Button
                    style="?android:borderlessButtonStyle"
                    android:layout_width="432dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="544dp"
                    android:background="@drawable/button_card" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="858dp"
                    android:text=":"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_24px" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="203.33dp"
                    android:orientation="horizontal">

                    <com.shawnlin.numberpicker.NumberPicker
                        android:id="@+id/npYear"
                        android:layout_width="67dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="134.67dp"
                        android:layout_marginTop="15dp"
                        app:np_dividerColor="@color/transparent"
                        app:np_selectedTextColor="@color/black"
                        app:np_textColor="@color/black_transparent_40" />

                    <com.shawnlin.numberpicker.NumberPicker
                        android:id="@+id/npMonth"
                        android:layout_width="28dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="98.67dp"
                        android:layout_marginTop="15dp"
                        app:np_dividerColor="@color/transparent"
                        app:np_selectedTextColor="@color/black"
                        app:np_textColor="@color/black_transparent_40" />

                    <com.shawnlin.numberpicker.NumberPicker
                        android:id="@+id/npDay"
                        android:layout_width="28dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="87.33dp"
                        android:layout_marginTop="15dp"
                        app:np_dividerColor="@color/transparent"
                        app:np_selectedTextColor="@color/black"
                        app:np_textColor="@color/black_transparent_40" />

                    <com.shawnlin.numberpicker.NumberPicker
                        android:id="@+id/npAmPm"
                        android:layout_width="48dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="164.67dp"
                        android:layout_marginTop="15dp"
                        app:np_dividerColor="@color/transparent"
                        app:np_selectedTextColor="@color/black"
                        app:np_textColor="@color/black_transparent_40" />

                    <com.shawnlin.numberpicker.NumberPicker
                        android:id="@+id/npHour"
                        android:layout_width="28dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="145.33dp"
                        android:layout_marginTop="15dp"
                        app:np_dividerColor="@color/transparent"
                        app:np_selectedTextColor="@color/black"
                        app:np_textColor="@color/black_transparent_40" />

                    <com.shawnlin.numberpicker.NumberPicker
                        android:id="@+id/npMinute"
                        android:layout_width="28dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="86.67dp"
                        android:layout_marginTop="15dp"
                        app:np_dividerColor="@color/transparent"
                        app:np_selectedTextColor="@color/black"
                        app:np_textColor="@color/black_transparent_40" />

                </LinearLayout>


            </FrameLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="52dp">

                <Button
                    android:id="@+id/btn_confirm"
                    style="?android:borderlessButtonStyle"
                    android:layout_width="432dp"
                    android:layout_height="64dp"
                    android:background="@drawable/button_blue"
                    android:text="@string/str_confirm2"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_36px" />

                <Button
                    android:id="@+id/btn_cancel"
                    style="?android:borderlessButtonStyle"
                    android:layout_width="432dp"
                    android:layout_height="64dp"
                    android:layout_marginStart="50.67dp"
                    android:background="@drawable/button_white"
                    android:text="@string/str_cancel"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50.67dp" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>
