<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.widget.ScanScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/settings_wifi_ssv"
    style="@style/settings_scroll_bar_style"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    tools:context=".view.connect.WifiFragment">
    
    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/dialog_background_984_520_style"
        android:layout_width="784dp"
        android:layout_height="347dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/dialog_title_tv"
            style="@style/settings_text_40_medium_17191e_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="34dp"
            android:layout_marginEnd="@dimen/dp_94"
            android:gravity="center"
            android:maxLength="32"
            android:textSize="28sp"
            android:text="@string/wifi_input_password_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/editCll"
            android:layout_width="624dp"
            android:layout_height="80dp"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="51dp"
            android:background="@drawable/shape_bg_white"
            app:layout_constraintBottom_toTopOf="@id/dialog_connected_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dialog_title_tv">

            <EditText
                android:id="@+id/dialog_password_input_et"
                android:layout_width="520dp"
                android:layout_height="match_parent"
                android:layout_marginStart="30dp"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:background="@android:color/transparent"
                android:textColor="@color/black"
                android:hint="密码"
                android:textColorHint="@color/black_transparent_40"
                android:inputType="textPassword"
                android:textSize="24sp"
                android:maxLength="32"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/dialog_wifi_icon_iv"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginEnd="33dp"
                android:src="@mipmap/img_closeeyes"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/errorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="密码不正确，连接失败"
            android:textColor="@color/colorRed"
            android:layout_marginStart="80dp"
            android:textSize="21sp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/editCll" />

        <Button
            android:id="@+id/dialog_connected_btn"
            android:layout_width="267dp"
            android:layout_height="67dp"
            android:background="@drawable/shape_bg_blue_s"
            android:layout_marginStart="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="51dp"
            android:enabled="false"
            android:text="@string/dialog_confirm_wifi_text"
            android:textSize="@dimen/sp_24"
            android:textColor="@color/white_s"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/dialog_cancel_btn"
            app:layout_constraintStart_toStartOf="parent" />
        
        <Button
            android:id="@+id/dialog_cancel_btn"
            android:layout_width="267dp"
            android:layout_height="67dp"
            android:background="@drawable/shape_bg_white"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_60"
            android:text="@string/dialog_cancel_text"
            android:textSize="@dimen/sp_24"
            android:textColor="@color/black"
            app:layout_constraintBottom_toBottomOf="@id/dialog_connected_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/dialog_connected_btn"
            app:layout_constraintTop_toTopOf="@id/dialog_connected_btn" />
    
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.bitech.vehiclesettings.view.widget.ScanScrollView>