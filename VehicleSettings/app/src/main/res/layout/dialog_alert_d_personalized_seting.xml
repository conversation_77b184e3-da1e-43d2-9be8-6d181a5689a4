<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1328dp"
    android:layout_height="780dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="1168dp"
                android:layout_height="101.33dp"
                android:layout_marginStart="80dp"
                android:orientation="vertical">
                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="33.33dp"
                    android:fontFamily="DreamHanSansCN"
                    android:text="@string/str_driving_car_mode_6_setting"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_48px" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="80dp"
                android:layout_marginTop="-14dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_driving_mode"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_32px" />

            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/spv_driving_personalized_mode"
                android:layout_marginStart="80dp"
                android:layout_marginTop="16dp"
                android:layout_width="1168dp"
                android:background="@color/color_driving_personal"
                android:layout_height="64dp"/>
            <LinearLayout
                android:layout_marginStart="80dp"
                android:layout_marginTop="21.34dp"
                android:layout_width="match_parent"
                android:layout_height="194.67dp"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="368dp"
                    android:layout_height="194.67dp"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_marginTop="18dp"
                        android:layout_height="30.67dp"
                        android:text="@string/str_driving_car_mode_6_setting_suspension"
                        android:textSize="@dimen/font_36px"
                        android:textColor="@color/black_transparent_60"/>
                    <LinearLayout
                        android:layout_width="368dp"
                        android:layout_height="128dp"
                        android:layout_marginTop="18dp">
                        <RadioGroup
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:background="@drawable/shape_bg_driving_personal"
                                android:orientation="vertical">
                                <LinearLayout
                                    android:id="@+id/ll_suspension_comfort"
                                    android:layout_width="match_parent"
                                    android:layout_height="64dp">
                                    <TextView
                                        android:layout_marginStart="32.67dp"
                                        android:layout_width="165.33dp"
                                        android:layout_height="64dp"
                                        android:gravity="center_vertical"
                                        android:text="@string/str_driving_car_mode_6_setting_comfort"
                                        android:textColor="@color/black"
                                        android:textSize="@dimen/font_36px"/>
                                    <RadioButton
                                        android:id="@+id/rb_suspension_comfort"
                                        android:layout_width="32dp"
                                        android:layout_height="32dp"
                                        android:background="@drawable/radio_driving_personal"
                                        android:button="@null"
                                        android:checked="true"
                                        android:layout_gravity="center"
                                        android:layout_marginLeft="106.67dp"/>
                                </LinearLayout>
                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:layout_marginLeft="32dp"
                                    android:layout_marginRight="32dp"
                                    android:background="@color/color_dividing_line" />
                                <LinearLayout
                                    android:id="@+id/ll_suspension_sport"
                                    android:layout_width="match_parent"
                                    android:layout_height="64dp">
                                    <TextView
                                        android:layout_marginStart="32.67dp"
                                        android:layout_width="165.33dp"
                                        android:layout_height="64dp"
                                        android:gravity="center_vertical"
                                        android:text="@string/str_driving_car_mode_6_setting_movement"
                                        android:textColor="@color/black"
                                        android:textSize="@dimen/font_36px"/>
                                    <RadioButton
                                        android:id="@+id/rb_suspension_sport"
                                        android:layout_width="32dp"
                                        android:layout_height="32dp"
                                        android:background="@drawable/radio_driving_personal"
                                        android:button="@null"
                                        android:checked="false"
                                        android:layout_gravity="center"
                                        android:layout_marginLeft="106.67dp"/>

                                </LinearLayout>

                            </LinearLayout>
                        </RadioGroup>
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_marginStart="32dp"
                    android:layout_width="368dp"
                    android:layout_height="194.67dp"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="368dp"
                        android:layout_height="194.67dp"
                        android:orientation="vertical">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_marginTop="18dp"
                            android:layout_height="30.67dp"
                            android:text="@string/str_driving_car_mode_6_setting_swerve"
                            android:textSize="@dimen/font_36px"
                            android:textColor="@color/black_transparent_60"/>
                        <LinearLayout
                            android:layout_width="368dp"
                            android:layout_height="128dp"
                            android:layout_marginTop="18dp">
                            <RadioGroup
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical">
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:background="@drawable/shape_bg_driving_personal"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:id="@+id/ll_swerve_mode_comfort"
                                        android:layout_width="match_parent"
                                        android:layout_height="64dp">

                                        <TextView
                                            android:layout_width="165.33dp"
                                            android:layout_height="64dp"
                                            android:layout_marginStart="32.67dp"
                                            android:gravity="center_vertical"
                                            android:text="@string/str_driving_car_mode_6_setting_comfort"
                                            android:textColor="@color/black"
                                            android:textSize="@dimen/font_36px" />

                                        <RadioButton
                                            android:id="@+id/rb_swerve_mode_comfort"
                                            android:layout_width="32dp"
                                            android:layout_height="32dp"
                                            android:layout_gravity="center"
                                            android:layout_marginLeft="106.67dp"
                                            android:background="@drawable/radio_driving_personal"
                                            android:button="@null"
                                            android:checked="true" />
                                    </LinearLayout>

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="1dp"
                                        android:layout_marginLeft="32dp"
                                        android:layout_marginRight="32dp"
                                        android:background="@color/color_dividing_line" />
                                    <LinearLayout
                                        android:id="@+id/ll_swerve_mode_sport"
                                        android:layout_width="match_parent"
                                        android:layout_height="64dp">
                                        <TextView
                                            android:layout_marginStart="32.67dp"
                                            android:layout_width="165.33dp"
                                            android:layout_height="64dp"
                                            android:gravity="center_vertical"
                                            android:text="@string/str_driving_car_mode_6_setting_movement"
                                            android:textColor="@color/black"
                                            android:textSize="@dimen/font_36px"/>
                                        <RadioButton
                                            android:id="@+id/rb_swerve_mode_sport"
                                            android:layout_width="32dp"
                                            android:layout_height="32dp"
                                            android:background="@drawable/radio_driving_personal"
                                            android:button="@null"
                                            android:checked="false"
                                            android:layout_gravity="center"
                                            android:layout_marginLeft="106.67dp"/>
                                    </LinearLayout>
                                </LinearLayout>
                            </RadioGroup>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_marginStart="32dp"
                    android:layout_width="368dp"
                    android:layout_height="194.67dp"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="368dp"
                        android:layout_height="194.67dp"
                        android:orientation="vertical">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_marginTop="18dp"
                            android:layout_height="30.67dp"
                            android:text="@string/str_driving_car_mode_6_setting_immobilize"
                            android:textSize="@dimen/font_36px"
                            android:textColor="@color/black_transparent_60"/>
                        <LinearLayout
                            android:layout_width="368dp"
                            android:layout_height="128dp"
                            android:layout_marginTop="18dp">
                            <RadioGroup
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical">
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:background="@drawable/shape_bg_driving_personal"
                                    android:orientation="vertical">
                                    <LinearLayout
                                        android:id="@+id/ll_immobilize_mode_comfort"
                                        android:layout_width="match_parent"
                                        android:layout_height="64dp">
                                        <TextView
                                            android:layout_marginStart="32.67dp"
                                            android:layout_width="165.33dp"
                                            android:layout_height="64dp"
                                            android:gravity="center_vertical"
                                            android:text="@string/str_driving_car_mode_6_setting_comfort"
                                            android:textColor="@color/black"
                                            android:textSize="@dimen/font_36px"/>
                                        <RadioButton
                                            android:id="@+id/rb_immobilize_mode_comfort"
                                            android:layout_width="32dp"
                                            android:layout_height="32dp"
                                            android:background="@drawable/radio_driving_personal"
                                            android:button="@null"
                                            android:checked="true"
                                            android:layout_gravity="center"
                                            android:layout_marginLeft="106.67dp"/>
                                    </LinearLayout>
                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="1dp"
                                        android:layout_marginLeft="32dp"
                                        android:layout_marginRight="32dp"
                                        android:background="@color/color_dividing_line" />
                                    <LinearLayout
                                        android:id="@+id/ll_immobilize_mode_sport"
                                        android:layout_width="match_parent"
                                        android:layout_height="64dp">
                                        <TextView
                                            android:layout_marginStart="32.67dp"
                                            android:layout_width="165.33dp"
                                            android:layout_height="64dp"
                                            android:gravity="center_vertical"
                                            android:text="@string/str_driving_car_mode_6_setting_movement"
                                            android:textColor="@color/black"
                                            android:textSize="@dimen/font_36px"/>
                                        <RadioButton
                                            android:id="@+id/rb_immobilize_mode_sport"
                                            android:layout_width="32dp"
                                            android:layout_height="32dp"
                                            android:background="@drawable/radio_driving_personal"
                                            android:button="@null"
                                            android:checked="false"
                                            android:layout_gravity="center"
                                            android:layout_marginLeft="106.67dp"/>
                                    </LinearLayout>
                                </LinearLayout>
                            </RadioGroup>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
            <!--            保电电量-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_marginStart="80dp"
                android:layout_marginTop="39.33dp"
                android:text="@string/str_driving_car_mode_6_setting_save_elect"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />
            <RelativeLayout
                android:layout_width="1168dp"
                android:layout_height="101.33dp"
                android:layout_marginStart="80dp"
                android:layout_marginTop="18dp">
                <com.bitech.vehiclesettings.view.common.ImageSeekBarView
                    android:id="@+id/isbv_iv_driving_roate"
                    android:layout_width="1168dp"
                    android:layout_height="64dp"
                    android:layout_centerHorizontal="true"
                    app:max="60"
                    app:progress="0"
                    app:seekBarWidth="1168dp"
                    app:seekBarHeight="64dp">

                    <LinearLayout
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:gravity="center">
                        <ImageView
                            android:id="@+id/iv_hud_high"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:background="@mipmap/icon_set_dark_batterys"/>
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_driving_power_protection"
                        android:layout_width="94.67dp"
                        android:layout_height="64dp"
                        android:layout_gravity="right"
                        android:gravity="center"
                        android:text="20%"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </com.bitech.vehiclesettings.view.common.ImageSeekBarView>

                <RelativeLayout
                    android:layout_width="1168dp"
                    android:layout_height="30.67dp"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginStart="21.33dp"
                    android:layout_marginEnd="21.33dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentLeft="true"
                        android:gravity="center"
                        android:text="20%"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:gravity="center"
                        android:text="80%"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                </RelativeLayout>

            </RelativeLayout>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="50.67dp">

                <Button
                    android:id="@+id/btn_confirm"
                    style="?android:borderlessButtonStyle"
                    android:layout_width="568dp"
                    android:layout_height="64dp"
                    android:background="@drawable/button_blue"
                    android:text="@string/str_confirm2"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_36px" />

                <Button
                    android:id="@+id/btn_cancel"
                    style="?android:borderlessButtonStyle"
                    android:layout_width="568dp"
                    android:layout_height="64dp"
                    android:layout_marginStart="32dp"
                    android:background="@drawable/button_white"
                    android:text="@string/str_cancel"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="45.33dp" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>
