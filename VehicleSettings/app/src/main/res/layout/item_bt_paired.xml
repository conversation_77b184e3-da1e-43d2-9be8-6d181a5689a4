<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/bt_cl_paired"
    android:layout_width="624dp"
    android:layout_height="107dp"
    android:layout_marginBottom="16dp"
    android:layout_marginStart="80dp"
    android:background="@drawable/shape_bg_white">
    
    <ImageView
        android:id="@+id/bt_icon_iv"
        style="@style/settings_icon_48x48_style"
        android:layout_marginStart="32dp"
        android:src="@drawable/image_button_bt_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/border_bg_preferences"
        android:layout_width="66dp"
        android:layout_height="40dp"
        android:layout_marginStart="20dp"
        android:gravity="center"
        android:textSize="24sp"
        android:textColor="@color/blue"
        android:text="@string/bt_preferences_text"
        android:background="@drawable/border_bg_preferences"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/bt_icon_iv"
        app:layout_constraintTop_toTopOf="parent" />
    
    <TextView
        android:id="@+id/bt_name_tv"
        android:layout_width="230dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="@dimen/dp_36"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textSize="24sp"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@id/bt_connected_state_tv"
        app:layout_constraintStart_toEndOf="@id/border_bg_preferences"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/bt_connected_state_tv"
        android:layout_width="240dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/black_transparent_40"
        android:textSize="19sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/bt_name_tv"
        app:layout_constraintTop_toBottomOf="@id/bt_name_tv" />

    <ImageView
        android:id="@+id/bt_carplay_iv"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:src="@mipmap/image_button_bt_carplay"
        android:layout_marginEnd="36dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/bt_music_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/bt_carlink_iv"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:src="@mipmap/image_button_bt_carlink"
        android:layout_marginEnd="36dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/bt_music_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/bt_hicar_iv"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:src="@mipmap/image_button_bt_hicar"
        android:layout_marginEnd="36dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/bt_music_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/bt_phone_iv"
        style="@style/settings_icon_48x48_style"
        android:layout_marginEnd="44dp"
        android:src="@drawable/image_button_bt_phone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/bt_delete_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/bt_music_iv"
        style="@style/settings_icon_48x48_style"
        android:layout_marginEnd="44dp"
        android:src="@drawable/image_button_bt_music"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/bt_phone_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/bt_delete_iv"
        style="@style/settings_icon_48x48_style"
        android:layout_marginEnd="32dp"
        android:src="@mipmap/icon_list_del_n_48"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    
    <ProgressBar
        android:id="@+id/bt_loading_pb"
        style="@style/settings_icon_48x48_style"
        android:layout_marginEnd="@dimen/dp_36"
        android:indeterminate="true"
        android:indeterminateDrawable="@drawable/progress_bar_bt_scan_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
