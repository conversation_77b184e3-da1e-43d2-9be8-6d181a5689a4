<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_recognition_camera"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <WebView
                android:id="@+id/webView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="33.33dp"
                android:layout_marginStart="33.33dp"
                android:layout_marginEnd="33.33dp"
                tools:ignore="WebViewLayout" />

            <TextView
                android:id="@+id/errorText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/str_system_basic_privacy_agreement_error"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:gravity="center"
                android:visibility="gone" />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>