<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="enableConfirm"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/border_bg_dialog"
        android:paddingHorizontal="@dimen/px_120"
        android:paddingBottom="@dimen/px_76">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_152"
            android:gravity="center"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_48"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="match_parent"
            android:layout_height="@dimen/px_100"
            android:gravity="center"
            android:text="@string/ne_book_charge_error"
            android:textColor="@color/text_color_error"
            android:textSize="@dimen/pxt_32"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle" />

        <TextView
            android:id="@+id/tvStartText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_29"
            android:text="@string/ne_start_time"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_40"
            app:layout_constraintEnd_toEndOf="@id/clStartTime"
            app:layout_constraintStart_toStartOf="@id/clStartTime"
            app:layout_constraintTop_toBottomOf="@id/tvTips" />

        <TextView
            android:id="@+id/tvChargingDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ne_charging_duration"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_40"
            app:layout_constraintBottom_toBottomOf="@id/tvStartText"
            app:layout_constraintEnd_toEndOf="@id/clChargeDuration"
            app:layout_constraintStart_toStartOf="@id/clChargeDuration"
            app:layout_constraintTop_toTopOf="@id/tvStartText" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clStartTime"
            android:layout_width="@dimen/px_648"
            android:layout_height="@dimen/px_310"
            android:layout_marginTop="@dimen/px_54"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvStartText">

            <View
                android:id="@+id/bgTimeSelected"
                android:layout_width="0dp"
                android:layout_height="@dimen/px_96"
                app:drawable_radius="@{@dimen/px_16}"
                app:drawable_solidColor="@{@color/color_white_50}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/px_14"
                android:text=":"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_48"
                app:layout_constraintBottom_toBottomOf="@id/bgTimeSelected"
                app:layout_constraintEnd_toStartOf="@id/npMinute"
                app:layout_constraintStart_toEndOf="@id/npHour"
                app:layout_constraintTop_toTopOf="@id/bgTimeSelected" />

            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/npAmPm"
                android:layout_width="@dimen/px_230"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/npHour"
                app:np_dividerColor="@color/transparent"
                app:np_dividerType="underline"
                app:np_fadingEdgeEnabled="false"
                app:np_itemSpacing="@dimen/px_16"
                app:np_selectedTextColor="@color/text_color_1"
                app:np_selectedTextSize="@dimen/pxt_48"
                app:np_textColor="@color/text_color_2"
                app:np_textSize="@dimen/pxt_40" />

            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/npHour"
                android:layout_width="@dimen/px_172"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/px_74"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/npAmPm"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginStart="@dimen/px_152"
                app:np_dividerColor="@color/transparent"
                app:np_dividerType="underline"
                app:np_fadingEdgeEnabled="false"
                app:np_itemSpacing="@dimen/px_16"
                app:np_selectedTextColor="@color/text_color_1"
                app:np_selectedTextSize="@dimen/pxt_48"
                app:np_textColor="@color/text_color_2"
                app:np_textSize="@dimen/pxt_40" />

            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/npMinute"
                android:layout_width="@dimen/px_172"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/npHour"
                app:layout_constraintTop_toTopOf="@id/npHour"
                app:np_dividerColor="@color/transparent"
                app:np_dividerType="underline"
                app:np_fadingEdgeEnabled="false"
                app:np_itemSpacing="@dimen/px_16"
                app:np_selectedTextColor="@color/text_color_1"
                app:np_selectedTextSize="@dimen/pxt_48"
                app:np_textColor="@color/text_color_2"
                app:np_textSize="@dimen/pxt_40" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clChargeDuration"
            android:layout_width="@dimen/px_648"
            android:layout_height="@dimen/px_310"
            app:layout_constraintBottom_toBottomOf="@id/clStartTime"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/clStartTime">

            <View
                android:id="@+id/bgDurationSelected"
                android:layout_width="0dp"
                android:layout_height="@dimen/px_96"
                app:drawable_radius="@{@dimen/px_16}"
                app:drawable_solidColor="@{@color/color_white_50}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_charging_remainder_hour"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_24"
                app:layout_constraintBottom_toBottomOf="@id/bgDurationSelected"
                app:layout_constraintStart_toEndOf="@id/npDuration"
                app:layout_constraintTop_toTopOf="@id/bgDurationSelected" />

            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/npDuration"
                android:layout_width="@dimen/px_98"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginStart="@dimen/px_169"
                app:np_dividerColor="@color/transparent"
                app:np_dividerType="underline"
                app:np_fadingEdgeEnabled="false"
                app:np_itemSpacing="@dimen/px_16"
                app:np_selectedTextColor="@color/text_color_1"
                app:np_selectedTextSize="@dimen/pxt_48"
                app:np_textColor="@color/text_color_2"
                app:np_textSize="@dimen/pxt_40" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <Button
            android:id="@+id/btnConfirm"
            android:layout_width="@dimen/px_648"
            android:layout_height="@dimen/px_96"
            android:alpha="@{enableConfirm ? 1.0f : 0.5f}"
            android:enabled="@{enableConfirm}"
            android:text="@string/dialog_ok_text"
            android:textColor="@color/white"
            android:textSize="@dimen/pxt_36"
            app:drawable_radius="@{@dimen/px_16}"
            app:drawable_solidColor="@{@color/blue}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="@dimen/px_648"
            android:layout_height="@dimen/px_96"
            android:text="@string/dialog_cancel_text"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_36"
            app:drawable_radius="@{@dimen/px_16}"
            app:drawable_solidColor="@{@color/bg_solid_color_1}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>