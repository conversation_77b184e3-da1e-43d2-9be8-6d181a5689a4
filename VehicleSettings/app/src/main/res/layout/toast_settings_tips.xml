<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="@dimen/px_196">

            <LinearLayout
                android:id="@+id/toast_common_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_toast"
                android:gravity="center"
                android:paddingHorizontal="@dimen/px_48"
                android:paddingVertical="@dimen/px_24">

                <TextView
                    android:id="@+id/tvToastText"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="start|center_vertical"
                    android:textColor="@color/text_color_1"
                    android:textSize="@dimen/px_36"
                    tools:text="toast" />

            </LinearLayout>
        </FrameLayout>
    </FrameLayout>
</layout>