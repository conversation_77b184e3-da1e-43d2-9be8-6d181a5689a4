<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1274.67dp"
    android:layout_height="720dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="35.33dp"
        android:text="@string/str_sound_equalizer_desc"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px" />

    <RelativeLayout
        android:id="@+id/rl_bg_effect"
        android:layout_width="match_parent"
        android:layout_height="462dp"
        android:layout_alignParentLeft="true"
        android:layout_marginTop="52dp"
        android:alpha="1"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/ll_effect_lab"
            android:layout_width="66.67dp"
            android:layout_height="361.33dp"
            android:layout_marginLeft="85.33dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="0dp"
                android:gravity="center"
                android:text="+7db"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_34px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="166.67dp"
                android:gravity="center"
                android:text="0"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_34px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="333.33dp"
                android:gravity="center"
                android:text="-7db"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_34px" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_volume_1"
            android:layout_width="128.33dp"
            android:layout_height="461.33dp"
            android:layout_marginLeft="20dp"
            android:layout_toRightOf="@id/ll_effect_lab">

            <com.bitech.vehiclesettings.view.voice.VerticalSeekBar
                android:id="@+id/sbSubBass"
                android:layout_width="32dp"
                android:layout_height="360dp"
                android:layout_centerHorizontal="true"
                android:max="14"
                android:maxHeight="13.33dp"
                android:minHeight="13.33dp"
                android:paddingStart="13dp"
                android:paddingEnd="13dp"
                android:progress="10"
                android:progressDrawable="@drawable/seekbar_progress_vertical"
                android:splitTrack="false"
                android:thumb="@mipmap/ic_sound_slider" />

            <TextView
                android:id="@+id/tvSubBass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="380.67dp"
                android:text="@string/str_sound_equalizer_sub_bass"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="429.33dp"
                android:gravity="center"
                android:text="@string/str_sound_equalizer_sub_bass"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_36px" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_volume_2"
            android:layout_width="128.33dp"
            android:layout_height="461.33dp"
            android:layout_marginLeft="14.67dp"

            android:layout_toRightOf="@+id/rl_volume_1">

            <com.bitech.vehiclesettings.view.voice.VerticalSeekBar
                android:id="@+id/sbBass"
                android:layout_width="32dp"
                android:layout_height="360dp"
                android:layout_centerHorizontal="true"
                android:max="14"
                android:maxHeight="13.33dp"
                android:minHeight="13.33dp"
                android:paddingStart="13dp"
                android:paddingEnd="13dp"
                android:progress="13"
                android:progressDrawable="@drawable/seekbar_progress_vertical"
                android:splitTrack="false"
                android:thumb="@mipmap/ic_sound_slider" />

            <TextView
                android:id="@+id/tvBass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="380.67dp"
                android:text="@string/str_sound_equalizer_bass"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="429.33dp"
                android:gravity="center"
                android:text="@string/str_sound_equalizer_bass"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_36px" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_volume_3"
            android:layout_width="128.33dp"
            android:layout_height="461.33dp"
            android:layout_marginLeft="14.67dp"

            android:layout_toRightOf="@+id/rl_volume_2">

            <com.bitech.vehiclesettings.view.voice.VerticalSeekBar
                android:id="@+id/sbLowMid"
                android:layout_width="32dp"
                android:layout_height="360dp"
                android:layout_centerHorizontal="true"
                android:max="14"
                android:maxHeight="13.33dp"
                android:minHeight="13.33dp"
                android:paddingStart="13dp"
                android:paddingEnd="13dp"
                android:progress="8"
                android:progressDrawable="@drawable/seekbar_progress_vertical"
                android:splitTrack="false"
                android:thumb="@mipmap/ic_sound_slider" />

            <TextView
                android:id="@+id/tvLowMid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="380.67dp"
                android:text="@string/str_sound_equalizer_low_mid"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="429.33dp"
                android:gravity="center"
                android:text="@string/str_sound_equalizer_low_mid"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_36px" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_volume_4"
            android:layout_width="128.33dp"
            android:layout_height="461.33dp"
            android:layout_marginLeft="14.67dp"

            android:layout_toRightOf="@+id/rl_volume_3">

            <com.bitech.vehiclesettings.view.voice.VerticalSeekBar
                android:id="@+id/sbMid"
                android:layout_width="32dp"
                android:layout_height="360dp"
                android:layout_centerHorizontal="true"
                android:max="14"
                android:maxHeight="13.33dp"
                android:minHeight="13.33dp"
                android:paddingStart="13dp"
                android:paddingEnd="13dp"
                android:progress="15"
                android:progressDrawable="@drawable/seekbar_progress_vertical"
                android:splitTrack="false"
                android:thumb="@mipmap/ic_sound_slider" />

            <TextView
                android:id="@+id/tvMid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="380.67dp"
                android:text="@string/str_sound_equalizer_mid"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="429.33dp"
                android:gravity="center"
                android:text="@string/str_sound_equalizer_mid"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_36px" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_volume_5"
            android:layout_width="128.33dp"
            android:layout_height="461.33dp"
            android:layout_marginLeft="14.67dp"

            android:layout_toRightOf="@+id/rl_volume_4">

            <com.bitech.vehiclesettings.view.voice.VerticalSeekBar
                android:id="@+id/sbHighMid"
                android:layout_width="32dp"
                android:layout_height="360dp"
                android:layout_centerHorizontal="true"
                android:max="14"
                android:maxHeight="13.33dp"
                android:minHeight="13.33dp"
                android:paddingStart="13dp"
                android:paddingEnd="13dp"
                android:progress="10"
                android:progressDrawable="@drawable/seekbar_progress_vertical"
                android:splitTrack="false"
                android:thumb="@mipmap/ic_sound_slider" />

            <TextView
                android:id="@+id/tvHighMid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="380.67dp"
                android:text="@string/str_sound_equalizer_high_mid"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="429.33dp"
                android:gravity="center"
                android:text="@string/str_sound_equalizer_high_mid"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_36px" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_volume_6"
            android:layout_width="128.33dp"
            android:layout_height="461.33dp"
            android:layout_marginLeft="14.67dp"
            android:layout_toRightOf="@+id/rl_volume_5">

            <com.bitech.vehiclesettings.view.voice.VerticalSeekBar
                android:id="@+id/sbTreble"
                android:layout_width="32dp"
                android:layout_height="360dp"
                android:layout_centerHorizontal="true"
                android:importantForAccessibility="yes"
                android:max="14"
                android:maxHeight="13.33dp"
                android:minHeight="13.33dp"
                android:paddingStart="13dp"
                android:paddingEnd="13dp"
                android:progress="8"
                android:progressDrawable="@drawable/seekbar_progress_vertical"
                android:splitTrack="false"
                android:thumb="@mipmap/ic_sound_slider" />

            <TextView
                android:id="@+id/tvTreble"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="380.67dp"
                android:text="@string/str_sound_equalizer_treble"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="429.33dp"
                android:gravity="center"
                android:text="@string/str_sound_equalizer_treble"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_36px" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_volume_7"
            android:layout_width="128.33dp"
            android:layout_height="461.33dp"
            android:layout_marginLeft="14.67dp"
            android:layout_toRightOf="@+id/rl_volume_6">

            <com.bitech.vehiclesettings.view.voice.VerticalSeekBar
                android:id="@+id/sbSuperTreble"
                android:layout_width="32dp"
                android:layout_height="360dp"
                android:layout_centerHorizontal="true"
                android:importantForAccessibility="yes"
                android:max="14"
                android:maxHeight="13.33dp"
                android:minHeight="13.33dp"
                android:paddingStart="13dp"
                android:paddingEnd="13dp"
                android:progress="8"
                android:progressDrawable="@drawable/seekbar_progress_vertical"
                android:splitTrack="false"
                android:thumb="@mipmap/ic_sound_slider" />

            <TextView
                android:id="@+id/tvSuperTreble"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="380.67dp"
                android:text="@string/str_sound_equalizer_super_treble"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="429.33dp"
                android:gravity="center"
                android:text="@string/str_sound_equalizer_super_treble"
                android:textColor="@color/color_transparent_60"
                android:textSize="@dimen/font_36px" />
        </RelativeLayout>

    </RelativeLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="101.33dp" />
</LinearLayout>