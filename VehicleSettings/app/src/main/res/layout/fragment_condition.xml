<?xml version="1.0" encoding="utf-8"?>

<com.bitech.vehiclesettings.view.common.BounceScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintTop_toBottomOf="parent">

    <LinearLayout
        android:layout_width="@dimen/dp_width"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_left"
        android:orientation="vertical">

        <!-- 顶部留白 -->
        <!--    车模-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- 左侧文字组 -->
            <LinearLayout
                android:id="@+id/left_text_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_right_front_tyre_pressure"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="66dp"
                    android:text="226kPa"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_right_front_tyre_temperature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:text="38℃"
                    android:textColor="@color/color_transparent_60"
                    android:textSize="@dimen/font_28px"/>

                <TextView
                    android:id="@+id/tv_left_front_tyre_pressure"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="118dp"
                    android:text="22kPa"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_left_front_tyre_temperature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:text="72℃"
                    android:textColor="@color/color_transparent_60"
                    android:textSize="@dimen/font_28px"/>
            </LinearLayout>

            <!-- 中间图片 -->
            <FrameLayout
                android:id="@id/iv_test_car"
                android:layout_width="1021dp"
                android:layout_height="446.67dp"
                android:layout_centerHorizontal="true">
                <ImageView
                    android:id="@+id/iv_image_base"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/img_set_vehicle_condition_0"
                    android:scaleType="center"/>

                <ImageView
                    android:id="@+id/iv_image_left_front"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/img_set_vehicle_condition_1"
                    android:visibility="gone"
                    android:scaleType="center"/>
                <ImageView
                    android:id="@+id/iv_image_right_front"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/img_set_vehicle_condition_2"
                    android:visibility="gone"
                    android:scaleType="center"/>
                <ImageView
                    android:id="@+id/iv_image_right_rear"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/img_set_vehicle_condition_3"
                    android:visibility="gone"
                    android:scaleType="center"/>
                <ImageView
                    android:id="@+id/iv_image_left_rear"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/img_set_vehicle_condition_4"
                    android:visibility="gone"
                    android:scaleType="center"/>
                <ImageView
                    android:id="@+id/iv_car"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/img_set_vehicle_condition_5"
                    android:scaleType="center"/>
                <!-- 最多6个 -->
            </FrameLayout>

            <!-- 右侧文字组 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/iv_test_car"
                android:orientation="vertical"
                android:layout_marginLeft="15px">

                <TextView
                    android:id="@+id/tv_right_rear_tyre_pressure"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="66dp"
                    android:text="226kPa"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_right_rear_tyre_temperature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:text="38℃"
                    android:textColor="@color/color_transparent_60"
                    android:textSize="@dimen/font_28px"/>

                <TextView
                    android:id="@+id/tv_left_rear_tyre_pressure"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="118dp"
                    android:text="226kPa"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_left_rear_tyre_temperature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:text="38℃"
                    android:textColor="@color/color_transparent_60"
                    android:textSize="@dimen/font_28px"/>
            </LinearLayout>
        </RelativeLayout>

<!--        android:layout_width="1141.33dp"-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="41.33dp"
            android:orientation="horizontal">
            <!--距离下次保养-->
                <!--雨刮维修模式-->
                <RelativeLayout
                    android:id="@+id/rl_maintain_reset"
                    android:layout_width="592dp"
                    android:layout_height="120dp"
                    android:layout_row="0"
                    android:layout_column="0"
                    android:background="@drawable/shape_bg_white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="32dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_condition_next_maintenance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_36px" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tv_maintain_mileage"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/str_condition_next_maintenance_content_m"
                                android:textColor="@color/color_transparent_40"
                                android:textSize="@dimen/font_28px" />
                            <TextView
                                android:layout_marginStart="10dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="|"
                                android:textColor="@color/color_transparent_40"
                                android:textSize="@dimen/font_32px" />
                            <TextView
                                android:id="@+id/tv_maintain_time"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:text="@string/str_condition_next_maintenance_content_day"
                                android:textColor="@color/color_transparent_40"
                                android:textSize="@dimen/font_28px" />
                        </LinearLayout>
                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="84dp"
                        android:layout_marginRight="6dp"
                        android:src="@mipmap/ic_small_marker" />

                </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_repair_check"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_condition_repair_check"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_condition_repair_check_content"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_marginTop="21.33dp"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:orientation="horizontal">
            <!--能耗清单-->
            <RelativeLayout
                android:id="@+id/rlEnergyConsumptionList"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_condition_energy_consumption_list"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
            <!--道路救援-->
            <RelativeLayout
                android:id="@+id/rl_road_rescue"
                android:layout_width="592dp"
                android:layout_height="120dp"
                android:layout_marginLeft="32dp"
                android:background="@drawable/shape_bg_white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_condition_road_rescue"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_36px" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_condition_road_rescue_content"
                        android:textColor="@color/color_transparent_40"
                        android:textSize="@dimen/font_28px" />

                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="84dp"
                    android:layout_marginRight="6dp"
                    android:src="@mipmap/ic_small_marker" />

            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</com.bitech.vehiclesettings.view.common.BounceScrollView>
