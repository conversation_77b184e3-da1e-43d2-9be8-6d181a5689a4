<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginBottom="8dp">
    
    <RelativeLayout
        android:id="@+id/rl_menu"
        android:layout_width="314.67dp"
        android:layout_height="106.67dp"
        android:background="@color/transparent">
        
        <ImageView
            android:id="@+id/iv_menu"
            android:layout_width="42.67dp"
            android:layout_height="42.67dp"
            android:layout_marginLeft="32dp"
            android:layout_centerVertical="true"
            android:layout_alignParentLeft="true"/>

        <LinearLayout
            android:layout_width="181.33dp"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="23.33dp"
            android:layout_marginTop="23.33dp"
            android:layout_marginRight="32dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_menu_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="@dimen/dp_0"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_confirm2"
                android:textColor="@color/black"
                android:textSize="@dimen/font_36px" />

            <TextView
                android:id="@+id/tv_access_times"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_confirm2"
                android:textColor="@color/color_transparent_40"
                android:textSize="@dimen/font_32px" />

        </LinearLayout>
        
    </RelativeLayout>

</LinearLayout>
