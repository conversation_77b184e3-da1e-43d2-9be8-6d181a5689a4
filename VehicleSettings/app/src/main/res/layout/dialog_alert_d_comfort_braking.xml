<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="752dp"
    android:layout_height="533.33dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_driving_car_pedal_control_1"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />
            <LinearLayout
                android:layout_width="581.33dp"
                android:layout_height="106.66dp"
                android:layout_marginTop="32dp"
                android:layout_marginStart="85.33dp"
                android:background="@drawable/shape_bg_white">

                <Switch
                    android:id="@+id/sw_comfort_braking"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="32dp"
                    android:text="@string/str_driving_car_pedal_control_1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="98.67dp"
                android:layout_marginTop="32.67dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_comfort_braking_rank"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_32px" />

            <com.bitech.vehiclesettings.view.common.SegmentedPickerView
                android:id="@+id/spv_comfort_braking_rank"
                android:layout_marginStart="85.33dp"
                android:layout_marginTop="16dp"
                android:layout_width="581.33dp"
                android:layout_height="77.34dp"/>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
