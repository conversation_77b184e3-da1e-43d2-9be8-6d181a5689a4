<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="106.66dp"
    android:layout_marginLeft="0dp"
    android:layout_marginRight="0dp"
    android:layout_marginBottom="0dp"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/ll_function1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:background="@drawable/selector_menu"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_function_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="35.33dp" />

        <TextView
            android:id="@+id/tv_function_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="24dp"
            android:text="Basic information"
            android:textColor="@color/color_transparent_80"
            android:textSize="@dimen/font_40px" />
    </LinearLayout>

</LinearLayout>
