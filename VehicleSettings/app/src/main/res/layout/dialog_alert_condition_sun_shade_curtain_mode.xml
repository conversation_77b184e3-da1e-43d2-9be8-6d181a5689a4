<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="784dp"
    android:layout_height="426.67dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="101.33dp"
        android:layout_marginStart="80dp"
        android:orientation="horizontal"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_42px"
            android:textColor="@color/black"
            android:text="@string/str_sun_shade_curtain_repair_mode"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="194.67dp"
        android:layout_marginTop="16dp"
        android:layout_marginStart="80dp">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_36px"
            android:textColor="@color/black_transparent_60"
            android:text="@string/str_sun_shade_curtain_repair_dialog_content"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginEnd="80dp"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/selector_bg_open"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_condition_start_repair"
            android:textColor="#ffffff"
            android:textSize="@dimen/font_36px" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="32dp"
            android:background="@drawable/selector_bg_cancel"
            android:fontFamily="HarmonyOS_Sans_SC"
            android:gravity="center"
            android:text="@string/str_cancel"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
    </LinearLayout>
</LinearLayout>