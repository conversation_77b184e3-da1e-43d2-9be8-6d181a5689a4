<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="752dp"
    android:layout_height="810.67dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_system_zone_setting"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/npLanguage"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                app:np_dividerColor="@color/transparent"
                app:np_textColor="@color/black_transparent_40"
                app:np_selectedTextColor="@color/black"
                />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="52dp">

                <Button
                    android:id="@+id/btn_confirm"
                    style="?android:borderlessButtonStyle"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:background="@drawable/button_blue"
                    android:text="@string/str_confirm2"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_36px" />

                <Button
                    android:id="@+id/btn_cancel"
                    style="?android:borderlessButtonStyle"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:layout_marginStart="50.67dp"
                    android:background="@drawable/button_white"
                    android:text="@string/str_cancel"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="45.33dp" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>
