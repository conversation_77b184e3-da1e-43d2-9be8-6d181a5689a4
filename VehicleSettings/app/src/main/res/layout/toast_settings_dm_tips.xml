<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    
    <LinearLayout
        android:id="@+id/toast_tips_cl"
        style="@style/toast_background_style"
        android:layout_width="1004dp"
        android:layout_height="236dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        
        <TextView
            android:id="@+id/toast_tips_tv"
            style="@style/settings_text_40_regular_17191e_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="2" />
        
        <Button
            android:id="@+id/toast_ok_btn"
            style="@style/dialog_btn_confirm_223_72_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_80"
            android:layout_marginEnd="@dimen/dp_60"
            android:background="@drawable/switch_btn_223_72"
            android:text="切换" />
    
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
