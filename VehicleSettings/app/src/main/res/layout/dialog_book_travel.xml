<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="enableConfirm"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/border_bg_dialog"
        android:paddingHorizontal="@dimen/px_120"
        android:paddingTop="@dimen/px_50"
        android:paddingBottom="@dimen/px_76">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_48"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_52"
            android:gravity="center"
            android:text="@string/ne_book_trip_error"
            android:textColor="@color/text_color_error"
            android:textSize="@dimen/pxt_32"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle" />

        <TextView
            android:id="@+id/tvWeekText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ne_repeat_every_week"
            android:textColor="@color/text_color_2"
            android:textSize="@dimen/pxt_40"
            app:layout_constraintBottom_toBottomOf="@id/rvRepeatWeek"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/rvRepeatWeek" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvRepeatWeek"
            android:layout_width="0dp"
            android:layout_height="@dimen/px_68"
            android:layout_marginStart="@dimen/px_48"
            android:layout_marginTop="@dimen/px_44"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvWeekText"
            app:layout_constraintTop_toBottomOf="@id/tvTips"
            app:layout_goneMarginTop="@dimen/px_102"
            tools:listitem="@layout/item_ne_repeat_week" />

        <TextView
            android:id="@+id/tvStartText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/px_66"
            android:text="@string/ne_start_time"
            android:textColor="@color/text_color_2"
            android:textSize="@dimen/pxt_40"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rvRepeatWeek" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/px_600"
            android:layout_height="@dimen/px_424"
            android:layout_marginStart="@dimen/px_172"
            app:layout_constraintStart_toEndOf="@id/tvStartText"
            app:layout_constraintTop_toTopOf="@id/tvStartText">

            <TextView
                android:id="@+id/tvHourText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_hour"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_40"
                app:layout_constraintEnd_toEndOf="@id/npHour"
                app:layout_constraintStart_toStartOf="@id/npHour"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvMinuteText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ne_minute"
                android:textColor="@color/text_color_2"
                android:textSize="@dimen/pxt_40"
                app:layout_constraintEnd_toEndOf="@id/npMinute"
                app:layout_constraintStart_toStartOf="@id/npMinute"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/bgTimeSelected"
                android:layout_width="match_parent"
                android:layout_height="@dimen/px_120"
                android:layout_marginTop="@dimen/px_194"
                app:drawable_radius="@{@dimen/px_24}"
                app:drawable_solidColor="@{@color/color_white_50}"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/px_14"
                android:text=":"
                android:textColor="@color/text_color_1"
                android:textSize="@dimen/pxt_48"
                app:layout_constraintBottom_toBottomOf="@id/bgTimeSelected"
                app:layout_constraintEnd_toStartOf="@id/npMinute"
                app:layout_constraintStart_toEndOf="@id/npHour"
                app:layout_constraintTop_toTopOf="@id/bgTimeSelected" />

            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/npAmPm"
                android:layout_width="@dimen/px_236"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/npHour"
                app:np_dividerColor="@color/transparent"
                app:np_dividerType="underline"
                app:np_itemSpacing="@dimen/px_16"
                app:np_selectedTextColor="@color/text_color_1"
                app:np_selectedTextSize="@dimen/pxt_48"
                app:np_textColor="@color/text_color_2"
                app:np_textSize="@dimen/pxt_40" />

            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/npHour"
                android:layout_width="@dimen/px_98"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/px_84"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/npAmPm"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginStart="@dimen/px_169"
                app:np_dividerColor="@color/transparent"
                app:np_dividerType="underline"
                app:np_itemSpacing="@dimen/px_16"
                app:np_selectedTextColor="@color/text_color_1"
                app:np_selectedTextSize="@dimen/pxt_48"
                app:np_textColor="@color/text_color_2"
                app:np_textSize="@dimen/pxt_40" />

            <com.shawnlin.numberpicker.NumberPicker
                android:id="@+id/npMinute"
                android:layout_width="@dimen/px_98"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/px_66"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/npHour"
                app:layout_constraintTop_toTopOf="@id/npHour"
                app:np_dividerColor="@color/transparent"
                app:np_dividerType="underline"
                app:np_itemSpacing="@dimen/px_16"
                app:np_selectedTextColor="@color/text_color_1"
                app:np_selectedTextSize="@dimen/pxt_48"
                app:np_textColor="@color/text_color_2"
                app:np_textSize="@dimen/pxt_40" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <Button
            android:id="@+id/btnConfirm"
            android:layout_width="@dimen/px_648"
            android:layout_height="@dimen/px_96"
            android:alpha="@{enableConfirm ? 1.0f : 0.5f}"
            android:enabled="@{enableConfirm}"
            android:text="@string/dialog_ok_text"
            android:textColor="@color/white"
            android:textSize="@dimen/pxt_36"
            app:drawable_radius="@{@dimen/px_16}"
            app:drawable_solidColor="@{@color/blue}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="@dimen/px_648"
            android:layout_height="@dimen/px_96"
            android:text="@string/dialog_cancel_text"
            android:textColor="@color/text_color_1"
            android:textSize="@dimen/pxt_36"
            app:drawable_radius="@{@dimen/px_16}"
            app:drawable_solidColor="@{@color/bg_solid_color_1}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>