<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    
    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/dialog_background_984_520_style"
        android:layout_width="984dp"
        android:layout_height="520dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        
        <com.bitech.vehiclesettings.view.widget.ScanScrollView
            android:id="@+id/dialog_tips_ssv"
            style="@style/settings_scroll_bar_style2"
            android:layout_width="@dimen/dp_0"
            android:layout_height="212dp"
            android:layout_marginStart="@dimen/dp_94"
            android:layout_marginTop="@dimen/dp_78"
            android:layout_marginEnd="@dimen/dp_77"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_17"
                android:minHeight="212dp">
                
                <TextView
                    android:id="@+id/dialog_tips_tv"
                    style="@style/settings_text_40_regular_747578_style"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            
            </androidx.constraintlayout.widget.ConstraintLayout>
        
        </com.bitech.vehiclesettings.view.widget.ScanScrollView>
        
        <Button
            android:id="@+id/dialog_confirm_btn"
            style="@style/dialog_btn_confirm_402_92_style"
            android:layout_marginStart="@dimen/dp_60"
            android:layout_marginTop="@dimen/dp_78"
            android:layout_marginEnd="@dimen/dp_60"
            android:layout_marginBottom="@dimen/dp_60"
            android:text="@string/dialog_confirm_text"
            app:layout_constraintEnd_toStartOf="@id/dialog_cancel_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dialog_tips_ssv" />
        
        <Button
            android:id="@+id/dialog_cancel_btn"
            style="@style/dialog_btn_cancel_402_92_style"
            android:layout_marginEnd="@dimen/dp_60"
            android:text="@string/dialog_cancel_text"
            app:layout_constraintBottom_toBottomOf="@id/dialog_confirm_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/dialog_confirm_btn"
            app:layout_constraintTop_toTopOf="@id/dialog_confirm_btn" />
    
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
