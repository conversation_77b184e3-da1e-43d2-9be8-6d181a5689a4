<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="1056dp"
    android:layout_height="656dp"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="35.33dp"
        android:text="@string/str_steerinng_wheel_title"
        android:textColor="@color/black"
        android:textSize="@dimen/font_48px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="85.33dp"
        android:layout_marginTop="32dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">
        <!--left-->
        <com.bitech.vehiclesettings.view.common.BounceScrollView
            android:id="@+id/scrollView"
            style="@style/scroll_bar"
            android:layout_width="match_parent"
            android:layout_height="501.33dp">

            <LinearLayout
                android:layout_width="392dp"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_steel_mode_1"
                    android:layout_width="392dp"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_bg_blue"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_q_whell_ajuset_1"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_steel_mode_7"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_q_whell_ajuset_7"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_steel_mode_2"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_q_whell_ajuset_2"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_steel_mode_3"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_q_whell_ajuset_3"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_steel_mode_4"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_q_whell_ajuset_4"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_steel_mode_5"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_q_whell_ajuset_5"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_steel_mode_6"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:layout_marginTop="13.33dp"
                    android:background="@drawable/selector_bg_blue"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_q_whell_ajuset_6"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />
            </LinearLayout>


        </com.bitech.vehiclesettings.view.common.BounceScrollView>
        <ImageView
            android:id="@+id/iv_whell_background"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|center_vertical"
            android:src="@mipmap/ic_q_whell_comstum_pop_s" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>