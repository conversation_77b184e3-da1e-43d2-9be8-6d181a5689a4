<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_recognition_camera"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />


                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="581.33dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:fontFamily="DreamHanSansCN"
                    android:singleLine="false"
                    android:text="@string/str_recognition_camera_1"
                    android:textColor="@color/color_transparent_40"
                    android:textSize="@dimen/font_32px" />

            <com.bitech.vehiclesettings.view.common.SlideToConfirmView
                android:id="@+id/slideView"
                android:layout_width="581.33dp"
                android:layout_height="77.33dp"
                android:layout_marginTop="53.33dp"
                android:layout_gravity="center"
                app:text="@string/str_carsetting_safe_confirm_seekbar_text"
                app:successText="@string/str_carsetting_safe_confirm_seekbar_open"
                app:textColor="@color/black"
                app:successTextColor="@color/color_white"
                app:backgroundColor="@color/bg_seekbar_progress_1"
                app:progressColor="@color/bg_seekbar_progress_green"
                app:thumbImage="@mipmap/ic_seekbar_thumb"
                app:successThumbImage="@mipmap/ic_seekbar_thumb_success"
                app:textSize="@dimen/font_40px"
                app:thumbMargin="6.67dp"
                app:cornerRadius="8dp"
                app:thumbWidth="61.33dp"
                app:thumbHeight="64dp"/>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>