<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_system_permission_4"
                android:textColor="@color/selector_text_color"
                android:textSize="@dimen/font_48px" />

            <LinearLayout
                android:layout_width="1104dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="85.33dp"
                android:layout_marginRight="85.33dp"
                android:layout_marginTop="33.33dp"
                android:layout_marginBottom="45.33dp"
                android:gravity="center_horizontal"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <!--左侧菜单-->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_menu"
                    android:layout_width="314.66dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="8dp"
                    android:overScrollMode="never" />

                <!--右侧内容，增加NestedScrollView-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="48dp"
                    android:orientation="vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_record"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:overScrollMode="never"/>
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>
