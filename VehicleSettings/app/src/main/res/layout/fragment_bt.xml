<?xml version="1.0" encoding="utf-8"?>
<com.bitech.vehiclesettings.view.widget.ScanScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/settings_bt_ssv"
    style="@style/settings_scroll_bar_style"
    android:layout_width="784dp"
    android:layout_height="533dp"
    android:background="@drawable/border_bg_dialog"
    tools:context=".view.connect.BtFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/settings_bt_content_cl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_blue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="33dp"
            android:fontFamily="DreamHanSansCN"
            android:text="@string/str_blue"
            android:textColor="@color/black"
            android:textSize="28sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_bt_switch_cl"
            style="@style/settings_content_card_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="33dp"
            android:minHeight="120dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_bluetooth"
                android:layout_width="624dp"
                android:layout_height="107dp"
                android:layout_marginTop="73dp"
                android:layout_marginStart="80dp"
                android:background="@drawable/shape_bg_white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <Switch
                    android:id="@+id/setting_bt_sw"
                    style="@style/settings_switch_style"
                    android:layout_width="64dp"
                    android:layout_height="37.33dp"
                    android:layout_marginLeft="32dp"
                    android:layout_marginTop="36dp"
                    android:background="@color/transparent"
                    android:checked="true"
                    android:switchMinWidth="64dp"
                    android:thumb="@drawable/thumb"
                    android:track="@drawable/track"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />

                <TextView
                    android:id="@+id/setting_bt_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="128dp"
                    android:layout_marginTop="23dp"
                    android:text="@string/bt_switcher"
                    android:textColor="@color/black"
                    android:textSize="@dimen/sp_24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/setting_bt_name_et_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="128dp"
                    android:layout_marginTop="61dp"
                    android:ellipsize="end"
                    android:gravity="center|right"
                    android:maxLength="32"
                    android:maxLines="1"
                    android:text="@string/bt_name"
                    android:textColor="@color/black_transparent_40"
                    android:textSize="@dimen/sp_19"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/setting_bt_paired_tv"
            android:layout_width="550dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:layout_marginStart="80dp"
            android:text="@string/bt_paired_device"
            android:textColor="@color/black"
            android:textSize="21sp"
            app:layout_constraintBottom_toTopOf="@id/settings_paired_devices_cl"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_paired_devices_cl"
            style="@style/settings_content_card_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            app:layout_constraintTop_toBottomOf="@id/settings_bt_switch_cl"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bt_phonelink_paired"
                android:layout_width="624dp"
                android:layout_height="107dp"
                android:visibility="visible"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_bg_blue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/bt_icon_iv"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="32dp"
                    android:src="@mipmap/icon_carlink"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/border_bg_preferences"
                    android:layout_width="66dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="20dp"
                    android:background="@drawable/border_bg_preferences"
                    android:gravity="center"
                    android:text="@string/bt_preferences_text"
                    android:textColor="@color/blue"
                    android:textSize="24sp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/bt_icon_iv"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/bt_phonelink_name_tv"
                    android:layout_width="230dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/white_s"
                    android:textSize="24sp"
                    android:layout_marginBottom="35dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/border_bg_preferences"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/bt_connected_state_tv"
                    android:layout_width="240dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/white_s"
                    android:textSize="19sp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/bt_phonelink_name_tv"
                    app:layout_constraintTop_toBottomOf="@id/bt_phonelink_name_tv" />

                <ImageView
                    android:id="@+id/bt_icon"
                    android:layout_width="54dp"
                    android:layout_height="54dp"
                    android:src="@mipmap/icon_bt_white"
                    android:layout_marginEnd="30dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/bt_delete_iv"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/bt_delete_iv"
                    style="@style/settings_icon_48x48_style"
                    android:layout_marginEnd="32dp"
                    android:src="@mipmap/icon_list_del_n_48"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/settings_paired_devices_rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bt_phonelink_paired" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/settings_bt_use_devices_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="80dp"
            android:layout_marginBottom="16dp"
            android:gravity="center|left"
            android:text="@string/bt_can_use_devices"
            android:textColor="@color/black"
            android:textSize="21sp"
            app:layout_constraintBottom_toTopOf="@+id/settings_can_use_devices_cl"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/settings_bt_start_scan_iv" />

        <ProgressBar
            android:id="@+id/settings_bt_scan_pb"
            style="@style/settings_icon_48x48_style"
            android:layout_marginStart="@dimen/dp_14"
            android:indeterminate="true"
            android:indeterminateDrawable="@drawable/progress_bar_bt_scan_bg"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/settings_bt_start_scan_iv"
            app:layout_constraintStart_toEndOf="@id/settings_bt_use_devices_tv"
            app:layout_constraintTop_toTopOf="@id/settings_bt_start_scan_iv" />

        <ImageView
            android:id="@+id/settings_bt_start_scan_iv"
            style="@style/settings_icon_48x48_style"
            android:layout_marginEnd="80dp"
            android:layout_marginBottom="@dimen/dp_15"
            android:src="@drawable/icon_list_refresh_48"
            app:layout_constraintBottom_toTopOf="@id/settings_can_use_devices_cl"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settings_can_use_devices_cl"
            style="@style/settings_content_card_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            android:minHeight="240dp"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/settings_paired_devices_cl">

            <TextView
                android:id="@+id/settings_no_scan_bt_tv"
                style="@style/settings_text_32_regular_747578_style"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/bt_not_found_can_devices"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/settings_can_use_devices_rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.bitech.vehiclesettings.view.widget.ScanScrollView>
