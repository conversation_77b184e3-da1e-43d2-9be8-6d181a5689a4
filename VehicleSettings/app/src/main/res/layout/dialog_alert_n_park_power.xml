<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="784dp"
    android:layout_height="960px"
    android:background="@drawable/border_bg_dialog"
    android:minWidth="752dp"
    android:minHeight="530.67dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="101.34dp"
        android:layout_gravity="center"
        android:gravity="center">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/str_park_power"
            android:textColor="@color/black"
            android:textSize="@dimen/font_42px" />
    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <FrameLayout
            android:layout_width="624dp"
            android:layout_height="64dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="80dp">

            <Button
                style="?android:borderlessButtonStyle"
                android:layout_width="624dp"
                android:layout_height="64dp"
                android:background="@drawable/button_card" />

<!--            <TextView-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginStart="330dp"-->
<!--                android:layout_marginTop="15dp"-->
<!--                android:text="@string/str_carsetting_more_hour"-->
<!--                android:textColor="@color/black"-->
<!--                android:textSize="@dimen/font_24px" />-->
        </FrameLayout>

        <com.shawnlin.numberpicker.NumberPicker
            android:id="@+id/npSetHour"
            android:layout_width="624dp"
            android:layout_height="206.67dp"
            android:layout_gravity="center_horizontal"
            app:np_dividerColor="@color/transparent"
            app:np_wrapSelectorWheel="false"
            app:np_selectedTextColor="@color/black"
            app:np_textColor="@color/black_transparent_40" />
    </FrameLayout>

    <TextView
        android:layout_width="624dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:layout_gravity="center"
        android:text="@string/str_park_power_desc"
        android:textColor="@color/black_transparent_60"
        android:textSize="@dimen/font_36px" />

    <LinearLayout
        android:layout_width="624dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:layout_marginTop="50.66dp">

        <Button
            android:id="@+id/btn_confirm"
            style="?android:borderlessButtonStyle"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:background="@drawable/button_blue_n"
            android:text="@string/str_open"
            android:textColor="@color/white"
            android:textSize="@dimen/font_36px" />

        <Button
            android:id="@+id/btn_cancel"
            style="?android:borderlessButtonStyle"
            android:layout_width="296dp"
            android:layout_height="64dp"
            android:layout_marginStart="32dp"
            android:background="@drawable/button_white"
            android:text="@string/str_cancel"
            android:textColor="@color/black"
            android:textSize="@dimen/font_36px" />
    </LinearLayout>
</LinearLayout>