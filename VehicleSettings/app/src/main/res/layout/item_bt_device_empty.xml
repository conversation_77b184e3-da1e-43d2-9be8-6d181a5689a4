<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_624"
    android:layout_height="@dimen/dp_107"
    android:layout_marginStart="@dimen/dp_80"
    android:layout_marginBottom="@dimen/dp_16"
    android:background="@drawable/shape_bg_white">

    <TextView
        android:id="@+id/bt_empty_info_tv"
        android:layout_width="@dimen/dp_550"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_32"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="@dimen/maxLines_1"
        android:text="@string/bt_not_found_can_devices"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_24"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
