<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:minWidth="752dp"
    android:layout_gravity="center"
    android:background="@drawable/border_bg_dialog">

    <TextView
        android:id="@+id/tv_simply_text"
        android:layout_width="wrap_content"
        android:layout_height="33.33dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="53.33dp"
        android:fontFamily="DreamHanSansCN"
        android:text="内容"
        android:textColor="@color/black"
        android:textSize="@dimen/font_36px" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="85.33dp"
        android:layout_marginTop="36.66dp"
        android:layout_marginRight="85.33dp"
        android:layout_marginBottom="45.33dp"
        android:layout_below="@id/tv_simply_text"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_simply_confirm"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:text="@string/str_confirm"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:textSize="@dimen/font_36px"
            android:textColor="@color/white"
            android:fontFamily="DreamHanSansCN"
            android:background="@drawable/selector_bg_open"/>
        <TextView
            android:id="@+id/tv_simply_cancel"
            android:layout_width="266.67dp"
            android:layout_height="66.67dp"
            android:text="@string/str_cancel"
            android:layout_marginLeft="26.66dp"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:textSize="@dimen/font_36px"
            android:textColor="@color/black"
            android:fontFamily="DreamHanSansCN"
            android:background="@drawable/selector_bg_cancel"/>

    </LinearLayout>
</RelativeLayout>