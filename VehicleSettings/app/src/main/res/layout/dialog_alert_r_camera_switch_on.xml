<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="550dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_recognition_fatigue_driver_status"
                android:textColor="@color/black"
                android:textSize="@dimen/font_40px" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="581.33dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:gravity="left"
                android:singleLine="false"
                android:text="@string/str_recognition_camera_1"
                android:textColor="@color/color_dialog_content"
                android:textSize="@dimen/font_36px" />

            <LinearLayout
                android:layout_width="590dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="53.33dp">
                <RadioGroup
                    android:id="@+id/rg_agree"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_marginTop="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">
                    <RadioButton
                        android:id="@+id/rb_agree"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:background="@drawable/selector_radio_check_bg"
                        android:foreground="@drawable/selector_radio_check_fg"
                        android:button="@null"
                        android:layout_width="26dp"
                        android:layout_height="26dp" />
                </RadioGroup>

                <TextView
                    android:id="@+id/tv_agree_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"

                    android:background="@drawable/selector_bg_blue_type"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="left"

                    android:text="@string/str_recognition_camera_2_3"

                    android:textColor="@color/color_dialog_content"
                    android:textSize="@dimen/font_36px" />

<!--                <TextView-->
<!--                    android:id="@+id/tv_agree_text"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginLeft="6dp"-->

<!--                    android:background="@drawable/selector_bg_blue_type"-->
<!--                    android:fontFamily="DreamHanSansCN"-->
<!--                    android:gravity="center"-->

<!--                    android:text="@string/str_recognition_camera_2"-->

<!--                    android:textColor="@color/color_dialog_content"-->
<!--                    android:textSize="@dimen/font_36px" />-->

<!--                <TextView-->
<!--                    android:id="@+id/tv_agree_link"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:autoLink="all"-->
<!--                    android:background="@drawable/selector_bg_blue_type"-->
<!--                    android:fontFamily="DreamHanSansCN"-->
<!--                    android:gravity="center"-->
<!--                    android:text="@string/str_recognition_camera_3"-->
<!--                    android:textColor="@color/blue"-->
<!--                    android:textSize="@dimen/font_36px" />-->
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="85.33dp"
                android:layout_marginTop="53.33dp"
                android:layout_marginRight="85.33dp"
                android:layout_marginBottom="45.33dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_camera_confirm"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/selector_bg_open"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_confirm2"
                    android:textColor="#ffffff"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_camera_cancel"
                    android:layout_width="266.67dp"
                    android:layout_height="66.67dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="26.66dp"
                    android:background="@drawable/selector_bg_cancel"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_cancel"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>