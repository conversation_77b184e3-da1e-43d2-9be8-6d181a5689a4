<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/border_bg_dialog"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33.33dp"
                android:fontFamily="DreamHanSansCN"
                android:text="@string/str_temperature_unit"
                android:textColor="@color/black"
                android:textSize="@dimen/font_48px" />

            <LinearLayout
                android:layout_width="576dp"
                android:layout_height="66.67dp"
                android:layout_gravity="center"
                android:layout_marginTop="86.67dp"
                android:background="@drawable/shape_bg_white">

                <TextView
                    android:id="@+id/tv_temperature_unit_celsius"
                    android:layout_width="288dp"
                    android:layout_height="66.67dp"
                    android:layout_marginLeft="0dp"
                    android:background="@drawable/selector_bg_blue_type"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_temperature_unit_celsius"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

                <TextView
                    android:id="@+id/tv_temperature_unit_fahrenheit"
                    android:layout_width="288dp"
                    android:layout_height="66.67dp"
                    android:layout_marginLeft="0dp"
                    android:background="@drawable/selector_bg_blue_type"
                    android:fontFamily="DreamHanSansCN"
                    android:gravity="center"
                    android:text="@string/str_temperature_unit_fahrenheit"
                    android:textColor="@color/selector_text_color"
                    android:textSize="@dimen/font_36px" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>