<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/px_1584"
    android:layout_height="@dimen/px_1049"
    android:orientation="vertical"
    android:background="@drawable/border_bg_dialog">

    <RelativeLayout
        android:layout_width="@dimen/px_1344"
        android:layout_height="@dimen/px_152"
        android:layout_gravity="center_horizontal">
        <TextView
            android:id="@+id/tv_title1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="@string/regular_car_wash"
            android:textSize="@dimen/px_42"
            android:layout_centerInParent="true"/>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="@dimen/px_1344"
        android:layout_height="@dimen/px_46"
        android:layout_marginTop="@dimen/px_24"
        android:layout_gravity="center_horizontal">
        <TextView
            android:id="@+id/tv_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/info_speed"
            android:textSize="@dimen/px_36"
            android:textColor="@color/color_transparent_60"
            android:layout_centerInParent="true"/>
    </RelativeLayout>

    <LinearLayout
        android:layout_width="@dimen/px_1344"
        android:layout_height="@dimen/px_369"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/px_48"
        android:layout_marginStart="@dimen/px_120">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="@dimen/px_556"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/px_32">

                <ImageView
                    android:id="@+id/iv_window"
                    android:layout_width="@dimen/px_50"
                    android:layout_height="@dimen/px_50"
                    android:background="@drawable/icon_set_yes"
                    android:visibility="visible"
                    android:layout_alignParentStart="true"/>

                <TextView
                    android:id="@+id/tv_window"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/window"
                    android:textSize="@dimen/px_36"
                    android:textColor="@color/black"
                    android:layout_marginStart="@dimen/px_25"
                    android:layout_toRightOf="@id/iv_window"/>

                <TextView
                    android:id="@+id/tv_window_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/closed"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_36"
                    android:layout_alignParentEnd="true"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="@dimen/px_556"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/px_32">

                <ImageView
                    android:id="@+id/iv_parking_mirror"
                    android:layout_width="@dimen/px_50"
                    android:layout_height="@dimen/px_50"
                    android:background="@drawable/icon_set_yes"
                    android:visibility="visible"
                    android:layout_alignParentStart="true"/>

                <TextView
                    android:id="@+id/tv_parking_mirror"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_25"
                    android:layout_toRightOf="@id/iv_parking_mirror"
                    android:text="@string/parking_mirror"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_36" />

                <TextView
                    android:id="@+id/tv_parking_mirror_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/folded"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_36"
                    android:layout_alignParentEnd="true"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="@dimen/px_556"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/px_32">

                <ImageView
                    android:id="@+id/iv_remote_lock"
                    android:layout_width="@dimen/px_50"
                    android:layout_height="@dimen/px_50"
                    android:background="@drawable/icon_set_yes"
                    android:visibility="visible"
                    android:layout_alignParentStart="true"/>

                <TextView
                    android:id="@+id/tv_remote_lock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/remote_lock"
                    android:textSize="@dimen/px_36"
                    android:textColor="@color/black"
                    android:layout_marginStart="@dimen/px_25"
                    android:layout_toRightOf="@id/iv_remote_lock"/>

                <TextView
                    android:id="@+id/tv_remote_lock_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/disabled"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_36"
                    android:layout_alignParentEnd="true"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="@dimen/px_556"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/px_32">

                <ImageView
                    android:id="@+id/iv_external_auto_door"
                    android:layout_width="@dimen/px_50"
                    android:layout_height="@dimen/px_50"
                    android:background="@drawable/icon_set_yes"
                    android:visibility="visible"
                    android:layout_alignParentStart="true"/>

                <TextView
                    android:id="@+id/tv_external_auto_door"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/external_auto_door"
                    android:textSize="@dimen/px_36"
                    android:textColor="@color/black"
                    android:layout_marginStart="@dimen/px_25"
                    android:layout_toRightOf="@id/iv_external_auto_door"/>

                <TextView
                    android:id="@+id/tv_external_auto_door_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/disabled"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_36"
                    android:layout_alignParentEnd="true"/>
            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_marginStart="@dimen/px_232"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">


            <RelativeLayout
                android:layout_width="@dimen/px_556"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/px_32">

                <ImageView
                    android:id="@+id/iv_auto_wiper"
                    android:layout_width="@dimen/px_50"
                    android:layout_height="@dimen/px_50"
                    android:background="@drawable/icon_set_no"
                    android:visibility="visible"
                    android:layout_alignParentStart="true"/>

                <TextView
                    android:id="@+id/tv_auto_wiper"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/auto_wiper"
                    android:textSize="@dimen/px_36"
                    android:textColor="@color/black"
                    android:layout_marginStart="@dimen/px_25"
                    android:layout_toRightOf="@id/iv_auto_wiper"/>

                <TextView
                    android:id="@+id/tv_auto_wiper_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/not_disabled"
                    android:textColor="#FF0000"
                    android:textSize="@dimen/px_36"
                    android:layout_alignParentEnd="true"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="@dimen/px_556"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/px_32">

                <ImageView
                    android:id="@+id/iv_auto_lock"
                    android:layout_width="@dimen/px_50"
                    android:layout_height="@dimen/px_50"
                    android:background="@drawable/icon_set_yes"
                    android:visibility="visible"
                    android:layout_alignParentStart="true"/>

                <TextView
                    android:id="@+id/tv_auto_lock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/auto_lock"
                    android:textSize="@dimen/px_36"
                    android:textColor="@color/black"
                    android:layout_marginStart="@dimen/px_25"
                    android:layout_toRightOf="@id/iv_auto_lock"/>

                <TextView
                    android:id="@+id/tv_auto_lock_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/disabled"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_36"
                    android:layout_alignParentEnd="true"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="@dimen/px_556"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/px_32">

                <ImageView
                    android:id="@+id/iv_air_conditioning"
                    android:layout_width="@dimen/px_50"
                    android:layout_height="@dimen/px_50"
                    android:background="@drawable/icon_set_yes"
                android:visibility="visible"
                android:layout_alignParentStart="true"/>

                <TextView
                    android:id="@+id/tv_air_conditioning"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/px_25"
                    android:layout_toRightOf="@id/iv_air_conditioning"
                    android:text="@string/air_conditioning"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_36" />

                <TextView
                    android:id="@+id/tv_air_conditioning_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/enabled"
                    android:textColor="@color/black"
                    android:textSize="@dimen/px_36"
                    android:layout_alignParentEnd="true"/>
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/px_96"
        android:layout_marginTop="@dimen/px_48"
        android:layout_marginStart="@dimen/px_120"
        android:orientation="horizontal">

        <View
            android:layout_width="@dimen/px_514"
            android:layout_height="@dimen/px_1"
            android:layout_marginTop="@dimen/px_48"
            android:background="#3A77F0" />

        <RelativeLayout
            android:id="@+id/rl_rear_mirror"
            android:layout_width="@dimen/px_252"
            android:layout_height="@dimen/px_96"
            android:layout_marginLeft="@dimen/px_32"
            android:layout_gravity="center_horizontal">

            <ImageView
                android:id="@+id/iv_unfold_mirror"
                android:layout_width="@dimen/px_50"
                android:layout_height="@dimen/px_50"
                android:layout_marginTop="@dimen/px_24"
                android:background="@drawable/icon_set_mirror_3"
                android:visibility="visible" />

            <TextView
                android:id="@+id/tv_unfold_mirror"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/px_72"
                android:layout_marginTop="@dimen/px_24"
                android:text="展开后视镜"
                android:textColor="#3A77F0"
                android:textSize="@dimen/px_36" />
        </RelativeLayout>

        <View
            android:layout_width="@dimen/px_514"
            android:layout_height="@dimen/px_1"
            android:layout_marginTop="@dimen/px_48"
            android:layout_marginLeft="@dimen/px_32"
            android:background="#3A77F0" />
    </LinearLayout>

    <Button
        android:id="@+id/btn_exit"
        android:layout_width="@dimen/px_1344"
        android:layout_height="@dimen/px_96"
        android:layout_marginTop="@dimen/px_76"
        android:layout_marginStart="@dimen/px_120"
        android:text="退出"
        android:textSize="@dimen/px_36"
        android:background="@drawable/shape_bg_blue"
        android:textColor="@color/white" />
</LinearLayout>